const Profile = require('../model/phase-2/profile');

class ProfileRepository {
  async createProfile(profileData) {
    try {
      const profile = new Profile(profileData);
      await profile.save();
      return profile;
    } catch (error) {
      throw error;
    }
  }

  async getProfileById(profileId) {
    try {
      const profile = await Profile.findById(profileId);
      return profile;
    } catch (error) {
      throw error;
    }
  }

  async getAllProfiles() {
    try {
      const profiles = await Profile.find();
      return profiles;
    } catch (error) {
      throw error;
    }
  }

  async updateProfile(profileId, profileData) {
    try {
      console.log(profileId , profileData)
      const profile = await Profile.findByIdAndUpdate(profileId, profileData, { new: true });
      return profile;
    } catch (error) {
      throw error;
    }
  }

  async deleteProfile(profileId) {
    try {
      const profile = await Profile.findByIdAndDelete(profileId);
      return profile;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new ProfileRepository();
