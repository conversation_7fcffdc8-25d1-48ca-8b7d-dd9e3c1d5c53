// impoterDetails.js
const mongoose = require('mongoose');

const impoterDetailsSchema = new mongoose.Schema({
    impotererNo: {
        type: String,
        required: true, 
    },
    impotererName: {
        type: String,
        required: true
    },
    invoiceNo: {
        type: String
    },
    areaOfUnit: {
        type: String
    },
    currency: {
        type: String
    },
    quantity: {
        type: Number
    },
    totalArea: {
        type: Number
    },
    amount: {
        type: Number
    },
    invoicePdf: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'PdfDetails'
    },
    blPdf: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'PdfDetails'
    },
    exl:{
        type: mongoose.Schema.Types.ObjectId,
        ref:'PackageListExcel'
    }
});


const impoterDetails = mongoose.model('impoterDetails', impoterDetailsSchema);

module.exports = impoterDetails;
