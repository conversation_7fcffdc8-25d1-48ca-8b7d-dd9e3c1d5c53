const profileRepository = require('../repositories/profile-repository');

class ProfileService {
  async createProfile(profileData) {
    try {
      const profile = await profileRepository.createProfile(profileData);
      return profile;
    } catch (error) {
      throw error;
    }
  }

  async getProfileById(profileId) {
    try {
      const profile = await profileRepository.getProfileById(profileId);
      return profile;
    } catch (error) {
      throw error;
    }
  }

  async getAllProfiles() {
    try {
      const profiles = await profileRepository.getAllProfiles();
      return profiles;
    } catch (error) {
      throw error;
    }
  }

  async updateProfile(profileId, profileData) {
    try {
      const profile = await profileRepository.updateProfile(profileId, profileData);
      return profile;
    } catch (error) {
      throw error;
    }
  }

  async deleteProfile(profileId) {
    try {
      const profile = await profileRepository.deleteProfile(profileId);
      return profile;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new ProfileService();
