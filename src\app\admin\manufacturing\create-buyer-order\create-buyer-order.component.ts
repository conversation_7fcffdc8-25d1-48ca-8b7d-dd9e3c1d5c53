import { Component, OnInit, ViewChild } from '@angular/core';
import { MasterService } from '../../../services/master.service';
import { MatDialog } from '@angular/material/dialog';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { DesignComponent } from '../../master/design/design.component';
export interface UserData {
  SrNo: string;
  Quality: string;
  Design: string;
  GroundBorder: string;
  size: string;
  KhapSize: string;
  Pcs: string;
  Area: string;
  Action: string;
}

const ELEMENT_DATA: UserData[] = [];
@Component({
  selector: 'app-create-buyer-order',
  templateUrl: './create-buyer-order.component.html',
  styleUrl: './create-buyer-order.component.css',
})
export class CreateBuyerOrderComponent implements OnInit {
  constructor(
    private _services: MasterService,
    private matDig: MatDialog,
    private fb: FormBuilder
  ) {}
  displayedColumns: string[] = [
    'SrNo',
    'Quality',
    'Design',
    'GroundBorder',
    'size',
    'KhapSize',
    'Pcs',
    'PcsesPending',
    'PcsesAssigned',
    'Area',
    'Action',
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);
  qualityList: any = [];
  sizeList: any = [];
  totalSizeInYaard: any;
  frmOrderItem!: FormGroup;
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  ngOnInit(): void {
    this.frmOrderItem = this.fb.group({
      quality: [''],
      design: [''],
      ground: [''],
      border: [''],
      size: [''],
      type: ['Normal ( NR )'],
      pcs: ['1'],
      PcsesPending: ['1'],
      PcsesAssigned: ['0'],
      totalArea: [''],
      khapSize: [''],
    });
    this.quality();
    this.size();
  }
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }
  quality() {
    this._services.getqualityList().subscribe((resp: any) => {
      // console.log(resp);

      resp.map((v: any) => {
        this.qualityList.push({
          id: v._id,
          quality: v.quality,
        });
      });
    });
  }

  size() {
    this._services.getAllSizesList().subscribe((resp: any) => {
      if (resp) {
        resp.map((v: any) => {
          this.sizeList.push({
            id: v._id,
            size: v.sizeInYard,
            khapSize: v.khapSize,
          });
        });
      }
    });
  }
  selectQuality(qty: any) {
    let dialogRef = this.matDig.open(DesignComponent, {
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.frmOrderItem.get('design')?.setValue(result.design);
        this.frmOrderItem
          .get('ground')
          ?.setValue(result.ground + '/' + result.border);
      }
      console.log('result result result', result);
    });
  }
  selectSize(data: any) {
    let khapsize = this.sizeList.find((x: any) => x.id === data);
    this.frmOrderItem.get('khapSize')?.setValue(khapsize.khapSize);
    this.setSizeYard(khapsize.size);
    return;
  }
  setSizeYard(size: any) {
    const formData = size;

    const calcData = formData.split(/[Xx]/);

    const width1 = calcData[0].split('.')[0];
    const width2 = calcData[0].split('.')[1];

    console.log('before ', width1, 'after ', width2);

    console.log(calcData);

    const length1 = calcData[1].split('.')[0];
    const length2 = calcData[1].split('.')[1];

    const sizeInYaardCalc1 = parseInt(width1) * 12 + parseInt(width2);

    const sizeInYaardCalc2 = parseInt(length1) * 12 + parseInt(length2);
    console.log(sizeInYaardCalc1);
    console.log(sizeInYaardCalc2);

    this.totalSizeInYaard = (sizeInYaardCalc1 * sizeInYaardCalc2) / 1296;

    this.frmOrderItem
      .get('totalArea')
      ?.setValue(this.totalSizeInYaard.toFixed(2));
  }
  addItems(){

  }
}
