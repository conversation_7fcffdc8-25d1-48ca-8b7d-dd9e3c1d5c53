// Script to check issue number to carpet number mapping
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function checkIssueCarpetMapping() {
  console.log('🔍 CHECKING ISSUE NUMBER TO CARPET NUMBER MAPPING');
  console.log('='.repeat(80));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get all K- records with their issue and carpet numbers
    const allRecords = await collection.find(
      { receiveNo: { $regex: /^K-/ } },
      { 
        receiveNo: 1, 
        'issueNo.Br_issueNo': 1, 
        carpetNo: 1,
        weaverName: 1,
        design: 1,
        quality: 1,
        area: 1,
        amount: 1,
        'issueNo.rate': 1
      }
    ).sort({ receiveNo: 1 }).toArray();
    
    console.log(`📊 Total K- records found: ${allRecords.length}`);
    
    // Create mapping of issue numbers to carpet numbers
    const issueMapping = {};
    
    console.log('\n📋 COMPLETE ISSUE TO CARPET MAPPING:');
    console.log('='.repeat(80));
    console.log('Issue No → Carpet No | Weaver | Design | Quality | Area | Amount | Rate');
    console.log('='.repeat(80));
    
    allRecords.forEach((record, index) => {
      const issueNo = record.issueNo?.Br_issueNo || 'N/A';
      const carpetNo = record.carpetNo || 'N/A';
      const receiveNo = record.receiveNo;
      
      // Extract issue number (e.g., K-2400001 → 1)
      const issueNum = issueNo.replace('K-24000', '').replace('K-2400', '');
      const carpetNum = carpetNo.replace('K-24000', '').replace('K-2400', '');
      
      if (!issueMapping[issueNum]) {
        issueMapping[issueNum] = [];
      }
      issueMapping[issueNum].push({
        carpetNo: carpetNum,
        receiveNo: receiveNo,
        weaver: record.weaverName || 'N/A',
        design: record.design || 'N/A',
        quality: record.quality || 'N/A',
        area: record.area || 'N/A',
        amount: record.amount || 'N/A',
        rate: record.issueNo?.rate || 'N/A'
      });
      
      console.log(`${issueNum.padStart(2, '0')} → ${carpetNum.padStart(2, '0')} | ${(record.weaverName || 'N/A').substring(0, 12).padEnd(12)} | ${(record.design || 'N/A').substring(0, 8).padEnd(8)} | ${(record.quality || 'N/A').substring(0, 5).padEnd(5)} | ${(record.area || 'N/A').substring(0, 8).padEnd(8)} | ₹${(record.amount || 'N/A').toString().substring(0, 6).padEnd(6)} | ₹${(record.issueNo?.rate || 'N/A').toString().substring(0, 4).padEnd(4)}`);
    });
    
    // Find unique issue numbers
    const uniqueIssueNums = Object.keys(issueMapping).sort((a, b) => parseInt(a) - parseInt(b));
    const maxIssueNum = Math.max(...uniqueIssueNums.map(n => parseInt(n)));
    const minIssueNum = Math.min(...uniqueIssueNums.map(n => parseInt(n)));
    
    console.log('\n📊 ISSUE NUMBER ANALYSIS:');
    console.log('='.repeat(50));
    console.log(`📊 Unique issue numbers: ${uniqueIssueNums.length}`);
    console.log(`📊 Issue number range: ${minIssueNum} to ${maxIssueNum}`);
    console.log(`📊 Total carpet numbers: ${allRecords.length}`);
    
    // Show which issue numbers have multiple carpets
    console.log('\n📋 ISSUE NUMBERS WITH MULTIPLE CARPETS:');
    console.log('='.repeat(50));
    uniqueIssueNums.forEach(issueNum => {
      const carpets = issueMapping[issueNum];
      if (carpets.length > 1) {
        console.log(`\n🔍 Issue ${issueNum} has ${carpets.length} carpets:`);
        carpets.forEach((carpet, index) => {
          console.log(`   ${index + 1}. Carpet ${carpet.carpetNo} (${carpet.receiveNo})`);
          console.log(`      Weaver: ${carpet.weaver} | Design: ${carpet.design} | Quality: ${carpet.quality}`);
          console.log(`      Area: ${carpet.area} | Amount: ₹${carpet.amount} | Rate: ₹${carpet.rate}/Sq.Ft`);
        });
      }
    });
    
    // Show issue numbers that are missing
    console.log('\n📋 MISSING ISSUE NUMBERS:');
    console.log('='.repeat(50));
    const missingIssues = [];
    for (let i = minIssueNum; i <= maxIssueNum; i++) {
      if (!uniqueIssueNums.includes(i.toString())) {
        missingIssues.push(i);
      }
    }
    
    if (missingIssues.length > 0) {
      console.log(`❌ Missing issue numbers: ${missingIssues.join(', ')}`);
    } else {
      console.log('✅ No missing issue numbers in the range');
    }
    
    // Show carpet numbers that don't match issue numbers
    console.log('\n📋 CARPET NUMBERS NOT MATCHING ISSUE NUMBERS:');
    console.log('='.repeat(50));
    const mismatchedRecords = allRecords.filter(record => {
      const issueNo = record.issueNo?.Br_issueNo || '';
      const carpetNo = record.carpetNo || '';
      return issueNo !== carpetNo;
    });
    
    if (mismatchedRecords.length > 0) {
      console.log(`❌ Found ${mismatchedRecords.length} mismatched records:`);
      mismatchedRecords.forEach((record, index) => {
        console.log(`${index + 1}. ${record.receiveNo}:`);
        console.log(`   Issue: ${record.issueNo?.Br_issueNo || 'N/A'}`);
        console.log(`   Carpet: ${record.carpetNo || 'N/A'}`);
        console.log(`   Weaver: ${record.weaverName || 'N/A'}`);
      });
    } else {
      console.log('✅ All issue and carpet numbers match perfectly');
    }
    
    return { issueMapping, uniqueIssueNums, allRecords };
    
  } catch (error) {
    console.error('❌ Error checking mapping:', error);
    return null;
  }
}

async function main() {
  console.log('🔍 ANALYZING ISSUE NUMBER TO CARPET NUMBER MAPPING');
  console.log('(Finding which issue numbers map to which carpet numbers)');
  console.log('='.repeat(80));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Check issue to carpet mapping
    const result = await checkIssueCarpetMapping();

    if (result) {
      console.log('\n🎯 SUMMARY:');
      console.log('='.repeat(50));
      console.log(`📊 Total records analyzed: ${result.allRecords.length}`);
      console.log(`📊 Unique issue numbers: ${result.uniqueIssueNums.length}`);
      console.log(`📊 Issue range: ${Math.min(...result.uniqueIssueNums.map(n => parseInt(n)))} to ${Math.max(...result.uniqueIssueNums.map(n => parseInt(n)))}`);
      
      // Show first 10 and last 10 mappings
      console.log('\n📋 FIRST 10 MAPPINGS:');
      result.allRecords.slice(0, 10).forEach((record, index) => {
        const issueNum = (record.issueNo?.Br_issueNo || '').replace('K-24000', '').replace('K-2400', '');
        const carpetNum = (record.carpetNo || '').replace('K-24000', '').replace('K-2400', '');
        console.log(`   Issue ${issueNum} → Carpet ${carpetNum} (${record.weaverName || 'N/A'})`);
      });
      
      console.log('\n📋 LAST 10 MAPPINGS:');
      result.allRecords.slice(-10).forEach((record, index) => {
        const issueNum = (record.issueNo?.Br_issueNo || '').replace('K-24000', '').replace('K-2400', '');
        const carpetNum = (record.carpetNo || '').replace('K-24000', '').replace('K-2400', '');
        console.log(`   Issue ${issueNum} → Carpet ${carpetNum} (${record.weaverName || 'N/A'})`);
      });
    }

  } catch (error) {
    console.error('❌ Analysis failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 ISSUE TO CARPET MAPPING ANALYSIS COMPLETE!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
