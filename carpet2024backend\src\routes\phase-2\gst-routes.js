const express = require('express');
const router = express.Router();
const gstController = require('../../controller/gst-controller');

router.post('/create-gst', gstController.createGST);
router.get('/get-gst/:id', gstController.getGSTById);
router.get('/get-all-gst/', gstController.getGST);
router.put('/update-gst/:id', gstController.updateGST);
router.delete('/delete-gst/:id', gstController.deleteGST);

module.exports = router;
