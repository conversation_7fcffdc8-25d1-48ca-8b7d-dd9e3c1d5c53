import { Component, OnInit } from '@angular/core';
import { MatTableDataSource, } from '@angular/material/table';
import { AfterViewInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ImporterService } from '../../../../services/importer.service';
import { CustomeServiceService } from '../../../../services/custome-service.service';
import Swal from 'sweetalert2';


export interface UserData {
  id: string;
  index: number;
  impoterNo: string;

  currency: string;

  noOfInvoice: string;
  totalNumberOfPcs: string;
  totalQuantity: string;
  totalAmount: string;

}



const ELEMENT_DATA: UserData[] = [];


@Component({
  selector: 'app-container-despatch',
  templateUrl: './container-despatch.component.html',
  styleUrl: './container-despatch.component.css'
})
export class ContainerDespatchComponent implements OnInit {
  selectedValue: any;

  ids: any;

  impotererNo: any = [];

  currencys: any = ['New', 'Dollar', 'Euro', 'Ruppes']
  countrys: any = ['Germany'];

  selected = 'option2';
  TotalNumberofpsc = 'option2';
  Country = 'option2';
  Curency = 'option2';
  ContainerNumber = 'option2';
  LinerDetails = 'option2';
  TotalQuantity = 'option2;'
  hide = true;

  displayedColumns: any = ['id', 'impoterNo', 
    'noOfInvoice','totalQuantity', 'totalNumberOfPcs',  'currency','totalAmount', 'action'];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  constructor(private _fb: FormBuilder, private _services: ImporterService, private customeServices: CustomeServiceService) { }
  frmContainerDespatch!: FormGroup;


  isUpdated: boolean = false;



  ngOnInit(): void {
    this.frmContainerDespatch = this._fb.group({
      impoterNo: [],
      chooseAdate: [],
      country: [],
      currency: [],
      containerNumber: [],
      linerDetails: [],
      noOfInvoice: [],
      totalNumberOfPcs: [],
      totalQuantity: [],
      totalAmount: [],
    });

    this.getAllImporterInvoice();
    // 
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();


  }

  addContainerDespatch() {
    console.log(this.frmContainerDespatch.value);
    debugger
    let frmData = this.frmContainerDespatch.value;
    console.log(parseFloat(this.frmContainerDespatch.value.totalQuantity))

    frmData.totalQuantity=parseFloat(this.frmContainerDespatch.value.totalQuantity);
    frmData.impoterNo = this.customerCode;
    let date = this.customeServices.convertDate2(frmData.chooseAdate)
    frmData.chooseAdate = date;
    this._services.addContainerDespatched(frmData).subscribe((resp: any) => {

      Swal.fire(
        'Success!',
        'Data has been saved successfully',
        'success'
      )
      // 
    }, (error) => {


      Swal.fire(
        'warning!',
        error.message,
        'warning'
      )
    })
  }

  getAllContainerDespatched(data: any) {

    ELEMENT_DATA.length = 0;




    this.allInvoices.map((val: any, ind: number) => {
      let importerCode = val.impotererNo.split('/')[0];
      if (importerCode === data) {

        // let date = this.customeServices.convertDate2(val.chooseAdate);
        ELEMENT_DATA.push({
          id: val._id,
          index: ELEMENT_DATA.length + 1,
          impoterNo: val.impotererName,
          currency: val.currency,
          noOfInvoice: val.invoiceNo,
          totalNumberOfPcs: val.totalArea,
          totalQuantity: val.quantity,
          totalAmount: val.amount,
        })
      }
    })
    this.dataSource = new MatTableDataSource(ELEMENT_DATA);
    this.ngAfterViewInit();
    return;
  }

  allInvoices: any;
  getAllImporterInvoice() {
    this._services.getAllImporterInvoice().subscribe((resp: any) => {
      this.allInvoices = resp;
      // Extract importer numbers from the response and push them into impotererNo array
      resp.forEach((val: any) => {

        // Extract numeric part of the importer number
        let importcode = val.impotererNo.split('/')[0];

        const numericPart = parseInt(importcode.split('-')[1]); // Assuming the format is "KOTI-XXXX"
        this.impotererNo.push({ original: importcode, numeric: numericPart });
      });

      // Sort impotererNo array based on the numeric part in descending order
      this.impotererNo.sort((a: any, b: any) => b.numeric - a.numeric);

      // Reformat the sorted elements back into the original format
      this.impotererNo = this.impotererNo.map((entry: any) => entry.original);

      debugger
      this.impotererNo = [...new Set(this.impotererNo)];

    });

  }
  totalInvoice: any = 0;
  totalAmount: any = 0;
  totalPcs: any = 0;
  customerCode: any;
  totalArea: any = 0;
  areaOfUnit: string = '';

  FindInvoice(event: any) {

    this.allInvoices.map((val: any) => {

      let importercode = val.impotererNo.split('/');

      if (importercode[0] === event) {
        debugger
        this.customerCode = val.impotererNo
        let count = 0;
        this.totalAmount = this.totalAmount + parseFloat(val.amount);
        this.totalPcs = this.totalPcs + parseFloat(val.quantity);
        this.totalInvoice = ++count
        this.totalArea = this.totalArea + parseFloat(val.totalArea);
        this.areaOfUnit = val.areaOfUnit;

      }
    })

    setTimeout(() => {

      this.frmContainerDespatch.get('noOfInvoice')?.setValue(this.totalInvoice);
      this.frmContainerDespatch.get('totalNumberOfPcs')?.setValue(this.totalPcs);
      this.frmContainerDespatch.get('totalQuantity')?.setValue(this.totalArea  +' '+this.areaOfUnit);
      this.frmContainerDespatch.get('totalAmount')?.setValue(this.totalAmount);
      this.getAllContainerDespatched(event);


    }, 3000);
  }

  edit(id: any) {
    this._services.getContainerDespatch(id).subscribe((resp: any) => {
      this.ids = id;
      this.isUpdated = true;
      this.frmContainerDespatch.patchValue(resp);

      let date = this.customeServices.formatDate(resp.chooseAdate);
      this.frmContainerDespatch.get('chooseAdate')?.setValue(new Date(date));
    })
  }
  updateContainer() {
    let frmData = this.frmContainerDespatch.value;
    if (this.ids) {

      let date = this.customeServices.convertDate2(frmData.chooseAdate)
      frmData.chooseAdate = date;
      this._services.updateContainerDespatched(this.ids, frmData).subscribe((resp) => {
        if (resp) {
          Swal.fire(
            'success!',
            'Container has been updated',
            'success'
          )
          // 
        }
      });
    }
  }
  deleteDespatchContainer(id: any) {

    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!"
    }).then((result) => {
      if (result.isConfirmed) {

        this._services.deleteContainerDespatched(id).subscribe((resp: any) => {

          Swal.fire({
            title: "Deleted!",
            text: 'Deleted successfully',
            icon: "success"
          });

        })
        // 

      }
    });

  }
}




