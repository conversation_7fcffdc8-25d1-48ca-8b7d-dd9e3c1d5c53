import { Component, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

@Component({
  selector: 'app-purchase-manufacturing',
  templateUrl: './purchase-manufacturing.component.html',
  styleUrl: './purchase-manufacturing.component.css'
})
export class PurchaseManufacturingComponent {
  Purchase = 'option2';
  Description = 'option1';
  Name = 'option3';
  WoolQuality = 'option1';
  Count = 'option2';
  Colour = 'option3';
  PurchaseOrder ='option1';


  displayedColumns: string[] = [
    'SrNo',
    'Purchase',
    'Description',
    'PartyName',
    'PurchaseOrder',
    'Date',
    'WoolQuality',
    'Count',
    'Colour',
    'Ply',
    'LotNo',
    'Weight',
    'Rate',
    'Amount',
    'DeliveryDate',
    'Action',
  ];
  dataSource = new MatTableDataSource<any>;
  totalSizeInYaard: any;
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }
}
