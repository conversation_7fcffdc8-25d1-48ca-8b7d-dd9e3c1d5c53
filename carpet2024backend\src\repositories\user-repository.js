const User = require('../model/phase-1/user');
const RoleRepository=require('./role-repository');
const ProfileRepository=require('./profile-repository');

class UserRepository {
  async createUser(userData) {
    try {
      const userRole=await RoleRepository.getRoleByName("NORMAL");
      console.log(userRole,"fyhl")
      if(userRole){ 
        const userProfile=await ProfileRepository.createProfile();
        const user = new User({...userData,role:[userRole.id],profile:userProfile.id});
      await user.save();
      return user;
    }
    } catch (error) {
      throw error;
    }
  }
  async getUserByEmail(email) {
    try {
      return await User.findOne({ email }).populate('role').populate('profile');
    } catch (error) {
      throw error;
    }
  }
  async getUserById(userId) {
    try {
      const user = await User.findById(userId).populate('role',"-_id -__v").populate('profile').select("-__v");
      return user;
    } catch (error) {
      throw error;
    }
  }

  async getAllUsers() {
    try {
      const users = await User.find().populate('role',"-_id -__v").populate('profile').select("-__v");
      return users;
    } catch (error) {
      throw error;
    }
  }

  async updateUser(userId, userData) {
    try {
      const user = await User.findByIdAndUpdate(userId, userData, { new: true });
      return user;
    } catch (error) {
      throw error;
    }
  }

  async deleteUser(userId) {
    try {
      const user = await User.findByIdAndDelete(userId).populate('role',"-_id -__v").populate('profile',"-_id -__v").select("-__v");
      return user;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new UserRepository();
