const carpetRepository = require('../repositories/carpet-repository');

class CarpetService {
  async createCarpet(carpetData) {
    try {
      const carpet = await carpetRepository.createCarpet(carpetData);
      return carpet;
    } catch (error) {
      throw error;
    }
  }

  async getCarpetById(carpetId) {
    try {
      const carpet = await carpetRepository.getCarpetById(carpetId);
      return carpet;
    } catch (error) {
      throw error;
    }
  }

  async getAllCarpets() {
    try {
      const carpets = await carpetRepository.getAllCarpets();
      return carpets;
    } catch (error) {
      throw error;
    }
  }

  async updateCarpet(carpetId, carpetData) {
    try {
      const carpet = await carpetRepository.updateCarpet(carpetId, carpetData);
      return carpet;
    } catch (error) {
      throw error;
    }
  }

  async deleteCarpet(carpetId) {
    try {
      const carpet = await carpetRepository.deleteCarpet(carpetId);
      return carpet;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new CarpetService();
