const gstRepository = require('../repositories/gst-repository');

async function createGST(toDate, fromDate, gstRate) {
  return await gstRepository.createGST(toDate, fromDate, gstRate);
}

async function getGSTById(id) {
  return await gstRepository.getGSTById(id);
}

async function getGST() {
    return await gstRepository.getGST();
  }

async function updateGST(id, updates) {
  return await gstRepository.updateGST(id, updates);
}

async function deleteGST(id) {
  return await gstRepository.deleteGST(id);
}

module.exports = {
  createGST,
  getGSTById,
  updateGST,
  deleteGST,
  getGST
};
