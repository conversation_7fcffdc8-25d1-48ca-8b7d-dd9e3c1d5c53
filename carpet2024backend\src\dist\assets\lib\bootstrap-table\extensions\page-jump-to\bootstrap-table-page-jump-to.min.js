/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var e=n(t);function r(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function o(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,n){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},u(t,n)}function c(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var e,r=i(t);if(n){var o=i(this).constructor;e=Reflect.construct(r,arguments,o)}else e=r.apply(this,arguments);return c(this,e)}}function f(t,n){for(;!Object.prototype.hasOwnProperty.call(t,n)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,n,e){var r=f(t,n);if(r){var o=Object.getOwnPropertyDescriptor(r,n);return o.get?o.get.call(arguments.length<3?t:e):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},p=function(t){return t&&t.Math==Math&&t},y=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof s&&s)||function(){return this}()||Function("return this")(),b={},d=function(t){try{return!!t()}catch(t){return!0}},v=!d((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),h=!d((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),g=h,m=Function.prototype.call,w=g?m.bind(m):function(){return m.apply(m,arguments)},O={},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,P=S&&!j.call({1:2},1);O.f=P?function(t){var n=S(this,t);return!!n&&n.enumerable}:j;var T,E,x=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},A=h,_=Function.prototype,R=_.call,C=A&&_.bind.bind(R,R),F=function(t){return A?C(t):function(){return R.apply(t,arguments)}},I=F,M=I({}.toString),k=I("".slice),D=function(t){return k(M(t),8,-1)},L=D,z=F,B=function(t){if("Function"===L(t))return z(t)},N=d,G=D,J=Object,W=B("".split),q=N((function(){return!J("z").propertyIsEnumerable(0)}))?function(t){return"String"==G(t)?W(t,""):J(t)}:J,H=function(t){return null==t},U=H,X=TypeError,$=function(t){if(U(t))throw X("Can't call method on "+t);return t},K=q,Q=$,V=function(t){return K(Q(t))},Y="object"==typeof document&&document.all,Z={all:Y,IS_HTMLDDA:void 0===Y&&void 0!==Y},tt=Z.all,nt=Z.IS_HTMLDDA?function(t){return"function"==typeof t||t===tt}:function(t){return"function"==typeof t},et=nt,rt=Z.all,ot=Z.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:et(t)||t===rt}:function(t){return"object"==typeof t?null!==t:et(t)},it=y,ut=nt,ct=function(t){return ut(t)?t:void 0},at=function(t,n){return arguments.length<2?ct(it[t]):it[t]&&it[t][n]},ft=B({}.isPrototypeOf),lt=y,st=at("navigator","userAgent")||"",pt=lt.process,yt=lt.Deno,bt=pt&&pt.versions||yt&&yt.version,dt=bt&&bt.v8;dt&&(E=(T=dt.split("."))[0]>0&&T[0]<4?1:+(T[0]+T[1])),!E&&st&&(!(T=st.match(/Edge\/(\d+)/))||T[1]>=74)&&(T=st.match(/Chrome\/(\d+)/))&&(E=+T[1]);var vt=E,ht=vt,gt=d,mt=!!Object.getOwnPropertySymbols&&!gt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ht&&ht<41})),wt=mt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Ot=at,jt=nt,St=ft,Pt=Object,Tt=wt?function(t){return"symbol"==typeof t}:function(t){var n=Ot("Symbol");return jt(n)&&St(n.prototype,Pt(t))},Et=String,xt=nt,At=function(t){try{return Et(t)}catch(t){return"Object"}},_t=TypeError,Rt=function(t){if(xt(t))return t;throw _t(At(t)+" is not a function")},Ct=Rt,Ft=H,It=w,Mt=nt,kt=ot,Dt=TypeError,Lt={exports:{}},zt=y,Bt=Object.defineProperty,Nt=function(t,n){try{Bt(zt,t,{value:n,configurable:!0,writable:!0})}catch(e){zt[t]=n}return n},Gt=Nt,Jt="__core-js_shared__",Wt=y[Jt]||Gt(Jt,{}),qt=Wt;(Lt.exports=function(t,n){return qt[t]||(qt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Ht=$,Ut=Object,Xt=function(t){return Ut(Ht(t))},$t=Xt,Kt=B({}.hasOwnProperty),Qt=Object.hasOwn||function(t,n){return Kt($t(t),n)},Vt=B,Yt=0,Zt=Math.random(),tn=Vt(1..toString),nn=function(t){return"Symbol("+(void 0===t?"":t)+")_"+tn(++Yt+Zt,36)},en=y,rn=Lt.exports,on=Qt,un=nn,cn=mt,an=wt,fn=rn("wks"),ln=en.Symbol,sn=ln&&ln.for,pn=an?ln:ln&&ln.withoutSetter||un,yn=function(t){if(!on(fn,t)||!cn&&"string"!=typeof fn[t]){var n="Symbol."+t;cn&&on(ln,t)?fn[t]=ln[t]:fn[t]=an&&sn?sn(n):pn(n)}return fn[t]},bn=w,dn=ot,vn=Tt,hn=function(t,n){var e=t[n];return Ft(e)?void 0:Ct(e)},gn=function(t,n){var e,r;if("string"===n&&Mt(e=t.toString)&&!kt(r=It(e,t)))return r;if(Mt(e=t.valueOf)&&!kt(r=It(e,t)))return r;if("string"!==n&&Mt(e=t.toString)&&!kt(r=It(e,t)))return r;throw Dt("Can't convert object to primitive value")},mn=TypeError,wn=yn("toPrimitive"),On=function(t,n){if(!dn(t)||vn(t))return t;var e,r=hn(t,wn);if(r){if(void 0===n&&(n="default"),e=bn(r,t,n),!dn(e)||vn(e))return e;throw mn("Can't convert object to primitive value")}return void 0===n&&(n="number"),gn(t,n)},jn=Tt,Sn=function(t){var n=On(t,"string");return jn(n)?n:n+""},Pn=ot,Tn=y.document,En=Pn(Tn)&&Pn(Tn.createElement),xn=function(t){return En?Tn.createElement(t):{}},An=xn,_n=!v&&!d((function(){return 7!=Object.defineProperty(An("div"),"a",{get:function(){return 7}}).a})),Rn=v,Cn=w,Fn=O,In=x,Mn=V,kn=Sn,Dn=Qt,Ln=_n,zn=Object.getOwnPropertyDescriptor;b.f=Rn?zn:function(t,n){if(t=Mn(t),n=kn(n),Ln)try{return zn(t,n)}catch(t){}if(Dn(t,n))return In(!Cn(Fn.f,t,n),t[n])};var Bn={},Nn=v&&d((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Gn=ot,Jn=String,Wn=TypeError,qn=function(t){if(Gn(t))return t;throw Wn(Jn(t)+" is not an object")},Hn=v,Un=_n,Xn=Nn,$n=qn,Kn=Sn,Qn=TypeError,Vn=Object.defineProperty,Yn=Object.getOwnPropertyDescriptor,Zn="enumerable",te="configurable",ne="writable";Bn.f=Hn?Xn?function(t,n,e){if($n(t),n=Kn(n),$n(e),"function"==typeof t&&"prototype"===n&&"value"in e&&ne in e&&!e.writable){var r=Yn(t,n);r&&r.writable&&(t[n]=e.value,e={configurable:te in e?e.configurable:r.configurable,enumerable:Zn in e?e.enumerable:r.enumerable,writable:!1})}return Vn(t,n,e)}:Vn:function(t,n,e){if($n(t),n=Kn(n),$n(e),Un)try{return Vn(t,n,e)}catch(t){}if("get"in e||"set"in e)throw Qn("Accessors not supported");return"value"in e&&(t[n]=e.value),t};var ee=Bn,re=x,oe=v?function(t,n,e){return ee.f(t,n,re(1,e))}:function(t,n,e){return t[n]=e,t},ie={exports:{}},ue=v,ce=Qt,ae=Function.prototype,fe=ue&&Object.getOwnPropertyDescriptor,le=ce(ae,"name"),se={EXISTS:le,PROPER:le&&"something"===function(){}.name,CONFIGURABLE:le&&(!ue||ue&&fe(ae,"name").configurable)},pe=nt,ye=Wt,be=B(Function.toString);pe(ye.inspectSource)||(ye.inspectSource=function(t){return be(t)});var de,ve,he,ge=ye.inspectSource,me=nt,we=y.WeakMap,Oe=me(we)&&/native code/.test(String(we)),je=Lt.exports,Se=nn,Pe=je("keys"),Te=function(t){return Pe[t]||(Pe[t]=Se(t))},Ee={},xe=Oe,Ae=y,_e=ot,Re=oe,Ce=Qt,Fe=Wt,Ie=Te,Me=Ee,ke="Object already initialized",De=Ae.TypeError,Le=Ae.WeakMap;if(xe||Fe.state){var ze=Fe.state||(Fe.state=new Le);ze.get=ze.get,ze.has=ze.has,ze.set=ze.set,de=function(t,n){if(ze.has(t))throw De(ke);return n.facade=t,ze.set(t,n),n},ve=function(t){return ze.get(t)||{}},he=function(t){return ze.has(t)}}else{var Be=Ie("state");Me[Be]=!0,de=function(t,n){if(Ce(t,Be))throw De(ke);return n.facade=t,Re(t,Be,n),n},ve=function(t){return Ce(t,Be)?t[Be]:{}},he=function(t){return Ce(t,Be)}}var Ne={set:de,get:ve,has:he,enforce:function(t){return he(t)?ve(t):de(t,{})},getterFor:function(t){return function(n){var e;if(!_e(n)||(e=ve(n)).type!==t)throw De("Incompatible receiver, "+t+" required");return e}}},Ge=d,Je=nt,We=Qt,qe=v,He=se.CONFIGURABLE,Ue=ge,Xe=Ne.enforce,$e=Ne.get,Ke=Object.defineProperty,Qe=qe&&!Ge((function(){return 8!==Ke((function(){}),"length",{value:8}).length})),Ve=String(String).split("String"),Ye=ie.exports=function(t,n,e){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),e&&e.getter&&(n="get "+n),e&&e.setter&&(n="set "+n),(!We(t,"name")||He&&t.name!==n)&&(qe?Ke(t,"name",{value:n,configurable:!0}):t.name=n),Qe&&e&&We(e,"arity")&&t.length!==e.arity&&Ke(t,"length",{value:e.arity});try{e&&We(e,"constructor")&&e.constructor?qe&&Ke(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=Xe(t);return We(r,"source")||(r.source=Ve.join("string"==typeof n?n:"")),t};Function.prototype.toString=Ye((function(){return Je(this)&&$e(this).source||Ue(this)}),"toString");var Ze=nt,tr=Bn,nr=ie.exports,er=Nt,rr=function(t,n,e,r){r||(r={});var o=r.enumerable,i=void 0!==r.name?r.name:n;if(Ze(e)&&nr(e,i,r),r.global)o?t[n]=e:er(n,e);else{try{r.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=e:tr.f(t,n,{value:e,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return t},or={},ir=Math.ceil,ur=Math.floor,cr=Math.trunc||function(t){var n=+t;return(n>0?ur:ir)(n)},ar=function(t){var n=+t;return n!=n||0===n?0:cr(n)},fr=ar,lr=Math.max,sr=Math.min,pr=ar,yr=Math.min,br=function(t){return t>0?yr(pr(t),9007199254740991):0},dr=function(t){return br(t.length)},vr=V,hr=function(t,n){var e=fr(t);return e<0?lr(e+n,0):sr(e,n)},gr=dr,mr=function(t){return function(n,e,r){var o,i=vr(n),u=gr(i),c=hr(r,u);if(t&&e!=e){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===e)return t||c||0;return!t&&-1}},wr={includes:mr(!0),indexOf:mr(!1)},Or=Qt,jr=V,Sr=wr.indexOf,Pr=Ee,Tr=B([].push),Er=function(t,n){var e,r=jr(t),o=0,i=[];for(e in r)!Or(Pr,e)&&Or(r,e)&&Tr(i,e);for(;n.length>o;)Or(r,e=n[o++])&&(~Sr(i,e)||Tr(i,e));return i},xr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ar=Er,_r=xr.concat("length","prototype");or.f=Object.getOwnPropertyNames||function(t){return Ar(t,_r)};var Rr={};Rr.f=Object.getOwnPropertySymbols;var Cr=at,Fr=or,Ir=Rr,Mr=qn,kr=B([].concat),Dr=Cr("Reflect","ownKeys")||function(t){var n=Fr.f(Mr(t)),e=Ir.f;return e?kr(n,e(t)):n},Lr=Qt,zr=Dr,Br=b,Nr=Bn,Gr=d,Jr=nt,Wr=/#|\.prototype\./,qr=function(t,n){var e=Ur[Hr(t)];return e==$r||e!=Xr&&(Jr(n)?Gr(n):!!n)},Hr=qr.normalize=function(t){return String(t).replace(Wr,".").toLowerCase()},Ur=qr.data={},Xr=qr.NATIVE="N",$r=qr.POLYFILL="P",Kr=qr,Qr=y,Vr=b.f,Yr=oe,Zr=rr,to=Nt,no=function(t,n,e){for(var r=zr(n),o=Nr.f,i=Br.f,u=0;u<r.length;u++){var c=r[u];Lr(t,c)||e&&Lr(e,c)||o(t,c,i(n,c))}},eo=Kr,ro=function(t,n){var e,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(e=a?Qr:f?Qr[c]||to(c,{}):(Qr[c]||{}).prototype)for(r in n){if(i=n[r],o=t.dontCallGetSet?(u=Vr(e,r))&&u.value:e[r],!eo(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;no(i,o)}(t.sham||o&&o.sham)&&Yr(i,"sham",!0),Zr(e,r,i,t)}},oo=D,io=Array.isArray||function(t){return"Array"==oo(t)},uo=TypeError,co=Sn,ao=Bn,fo=x,lo={};lo[yn("toStringTag")]="z";var so="[object z]"===String(lo),po=so,yo=nt,bo=D,vo=yn("toStringTag"),ho=Object,go="Arguments"==bo(function(){return arguments}()),mo=po?bo:function(t){var n,e,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,n){try{return t[n]}catch(t){}}(n=ho(t),vo))?e:go?bo(n):"Object"==(r=bo(n))&&yo(n.callee)?"Arguments":r},wo=B,Oo=d,jo=nt,So=mo,Po=ge,To=function(){},Eo=[],xo=at("Reflect","construct"),Ao=/^\s*(?:class|function)\b/,_o=wo(Ao.exec),Ro=!Ao.exec(To),Co=function(t){if(!jo(t))return!1;try{return xo(To,Eo,t),!0}catch(t){return!1}},Fo=function(t){if(!jo(t))return!1;switch(So(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Ro||!!_o(Ao,Po(t))}catch(t){return!0}};Fo.sham=!0;var Io=!xo||Oo((function(){var t;return Co(Co.call)||!Co(Object)||!Co((function(){t=!0}))||t}))?Fo:Co,Mo=io,ko=Io,Do=ot,Lo=yn("species"),zo=Array,Bo=function(t){var n;return Mo(t)&&(n=t.constructor,(ko(n)&&(n===zo||Mo(n.prototype))||Do(n)&&null===(n=n[Lo]))&&(n=void 0)),void 0===n?zo:n},No=function(t,n){return new(Bo(t))(0===n?0:n)},Go=d,Jo=vt,Wo=yn("species"),qo=ro,Ho=d,Uo=io,Xo=ot,$o=Xt,Ko=dr,Qo=function(t){if(t>9007199254740991)throw uo("Maximum allowed index exceeded");return t},Vo=function(t,n,e){var r=co(n);r in t?ao.f(t,r,fo(0,e)):t[r]=e},Yo=No,Zo=function(t){return Jo>=51||!Go((function(){var n=[];return(n.constructor={})[Wo]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},ti=vt,ni=yn("isConcatSpreadable"),ei=ti>=51||!Ho((function(){var t=[];return t[ni]=!1,t.concat()[0]!==t})),ri=Zo("concat"),oi=function(t){if(!Xo(t))return!1;var n=t[ni];return void 0!==n?!!n:Uo(t)};qo({target:"Array",proto:!0,arity:1,forced:!ei||!ri},{concat:function(t){var n,e,r,o,i,u=$o(this),c=Yo(u,0),a=0;for(n=-1,r=arguments.length;n<r;n++)if(oi(i=-1===n?u:arguments[n]))for(o=Ko(i),Qo(a+o),e=0;e<o;e++,a++)e in i&&Vo(c,a,i[e]);else Qo(a+1),Vo(c,a++,i);return c.length=a,c}});var ii=Rt,ui=h,ci=B(B.bind),ai=function(t,n){return ii(t),void 0===n?t:ui?ci(t,n):function(){return t.apply(n,arguments)}},fi=q,li=Xt,si=dr,pi=No,yi=B([].push),bi=function(t){var n=1==t,e=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,l,s){for(var p,y,b=li(a),d=fi(b),v=ai(f,l),h=si(d),g=0,m=s||pi,w=n?m(a,h):e||u?m(a,0):void 0;h>g;g++)if((c||g in d)&&(y=v(p=d[g],g,b),t))if(n)w[g]=y;else if(y)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:yi(w,p)}else switch(t){case 4:return!1;case 7:yi(w,p)}return i?-1:r||o?o:w}},di={forEach:bi(0),map:bi(1),filter:bi(2),some:bi(3),every:bi(4),find:bi(5),findIndex:bi(6),filterReject:bi(7)},vi={},hi=Er,gi=xr,mi=Object.keys||function(t){return hi(t,gi)},wi=v,Oi=Nn,ji=Bn,Si=qn,Pi=V,Ti=mi;vi.f=wi&&!Oi?Object.defineProperties:function(t,n){Si(t);for(var e,r=Pi(n),o=Ti(n),i=o.length,u=0;i>u;)ji.f(t,e=o[u++],r[e]);return t};var Ei,xi=at("document","documentElement"),Ai=qn,_i=vi,Ri=xr,Ci=Ee,Fi=xi,Ii=xn,Mi=Te("IE_PROTO"),ki=function(){},Di=function(t){return"<script>"+t+"</"+"script>"},Li=function(t){t.write(Di("")),t.close();var n=t.parentWindow.Object;return t=null,n},zi=function(){try{Ei=new ActiveXObject("htmlfile")}catch(t){}var t,n;zi="undefined"!=typeof document?document.domain&&Ei?Li(Ei):((n=Ii("iframe")).style.display="none",Fi.appendChild(n),n.src=String("javascript:"),(t=n.contentWindow.document).open(),t.write(Di("document.F=Object")),t.close(),t.F):Li(Ei);for(var e=Ri.length;e--;)delete zi.prototype[Ri[e]];return zi()};Ci[Mi]=!0;var Bi=yn,Ni=Object.create||function(t,n){var e;return null!==t?(ki.prototype=Ai(t),e=new ki,ki.prototype=null,e[Mi]=t):e=zi(),void 0===n?e:_i.f(e,n)},Gi=Bn.f,Ji=Bi("unscopables"),Wi=Array.prototype;null==Wi[Ji]&&Gi(Wi,Ji,{configurable:!0,value:Ni(null)});var qi=ro,Hi=di.find,Ui=function(t){Wi[Ji][t]=!0},Xi="find",$i=!0;Xi in[]&&Array(1).find((function(){$i=!1})),qi({target:"Array",proto:!0,forced:$i},{find:function(t){return Hi(this,t,arguments.length>1?arguments[1]:void 0)}}),Ui(Xi);var Ki=mo,Qi=so?{}.toString:function(){return"[object "+Ki(this)+"]"};so||rr(Object.prototype,"toString",Qi,{unsafe:!0});var Vi=e.default.fn.bootstrapTable.utils;e.default.extend(e.default.fn.bootstrapTable.defaults,{showJumpTo:!1,showJumpToByPages:0}),e.default.extend(e.default.fn.bootstrapTable.locales,{formatJumpTo:function(){return"GO"}}),e.default.extend(e.default.fn.bootstrapTable.defaults,e.default.fn.bootstrapTable.locales),e.default.BootstrapTable=function(t){!function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&u(t,n)}(p,t);var n,c,f,s=a(p);function p(){return r(this,p),s.apply(this,arguments)}return n=p,c=[{key:"initPagination",value:function(){for(var t,n=this,r=arguments.length,o=new Array(r),u=0;u<r;u++)o[u]=arguments[u];if((t=l(i(p.prototype),"initPagination",this)).call.apply(t,[this].concat(o)),this.options.showJumpTo&&this.totalPages>=this.options.showJumpToByPages){var c=this.$pagination.find("> .pagination"),a=c.find(".page-jump-to");if(!a.length){var f=(a=e.default(Vi.sprintf(this.constants.html.inputGroup,'<input type="number"\n            class="'.concat(this.constants.classes.input).concat(Vi.sprintf(" %s%s",this.constants.classes.inputPrefix,this.options.iconSize),'"\n            value="').concat(this.options.pageNumber,'"\n            min="1"\n            max="').concat(this.totalPages,'">'),'<button class="'.concat(this.constants.buttonsClass,'"  type="button">\n          ').concat(this.options.formatJumpTo(),"\n          </button>"))).addClass("page-jump-to").appendTo(c)).find("input");a.find("button").click((function(){n.selectPage(+f.val())})),f.keyup((function(t){""!==f.val()&&(13!==t.keyCode?+f.val()<+f.attr("min")?f.val(f.attr("min")):+f.val()>+f.attr("max")&&f.val(f.attr("max")):n.selectPage(+f.val()))})),f.blur((function(){""===f.val()&&f.val(n.options.pageNumber)}))}}}}],c&&o(n.prototype,c),f&&o(n,f),Object.defineProperty(n,"prototype",{writable:!1}),p}(e.default.BootstrapTable)}));
