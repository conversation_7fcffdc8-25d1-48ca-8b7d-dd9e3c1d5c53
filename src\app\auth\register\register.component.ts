import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AuthServicesService } from '../../services/auth-services.service';
import Swal from 'sweetalert2';
import { title } from 'process';
import { Router } from '@angular/router';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrl: './register.component.css'
})
export class RegisterComponent implements OnInit {
frmRegister!:FormGroup;
constructor(private fb:FormBuilder,private authService : AuthServicesService, private route:Router){}
  ngOnInit(): void {
this.frmRegister = this.fb.group({
  name:[],
  email:[],
  password:[]
})
}
register(){
  debugger
  let frmData = this.frmRegister.value;
this.authService.register(frmData).subscribe({
  next:(value) =>{
Swal.fire({title:'Success',text:'Registration has been successfull', icon:'success'});
this.route.navigate(['/login'])
  },error:(err) =>{
    Swal.fire({title:'Failed',text:'Something went wrong', icon:'warning'});
  },
})
}
}




































