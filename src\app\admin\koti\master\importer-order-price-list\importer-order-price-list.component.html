<div class="container-fluid vh-100">

  <!-- start page title -->
  <div class="py-3 py-lg-4">
    <div class="row">
      <div class="col-lg-6">
        <h4 class="page-title mb-0">Add</h4>
      </div>

    </div>
    <hr>

    <!-- Form row -->
    <div class="row">
      <div class="col-xl-12">
        <div class="card">
          <div class="card-body">



            <form [formGroup]="frmImporterOrderPrice" (ngSubmit)="importerOrderPrice()">
              <div class="row">
                <div class="mb-2 col-md-3">
                  <!-- <label for="inputPassword4" class="form-label">To date</label>
                  <input type="date" class="ms-2 form-control ps-2" formControlName="toDate"> -->
                  <mat-form-field appearance="outline">
                    <mat-label>To date</mat-label>
                    <input matInput [matDatepicker]="picker" formControlName="toDate">
                    
                    <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                </mat-form-field>


                </div>
                <div class="mb-2 col-md-3">
                  <!-- <label for="inputPassword4" class="form-label">Form date</label>
                  <input type="date" class="ms-2 form-control ps-2" formControlName="fromDate"> -->
                  <mat-form-field appearance="outline">
                    <mat-label>Form date</mat-label>
                    <input matInput [matDatepicker]="picker1" formControlName="fromDate">
                    
                    <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
                    <mat-datepicker #picker1></mat-datepicker>
                </mat-form-field>


                </div>

                <div class="mb-2 col-md-3">
                  
                  <mat-form-field appearance="outline">
                    <mat-label>Quality</mat-label>
                    <mat-select formControlName="quality" >
                     
                      @for (quality of qualityList; track quality) {
                    <mat-option [value]="quality">{{quality}}</mat-option>
                  }
                    </mat-select>
                  </mat-form-field>
                  
                </div>
                <div class="mb-2 col-md-3">
                  
                  <mat-form-field appearance="outline">
                    <mat-label>Design</mat-label>
                    <mat-select formControlName="design" >
                     
                      @for (design of designList; track design) {
                    <mat-option [value]="design">{{design}}</mat-option>
                  }
                    </mat-select>
                  </mat-form-field>
                </div>
                <div class="mb-2 col-md-2">
                  <!-- <label for="inputPassword4" class="form-label">Order Price</label>
                  <input type="text" placeholder="00.00" class="ms-2 form-control ps-2" formControlName="orderPrice"> -->

                  <mat-form-field appearance="outline" class="example-full-width">
                    <mat-label>Order Price</mat-label>
                    <input type="text"matInput placeholder="00.00" formControlName="orderPrice">
                </mat-form-field>
                </div>
              </div>






              <button type="submit" class="btn btn-primary">Save</button>
            </form>
          </div>
        </div>

        <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">

          <!--- Note that these columns can be defined in any order.
                      The actual rendered columns are set as a property on the row definition" -->

          <!-- Position Column -->
          <ng-container matColumnDef="toDate">
            <th mat-header-cell *matHeaderCellDef> To Date </th>
            <td mat-cell *matCellDef="let element"> {{element.toDate}} </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="fromDate">
            <th mat-header-cell *matHeaderCellDef> From Date </th>
            <td mat-cell *matCellDef="let element"> {{element.fromDate}} </td>
          </ng-container>

          <!-- Weight Column -->
          <ng-container matColumnDef="quality">
            <th mat-header-cell *matHeaderCellDef> Quality </th>
            <td mat-cell *matCellDef="let element"> {{element.quality}} </td>
          </ng-container>


          <ng-container matColumnDef="orderPrice">
            <th mat-header-cell *matHeaderCellDef> Price </th>
            <td mat-cell *matCellDef="let element"> {{element.orderPrice}} </td>
          </ng-container>

          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef> Sr. No. </th>
            <td mat-cell *matCellDef="let element"> {{element.index}} </td>
          </ng-container>


          <!-- Symbol Column -->
          <ng-container matColumnDef="design">
            <th mat-header-cell *matHeaderCellDef> Design </th>
            <td mat-cell *matCellDef="let element"> {{element.design}} </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>


      </div>