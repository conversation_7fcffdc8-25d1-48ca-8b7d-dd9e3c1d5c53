const BillForWholeseller = require("../model/phase-2/billWholeseller");
const { CreateChallan } = require("../model/phase-2/createChallan");
const ContainerReceived = require("../model/phase-1/container-rvc");
class BillForWholesellerRepository {
  async create(billData) {
    try {
      return await BillForWholeseller.create(billData);
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      return await BillForWholeseller.find();
    } catch (error) {
      throw error;
    }
  }

  async findById(id) {
    try {
      return await BillForWholeseller.findById(id);
    } catch (error) {
      throw error;
    }
  }

  async update(id, billData) {
    try {
      let challan = [];
      if (Array.isArray(billData.challanNo)) {
        billData.challanNo.map((x) => {
          challan.push({
            challanNumber: x,
            status: "sale",
          });
        });
        billData.challanNo = challan;
      }
      let updated = await BillForWholeseller.findByIdAndUpdate(id, billData, {
        new: true,
      });

      if (updated) {
        let flag = 0;
        const updateChallanPromises = billData.challanNo.map(async (ch) => {
          let updatedChallan = await CreateChallan.updateOne(
            { challanNo: ch.challanNumber },
            {
              $set: {
                isBillCreated: true,
              },
            }
          );

          if (
            updatedChallan.acknowledged === true &&
            updatedChallan.modifiedCount === 1
          ) {
            flag = 1;
          }
        });

        await Promise.all(updateChallanPromises);

        if (flag === 1) {
          return updated;
        } else {
          throw new Error("Failed to update all challans");
        }
      } else {
        throw new Error("Failed to update bill");
      }
    } catch (error) {
      throw error;
    }
  }

  async delete(id) {
    try {
      let flag = 0;
      let ids = id.split(" ");
      let billDetails = await BillForWholeseller.find({ _id: ids[0] });
      if (ids.length > 1) {
        let billData = await BillForWholeseller.updateOne(
          { _id: ids[0], "challanNo.challanNumber": ids[1] },
          { $set: { "challanNo.$.status": "return" } }
        );
        if (billData.acknowledged === true && billData.modifiedCount == 1) {
          let getChallan = await CreateChallan.find({ challanNo: ids[1] });
          if (getChallan[0].isDeleted == true) {
            return billData;
          } else {
            let updatedChallan = await CreateChallan.updateOne(
              { challanNo: ids[1] },
              {
                $set: {
                  // isDeleted: false,
                  isBillCreated: false,
                },
              }
            );
            if (
              updatedChallan.acknowledged === true &&
              updatedChallan.modifiedCount == 1
            ) {
              return billData;
            }
          }
        }
      } else {
        for (const i of billDetails[0].challanNo) {
          console.log(i);
          let getChallan = await CreateChallan.find({
            challanNo: i.challanNumber,
          });
          if ( getChallan[0].isDeleted !== true && getChallan[0].isBillCreated === true ) {
            let updatedChallan = await CreateChallan.updateOne({ challanNo: i.challanNumber },
              {
                $set: {
                  // isDeleted: true,
                  isBillCreated: false,
                },
              }
            );
            if (updatedChallan.acknowledged === true && updatedChallan.modifiedCount == 1) {
              let upb = await BillForWholeseller.updateOne(
                { _id: ids[0], "challanNo.challanNumber": i.challanNumber },
                { $set: { "challanNo.$.status": "return" } }
              );

              console.log(upb);
              flag = 1;
            }
          }else{
            
              let billDeleted = await BillForWholeseller.updateOne(
                { _id: ids[0] },
                {
                  $set: {
                    isBillDeleted: true,
                  },
                }
              );
    
              if (
                billDeleted.acknowledged === true &&
                billDeleted.modifiedCount == 1
              ) {
                if (updatedChallan.acknowledged === true && updatedChallan.modifiedCount == 1) {
                  let upb = await BillForWholeseller.updateOne(
                    { _id: ids[0], "challanNo.challanNumber": i.challanNumber },
                    { $set: { "challanNo.$.status": "return" } }
                  );
    
                  console.log(upb);
                  flag = 1;
                }
                return billDeleted;
              }
            
          }
        }
        if (flag == 1) {          
          return await BillForWholeseller.findByIdAndDelete(id);   
        }
      }
      //   return await BillForWholeseller.findByIdAndDelete(id);
    } catch (error) {
      throw error;
    }
  }
 
  async getCarpetDetails(bcr) {
    try {
      let carpetDetails = [];
      let bcrDatas = [];
  
      if (bcr.carpetNo !== '') {
        bcrDatas = await ContainerReceived.find(
          { containerItem: { $elemMatch: { GerCarpetNo: bcr.carpetNo } } },
          { "containerItem.$": 1 }
        );
      } else if (bcr.fromDate && bcr.toDate) {
        let dbList = await ContainerReceived.find();
  
        dbList.forEach(item => {
          item.containerItem.forEach(bcrData => {
            const dateObject = new Date(bcrData.Date);
            

    const day = dateObject.getDate();
    const month = dateObject.getMonth() + 1;
    const year = dateObject.getFullYear();
    const formattedDate = `${day < 10 ? '0' : ''}${day}.${month < 10 ? '0' : ''
      }${month}.${year}`;
  
            if (parseDate(bcr.fromDate) <= parseDate(formattedDate) && parseDate(formattedDate) <= parseDate(bcr.toDate)) {
              bcrDatas.push(bcrData);
            }
          });
        });
      } else {
        throw new Error("Invalid parameters. Provide either 'carpetNo' or 'fromDate' and 'toDate'.");
      }
  
      if (bcrDatas.length === 0) {
        throw new Error("No container item found ");
      }
  if(bcrDatas.length>1){
    
      for (const bcrData of bcrDatas) {
        let getChallan = await CreateChallan.find({
          carpetList: { $elemMatch: { barcodeNo: parseInt(bcrData.GerCarpetNo) } }
        });
  
        for (const ch of getChallan) {
          let billData = await BillForWholeseller.find({
            challanNo: { $elemMatch: { challanNumber: ch.challanNo } }
          });
  
          for (const br of ch.carpetList) {
            if (br.barcodeNo == parseInt(bcrData.GerCarpetNo)) {
              let detail = {
                challanNo: ch.challanNo,
                challanDate: ch.chooseAdate,
                challanCustomer: ch.wholeseller,
                challanStatus: br.status,
                retailerOutlet: ch.retailerOutlet,
                challanRetailer: ch.RetailerName,
                gerCarpetNo: bcrData.GerCarpetNo,
                qualityDesign: bcrData.QualityDesign,
                color: bcrData.Color,
                cCode: bcrData.CCode,
                qCode: bcrData.QCode,
                size: bcrData.Size,
                sCode: bcrData.SCore,
                area: bcrData.Area,
                rate: bcrData.Rate,
                evkPrice: bcrData.EvKPrice,
                amount: bcrData.Amount,
                invoiceNo: bcrData.InvoiceNo,
                importerCode: bcrData.ImporterCode,
                remarks: bcrData.Remarks,
                status: bcrData.status
              };
  
              if (billData.length > 0) {
                for (const b of billData) {
                  carpetDetails.push({
                    ...detail,
                    billNo: b.billNo,
                    billDate: b.chooseAdate,
                    billCustomer: b.wholesellerName
                  });
                }
              } else {
                carpetDetails.push(detail);
              }
            }
          }
        }
      }
    }else{
      for (const bcrData of bcrDatas[0].containerItem) {
        let getChallan = await CreateChallan.find({
          carpetList: { $elemMatch: { barcodeNo: parseInt(bcrData.GerCarpetNo) } }
        });
  
if(getChallan.length===0){
  carpetDetails.push({
    gerCarpetNo: bcrData.GerCarpetNo,
    qualityDesign: bcrData.QualityDesign,
    color: bcrData.Color,
    cCode: bcrData.CCode,
    qCode: bcrData.QCode,
    size: bcrData.Size,
    sCode: bcrData.SCore,
    area: bcrData.Area,
    rate: bcrData.Rate,
    evkPrice: bcrData.EvKPrice,
    amount: bcrData.Amount,
    invoiceNo: bcrData.InvoiceNo,
    importerCode: bcrData.ImporterCode,
    remarks: bcrData.Remarks,
    status: bcrData.status
  })
}

        for (const ch of getChallan) {
          let billData = await BillForWholeseller.find({
            challanNo: { $elemMatch: { challanNumber: ch.challanNo } }
          });
  
          for (const br of ch.carpetList) {
            if (br.barcodeNo == parseInt(bcrData.GerCarpetNo)) {
              let detail = {
                challanNo: ch.challanNo,
                challanDate: ch.chooseAdate,
                challanCustomer: ch.wholeseller,
                challanStatus: br.status,
                retailerOutlet: ch.retailerOutlet,
                challanRetailer: ch.RetailerName,
                gerCarpetNo: bcrData.GerCarpetNo,
                qualityDesign: bcrData.QualityDesign,
                color: bcrData.Color,
                cCode: bcrData.CCode,
                qCode: bcrData.QCode,
                size: bcrData.Size,
                sCode: bcrData.SCore,
                area: bcrData.Area,
                rate: bcrData.Rate,
                evkPrice: bcrData.EvKPrice,
                amount: bcrData.Amount,
                invoiceNo: bcrData.InvoiceNo,
                importerCode: bcrData.ImporterCode,
                remarks: bcrData.Remarks,
                status: bcrData.status
              };
  
              if (billData.length > 0) {
                for (const b of billData) {
                  carpetDetails.push({
                    ...detail,
                    billNo: b.billNo,
                    billDate: b.chooseAdate,
                    billCustomer: b.wholesellerName
                  });
                }
              } else {
                carpetDetails.push(detail);
              }
            }
          }
        }
      }
    }
      return carpetDetails;
    } catch (error) {
      throw error;
    }
  }

}



  function parseDate(dateStr) {
    // Convert "DD.MM.YYYY" to "YYYY-MM-DD" and then create a Date object
    let parts = dateStr.split('.');
    return new Date(parts[2], parts[1] - 1, parts[0]); // Date(year, monthIndex, day)
}
module.exports = BillForWholesellerRepository;
