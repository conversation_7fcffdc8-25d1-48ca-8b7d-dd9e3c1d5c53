import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { ImporterService } from '../../../../services/importer.service';
import { CustomeServiceService } from '../../../../services/custome-service.service';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { Observable, firstValueFrom } from 'rxjs';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import Swal from 'sweetalert2';
import { MatSelectChange } from '@angular/material/select';
export interface UserData {
  SrNo: string;
  BillNo: string;
  Date: string;
  ChallanNo: string;
  WholeSellerName: string;
  Action: string;
}

let ELEMENT_DATA2: UserData[] = [];
@Component({
  selector: 'app-bills-for-retailer',
  templateUrl: './bills-for-retailer.component.html',
  styleUrl: './bills-for-retailer.component.css',
})
export class BillsForRetailerComponent implements OnInit {
  retailerList: any = [];
  challanList: any = [];
  isUpdated: boolean = false;
  @ViewChild('setDiscountAmt') setDiscountAmt!: ElementRef;
  displayedColumns: string[] = [
    'SrNo',
    'BillNo',
    'Date',
    'ChallanNo',
    'retailserName',
    'Action',
  ];
  dataSource = new MatTableDataSource(ELEMENT_DATA2);
  challanno: any = [];
  billItemList: any = [];
  constructor(
    private _services: ImporterService,
    private customeService: CustomeServiceService,
    private _fb: FormBuilder,
    private activatedRoute: ActivatedRoute,
    private ngxLoader: NgxUiLoaderService
  ) {}

  frmBill!: FormGroup;
  myControl = new FormControl();
  filteredOptions!: Observable<string[]>;
  options: string[] = [];

  isEditedId: string | undefined;
  ngOnInit(): void {
    this.frmBill = this._fb.group({
      billNo: [],
      chooseAdate: [],
      retailserName: [],
    });

    this.isEditedId = this.activatedRoute.snapshot.paramMap.get('id') || '';

    this.executeAllAndEditBill(this.isEditedId);
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  allStock: any;

  async getAllContainerReceived(): Promise<any> {
    const resp = await firstValueFrom(this._services.getAllContainerRecieved());
    this.allStock = resp;
  }

  async getAllChallan(): Promise<any> {
    try {
      const resp = await firstValueFrom(this._services.getAllChallan());
      if (Array.isArray(resp)) {
        this.challanList = resp;
        this.options.length = 0;
        resp.forEach((x: any) => {
          if (x.isBillCreated !== true && x.isDeleted !== true && x.group=='Retailer') {
            this.challanno.push(x.challanNo);
            this.options = this.challanno;
            this.options = this.options.sort((a: any, b: any) => a - b);
          }
        });
        debugger;
        console.log(this.options);
      } else {
        console.error('Unexpected response format:', resp);
      }
    } catch (error) {
      console.error('Error in getAllChallan:', error);
    }
  }
  async getRetailerList(): Promise<any> {
    return firstValueFrom(this._services.getRetailerList())
      .then((resp: any) => {
        if (resp) {
          resp.map((x: any) => {
            this.retailerList.push({
              id: x._id,
              retailerName: x.retailerStoreName,
            });
          });
        }
      })
      .catch((error) => {
        console.error(error);
      });
  }

  events: any;
  challanOptionList: any = [];
  addEvent(event: MatDatepickerInputEvent<Date>) {
    debugger;
    let retailser = this.frmBill.value.retailserName;
    this.events = `${event.value}`;
    const eventDate = new Date(this.events).toISOString().split('T')[0];
    const uniqueChallanNo = new Set<string>();
    this.challanList.forEach((x: any) => {
      let date = new Date(eventDate);
      date.setDate(date.getDate() + 1);
      let incrementedDateString = date.toISOString().split('T')[0];
      const challanDate = new Date(x.chooseAdate).toISOString().split('T')[0];
      if (
        incrementedDateString >= challanDate &&
        x.isBillCreated !== true &&
        x.isDeleted !== true &&  x.group=='Retailer'
      ) {
        x.carpetList.forEach((y: any) => {
          if (y.status !== 'return') {
            uniqueChallanNo.add(x.challanNo);
          }
        });
      }
    });

    this.challanOptionList = Array.from(uniqueChallanNo);
    this.options.length = 0;
    this.options = this.challanOptionList.sort((a: any, b: any) => a - b);
    debugger;
    console.log(this.options);
  }

  onOptionSelected(challanNo: any) {
    const processedChallans = new Set();
    debugger;
    let challanno = challanNo;
    let isExist = this.billItemList.some(
      (ex: any) => ex.challanNo == challanno
    );
    debugger;
    if (!isExist) {
      this.challanList.forEach((x: any) => {
        if (x.challanNo == challanno) {
          x.carpetList.forEach((element: any) => {
            this.allStock[0].containerItem.filter((elem: any) => {
              if (element.barcodeNo == elem.GerCarpetNo) {
                debugger;
                const includeChallanDetails = this.billItemList.some(
                  (a: any) => a.challanNo == x.challanNo
                );

                let date = this.customeService.convertDate(x.chooseAdate);

                let carpetPrice =element.evkPrice?element.evkPrice:elem.EvKPrice
                this.billItemList.push({
                  c: x.challanNo,
                  challanNo: includeChallanDetails ? undefined : x.challanNo,
                  challanDate: includeChallanDetails ? undefined : date,
                  customer: includeChallanDetails
                    ? undefined
                    : x.retailerOutlet,
                  carpetNo: elem.GerCarpetNo,
                  qualityDesign: elem.QualityDesign,
                  colour: elem.Color,
                  colourCode: elem.CCode,
                  qualityCode: elem.QCode,
                  size: element.size?element.size:elem.Size,
                  area:element.area?element.area: elem.Area,
                  evkPrice:carpetPrice,
                  amount: parseFloat(elem.Area) * parseFloat(carpetPrice),
                  invoiceNo: elem.InvoiceNo,
                  saleStatus: elem.status,
                });
              }
            });
          });
        }
      });
      this.calculateAmount(this.billItemList);
    } else {
      Swal.fire({
        title: 'warning',
        text: `Failed !, ${challanno} this challan already exist`,
        icon: 'warning',
      });
    }
  }
  a: any;
  allBills: any;
  // getBills() {
  //   this._services.getsBill().subscribe((resp: any) => {
  //     this.allBills=resp;
  //     setTimeout(() => {

  //       if (resp.length > 0) {
  //         resp = resp.map((x: any) => parseInt(x.billNo));
  //         debugger;
  //         console.log(resp);
  //         let maxItem = Math.max(...resp);
  //         console.log(maxItem);
  //         this.a = ++maxItem;
  //         this.frmBill.get('billNo')?.patchValue(maxItem);
  //       } else {
  //         let value = 2400001;
  //         this.a = value;
  //         this.frmBill.get('billNo')?.patchValue(value);
  //       }
  //     }, 1000);
  //   });
  // }

  async getBills(): Promise<void> {
    try {
      debugger;
      const resp = await firstValueFrom(this._services.getsBill());
      if (Array.isArray(resp)) {
        this.allBills = resp;

        if (resp.length > 0) {
          const billNos = resp.map((x: any) => parseInt(x.billNo, 10));
          debugger;
          console.log(billNos);
          let maxItem = Math.max(...billNos);
          console.log(maxItem);
          this.a = ++maxItem;
          this.frmBill.get('billNo')?.patchValue(maxItem);
        } else {
          let value = 2400001;
          this.a = value;
          this.frmBill.get('billNo')?.patchValue(value);
        }
      } else {
        throw new Error('Unexpected response format');
      }
    } catch (error) {
      console.error('Error in getBills:', error);
    }
  }

  onSubmit() {
    const challans = Array.from(
      new Set(
        this.billItemList
          .map((x: any) => x.challanNo)
          .filter((challanNo: any) => challanNo !== undefined)
      )
    );

    const formData = this.frmBill.value;

    formData['challanNo'] = challans.map(String);
    this._services.createBill(formData).subscribe(
      (resp: any) => {
        Swal.fire({
          title: 'success',
          text: 'Bill has been created successfully',
          icon: 'success',
        });
        this.reset();
      },
      (error) => {
        Swal.fire({
          title: 'warning',
          text: 'Failed !, Somthing went wrong',
          icon: 'warning',
        });
      }
    );
    console.log(formData);
  }
  newAmount: any = 0;
  financialData: any = {};
  isEditAmount: boolean = false;
  calculateAmount(data: any) {
    this.newAmount = 0;
    data.map((val: any) => {
      // let amount = this.isEditAmount
      //   ? parseFloat()
      //   : parseFloat(val.area) * parseFloat(val.evkPrice);
      this.newAmount = this.newAmount + parseFloat(val.amount);
    });
    this.financialData.amount = this._isDiscount
      ? this.newAmount - this.discountAmount
      : this.newAmount;
    this.financialData.profit = (this.financialData.amount / 100) * 13;
    this.financialData.gst =
      ((this.financialData.profit + this.financialData.amount) / 100) * 19;
    this.financialData.grossAmount =
      this.financialData.amount +
      this.financialData.gst +
      this.financialData.profit;

    console.log('```````', this.financialData);
  }
  discountAmount = 0;
  _isDiscount: boolean = false;
  setDiscount() {
    this._isDiscount = true;
    this.discountAmount = this.setDiscountAmt.nativeElement.value;
    this.calculateAmount(this.billItemList);
  }

  removeChallan(id: any) {
    debugger;
    let billId: any = this.isEditedId;

    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        debugger;
        if (!billId) {
          this.billItemList = this.billItemList.filter((x: any) => x.c != id);
          this.calculateAmount(this.billItemList);
          Swal.fire({
            title: 'Deleted!',
            text: 'Your bill item has been deleted.',
            icon: 'success',
          });
        } else {
          this._services.deleteWholesellerBill(billId + ' ' + id).subscribe(
            (resp: any) => {
              this.billItemList = this.billItemList.filter(
                (x: any) => x.c != id
              );
              this.calculateAmount(this.billItemList);
              Swal.fire({
                title: 'Deleted!',
                text: 'Your bill item has been deleted.',
                icon: 'success',
              });
            },
            (error) => {
              Swal.fire({
                title: 'Warning!',
                text: 'Your file has been deleted.',
                icon: 'warning',
              });
            }
          );
        }
      }
    });
  }

  reset() {
    this.frmBill.reset();
    this.getBills();
    this.myControl.reset();
    debugger;
    this.billItemList = [];
    this.calculateAmount(this.billItemList);
    this.getAllChallan();
  }
  lastSelected: any = '';
  selectBcr(data: MatSelectChange) {
    debugger;

    const selectedValues = data.value;
    const previousValues = this.billItemList
      .map((item: any) => item.challanNo)
      .filter((challanNo: any) => challanNo !== undefined);

    // If there are no previously selected values, take the first of the new selection
    if (previousValues.length === 0) {
      this.lastSelected = selectedValues[0];
    } else {
      // If an item was added
      if (selectedValues.length > previousValues.length) {
        // Find the newly added item
        this.lastSelected = selectedValues.find(
          (value: any) => !previousValues.includes(value)
        );
        debugger;
      } else {
        // If an item was removed, find the removed item
        this.lastSelected = previousValues.find(
          (value: any) => !selectedValues.includes(value)
        );
      }
    }
    debugger;
    console.log('Selected values:', selectedValues);
    console.log('Last selected value:', this.lastSelected);
    this.onOptionSelected(this.lastSelected);
    console.log('Selected values:', this.dataSource.filteredData);
  }

  _bill: any = {};
  editBill(billId: string) {
    debugger;
    this.isUpdated = true;
    let getChallans = this.allBills.find(
      (x: any) => x._id === billId.toString()
    );
    this.options = this.options.concat(getChallans.challanNo);
    this.options = this.options.sort((a: any, b: any) => a - b);
    this.frmBill.patchValue(getChallans);
    this.myControl.patchValue(getChallans.challanNo);

    getChallans.challanNo.forEach((element: any) => {
      let getChallanDetails = this.challanList.find(
        (x: any) => x.challanNo === element
      );

      getChallanDetails.carpetList.forEach((elem: any) => {
        // Find the item details in allContainerStock
        let matchedContainers = this.allStock.filter((container: any) =>
          container.containerItem.some(
            (item: any) => parseInt(item.GerCarpetNo) === elem.barcodeNo
          )
        );
        matchedContainers.forEach((container: any) => {
          container.containerItem.forEach((item: any) => {
            if (parseInt(item.GerCarpetNo) === elem.barcodeNo) {
              let date = this.customeService.convertDate(
                getChallanDetails.chooseAdate
              );
              const includeChallanDetails = this.billItemList.some(
                (a: any) => a.challanNo == getChallanDetails.challanNo
              );
              this.billItemList.push({
                c: getChallanDetails.challanNo,
                challanNo: includeChallanDetails
                  ? undefined
                  : getChallanDetails.challanNo,
                challanDate: includeChallanDetails ? undefined : date,
                customer: includeChallanDetails
                  ? undefined
                  : getChallanDetails.retailerOutlet,
                carpetNo: item.GerCarpetNo,
                qualityDesign: item.QualityDesign,
                colour: item.Color,
                colourCode: item.CCode,
                qualityCode: item.QCode,
                size: elem.size ? elem.size : item.Size,
                area: elem.area ? elem.area : item.Area,
                evkPrice: elem.evkPrice ? elem.evkPrice : item.EvKPrice,
                amount:
                  parseFloat(elem.evkPrice ? elem.evkPrice : item.EvKPrice) *
                  parseFloat(elem.area ? elem.area : item.Area),
                invoiceNo: item.InvoiceNo,
                saleStatus: item.status,
              });
            }
          });
        });
      });
    });

    this.calculateAmount(this.billItemList);
  }

  updateBill() {
    const challans = Array.from(
      new Set(
        this.billItemList
          .map((x: any) => x.challanNo)
          .filter((challanNo: any) => challanNo !== undefined)
      )
    );

    const formData = this.frmBill.value;
    debugger;
    formData['challanNo'] = challans.map(String);
    if (this.isEditedId) {
      let id = this.isEditedId;
      this._services.updateBill(formData, id).subscribe(
        (resp: any) => {
          Swal.fire({
            title: 'success',
            text: 'Bill has been updated successfully',
            icon: 'success',
          });
          this.reset();
        },
        (error) => {
          Swal.fire({
            title: 'warning',
            text: 'Failed !, Somthing went wrong',
            icon: 'warning',
          });
        }
      );
    }

    console.log(formData);
  }

  executeAllAndEditBill(billId: string) {
    this.ngxLoader.start();
    Promise.all([
      this.getAllChallan(),
      this.getRetailerList(),
      this.getAllContainerReceived(),
      this.getBills(),
    ])
      .then(() => {
        this.ngxLoader.start();
        if (this.isEditedId != null && this.isEditedId !== '') {
          this.editBill(billId);
        }
        this.ngxLoader.stopAll();
      })
      .catch((error) => {
        console.error('An error occurred: ', error);
      });
  }
}
