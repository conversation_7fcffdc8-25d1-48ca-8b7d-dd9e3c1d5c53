// Script to fix weaver display to K-format (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, etc.)
const mongoose = require('mongoose');

// Import models
const WeaverEmployee = require('../src/model/phase-3/weaver_employee');
const CarpetReceived = require('../src/model/phase-4/carpetReceived');
const Branch = require('../src/model/phase-3/manageBranch');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

// Original weaver names in K-format
const kFormatWeaverNames = [
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>', 
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>-<PERSON><PERSON><PERSON>',
  '<PERSON>-sa<PERSON> beg<PERSON>',
  '<PERSON>-nas<PERSON><PERSON>',
  '<PERSON>-r<PERSON><PERSON>',
  '<PERSON>-<PERSON><PERSON><PERSON>',
  '<PERSON>-<PERSON><PERSON>ar <PERSON>',
  '<PERSON>-<PERSON><PERSON>j<PERSON>',
  '<PERSON>-<PERSON><PERSON><PERSON>',
  '<PERSON>-<PERSON>hur<PERSON>',
  '<PERSON>-<PERSON><PERSON><PERSON>',
  '<PERSON>-<PERSON><PERSON><PERSON>',
  '<PERSON>-<PERSON><PERSON><PERSON>',
  '<PERSON>-<PERSON><PERSON>',
  '<PERSON>-<PERSON><PERSON> <PERSON><PERSON>',
  '<PERSON>-<PERSON><PERSON><PERSON> <PERSON>'
];

async function createKFormatWeavers() {
  console.log('🔄 CREATING K-FORMAT WEAVERS');
  console.log('='.repeat(60));
  
  try {
    // Get default branch
    const defaultBranch = await Branch.findOne({ branchName: 'Main Branch' });
    if (!defaultBranch) {
      console.error('❌ Default branch not found');
      return [];
    }
    
    const createdWeavers = [];
    
    // Create K-format weavers
    for (let i = 0; i < kFormatWeaverNames.length; i++) {
      try {
        const weaverName = kFormatWeaverNames[i];
        
        // Check if weaver already exists
        const existingWeaver = await WeaverEmployee.findOne({ name: weaverName });
        if (existingWeaver) {
          createdWeavers.push(existingWeaver);
          continue;
        }
        
        // Create new K-format weaver
        const newWeaver = await WeaverEmployee.create({
          branch: defaultBranch._id,
          groupName: 'K-Weavers',
          name: weaverName,
          address: 'Kashmir, India',
          zipcode: '190001',
          country: 'India',
          contactNo: `+91-9876543${i.toString().padStart(3, '0')}`,
          bankAccountNo: `*********${i.toString().padStart(3, '0')}`,
          ifscCode: 'SBIN0000123',
          tds: '10',
          commission: '5',
          bankName: 'State Bank of India',
          aadhaarDetails: {
            aadhaarNo: `*********${i.toString().padStart(3, '0')}`,
            aadhaarFile: ''
          },
          panDetails: {
            panNo: `ABCDE1234${String.fromCharCode(65 + i)}`,
            panFile: ''
          }
        });
        
        createdWeavers.push(newWeaver);
        console.log(`✅ Created weaver: ${weaverName}`);
        
      } catch (error) {
        console.error(`❌ Error creating weaver ${kFormatWeaverNames[i]}:`, error.message);
      }
    }
    
    return createdWeavers;
    
  } catch (error) {
    console.error('❌ Error creating K-format weavers:', error);
    return [];
  }
}

async function updateCarpetReceivedWeavers(createdWeavers) {
  console.log('\n🔄 UPDATING CARPET RECEIVED WEAVERS');
  console.log('='.repeat(60));
  
  try {
    // Get all carpet received records
    const allRecords = await CarpetReceived.find().sort({ receiveNo: 1 });
    console.log(`📊 Found ${allRecords.length} records to update`);
    
    const results = { success: [], errors: [] };
    
    // Update each record with K-format weaver
    for (let i = 0; i < allRecords.length; i++) {
      try {
        const record = allRecords[i];
        const weaverIndex = i % createdWeavers.length;
        const selectedWeaver = createdWeavers[weaverIndex];
        
        // Update both weaverNumber and weaverName
        await CarpetReceived.findByIdAndUpdate(record._id, {
          weaverNumber: selectedWeaver._id,
          weaverName: selectedWeaver.name
        });
        
        results.success.push({
          receiveNo: record.receiveNo,
          oldWeaver: record.weaverName,
          newWeaver: selectedWeaver.name,
          weaverId: selectedWeaver._id
        });
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Updated ${i + 1}/${allRecords.length} records`);
        }
        
      } catch (error) {
        console.error(`❌ Error updating record ${allRecords[i].receiveNo}:`, error.message);
        results.errors.push({
          receiveNo: allRecords[i].receiveNo,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error updating carpet received weavers:', error);
    return { success: [], errors: [] };
  }
}

async function verifyKFormatDisplay() {
  console.log('\n🔍 Verifying K-format display...');
  
  try {
    // Get sample records with populated weaver data
    const samples = await CarpetReceived.find()
      .populate('weaverNumber', 'name')
      .populate('K', 'branchCode')
      .limit(10)
      .lean();
    
    console.log('\n📋 Sample records with K-format weavers:');
    samples.forEach((record, index) => {
      const weaverDisplay = record.K?.branchCode ? 
        `${record.K.branchCode} - ${record.weaverNumber?.name || record.weaverName}` : 
        (record.weaverNumber?.name || record.weaverName);
        
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver Display: ${weaverDisplay || 'N/A'}`);
      console.log(`   WeaverName Field: ${record.weaverName || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ${record.amount || 'N/A'}`);
      console.log('   ' + '-'.repeat(50));
    });
    
    // Check distinct weaver names
    const distinctWeavers = await CarpetReceived.distinct('weaverName');
    console.log(`\n👥 All K-format weaver names:`);
    distinctWeavers.forEach((weaver, index) => {
      console.log(`   ${index + 1}. ${weaver}`);
    });
    
  } catch (error) {
    console.error('❌ Error verifying K-format display:', error);
  }
}

async function main() {
  console.log('🔄 FIXING WEAVER DISPLAY TO K-FORMAT');
  console.log('(K-Rahila, K-Jeet Narayan, K-Yaseen, etc.)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Create K-format weavers
    const createdWeavers = await createKFormatWeavers();
    console.log(`\n✅ Created/Found ${createdWeavers.length} K-format weavers`);

    // Update carpet received records
    const results = await updateCarpetReceivedWeavers(createdWeavers);

    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 K-FORMAT WEAVER UPDATE COMPLETE');
    console.log('='.repeat(60));
    console.log(`✅ Successfully updated: ${results.success.length} records`);
    console.log(`❌ Failed: ${results.errors.length} records`);
    
    if (results.success.length > 0) {
      console.log('\n✅ SAMPLE UPDATED RECORDS:');
      results.success.slice(0, 10).forEach(record => {
        console.log(`  - ${record.receiveNo}: ${record.oldWeaver} → ${record.newWeaver}`);
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.slice(0, 5).forEach(error => {
        console.log(`  - ${error.receiveNo}: ${error.error}`);
      });
    }
    
    // Verify K-format display
    await verifyKFormatDisplay();

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 K-FORMAT WEAVER DISPLAY FIXED!');
    console.log('✅ All records now have K-format weavers');
    console.log('✅ Frontend will show: MB001 - K-Rahila, MB001 - K-Jeet Narayan, etc.');
    console.log('✅ Proper weaver display restored!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
