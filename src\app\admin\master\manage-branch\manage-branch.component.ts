import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import { SweetalertService } from '../../../services/sweetalert.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

export interface UserData {
  id: string;
  index: number;
  branchName: string;
  branchCode: string;
}

const ELEMENT_DATA: UserData[] = [];
@Component({
  selector: 'app-manage-branch',
  templateUrl: './manage-branch.component.html',
  styleUrl: './manage-branch.component.css',
})
export class ManageBranchComponent implements OnInit {
  displayedColumns: string[] = ['id', 'branchName', 'branchCode', 'action'];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);

  constructor(
    private service: MasterService,
    private alert: SweetalertService,
    private fb: FormBuilder
  ) {}
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  isUpdated: boolean = false;
  editId!: string;
  frmBranch!: FormGroup;
  ngOnInit(): void {
    this.frmBranch = this.fb.group({
      branchName: ['',[Validators.required]],
      branchCode: ['',[Validators.required]],
    });

    this.gets();
  }
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  addBranch() {
    debugger;
    let formData = this.frmBranch.value;
    this.service.addBranch(formData).subscribe({
      next: (value) => {
        this.alert.success('Success', 'Branch has been saved');
        this.gets();
      },
      error: (err) => {
        this.alert.error('Failed !', 'Something went wrong');
      },
    });
  }

  gets() {
    this.service.getsBranch().subscribe({
      next: (value: any) => {
        ELEMENT_DATA.length = 0;
        value.map((v: any, i: number) => {
          ELEMENT_DATA.push({
            id: v._id,
            index: i + 1,
            branchName: v.branchName,
            branchCode: v.branchCode,
          });
        });

        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
      },
    });
  }

  edit(id: string) {
    this.service.getBranch(id).subscribe({
      next: (value) => {
        this.editId = id;
        this.isUpdated = true;
        this.frmBranch.patchValue(value);
      },
    });
  }
  delete(id: string) {
    this.alert.confirm({}).then((confi: any) => {
      if (confi.isConfirmed) {
        this.service.deleteBranch(id).subscribe({
          next: (value) => {
            this.alert.success('success', 'Branch has been deleted');
            this.gets();
          },
          error: (err) => {
            this.alert.error('Failed !', 'Something went wrong');
          },
        });
      }
    });
  }
  update() {
    let frmData = this.frmBranch.value;
    this.service.updateBranch(this.editId, frmData).subscribe({
      next: (value) => {
        this.alert.success('success', 'Branch has been updated');
        this.gets();
      },
      error: (err) => {
        this.alert.error('Failed !', 'Something went wrong');
      },
    });
  }
}
