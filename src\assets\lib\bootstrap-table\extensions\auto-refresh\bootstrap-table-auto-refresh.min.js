/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return c(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function s(){return s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=f(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},s.apply(this,arguments)}var l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},p=function(t){return t&&t.Math==Math&&t},h=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof l&&l)||function(){return this}()||Function("return this")(),b={},y=function(t){try{return!!t()}catch(t){return!0}},d=!y((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),v=!y((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),g=v,m=Function.prototype.call,O=g?m.bind(m):function(){return m.apply(m,arguments)},j={},w={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,R=S&&!w.call({1:2},1);j.f=R?function(t){var e=S(this,t);return!!e&&e.enumerable}:w;var P,T,A=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},E=v,x=Function.prototype,I=x.call,F=E&&x.bind.bind(I,I),k=function(t){return E?F(t):function(){return I.apply(t,arguments)}},_=k,C=_({}.toString),M=_("".slice),D=function(t){return M(C(t),8,-1)},L=D,z=k,B=function(t){if("Function"===L(t))return z(t)},N=y,q=D,G=Object,W=B("".split),H=N((function(){return!G("z").propertyIsEnumerable(0)}))?function(t){return"String"==q(t)?W(t,""):G(t)}:G,U=function(t){return null==t},$=U,X=TypeError,K=function(t){if($(t))throw X("Can't call method on "+t);return t},Q=H,V=K,Y=function(t){return Q(V(t))},J="object"==typeof document&&document.all,Z={all:J,IS_HTMLDDA:void 0===J&&void 0!==J},tt=Z.all,et=Z.IS_HTMLDDA?function(t){return"function"==typeof t||t===tt}:function(t){return"function"==typeof t},nt=et,rt=Z.all,ot=Z.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:nt(t)||t===rt}:function(t){return"object"==typeof t?null!==t:nt(t)},it=h,ut=et,ct=function(t){return ut(t)?t:void 0},at=function(t,e){return arguments.length<2?ct(it[t]):it[t]&&it[t][e]},ft=B({}.isPrototypeOf),st=h,lt=at("navigator","userAgent")||"",pt=st.process,ht=st.Deno,bt=pt&&pt.versions||ht&&ht.version,yt=bt&&bt.v8;yt&&(T=(P=yt.split("."))[0]>0&&P[0]<4?1:+(P[0]+P[1])),!T&&lt&&(!(P=lt.match(/Edge\/(\d+)/))||P[1]>=74)&&(P=lt.match(/Chrome\/(\d+)/))&&(T=+P[1]);var dt=T,vt=dt,gt=y,mt=!!Object.getOwnPropertySymbols&&!gt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&vt&&vt<41})),Ot=mt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,jt=at,wt=et,St=ft,Rt=Object,Pt=Ot?function(t){return"symbol"==typeof t}:function(t){var e=jt("Symbol");return wt(e)&&St(e.prototype,Rt(t))},Tt=String,At=et,Et=function(t){try{return Tt(t)}catch(t){return"Object"}},xt=TypeError,It=function(t){if(At(t))return t;throw xt(Et(t)+" is not a function")},Ft=It,kt=U,_t=O,Ct=et,Mt=ot,Dt=TypeError,Lt={exports:{}},zt=h,Bt=Object.defineProperty,Nt=function(t,e){try{Bt(zt,t,{value:e,configurable:!0,writable:!0})}catch(n){zt[t]=e}return e},qt=Nt,Gt="__core-js_shared__",Wt=h[Gt]||qt(Gt,{}),Ht=Wt;(Lt.exports=function(t,e){return Ht[t]||(Ht[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Ut=K,$t=Object,Xt=function(t){return $t(Ut(t))},Kt=Xt,Qt=B({}.hasOwnProperty),Vt=Object.hasOwn||function(t,e){return Qt(Kt(t),e)},Yt=B,Jt=0,Zt=Math.random(),te=Yt(1..toString),ee=function(t){return"Symbol("+(void 0===t?"":t)+")_"+te(++Jt+Zt,36)},ne=h,re=Lt.exports,oe=Vt,ie=ee,ue=mt,ce=Ot,ae=re("wks"),fe=ne.Symbol,se=fe&&fe.for,le=ce?fe:fe&&fe.withoutSetter||ie,pe=function(t){if(!oe(ae,t)||!ue&&"string"!=typeof ae[t]){var e="Symbol."+t;ue&&oe(fe,t)?ae[t]=fe[t]:ae[t]=ce&&se?se(e):le(e)}return ae[t]},he=O,be=ot,ye=Pt,de=function(t,e){var n=t[e];return kt(n)?void 0:Ft(n)},ve=function(t,e){var n,r;if("string"===e&&Ct(n=t.toString)&&!Mt(r=_t(n,t)))return r;if(Ct(n=t.valueOf)&&!Mt(r=_t(n,t)))return r;if("string"!==e&&Ct(n=t.toString)&&!Mt(r=_t(n,t)))return r;throw Dt("Can't convert object to primitive value")},ge=TypeError,me=pe("toPrimitive"),Oe=function(t,e){if(!be(t)||ye(t))return t;var n,r=de(t,me);if(r){if(void 0===e&&(e="default"),n=he(r,t,e),!be(n)||ye(n))return n;throw ge("Can't convert object to primitive value")}return void 0===e&&(e="number"),ve(t,e)},je=Pt,we=function(t){var e=Oe(t,"string");return je(e)?e:e+""},Se=ot,Re=h.document,Pe=Se(Re)&&Se(Re.createElement),Te=function(t){return Pe?Re.createElement(t):{}},Ae=Te,Ee=!d&&!y((function(){return 7!=Object.defineProperty(Ae("div"),"a",{get:function(){return 7}}).a})),xe=d,Ie=O,Fe=j,ke=A,_e=Y,Ce=we,Me=Vt,De=Ee,Le=Object.getOwnPropertyDescriptor;b.f=xe?Le:function(t,e){if(t=_e(t),e=Ce(e),De)try{return Le(t,e)}catch(t){}if(Me(t,e))return ke(!Ie(Fe.f,t,e),t[e])};var ze={},Be=d&&y((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Ne=ot,qe=String,Ge=TypeError,We=function(t){if(Ne(t))return t;throw Ge(qe(t)+" is not an object")},He=d,Ue=Ee,$e=Be,Xe=We,Ke=we,Qe=TypeError,Ve=Object.defineProperty,Ye=Object.getOwnPropertyDescriptor,Je="enumerable",Ze="configurable",tn="writable";ze.f=He?$e?function(t,e,n){if(Xe(t),e=Ke(e),Xe(n),"function"==typeof t&&"prototype"===e&&"value"in n&&tn in n&&!n.writable){var r=Ye(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Ze in n?n.configurable:r.configurable,enumerable:Je in n?n.enumerable:r.enumerable,writable:!1})}return Ve(t,e,n)}:Ve:function(t,e,n){if(Xe(t),e=Ke(e),Xe(n),Ue)try{return Ve(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Qe("Accessors not supported");return"value"in n&&(t[e]=n.value),t};var en=ze,nn=A,rn=d?function(t,e,n){return en.f(t,e,nn(1,n))}:function(t,e,n){return t[e]=n,t},on={exports:{}},un=d,cn=Vt,an=Function.prototype,fn=un&&Object.getOwnPropertyDescriptor,sn=cn(an,"name"),ln={EXISTS:sn,PROPER:sn&&"something"===function(){}.name,CONFIGURABLE:sn&&(!un||un&&fn(an,"name").configurable)},pn=et,hn=Wt,bn=B(Function.toString);pn(hn.inspectSource)||(hn.inspectSource=function(t){return bn(t)});var yn,dn,vn,gn=hn.inspectSource,mn=et,On=h.WeakMap,jn=mn(On)&&/native code/.test(String(On)),wn=Lt.exports,Sn=ee,Rn=wn("keys"),Pn=function(t){return Rn[t]||(Rn[t]=Sn(t))},Tn={},An=jn,En=h,xn=ot,In=rn,Fn=Vt,kn=Wt,_n=Pn,Cn=Tn,Mn="Object already initialized",Dn=En.TypeError,Ln=En.WeakMap;if(An||kn.state){var zn=kn.state||(kn.state=new Ln);zn.get=zn.get,zn.has=zn.has,zn.set=zn.set,yn=function(t,e){if(zn.has(t))throw Dn(Mn);return e.facade=t,zn.set(t,e),e},dn=function(t){return zn.get(t)||{}},vn=function(t){return zn.has(t)}}else{var Bn=_n("state");Cn[Bn]=!0,yn=function(t,e){if(Fn(t,Bn))throw Dn(Mn);return e.facade=t,In(t,Bn,e),e},dn=function(t){return Fn(t,Bn)?t[Bn]:{}},vn=function(t){return Fn(t,Bn)}}var Nn={set:yn,get:dn,has:vn,enforce:function(t){return vn(t)?dn(t):yn(t,{})},getterFor:function(t){return function(e){var n;if(!xn(e)||(n=dn(e)).type!==t)throw Dn("Incompatible receiver, "+t+" required");return n}}},qn=y,Gn=et,Wn=Vt,Hn=d,Un=ln.CONFIGURABLE,$n=gn,Xn=Nn.enforce,Kn=Nn.get,Qn=Object.defineProperty,Vn=Hn&&!qn((function(){return 8!==Qn((function(){}),"length",{value:8}).length})),Yn=String(String).split("String"),Jn=on.exports=function(t,e,n){"Symbol("===String(e).slice(0,7)&&(e="["+String(e).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(e="get "+e),n&&n.setter&&(e="set "+e),(!Wn(t,"name")||Un&&t.name!==e)&&(Hn?Qn(t,"name",{value:e,configurable:!0}):t.name=e),Vn&&n&&Wn(n,"arity")&&t.length!==n.arity&&Qn(t,"length",{value:n.arity});try{n&&Wn(n,"constructor")&&n.constructor?Hn&&Qn(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=Xn(t);return Wn(r,"source")||(r.source=Yn.join("string"==typeof e?e:"")),t};Function.prototype.toString=Jn((function(){return Gn(this)&&Kn(this).source||$n(this)}),"toString");var Zn=et,tr=ze,er=on.exports,nr=Nt,rr=function(t,e,n,r){r||(r={});var o=r.enumerable,i=void 0!==r.name?r.name:e;if(Zn(n)&&er(n,i,r),r.global)o?t[e]=n:nr(e,n);else{try{r.unsafe?t[e]&&(o=!0):delete t[e]}catch(t){}o?t[e]=n:tr.f(t,e,{value:n,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return t},or={},ir=Math.ceil,ur=Math.floor,cr=Math.trunc||function(t){var e=+t;return(e>0?ur:ir)(e)},ar=function(t){var e=+t;return e!=e||0===e?0:cr(e)},fr=ar,sr=Math.max,lr=Math.min,pr=ar,hr=Math.min,br=function(t){return t>0?hr(pr(t),9007199254740991):0},yr=function(t){return br(t.length)},dr=Y,vr=function(t,e){var n=fr(t);return n<0?sr(n+e,0):lr(n,e)},gr=yr,mr=function(t){return function(e,n,r){var o,i=dr(e),u=gr(i),c=vr(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},Or={includes:mr(!0),indexOf:mr(!1)},jr=Vt,wr=Y,Sr=Or.indexOf,Rr=Tn,Pr=B([].push),Tr=function(t,e){var n,r=wr(t),o=0,i=[];for(n in r)!jr(Rr,n)&&jr(r,n)&&Pr(i,n);for(;e.length>o;)jr(r,n=e[o++])&&(~Sr(i,n)||Pr(i,n));return i},Ar=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Er=Tr,xr=Ar.concat("length","prototype");or.f=Object.getOwnPropertyNames||function(t){return Er(t,xr)};var Ir={};Ir.f=Object.getOwnPropertySymbols;var Fr=at,kr=or,_r=Ir,Cr=We,Mr=B([].concat),Dr=Fr("Reflect","ownKeys")||function(t){var e=kr.f(Cr(t)),n=_r.f;return n?Mr(e,n(t)):e},Lr=Vt,zr=Dr,Br=b,Nr=ze,qr=y,Gr=et,Wr=/#|\.prototype\./,Hr=function(t,e){var n=$r[Ur(t)];return n==Kr||n!=Xr&&(Gr(e)?qr(e):!!e)},Ur=Hr.normalize=function(t){return String(t).replace(Wr,".").toLowerCase()},$r=Hr.data={},Xr=Hr.NATIVE="N",Kr=Hr.POLYFILL="P",Qr=Hr,Vr=h,Yr=b.f,Jr=rn,Zr=rr,to=Nt,eo=function(t,e,n){for(var r=zr(e),o=Nr.f,i=Br.f,u=0;u<r.length;u++){var c=r[u];Lr(t,c)||n&&Lr(n,c)||o(t,c,i(e,c))}},no=Qr,ro=function(t,e){var n,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(n=a?Vr:f?Vr[c]||to(c,{}):(Vr[c]||{}).prototype)for(r in e){if(i=e[r],o=t.dontCallGetSet?(u=Yr(n,r))&&u.value:n[r],!no(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;eo(i,o)}(t.sham||o&&o.sham)&&Jr(i,"sham",!0),Zr(n,r,i,t)}},oo=D,io=Array.isArray||function(t){return"Array"==oo(t)},uo=TypeError,co=we,ao=ze,fo=A,so={};so[pe("toStringTag")]="z";var lo="[object z]"===String(so),po=lo,ho=et,bo=D,yo=pe("toStringTag"),vo=Object,go="Arguments"==bo(function(){return arguments}()),mo=po?bo:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=vo(t),yo))?n:go?bo(e):"Object"==(r=bo(e))&&ho(e.callee)?"Arguments":r},Oo=B,jo=y,wo=et,So=mo,Ro=gn,Po=function(){},To=[],Ao=at("Reflect","construct"),Eo=/^\s*(?:class|function)\b/,xo=Oo(Eo.exec),Io=!Eo.exec(Po),Fo=function(t){if(!wo(t))return!1;try{return Ao(Po,To,t),!0}catch(t){return!1}},ko=function(t){if(!wo(t))return!1;switch(So(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Io||!!xo(Eo,Ro(t))}catch(t){return!0}};ko.sham=!0;var _o=!Ao||jo((function(){var t;return Fo(Fo.call)||!Fo(Object)||!Fo((function(){t=!0}))||t}))?ko:Fo,Co=io,Mo=_o,Do=ot,Lo=pe("species"),zo=Array,Bo=function(t){var e;return Co(t)&&(e=t.constructor,(Mo(e)&&(e===zo||Co(e.prototype))||Do(e)&&null===(e=e[Lo]))&&(e=void 0)),void 0===e?zo:e},No=function(t,e){return new(Bo(t))(0===e?0:e)},qo=y,Go=dt,Wo=pe("species"),Ho=ro,Uo=y,$o=io,Xo=ot,Ko=Xt,Qo=yr,Vo=function(t){if(t>9007199254740991)throw uo("Maximum allowed index exceeded");return t},Yo=function(t,e,n){var r=co(e);r in t?ao.f(t,r,fo(0,n)):t[r]=n},Jo=No,Zo=function(t){return Go>=51||!qo((function(){var e=[];return(e.constructor={})[Wo]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},ti=dt,ei=pe("isConcatSpreadable"),ni=ti>=51||!Uo((function(){var t=[];return t[ei]=!1,t.concat()[0]!==t})),ri=Zo("concat"),oi=function(t){if(!Xo(t))return!1;var e=t[ei];return void 0!==e?!!e:$o(t)};Ho({target:"Array",proto:!0,arity:1,forced:!ni||!ri},{concat:function(t){var e,n,r,o,i,u=Ko(this),c=Jo(u,0),a=0;for(e=-1,r=arguments.length;e<r;e++)if(oi(i=-1===e?u:arguments[e]))for(o=Qo(i),Vo(a+o),n=0;n<o;n++,a++)n in i&&Yo(c,a,i[n]);else Vo(a+1),Yo(c,a++,i);return c.length=a,c}});var ii=Tr,ui=Ar,ci=Object.keys||function(t){return ii(t,ui)},ai=d,fi=B,si=O,li=y,pi=ci,hi=Ir,bi=j,yi=Xt,di=H,vi=Object.assign,gi=Object.defineProperty,mi=fi([].concat),Oi=!vi||li((function(){if(ai&&1!==vi({b:1},vi(gi({},"a",{enumerable:!0,get:function(){gi(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!=vi({},t)[n]||pi(vi({},e)).join("")!=r}))?function(t,e){for(var n=yi(t),r=arguments.length,o=1,i=hi.f,u=bi.f;r>o;)for(var c,a=di(arguments[o++]),f=i?mi(pi(a),i(a)):pi(a),s=f.length,l=0;s>l;)c=f[l++],ai&&!si(u,a,c)||(n[c]=a[c]);return n}:vi,ji=Oi;ro({target:"Object",stat:!0,arity:2,forced:Object.assign!==ji},{assign:ji});var wi=It,Si=v,Ri=B(B.bind),Pi=function(t,e){return wi(t),void 0===e?t:Si?Ri(t,e):function(){return t.apply(e,arguments)}},Ti=H,Ai=Xt,Ei=yr,xi=No,Ii=B([].push),Fi=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,s,l){for(var p,h,b=Ai(a),y=Ti(b),d=Pi(f,s),v=Ei(y),g=0,m=l||xi,O=e?m(a,v):n||u?m(a,0):void 0;v>g;g++)if((c||g in y)&&(h=d(p=y[g],g,b),t))if(e)O[g]=h;else if(h)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:Ii(O,p)}else switch(t){case 4:return!1;case 7:Ii(O,p)}return i?-1:r||o?o:O}},ki={forEach:Fi(0),map:Fi(1),filter:Fi(2),some:Fi(3),every:Fi(4),find:Fi(5),findIndex:Fi(6),filterReject:Fi(7)},_i={},Ci=d,Mi=Be,Di=ze,Li=We,zi=Y,Bi=ci;_i.f=Ci&&!Mi?Object.defineProperties:function(t,e){Li(t);for(var n,r=zi(e),o=Bi(e),i=o.length,u=0;i>u;)Di.f(t,n=o[u++],r[n]);return t};var Ni,qi=at("document","documentElement"),Gi=We,Wi=_i,Hi=Ar,Ui=Tn,$i=qi,Xi=Te,Ki=Pn("IE_PROTO"),Qi=function(){},Vi=function(t){return"<script>"+t+"</"+"script>"},Yi=function(t){t.write(Vi("")),t.close();var e=t.parentWindow.Object;return t=null,e},Ji=function(){try{Ni=new ActiveXObject("htmlfile")}catch(t){}var t,e;Ji="undefined"!=typeof document?document.domain&&Ni?Yi(Ni):((e=Xi("iframe")).style.display="none",$i.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Vi("document.F=Object")),t.close(),t.F):Yi(Ni);for(var n=Hi.length;n--;)delete Ji.prototype[Hi[n]];return Ji()};Ui[Ki]=!0;var Zi=pe,tu=Object.create||function(t,e){var n;return null!==t?(Qi.prototype=Gi(t),n=new Qi,Qi.prototype=null,n[Ki]=t):n=Ji(),void 0===e?n:Wi.f(n,e)},eu=ze.f,nu=Zi("unscopables"),ru=Array.prototype;null==ru[nu]&&eu(ru,nu,{configurable:!0,value:tu(null)});var ou=ro,iu=ki.find,uu=function(t){ru[nu][t]=!0},cu="find",au=!0;cu in[]&&Array(1).find((function(){au=!1})),ou({target:"Array",proto:!0,forced:au},{find:function(t){return iu(this,t,arguments.length>1?arguments[1]:void 0)}}),uu(cu);var fu=mo,su=lo?{}.toString:function(){return"[object "+fu(this)+"]"};lo||rr(Object.prototype,"toString",su,{unsafe:!0});var lu=n.default.fn.bootstrapTable.utils;n.default.extend(n.default.fn.bootstrapTable.defaults,{autoRefresh:!1,showAutoRefresh:!0,autoRefreshInterval:60,autoRefreshSilent:!0,autoRefreshStatus:!0,autoRefreshFunction:null}),n.default.extend(n.default.fn.bootstrapTable.defaults.icons,{autoRefresh:{bootstrap3:"glyphicon-time icon-time",bootstrap5:"bi-clock",materialize:"access_time","bootstrap-table":"icon-clock"}[n.default.fn.bootstrapTable.theme]||"fa-clock"}),n.default.extend(n.default.fn.bootstrapTable.locales,{formatAutoRefresh:function(){return"Auto Refresh"}}),n.default.extend(n.default.fn.bootstrapTable.defaults,n.default.fn.bootstrapTable.locales),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(l,t);var e,n,c,f=a(l);function l(){return r(this,l),f.apply(this,arguments)}return e=l,n=[{key:"init",value:function(){for(var t,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=s(i(l.prototype),"init",this)).call.apply(t,[this].concat(n)),this.options.autoRefresh&&this.options.autoRefreshStatus&&this.setupRefreshInterval()}},{key:"initToolbar",value:function(){var t;this.options.autoRefresh&&(this.buttons=Object.assign(this.buttons,{autoRefresh:{html:'\n            <button class="auto-refresh '.concat(this.constants.buttonsClass,"\n              ").concat(this.options.autoRefreshStatus?" ".concat(this.constants.classes.buttonActive):"",'"\n              type="button" name="autoRefresh" title="').concat(this.options.formatAutoRefresh(),'">\n              ').concat(this.options.showButtonIcons?lu.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.autoRefresh):"","\n              ").concat(this.options.showButtonText?this.options.formatAutoRefresh():"","\n            </button>\n           "),event:this.toggleAutoRefresh}}));for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=s(i(l.prototype),"initToolbar",this)).call.apply(t,[this].concat(n))}},{key:"toggleAutoRefresh",value:function(){this.options.autoRefresh&&(this.options.autoRefreshStatus?(clearInterval(this.options.autoRefreshFunction),this.$toolbar.find(">.columns .auto-refresh").removeClass(this.constants.classes.buttonActive)):(this.setupRefreshInterval(),this.$toolbar.find(">.columns .auto-refresh").addClass(this.constants.classes.buttonActive)),this.options.autoRefreshStatus=!this.options.autoRefreshStatus)}},{key:"destroy",value:function(){this.options.autoRefresh&&this.options.autoRefreshStatus&&clearInterval(this.options.autoRefreshFunction),s(i(l.prototype),"destroy",this).call(this)}},{key:"setupRefreshInterval",value:function(){var t=this;this.options.autoRefreshFunction=setInterval((function(){t.options.autoRefresh&&t.options.autoRefreshStatus&&t.refresh({silent:t.options.autoRefreshSilent})}),1e3*this.options.autoRefreshInterval)}}],n&&o(e.prototype,n),c&&o(e,c),Object.defineProperty(e,"prototype",{writable:!1}),l}(n.default.BootstrapTable)}));
