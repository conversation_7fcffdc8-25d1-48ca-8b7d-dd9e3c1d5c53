import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment.development';
import { Observable } from 'rxjs';
@Injectable({
  providedIn: 'root',
})
export class MasterService {
  apiUrl: any = environment.apiUrl;
  phaseOne: any = `${this.apiUrl}/phase-one`;
  phaseTwo: any = `${this.apiUrl}/phase-two`;
  phaseThree: any = `${this.apiUrl}/phase-three`;
  phaseFour:any=`${this.apiUrl}/phase-four`;
  constructor(private http: HttpClient) {}

  /////////////////////////////////////Buyer service///////////////////////////////////

  addBuyer(data: any): Observable<any> {
    return this.http.post(`${this.phaseThree}/buyer/craete-buyer`, data);
  }
  getAllBuyerList(): Observable<any> {
    return this.http.get(`${this.phaseThree}/buyer/getAll-buyer`);
  }
  deleteBuyer(id: any): Observable<any> {
    return this.http.delete(`${this.phaseThree}/buyer/delete-buyer/${id}`);
  }

  updateBuyer(id: any, data: any): Observable<any> {
    return this.http.put<any>(
      `${this.phaseThree}/buyer/update-buyer/${id}`,
      data
    );
  }
  /////////////////////////////////////Quality service///////////////////////////////////
  addquality(data: any): Observable<any> {
    return this.http.post(`${this.phaseTwo}/quality/create-quality`, data);
  }
  getqualityList(): Observable<any> {
    return this.http.get(`${this.phaseTwo}/quality/getAll-quality`);
  }
  deletequality(id: any): Observable<any> {
    return this.http.delete(`${this.phaseTwo}/quality/delete-quality/${id}`);
  }
  updatequality(id: any, data: any): Observable<any> {
    return this.http.put<any>(
      `${this.phaseTwo}/quality/update-quality/${id}`,
      data
    );
  }
  ///////////////////////////////////Design service///////////////////////////////////////////

  addDesign(data: any): Observable<any> {
    return this.http.post(`${this.phaseTwo}/design/add-design`, data);
  }
  getAllDesignList(): Observable<any> {
    return this.http.get(`${this.phaseTwo}/design/designs`);
  }

  getDesign(id:string){
    return this.http.get(`${this.phaseTwo}/design/designs/${id}`);
  }
  deleteDesign(id: any): Observable<any> {
    return this.http.delete(`${this.phaseTwo}/design/designs/${id}`);
  }
  updateDesign(id: any, data: any): Observable<any> {
    return this.http.put<any>(`${this.phaseTwo}/design/designs/${id}`, data);
  }

  ///////////////////////////////////// Master Size service///////////////////////////////////

  addSizes(data: any): Observable<any> {
    return this.http.post(`${this.phaseThree}/sizeMaster/create-sizes`, data);
  }
  getAllSizesList(): Observable<any> {
    return this.http.get(`${this.phaseThree}/sizeMaster/getAll-sizes`);
  }
  deleteSizes(id: any): Observable<any> {
    return this.http.delete(`${this.phaseThree}/sizeMaster/delete-sizes/${id}`);
  }
  updateSizes(id: any, data: any): Observable<any> {
    return this.http.put<any>(
      `${this.phaseThree}/sizeMaster/update-sizes/${id}`,
      data
    );
  }

  addWeaverEmployee(data: any) {
    return this.http.post(`${this.phaseThree}/weaver/employees`, data);
  }

  getAllWeaverEmployee() {
    return this.http.get(`${this.phaseThree}/weaver/employees`);
  }

  getWeaverEmployee(id: string) {
    return this.http.get(`${this.phaseThree}/weaver/employees/${id}`);
  }
  updateWeaverEmployee(id: string, data: any) {
    return this.http.put(`${this.phaseThree}/weaver/employees/${id}`, data);
  }
  deleteWeaverEmployee(id: string) {
    return this.http.delete(`${this.phaseThree}/weaver/employees/${id}`);
  }

  // purchaseDetails/purchase-details

  addPurchaseDetails(data: any) {
    return this.http.post(
      `${this.phaseThree}/purchaserDetails/purchaser-details`,
      data
    );
  }
  getsPurchaseDetails() {
    return this.http.get(`${this.phaseThree}/purchaserDetails/purchaser-details`);
  }
  getPurchaseDetails(id: any) {
    return this.http.get(
      `${this.phaseThree}/purchaserDetails/purchaser-details/${id}`
    );
  }
  updatePurchaseDetails(id: string, data: any) {
    return this.http.put(
      `${this.phaseThree}/purchaserDetails/purchaser-details/${id}`,
      data
    );
  }
  deletePurchaseDetails(id: any) {
    return this.http.delete(
      `${this.phaseThree}/purchaserDetails/purchaser-details/${id}`
    );
  }

  getsRawMaterial(){
    return this.http.get(`${this.phaseThree}/rawMaterial/raw-material-groups`);
  }

  getRawMaterial(id:string){
    return this.http.get(`${this.phaseThree}/rawMaterial/raw-material-groups/${id}`);
  }
  addRawMaterial(data:any){
    return this.http.post(`${this.phaseThree}/rawMaterial/raw-material-groups`,data);
  }
  updateRawMaterial(id:any, data:any){
    return this.http.put(`${this.phaseThree}/rawMaterial/raw-material-groups/${id}`,data);
  }
  deleteRawMaterial(id:any){
    return this.http.delete(`${this.phaseThree}/rawMaterial/raw-material-groups/${id}`);
  }



  addColourDetails(data:any){
    return this.http.post(`${this.phaseOne}/color/create-color`,data);
  }
  getsColourCodeDetails(){
    return this.http.get(`${this.phaseOne}/color/get-all-colors`);
  }
  getColourCodeDetail(id:string){
    return this.http.get(`${this.phaseOne}/color/get-color/${id}`);
  }
  deleteColourDetail(id:string){
    return this.http.delete(`${this.phaseOne}/color/delete-color/${id}`);
  }
  updateColorCodeDetails(id:any, data:any){
    return this.http.put(`${this.phaseOne}/color/update-color/${id}`,data)
  }

  addBranch(data:any){
    return this.http.post(`${this.phaseThree}/branch/branches`,data);
  }
  getsBranch(){
    return this.http.get(`${this.phaseThree}/branch/branches`);
  }
  getBranch(id:string){
    return this.http.get(`${this.phaseThree}/branch/branches/${id}`);
  }
  deleteBranch(id:string){
    return this.http.delete(`${this.phaseThree}/branch/branches/${id}`);
  }
  updateBranch(id:string , data:any){
    return this.http.put(`${this.phaseThree}/branch/branches/${id}`,data);
  }




  addDyeingDetail(data:any){
    return this.http.post(`${this.phaseThree }/colourcodeDeying/colourcodeDeying`,data);
  }

  getsDyeingDetail(){
    return this.http.get(`${this.phaseThree }/colourcodeDeying/colourcodeDeying`);
  }
  getDyeingDetail(id:any){
    return this.http.get(`${this.phaseThree }/colourcodeDeying/colourcodeDeying/${id}`,);
  }
  deleteDyeingDetail(id:any){
    return this.http.delete(`${this.phaseThree }/colourcodeDeying/colourcodeDeying/${id}`);
  }
  updateDyeingDetail(id:any, data:any){
    return this.http.put(`${this.phaseThree }/colourcodeDeying/colourcodeDeying/${id}`,data);
  }

  addDyeingRate(data:any){
    return this.http.post(`${this.phaseThree}/deyingRate/deyingRate/`,data);
  }
  getsDyeingRate(){
    return this.http.get(`${this.phaseThree}/deyingRate/deyingRate`);
  }
  getDyeingRate(id:string){
    return this.http.get(`${this.phaseThree}/deyingRate/deyingRate/${id}`);
  }
  deleteDyeingRate(id:string){
    return this.http.delete(`${this.phaseThree}/deyingRate/deyingRate/${id}`);
  }
  updateDyeingRate(id:string,data:any){
    return this.http.put(`${this.phaseThree}/deyingRate/deyingRate/${id}`,data);
  }


  addMapMaster(data:any){
    return this.http.post(`${this.phaseThree}/mapMaster/add-mapMaster`,data);
  }
  getsMapMaster(){
    return this.http.get(`${this.phaseThree}/mapMaster/mapMasters`);
  }
  getMapMaster(id:string){
    return this.http.get(`${this.phaseThree}/mapMaster/mapMasters/${id}`);
  }

  deleteMapMaster(id:string){
    return this.http.delete(`${this.phaseThree}/mapMaster/mapMasters/${id}`);
  }
  updateMapMaster(id:string, data:any){
    return this.http.put(`${this.phaseThree}/mapMaster/mapMasters/${id}`,data)
  }
  getImageUrl(imageName: string): string {
debugger
    return `http://localhost:2000/images/${imageName}`;

  }
  addMapRate(data:any){
    return this.http.post(`${this.phaseThree}/mapRate/mapRates`,data);
  }
  getMapRate(id:any){
    return this.http.get(`${this.phaseThree}/mapRate/mapRates/${id}`);
  }
  getsMapRate(){
    return this.http.get(`${this.phaseThree}/mapRate/mapRates`);
  }
  updateMapRate(id:string,data:any){
    return this.http.put(`${this.phaseThree}/mapRate/mapRates/${id}`,data);
  }
  deleteMapRate(id:any){
    return this.http.delete(`${this.phaseThree}/mapRate/mapRates/${id}`);
  }


  addMaterialLagat(data:any){
    return this.http.post(`${this.phaseThree}/materialLagat/materialLagat`,data);
  }
  getMaterialLagat(id:any){
    return this.http.get(`${this.phaseThree}/materialLagat/materialLagat/${id}`);
  }
getsMaterialLagat(){
    return this.http.get(`${this.phaseThree}/materialLagat/materialLagat`);
  }

  deleteMaterialLagat(id:string){
    return this.http.delete(`${this.phaseThree}/materialLagat/materialLagat/${id}`);
  }
  updateMaterialLagat(id:string,data:any){
    return this.http.put(`${this.phaseThree}/materialLagat/materialLagat/${id}`,data);
  }


  addBuyerOrder(data:any){
    return this.http.post(`${this.phaseFour}/buyerOrder/BuyerOrder`,data);
  }
  getsBuyerOrder(){
    return this.http.get(`${this.phaseFour}/buyerOrder/BuyerOrder`);
  }
  getBuyerOrder(id:string){
    return this.http.get(`${this.phaseFour}/buyerOrder/BuyerOrder/${id}`);
  }
  deleteBuyerOrder(id:any){
    return this.http.delete(`${this.phaseFour}/buyerOrder/BuyerOrder/${id}`);
  }
  updateBuyerOrder(id:string,data:any){
    return this.http.put(`${this.phaseFour}/buyerOrder/BuyerOrder/${id}`,data);
  }


}
