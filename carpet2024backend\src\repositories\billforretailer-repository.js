const BillForretailer = require("../model/phase-2/billretailer");
const { CreateChallan } = require("../model/phase-2/createChallan");
class BillForretailerRepository {
  async create(billData) {
    try {
      return await BillForretailer.create(billData);
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      return await BillForretailer.find();
    } catch (error) {
      throw error;
    }
  }

  async findById(id) {
    try {
      return await BillForretailer.findById(id);
    } catch (error) {
      throw error;
    }
  }

  async update(id, billData) {
    // try {
    //   let flag = 0;
    //   let updated = await BillForretailer.findByIdAndUpdate(id, billData, {
    //     new: true,
    //   });
    //   if (updated) {
    //     billData.challanNo.forEach(async (ch) => {
    //       let updatedChallan = await CreateChallan.updateOne(
    //         { challanNo: ch },
    //         {
    //           $set: {
    //             // isDeleted: true,
    //             isBillCreated: true,
    //           },
    //         }
    //       );
    //       if (
    //         updatedChallan.acknowledged === true &&
    //         updatedChallan.modifiedCount == 1
    //       ) {
    //         flag = 1;
    //       }
    //     });
    //     if(flag==1){
    //       return updated
    //     }else{
    //       return
    //     }
    //   }
    // } catch (error) {
    //   throw error;
    // }

    try {
      let updated = await BillForretailer.findByIdAndUpdate(id, billData, {
        new: true,
      });
    
      if (updated) {
        let flag = 0;
        const updateChallanPromises = billData.challanNo.map(async (ch) => {
          let updatedChallan = await CreateChallan.updateOne(
            { challanNo: ch },
            {
              $set: {
                isBillCreated: true,
              },
            }
          );
    
          if (
            updatedChallan.acknowledged === true &&
            updatedChallan.modifiedCount === 1
          ) {
            flag = 1;
          }
        });
    
        await Promise.all(updateChallanPromises);
    
        if (flag === 1) {
          return updated;
        } else {
          throw new Error('Failed to update all challans');
        }
      } else {
        throw new Error('Failed to update bill');
      }
    } catch (error) {
      throw error;
    }
    
  }

  async delete(id) {
    try {
      let flag = 0;
      let ids = id.split(" ");
      let billDetails = await BillForretailer.find({ _id: ids[0] });
      if (ids.length > 1) {
        let billData = await BillForretailer.updateOne(
          { _id: ids[0] },
          { $pull: { challanNo: ids[1] } }
        );
        if (billData.acknowledged === true && billData.modifiedCount == 1) {
          let getChallan = await CreateChallan.find({ challanNo: ids[1] });
          if (getChallan[0].isDeleted == true) {
            return billData;
          } else {
            let updatedChallan = await CreateChallan.updateOne(
              { challanNo: ids[1] },
              {
                $set: {
                  // isDeleted: false,
                  isBillCreated: false,
                },
              }
            );
            if (
              updatedChallan.acknowledged === true &&
              updatedChallan.modifiedCount == 1
            ) {
              return billData;
            }
          }
        }
      } else {
        for (const i of billDetails[0].challanNo) {
          let getChallan = await CreateChallan.find({ challanNo: i });
          if (
            getChallan[0].isDeleted !== true &&
            getChallan[0].isBillCreated === true
          ) {
            let updatedChallan = await CreateChallan.updateOne(
              { challanNo: i },
              {
                $set: {
                  // isDeleted: true,
                  isBillCreated: false,
                },
              }
            );
            if (
              updatedChallan.acknowledged === true &&
              updatedChallan.modifiedCount == 1
            ) {
              let upb = await BillForretailer.updateOne(
                { _id: ids[0] },
                { $pull: { challanNo: i } }
              );
              console.log(upb);
              flag = 1;
            }
          }
        }
        if (flag == 1) {
          let billDeleted = await BillForretailer.updateOne(
            { _id: ids[0] },
            {
              $set: {
                isBillDeleted: true,
              },
            }
          );

          if (
            billDeleted.acknowledged === true &&
            billDeleted.modifiedCount == 1
          ) {
            return billDeleted;
          }
        }
      }
      //   return await BillForretailer.findByIdAndDelete(id);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = BillForretailerRepository;
