<table mat-table [dataSource]="dataSource" class="mat-elevation-z8">

    <!--- Note that these columns can be defined in any order.
          The actual rendered columns are set as a property on the row definition" -->
  
    <!-- Position Column -->
    <ng-container matColumnDef="areaOfUnit">
      <th mat-header-cell *matHeaderCellDef> Area Of Unit </th>
      <td mat-cell *matCellDef="let element"> {{element.areaOfUnit}} </td>
    </ng-container>
  
    <!-- Name Column -->
    <ng-container matColumnDef="impotererNo">
      <th mat-header-cell *matHeaderCellDef> Impoterer No </th>
      <td mat-cell *matCellDef="let element"> {{element.impotererNo}} </td>
    </ng-container>
  
    <!-- Weight Column -->
    <ng-container matColumnDef="impotererName">
      <th mat-header-cell *matHeaderCellDef> Impoterer Name </th>
      <td mat-cell *matCellDef="let element"> {{element.impotererName}} </td>
    </ng-container>
    
    
    
    
    
    

    <ng-container matColumnDef="currency">
        <th mat-header-cell *matHeaderCellDef> Currency </th>
        <td mat-cell *matCellDef="let element"> {{element.currency}} </td>
      </ng-container>

      <ng-container matColumnDef="quantity">
        <th mat-header-cell *matHeaderCellDef> Quantity </th>
        <td mat-cell *matCellDef="let element"> {{element.quantity}} </td>
      </ng-container>

      <ng-container matColumnDef="totalArea">
        <th mat-header-cell *matHeaderCellDef> Total Area </th>
        <td mat-cell *matCellDef="let element"> {{element.totalArea}} </td>
      </ng-container>

      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef> Amount </th>
        <td mat-cell *matCellDef="let element"> {{element.amount}} </td>
      </ng-container>

      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef> Sr No. </th>
        <td mat-cell *matCellDef="let element"> {{element.index}} </td>
      </ng-container>
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef> Action</th>
        <td mat-cell *matCellDef="let element"><a routerLink="../import-invoices/{{element.id}}"><i class="fa fa-pencil-square-o" aria-hidden="true"></i></a> <a (click)="deleteImporterInvoice(element.id)"><i class="fa fa-trash-o" aria-hidden="true"></i></a></td>
      </ng-container>
    <!-- Symbol Column -->
    <ng-container matColumnDef="invoiceNo">
      <th mat-header-cell *matHeaderCellDef> Invoice No </th>
      <td mat-cell *matCellDef="let element"> {{element.invoiceNo}} </td>
    </ng-container>
  
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>
  <mat-paginator [length]="100"
              [pageSize]="10"
              [pageSizeOptions]="[5, 10, 25, 100]"
              aria-label="Select page">
</mat-paginator>

  