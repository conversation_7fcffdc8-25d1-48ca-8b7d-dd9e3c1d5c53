<div class="container mt-4">
    <section>
      <fieldset> <legend><b>Import Invoice Report</b></legend>
        <div class="row">
          <div class="col-md-12">
              <mat-form-field appearance="outline">
                  <mat-label>Search</mat-label>
                  <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Mia" #input>
                </mat-form-field>
                
                <div class="mat-elevation-z8" style="overflow: auto;">
                  <table mat-table [dataSource]="dataSource" matSort>
                
                    <!-- ID Column -->
                    <ng-container matColumnDef="srNo">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Sr.No</th>
                      <td mat-cell *matCellDef="let row"> {{row.srNo}} </td>
                    </ng-container>
                
                    <!-- Progress Column -->
                    <ng-container matColumnDef="importerNo">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Importer No.</th>
                      <td mat-cell *matCellDef="let row"> {{row.importerNo}}</td>
                    </ng-container>
                
                    <!-- Name Column -->
                    <ng-container matColumnDef="importerName">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Importer Name</th>
                      <td mat-cell *matCellDef="let row" style="text-transform: capitalize;"> {{row.importerName}} </td>
                    </ng-container>
                
                    <!-- Fruit Column -->
                    <ng-container matColumnDef="invoiceNo">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Invoice No.</th>
                      <td mat-cell *matCellDef="let row"> {{row.invoiceNo}} </td>
                    </ng-container>
  
                    <!-- <ng-container matColumnDef="chooseDate">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Choose Date</th>
                      <td mat-cell *matCellDef="let row"> {{row.chooseDate}} </td>
                    </ng-container>
   -->
                  
  
                    <ng-container matColumnDef="areaUnit">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Area of Unit</th>
                      <td mat-cell *matCellDef="let row"> {{row.areaUnit}} </td>
                    </ng-container>
                    <ng-container matColumnDef="currency">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Currency</th>
                      <td mat-cell *matCellDef="let row"> {{row.currency}} </td>
                    </ng-container>
  
                    <ng-container matColumnDef="quality">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
                      <td mat-cell *matCellDef="let row"> {{row.quality}} </td>
                    </ng-container>
                    <ng-container matColumnDef="totalArea">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Total Area</th>
                      <td mat-cell *matCellDef="let row"> {{row.totalArea}} </td>
                    </ng-container>
  
                    <!-- <ng-container matColumnDef="area">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>area</th>
                      <td mat-cell *matCellDef="let row"> {{row.area}} </td>
                    </ng-container> -->
  
                    <ng-container matColumnDef="amount">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Amount</th>
                      <td mat-cell *matCellDef="let row"> {{row.amount}} </td>
                    </ng-container>
  
                    <ng-container matColumnDef="action">
                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Action </th>
                      <td mat-cell *matCellDef="let row"> {{row.action}} </td>
                    </ng-container>
                
                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                
                    <!-- Row shown when there is no matching data. -->
                    <tr class="mat-row" *matNoDataRow>
                      <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>
                    </tr>
                  </table>
                
                  <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>
                </div>
                
          </div>
        </div>
  
      </fieldset>
    </section>
  </div>
  

<p>import-invoice-report works!</p>
dfghjk