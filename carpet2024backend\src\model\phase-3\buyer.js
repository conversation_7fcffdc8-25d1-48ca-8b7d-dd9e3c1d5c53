// models/Buyer.js
const mongoose = require('mongoose');

const buyerSchema = new mongoose.Schema({
    customerCode: {
        type: String,
        
    },
    customerName: {
        type: String,
         
    },
    customerAddress: {
        type: String,
         
    },
    country: {
        type: String,
        
    },
    zipCode: {
        type: String,
        
    },
    customerNo: {
        type: String,
         
    },
    customerEmail: {
        type: String,
        
    },
    bankDetails: {
        bankName: {
            type: String,
             
        },
        bankAddress: {
            type: String,
             
        },
        bankContactNo:{
            type: String
        },

        bankCountry: {
            type: String,
             
        },
        bankEmail: {
            type: String,
            
        },
        branch: {
            type: String,
            
        },
        swiftCode: {
            type: String,
             
        },
        bankZipCode:{
            type: String,
        }
    },
    numberOfStores: {
        type: Number,
         
    }
});

const Buyer = mongoose.model('Buyer', buyerSchema);

module.exports = Buyer;
