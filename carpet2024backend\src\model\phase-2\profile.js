const mongoose = require("mongoose");

const addressSchema = new mongoose.Schema({
  customerName:String,
  customerCode:String,
  email:String,
  userAddress: String,
  zipCode: String,
  country: String,
  contactPersonNa: String,
});

const bankDataSchema = new mongoose.Schema({
  accountNumber: String,
  bankAddress: String,
  bankName: String,
  branch: String,
  swiftCode: String,
  bankEmail: String,
  bankContactNo: String,
  noOfStore: String
});


const profileSchema = new mongoose.Schema({
  address: {
    type: addressSchema,
  },
  bankData: {
    type: bankDataSchema,
  },
  userCode: {
    type:String,
  },
  userType: {
    type: String,
  },

  contactNo: {
    type: String,
  }, 
  
  alternateContactNo: {
    type: String,
  },
  gstnNo: {
    type: String,
  },
  countryOfBelonging: {
    type: String,
  },
});
// // Middleware to lowercase userType before saving
// profileSchema.pre("save", function (next) {
//   if (this.userType) this.userType = this.userType.toLowerCase();
//   next();
// });

const Profile = mongoose.model("Profile", profileSchema);

module.exports = Profile;
