const finishingHead = require('../model/phase-3/finishingHead');

const createFinishingHead = async (data) => {
  return await finishingHead.create(data);
};

const getAllFinishingHeads = async () => {
  return await finishingHead.find()
    .populate('quality')
    .populate('design');
};

const getFinishingHeadById = async (id) => {
  return await finishingHead.findById(id)
    .populate('quality')
    .populate('design');
};

const updateFinishingHead = async (id, data) => {
  return await finishingHead.findByIdAndUpdate(id, data, { new: true })

};

const deleteFinishingHead = async (id) => {
  return await finishingHead.findByIdAndDelete(id);
};

module.exports = {
  createFinishingHead,
  getAllFinishingHeads,
  getFinishingHeadById,
  updateFinishingHead,
  deleteFinishingHead
};
