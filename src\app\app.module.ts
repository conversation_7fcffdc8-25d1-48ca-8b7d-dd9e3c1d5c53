import { NgModule } from '@angular/core';
import { BrowserModule, provideClientHydration } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { NotfoundComponent } from './notfound/notfound.component';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { HttpClientModule } from '@angular/common/http';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { MyStandaloneComponentComponent } from './my-standalone-component/my-standalone-component.component';
import { NgxUiLoaderConfig, NgxUiLoaderModule, PB_DIRECTION } from 'ngx-ui-loader';
import { RouteReuseStrategy } from '@angular/router';
import { CustomRouteReuseStrategy } from './Shared/customeReuseRoute';
import { DatePipePipe } from './pipe/date-pipe.pipe';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CarpetBorderComponent } from './admin/manufacturing/view-main-carpet-reciving.component/modals/carpet-border/carpet-border.component';
import { MatRadioModule } from '@angular/material/radio';



const ngxUiLoaderConfig: NgxUiLoaderConfig = {
  bgsColor: 'red',
  text:'Loading',
  textColor:'#85c5ed',
  textPosition:'center-center',
  pbColor:'#85c5ed',
  fgsColor:'#85c5ed',
  fgsType:'three-strings',
  fgsSize:100,
  pbDirection:PB_DIRECTION.leftToRight,
  pbThickness:5,
  };
@NgModule({
  declarations: [
    AppComponent,
    // ExportInvoiceComponent,
    NotfoundComponent,
      MyStandaloneComponentComponent,
      DatePipePipe,
      CarpetBorderComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTableModule,
    MatDialogModule,
    MatIconModule,
    MatTooltipModule,
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    NgxUiLoaderModule.forRoot(ngxUiLoaderConfig),BrowserModule,
    MatCardModule,
    MatRadioModule
  ],
  providers: [
    // provideClientHydration(),
    provideAnimationsAsync(),
    {provide: MAT_DATE_LOCALE, useValue: 'en-GB' },
    // { provide: RouteReuseStrategy, useClass: CustomRouteReuseStrategy }
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }


