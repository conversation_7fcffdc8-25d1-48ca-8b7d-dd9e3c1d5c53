const express = require('express');
const router = express.Router();
const materialLagatController = require('../../controller/materialLagat-controller');

// POST /api/material-lagats
router.post('/materialLagat', materialLagatController.createMaterialLagat);

// GET /api/material-lagats
router.get('/materialLagat', materialLagatController.getAllMaterialLagats);

// GET /api/material-lagats/:id
router.get('/materialLagat/:id', materialLagatController.getMaterialLagatById);

// PUT /api/material-lagats/:id
router.put('/materialLagat/:id', materialLagatController.updateMaterialLagat);

// DELETE /api/material-lagats/:id
router.delete('/materialLagat/:id', materialLagatController.deleteMaterialLagat);

// PATCH /api/material-lagats/:id/update-field
router.patch('/materialLagat/:id/update-field', materialLagatController.updateField);

// PATCH /api/material-lagats/:id/delete-field
router.patch('/materialLagat/:id/delete-field', materialLagatController.deleteField);

module.exports = router;
