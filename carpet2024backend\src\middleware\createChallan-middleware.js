// middleware/checkDuplicateChallanNo.js

const { CreateChallan } = require('../model/phase-2/createChallan');

const checkDuplicateChallanNo = async (req, res, next) => {
    try {
        const { challanNo } = req.body;
        if (!challanNo) {
            return res.status(400).json({ message: 'Challan number is required' });
        }

        const existingChallan = await CreateChallan.findOne({ challanNo });
        if (existingChallan) {
            return res.status(400).json({ message: 'Duplicate challan number. Please use a different challan number.' });
        }

        next();
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

module.exports = checkDuplicateChallanNo;
