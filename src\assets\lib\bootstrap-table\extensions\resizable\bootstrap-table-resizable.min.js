/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return c(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=f(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},p=function(t){return t&&t.Math==Math&&t},y=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof s&&s)||function(){return this}()||Function("return this")(),b={},h=function(t){try{return!!t()}catch(t){return!0}},v=!h((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),d=!h((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),g=d,m=Function.prototype.call,w=g?m.bind(m):function(){return m.apply(m,arguments)},O={},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,P=S&&!j.call({1:2},1);O.f=P?function(t){var e=S(this,t);return!!e&&e.enumerable}:j;var T,E,A=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},x=d,_=Function.prototype,z=_.call,C=x&&_.bind.bind(z,z),M=function(t){return x?C(t):function(){return z.apply(t,arguments)}},R=M,D=R({}.toString),F=R("".slice),I=function(t){return F(D(t),8,-1)},k=I,L=M,B=function(t){if("Function"===k(t))return L(t)},N=h,V=I,$=Object,G=B("".split),q=N((function(){return!$("z").propertyIsEnumerable(0)}))?function(t){return"String"==V(t)?G(t,""):$(t)}:$,H=function(t){return null==t},U=H,W=TypeError,K=function(t){if(U(t))throw W("Can't call method on "+t);return t},Q=q,X=K,Y=function(t){return Q(X(t))},J="object"==typeof document&&document.all,Z={all:J,IS_HTMLDDA:void 0===J&&void 0!==J},tt=Z.all,et=Z.IS_HTMLDDA?function(t){return"function"==typeof t||t===tt}:function(t){return"function"==typeof t},nt=et,rt=Z.all,ot=Z.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:nt(t)||t===rt}:function(t){return"object"==typeof t?null!==t:nt(t)},it=y,ut=et,ct=function(t){return ut(t)?t:void 0},at=function(t,e){return arguments.length<2?ct(it[t]):it[t]&&it[t][e]},ft=B({}.isPrototypeOf),lt=y,st=at("navigator","userAgent")||"",pt=lt.process,yt=lt.Deno,bt=pt&&pt.versions||yt&&yt.version,ht=bt&&bt.v8;ht&&(E=(T=ht.split("."))[0]>0&&T[0]<4?1:+(T[0]+T[1])),!E&&st&&(!(T=st.match(/Edge\/(\d+)/))||T[1]>=74)&&(T=st.match(/Chrome\/(\d+)/))&&(E=+T[1]);var vt=E,dt=vt,gt=h,mt=!!Object.getOwnPropertySymbols&&!gt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&dt&&dt<41})),wt=mt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Ot=at,jt=et,St=ft,Pt=Object,Tt=wt?function(t){return"symbol"==typeof t}:function(t){var e=Ot("Symbol");return jt(e)&&St(e.prototype,Pt(t))},Et=String,At=et,xt=function(t){try{return Et(t)}catch(t){return"Object"}},_t=TypeError,zt=function(t){if(At(t))return t;throw _t(xt(t)+" is not a function")},Ct=H,Mt=w,Rt=et,Dt=ot,Ft=TypeError,It={exports:{}},kt=y,Lt=Object.defineProperty,Bt=function(t,e){try{Lt(kt,t,{value:e,configurable:!0,writable:!0})}catch(n){kt[t]=e}return e},Nt=Bt,Vt="__core-js_shared__",$t=y[Vt]||Nt(Vt,{}),Gt=$t;(It.exports=function(t,e){return Gt[t]||(Gt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var qt=K,Ht=Object,Ut=function(t){return Ht(qt(t))},Wt=Ut,Kt=B({}.hasOwnProperty),Qt=Object.hasOwn||function(t,e){return Kt(Wt(t),e)},Xt=B,Yt=0,Jt=Math.random(),Zt=Xt(1..toString),te=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Zt(++Yt+Jt,36)},ee=y,ne=It.exports,re=Qt,oe=te,ie=mt,ue=wt,ce=ne("wks"),ae=ee.Symbol,fe=ae&&ae.for,le=ue?ae:ae&&ae.withoutSetter||oe,se=function(t){if(!re(ce,t)||!ie&&"string"!=typeof ce[t]){var e="Symbol."+t;ie&&re(ae,t)?ce[t]=ae[t]:ce[t]=ue&&fe?fe(e):le(e)}return ce[t]},pe=w,ye=ot,be=Tt,he=function(t,e){var n=t[e];return Ct(n)?void 0:zt(n)},ve=function(t,e){var n,r;if("string"===e&&Rt(n=t.toString)&&!Dt(r=Mt(n,t)))return r;if(Rt(n=t.valueOf)&&!Dt(r=Mt(n,t)))return r;if("string"!==e&&Rt(n=t.toString)&&!Dt(r=Mt(n,t)))return r;throw Ft("Can't convert object to primitive value")},de=TypeError,ge=se("toPrimitive"),me=function(t,e){if(!ye(t)||be(t))return t;var n,r=he(t,ge);if(r){if(void 0===e&&(e="default"),n=pe(r,t,e),!ye(n)||be(n))return n;throw de("Can't convert object to primitive value")}return void 0===e&&(e="number"),ve(t,e)},we=Tt,Oe=function(t){var e=me(t,"string");return we(e)?e:e+""},je=ot,Se=y.document,Pe=je(Se)&&je(Se.createElement),Te=function(t){return Pe?Se.createElement(t):{}},Ee=!v&&!h((function(){return 7!=Object.defineProperty(Te("div"),"a",{get:function(){return 7}}).a})),Ae=v,xe=w,_e=O,ze=A,Ce=Y,Me=Oe,Re=Qt,De=Ee,Fe=Object.getOwnPropertyDescriptor;b.f=Ae?Fe:function(t,e){if(t=Ce(t),e=Me(e),De)try{return Fe(t,e)}catch(t){}if(Re(t,e))return ze(!xe(_e.f,t,e),t[e])};var Ie={},ke=v&&h((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Le=ot,Be=String,Ne=TypeError,Ve=function(t){if(Le(t))return t;throw Ne(Be(t)+" is not an object")},$e=v,Ge=Ee,qe=ke,He=Ve,Ue=Oe,We=TypeError,Ke=Object.defineProperty,Qe=Object.getOwnPropertyDescriptor,Xe="enumerable",Ye="configurable",Je="writable";Ie.f=$e?qe?function(t,e,n){if(He(t),e=Ue(e),He(n),"function"==typeof t&&"prototype"===e&&"value"in n&&Je in n&&!n.writable){var r=Qe(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Ye in n?n.configurable:r.configurable,enumerable:Xe in n?n.enumerable:r.enumerable,writable:!1})}return Ke(t,e,n)}:Ke:function(t,e,n){if(He(t),e=Ue(e),He(n),Ge)try{return Ke(t,e,n)}catch(t){}if("get"in n||"set"in n)throw We("Accessors not supported");return"value"in n&&(t[e]=n.value),t};var Ze=Ie,tn=A,en=v?function(t,e,n){return Ze.f(t,e,tn(1,n))}:function(t,e,n){return t[e]=n,t},nn={exports:{}},rn=v,on=Qt,un=Function.prototype,cn=rn&&Object.getOwnPropertyDescriptor,an=on(un,"name"),fn={EXISTS:an,PROPER:an&&"something"===function(){}.name,CONFIGURABLE:an&&(!rn||rn&&cn(un,"name").configurable)},ln=et,sn=$t,pn=B(Function.toString);ln(sn.inspectSource)||(sn.inspectSource=function(t){return pn(t)});var yn,bn,hn,vn=sn.inspectSource,dn=et,gn=y.WeakMap,mn=dn(gn)&&/native code/.test(String(gn)),wn=It.exports,On=te,jn=wn("keys"),Sn={},Pn=mn,Tn=y,En=ot,An=en,xn=Qt,_n=$t,zn=function(t){return jn[t]||(jn[t]=On(t))},Cn=Sn,Mn="Object already initialized",Rn=Tn.TypeError,Dn=Tn.WeakMap;if(Pn||_n.state){var Fn=_n.state||(_n.state=new Dn);Fn.get=Fn.get,Fn.has=Fn.has,Fn.set=Fn.set,yn=function(t,e){if(Fn.has(t))throw Rn(Mn);return e.facade=t,Fn.set(t,e),e},bn=function(t){return Fn.get(t)||{}},hn=function(t){return Fn.has(t)}}else{var In=zn("state");Cn[In]=!0,yn=function(t,e){if(xn(t,In))throw Rn(Mn);return e.facade=t,An(t,In,e),e},bn=function(t){return xn(t,In)?t[In]:{}},hn=function(t){return xn(t,In)}}var kn={set:yn,get:bn,has:hn,enforce:function(t){return hn(t)?bn(t):yn(t,{})},getterFor:function(t){return function(e){var n;if(!En(e)||(n=bn(e)).type!==t)throw Rn("Incompatible receiver, "+t+" required");return n}}},Ln=h,Bn=et,Nn=Qt,Vn=v,$n=fn.CONFIGURABLE,Gn=vn,qn=kn.enforce,Hn=kn.get,Un=Object.defineProperty,Wn=Vn&&!Ln((function(){return 8!==Un((function(){}),"length",{value:8}).length})),Kn=String(String).split("String"),Qn=nn.exports=function(t,e,n){"Symbol("===String(e).slice(0,7)&&(e="["+String(e).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(e="get "+e),n&&n.setter&&(e="set "+e),(!Nn(t,"name")||$n&&t.name!==e)&&(Vn?Un(t,"name",{value:e,configurable:!0}):t.name=e),Wn&&n&&Nn(n,"arity")&&t.length!==n.arity&&Un(t,"length",{value:n.arity});try{n&&Nn(n,"constructor")&&n.constructor?Vn&&Un(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=qn(t);return Nn(r,"source")||(r.source=Kn.join("string"==typeof e?e:"")),t};Function.prototype.toString=Qn((function(){return Bn(this)&&Hn(this).source||Gn(this)}),"toString");var Xn=et,Yn=Ie,Jn=nn.exports,Zn=Bt,tr={},er=Math.ceil,nr=Math.floor,rr=Math.trunc||function(t){var e=+t;return(e>0?nr:er)(e)},or=function(t){var e=+t;return e!=e||0===e?0:rr(e)},ir=or,ur=Math.max,cr=Math.min,ar=or,fr=Math.min,lr=function(t){return t>0?fr(ar(t),9007199254740991):0},sr=function(t){return lr(t.length)},pr=Y,yr=function(t,e){var n=ir(t);return n<0?ur(n+e,0):cr(n,e)},br=sr,hr=function(t){return function(e,n,r){var o,i=pr(e),u=br(i),c=yr(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},vr={includes:hr(!0),indexOf:hr(!1)},dr=Qt,gr=Y,mr=vr.indexOf,wr=Sn,Or=B([].push),jr=function(t,e){var n,r=gr(t),o=0,i=[];for(n in r)!dr(wr,n)&&dr(r,n)&&Or(i,n);for(;e.length>o;)dr(r,n=e[o++])&&(~mr(i,n)||Or(i,n));return i},Sr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");tr.f=Object.getOwnPropertyNames||function(t){return jr(t,Sr)};var Pr={};Pr.f=Object.getOwnPropertySymbols;var Tr=at,Er=tr,Ar=Pr,xr=Ve,_r=B([].concat),zr=Tr("Reflect","ownKeys")||function(t){var e=Er.f(xr(t)),n=Ar.f;return n?_r(e,n(t)):e},Cr=Qt,Mr=zr,Rr=b,Dr=Ie,Fr=h,Ir=et,kr=/#|\.prototype\./,Lr=function(t,e){var n=Nr[Br(t)];return n==$r||n!=Vr&&(Ir(e)?Fr(e):!!e)},Br=Lr.normalize=function(t){return String(t).replace(kr,".").toLowerCase()},Nr=Lr.data={},Vr=Lr.NATIVE="N",$r=Lr.POLYFILL="P",Gr=Lr,qr=y,Hr=b.f,Ur=en,Wr=function(t,e,n,r){r||(r={});var o=r.enumerable,i=void 0!==r.name?r.name:e;if(Xn(n)&&Jn(n,i,r),r.global)o?t[e]=n:Zn(e,n);else{try{r.unsafe?t[e]&&(o=!0):delete t[e]}catch(t){}o?t[e]=n:Yn.f(t,e,{value:n,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return t},Kr=Bt,Qr=function(t,e,n){for(var r=Mr(e),o=Dr.f,i=Rr.f,u=0;u<r.length;u++){var c=r[u];Cr(t,c)||n&&Cr(n,c)||o(t,c,i(e,c))}},Xr=Gr,Yr=I,Jr=Array.isArray||function(t){return"Array"==Yr(t)},Zr=TypeError,to=Oe,eo=Ie,no=A,ro={};ro[se("toStringTag")]="z";var oo="[object z]"===String(ro),io=et,uo=I,co=se("toStringTag"),ao=Object,fo="Arguments"==uo(function(){return arguments}()),lo=B,so=h,po=et,yo=oo?uo:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=ao(t),co))?n:fo?uo(e):"Object"==(r=uo(e))&&io(e.callee)?"Arguments":r},bo=vn,ho=function(){},vo=[],go=at("Reflect","construct"),mo=/^\s*(?:class|function)\b/,wo=lo(mo.exec),Oo=!mo.exec(ho),jo=function(t){if(!po(t))return!1;try{return go(ho,vo,t),!0}catch(t){return!1}},So=function(t){if(!po(t))return!1;switch(yo(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Oo||!!wo(mo,bo(t))}catch(t){return!0}};So.sham=!0;var Po=!go||so((function(){var t;return jo(jo.call)||!jo(Object)||!jo((function(){t=!0}))||t}))?So:jo,To=Jr,Eo=Po,Ao=ot,xo=se("species"),_o=Array,zo=function(t){var e;return To(t)&&(e=t.constructor,(Eo(e)&&(e===_o||To(e.prototype))||Ao(e)&&null===(e=e[xo]))&&(e=void 0)),void 0===e?_o:e},Co=h,Mo=vt,Ro=se("species"),Do=function(t,e){var n,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(n=a?qr:f?qr[c]||Kr(c,{}):(qr[c]||{}).prototype)for(r in e){if(i=e[r],o=t.dontCallGetSet?(u=Hr(n,r))&&u.value:n[r],!Xr(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Qr(i,o)}(t.sham||o&&o.sham)&&Ur(i,"sham",!0),Wr(n,r,i,t)}},Fo=h,Io=Jr,ko=ot,Lo=Ut,Bo=sr,No=function(t){if(t>9007199254740991)throw Zr("Maximum allowed index exceeded");return t},Vo=function(t,e,n){var r=to(e);r in t?eo.f(t,r,no(0,n)):t[r]=n},$o=function(t,e){return new(zo(t))(0===e?0:e)},Go=function(t){return Mo>=51||!Co((function(){var e=[];return(e.constructor={})[Ro]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},qo=vt,Ho=se("isConcatSpreadable"),Uo=qo>=51||!Fo((function(){var t=[];return t[Ho]=!1,t.concat()[0]!==t})),Wo=Go("concat"),Ko=function(t){if(!ko(t))return!1;var e=t[Ho];return void 0!==e?!!e:Io(t)};Do({target:"Array",proto:!0,arity:1,forced:!Uo||!Wo},{concat:function(t){var e,n,r,o,i,u=Lo(this),c=$o(u,0),a=0;for(e=-1,r=arguments.length;e<r;e++)if(Ko(i=-1===e?u:arguments[e]))for(o=Bo(i),No(a+o),n=0;n<o;n++,a++)n in i&&Vo(c,a,i[n]);else No(a+1),Vo(c,a++,i);return c.length=a,c}});var Qo=function(t){return void 0!==t.$el.data("resizableColumns")},Xo=function(t){t.options.resizable&&!t.options.cardView&&!Qo(t)&&t.$el.is(":visible")&&t.$el.resizableColumns({store:window.store})},Yo=function(t){Qo(t)&&t.$el.data("resizableColumns").destroy()},Jo=function(t){Yo(t),Xo(t)};n.default.extend(n.default.fn.bootstrapTable.defaults,{resizable:!1}),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(s,t);var e,n,c,f=a(s);function s(){return r(this,s),f.apply(this,arguments)}return e=s,n=[{key:"initBody",value:function(){for(var t,e=this,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];(t=l(i(s.prototype),"initBody",this)).call.apply(t,[this].concat(r)),this.$el.off("column-switch.bs.table page-change.bs.table").on("column-switch.bs.table page-change.bs.table",(function(){Jo(e)})),Jo(this)}},{key:"toggleView",value:function(){for(var t,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=l(i(s.prototype),"toggleView",this)).call.apply(t,[this].concat(n)),this.options.resizable&&this.options.cardView&&Yo(this)}},{key:"resetView",value:function(){for(var t,e=this,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];(t=l(i(s.prototype),"resetView",this)).call.apply(t,[this].concat(r)),this.options.resizable&&setTimeout((function(){Xo(e)}),100)}}],n&&o(e.prototype,n),c&&o(e,c),Object.defineProperty(e,"prototype",{writable:!1}),s}(n.default.BootstrapTable)}));
