const mapRate = require('../model/phase-3/mapRate');

const createMapRate = async (data) => {
  return await mapRate.create(data);
};

const getAllMapRates = async () => {
  return await mapRate.find()
    .populate('quality')
    .populate('design')
    .populate('name');
};

const getMapRateById = async (id) => {
  return await mapRate.findById(id);
   
};

const updateMapRate = async (id, data) => {
  return await mapRate.findByIdAndUpdate(id, data, { new: true })
  
};

const deleteMapRate = async (id) => {
  return await mapRate.findByIdAndDelete(id);
};

module.exports = {
  createMapRate,
  getAllMapRates,
  getMapRateById,
  updateMapRate,
  deleteMapRate
};
