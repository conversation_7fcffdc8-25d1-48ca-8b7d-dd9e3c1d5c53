<div class="container mt-5">
    <section>
        <fieldset>
            <legend><b>Container-Despatch-Report</b></legend>
            <div class="row">
                <div class="col-md-12">
            <mat-form-field appearance="outline">
                <mat-label>Search</mat-label>
                <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Jack" #input>
              </mat-form-field>
              
              <div class="mat-elevation-z8" class="scroll">
                <table mat-table [dataSource]="dataSource" matSort>
              

                    <!-- ID Column -->
                  <ng-container matColumnDef="No">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> No </th>
                    <td mat-cell *matCellDef="let row"> {{row.No}} </td>
                  </ng-container>


                  <!-- ID Column -->
                  <ng-container matColumnDef="containerNo">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Container No. </th>
                    <td mat-cell *matCellDef="let row"> {{row.containerNo}} </td>
                  </ng-container>
              
                  <!-- Group Column -->
                  <ng-container matColumnDef="expenseAmount">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Expense Amount </th>
                    <td mat-cell *matCellDef="let row"> {{row.expenseAmount}}</td>
                  </ng-container>
              
                  <!-- Item Column -->
                  <!-- <ng-container matColumnDef="uploadPdf">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Upload PDF</th>
                    <td mat-cell *matCellDef="let row"> {{row.uploadPdf}} </td>
                  </ng-container> -->
              
                  <!-- Count Column -->
                  <ng-container matColumnDef="totalArea">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Total Area</th>
                    <td mat-cell *matCellDef="let row"> {{row.totalArea}} </td>
                  </ng-container>
      
                 <!-- Colour Column -->
                 <ng-container matColumnDef="EspPrice">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>ESP Price</th>
                    <td mat-cell *matCellDef="let row"> {{row.EspPrice}} </td>
                  </ng-container>
      
      
                      <ng-container matColumnDef="Action">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                        <td mat-cell *matCellDef="let row">
                           <!-- <a > <i class="fa fa-eye" data-toggle="modal" data-target="#exampleModal" aria-hidden="true"></i></a> -->
                          </td>
                      </ng-container>
                  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
              
                  <!-- Row shown when there is no matching data. -->
                  <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>
                  </tr>
                </table>
              
                <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>
                </div>
            </div>
        </div>
        </fieldset>
    </section>

</div>











<!--  here code for set modal dialog  -->

<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Modal title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>Modal body text goes here.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary">Save changes</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>