const express = require('express');
const { uploadPdf } = require('../../middleware/fileUpload-middleware');
const pdfDetailesController=require('../../controller/pdfDetails-controller');

const router = express.Router();

router.post('/upload-invoice-pdf', uploadPdf.single('invoicePdf'),pdfDetailesController.createPdf);
router.get('/get-all-invoice-pdf', pdfDetailesController.getAllPdf);
 router.get('/get-invoice-pdf/:id', pdfDetailesController.getPdfById);
 router.put('/update-invoice-pdf/:id', pdfDetailesController.updatePdf);
 router.delete('/delete-invoice-pdf/:id', pdfDetailesController.deletePdf);

module.exports = router;  
