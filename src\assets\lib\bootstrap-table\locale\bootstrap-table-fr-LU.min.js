/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},h={}.propertyIsEnumerable,y=Object.getOwnPropertyDescriptor,b=y&&!h.call({1:2},1);g.f=b?function(t){var n=y(this,t);return!!n&&n.enumerable}:h;var m,d,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),C=T("".slice),E=function(t){return C(A(t),8,-1)},x=E,F=P,R=function(t){if("Function"===x(t))return F(t)},M=c,D=E,I=Object,L=R("".split),N=M((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"==D(t)?L(t,""):I(t)}:I,_=function(t){return null==t},k=_,z=TypeError,U=function(t){if(k(t))throw z("Can't call method on "+t);return t},q=N,G=U,B=function(t){return q(G(t))},H="object"==typeof document&&document.all,W={all:H,IS_HTMLDDA:void 0===H&&void 0!==H},J=W.all,K=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===J}:function(t){return"function"==typeof t},Q=K,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,$=K,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=R({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(d=(m=ct.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!d&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(d=+m[1]);var at=d,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,ht=K,yt=nt,bt=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return ht(n)&&yt(n.prototype,bt(t))},dt=String,vt=K,St=function(t){try{return dt(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=_,Pt=p,Tt=K,At=X,Ct=TypeError,Et={exports:{}},xt=i,Ft=Object.defineProperty,Rt=function(t,n){try{Ft(xt,t,{value:n,configurable:!0,writable:!0})}catch(r){xt[t]=n}return n},Mt=Rt,Dt="__core-js_shared__",It=i[Dt]||Mt(Dt,{}),Lt=It;(Et.exports=function(t,n){return Lt[t]||(Lt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Nt=U,_t=Object,kt=function(t){return _t(Nt(t))},zt=kt,Ut=R({}.hasOwnProperty),qt=Object.hasOwn||function(t,n){return Ut(zt(t),n)},Gt=R,Bt=0,Ht=Math.random(),Wt=Gt(1..toString),Jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Bt+Ht,36)},Kt=i,Qt=Et.exports,Vt=qt,Xt=Jt,Yt=st,$t=pt,Zt=Qt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,cn=mt,an=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw Ct("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=mt,hn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},yn=X,bn=i.document,mn=yn(bn)&&yn(bn.createElement),dn=function(t){return mn?bn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(dn("div"),"a",{get:function(){return 7}}).a})),Sn=a,wn=p,On=g,jn=v,Pn=B,Tn=hn,An=qt,Cn=vn,En=Object.getOwnPropertyDescriptor;u.f=Sn?En:function(t,n){if(t=Pn(t),n=Tn(n),Cn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var xn={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Rn=X,Mn=String,Dn=TypeError,In=function(t){if(Rn(t))return t;throw Dn(Mn(t)+" is not an object")},Ln=a,Nn=vn,_n=Fn,kn=In,zn=hn,Un=TypeError,qn=Object.defineProperty,Gn=Object.getOwnPropertyDescriptor,Bn="enumerable",Hn="configurable",Wn="writable";xn.f=Ln?_n?function(t,n,r){if(kn(t),n=zn(n),kn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=Gn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Hn in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return qn(t,n,r)}:qn:function(t,n,r){if(kn(t),n=zn(n),kn(r),Nn)try{return qn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Un("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Jn=xn,Kn=v,Qn=a?function(t,n,r){return Jn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=a,Yn=qt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=K,er=It,or=R(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,gr=Jt,hr=pr("keys"),yr={},br=sr,mr=i,dr=X,vr=Qn,Sr=qt,wr=It,Or=function(t){return hr[t]||(hr[t]=gr(t))},jr=yr,Pr="Object already initialized",Tr=mr.TypeError,Ar=mr.WeakMap;if(br||wr.state){var Cr=wr.state||(wr.state=new Ar);Cr.get=Cr.get,Cr.has=Cr.has,Cr.set=Cr.set,ir=function(t,n){if(Cr.has(t))throw Tr(Pr);return n.facade=t,Cr.set(t,n),n},ur=function(t){return Cr.get(t)||{}},cr=function(t){return Cr.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(Sr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return Sr(t,Er)?t[Er]:{}},cr=function(t){return Sr(t,Er)}}var xr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!dr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=c,Rr=K,Mr=qt,Dr=a,Ir=nr.CONFIGURABLE,Lr=ar,Nr=xr.enforce,_r=xr.get,kr=Object.defineProperty,zr=Dr&&!Fr((function(){return 8!==kr((function(){}),"length",{value:8}).length})),Ur=String(String).split("String"),qr=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Mr(t,"name")||Ir&&t.name!==n)&&(Dr?kr(t,"name",{value:n,configurable:!0}):t.name=n),zr&&r&&Mr(r,"arity")&&t.length!==r.arity&&kr(t,"length",{value:r.arity});try{r&&Mr(r,"constructor")&&r.constructor?Dr&&kr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Nr(t);return Mr(e,"source")||(e.source=Ur.join("string"==typeof n?n:"")),t};Function.prototype.toString=qr((function(){return Rr(this)&&_r(this).source||Lr(this)}),"toString");var Gr=K,Br=xn,Hr=Vn.exports,Wr=Rt,Jr={},Kr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Kr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=qt,le=B,se=ae.indexOf,pe=yr,ge=R([].push),he=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},ye=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Jr.f=Object.getOwnPropertyNames||function(t){return he(t,ye)};var be={};be.f=Object.getOwnPropertySymbols;var me=tt,de=Jr,ve=be,Se=In,we=R([].concat),Oe=me("Reflect","ownKeys")||function(t){var n=de.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},je=qt,Pe=Oe,Te=u,Ae=xn,Ce=c,Ee=K,xe=/#|\.prototype\./,Fe=function(t,n){var r=Me[Re(t)];return r==Ie||r!=De&&(Ee(n)?Ce(n):!!n)},Re=Fe.normalize=function(t){return String(t).replace(xe,".").toLowerCase()},Me=Fe.data={},De=Fe.NATIVE="N",Ie=Fe.POLYFILL="P",Le=Fe,Ne=i,_e=u.f,ke=Qn,ze=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Gr(r)&&Hr(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},Ue=Rt,qe=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var c=e[u];je(t,c)||r&&je(r,c)||o(t,c,i(n,c))}},Ge=Le,Be=E,He=Array.isArray||function(t){return"Array"==Be(t)},We=TypeError,Je=hn,Ke=xn,Qe=v,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=K,$e=E,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=R,eo=c,oo=K,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},ho=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};ho.sham=!0;var yo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?ho:go,bo=He,mo=yo,vo=X,So=en("species"),wo=Array,Oo=function(t){var n;return bo(t)&&(n=t.constructor,(mo(n)&&(n===wo||bo(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=c,Po=at,To=en("species"),Ao=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Ne:f?Ne[c]||Ue(c,{}):(Ne[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=_e(r,e))&&u.value:r[e],!Ge(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;qe(i,o)}(t.sham||o&&o.sham)&&ke(i,"sham",!0),ze(r,e,i,t)}},Co=c,Eo=He,xo=X,Fo=kt,Ro=ee,Mo=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Do=function(t,n,r){var e=Je(n);e in t?Ke.f(t,e,Qe(0,r)):t[e]=r},Io=function(t,n){return new(Oo(t))(0===n?0:n)},Lo=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},No=at,_o=en("isConcatSpreadable"),ko=No>=51||!Co((function(){var t=[];return t[_o]=!1,t.concat()[0]!==t})),zo=Lo("concat"),Uo=function(t){if(!xo(t))return!1;var n=t[_o];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!ko||!zo},{concat:function(t){var n,r,e,o,i,u=Fo(this),c=Io(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(Uo(i=-1===n?u:arguments[n]))for(o=Ro(i),Mo(a+o),r=0;r<o;r++,a++)r in i&&Do(c,a,i[r]);else Mo(a+1),Do(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["fr-LU"]={formatCopyRows:function(){return"Copier les lignes"},formatPrint:function(){return"Imprimer"},formatLoadingMessage:function(){return"Chargement en cours"},formatRecordsPerPage:function(t){return"".concat(t," lignes par page")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Affiche de ".concat(t," à ").concat(n," sur ").concat(r," lignes (filtrés à partir de ").concat(e," lignes)"):"Affiche de ".concat(t," à ").concat(n," sur ").concat(r," lignes")},formatSRPaginationPreText:function(){return"page précédente"},formatSRPaginationPageText:function(t){return"vers la page ".concat(t)},formatSRPaginationNextText:function(){return"page suivante"},formatDetailPagination:function(t){return"Affiche ".concat(t," lignes")},formatClearSearch:function(){return"Effacer la recherche"},formatSearch:function(){return"Recherche"},formatNoMatches:function(){return"Pas de lignes trouvés"},formatPaginationSwitch:function(){return"Cacher/Afficher pagination"},formatPaginationSwitchDown:function(){return"Afficher pagination"},formatPaginationSwitchUp:function(){return"Cacher pagination"},formatRefresh:function(){return"Rafraichir"},formatToggleOn:function(){return"Afficher vue carte"},formatToggleOff:function(){return"Cacher vue carte"},formatColumns:function(){return"Colonnes"},formatColumnsToggleAll:function(){return"Tout basculer"},formatFullscreen:function(){return"Plein écran"},formatAllRows:function(){return"Tout"},formatAutoRefresh:function(){return"Rafraîchissement automatique"},formatExport:function(){return"Exporter les données"},formatJumpTo:function(){return"Aller à"},formatAdvancedSearch:function(){return"Recherche avancée"},formatAdvancedCloseButton:function(){return"Fermer"},formatFilterControlSwitch:function(){return"Cacher/Afficher controls"},formatFilterControlSwitchHide:function(){return"Cacher controls"},formatFilterControlSwitchShow:function(){return"Afficher controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fr-LU"])}));
