const colourcodeDeying = require('../model/phase-3/colourDeying-master');

const createColourcodeDeying = async (data) => {
  return await colourcodeDeying.create(data);
};

const getAllColourcodeDeyings = async () => {
  return await colourcodeDeying.find()
    .populate('quality')
    .populate('Color')
    .populate('material')
    .exec();
};

const getColourcodeDeyingById = async (id) => {
  return await colourcodeDeying.findById(id) .populate('quality')
  .populate('Color')
  .populate('material')
  .exec();;
};



const updateColourcodeDeying = async (id, data) => {
  return await colourcodeDeying.findByIdAndUpdate(id, data, { new: true });
};

const deleteColourcodeDeying = async (id) => {
  return await colourcodeDeying.findByIdAndDelete(id).exec();
};

const updateField = async (id, update) => {
  return await colourcodeDeying.findByIdAndUpdate(id, { $set: update }, { new: true });
};

const deleteField = async (id, field) => {
  return await colourcodeDeying.findByIdAndUpdate(id, { $unset: { [field]: "" } }, { new: true });
};

module.exports = {
  createColourcodeDeying,
  getAllColourcodeDeyings,
  getColourcodeDeyingById,
  updateColourcodeDeying,
  deleteColourcodeDeying,
  updateField,
  deleteField
};
