import { Component, Inject, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import Swal from 'sweetalert2';

interface CarpetBorderData {
  quality: string;
  design: string;
  color: string;
  size: string;
  carpetNo?: string;
  carpetReceivedId?: string;
  existingBorderData?: any; // For loading existing border data
}

@Component({
  selector: 'app-carpet-border',
  templateUrl: './carpet-border.component.html',
  styleUrl: './carpet-border.component.css'
})
export class CarpetBorderComponent implements AfterViewInit {
  borderForm: FormGroup;
  get boxArray() {
    return this.borderForm.get('boxArray') as FormArray;
  }
  get imagesArray() {
    return this.borderForm.get('imagesArray') as FormArray;
  }
  images: { file?: File, preview?: string }[] = [ { } ];
  previewImage: string | null = null;

  @ViewChild('weightInput') weightInput!: ElementRef;
  @ViewChild('bottomBorder1Input') bottomBorder1Input!: ElementRef;
  @ViewChild('bottomBorder2Input') bottomBorder2Input!: ElementRef;
  @ViewChild('topBorder1Input') topBorder1Input!: ElementRef;
  @ViewChild('topBorder2Input') topBorder2Input!: ElementRef;
  @ViewChild('rightBorder1Input') rightBorder1Input!: ElementRef;
  @ViewChild('rightBorder2Input') rightBorder2Input!: ElementRef;
  @ViewChild('leftBorder1Input') leftBorder1Input!: ElementRef;
  @ViewChild('leftBorder2Input') leftBorder2Input!: ElementRef;
  @ViewChild('halfBorder1Input') halfBorder1Input!: ElementRef;
  @ViewChild('halfBorder2Input') halfBorder2Input!: ElementRef;
  @ViewChild('groundBorder1Input') groundBorder1Input!: ElementRef;
  @ViewChild('groundBorder2Input') groundBorder2Input!: ElementRef;
  @ViewChild('saveButton') saveButton!: ElementRef;

  constructor(
    private dialogRef: MatDialogRef<CarpetBorderComponent>,
    @Inject(MAT_DIALOG_DATA) public data: CarpetBorderData,
    private fb: FormBuilder,
    private http: HttpClient // <-- added
  ) {
    this.borderForm = this.fb.group({
      lastCarpetSizedate:[new Date(), Validators.required],
      weight:['', Validators.required],
      yes:[''],
      no:[''],
      BottomBorder1:['', Validators.required],
      BottomBorder2:['', Validators.required],
      BottomBorder3:[''],// optional
      TopBorder1:['', Validators.required],
      TopBorder2:['', Validators.required],
      TopBorder3:[''],// optional
      leftBorder1: ['', Validators.required],
      leftBorder2: ['', Validators.required],
      leftBorder3:[''],// optional
      rightBorder1: ['', Validators.required],
      rightBorder2: ['', Validators.required],
      rightBorder3:[''],// optional
      halfBorder1: ['', Validators.required],
      halfBorder2: ['', Validators.required],
      halfBorder3:[''],// optional
      groundBorder1:['', Validators.required],
      groundBorder2:['', Validators.required],
      groundBorder3:[''],// optional
      boxArray: this.fb.array([
        this.fb.control('')
      ]),// optional
      imagesArray: this.fb.array([
        this.fb.control('')
      ]),
      Remarks:[''],

    });

    // Load existing data if available
    this.loadExistingData();
  }

  loadExistingData() {
    if (this.data.existingBorderData) {
      const existingData = this.data.existingBorderData;
      this.borderForm.patchValue({
        weight: existingData.weight || '',
        BottomBorder1: existingData.BottomBorder1 || '',
        BottomBorder2: existingData.BottomBorder2 || '',
        BottomBorder3: existingData.BottomBorder3 || '',
        TopBorder1: existingData.TopBorder1 || '',
        TopBorder2: existingData.TopBorder2 || '',
        TopBorder3: existingData.TopBorder3 || '',
        leftBorder1: existingData.leftBorder1 || '',
        leftBorder2: existingData.leftBorder2 || '',
        leftBorder3: existingData.leftBorder3 || '',
        rightBorder1: existingData.rightBorder1 || '',
        rightBorder2: existingData.rightBorder2 || '',
        rightBorder3: existingData.rightBorder3 || '',
        halfBorder1: existingData.halfBorder1 || '',
        halfBorder2: existingData.halfBorder2 || '',
        halfBorder3: existingData.halfBorder3 || '',
        groundBorder1: existingData.groundBorder1 || '',
        groundBorder2: existingData.groundBorder2 || '',
        groundBorder3: existingData.groundBorder3 || '',
        Remarks: existingData.Remarks || ''
      });

      // Load box array data
      if (existingData.boxArray && existingData.boxArray.length > 0) {
        this.boxArray.clear();
        existingData.boxArray.forEach((item: string) => {
          this.boxArray.push(this.fb.control(item));
        });
      }

      // Load images array data
      if (existingData.imagesArray && existingData.imagesArray.length > 0) {
        this.imagesArray.clear();
        this.images = [];
        existingData.imagesArray.forEach((imageData: string) => {
          this.imagesArray.push(this.fb.control(imageData));
          this.images.push({ preview: imageData });
        });
      }
    }
  }

  ngAfterViewInit() {
    // Focus on the weight input field after view is initialized
    // Use setTimeout to ensure all Angular lifecycle hooks are complete
    setTimeout(() => {
      if (this.weightInput && this.weightInput.nativeElement) {
        this.weightInput.nativeElement.focus();
        this.weightInput.nativeElement.select(); // Also select the text if any
      }
    }, 100);
  }

  // Navigation methods for keyboard flow
  onWeightEnter(): void {
    if (this.bottomBorder1Input) {
      this.bottomBorder1Input.nativeElement.focus();
    }
  }

  onBottomBorder1Enter(): void {
    if (this.bottomBorder2Input) {
      this.bottomBorder2Input.nativeElement.focus();
    }
  }

  onBottomBorder2Enter(): void {
    if (this.topBorder1Input) {
      this.topBorder1Input.nativeElement.focus();
    }
  }

  onTopBorder1Enter(): void {
    if (this.topBorder2Input) {
      this.topBorder2Input.nativeElement.focus();
    }
  }

  onTopBorder2Enter(): void {
    if (this.rightBorder1Input) {
      this.rightBorder1Input.nativeElement.focus();
    }
  }

  onRightBorder1Enter(): void {
    if (this.rightBorder2Input) {
      this.rightBorder2Input.nativeElement.focus();
    }
  }

  onRightBorder2Enter(): void {
    if (this.leftBorder1Input) {
      this.leftBorder1Input.nativeElement.focus();
    }
  }

  onLeftBorder1Enter(): void {
    if (this.leftBorder2Input) {
      this.leftBorder2Input.nativeElement.focus();
    }
  }

  onLeftBorder2Enter(): void {
    if (this.halfBorder1Input) {
      this.halfBorder1Input.nativeElement.focus();
    }
  }

  onHalfBorder1Enter(): void {
    if (this.halfBorder2Input) {
      this.halfBorder2Input.nativeElement.focus();
    }
  }

  onHalfBorder2Enter(): void {
    if (this.groundBorder1Input) {
      this.groundBorder1Input.nativeElement.focus();
    }
  }

  onGroundBorder1Enter(): void {
    if (this.groundBorder2Input) {
      this.groundBorder2Input.nativeElement.focus();
    }
  }

  onGroundBorder2Enter(): void {
    if (this.saveButton) {
      this.saveButton.nativeElement.focus();
    }
  }

  onSaveEnter(): void {
    this.onSave();
  }

  setDigit(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.borderForm.get('weight')?.patchValue(_val.toFixed(3));
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (!this.showValidationErrors()) {
      return; // Stop execution if validation fails
    }

    const formValue = this.borderForm.value;

    // Filter out empty/null values from arrays
    if (formValue.boxArray) {
      formValue.boxArray = formValue.boxArray.filter((item: any) => item && item.trim() !== '');
    }
    if (formValue.imagesArray) {
      formValue.imagesArray = formValue.imagesArray.filter((item: any) => item && item.trim() !== '');
    }

    // Save to history if carpetReceivedId is available
    if (this.data.carpetReceivedId) {
      const historyData = {
        ...formValue,
        updatedAt: new Date()
      };

      this.http.post(`http://localhost:2000/api/phase-four/carpetReceivedUpdates/${this.data.carpetReceivedId}/border-size-history`, historyData)
        .subscribe({
          next: (response) => {
            console.log('Border size history saved:', response);
            // Show success message
            Swal.fire({
              title: 'Success!',
              text: 'Border size data saved successfully',
              icon: 'success',
              confirmButtonText: 'OK'
            }).then(() => {
              this.dialogRef.close(formValue);
            });
          },
          error: (error) => {
            console.error('Error saving border size history:', error);
            // Still close dialog with form value even if history save fails
            this.dialogRef.close(formValue);
          }
        });
    } else {
      // Show success message for non-history saves
      Swal.fire({
        title: 'Success!',
        text: 'Border size data saved successfully',
        icon: 'success',
        confirmButtonText: 'OK'
      }).then(() => {
        this.dialogRef.close(formValue);
      });
    }
  }

  showValidationErrors(): boolean {
    let errorFields = Object.keys(this.borderForm.controls)
      .filter(key => this.borderForm.get(key)?.invalid)
      .map(key => key.replace(/([A-Z])/g, ' $1').trim());

    if (errorFields.length > 0) {
      Swal.fire({
        title: 'Validation Error',
        text: `Please fill the following fields: ${errorFields.join(', ')}`,
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return false;  // Stop further execution
    }
    return true;  // Proceed if valid
  }

  getFormValidationErrors() {
    let formErrors: any = {};
    Object.keys(this.borderForm.controls).forEach(key => {
      const controlErrors = this.borderForm.get(key)?.errors;
      if (controlErrors) {
        formErrors[key] = controlErrors;
      }
    });
    return formErrors;
  }

  addBoxInput() {
    if (this.boxArray.length < 20) {
      this.boxArray.push(this.fb.control(''));
    }
  }

  addImageInput() {
    this.images.push({});
    this.imagesArray.push(this.fb.control(''));
  }

  triggerFileInput(i: number) {
    const input = document.getElementById('imgInput' + i) as HTMLInputElement;
    if (input) input.click();
  }

  onImageSelected(event: any, i: number) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.images[i] = { file, preview: e.target.result };
        // Store base64 string instead of file object
        this.imagesArray.at(i).setValue(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  }

  openImagePreview(url: string) {
    this.previewImage = url;
  }

  closeImagePreview() {
    this.previewImage = null;
  }

}
