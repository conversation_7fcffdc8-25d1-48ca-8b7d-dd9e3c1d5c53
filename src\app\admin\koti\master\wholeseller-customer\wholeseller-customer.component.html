<!-- Start Content-->
<div class="container-fluid vh-100">

    <!-- start page title -->
    <div class="py-3 py-lg-4">
        <div class="row text-center" *ngIf="!isOpenDialog" >
            <div class="col-lg-12">
                <h2 class="page-title mb-0">{{isUpdate?'Update Whole seller':'Add Whole seller'}}</h2>
            </div>

        </div>


        <!-- Form row -->
        <div class="row" *ngIf="!isOpenDialog">
            <div class="col-xl-12">
                <div class="card">
                    <div class="card-body">



                        <form [formGroup]="frmWholeshaler" (ngSubmit)="addWholshaler()">
                            <div class="row">
                                <div class="mb-2 col-md-4">

                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Customer Code</mat-label>
                                        <input matInput placeholder="Customer Code" formControlName="customerCode">


                                    </mat-form-field>
                                </div>
                                <div class="mb-2 col-md-4">

                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Customer Name</mat-label>
                                        <input matInput placeholder="Customer Name" formControlName="customerName">


                                    </mat-form-field>
                                </div>

                                <div class="mb-2 col-md-4">
                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Address</mat-label>
                                        <textarea matInput
                                            placeholder="Ex. 100 Main St" formControlName="address">1600 Amphitheatre Pkwy</textarea>
                                    </mat-form-field>
                                </div>
                                <div class="mb-2 col-md-4">
                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>ZipCode</mat-label>
                                        <input matInput placeholder="ZipCode" formControlName="zipCode">
                                    </mat-form-field>
                                </div>

                                <div class="mb-2 col-md-4">
                                    <mat-form-field appearance="outline">
                                        <mat-label>Country</mat-label>
                                        <mat-select formControlName="country">

                                            @for (country of country; track country) {
                                            <mat-option [value]="country">{{country}}</mat-option>
                                            }
                                        </mat-select>
                                    </mat-form-field>

                                </div>


                                <div class="mb-2 col-md-4">
                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Contact Number</mat-label>
                                        <input matInput placeholder="Contact Number" formControlName="contactNumber">
                                    </mat-form-field>

                                </div>
                            </div>
                            <div class="row">




                                <div class="mb-2 col-md-4">
                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Email</mat-label>
                                        <input type="email" matInput formControlName="email"
                                            [errorStateMatcher]="matcher" placeholder="Ex. <EMAIL>">

                                        @if (emailFormControl.hasError('email') &&
                                        !emailFormControl.hasError('required')) {
                                        <mat-error>Please enter a valid email address</mat-error>
                                        }
                                        @if (emailFormControl.hasError('required')) {
                                        <mat-error>Email is <strong>required</strong></mat-error>
                                        }
                                    </mat-form-field>
                                </div>

                                <hr>
                                <h2>Bank Details</h2>

                                <div class="mb-2 col-md-4">
                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Email</mat-label>
                                        <input type="email" matInput formControlName="bankEmail"
                                            [errorStateMatcher]="matcher" placeholder="Ex. <EMAIL>">

                                        @if (emailFormControl.hasError('email') &&
                                        !emailFormControl.hasError('required')) {
                                        <mat-error>Please enter a valid email address</mat-error>
                                        }
                                        @if (emailFormControl.hasError('required')) {
                                        <mat-error>Email is <strong>required</strong></mat-error>
                                        }
                                    </mat-form-field>
                                </div>
                                <div class="mb-2 col-md-4">
                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Enter your password</mat-label>
                                        <input matInput [type]="hide ? 'password' : 'text'" formControlName="enterPassword">
                                        <button mat-icon-button matSuffix (click)="hide = !hide"
                                            [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hide">
                                            <mat-icon>{{hide ? 'visibility_off' : 'visibility'}}</mat-icon></button>
                                    </mat-form-field>
                                </div>
                                <div class="mb-2 col-md-4">

                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label> Bank Name</mat-label>
                                        <input matInput placeholder="Bank Name" formControlName="bankName">
                                    </mat-form-field>
                                </div>

                                <div class="mb-2 col-md-4">
                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label> Bank Address</mat-label>
                                        <textarea matInput
                                            placeholder="Ex. 100 Main St" formControlName="bankAddress">1600 Amphitheatre Pkwy</textarea>
                                    </mat-form-field>
                                </div>

                                <div class="mb-2 col-md-4">

                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Account Number</mat-label>
                                        <input matInput placeholder="Account Number" formControlName="accountNumber">
                                    </mat-form-field>
                                </div>


                                <div class="mb-2 col-md-4">

                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label> Bank Swift Code</mat-label>
                                        <input matInput placeholder="Bank Swift Code" formControlName="swiftCode">
                                    </mat-form-field>
                                </div>


                                <div class="mb-2 col-md-4">

                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label> Contact number</mat-label>
                                        <input matInput placeholder="Bank Contact number" formControlName="bankContactNumber">
                                    </mat-form-field>
                                </div>

                            </div>

                           <div *ngIf="!isUpdate">
                            <button mat-flat-button color="primary">Add</button>
                           </div>
                           <div *ngIf="isUpdate"> <button mat-flat-button color="primary" type="button" (click)="Update()">Update</button> </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- end col -->


            <!-- end col -->
        </div>
        <!-- end row -->

        <div class="row">
          <div class="col-md-4"><mat-form-field>
            <mat-label>Filter</mat-label>
            <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Mia" #input>
        </mat-form-field></div>
            <div class="col-12">
                <div class="card">
                    <div class="card-body">






                        <div class="mat-elevation-z8" style="overflow: auto;">
                            <table mat-table [dataSource]="dataSource" matSort>

                                <!-- ID Column -->
                                <ng-container matColumnDef="id">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header> No. </th>
                                    <td mat-cell *matCellDef="let row"> {{row.index}} </td>
                                </ng-container>

                                <!-- Progress Column -->
                                <ng-container matColumnDef="customerCode">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Customer Code </th>
                                    <td mat-cell *matCellDef="let row"> {{row.customerCode}}</td>
                                </ng-container>

                                <!-- Name Column -->
                                <ng-container matColumnDef="customerName">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Customer Name</th>
                                    <td mat-cell *matCellDef="let row"> {{row.customerName}} </td>
                                </ng-container>

                                <!-- Fruit Column -->
                                <ng-container matColumnDef="address">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Address</th>
                                    <td mat-cell *matCellDef="let row"> {{row.address}} </td>
                                </ng-container>

                                <ng-container matColumnDef="email2">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Email</th>
                                    <td mat-cell *matCellDef="let row"> {{row.email2}} </td>
                                </ng-container>
                                <ng-container matColumnDef="bankName">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Name</th>
                                    <td mat-cell *matCellDef="let row"> {{row.bankName}} </td>
                                </ng-container>

                                <ng-container matColumnDef="email">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
                                    <td mat-cell *matCellDef="let row"> {{row.email}} </td>
                                </ng-container>
                                <ng-container matColumnDef="enterPassword">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Password</th>
                                    <td mat-cell *matCellDef="let row"> {{row.enterPassword}} </td>
                                </ng-container>

                                <ng-container matColumnDef="bankAddress">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Address</th>
                                    <td mat-cell *matCellDef="let row"> {{row.bankAddress}} </td>
                                </ng-container>
                                <ng-container matColumnDef="accountNumber">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Account Number</th>
                                    <td mat-cell *matCellDef="let row"> {{row.accountNumber}} </td>
                                </ng-container>
                                <ng-container matColumnDef="swiftCode">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Swift Code</th>
                                    <td mat-cell *matCellDef="let row"> {{row.swiftCode}} </td>
                                </ng-container>
                                <ng-container matColumnDef="contactNumber2">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Contact Number</th>
                                    <td mat-cell *matCellDef="let row"> {{row.contactNumber2}} </td>
                                </ng-container>


                                <ng-container matColumnDef="bankContactNumber">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Contact</th>
                                    <td mat-cell *matCellDef="let row"> {{row.bankContactNumber}} </td>
                                </ng-container>






                                <!-- Fruit Column -->
                                <ng-container matColumnDef="zipCode">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Zip Code </th>
                                    <td mat-cell *matCellDef="let row"> {{row.zipCode}} </td>
                                </ng-container>

                                <!-- Fruit Column -->
                                <ng-container matColumnDef="country">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Country</th>
                                    <td mat-cell *matCellDef="let row"> {{row.country}} </td>
                                </ng-container>
                                <ng-container matColumnDef="action">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                                    <td mat-cell *matCellDef="let row"> <a title="Edit" (click)="edit(row.id)">
                                      <i class="fa fa-pencil-square-o fa-lg" aria-hidden="true"></i>
                                  </a> &nbsp;&nbsp; <a title="Delete" (click)="delete(row.id)"><i class="fa fa-trash-o fa-lg"
                                          aria-hidden="true"></i></a>   </td>
                                </ng-container>

                                <!-- Fruit Column -->
                                <ng-container matColumnDef="contactNumber">
                                    <th mat-header-cell *matHeaderCellDef mat-sort-header>contactNumber</th>
                                    <td mat-cell *matCellDef="let row"> {{row.contactNumber}} </td>
                                </ng-container>
                                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                <tr mat-row *matRowDef="let row; columns: displayedColumns;"  (click)="logRow(row)"></tr>

                                <!-- Row shown when there is no matching data. -->
                                <tr class="mat-row" *matNoDataRow>
                                    <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>
                                </tr>
                            </table>

                            <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]"
                                aria-label="Select page of users"></mat-paginator>
                        </div>



                    </div> <!-- end card body-->
                </div> <!-- end card -->
            </div><!-- end col-->
        </div>
        <!-- end row-->





    </div>
    <!-- end page title -->

</div> <!-- container -->
