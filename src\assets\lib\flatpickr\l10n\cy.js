(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.cy = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Welsh = {
      weekdays: {
          shorthand: ["<PERSON>", "<PERSON>lun", "Maw", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>we", "<PERSON>"],
          longhand: [
              "<PERSON>ydd Sul",
              "<PERSON>ydd Llun",
              "<PERSON>yd<PERSON> Mawrth",
              "<PERSON>yd<PERSON> Mercher",
              "<PERSON>ydd Iau",
              "<PERSON>yd<PERSON> Gwener",
              "Dydd Sadwrn",
          ],
      },
      months: {
          shorthand: [
              "Ion",
              "Chwef",
              "<PERSON>w",
              "<PERSON>b<PERSON>",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "Rhag",
          ],
          longhand: [
              "<PERSON><PERSON>r",
              "<PERSON>we<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>",
              "Mehefin",
              "Gorffennaf",
              "Awst",
              "Medi",
              "Hydref",
              "Tachwedd",
              "Rhagfyr",
          ],
      },
      firstDayOfWeek: 1,
      ordinal: function (nth) {
          if (nth === 1)
              return "af";
          if (nth === 2)
              return "ail";
          if (nth === 3 || nth === 4)
              return "ydd";
          if (nth === 5 || nth === 6)
              return "ed";
          if ((nth >= 7 && nth <= 10) ||
              nth == 12 ||
              nth == 15 ||
              nth == 18 ||
              nth == 20)
              return "fed";
          if (nth == 11 ||
              nth == 13 ||
              nth == 14 ||
              nth == 16 ||
              nth == 17 ||
              nth == 19)
              return "eg";
          if (nth >= 21 && nth <= 39)
              return "ain";
          // Inconclusive.
          return "";
      },
      time_24hr: true,
  };
  fp.l10ns.cy = Welsh;
  var cy = fp.l10ns;

  exports.Welsh = Welsh;
  exports.default = cy;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
