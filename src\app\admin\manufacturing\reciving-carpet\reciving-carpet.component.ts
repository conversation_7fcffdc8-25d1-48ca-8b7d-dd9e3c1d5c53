import { Component, OnInit, ViewChild, HostListener, ElementRef, AfterViewInit } from '@angular/core';
import { FormBuilder, FormGroup , Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import { ManufactureService } from '../../../services/manufacture.service';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { SweetalertService } from '../../../services/sweetalert.service';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';
import Swal from 'sweetalert2';
import { environment } from '../../../../environments/environment';

export interface UserData {
  SrNo: any;
  CarpetNo: any;
  CDate: any;
  IssueNo: any;
  IDate: any;
  Weaver: any;
  Quality: string;
  Design: string;
  Border: string;
  Size: string;
  Pcs: string;
  Area: any;
  Rate: any;
  Amount: any;
  _id?: string; // Add ID for delete functionality
}


const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-reciving-carpet',
  templateUrl: './reciving-carpet.component.html',
  styleUrl: './reciving-carpet.component.css',
})
export class RecivingCarpetComponent implements OnInit, AfterViewInit {



  displayedColumns: string[] = [
    'SrNo',
    'CarpetNo',
    'CDate',
    'IssueNo',
    'IDate',
    'Weaver',
    'Quality',
    'Design',
    'Border',
    'Size', 
    'Pcs',
    'Area',
    'Rate',
    'Amount',
    'Actions',
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);
  frmReceivingCarpet!: FormGroup;
  isSaving = false;
  previewRow: any = null;

  constructor(
    private formBuilder: FormBuilder,
    private masterService: MasterService,
    private manufacturingService: ManufactureService,
    private alert: SweetalertService,
    private customeServices: CustomeServiceService,
    private http: HttpClient,
    private activeRoute: ActivatedRoute,
    private router: Router
  ) {
    // Subscribe to router events to detect when we return to this page
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // Check if we've navigated to the receiving-carpet page
        if (event.url.includes('/admin/manufacturing/reciving-carpet')) {
          // Reset form data
          if (this.frmReceivingCarpet) {
            this.frmReceivingCarpet.reset({ date: new Date() });
          }
          // Clear order list
          ELEMENT_DATA.length = 0;
          this.dataSource = new MatTableDataSource(ELEMENT_DATA);
          this.issuePcs = null;
          this.pcsCount = 0;
          this.previewRow = null;
          this.filterData = [];

          // Refresh all data
          this.getLastReceiveNo();
          this.getWeaverName();
        }
      }
    });
  }
  ngOnInit(): void {
    this.frmReceivingCarpet = this.formBuilder.group({
      weaver: ['', Validators.required],
      issueNo: ['', Validators.required],
      date: [new Date(), Validators.required],
      receiveNo: [''],
      // weight: ['', Validators.required],
    });

    // Add form value change debugging
    this.frmReceivingCarpet.valueChanges.subscribe(value => {
      console.log('🔍 Form value changed:', value);
    });

    this.getWeaverName();
    this.getLastReceiveNo();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;

    // Set sorting accessor for CarpetNo
    this.dataSource.sortingDataAccessor = (item: any, property: string) => {
      switch (property) {
        case 'CarpetNo':
          // Ensure consistent string format for comparison
          return item.CarpetNo ? item.CarpetNo.toString().padStart(10, '0') : '';
        default:
          return item[property];
      }
    };

    // Set default sorting to CarpetNo in descending order
    setTimeout(() => {
      if (this.sort) {
        this.sort.sort({ id: 'CarpetNo', start: 'desc', disableClear: true });
        this.sort.sortChange.subscribe(() => {
          // If CarpetNo column is clicked, force descending order
          if (this.sort.active === 'CarpetNo' && this.sort.direction === 'asc') {
            this.sort.direction = 'desc';
          }
        });
      }
    }, 0);

    // Focus on weaver dropdown when component loads
    setTimeout(() => {
      if (this.weaverSelect) {
        this.weaverSelect.focus();
        
      }
    }, 500);
  }

  // Keyboard navigation methods
  onWeaverSelectionChange(event: any): void {
    console.log('🔍 Weaver selection changed:', event.value);
    this.selectWeaver(event.value);
    // Ensure form control is updated
    this.frmReceivingCarpet.patchValue({ weaver: event.value });

    // Update receiveNo based on selected weaver's branch
    this.getLastReceiveNo();

    // Focus on issue select after weaver selection
    setTimeout(() => {
      if (this.issueSelect) {
        this.issueSelect.focus();
        this.issueSelect.open(); // Open the dropdown
      }
    }, 100);
  }

  onIssueSelectionChange(event: any): void {
    console.log('🔍 Issue selection changed:', event.value);
    this.selectedIssue(event.value);
    // Ensure form control is updated
    this.frmReceivingCarpet.patchValue({ issueNo: event.value });
    // Focus on date input after issue selection
    setTimeout(() => {
      if (this.dateInput && this.dateInput.nativeElement) {
        this.dateInput.nativeElement.focus();
      }
    }, 100);
  }

  onDateEnter(): void {
    if (this.saveNextButton && this.saveNextButton.nativeElement) {
      this.saveNextButton.nativeElement.focus();
    }
  }

  onSaveNextEnter(): void {
    this.saveNext();
  }

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild('weaverSelect') weaverSelect!: any;
  @ViewChild('issueSelect') issueSelect!: any;
  @ViewChild('dateInput') dateInput!: ElementRef;
  @ViewChild('saveNextButton') saveNextButton!: ElementRef;



  // Method to handle all table updates
  private updateTableNumbers() {
    // Get current data
    const data = [...this.dataSource.data];
    
    // Ensure data is sorted by CarpetNo in descending order
    data.sort((a, b) => {
      const aNo = a.CarpetNo ? a.CarpetNo.toString().padStart(10, '0') : '';
      const bNo = b.CarpetNo ? b.CarpetNo.toString().padStart(10, '0') : '';
      return bNo.localeCompare(aNo); // Descending order
    });
    
    // Then update SrNo sequentially from top (1,2,3,4)
    data.forEach((item, index) => {
      item.SrNo = index + 1;
    });
    
    this.dataSource.data = data;
  }
  
  // Add new items to table
  addToOrderList(newItem: any) {
    // Add the new item to the data array
    const data = [...this.dataSource.data, newItem];
    
    // Re-sort the data by CarpetNo in descending order
    data.sort((a, b) => {
      const aNo = a.CarpetNo ? a.CarpetNo.toString().padStart(10, '0') : '';
      const bNo = b.CarpetNo ? b.CarpetNo.toString().padStart(10, '0') : '';
      return bNo.localeCompare(aNo); // Descending order
    });
    
    // Update SrNo sequentially
    data.forEach((item, index) => {
      item.SrNo = index + 1;
    });
    
    // Update the data source
    this.dataSource.data = data;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }




  filterData: any;
  allOrderList: any[] = [];
  list: any[] = [];

  selectWeaver(id: any) {
    console.log('🔍 Selecting weaver:', id);
    console.log('🔍 All order list before filtering:', this.allOrderList.length);

    // Filter data based on selected weaver and exclude issues with PCSWaitingToBeReceived = 0
    this.filterData = this.allOrderList.filter((x: any) => {
      const isMatchingWeaver = x.weaver._id === id;
      const waitingPcs = x.PCSWaitingToBeReceived !== undefined
        ? x.PCSWaitingToBeReceived
        : parseInt(x.pcs) || 0;
      const hasWaitingPieces = waitingPcs > 0;

      console.log(`🔍 Issue ${x.Br_issueNo}: Weaver Match = ${isMatchingWeaver}, Waiting PCS = ${waitingPcs}, Show = ${isMatchingWeaver && hasWaitingPieces}`);

      return isMatchingWeaver && hasWaitingPieces;
    });

    console.log('📊 Filtered Data for Weaver (excluding 0 PCS):', this.filterData.length, 'issues');
    console.log('📊 Available issues after filtering:', this.filterData.map((x: any) => ({ issueNo: x.Br_issueNo, waitingPcs: x.PCSWaitingToBeReceived || x.pcs })));

    // Reset issue selection when weaver changes
    this.frmReceivingCarpet.patchValue({ issueNo: '' });
    this.previewRow = null;
    this.issuePcs = null;
  }

  addReceivingCarpet() {
    console.log(this.frmReceivingCarpet.value);
  }

  getWeaverName() {
    this.http.get('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied').subscribe((receivedData: any) => {
      // Count received pcs for each issueNo
      const receivedCountMap: Record<string, number> = {};
      (receivedData || []).forEach((x: any) => {
        const id = x.issueNo?._id;
        if (id) {
          receivedCountMap[id] = (receivedCountMap[id] || 0) + (x.pcs ? Number(x.pcs) : 1);
        }
      });

      this.manufacturingService.getsOrderIssueList().subscribe((res: any) => {
        const validOrderList = res.filter((x: any) => x && x._id);

        // Step 1: For all orders, get their remaining pcs from CRecievedOrderListDt
        const updateOrdersWithRemainingPcs = (orders: any[]) => {
          const updatePromises = orders.map(order =>
            this.http.get(`http://localhost:2000/api/phase-four/CRecievedOrderListDt/get?issueNo=${order._id}`).toPromise()
              .then((result: any) => {
                if (result && result.pcs !== undefined) {
                  order.pcs = result.pcs; // update with remaining pcs
                }
                return order;
              })
              .catch(() => order)
          );
          Promise.all(updatePromises).then(updatedOrders => {
            // Filter out orders with pcs === 0 (no pieces waiting to be received)
            this.allOrderList = updatedOrders.filter((x: any) => {
              const waitingPcs = x.PCSWaitingToBeReceived !== undefined
                ? x.PCSWaitingToBeReceived
                : parseInt(x.pcs) || 0;
              return waitingPcs > 0;
            });

            console.log('🔍 Orders with available pieces:', this.allOrderList.length);

            // Step 3: Build dropdown list of weavers (only from orders that have available pieces)
            this.list = [];
            this.allOrderList.map((x: any) => {
              this.list.push({
                id: x.weaver._id,
                weaver: x.weaver.name,
                branch: x.branch?.branchCode,
              });
            });

            // Step 4: Unique by Weaver + Branch (only weavers with available pieces will be included)
            this.list.sort((a, b) => a.weaver.localeCompare(b.weaver));
            this.list = this.list.filter(
              (value, index, self) =>
                index === self.findIndex(obj => obj.id === value.id && obj.weaver === value.weaver)
            );

            console.log('📊 Available weavers (with pending pieces):', this.list.length);
            console.log('📊 Weaver list:', this.list.map((w: any) => `${w.branch} - ${w.weaver}`));
            // Step 5: Check if currently selected weaver is still available
            const currentWeaver = this.frmReceivingCarpet.get('weaver')?.value;
            if (currentWeaver) {
              // Check if the current weaver still has available pieces
              const weaverStillAvailable = this.list.find(w => w.id === currentWeaver);

              if (weaverStillAvailable) {
                // Weaver still has pieces, update filter data
                this.selectWeaver(currentWeaver);
              } else {
                // Weaver has no more pieces, reset the form
                console.log('🔄 Current weaver has no more pieces available. Resetting form.');
                this.frmReceivingCarpet.patchValue({
                  weaver: '',
                  issueNo: ''
                });
                this.previewRow = null;
                this.issuePcs = null;
                this.filterData = [];

                // Show notification
                // Swal.fire({
                //   title: 'Completed!',
                //   text: 'All pieces for this weaver have been received. Please select another weaver.',
                //   icon: 'info',
                //   confirmButtonText: 'OK'
                // });
              }
            }

            // Step 6: Load saved carpet data after weaver list is ready
            this.loadSavedCarpetData();

            // Step 7: Check if current issue needs to be reset due to 0 pieces
            this.checkAndResetIfNoPieces();
          });
        };
        updateOrdersWithRemainingPcs(validOrderList);
      });
    });
  }

  // isMap: boolean = false;
  rno = 2400001;
  issucarpet: any;
  issuePcs: any;
  // isNextBtn: boolean = true;
  pcsCount: number = 0;

  // selectedIssue(id: string) {
  //   this.issuePcs = '';
  //   this.pcsCount = 0;
  //   this.isNextBtn = true;
  //   this.issuePcs = this.filterData.find((x: any) => x._id === id);
  //   this.isMap = false;


  //   if (parseInt(this.issuePcs.pcs) === 1) {
  //     this.isMap = true;
  //   }
  //   this.itemList(this.issuePcs);
  // }

  selectedIssue(id: string) {
    this.issuePcs = this.filterData.find((x: any) => x._id === id);
    console.log('Selected Issue:', this.issuePcs);
    console.log('Issue Pcs:', this.issuePcs?.pcs);

    if (this.issuePcs) {
      // Calculate Area for waiting pieces (show pieces waiting to be received)
      let totalArea = 0;
      let areaUnit = '';
      const waitingPcs = this.issuePcs.PCSWaitingToBeReceived || parseInt(this.issuePcs.pcs);
      const receivedPcs = this.issuePcs.PCSReceived || 0;

      console.log('Waiting Pcs:', waitingPcs);
      console.log('Received Pcs:', receivedPcs);
      console.log('Total Pcs:', this.issuePcs.pcs);
      console.log('Area In:', this.issuePcs.areaIn);
      console.log('Size:', this.issuePcs.size);

      if (this.issuePcs.areaIn === 'Sq.Feet' && this.issuePcs.size?.areaInFeet) {
        totalArea = parseFloat(this.issuePcs.size.areaInFeet) * waitingPcs;
        areaUnit = 'Ft';
      } else if (this.issuePcs.areaIn === 'Sq.Yard' && this.issuePcs.size?.areaInYard) {
        totalArea = parseFloat(this.issuePcs.size.areaInYard) * waitingPcs;
        areaUnit = 'Yd';
      } else {
        // Calculate area per piece and multiply by waiting pieces
        const areaPerPiece = parseFloat(this.issuePcs.area) / parseInt(this.issuePcs.pcs) || 0;
        totalArea = areaPerPiece * waitingPcs;
        areaUnit = this.issuePcs.areaIn === 'Sq.Feet' ? 'Ft' : (this.issuePcs.areaIn === 'Sq.Yard' ? 'Yd' : '');
      }

      console.log('Calculated Total Area for waiting pieces:', totalArea);
      console.log('Area Unit:', areaUnit);

      // Set previewRow showing waiting pieces data
      this.previewRow = {
        IssueNo: this.issuePcs.Br_issueNo,
        IssueDt: this.customeServices.convertDate(this.issuePcs.date),
        Weaver: this.issuePcs.branch?.branchCode + ' - ' + this.issuePcs.weaver?.name,
        Quality: this.issuePcs.quality?.quality,
        Design: this.issuePcs.design?.design,
        GroundBorder: this.issuePcs.borderColour,
        Size: this.issuePcs.size?.sizeInYard,
        Pcs: waitingPcs, // Show waiting pieces
        Area: totalArea + (areaUnit ? ' ' + areaUnit : ''),
        Rate: this.issuePcs.rate,
        Amount: (totalArea * parseFloat(this.issuePcs.rate || 0)).toFixed(2),
      };

      console.log('Preview Row:', this.previewRow);

      // Check if PCS is 0 and reset if needed
      this.checkAndResetIfNoPieces();
    }
  }

  // Helper function to check and reset when no pieces are waiting
  checkAndResetIfNoPieces() {
    if (this.issuePcs && this.previewRow) {
      const waitingPcs = this.issuePcs.PCSWaitingToBeReceived || parseInt(this.issuePcs.pcs);

      console.log('🔍 Check Reset Debug:', {
        waitingPcs: waitingPcs,
        PCSWaitingToBeReceived: this.issuePcs.PCSWaitingToBeReceived,
        PCSReceived: this.issuePcs.PCSReceived,
        originalPcs: this.issuePcs.pcs,
        shouldReset: waitingPcs <= 0
      });

      if (waitingPcs <= 0) {
        console.log('🔄 No pieces waiting to be received. Resetting form and preview.');

        // Reset form controls
        this.frmReceivingCarpet.patchValue({
          weaver: '',
          issueNo: ''
        });

        // Clear preview row
        this.previewRow = null;

        // Clear issue data
        this.issuePcs = null;
        this.filterData = [];

        // Show notification
        Swal.fire({
          title: 'Complete!',
          text: 'All pieces for this issue have been received. Form has been reset.',
          icon: 'success',
          confirmButtonText: 'OK'
        });
      }
    }
  }




  // Filter function to show only issues with available pieces (PCS > 0)
  getAvailableIssues() {
    if (!this.filterData) {
      return [];
    }

    const availableIssues = this.filterData.filter((issue: any) => {
      // Use the most up-to-date PCS value from the database
      const waitingPcs = issue.PCSWaitingToBeReceived !== undefined
        ? issue.PCSWaitingToBeReceived
        : parseInt(issue.pcs) || 0;

      console.log(`🔍 Issue ${issue.Br_issueNo}: PCSWaitingToBeReceived = ${waitingPcs}, Original PCS = ${issue.pcs}`);

      const isAvailable = waitingPcs > 0;
      if (!isAvailable) {
        console.log(`❌ Filtering out ${issue.Br_issueNo} - no pieces waiting`);
      }

      return isAvailable;
    });

    console.log(`📊 Available issues: ${availableIssues.length} out of ${this.filterData.length}`);
    return availableIssues;
  }


  getLastReceiveNo() {
    // Get current selected weaver's branch code
    const currentWeaverId = this.frmReceivingCarpet.get('weaver')?.value;
    let currentBranchCode = 'K'; // default fallback

    if (currentWeaverId && this.list) {
      const selectedWeaver = this.list.find(w => w.id === currentWeaverId);
      if (selectedWeaver && selectedWeaver.branch) {
        currentBranchCode = selectedWeaver.branch;
      }
    }

    this.http.get('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied').subscribe({
      next: (response: any) => {
        const allData = Array.isArray(response) ? response : [];
        // Find max receiveNo for the current branch
        let maxNumber = 2400000; // default start

        // Find the highest sequential number across ALL branches (like carpet-order-issue)
        allData.forEach((item: any) => {
          if (item.receiveNo) {
            const parts = item.receiveNo.split('-');
            if (parts.length === 2) {
              const num = parseInt(parts[1]);
              if (!isNaN(num) && num > maxNumber) {
                maxNumber = num;
              }
            }
          }
        });

        const nextNumber = maxNumber + 1;
        const nextReceiveNo = `${currentBranchCode}-${nextNumber.toString().padStart(7, '0')}`;
        this.issucarpet = nextReceiveNo;
        this.frmReceivingCarpet.patchValue({ receiveNo: nextReceiveNo });

        console.log(`🔢 Generated receiveNo: ${nextReceiveNo} for branch: ${currentBranchCode}`);
      },
      error: (err) => {
        console.error('Error fetching last receiveNo:', err);
        // fallback with current branch code
        const defaultNo = `${currentBranchCode}-2400001`;
        this.issucarpet = defaultNo;
        this.frmReceivingCarpet.patchValue({ receiveNo: defaultNo });
      }
    });
  }






  // setDigit(val: any) {
  //   let _val = parseFloat(val.target.value);
  //   if (!isNaN(_val)) {
  //     this.frmReceivingCarpet.get('weight')?.patchValue(_val.toFixed(3));
  //   }
  // }

  showValidationErrors() {
    const errorFields = Object.keys(this.frmReceivingCarpet.controls)
      .filter(key => this.frmReceivingCarpet.get(key)?.invalid)
      .map(key => key.replace(/([A-Z])/g, ' $1').trim());

    if (errorFields.length > 0) {
      Swal.fire({
        title: 'Validation Error',
        text: `Please fill the following fields: ${errorFields.join(', ')}`,
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return false;
    }

    return true;
  }

  async saveNext() {
    if (!this.showValidationErrors()) {
      return;
    }
    if (!this.issuePcs) {
      this.alert.error('warning', 'No carpet data selected');
      return;
    }

    // Check if there are waiting pieces to be received
    const waitingPcs = this.issuePcs.PCSWaitingToBeReceived || parseInt(this.issuePcs.pcs);
    if (waitingPcs <= 0) {
      this.alert.error('warning', 'No pieces waiting to be received for this issue');
      return;
    }

    // --- Duplicate Carpet No. check ---
    const enteredCarpetNo = this.frmReceivingCarpet.value.receiveNo;
    try {
      const carpets: any = await this.http.get('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied').toPromise();
      if (Array.isArray(carpets) && carpets.some((c: any) => c.receiveNo === enteredCarpetNo)) {
        Swal.fire({
          title: 'Duplicate Carpet No.',
          text: `Carpet No. ${enteredCarpetNo} already received!`,
          icon: 'warning',
          confirmButtonText: 'OK'
        });
        return;
      }
    } catch (err) {
      // ignore error, allow save to continue
    }
    // --- End duplicate check ---

    // Add confirmation before saving
    Swal.fire({
      title: 'Save Confirmation',
      text: 'Are you sure you want to save this carpet piece?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes, save it',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (!result.isConfirmed) return;

      // Calculate Area for 1 piece based on areaIn
      let calculatedArea = 0;
      if (this.issuePcs.areaIn === 'Sq.Feet' && this.issuePcs.size?.areaInFeet) {
        calculatedArea = parseFloat(this.issuePcs.size.areaInFeet);
      } else if (this.issuePcs.areaIn === 'Sq.Yard' && this.issuePcs.size?.areaInYard) {
        calculatedArea = parseFloat(this.issuePcs.size.areaInYard);
      } else {
        calculatedArea = parseFloat(this.issuePcs.area) / parseInt(this.issuePcs.pcs) || 0;
      }

      // Prepare data for 1 piece
      const formattedDate = this.frmReceivingCarpet.value.date instanceof Date
        ? this.frmReceivingCarpet.value.date.toISOString()
        : this.frmReceivingCarpet.value.date;

      const areaUnit = this.issuePcs.areaIn === 'Sq.Feet' ? 'Ft' : (this.issuePcs.areaIn === 'Sq.Yard' ? 'Yd' : '');      const carpetData = {
        K: this.issuePcs?.branch?._id,
        receivingDate: formattedDate,
        issueNo: {
          _id: this.issuePcs._id,
          Br_issueNo: this.issuePcs.Br_issueNo,
          date: this.issuePcs.date,
          quality: this.issuePcs.quality,
          design: this.issuePcs.design,
          borderColour: this.issuePcs.borderColour,
          size: this.issuePcs.size,
          rate: this.issuePcs.rate,
         areaIn: this.issuePcs.areaIn,
          buyerOrder: this.issuePcs.buyerOrder 
        },
        timestamp: new Date().getTime(),
        weaverNumber: this.frmReceivingCarpet.get('weaver')?.value,
        // weight: this.frmReceivingCarpet.value.weight?.toString() || '0',
        receiveNo: this.frmReceivingCarpet.value.receiveNo || this.issucarpet,
        // yes: true,
        // no: false,
        area: calculatedArea.toFixed(2) + ' ' + areaUnit,
        amount: calculatedArea * parseFloat(this.issuePcs.rate || 0),
        pcs: 1,
        areaIn: this.issuePcs.areaIn,
        
      };

      this.http.post('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied', carpetData, {
        headers: { 'Content-Type': 'application/json' }
      }).subscribe({
        next: (response: any) => {          // After saving successfully:          // Add the newly saved carpet to the order list
          const issueNo = this.issuePcs || {};
          const weaverName = this.list?.find(w => w.id === this.frmReceivingCarpet.get('weaver')?.value)?.weaver || '';
          const branchCode = this.list?.find(w => w.id === this.frmReceivingCarpet.get('weaver')?.value)?.branch || '';
          
          const newItem: UserData = {
            SrNo: this.dataSource.data.length + 1,
            CarpetNo: carpetData.receiveNo,
            CDate: this.customeServices.convertDate(carpetData.receivingDate),
            IssueNo: issueNo.Br_issueNo || '',
            IDate: this.customeServices.convertDate(issueNo.date),
            Weaver: branchCode ? `${branchCode} - ${weaverName}` : weaverName,
            Quality: issueNo.quality?.quality || '',
            Design: issueNo.design?.design || '',
            Border: issueNo.borderColour || '',
            Size: issueNo.size?.sizeInYard || '',
            Pcs: '1',
            Area: carpetData.area,
            Rate: issueNo.rate?.toString() || '0',
            Amount: carpetData.amount?.toString() || '0',
            _id: response._id
          };

          this.addToOrderList(newItem);

          // Show success message
          this.alert.success('Success', 'Carpet piece saved successfully.');

          // Reset form fields except date and weaver
          const currentDate = this.frmReceivingCarpet.get('date')?.value;
          const currentWeaver = this.frmReceivingCarpet.get('weaver')?.value;

          // Use patchValue instead of reset to maintain form control state
          this.frmReceivingCarpet.patchValue({
            issueNo: '',
            receiveNo: ''
          });

          // Ensure weaver and date remain selected
          this.frmReceivingCarpet.patchValue({
            date: currentDate,
            weaver: currentWeaver
          });

          // Clear preview row and issue data
          this.previewRow = null;
          this.issuePcs = null;
          
          // Refresh the weaver's order list to get updated PCS counts
          this.getWeaverName();
          
          // Get new receive number for next entry
          this.getLastReceiveNo();

          this.isSaving = false;
        },
        error: (err) => {
          this.alert.error('error', 'Failed to save carpet item: ' + (err.error?.message || 'Unknown error'));
          this.isSaving = false;
        }
      });
    });
  }
  // Load saved carpet data from database and display in order list
  loadSavedCarpetData() {
    // Check if user has exited and should not load saved data
    const hasExited = sessionStorage.getItem('carpetReceivingExited') === 'true';
    if (hasExited) {
      console.log('User has exited, not loading saved carpet data');
      return;
    }

    this.http.get('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied').subscribe({
      next: (response: any) => {
        console.log('Saved carpet data:', response);
        const allData = Array.isArray(response) ? response : [];

        // Clear existing data
        ELEMENT_DATA.length = 0;

        // Transform saved data to match UserData interface
        allData.forEach((item: any, index: number) => {
          const issueNo = item.issueNo || {};

          // Get weaver name from the current weaver list or from populated data
          let weaverName = '';
          if (item.receiveNo?.startsWith('H-')) {
            // For H- format records, use weaverName directly
            weaverName = item.weaverName || 'N/A';
          } else if (item.weaverNumber?.name) {
            // If weaver data is populated, try to get branch info too
            const branchCode = item.weaverNumber?.branch?.branchCode || '';
            weaverName = branchCode ? `${branchCode} - ${item.weaverNumber.name}` : item.weaverNumber.name;
          } else if (item.weaverNumber && this.list) {
            // If only ID is available, find weaver name from current list
            const weaver = this.list.find(w => w.id === item.weaverNumber);
            weaverName = weaver ? `${weaver.branch} - ${weaver.weaver}` : '';
          }

          const transformedItem: UserData = {
            SrNo: index + 1,
            CarpetNo: item.receiveNo || '',
            CDate: this.customeServices.convertDate(item.receivingDate),
            IssueNo: issueNo.Br_issueNo || '',
            IDate: this.customeServices.convertDate(issueNo.date),
            Weaver: weaverName,
            Quality: issueNo.quality?.quality || '',
            Design: issueNo.design?.design || '',
            Border: issueNo.borderColour || '',
            Size: issueNo.size?.sizeInYard || '',
            Pcs: item.pcs?.toString() || '1',
            Area: item.area?.toString() || '0',
            Rate: issueNo.rate?.toString() || '0',
            Amount: item.amount?.toString() || '0',
            _id: item._id // Store the carpet ID for delete functionality
          };
          ELEMENT_DATA.push(transformedItem);
        });

        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.ngAfterViewInit();
      },
      error: (err) => {
        console.error('Error loading saved carpet data:', err);
        this.alert.error('error', 'Failed to load saved carpet data');
      }
    });
  }






  exit() {
    // Clear all data immediately
    ELEMENT_DATA.length = 0;
    this.dataSource = new MatTableDataSource(ELEMENT_DATA);
    this.issuePcs = null;
    this.pcsCount = 0;
    this.previewRow = null;
    this.filterData = [];
    if (this.frmReceivingCarpet) {
      this.frmReceivingCarpet.reset({ date: new Date() });
    }

    // Set flag to indicate user has exited
    sessionStorage.setItem('carpetReceivingExited', 'true');

    // Navigate to dashboard
    this.router.navigate(['/admin/dashboard']);
  }

  // Delete carpet received function
  deleteCarpetReceived(row: UserData) {
    if (!row._id) {
      this.alert.error('error', 'Cannot delete: Carpet ID not found');
      return;
    }

    Swal.fire({
      title: 'Delete Confirmation',
      text: `Are you sure you want to delete carpet ${row.CarpetNo}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#d33'
    }).then((result) => {
      if (result.isConfirmed) {
        this.http.delete(`http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied/${row._id}`).subscribe({
          next: (response: any) => {
            // Clear the exit flag so remaining data will be visible
            sessionStorage.removeItem('carpetReceivingExited');

            // Show success message
            Swal.fire({
              title: 'Deleted!',
              text: 'Carpet has been deleted successfully.',
              icon: 'success',
              confirmButtonText: 'OK'
            });

            // Refresh the data
            this.loadSavedCarpetData(); // Reload order list
            this.getWeaverName(); // Refresh weaver data to update PCS fields

            // If current issue is selected, refresh its preview immediately
            if (this.issuePcs) {
              // Update the preview row PCS count immediately
              const currentWaitingPcs = this.issuePcs.PCSWaitingToBeReceived || parseInt(this.issuePcs.pcs);
              const newWaitingPcs = currentWaitingPcs + 1; // Add 1 back since we deleted 1 piece

              // Update the preview row with new PCS count
              if (this.previewRow) {
                this.previewRow.Pcs = newWaitingPcs;

                // Recalculate area and amount for new PCS count
                let areaPerPiece = 0;
                if (this.issuePcs.areaIn === 'Sq.Feet' && this.issuePcs.size?.areaInFeet) {
                  areaPerPiece = parseFloat(this.issuePcs.size.areaInFeet);
                } else if (this.issuePcs.areaIn === 'Sq.Yard' && this.issuePcs.size?.areaInYard) {
                  areaPerPiece = parseFloat(this.issuePcs.size.areaInYard);
                } else {
                  areaPerPiece = parseFloat(this.issuePcs.area) / parseInt(this.issuePcs.pcs) || 0;
                }

                const totalArea = areaPerPiece * newWaitingPcs;
                const areaUnit = this.issuePcs.areaIn === 'Sq.Feet' ? 'Ft' : (this.issuePcs.areaIn === 'Sq.Yard' ? 'Yd' : '');                this.previewRow.Area = totalArea + (areaUnit ? ' ' + areaUnit : '');
                // Calculate amount with proper number handling
                this.previewRow.Amount = (totalArea * parseFloat(this.issuePcs.rate || '0')).toFixed(2);
              }

              // Also refresh the issue data for accurate PCS fields
              this.selectedIssue(this.issuePcs._id);
            }
          },
          error: (err) => {
            this.alert.error('error', 'Failed to delete carpet: ' + (err.error?.message || 'Unknown error'));
          }
        });
      }
    });
  }



  // Format area to show only 2 decimal places
  formatArea(area: string): string {
    if (!area) return '0.00';

    // Extract the numeric part and unit from the area string
    const match = area.toString().match(/^([\d.]+)\s*(.*)$/);
    if (match) {
      const numericValue = parseFloat(match[1]);
      const unit = match[2] || '';
      return `${numericValue.toFixed(2)} ${unit}`.trim();
    }

    // If no match, try to parse as number
    const numericValue = parseFloat(area.toString());
    if (!isNaN(numericValue)) {
      return numericValue.toFixed(2);
    }

    return area.toString();
  }

  @HostListener('document:keydown.enter', ['$event'])
  handleEnterKey(event: KeyboardEvent) {
    // Check if any modal/dialog is open before refreshing
    const modalBackdrop = document.querySelector('.modal-backdrop');
    const sweetAlert = document.querySelector('.swal2-container');

    if (!modalBackdrop && !sweetAlert) {
      // Prevent default Enter key behavior
      event.preventDefault();

      // Clear ELEMENT_DATA and refresh
      ELEMENT_DATA.length = 0;
      this.dataSource = new MatTableDataSource(ELEMENT_DATA);

      // Refresh form and data
      this.frmReceivingCarpet.reset({ date: new Date() });
      this.issuePcs = null;
      this.pcsCount = 0;
      // this.isMap = false;
      this.getLastReceiveNo();
      this.getWeaverName();
    }
  }
}
