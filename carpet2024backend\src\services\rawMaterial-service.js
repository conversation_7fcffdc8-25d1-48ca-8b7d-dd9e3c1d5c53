const rawMaterialGroupRepository = require('../repositories/rawMaterial-repository');

const createRawMaterialGroup = async (data) => {
  return await rawMaterialGroupRepository.createRawMaterialGroup(data);
};

const getAllRawMaterialGroups = async () => {
  return await rawMaterialGroupRepository.getAllRawMaterialGroups();
};

const getRawMaterialGroupById = async (id) => {
  return await rawMaterialGroupRepository.getRawMaterialGroupById(id);
};

const updateRawMaterialGroup = async (id, data) => {
  return await rawMaterialGroupRepository.updateRawMaterialGroup(id, data);
};

const deleteRawMaterialGroup = async (id) => {
  return await rawMaterialGroupRepository.deleteRawMaterialGroup(id);
};

module.exports = {
  createRawMaterialGroup,
  getAllRawMaterialGroups,
  getRawMaterialGroupById,
  updateRawMaterialGroup,
  deleteRawMaterialGroup
};
