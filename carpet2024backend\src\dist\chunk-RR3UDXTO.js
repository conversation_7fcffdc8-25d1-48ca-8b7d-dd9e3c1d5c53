import{a as S,b as B,f as yt,g as Me,m as x,n as Ce,o as ue}from"./chunk-RGNDWIHZ.js";import{X as E,_ as gt}from"./chunk-YIUCZFK7.js";import{a as he,c as pt}from"./chunk-P2VZOJAX.js";function _t(n){return new E(3e3,!1)}function Ht(){return new E(3100,!1)}function Yt(){return new E(3101,!1)}function Xt(n){return new E(3001,!1)}function Zt(n){return new E(3003,!1)}function Jt(n){return new E(3004,!1)}function xt(n,e){return new E(3005,!1)}function es(){return new E(3006,!1)}function ts(){return new E(3007,!1)}function ss(n,e){return new E(3008,!1)}function is(n){return new E(3002,!1)}function ns(n,e,t,s,i){return new E(3010,!1)}function rs(){return new E(3011,!1)}function as(){return new E(3012,!1)}function os(){return new E(3200,!1)}function ls(){return new E(3202,!1)}function hs(){return new E(3013,!1)}function us(n){return new E(3014,!1)}function cs(n){return new E(3015,!1)}function fs(n){return new E(3016,!1)}function ds(n){return new E(3500,!1)}function ms(n){return new E(3501,!1)}function ps(n,e){return new E(3404,!1)}function gs(n){return new E(3502,!1)}function ys(n){return new E(3503,!1)}function _s(){return new E(3300,!1)}function Es(n){return new E(3504,!1)}function Ss(n){return new E(3301,!1)}function Ts(n,e){return new E(3302,!1)}function vs(n){return new E(3303,!1)}function ws(n,e){return new E(3400,!1)}function bs(n){return new E(3401,!1)}function As(n){return new E(3402,!1)}function Ps(n,e){return new E(3505,!1)}var Ns=new Set(["-moz-outline-radius","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius-topleft","-moz-outline-radius-topright","-ms-grid-columns","-ms-grid-rows","-webkit-line-clamp","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color","accent-color","all","backdrop-filter","background","background-color","background-position","background-size","block-size","border","border-block-end","border-block-end-color","border-block-end-width","border-block-start","border-block-start-color","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-width","border-color","border-end-end-radius","border-end-start-radius","border-image-outset","border-image-slice","border-image-width","border-inline-end","border-inline-end-color","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-width","border-left","border-left-color","border-left-width","border-radius","border-right","border-right-color","border-right-width","border-start-end-radius","border-start-start-radius","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-width","border-width","bottom","box-shadow","caret-color","clip","clip-path","color","column-count","column-gap","column-rule","column-rule-color","column-rule-width","column-width","columns","filter","flex","flex-basis","flex-grow","flex-shrink","font","font-size","font-size-adjust","font-stretch","font-variation-settings","font-weight","gap","grid-column-gap","grid-gap","grid-row-gap","grid-template-columns","grid-template-rows","height","inline-size","input-security","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","left","letter-spacing","line-clamp","line-height","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","mask","mask-border","mask-position","mask-size","max-block-size","max-height","max-inline-size","max-lines","max-width","min-block-size","min-height","min-inline-size","min-width","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","outline","outline-color","outline-offset","outline-width","padding","padding-block-end","padding-block-start","padding-bottom","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","perspective","perspective-origin","right","rotate","row-gap","scale","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-coordinate","scroll-snap-destination","scrollbar-color","shape-image-threshold","shape-margin","shape-outside","tab-size","text-decoration","text-decoration-color","text-decoration-thickness","text-emphasis","text-emphasis-color","text-indent","text-shadow","text-underline-offset","top","transform","transform-origin","translate","vertical-align","visibility","width","word-spacing","z-index","zoom"]);function U(n){switch(n.length){case 0:return new x;case 1:return n[0];default:return new Ce(n)}}function It(n,e,t=new Map,s=new Map){let i=[],r=[],a=-1,o=null;if(e.forEach(l=>{let h=l.get("offset"),c=h==a,u=c&&o||new Map;l.forEach((_,y)=>{let d=y,g=_;if(y!=="offset")switch(d=n.normalizePropertyName(d,i),g){case ue:g=t.get(y);break;case B:g=s.get(y);break;default:g=n.normalizeStyleValue(y,d,g,i);break}u.set(d,g)}),c||r.push(u),o=u,a=h}),i.length)throw gs(i);return r}function tt(n,e,t,s){switch(e){case"start":n.onStart(()=>s(t&&ke(t,"start",n)));break;case"done":n.onDone(()=>s(t&&ke(t,"done",n)));break;case"destroy":n.onDestroy(()=>s(t&&ke(t,"destroy",n)));break}}function ke(n,e,t){let s=t.totalTime,i=!!t.disabled,r=st(n.element,n.triggerName,n.fromState,n.toState,e||n.phaseName,s??n.totalTime,i),a=n._data;return a!=null&&(r._data=a),r}function st(n,e,t,s,i="",r=0,a){return{element:n,triggerName:e,fromState:t,toState:s,phaseName:i,totalTime:r,disabled:!!a}}function L(n,e,t){let s=n.get(e);return s||n.set(e,s=t),s}function Et(n){let e=n.indexOf(":"),t=n.substring(1,e),s=n.slice(e+1);return[t,s]}var Ms=typeof document>"u"?null:document.documentElement;function it(n){let e=n.parentNode||n.host||null;return e===Ms?null:e}function Cs(n){return n.substring(1,6)=="ebkit"}var H=null,St=!1;function ks(n){H||(H=Ds()||{},St=H.style?"WebkitAppearance"in H.style:!1);let e=!0;return H.style&&!Cs(n)&&(e=n in H.style,!e&&St&&(e="Webkit"+n.charAt(0).toUpperCase()+n.slice(1)in H.style)),e}function Ai(n){return Ns.has(n)}function Ds(){return typeof document<"u"?document.body:null}function zt(n,e){for(;e;){if(e===n)return!0;e=it(e)}return!1}function Kt(n,e,t){if(t)return Array.from(n.querySelectorAll(e));let s=n.querySelector(e);return s?[s]:[]}var qt=(()=>{let e=class e{validateStyleProperty(s){return ks(s)}matchesElement(s,i){return!1}containsElement(s,i){return zt(s,i)}getParentElement(s){return it(s)}query(s,i,r){return Kt(s,i,r)}computeStyle(s,i,r){return r||""}animate(s,i,r,a,o,l=[],h){return new x(r,a)}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=gt({token:e,factory:e.\u0275fac});let n=e;return n})(),ut=class ut{};ut.NOOP=new qt;var Tt=ut,Ie=class{},ze=class{normalizePropertyName(e,t){return e}normalizeStyleValue(e,t,s,i){return s}},Rs=1e3,Bt="{{",Os="}}",nt="ng-enter",ge="ng-leave",ce="ng-trigger",ye=".ng-trigger",vt="ng-animating",Ke=".ng-animating";function $(n){if(typeof n=="number")return n;let e=n.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:qe(parseFloat(e[1]),e[2])}function qe(n,e){switch(e){case"s":return n*Rs;default:return n}}function _e(n,e,t){return n.hasOwnProperty("duration")?n:Ls(n,e,t)}function Ls(n,e,t){let s=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,i,r=0,a="";if(typeof n=="string"){let o=n.match(s);if(o===null)return e.push(_t(n)),{duration:0,delay:0,easing:""};i=qe(parseFloat(o[1]),o[2]);let l=o[3];l!=null&&(r=qe(parseFloat(l),o[4]));let h=o[5];h&&(a=h)}else i=n;if(!t){let o=!1,l=e.length;i<0&&(e.push(Ht()),o=!0),r<0&&(e.push(Yt()),o=!0),o&&e.splice(l,0,_t(n))}return{duration:i,delay:r,easing:a}}function Fs(n){return n.length?n[0]instanceof Map?n:n.map(e=>new Map(Object.entries(e))):[]}function wt(n){return Array.isArray(n)?new Map(...n):new Map(n)}function Q(n,e,t){e.forEach((s,i)=>{let r=rt(i);t&&!t.has(i)&&t.set(i,n.style[r]),n.style[r]=s})}function X(n,e){e.forEach((t,s)=>{let i=rt(s);n.style[i]=""})}function ie(n){return Array.isArray(n)?n.length==1?n[0]:yt(n):n}function Is(n,e,t){let s=e.params||{},i=Qt(n);i.length&&i.forEach(r=>{s.hasOwnProperty(r)||t.push(Xt(r))})}var Be=new RegExp(`${Bt}\\s*(.+?)\\s*${Os}`,"g");function Qt(n){let e=[];if(typeof n=="string"){let t;for(;t=Be.exec(n);)e.push(t[1]);Be.lastIndex=0}return e}function re(n,e,t){let s=`${n}`,i=s.replace(Be,(r,a)=>{let o=e[a];return o==null&&(t.push(Zt(a)),o=""),o.toString()});return i==s?n:i}var zs=/-+([a-z0-9])/g;function rt(n){return n.replace(zs,(...e)=>e[1].toUpperCase())}function Pi(n){return n.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function Ks(n,e){return n===0||e===0}function qs(n,e,t){if(t.size&&e.length){let s=e[0],i=[];if(t.forEach((r,a)=>{s.has(a)||i.push(a),s.set(a,r)}),i.length)for(let r=1;r<e.length;r++){let a=e[r];i.forEach(o=>a.set(o,at(n,o)))}}return e}function O(n,e,t){switch(e.type){case S.Trigger:return n.visitTrigger(e,t);case S.State:return n.visitState(e,t);case S.Transition:return n.visitTransition(e,t);case S.Sequence:return n.visitSequence(e,t);case S.Group:return n.visitGroup(e,t);case S.Animate:return n.visitAnimate(e,t);case S.Keyframes:return n.visitKeyframes(e,t);case S.Style:return n.visitStyle(e,t);case S.Reference:return n.visitReference(e,t);case S.AnimateChild:return n.visitAnimateChild(e,t);case S.AnimateRef:return n.visitAnimateRef(e,t);case S.Query:return n.visitQuery(e,t);case S.Stagger:return n.visitStagger(e,t);default:throw Jt(e.type)}}function at(n,e){return window.getComputedStyle(n)[e]}var Bs=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),Qe=class extends Ie{normalizePropertyName(e,t){return rt(e)}normalizeStyleValue(e,t,s,i){let r="",a=s.toString().trim();if(Bs.has(t)&&s!==0&&s!=="0")if(typeof s=="number")r="px";else{let o=s.match(/^[+-]?[\d\.]+([a-z]*)$/);o&&o[1].length==0&&i.push(xt(e,s))}return a+r}};var Ee="*";function Qs(n,e){let t=[];return typeof n=="string"?n.split(/\s*,\s*/).forEach(s=>$s(s,t,e)):t.push(n),t}function $s(n,e,t){if(n[0]==":"){let l=Vs(n,t);if(typeof l=="function"){e.push(l);return}n=l}let s=n.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(s==null||s.length<4)return t.push(cs(n)),e;let i=s[1],r=s[2],a=s[3];e.push(bt(i,a));let o=i==Ee&&a==Ee;r[0]=="<"&&!o&&e.push(bt(a,i))}function Vs(n,e){switch(n){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(t,s)=>parseFloat(s)>parseFloat(t);case":decrement":return(t,s)=>parseFloat(s)<parseFloat(t);default:return e.push(fs(n)),"* => *"}}var fe=new Set(["true","1"]),de=new Set(["false","0"]);function bt(n,e){let t=fe.has(n)||de.has(n),s=fe.has(e)||de.has(e);return(i,r)=>{let a=n==Ee||n==i,o=e==Ee||e==r;return!a&&t&&typeof i=="boolean"&&(a=i?fe.has(n):de.has(n)),!o&&s&&typeof r=="boolean"&&(o=r?fe.has(e):de.has(e)),a&&o}}var $t=":self",Us=new RegExp(`s*${$t}s*,?`,"g");function ot(n,e,t,s){return new $e(n).build(e,t,s)}var At="",$e=class{constructor(e){this._driver=e}build(e,t,s){let i=new Ve(t);return this._resetContextStyleTimingState(i),O(this,ie(e),i)}_resetContextStyleTimingState(e){e.currentQuerySelector=At,e.collectedStyles=new Map,e.collectedStyles.set(At,new Map),e.currentTime=0}visitTrigger(e,t){let s=t.queryCount=0,i=t.depCount=0,r=[],a=[];return e.name.charAt(0)=="@"&&t.errors.push(es()),e.definitions.forEach(o=>{if(this._resetContextStyleTimingState(t),o.type==S.State){let l=o,h=l.name;h.toString().split(/\s*,\s*/).forEach(c=>{l.name=c,r.push(this.visitState(l,t))}),l.name=h}else if(o.type==S.Transition){let l=this.visitTransition(o,t);s+=l.queryCount,i+=l.depCount,a.push(l)}else t.errors.push(ts())}),{type:S.Trigger,name:e.name,states:r,transitions:a,queryCount:s,depCount:i,options:null}}visitState(e,t){let s=this.visitStyle(e.styles,t),i=e.options&&e.options.params||null;if(s.containsDynamicStyles){let r=new Set,a=i||{};s.styles.forEach(o=>{o instanceof Map&&o.forEach(l=>{Qt(l).forEach(h=>{a.hasOwnProperty(h)||r.add(h)})})}),r.size&&t.errors.push(ss(e.name,[...r.values()]))}return{type:S.State,name:e.name,style:s,options:i?{params:i}:null}}visitTransition(e,t){t.queryCount=0,t.depCount=0;let s=O(this,ie(e.animation),t),i=Qs(e.expr,t.errors);return{type:S.Transition,matchers:i,animation:s,queryCount:t.queryCount,depCount:t.depCount,options:Y(e.options)}}visitSequence(e,t){return{type:S.Sequence,steps:e.steps.map(s=>O(this,s,t)),options:Y(e.options)}}visitGroup(e,t){let s=t.currentTime,i=0,r=e.steps.map(a=>{t.currentTime=s;let o=O(this,a,t);return i=Math.max(i,t.currentTime),o});return t.currentTime=i,{type:S.Group,steps:r,options:Y(e.options)}}visitAnimate(e,t){let s=Hs(e.timings,t.errors);t.currentAnimateTimings=s;let i,r=e.styles?e.styles:Me({});if(r.type==S.Keyframes)i=this.visitKeyframes(r,t);else{let a=e.styles,o=!1;if(!a){o=!0;let h={};s.easing&&(h.easing=s.easing),a=Me(h)}t.currentTime+=s.duration+s.delay;let l=this.visitStyle(a,t);l.isEmptyStep=o,i=l}return t.currentAnimateTimings=null,{type:S.Animate,timings:s,style:i,options:null}}visitStyle(e,t){let s=this._makeStyleAst(e,t);return this._validateStyleAst(s,t),s}_makeStyleAst(e,t){let s=[],i=Array.isArray(e.styles)?e.styles:[e.styles];for(let o of i)typeof o=="string"?o===B?s.push(o):t.errors.push(is(o)):s.push(new Map(Object.entries(o)));let r=!1,a=null;return s.forEach(o=>{if(o instanceof Map&&(o.has("easing")&&(a=o.get("easing"),o.delete("easing")),!r)){for(let l of o.values())if(l.toString().indexOf(Bt)>=0){r=!0;break}}}),{type:S.Style,styles:s,easing:a,offset:e.offset,containsDynamicStyles:r,options:null}}_validateStyleAst(e,t){let s=t.currentAnimateTimings,i=t.currentTime,r=t.currentTime;s&&r>0&&(r-=s.duration+s.delay),e.styles.forEach(a=>{typeof a!="string"&&a.forEach((o,l)=>{let h=t.collectedStyles.get(t.currentQuerySelector),c=h.get(l),u=!0;c&&(r!=i&&r>=c.startTime&&i<=c.endTime&&(t.errors.push(ns(l,c.startTime,c.endTime,r,i)),u=!1),r=c.startTime),u&&h.set(l,{startTime:r,endTime:i}),t.options&&Is(o,t.options,t.errors)})})}visitKeyframes(e,t){let s={type:S.Keyframes,styles:[],options:null};if(!t.currentAnimateTimings)return t.errors.push(rs()),s;let i=1,r=0,a=[],o=!1,l=!1,h=0,c=e.steps.map(b=>{let A=this._makeStyleAst(b,t),C=A.offset!=null?A.offset:Gs(A.styles),N=0;return C!=null&&(r++,N=A.offset=C),l=l||N<0||N>1,o=o||N<h,h=N,a.push(N),A});l&&t.errors.push(as()),o&&t.errors.push(os());let u=e.steps.length,_=0;r>0&&r<u?t.errors.push(ls()):r==0&&(_=i/(u-1));let y=u-1,d=t.currentTime,g=t.currentAnimateTimings,v=g.duration;return c.forEach((b,A)=>{let C=_>0?A==y?1:_*A:a[A],N=C*v;t.currentTime=d+g.delay+N,g.duration=N,this._validateStyleAst(b,t),b.offset=C,s.styles.push(b)}),s}visitReference(e,t){return{type:S.Reference,animation:O(this,ie(e.animation),t),options:Y(e.options)}}visitAnimateChild(e,t){return t.depCount++,{type:S.AnimateChild,options:Y(e.options)}}visitAnimateRef(e,t){return{type:S.AnimateRef,animation:this.visitReference(e.animation,t),options:Y(e.options)}}visitQuery(e,t){let s=t.currentQuerySelector,i=e.options||{};t.queryCount++,t.currentQuery=e;let[r,a]=js(e.selector);t.currentQuerySelector=s.length?s+" "+r:r,L(t.collectedStyles,t.currentQuerySelector,new Map);let o=O(this,ie(e.animation),t);return t.currentQuery=null,t.currentQuerySelector=s,{type:S.Query,selector:r,limit:i.limit||0,optional:!!i.optional,includeSelf:a,animation:o,originalSelector:e.selector,options:Y(e.options)}}visitStagger(e,t){t.currentQuery||t.errors.push(hs());let s=e.timings==="full"?{duration:0,delay:0,easing:"full"}:_e(e.timings,t.errors,!0);return{type:S.Stagger,animation:O(this,ie(e.animation),t),timings:s,options:null}}};function js(n){let e=!!n.split(/\s*,\s*/).find(t=>t==$t);return e&&(n=n.replace(Us,"")),n=n.replace(/@\*/g,ye).replace(/@\w+/g,t=>ye+"-"+t.slice(1)).replace(/:animating/g,Ke),[n,e]}function Ws(n){return n?he({},n):null}var Ve=class{constructor(e){this.errors=e,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}};function Gs(n){if(typeof n=="string")return null;let e=null;if(Array.isArray(n))n.forEach(t=>{if(t instanceof Map&&t.has("offset")){let s=t;e=parseFloat(s.get("offset")),s.delete("offset")}});else if(n instanceof Map&&n.has("offset")){let t=n;e=parseFloat(t.get("offset")),t.delete("offset")}return e}function Hs(n,e){if(n.hasOwnProperty("duration"))return n;if(typeof n=="number"){let r=_e(n,e).duration;return De(r,0,"")}let t=n;if(t.split(/\s+/).some(r=>r.charAt(0)=="{"&&r.charAt(1)=="{")){let r=De(0,0,"");return r.dynamic=!0,r.strValue=t,r}let i=_e(t,e);return De(i.duration,i.delay,i.easing)}function Y(n){return n?(n=he({},n),n.params&&(n.params=Ws(n.params))):n={},n}function De(n,e,t){return{duration:n,delay:e,easing:t}}function lt(n,e,t,s,i,r,a=null,o=!1){return{type:1,element:n,keyframes:e,preStyleProps:t,postStyleProps:s,duration:i,delay:r,totalTime:i+r,easing:a,subTimeline:o}}var se=class{constructor(){this._map=new Map}get(e){return this._map.get(e)||[]}append(e,t){let s=this._map.get(e);s||this._map.set(e,s=[]),s.push(...t)}has(e){return this._map.has(e)}clear(){this._map.clear()}},Ys=1,Xs=":enter",Zs=new RegExp(Xs,"g"),Js=":leave",xs=new RegExp(Js,"g");function ht(n,e,t,s,i,r=new Map,a=new Map,o,l,h=[]){return new Ue().buildKeyframes(n,e,t,s,i,r,a,o,l,h)}var Ue=class{buildKeyframes(e,t,s,i,r,a,o,l,h,c=[]){h=h||new se;let u=new je(e,t,h,i,r,c,[]);u.options=l;let _=l.delay?$(l.delay):0;u.currentTimeline.delayNextStep(_),u.currentTimeline.setStyles([a],null,u.errors,l),O(this,s,u);let y=u.timelines.filter(d=>d.containsAnimation());if(y.length&&o.size){let d;for(let g=y.length-1;g>=0;g--){let v=y[g];if(v.element===t){d=v;break}}d&&!d.allowOnlyTimelineStyles()&&d.setStyles([o],null,u.errors,l)}return y.length?y.map(d=>d.buildKeyframes()):[lt(t,[],[],[],0,_,"",!1)]}visitTrigger(e,t){}visitState(e,t){}visitTransition(e,t){}visitAnimateChild(e,t){let s=t.subInstructions.get(t.element);if(s){let i=t.createSubContext(e.options),r=t.currentTimeline.currentTime,a=this._visitSubInstructions(s,i,i.options);r!=a&&t.transformIntoNewTimeline(a)}t.previousNode=e}visitAnimateRef(e,t){let s=t.createSubContext(e.options);s.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],t,s),this.visitReference(e.animation,s),t.transformIntoNewTimeline(s.currentTimeline.currentTime),t.previousNode=e}_applyAnimationRefDelays(e,t,s){for(let i of e){let r=i?.delay;if(r){let a=typeof r=="number"?r:$(re(r,i?.params??{},t.errors));s.delayNextStep(a)}}}_visitSubInstructions(e,t,s){let r=t.currentTimeline.currentTime,a=s.duration!=null?$(s.duration):null,o=s.delay!=null?$(s.delay):null;return a!==0&&e.forEach(l=>{let h=t.appendInstructionToTimeline(l,a,o);r=Math.max(r,h.duration+h.delay)}),r}visitReference(e,t){t.updateOptions(e.options,!0),O(this,e.animation,t),t.previousNode=e}visitSequence(e,t){let s=t.subContextCount,i=t,r=e.options;if(r&&(r.params||r.delay)&&(i=t.createSubContext(r),i.transformIntoNewTimeline(),r.delay!=null)){i.previousNode.type==S.Style&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=Se);let a=$(r.delay);i.delayNextStep(a)}e.steps.length&&(e.steps.forEach(a=>O(this,a,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>s&&i.transformIntoNewTimeline()),t.previousNode=e}visitGroup(e,t){let s=[],i=t.currentTimeline.currentTime,r=e.options&&e.options.delay?$(e.options.delay):0;e.steps.forEach(a=>{let o=t.createSubContext(e.options);r&&o.delayNextStep(r),O(this,a,o),i=Math.max(i,o.currentTimeline.currentTime),s.push(o.currentTimeline)}),s.forEach(a=>t.currentTimeline.mergeTimelineCollectedStyles(a)),t.transformIntoNewTimeline(i),t.previousNode=e}_visitTiming(e,t){if(e.dynamic){let s=e.strValue,i=t.params?re(s,t.params,t.errors):s;return _e(i,t.errors)}else return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,t){let s=t.currentAnimateTimings=this._visitTiming(e.timings,t),i=t.currentTimeline;s.delay&&(t.incrementTime(s.delay),i.snapshotCurrentStyles());let r=e.style;r.type==S.Keyframes?this.visitKeyframes(r,t):(t.incrementTime(s.duration),this.visitStyle(r,t),i.applyStylesToKeyframe()),t.currentAnimateTimings=null,t.previousNode=e}visitStyle(e,t){let s=t.currentTimeline,i=t.currentAnimateTimings;!i&&s.hasCurrentStyleProperties()&&s.forwardFrame();let r=i&&i.easing||e.easing;e.isEmptyStep?s.applyEmptyStep(r):s.setStyles(e.styles,r,t.errors,t.options),t.previousNode=e}visitKeyframes(e,t){let s=t.currentAnimateTimings,i=t.currentTimeline.duration,r=s.duration,o=t.createSubContext().currentTimeline;o.easing=s.easing,e.styles.forEach(l=>{let h=l.offset||0;o.forwardTime(h*r),o.setStyles(l.styles,l.easing,t.errors,t.options),o.applyStylesToKeyframe()}),t.currentTimeline.mergeTimelineCollectedStyles(o),t.transformIntoNewTimeline(i+r),t.previousNode=e}visitQuery(e,t){let s=t.currentTimeline.currentTime,i=e.options||{},r=i.delay?$(i.delay):0;r&&(t.previousNode.type===S.Style||s==0&&t.currentTimeline.hasCurrentStyleProperties())&&(t.currentTimeline.snapshotCurrentStyles(),t.previousNode=Se);let a=s,o=t.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!i.optional,t.errors);t.currentQueryTotal=o.length;let l=null;o.forEach((h,c)=>{t.currentQueryIndex=c;let u=t.createSubContext(e.options,h);r&&u.delayNextStep(r),h===t.element&&(l=u.currentTimeline),O(this,e.animation,u),u.currentTimeline.applyStylesToKeyframe();let _=u.currentTimeline.currentTime;a=Math.max(a,_)}),t.currentQueryIndex=0,t.currentQueryTotal=0,t.transformIntoNewTimeline(a),l&&(t.currentTimeline.mergeTimelineCollectedStyles(l),t.currentTimeline.snapshotCurrentStyles()),t.previousNode=e}visitStagger(e,t){let s=t.parentContext,i=t.currentTimeline,r=e.timings,a=Math.abs(r.duration),o=a*(t.currentQueryTotal-1),l=a*t.currentQueryIndex;switch(r.duration<0?"reverse":r.easing){case"reverse":l=o-l;break;case"full":l=s.currentStaggerTime;break}let c=t.currentTimeline;l&&c.delayNextStep(l);let u=c.currentTime;O(this,e.animation,t),t.previousNode=e,s.currentStaggerTime=i.currentTime-u+(i.startTime-s.currentTimeline.startTime)}},Se={},je=class n{constructor(e,t,s,i,r,a,o,l){this._driver=e,this.element=t,this.subInstructions=s,this._enterClassName=i,this._leaveClassName=r,this.errors=a,this.timelines=o,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=Se,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=l||new Te(this._driver,t,0),o.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,t){if(!e)return;let s=e,i=this.options;s.duration!=null&&(i.duration=$(s.duration)),s.delay!=null&&(i.delay=$(s.delay));let r=s.params;if(r){let a=i.params;a||(a=this.options.params={}),Object.keys(r).forEach(o=>{(!t||!a.hasOwnProperty(o))&&(a[o]=re(r[o],a,this.errors))})}}_copyOptions(){let e={};if(this.options){let t=this.options.params;if(t){let s=e.params={};Object.keys(t).forEach(i=>{s[i]=t[i]})}}return e}createSubContext(e=null,t,s){let i=t||this.element,r=new n(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,s||0));return r.previousNode=this.previousNode,r.currentAnimateTimings=this.currentAnimateTimings,r.options=this._copyOptions(),r.updateOptions(e),r.currentQueryIndex=this.currentQueryIndex,r.currentQueryTotal=this.currentQueryTotal,r.parentContext=this,this.subContextCount++,r}transformIntoNewTimeline(e){return this.previousNode=Se,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,t,s){let i={duration:t??e.duration,delay:this.currentTimeline.currentTime+(s??0)+e.delay,easing:""},r=new We(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,i,e.stretchStartingKeyframe);return this.timelines.push(r),i}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,t,s,i,r,a){let o=[];if(i&&o.push(this.element),e.length>0){e=e.replace(Zs,"."+this._enterClassName),e=e.replace(xs,"."+this._leaveClassName);let l=s!=1,h=this._driver.query(this.element,e,l);s!==0&&(h=s<0?h.slice(h.length+s,h.length):h.slice(0,s)),o.push(...h)}return!r&&o.length==0&&a.push(us(t)),o}},Te=class n{constructor(e,t,s,i){this._driver=e,this.element=t,this.startTime=s,this._elementTimelineStylesLookup=i,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(t),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(t,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){let t=this._keyframes.size===1&&this._pendingStyles.size;this.duration||t?(this.forwardTime(this.currentTime+e),t&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,t){return this.applyStylesToKeyframe(),new n(this._driver,e,t||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=Ys,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,t){this._localTimelineStyles.set(e,t),this._globalTimelineStyles.set(e,t),this._styleSummary.set(e,{time:this.currentTime,value:t})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[t,s]of this._globalTimelineStyles)this._backFill.set(t,s||B),this._currentKeyframe.set(t,B);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,t,s,i){t&&this._previousKeyframe.set("easing",t);let r=i&&i.params||{},a=ei(e,this._globalTimelineStyles);for(let[o,l]of a){let h=re(l,r,s);this._pendingStyles.set(o,h),this._localTimelineStyles.has(o)||this._backFill.set(o,this._globalTimelineStyles.get(o)??B),this._updateStyle(o,h)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((e,t)=>{this._currentKeyframe.set(t,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,t)=>{this._currentKeyframe.has(t)||this._currentKeyframe.set(t,e)}))}snapshotCurrentStyles(){for(let[e,t]of this._localTimelineStyles)this._pendingStyles.set(e,t),this._updateStyle(e,t)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let e=[];for(let t in this._currentKeyframe)e.push(t);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((t,s)=>{let i=this._styleSummary.get(s);(!i||t.time>i.time)&&this._updateStyle(s,t.value)})}buildKeyframes(){this.applyStylesToKeyframe();let e=new Set,t=new Set,s=this._keyframes.size===1&&this.duration===0,i=[];this._keyframes.forEach((o,l)=>{let h=new Map([...this._backFill,...o]);h.forEach((c,u)=>{c===ue?e.add(u):c===B&&t.add(u)}),s||h.set("offset",l/this.duration),i.push(h)});let r=[...e.values()],a=[...t.values()];if(s){let o=i[0],l=new Map(o);o.set("offset",0),l.set("offset",1),i=[o,l]}return lt(this.element,i,r,a,this.duration,this.startTime,this.easing,!1)}},We=class extends Te{constructor(e,t,s,i,r,a,o=!1){super(e,t,a.delay),this.keyframes=s,this.preStyleProps=i,this.postStyleProps=r,this._stretchStartingKeyframe=o,this.timings={duration:a.duration,delay:a.delay,easing:a.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:t,duration:s,easing:i}=this.timings;if(this._stretchStartingKeyframe&&t){let r=[],a=s+t,o=t/a,l=new Map(e[0]);l.set("offset",0),r.push(l);let h=new Map(e[0]);h.set("offset",Pt(o)),r.push(h);let c=e.length-1;for(let u=1;u<=c;u++){let _=new Map(e[u]),y=_.get("offset"),d=t+y*s;_.set("offset",Pt(d/a)),r.push(_)}s=a,t=0,i="",e=r}return lt(this.element,e,this.preStyleProps,this.postStyleProps,s,t,i,!0)}};function Pt(n,e=3){let t=Math.pow(10,e-1);return Math.round(n*t)/t}function ei(n,e){let t=new Map,s;return n.forEach(i=>{if(i==="*"){s??=e.keys();for(let r of s)t.set(r,B)}else for(let[r,a]of i)t.set(r,a)}),t}function Nt(n,e,t,s,i,r,a,o,l,h,c,u,_){return{type:0,element:n,triggerName:e,isRemovalTransition:i,fromState:t,fromStyles:r,toState:s,toStyles:a,timelines:o,queriedElements:l,preStyleProps:h,postStyleProps:c,totalTime:u,errors:_}}var Re={},ve=class{constructor(e,t,s){this._triggerName=e,this.ast=t,this._stateStyles=s}match(e,t,s,i){return ti(this.ast.matchers,e,t,s,i)}buildStyles(e,t,s){let i=this._stateStyles.get("*");return e!==void 0&&(i=this._stateStyles.get(e?.toString())||i),i?i.buildStyles(t,s):new Map}build(e,t,s,i,r,a,o,l,h,c){let u=[],_=this.ast.options&&this.ast.options.params||Re,y=o&&o.params||Re,d=this.buildStyles(s,y,u),g=l&&l.params||Re,v=this.buildStyles(i,g,u),b=new Set,A=new Map,C=new Map,N=i==="void",Z={params:Vt(g,_),delay:this.ast.options?.delay},K=c?[]:ht(e,t,this.ast.animation,r,a,d,v,Z,h,u),k=0;return K.forEach(D=>{k=Math.max(D.duration+D.delay,k)}),u.length?Nt(t,this._triggerName,s,i,N,d,v,[],[],A,C,k,u):(K.forEach(D=>{let j=D.element,J=L(A,j,new Set);D.preStyleProps.forEach(W=>J.add(W));let ct=L(C,j,new Set);D.postStyleProps.forEach(W=>ct.add(W)),j!==t&&b.add(j)}),Nt(t,this._triggerName,s,i,N,d,v,K,[...b.values()],A,C,k))}};function ti(n,e,t,s,i){return n.some(r=>r(e,t,s,i))}function Vt(n,e){let t=he({},e);return Object.entries(n).forEach(([s,i])=>{i!=null&&(t[s]=i)}),t}var Ge=class{constructor(e,t,s){this.styles=e,this.defaultParams=t,this.normalizer=s}buildStyles(e,t){let s=new Map,i=Vt(e,this.defaultParams);return this.styles.styles.forEach(r=>{typeof r!="string"&&r.forEach((a,o)=>{a&&(a=re(a,i,t));let l=this.normalizer.normalizePropertyName(o,t);a=this.normalizer.normalizeStyleValue(o,l,a,t),s.set(o,a)})}),s}};function si(n,e,t){return new He(n,e,t)}var He=class{constructor(e,t,s){this.name=e,this.ast=t,this._normalizer=s,this.transitionFactories=[],this.states=new Map,t.states.forEach(i=>{let r=i.options&&i.options.params||{};this.states.set(i.name,new Ge(i.style,r,s))}),Mt(this.states,"true","1"),Mt(this.states,"false","0"),t.transitions.forEach(i=>{this.transitionFactories.push(new ve(e,i,this.states))}),this.fallbackTransition=ii(e,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,t,s,i){return this.transitionFactories.find(a=>a.match(e,t,s,i))||null}matchStyles(e,t,s){return this.fallbackTransition.buildStyles(e,t,s)}};function ii(n,e,t){let s=[(a,o)=>!0],i={type:S.Sequence,steps:[],options:null},r={type:S.Transition,animation:i,matchers:s,options:null,queryCount:0,depCount:0};return new ve(n,r,e)}function Mt(n,e,t){n.has(e)?n.has(t)||n.set(t,n.get(e)):n.has(t)&&n.set(e,n.get(t))}var ni=new se,Ye=class{constructor(e,t,s){this.bodyNode=e,this._driver=t,this._normalizer=s,this._animations=new Map,this._playersById=new Map,this.players=[]}register(e,t){let s=[],i=[],r=ot(this._driver,t,s,i);if(s.length)throw ys(s);i.length&&void 0,this._animations.set(e,r)}_buildPlayer(e,t,s){let i=e.element,r=It(this._normalizer,e.keyframes,t,s);return this._driver.animate(i,r,e.duration,e.delay,e.easing,[],!0)}create(e,t,s={}){let i=[],r=this._animations.get(e),a,o=new Map;if(r?(a=ht(this._driver,t,r,nt,ge,new Map,new Map,s,ni,i),a.forEach(c=>{let u=L(o,c.element,new Map);c.postStyleProps.forEach(_=>u.set(_,null))})):(i.push(_s()),a=[]),i.length)throw Es(i);o.forEach((c,u)=>{c.forEach((_,y)=>{c.set(y,this._driver.computeStyle(u,y,B))})});let l=a.map(c=>{let u=o.get(c.element);return this._buildPlayer(c,new Map,u)}),h=U(l);return this._playersById.set(e,h),h.onDestroy(()=>this.destroy(e)),this.players.push(h),h}destroy(e){let t=this._getPlayer(e);t.destroy(),this._playersById.delete(e);let s=this.players.indexOf(t);s>=0&&this.players.splice(s,1)}_getPlayer(e){let t=this._playersById.get(e);if(!t)throw Ss(e);return t}listen(e,t,s,i){let r=st(t,"","","");return tt(this._getPlayer(e),s,r,i),()=>{}}command(e,t,s,i){if(s=="register"){this.register(e,i[0]);return}if(s=="create"){let a=i[0]||{};this.create(e,t,a);return}let r=this._getPlayer(e);switch(s){case"play":r.play();break;case"pause":r.pause();break;case"reset":r.reset();break;case"restart":r.restart();break;case"finish":r.finish();break;case"init":r.init();break;case"setPosition":r.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(e);break}}},Ct="ng-animate-queued",ri=".ng-animate-queued",Oe="ng-animate-disabled",ai=".ng-animate-disabled",oi="ng-star-inserted",li=".ng-star-inserted",hi=[],Ut={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},ui={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},z="__ng_removed",ae=class{get params(){return this.options.params}constructor(e,t=""){this.namespaceId=t;let s=e&&e.hasOwnProperty("value"),i=s?e.value:e;if(this.value=fi(i),s){let r=e,{value:a}=r,o=pt(r,["value"]);this.options=o}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){let t=e.params;if(t){let s=this.options.params;Object.keys(t).forEach(i=>{s[i]==null&&(s[i]=t[i])})}}},ne="void",Le=new ae(ne),Xe=class{constructor(e,t,s){this.id=e,this.hostElement=t,this._engine=s,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+e,I(t,this._hostClassName)}listen(e,t,s,i){if(!this._triggers.has(t))throw Ts(s,t);if(s==null||s.length==0)throw vs(t);if(!di(s))throw ws(s,t);let r=L(this._elementListeners,e,[]),a={name:t,phase:s,callback:i};r.push(a);let o=L(this._engine.statesByElement,e,new Map);return o.has(t)||(I(e,ce),I(e,ce+"-"+t),o.set(t,Le)),()=>{this._engine.afterFlush(()=>{let l=r.indexOf(a);l>=0&&r.splice(l,1),this._triggers.has(t)||o.delete(t)})}}register(e,t){return this._triggers.has(e)?!1:(this._triggers.set(e,t),!0)}_getTrigger(e){let t=this._triggers.get(e);if(!t)throw bs(e);return t}trigger(e,t,s,i=!0){let r=this._getTrigger(t),a=new oe(this.id,t,e),o=this._engine.statesByElement.get(e);o||(I(e,ce),I(e,ce+"-"+t),this._engine.statesByElement.set(e,o=new Map));let l=o.get(t),h=new ae(s,this.id);if(!(s&&s.hasOwnProperty("value"))&&l&&h.absorbOptions(l.options),o.set(t,h),l||(l=Le),!(h.value===ne)&&l.value===h.value){if(!gi(l.params,h.params)){let g=[],v=r.matchStyles(l.value,l.params,g),b=r.matchStyles(h.value,h.params,g);g.length?this._engine.reportError(g):this._engine.afterFlush(()=>{X(e,v),Q(e,b)})}return}let _=L(this._engine.playersByElement,e,[]);_.forEach(g=>{g.namespaceId==this.id&&g.triggerName==t&&g.queued&&g.destroy()});let y=r.matchTransition(l.value,h.value,e,h.params),d=!1;if(!y){if(!i)return;y=r.fallbackTransition,d=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:t,transition:y,fromState:l,toState:h,player:a,isFallbackTransition:d}),d||(I(e,Ct),a.onStart(()=>{ee(e,Ct)})),a.onDone(()=>{let g=this.players.indexOf(a);g>=0&&this.players.splice(g,1);let v=this._engine.playersByElement.get(e);if(v){let b=v.indexOf(a);b>=0&&v.splice(b,1)}}),this.players.push(a),_.push(a),a}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(t=>t.delete(e)),this._elementListeners.forEach((t,s)=>{this._elementListeners.set(s,t.filter(i=>i.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);let t=this._engine.playersByElement.get(e);t&&(t.forEach(s=>s.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,t){let s=this._engine.driver.query(e,ye,!0);s.forEach(i=>{if(i[z])return;let r=this._engine.fetchNamespacesByElement(i);r.size?r.forEach(a=>a.triggerLeaveAnimation(i,t,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>s.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(e,t,s,i){let r=this._engine.statesByElement.get(e),a=new Map;if(r){let o=[];if(r.forEach((l,h)=>{if(a.set(h,l.value),this._triggers.has(h)){let c=this.trigger(e,h,ne,i);c&&o.push(c)}}),o.length)return this._engine.markElementAsRemoved(this.id,e,!0,t,a),s&&U(o).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){let t=this._elementListeners.get(e),s=this._engine.statesByElement.get(e);if(t&&s){let i=new Set;t.forEach(r=>{let a=r.name;if(i.has(a))return;i.add(a);let l=this._triggers.get(a).fallbackTransition,h=s.get(a)||Le,c=new ae(ne),u=new oe(this.id,a,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:a,transition:l,fromState:h,toState:c,player:u,isFallbackTransition:!0})})}}removeNode(e,t){let s=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,t),this.triggerLeaveAnimation(e,t,!0))return;let i=!1;if(s.totalAnimations){let r=s.players.length?s.playersByQueriedElement.get(e):[];if(r&&r.length)i=!0;else{let a=e;for(;a=a.parentNode;)if(s.statesByElement.get(a)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(e),i)s.markElementAsRemoved(this.id,e,!1,t);else{let r=e[z];(!r||r===Ut)&&(s.afterFlush(()=>this.clearElementCache(e)),s.destroyInnerAnimations(e),s._onRemovalComplete(e,t))}}insertNode(e,t){I(e,this._hostClassName)}drainQueuedTransitions(e){let t=[];return this._queue.forEach(s=>{let i=s.player;if(i.destroyed)return;let r=s.element,a=this._elementListeners.get(r);a&&a.forEach(o=>{if(o.name==s.triggerName){let l=st(r,s.triggerName,s.fromState.value,s.toState.value);l._data=e,tt(s.player,o.phase,l,o.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):t.push(s)}),this._queue=[],t.sort((s,i)=>{let r=s.transition.ast.depCount,a=i.transition.ast.depCount;return r==0||a==0?r-a:this._engine.driver.containsElement(s.element,i.element)?1:-1})}destroy(e){this.players.forEach(t=>t.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}},Ze=class{_onRemovalComplete(e,t){this.onRemovalComplete(e,t)}constructor(e,t,s,i){this.bodyNode=e,this.driver=t,this._normalizer=s,this.scheduler=i,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(r,a)=>{}}get queuedPlayers(){let e=[];return this._namespaceList.forEach(t=>{t.players.forEach(s=>{s.queued&&e.push(s)})}),e}createNamespace(e,t){let s=new Xe(e,t,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,t)?this._balanceNamespaceList(s,t):(this.newHostElements.set(t,s),this.collectEnterElement(t)),this._namespaceLookup[e]=s}_balanceNamespaceList(e,t){let s=this._namespaceList,i=this.namespacesByHostElement;if(s.length-1>=0){let a=!1,o=this.driver.getParentElement(t);for(;o;){let l=i.get(o);if(l){let h=s.indexOf(l);s.splice(h+1,0,e),a=!0;break}o=this.driver.getParentElement(o)}a||s.unshift(e)}else s.push(e);return i.set(t,e),e}register(e,t){let s=this._namespaceLookup[e];return s||(s=this.createNamespace(e,t)),s}registerTrigger(e,t,s){let i=this._namespaceLookup[e];i&&i.register(t,s)&&this.totalAnimations++}destroy(e,t){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let s=this._fetchNamespace(e);this.namespacesByHostElement.delete(s.hostElement);let i=this._namespaceList.indexOf(s);i>=0&&this._namespaceList.splice(i,1),s.destroy(t),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){let t=new Set,s=this.statesByElement.get(e);if(s){for(let i of s.values())if(i.namespaceId){let r=this._fetchNamespace(i.namespaceId);r&&t.add(r)}}return t}trigger(e,t,s,i){if(me(t)){let r=this._fetchNamespace(e);if(r)return r.trigger(t,s,i),!0}return!1}insertNode(e,t,s,i){if(!me(t))return;let r=t[z];if(r&&r.setForRemoval){r.setForRemoval=!1,r.setForMove=!0;let a=this.collectedLeaveElements.indexOf(t);a>=0&&this.collectedLeaveElements.splice(a,1)}if(e){let a=this._fetchNamespace(e);a&&a.insertNode(t,s)}i&&this.collectEnterElement(t)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,t){t?this.disabledNodes.has(e)||(this.disabledNodes.add(e),I(e,Oe)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),ee(e,Oe))}removeNode(e,t,s){if(me(t)){this.scheduler?.notify();let i=e?this._fetchNamespace(e):null;i?i.removeNode(t,s):this.markElementAsRemoved(e,t,!1,s);let r=this.namespacesByHostElement.get(t);r&&r.id!==e&&r.removeNode(t,s)}else this._onRemovalComplete(t,s)}markElementAsRemoved(e,t,s,i,r){this.collectedLeaveElements.push(t),t[z]={namespaceId:e,setForRemoval:i,hasAnimation:s,removedBeforeQueried:!1,previousTriggersValues:r}}listen(e,t,s,i,r){return me(t)?this._fetchNamespace(e).listen(t,s,i,r):()=>{}}_buildInstruction(e,t,s,i,r){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,s,i,e.fromState.options,e.toState.options,t,r)}destroyInnerAnimations(e){let t=this.driver.query(e,ye,!0);t.forEach(s=>this.destroyActiveAnimationsForElement(s)),this.playersByQueriedElement.size!=0&&(t=this.driver.query(e,Ke,!0),t.forEach(s=>this.finishActiveQueriedAnimationOnElement(s)))}destroyActiveAnimationsForElement(e){let t=this.playersByElement.get(e);t&&t.forEach(s=>{s.queued?s.markedForDestroy=!0:s.destroy()})}finishActiveQueriedAnimationOnElement(e){let t=this.playersByQueriedElement.get(e);t&&t.forEach(s=>s.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return U(this.players).onDone(()=>e());e()})}processLeaveNode(e){let t=e[z];if(t&&t.setForRemoval){if(e[z]=Ut,t.namespaceId){this.destroyInnerAnimations(e);let s=this._fetchNamespace(t.namespaceId);s&&s.clearElementCache(e)}this._onRemovalComplete(e,t.setForRemoval)}e.classList?.contains(Oe)&&this.markElementAsDisabled(e,!1),this.driver.query(e,ai,!0).forEach(s=>{this.markElementAsDisabled(s,!1)})}flush(e=-1){let t=[];if(this.newHostElements.size&&(this.newHostElements.forEach((s,i)=>this._balanceNamespaceList(s,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let s=0;s<this.collectedEnterElements.length;s++){let i=this.collectedEnterElements[s];I(i,oi)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let s=[];try{t=this._flushAnimations(s,e)}finally{for(let i=0;i<s.length;i++)s[i]()}}else for(let s=0;s<this.collectedLeaveElements.length;s++){let i=this.collectedLeaveElements[s];this.processLeaveNode(i)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(s=>s()),this._flushFns=[],this._whenQuietFns.length){let s=this._whenQuietFns;this._whenQuietFns=[],t.length?U(t).onDone(()=>{s.forEach(i=>i())}):s.forEach(i=>i())}}reportError(e){throw As(e)}_flushAnimations(e,t){let s=new se,i=[],r=new Map,a=[],o=new Map,l=new Map,h=new Map,c=new Set;this.disabledNodes.forEach(f=>{c.add(f);let m=this.driver.query(f,ri,!0);for(let p=0;p<m.length;p++)c.add(m[p])});let u=this.bodyNode,_=Array.from(this.statesByElement.keys()),y=Rt(_,this.collectedEnterElements),d=new Map,g=0;y.forEach((f,m)=>{let p=nt+g++;d.set(m,p),f.forEach(T=>I(T,p))});let v=[],b=new Set,A=new Set;for(let f=0;f<this.collectedLeaveElements.length;f++){let m=this.collectedLeaveElements[f],p=m[z];p&&p.setForRemoval&&(v.push(m),b.add(m),p.hasAnimation?this.driver.query(m,li,!0).forEach(T=>b.add(T)):A.add(m))}let C=new Map,N=Rt(_,Array.from(b));N.forEach((f,m)=>{let p=ge+g++;C.set(m,p),f.forEach(T=>I(T,p))}),e.push(()=>{y.forEach((f,m)=>{let p=d.get(m);f.forEach(T=>ee(T,p))}),N.forEach((f,m)=>{let p=C.get(m);f.forEach(T=>ee(T,p))}),v.forEach(f=>{this.processLeaveNode(f)})});let Z=[],K=[];for(let f=this._namespaceList.length-1;f>=0;f--)this._namespaceList[f].drainQueuedTransitions(t).forEach(p=>{let T=p.player,P=p.element;if(Z.push(T),this.collectedEnterElements.length){let M=P[z];if(M&&M.setForMove){if(M.previousTriggersValues&&M.previousTriggersValues.has(p.triggerName)){let G=M.previousTriggersValues.get(p.triggerName),F=this.statesByElement.get(p.element);if(F&&F.has(p.triggerName)){let le=F.get(p.triggerName);le.value=G,F.set(p.triggerName,le)}}T.destroy();return}}let q=!u||!this.driver.containsElement(u,P),R=C.get(P),V=d.get(P),w=this._buildInstruction(p,s,V,R,q);if(w.errors&&w.errors.length){K.push(w);return}if(q){T.onStart(()=>X(P,w.fromStyles)),T.onDestroy(()=>Q(P,w.toStyles)),i.push(T);return}if(p.isFallbackTransition){T.onStart(()=>X(P,w.fromStyles)),T.onDestroy(()=>Q(P,w.toStyles)),i.push(T);return}let mt=[];w.timelines.forEach(M=>{M.stretchStartingKeyframe=!0,this.disabledNodes.has(M.element)||mt.push(M)}),w.timelines=mt,s.append(P,w.timelines);let Gt={instruction:w,player:T,element:P};a.push(Gt),w.queriedElements.forEach(M=>L(o,M,[]).push(T)),w.preStyleProps.forEach((M,G)=>{if(M.size){let F=l.get(G);F||l.set(G,F=new Set),M.forEach((le,Ne)=>F.add(Ne))}}),w.postStyleProps.forEach((M,G)=>{let F=h.get(G);F||h.set(G,F=new Set),M.forEach((le,Ne)=>F.add(Ne))})});if(K.length){let f=[];K.forEach(m=>{f.push(Ps(m.triggerName,m.errors))}),Z.forEach(m=>m.destroy()),this.reportError(f)}let k=new Map,D=new Map;a.forEach(f=>{let m=f.element;s.has(m)&&(D.set(m,m),this._beforeAnimationBuild(f.player.namespaceId,f.instruction,k))}),i.forEach(f=>{let m=f.element;this._getPreviousPlayers(m,!1,f.namespaceId,f.triggerName,null).forEach(T=>{L(k,m,[]).push(T),T.destroy()})});let j=v.filter(f=>Ot(f,l,h)),J=new Map;Dt(J,this.driver,A,h,B).forEach(f=>{Ot(f,l,h)&&j.push(f)});let W=new Map;y.forEach((f,m)=>{Dt(W,this.driver,new Set(f),l,ue)}),j.forEach(f=>{let m=J.get(f),p=W.get(f);J.set(f,new Map([...m?.entries()??[],...p?.entries()??[]]))});let Pe=[],ft=[],dt={};a.forEach(f=>{let{element:m,player:p,instruction:T}=f;if(s.has(m)){if(c.has(m)){p.onDestroy(()=>Q(m,T.toStyles)),p.disabled=!0,p.overrideTotalTime(T.totalTime),i.push(p);return}let P=dt;if(D.size>1){let R=m,V=[];for(;R=R.parentNode;){let w=D.get(R);if(w){P=w;break}V.push(R)}V.forEach(w=>D.set(w,P))}let q=this._buildAnimation(p.namespaceId,T,k,r,W,J);if(p.setRealPlayer(q),P===dt)Pe.push(p);else{let R=this.playersByElement.get(P);R&&R.length&&(p.parentPlayer=U(R)),i.push(p)}}else X(m,T.fromStyles),p.onDestroy(()=>Q(m,T.toStyles)),ft.push(p),c.has(m)&&i.push(p)}),ft.forEach(f=>{let m=r.get(f.element);if(m&&m.length){let p=U(m);f.setRealPlayer(p)}}),i.forEach(f=>{f.parentPlayer?f.syncPlayerEvents(f.parentPlayer):f.destroy()});for(let f=0;f<v.length;f++){let m=v[f],p=m[z];if(ee(m,ge),p&&p.hasAnimation)continue;let T=[];if(o.size){let q=o.get(m);q&&q.length&&T.push(...q);let R=this.driver.query(m,Ke,!0);for(let V=0;V<R.length;V++){let w=o.get(R[V]);w&&w.length&&T.push(...w)}}let P=T.filter(q=>!q.destroyed);P.length?mi(this,m,P):this.processLeaveNode(m)}return v.length=0,Pe.forEach(f=>{this.players.push(f),f.onDone(()=>{f.destroy();let m=this.players.indexOf(f);this.players.splice(m,1)}),f.play()}),Pe}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,t,s,i,r){let a=[];if(t){let o=this.playersByQueriedElement.get(e);o&&(a=o)}else{let o=this.playersByElement.get(e);if(o){let l=!r||r==ne;o.forEach(h=>{h.queued||!l&&h.triggerName!=i||a.push(h)})}}return(s||i)&&(a=a.filter(o=>!(s&&s!=o.namespaceId||i&&i!=o.triggerName))),a}_beforeAnimationBuild(e,t,s){let i=t.triggerName,r=t.element,a=t.isRemovalTransition?void 0:e,o=t.isRemovalTransition?void 0:i;for(let l of t.timelines){let h=l.element,c=h!==r,u=L(s,h,[]);this._getPreviousPlayers(h,c,a,o,t.toState).forEach(y=>{let d=y.getRealPlayer();d.beforeDestroy&&d.beforeDestroy(),y.destroy(),u.push(y)})}X(r,t.fromStyles)}_buildAnimation(e,t,s,i,r,a){let o=t.triggerName,l=t.element,h=[],c=new Set,u=new Set,_=t.timelines.map(d=>{let g=d.element;c.add(g);let v=g[z];if(v&&v.removedBeforeQueried)return new x(d.duration,d.delay);let b=g!==l,A=pi((s.get(g)||hi).map(k=>k.getRealPlayer())).filter(k=>{let D=k;return D.element?D.element===g:!1}),C=r.get(g),N=a.get(g),Z=It(this._normalizer,d.keyframes,C,N),K=this._buildPlayer(d,Z,A);if(d.subTimeline&&i&&u.add(g),b){let k=new oe(e,o,g);k.setRealPlayer(K),h.push(k)}return K});h.forEach(d=>{L(this.playersByQueriedElement,d.element,[]).push(d),d.onDone(()=>ci(this.playersByQueriedElement,d.element,d))}),c.forEach(d=>I(d,vt));let y=U(_);return y.onDestroy(()=>{c.forEach(d=>ee(d,vt)),Q(l,t.toStyles)}),u.forEach(d=>{L(i,d,[]).push(y)}),y}_buildPlayer(e,t,s){return t.length>0?this.driver.animate(e.element,t,e.duration,e.delay,e.easing,s):new x(e.duration,e.delay)}},oe=class{constructor(e,t,s){this.namespaceId=e,this.triggerName=t,this.element=s,this._player=new x,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((t,s)=>{t.forEach(i=>tt(e,s,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){let t=this._player;t.triggerCallback&&e.onStart(()=>t.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,t){L(this._queuedCallbacks,e,[]).push(t)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){let t=this._player;t.triggerCallback&&t.triggerCallback(e)}};function ci(n,e,t){let s=n.get(e);if(s){if(s.length){let i=s.indexOf(t);s.splice(i,1)}s.length==0&&n.delete(e)}return s}function fi(n){return n??null}function me(n){return n&&n.nodeType===1}function di(n){return n=="start"||n=="done"}function kt(n,e){let t=n.style.display;return n.style.display=e??"none",t}function Dt(n,e,t,s,i){let r=[];t.forEach(l=>r.push(kt(l)));let a=[];s.forEach((l,h)=>{let c=new Map;l.forEach(u=>{let _=e.computeStyle(h,u,i);c.set(u,_),(!_||_.length==0)&&(h[z]=ui,a.push(h))}),n.set(h,c)});let o=0;return t.forEach(l=>kt(l,r[o++])),a}function Rt(n,e){let t=new Map;if(n.forEach(o=>t.set(o,[])),e.length==0)return t;let s=1,i=new Set(e),r=new Map;function a(o){if(!o)return s;let l=r.get(o);if(l)return l;let h=o.parentNode;return t.has(h)?l=h:i.has(h)?l=s:l=a(h),r.set(o,l),l}return e.forEach(o=>{let l=a(o);l!==s&&t.get(l).push(o)}),t}function I(n,e){n.classList?.add(e)}function ee(n,e){n.classList?.remove(e)}function mi(n,e,t){U(t).onDone(()=>n.processLeaveNode(e))}function pi(n){let e=[];return jt(n,e),e}function jt(n,e){for(let t=0;t<n.length;t++){let s=n[t];s instanceof Ce?jt(s.players,e):e.push(s)}}function gi(n,e){let t=Object.keys(n),s=Object.keys(e);if(t.length!=s.length)return!1;for(let i=0;i<t.length;i++){let r=t[i];if(!e.hasOwnProperty(r)||n[r]!==e[r])return!1}return!0}function Ot(n,e,t){let s=t.get(n);if(!s)return!1;let i=e.get(n);return i?s.forEach(r=>i.add(r)):e.set(n,s),t.delete(n),!0}var we=class{constructor(e,t,s,i){this._driver=t,this._normalizer=s,this._triggerCache={},this.onRemovalComplete=(r,a)=>{},this._transitionEngine=new Ze(e.body,t,s,i),this._timelineEngine=new Ye(e.body,t,s),this._transitionEngine.onRemovalComplete=(r,a)=>this.onRemovalComplete(r,a)}registerTrigger(e,t,s,i,r){let a=e+"-"+i,o=this._triggerCache[a];if(!o){let l=[],h=[],c=ot(this._driver,r,l,h);if(l.length)throw ps(i,l);h.length&&void 0,o=si(i,c,this._normalizer),this._triggerCache[a]=o}this._transitionEngine.registerTrigger(t,i,o)}register(e,t){this._transitionEngine.register(e,t)}destroy(e,t){this._transitionEngine.destroy(e,t)}onInsert(e,t,s,i){this._transitionEngine.insertNode(e,t,s,i)}onRemove(e,t,s){this._transitionEngine.removeNode(e,t,s)}disableAnimations(e,t){this._transitionEngine.markElementAsDisabled(e,t)}process(e,t,s,i){if(s.charAt(0)=="@"){let[r,a]=Et(s),o=i;this._timelineEngine.command(r,t,a,o)}else this._transitionEngine.trigger(e,t,s,i)}listen(e,t,s,i,r){if(s.charAt(0)=="@"){let[a,o]=Et(s);return this._timelineEngine.listen(a,t,o,r)}return this._transitionEngine.listen(e,t,s,i,r)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}};function yi(n,e){let t=null,s=null;return Array.isArray(e)&&e.length?(t=Fe(e[0]),e.length>1&&(s=Fe(e[e.length-1]))):e instanceof Map&&(t=Fe(e)),t||s?new Je(n,t,s):null}var te=class te{constructor(e,t,s){this._element=e,this._startStyles=t,this._endStyles=s,this._state=0;let i=te.initialStylesByElement.get(e);i||te.initialStylesByElement.set(e,i=new Map),this._initialStyles=i}start(){this._state<1&&(this._startStyles&&Q(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(Q(this._element,this._initialStyles),this._endStyles&&(Q(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(te.initialStylesByElement.delete(this._element),this._startStyles&&(X(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(X(this._element,this._endStyles),this._endStyles=null),Q(this._element,this._initialStyles),this._state=3)}};te.initialStylesByElement=new WeakMap;var Je=te;function Fe(n){let e=null;return n.forEach((t,s)=>{_i(s)&&(e=e||new Map,e.set(s,t))}),e}function _i(n){return n==="display"||n==="position"}var be=class{constructor(e,t,s,i){this.element=e,this.keyframes=t,this.options=s,this._specialStyles=i,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=s.duration,this._delay=s.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;let t=()=>this._onFinish();this.domPlayer.addEventListener("finish",t),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",t)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){let t=[];return e.forEach(s=>{t.push(Object.fromEntries(s))}),t}_triggerWebAnimation(e,t,s){return e.animate(this._convertKeyframesToObject(t),s)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((s,i)=>{i!=="offset"&&e.set(i,this._finished?s:at(this.element,i))}),this.currentSnapshot=e}triggerCallback(e){let t=e==="start"?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}},xe=class{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}matchesElement(e,t){return!1}containsElement(e,t){return zt(e,t)}getParentElement(e){return it(e)}query(e,t,s){return Kt(e,t,s)}computeStyle(e,t,s){return at(e,t)}animate(e,t,s,i,r,a=[]){let o=i==0?"both":"forwards",l={duration:s,delay:i,fill:o};r&&(l.easing=r);let h=new Map,c=a.filter(y=>y instanceof be);Ks(s,i)&&c.forEach(y=>{y.currentSnapshot.forEach((d,g)=>h.set(g,d))});let u=Fs(t).map(y=>new Map(y));u=qs(e,u,h);let _=yi(e,u);return new be(e,u,l,_)}};function Ni(n,e,t){return n==="noop"?new we(e,new qt,new ze,t):new we(e,new xe,new Qe,t)}var Lt=class{constructor(e,t){this._driver=e;let s=[],i=[],r=ot(e,t,s,i);if(s.length)throw ds(s);i.length&&void 0,this._animationAst=r}buildTimelines(e,t,s,i,r){let a=Array.isArray(t)?wt(t):t,o=Array.isArray(s)?wt(s):s,l=[];r=r||new se;let h=ht(this._driver,e,this._animationAst,nt,ge,a,o,i,r,l);if(l.length)throw ms(l);return h}},pe="@",Wt="@.disabled",Ae=class{constructor(e,t,s,i){this.namespaceId=e,this.delegate=t,this.engine=s,this._onDestroy=i,this.\u0275type=0}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,t){return this.delegate.createElement(e,t)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,t){this.delegate.appendChild(e,t),this.engine.onInsert(this.namespaceId,t,e,!1)}insertBefore(e,t,s,i=!0){this.delegate.insertBefore(e,t,s),this.engine.onInsert(this.namespaceId,t,e,i)}removeChild(e,t,s){this.engine.onRemove(this.namespaceId,t,this.delegate)}selectRootElement(e,t){return this.delegate.selectRootElement(e,t)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,t,s,i){this.delegate.setAttribute(e,t,s,i)}removeAttribute(e,t,s){this.delegate.removeAttribute(e,t,s)}addClass(e,t){this.delegate.addClass(e,t)}removeClass(e,t){this.delegate.removeClass(e,t)}setStyle(e,t,s,i){this.delegate.setStyle(e,t,s,i)}removeStyle(e,t,s){this.delegate.removeStyle(e,t,s)}setProperty(e,t,s){t.charAt(0)==pe&&t==Wt?this.disableAnimations(e,!!s):this.delegate.setProperty(e,t,s)}setValue(e,t){this.delegate.setValue(e,t)}listen(e,t,s){return this.delegate.listen(e,t,s)}disableAnimations(e,t){this.engine.disableAnimations(e,t)}},et=class extends Ae{constructor(e,t,s,i,r){super(t,s,i,r),this.factory=e,this.namespaceId=t}setProperty(e,t,s){t.charAt(0)==pe?t.charAt(1)=="."&&t==Wt?(s=s===void 0?!0:!!s,this.disableAnimations(e,s)):this.engine.process(this.namespaceId,e,t.slice(1),s):this.delegate.setProperty(e,t,s)}listen(e,t,s){if(t.charAt(0)==pe){let i=Ei(e),r=t.slice(1),a="";return r.charAt(0)!=pe&&([r,a]=Si(r)),this.engine.listen(this.namespaceId,i,r,a,o=>{let l=o._data||-1;this.factory.scheduleListenerCallback(l,s,o)})}return this.delegate.listen(e,t,s)}};function Ei(n){switch(n){case"body":return document.body;case"document":return document;case"window":return window;default:return n}}function Si(n){let e=n.indexOf("."),t=n.substring(0,e),s=n.slice(e+1);return[t,s]}var Ft=class{constructor(e,t,s){this.delegate=e,this.engine=t,this._zone=s,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,t.onRemovalComplete=(i,r)=>{let a=r?.parentNode(i);a&&r.removeChild(a,i)}}createRenderer(e,t){let s="",i=this.delegate.createRenderer(e,t);if(!e||!t?.data?.animation){let h=this._rendererCache,c=h.get(i);if(!c){let u=()=>h.delete(i);c=new Ae(s,i,this.engine,u),h.set(i,c)}return c}let r=t.id,a=t.id+"-"+this._currentId;this._currentId++,this.engine.register(a,e);let o=h=>{Array.isArray(h)?h.forEach(o):this.engine.registerTrigger(r,a,e,h.name,h)};return t.data.animation.forEach(o),new et(this,a,i,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,t,s){if(e>=0&&e<this._microtaskId){this._zone.run(()=>t(s));return}let i=this._animationCallbacksBuffer;i.length==0&&queueMicrotask(()=>{this._zone.run(()=>{i.forEach(r=>{let[a,o]=r;a(o)}),this._animationCallbacksBuffer=[]})}),i.push([t,s])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}};export{Tt as AnimationDriver,qt as NoopAnimationDriver,Lt as \u0275Animation,we as \u0275AnimationEngine,et as \u0275AnimationRenderer,Ft as \u0275AnimationRendererFactory,Ie as \u0275AnimationStyleNormalizer,Ae as \u0275BaseAnimationRenderer,ze as \u0275NoopAnimationStyleNormalizer,xe as \u0275WebAnimationsDriver,be as \u0275WebAnimationsPlayer,Qe as \u0275WebAnimationsStyleNormalizer,Ks as \u0275allowPreviousPlayerStylesMerge,Pi as \u0275camelCaseToDashCase,zt as \u0275containsElement,Ni as \u0275createEngine,it as \u0275getParentElement,Kt as \u0275invokeQuery,Fs as \u0275normalizeKeyframes,ks as \u0275validateStyleProperty,Ai as \u0275validateWebAnimatableStyleProperty};
