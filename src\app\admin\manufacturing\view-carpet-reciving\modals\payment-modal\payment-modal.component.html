<h2 mat-dialog-title class="title mt-2 text-center">Carpet No. {{data.CarpetNo || data.Carpet_No || data.Carpet_no || 'N/A'}} <br> Amount = {{data.NetAmount | number:'1.0-0'}}.00 </h2>
<mat-dialog-content>
  <form [formGroup]="paymentForm">
    <div class="row">
      <div class="col-md-4">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Payment Date</mat-label>
          <input matInput [matDatepicker]="picker" formControlName="PaymentDt" required (keydown.enter)="onPaymentDateEnter()" #paymentDateInput>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
      </div>
    

    <!-- Bank Name -->

  <div class="col-md-8">
  <mat-form-field appearance="outline" class="w-100">
  <mat-label>Bank</mat-label>
  <input
    type="text"
    matInput
    formControlName="BankName"
    [matAutocomplete]="auto"
    autocomplete="off"
    (keydown.enter)="onBankNameEnter()"
    #bankNameInput
  />  <span matSuffix style="margin-right: 12px;">
                <i class="fa-solid fa-caret-down text-secondary"></i>
              </span>
  <mat-autocomplete #auto="matAutocomplete">
    <mat-option *ngFor="let bank of filteredBanks | async" [value]="bank">
      <div class="bank-option">
        <span class="bank-name">{{ bank }}</span>
        <button
          mat-icon-button
          color="warn"
          class="bank-remove-btn"
          matTooltip="Remove"
          (click)="removeBank(bank); $event.stopPropagation()"
        >
      
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>



  </div>
</div>

    <!-- Bank Name -->
<div class="row">
  <div class="col-md-7">
    <mat-form-field appearance="outline" class="w-100 mt-2">
  <mat-label>Cheque No.  Or  RGS No.</mat-label>
  <input matInput formControlName="ChequeNoOrRGSno" maxlength="50" (keydown.enter)="onChequeEnter()" #chequeInput />
</mat-form-field>

    </div>
   
  <div class="col-md-5">
  <mat-form-field appearance="outline" class="w-100 mt-2">
  <mat-label>Payed Amount</mat-label>
  <input
    matInput
    placeholder=".00"
    formControlName="PayedAmount"
    (blur)="setDigit($event)"
    (keydown.enter)="onPayedAmountEnter()"
    #payedAmountInput
  />
</mat-form-field>

    </div>
    </div>

    <div class="row">
      <div class="col-md-12">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Remarks</mat-label>
          <textarea matInput formControlName="Remarks" rows="2"></textarea>
        </mat-form-field>
      </div>
    </div>
  </form>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">Cancel</button>
  <button mat-raised-button color="primary" (click)="onSave()" (keydown.enter)="onSaveEnter()" [disabled]="paymentForm.invalid" #saveButton tabindex="0">Save</button>
</mat-dialog-actions>