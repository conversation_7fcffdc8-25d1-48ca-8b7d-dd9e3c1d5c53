const WeaverEmployee = require('../model/phase-3/weaver_employee');

const createEmployee = async (employeeData) => {
  const employee = new WeaverEmployee(employeeData);
  return await employee.save();
};

const getEmployees = async () => {
  return await WeaverEmployee.find().populate('branch');
};

const getEmployeeById = async (id) => {
  return await WeaverEmployee.findById(id);
};

const updateEmployee = async (id, updateData) => {
  return await WeaverEmployee.findByIdAndUpdate(id, updateData, { new: true });
};

const deleteEmployee = async (id) => {
  return await WeaverEmployee.findByIdAndDelete(id);
};

module.exports = {
  createEmployee,
  getEmployees,
  getEmployeeById,
  updateEmployee,
  deleteEmployee
};
