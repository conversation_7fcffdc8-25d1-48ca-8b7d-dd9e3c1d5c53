// Script to fix names that start with dash (- Yaseen → Yaseen)
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function fixDashNames() {
  console.log('🔄 FIXING DASH NAMES');
  console.log('='.repeat(60));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get all records
    const allRecords = await collection.find({}).toArray();
    console.log(`📊 Found ${allRecords.length} records to check`);
    
    const results = { success: [], errors: [] };
    
    // Update each record's weaverName to remove leading dash
    for (let i = 0; i < allRecords.length; i++) {
      try {
        const record = allRecords[i];
        let currentWeaverName = record.weaverName || '';
        
        // Remove leading dash and space
        let newWeaverName = currentWeaverName;
        if (currentWeaverName.startsWith('- ')) {
          newWeaverName = currentWeaverName.substring(2); // Remove "- "
        } else if (currentWeaverName.startsWith('-')) {
          newWeaverName = currentWeaverName.substring(1); // Remove "-"
        }
        
        // Only update if there was a change
        if (newWeaverName !== currentWeaverName) {
          await collection.updateOne(
            { _id: record._id },
            { 
              $set: { 
                weaverName: newWeaverName 
              } 
            }
          );
          
          results.success.push({
            receiveNo: record.receiveNo,
            oldWeaver: currentWeaverName,
            newWeaver: newWeaverName
          });
        }
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Checked ${i + 1}/${allRecords.length} records`);
        }
        
      } catch (error) {
        console.error(`❌ Error updating record ${i + 1}:`, error.message);
        results.errors.push({
          receiveNo: allRecords[i]?.receiveNo || `Record ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error fixing dash names:', error);
    return { success: [], errors: [] };
  }
}

async function verifyFinalNames() {
  console.log('\n🔍 Verifying final clean names...');
  
  try {
    const db = mongoose.connection.db;
    
    // Get sample carpet received records
    const samples = await db.collection('carpetreceiveds').find({}).limit(15).toArray();
    
    console.log('\n📋 Sample records with final clean names:');
    samples.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver Name: ${record.weaverName || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ${record.amount || 'N/A'}`);
      console.log('   ' + '-'.repeat(50));
    });
    
    // Check distinct weaver names
    const distinctWeavers = await db.collection('carpetreceiveds').distinct('weaverName');
    console.log(`\n👥 All final clean weaver names (${distinctWeavers.length}):`);
    distinctWeavers.forEach((weaver, index) => {
      console.log(`   ${index + 1}. ${weaver}`);
    });
    
  } catch (error) {
    console.error('❌ Error verifying final names:', error);
  }
}

async function main() {
  console.log('🔄 FIXING DASH NAMES TO CLEAN FORMAT');
  console.log('(- Yaseen → Yaseen, - Jafar Muhammad → Jafar Muhammad)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Fix dash names
    const results = await fixDashNames();

    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 DASH NAME FIX COMPLETE');
    console.log('='.repeat(60));
    console.log(`✅ Records Updated: ${results.success.length}`);
    console.log(`❌ Errors: ${results.errors.length}`);
    
    if (results.success.length > 0) {
      console.log('\n✅ UPDATED RECORDS:');
      results.success.forEach(record => {
        console.log(`  - ${record.receiveNo}: "${record.oldWeaver}" → "${record.newWeaver}"`);
      });
    }
    
    // Verify final names
    await verifyFinalNames();

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 ALL NAMES CLEANED PERFECTLY!');
    console.log('✅ All weaver names now completely clean');
    console.log('✅ Frontend will show: Rahila, Jeet Narayan, Yaseen, Jafar Muhammad, etc.');
    console.log('✅ No prefixes, no dashes - only clean names!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
