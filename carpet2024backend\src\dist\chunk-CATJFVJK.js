import{$ as G,$b as be,A as Me,Aa as Z,Ac as Uc,Ba as gs,Bc as Ct,C as jt,Ca as mc,Cc as Bc,D as Ze,Da as zt,Dc as $c,E as Ut,Ea as gc,Eb as Rc,Ec as z,F as ds,Fa as Ue,Fc as zc,G as Pe,Ga as xi,Gb as Be,Gc as Sn,H as ac,Ha as In,Hc,Ia as Ht,Ib as Pi,Ic as Wc,J as cc,Ja as Wt,Jb as _s,Jc as Rn,<PERSON> as yt,<PERSON> as vc,Kb as Ni,Kc as <PERSON>,<PERSON> as Ye,<PERSON> as yc,<PERSON> as hs,Ma as wc,N as lc,Na as bc,Nb as xc,O as uc,Oa as _c,Ob as kc,Pa as Cc,Pb as Fi,Qa as Ec,Qb as Li,R as Ti,Ra as bt,S as Mi,Sb as Oc,T as we,U as Bt,Ua as Ac,Ub as Pc,Uc as Gc,Vc as qc,W as te,Wa as Dn,Wc as Kc,X as B,Xa as Tn,Xc as Wi,Y as Xe,Ya as C,Yc as Zc,Z as Se,Za as Ic,Zc as Gi,_ as A,a as Ii,aa as dc,ac as Kt,b as ic,ba as T,bb as Dc,c as vt,ca as fs,cb as Gt,d as cs,da as w,e as ls,ea as _,eb as $,f as J,fa as ps,fb as Tc,g as he,ga as hc,gb as Mc,ha as An,hb as ce,ia as F,ib as Je,ic as Vi,j as Ke,ja as $t,jb as Sc,jc as Nc,k as ie,ka as q,kb as vs,kc as ji,l as D,la as V,lb as Mn,lc as Ui,m as Cn,ma as Si,mb as ys,mc as Cs,n as rc,na as fc,nb as _t,nc as Bi,o as sc,oa as wt,ob as ki,oc as Es,pa as Ne,pc as As,q as k,qa as Ie,qb as et,qc as $i,r as Vt,rc as zi,s as ye,sc as Fc,t as En,tb as Oi,tc as Lc,u as Di,uc as tt,v as us,va as Qe,w as oc,wa as pc,wc as Vc,xa as Ri,xb as ws,xc as jc,ya as ms,yb as bs,yc as _e,za as re,zb as qt}from"./chunk-YIUCZFK7.js";import{a as y,b as W,e as Jf}from"./chunk-P2VZOJAX.js";var uw=Jf((Ve,ta)=>{"use strict";(function(n,e){typeof Ve=="object"&&typeof ta<"u"?ta.exports=e():typeof define=="function"&&define.amd?define(e):(n=typeof globalThis<"u"?globalThis:n||self,n.Sweetalert2=e())})(Ve,function(){"use strict";function n(u,o,a){if(typeof u=="function"?u===o:u.has(o))return arguments.length<3?o:a;throw new TypeError("Private element is not present on this object")}function e(u,o,a){return o=ee(o),P(u,s()?Reflect.construct(o,a||[],ee(u).constructor):o.apply(u,a))}function r(u,o){return u.get(n(u,o))}function t(u,o,a){return u.set(n(u,o),a),a}function i(u,o,a){if(s())return Reflect.construct.apply(null,arguments);var l=[null];l.push.apply(l,o);var h=new(u.bind.apply(u,l));return a&&Y(h,a.prototype),h}function s(){try{var u=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(s=function(){return!!u})()}function c(u,o){var a=u==null?null:typeof Symbol<"u"&&u[Symbol.iterator]||u["@@iterator"];if(a!=null){var l,h,g,b,R=[],x=!0,K=!1;try{if(g=(a=a.call(u)).next,o===0){if(Object(a)!==a)return;x=!1}else for(;!(x=(l=g.call(a)).done)&&(R.push(l.value),R.length!==o);x=!0);}catch(_n){K=!0,h=_n}finally{try{if(!x&&a.return!=null&&(b=a.return(),Object(b)!==b))return}finally{if(K)throw h}}return R}}function d(u,o){if(typeof u!="object"||!u)return u;var a=u[Symbol.toPrimitive];if(a!==void 0){var l=a.call(u,o||"default");if(typeof l!="object")return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return(o==="string"?String:Number)(u)}function p(u){var o=d(u,"string");return typeof o=="symbol"?o:String(o)}function f(u){"@babel/helpers - typeof";return f=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(o){return typeof o}:function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},f(u)}function v(u,o){if(!(u instanceof o))throw new TypeError("Cannot call a class as a function")}function I(u,o){for(var a=0;a<o.length;a++){var l=o[a];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(u,p(l.key),l)}}function E(u,o,a){return o&&I(u.prototype,o),a&&I(u,a),Object.defineProperty(u,"prototype",{writable:!1}),u}function O(u,o){if(typeof o!="function"&&o!==null)throw new TypeError("Super expression must either be null or a function");u.prototype=Object.create(o&&o.prototype,{constructor:{value:u,writable:!0,configurable:!0}}),Object.defineProperty(u,"prototype",{writable:!1}),o&&Y(u,o)}function ee(u){return ee=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)},ee(u)}function Y(u,o){return Y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(l,h){return l.__proto__=h,l},Y(u,o)}function L(u){if(u===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u}function P(u,o){if(o&&(typeof o=="object"||typeof o=="function"))return o;if(o!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return L(u)}function pe(u,o){for(;!Object.prototype.hasOwnProperty.call(u,o)&&(u=ee(u),u!==null););return u}function ut(){return typeof Reflect<"u"&&Reflect.get?ut=Reflect.get.bind():ut=function(o,a,l){var h=pe(o,a);if(h){var g=Object.getOwnPropertyDescriptor(h,a);return g.get?g.get.call(arguments.length<3?o:l):g.value}},ut.apply(this,arguments)}function oe(u,o){return $r(u)||c(u,o)||na(u,o)||pd()}function fn(u){return Br(u)||hd(u)||na(u)||fd()}function Br(u){if(Array.isArray(u))return zr(u)}function $r(u){if(Array.isArray(u))return u}function hd(u){if(typeof Symbol<"u"&&u[Symbol.iterator]!=null||u["@@iterator"]!=null)return Array.from(u)}function na(u,o){if(u){if(typeof u=="string")return zr(u,o);var a=Object.prototype.toString.call(u).slice(8,-1);if(a==="Object"&&u.constructor&&(a=u.constructor.name),a==="Map"||a==="Set")return Array.from(u);if(a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return zr(u,o)}}function zr(u,o){(o==null||o>u.length)&&(o=u.length);for(var a=0,l=new Array(o);a<o;a++)l[a]=u[a];return l}function fd(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pd(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function md(u,o){if(o.has(u))throw new TypeError("Cannot initialize the same private elements twice on an object")}function gd(u,o,a){md(u,o),o.set(u,a)}var vd=100,S={},yd=function(){S.previousActiveElement instanceof HTMLElement?(S.previousActiveElement.focus(),S.previousActiveElement=null):document.body&&document.body.focus()},wd=function(o){return new Promise(function(a){if(!o)return a();var l=window.scrollX,h=window.scrollY;S.restoreFocusTimeout=setTimeout(function(){yd(),a()},vd),window.scrollTo(l,h)})},ia="swal2-",bd=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","default-outline","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"],m=bd.reduce(function(u,o){return u[o]=ia+o,u},{}),_d=["success","warning","info","question","error"],fi=_d.reduce(function(u,o){return u[o]=ia+o,u},{}),ra="SweetAlert2:",Hr=function(o){return o.charAt(0).toUpperCase()+o.slice(1)},ue=function(o){console.warn("".concat(ra," ").concat(f(o)==="object"?o.join(" "):o))},dt=function(o){console.error("".concat(ra," ").concat(o))},sa=[],Cd=function(o){sa.includes(o)||(sa.push(o),ue(o))},Ed=function(o,a){Cd('"'.concat(o,'" is deprecated and will be removed in the next major release. Please use "').concat(a,'" instead.'))},pi=function(o){return typeof o=="function"?o():o},Wr=function(o){return o&&typeof o.toPromise=="function"},pn=function(o){return Wr(o)?o.toPromise():Promise.resolve(o)},Gr=function(o){return o&&Promise.resolve(o)===o},de=function(){return document.body.querySelector(".".concat(m.container))},mn=function(o){var a=de();return a?a.querySelector(o):null},me=function(o){return mn(".".concat(o))},U=function(){return me(m.popup)},gn=function(){return me(m.icon)},Ad=function(){return me(m["icon-content"])},oa=function(){return me(m.title)},qr=function(){return me(m["html-container"])},aa=function(){return me(m.image)},Kr=function(){return me(m["progress-steps"])},mi=function(){return me(m["validation-message"])},xe=function(){return mn(".".concat(m.actions," .").concat(m.confirm))},xt=function(){return mn(".".concat(m.actions," .").concat(m.cancel))},ht=function(){return mn(".".concat(m.actions," .").concat(m.deny))},Id=function(){return me(m["input-label"])},kt=function(){return mn(".".concat(m.loader))},vn=function(){return me(m.actions)},ca=function(){return me(m.footer)},gi=function(){return me(m["timer-progress-bar"])},Zr=function(){return me(m.close)},Dd=`
  a[href],
  area[href],
  input:not([disabled]),
  select:not([disabled]),
  textarea:not([disabled]),
  button:not([disabled]),
  iframe,
  object,
  embed,
  [tabindex="0"],
  [contenteditable],
  audio[controls],
  video[controls],
  summary
`,Yr=function(){var o=U();if(!o)return[];var a=o.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),l=Array.from(a).sort(function(b,R){var x=parseInt(b.getAttribute("tabindex")||"0"),K=parseInt(R.getAttribute("tabindex")||"0");return x>K?1:x<K?-1:0}),h=o.querySelectorAll(Dd),g=Array.from(h).filter(function(b){return b.getAttribute("tabindex")!=="-1"});return fn(new Set(l.concat(g))).filter(function(b){return ve(b)})},Xr=function(){return je(document.body,m.shown)&&!je(document.body,m["toast-shown"])&&!je(document.body,m["no-backdrop"])},vi=function(){var o=U();return o?je(o,m.toast):!1},Td=function(){var o=U();return o?o.hasAttribute("data-loading"):!1},ge=function(o,a){if(o.textContent="",a){var l=new DOMParser,h=l.parseFromString(a,"text/html"),g=h.querySelector("head");g&&Array.from(g.childNodes).forEach(function(R){o.appendChild(R)});var b=h.querySelector("body");b&&Array.from(b.childNodes).forEach(function(R){R instanceof HTMLVideoElement||R instanceof HTMLAudioElement?o.appendChild(R.cloneNode(!0)):o.appendChild(R)})}},je=function(o,a){if(!a)return!1;for(var l=a.split(/\s+/),h=0;h<l.length;h++)if(!o.classList.contains(l[h]))return!1;return!0},Md=function(o,a){Array.from(o.classList).forEach(function(l){!Object.values(m).includes(l)&&!Object.values(fi).includes(l)&&!Object.values(a.showClass||{}).includes(l)&&o.classList.remove(l)})},Ae=function(o,a,l){if(Md(o,a),a.customClass&&a.customClass[l]){if(typeof a.customClass[l]!="string"&&!a.customClass[l].forEach){ue("Invalid type of customClass.".concat(l,'! Expected string or iterable object, got "').concat(f(a.customClass[l]),'"'));return}N(o,a.customClass[l])}},yi=function(o,a){if(!a)return null;switch(a){case"select":case"textarea":case"file":return o.querySelector(".".concat(m.popup," > .").concat(m[a]));case"checkbox":return o.querySelector(".".concat(m.popup," > .").concat(m.checkbox," input"));case"radio":return o.querySelector(".".concat(m.popup," > .").concat(m.radio," input:checked"))||o.querySelector(".".concat(m.popup," > .").concat(m.radio," input:first-child"));case"range":return o.querySelector(".".concat(m.popup," > .").concat(m.range," input"));default:return o.querySelector(".".concat(m.popup," > .").concat(m.input))}},la=function(o){if(o.focus(),o.type!=="file"){var a=o.value;o.value="",o.value=a}},ua=function(o,a,l){!o||!a||(typeof a=="string"&&(a=a.split(/\s+/).filter(Boolean)),a.forEach(function(h){Array.isArray(o)?o.forEach(function(g){l?g.classList.add(h):g.classList.remove(h)}):l?o.classList.add(h):o.classList.remove(h)}))},N=function(o,a){ua(o,a,!0)},ke=function(o,a){ua(o,a,!1)},Ge=function(o,a){for(var l=Array.from(o.children),h=0;h<l.length;h++){var g=l[h];if(g instanceof HTMLElement&&je(g,a))return g}},ft=function(o,a,l){l==="".concat(parseInt(l))&&(l=parseInt(l)),l||parseInt(l)===0?o.style.setProperty(a,typeof l=="number"?"".concat(l,"px"):l):o.style.removeProperty(a)},ne=function(o){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"flex";o&&(o.style.display=a)},ae=function(o){o&&(o.style.display="none")},Qr=function(o){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"block";o&&new MutationObserver(function(){yn(o,o.innerHTML,a)}).observe(o,{childList:!0,subtree:!0})},da=function(o,a,l,h){var g=o.querySelector(a);g&&g.style.setProperty(l,h)},yn=function(o,a){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"flex";a?ne(o,l):ae(o)},ve=function(o){return!!(o&&(o.offsetWidth||o.offsetHeight||o.getClientRects().length))},Sd=function(){return!ve(xe())&&!ve(ht())&&!ve(xt())},ha=function(o){return o.scrollHeight>o.clientHeight},fa=function(o){var a=window.getComputedStyle(o),l=parseFloat(a.getPropertyValue("animation-duration")||"0"),h=parseFloat(a.getPropertyValue("transition-duration")||"0");return l>0||h>0},Jr=function(o){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l=gi();l&&ve(l)&&(a&&(l.style.transition="none",l.style.width="100%"),setTimeout(function(){l.style.transition="width ".concat(o/1e3,"s linear"),l.style.width="0%"},10))},Rd=function(){var o=gi();if(o){var a=parseInt(window.getComputedStyle(o).width);o.style.removeProperty("transition"),o.style.width="100%";var l=parseInt(window.getComputedStyle(o).width),h=a/l*100;o.style.width="".concat(h,"%")}},pa=function(){return typeof window>"u"||typeof document>"u"},xd=`
 <div aria-labelledby="`.concat(m.title,'" aria-describedby="').concat(m["html-container"],'" class="').concat(m.popup,`" tabindex="-1">
   <button type="button" class="`).concat(m.close,`"></button>
   <ul class="`).concat(m["progress-steps"],`"></ul>
   <div class="`).concat(m.icon,`"></div>
   <img class="`).concat(m.image,`" />
   <h2 class="`).concat(m.title,'" id="').concat(m.title,`"></h2>
   <div class="`).concat(m["html-container"],'" id="').concat(m["html-container"],`"></div>
   <input class="`).concat(m.input,'" id="').concat(m.input,`" />
   <input type="file" class="`).concat(m.file,`" />
   <div class="`).concat(m.range,`">
     <input type="range" />
     <output></output>
   </div>
   <select class="`).concat(m.select,'" id="').concat(m.select,`"></select>
   <div class="`).concat(m.radio,`"></div>
   <label class="`).concat(m.checkbox,`">
     <input type="checkbox" id="`).concat(m.checkbox,`" />
     <span class="`).concat(m.label,`"></span>
   </label>
   <textarea class="`).concat(m.textarea,'" id="').concat(m.textarea,`"></textarea>
   <div class="`).concat(m["validation-message"],'" id="').concat(m["validation-message"],`"></div>
   <div class="`).concat(m.actions,`">
     <div class="`).concat(m.loader,`"></div>
     <button type="button" class="`).concat(m.confirm,`"></button>
     <button type="button" class="`).concat(m.deny,`"></button>
     <button type="button" class="`).concat(m.cancel,`"></button>
   </div>
   <div class="`).concat(m.footer,`"></div>
   <div class="`).concat(m["timer-progress-bar-container"],`">
     <div class="`).concat(m["timer-progress-bar"],`"></div>
   </div>
 </div>
`).replace(/(^|\n)\s*/g,""),kd=function(){var o=de();return o?(o.remove(),ke([document.documentElement,document.body],[m["no-backdrop"],m["toast-shown"],m["has-column"]]),!0):!1},pt=function(){S.currentInstance.resetValidationMessage()},Od=function(){var o=U(),a=Ge(o,m.input),l=Ge(o,m.file),h=o.querySelector(".".concat(m.range," input")),g=o.querySelector(".".concat(m.range," output")),b=Ge(o,m.select),R=o.querySelector(".".concat(m.checkbox," input")),x=Ge(o,m.textarea);a.oninput=pt,l.onchange=pt,b.onchange=pt,R.onchange=pt,x.oninput=pt,h.oninput=function(){pt(),g.value=h.value},h.onchange=function(){pt(),g.value=h.value}},Pd=function(o){return typeof o=="string"?document.querySelector(o):o},Nd=function(o){var a=U();a.setAttribute("role",o.toast?"alert":"dialog"),a.setAttribute("aria-live",o.toast?"polite":"assertive"),o.toast||a.setAttribute("aria-modal","true")},Fd=function(o){window.getComputedStyle(o).direction==="rtl"&&N(de(),m.rtl)},Ld=function(o){var a=kd();if(pa()){dt("SweetAlert2 requires document to initialize");return}var l=document.createElement("div");l.className=m.container,a&&N(l,m["no-transition"]),ge(l,xd);var h=Pd(o.target);h.appendChild(l),Nd(o),Fd(h),Od()},es=function(o,a){o instanceof HTMLElement?a.appendChild(o):f(o)==="object"?Vd(o,a):o&&ge(a,o)},Vd=function(o,a){o.jquery?jd(a,o):ge(a,o.toString())},jd=function(o,a){if(o.textContent="",0 in a)for(var l=0;l in a;l++)o.appendChild(a[l].cloneNode(!0));else o.appendChild(a.cloneNode(!0))},mt=function(){if(pa())return!1;var u=document.createElement("div");return typeof u.style.webkitAnimation<"u"?"webkitAnimationEnd":typeof u.style.animation<"u"?"animationend":!1}(),Ud=function(o,a){var l=vn(),h=kt();!l||!h||(!a.showConfirmButton&&!a.showDenyButton&&!a.showCancelButton?ae(l):ne(l),Ae(l,a,"actions"),Bd(l,h,a),ge(h,a.loaderHtml||""),Ae(h,a,"loader"))};function Bd(u,o,a){var l=xe(),h=ht(),g=xt();!l||!h||!g||(ts(l,"confirm",a),ts(h,"deny",a),ts(g,"cancel",a),$d(l,h,g,a),a.reverseButtons&&(a.toast?(u.insertBefore(g,l),u.insertBefore(h,l)):(u.insertBefore(g,o),u.insertBefore(h,o),u.insertBefore(l,o))))}function $d(u,o,a,l){if(!l.buttonsStyling){ke([u,o,a],m.styled);return}N([u,o,a],m.styled),l.confirmButtonColor&&(u.style.backgroundColor=l.confirmButtonColor,N(u,m["default-outline"])),l.denyButtonColor&&(o.style.backgroundColor=l.denyButtonColor,N(o,m["default-outline"])),l.cancelButtonColor&&(a.style.backgroundColor=l.cancelButtonColor,N(a,m["default-outline"]))}function ts(u,o,a){var l=Hr(o);yn(u,a["show".concat(l,"Button")],"inline-block"),ge(u,a["".concat(o,"ButtonText")]||""),u.setAttribute("aria-label",a["".concat(o,"ButtonAriaLabel")]||""),u.className=m[o],Ae(u,a,"".concat(o,"Button"))}var zd=function(o,a){var l=Zr();l&&(ge(l,a.closeButtonHtml||""),Ae(l,a,"closeButton"),yn(l,a.showCloseButton),l.setAttribute("aria-label",a.closeButtonAriaLabel||""))},Hd=function(o,a){var l=de();l&&(Wd(l,a.backdrop),Gd(l,a.position),qd(l,a.grow),Ae(l,a,"container"))};function Wd(u,o){typeof o=="string"?u.style.background=o:o||N([document.documentElement,document.body],m["no-backdrop"])}function Gd(u,o){o&&(o in m?N(u,m[o]):(ue('The "position" parameter is not valid, defaulting to "center"'),N(u,m.center)))}function qd(u,o){o&&N(u,m["grow-".concat(o)])}var H={innerParams:new WeakMap,domCache:new WeakMap},Kd=["input","file","range","select","radio","checkbox","textarea"],Zd=function(o,a){var l=U();if(l){var h=H.innerParams.get(o),g=!h||a.input!==h.input;Kd.forEach(function(b){var R=Ge(l,m[b]);R&&(Qd(b,a.inputAttributes),R.className=m[b],g&&ae(R))}),a.input&&(g&&Yd(a),Jd(a))}},Yd=function(o){if(o.input){if(!X[o.input]){dt("Unexpected type of input! Expected ".concat(Object.keys(X).join(" | "),', got "').concat(o.input,'"'));return}var a=ma(o.input),l=X[o.input](a,o);ne(a),o.inputAutoFocus&&setTimeout(function(){la(l)})}},Xd=function(o){for(var a=0;a<o.attributes.length;a++){var l=o.attributes[a].name;["id","type","value","style"].includes(l)||o.removeAttribute(l)}},Qd=function(o,a){var l=yi(U(),o);if(l){Xd(l);for(var h in a)l.setAttribute(h,a[h])}},Jd=function(o){var a=ma(o.input);f(o.customClass)==="object"&&N(a,o.customClass.input)},ns=function(o,a){(!o.placeholder||a.inputPlaceholder)&&(o.placeholder=a.inputPlaceholder)},wn=function(o,a,l){if(l.inputLabel){var h=document.createElement("label"),g=m["input-label"];h.setAttribute("for",o.id),h.className=g,f(l.customClass)==="object"&&N(h,l.customClass.inputLabel),h.innerText=l.inputLabel,a.insertAdjacentElement("beforebegin",h)}},ma=function(o){return Ge(U(),m[o]||m.input)},wi=function(o,a){["string","number"].includes(f(a))?o.value="".concat(a):Gr(a)||ue('Unexpected type of inputValue! Expected "string", "number" or "Promise", got "'.concat(f(a),'"'))},X={};X.text=X.email=X.password=X.number=X.tel=X.url=X.search=X.date=X["datetime-local"]=X.time=X.week=X.month=function(u,o){return wi(u,o.inputValue),wn(u,u,o),ns(u,o),u.type=o.input,u},X.file=function(u,o){return wn(u,u,o),ns(u,o),u},X.range=function(u,o){var a=u.querySelector("input"),l=u.querySelector("output");return wi(a,o.inputValue),a.type=o.input,wi(l,o.inputValue),wn(a,u,o),u},X.select=function(u,o){if(u.textContent="",o.inputPlaceholder){var a=document.createElement("option");ge(a,o.inputPlaceholder),a.value="",a.disabled=!0,a.selected=!0,u.appendChild(a)}return wn(u,u,o),u},X.radio=function(u){return u.textContent="",u},X.checkbox=function(u,o){var a=yi(U(),"checkbox");a.value="1",a.checked=!!o.inputValue;var l=u.querySelector("span");return ge(l,o.inputPlaceholder),a},X.textarea=function(u,o){wi(u,o.inputValue),ns(u,o),wn(u,u,o);var a=function(h){return parseInt(window.getComputedStyle(h).marginLeft)+parseInt(window.getComputedStyle(h).marginRight)};return setTimeout(function(){if("MutationObserver"in window){var l=parseInt(window.getComputedStyle(U()).width),h=function(){if(document.body.contains(u)){var b=u.offsetWidth+a(u);b>l?U().style.width="".concat(b,"px"):ft(U(),"width",o.width)}};new MutationObserver(h).observe(u,{attributes:!0,attributeFilter:["style"]})}}),u};var eh=function(o,a){var l=qr();l&&(Qr(l),Ae(l,a,"htmlContainer"),a.html?(es(a.html,l),ne(l,"block")):a.text?(l.textContent=a.text,ne(l,"block")):ae(l),Zd(o,a))},th=function(o,a){var l=ca();l&&(Qr(l),yn(l,a.footer,"block"),a.footer&&es(a.footer,l),Ae(l,a,"footer"))},nh=function(o,a){var l=H.innerParams.get(o),h=gn();if(h){if(l&&a.icon===l.icon){va(h,a),ga(h,a);return}if(!a.icon&&!a.iconHtml){ae(h);return}if(a.icon&&Object.keys(fi).indexOf(a.icon)===-1){dt('Unknown icon! Expected "success", "error", "warning", "info" or "question", got "'.concat(a.icon,'"')),ae(h);return}ne(h),va(h,a),ga(h,a),N(h,a.showClass&&a.showClass.icon)}},ga=function(o,a){for(var l=0,h=Object.entries(fi);l<h.length;l++){var g=oe(h[l],2),b=g[0],R=g[1];a.icon!==b&&ke(o,R)}N(o,a.icon&&fi[a.icon]),oh(o,a),ih(),Ae(o,a,"icon")},ih=function(){var o=U();if(o)for(var a=window.getComputedStyle(o).getPropertyValue("background-color"),l=o.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix"),h=0;h<l.length;h++)l[h].style.backgroundColor=a},rh=`
  <div class="swal2-success-circular-line-left"></div>
  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>
  <div class="swal2-success-circular-line-right"></div>
`,sh=`
  <span class="swal2-x-mark">
    <span class="swal2-x-mark-line-left"></span>
    <span class="swal2-x-mark-line-right"></span>
  </span>
`,va=function(o,a){if(!(!a.icon&&!a.iconHtml)){var l=o.innerHTML,h="";if(a.iconHtml)h=ya(a.iconHtml);else if(a.icon==="success")h=rh,l=l.replace(/ style=".*?"/g,"");else if(a.icon==="error")h=sh;else if(a.icon){var g={question:"?",warning:"!",info:"i"};h=ya(g[a.icon])}l.trim()!==h.trim()&&ge(o,h)}},oh=function(o,a){if(a.iconColor){o.style.color=a.iconColor,o.style.borderColor=a.iconColor;for(var l=0,h=[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"];l<h.length;l++){var g=h[l];da(o,g,"background-color",a.iconColor)}da(o,".swal2-success-ring","border-color",a.iconColor)}},ya=function(o){return'<div class="'.concat(m["icon-content"],'">').concat(o,"</div>")},ah=function(o,a){var l=aa();if(l){if(!a.imageUrl){ae(l);return}ne(l,""),l.setAttribute("src",a.imageUrl),l.setAttribute("alt",a.imageAlt||""),ft(l,"width",a.imageWidth),ft(l,"height",a.imageHeight),l.className=m.image,Ae(l,a,"image")}},ch=function(o,a){var l=de(),h=U();if(!(!l||!h)){if(a.toast){ft(l,"width",a.width),h.style.width="100%";var g=kt();g&&h.insertBefore(g,gn())}else ft(h,"width",a.width);ft(h,"padding",a.padding),a.color&&(h.style.color=a.color),a.background&&(h.style.background=a.background),ae(mi()),lh(h,a)}},lh=function(o,a){var l=a.showClass||{};o.className="".concat(m.popup," ").concat(ve(o)?l.popup:""),a.toast?(N([document.documentElement,document.body],m["toast-shown"]),N(o,m.toast)):N(o,m.modal),Ae(o,a,"popup"),typeof a.customClass=="string"&&N(o,a.customClass),a.icon&&N(o,m["icon-".concat(a.icon)])},uh=function(o,a){var l=Kr();if(l){var h=a.progressSteps,g=a.currentProgressStep;if(!h||h.length===0||g===void 0){ae(l);return}ne(l),l.textContent="",g>=h.length&&ue("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),h.forEach(function(b,R){var x=dh(b);if(l.appendChild(x),R===g&&N(x,m["active-progress-step"]),R!==h.length-1){var K=hh(a);l.appendChild(K)}})}},dh=function(o){var a=document.createElement("li");return N(a,m["progress-step"]),ge(a,o),a},hh=function(o){var a=document.createElement("li");return N(a,m["progress-step-line"]),o.progressStepsDistance&&ft(a,"width",o.progressStepsDistance),a},fh=function(o,a){var l=oa();l&&(Qr(l),yn(l,a.title||a.titleText,"block"),a.title&&es(a.title,l),a.titleText&&(l.innerText=a.titleText),Ae(l,a,"title"))},wa=function(o,a){ch(o,a),Hd(o,a),uh(o,a),nh(o,a),ah(o,a),fh(o,a),zd(o,a),eh(o,a),Ud(o,a),th(o,a);var l=U();typeof a.didRender=="function"&&l&&a.didRender(l)},ph=function(){return ve(U())},ba=function(){var o;return(o=xe())===null||o===void 0?void 0:o.click()},mh=function(){var o;return(o=ht())===null||o===void 0?void 0:o.click()},gh=function(){var o;return(o=xt())===null||o===void 0?void 0:o.click()},Ot=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),_a=function(o){o.keydownTarget&&o.keydownHandlerAdded&&(o.keydownTarget.removeEventListener("keydown",o.keydownHandler,{capture:o.keydownListenerCapture}),o.keydownHandlerAdded=!1)},vh=function(o,a,l){_a(o),a.toast||(o.keydownHandler=function(h){return wh(a,h,l)},o.keydownTarget=a.keydownListenerCapture?window:U(),o.keydownListenerCapture=a.keydownListenerCapture,o.keydownTarget.addEventListener("keydown",o.keydownHandler,{capture:o.keydownListenerCapture}),o.keydownHandlerAdded=!0)},is=function(o,a){var l,h=Yr();if(h.length){o=o+a,o===h.length?o=0:o===-1&&(o=h.length-1),h[o].focus();return}(l=U())===null||l===void 0||l.focus()},Ca=["ArrowRight","ArrowDown"],yh=["ArrowLeft","ArrowUp"],wh=function(o,a,l){o&&(a.isComposing||a.keyCode===229||(o.stopKeydownPropagation&&a.stopPropagation(),a.key==="Enter"?bh(a,o):a.key==="Tab"?_h(a):[].concat(Ca,yh).includes(a.key)?Ch(a.key):a.key==="Escape"&&Eh(a,o,l)))},bh=function(o,a){if(pi(a.allowEnterKey)){var l=yi(U(),a.input);if(o.target&&l&&o.target instanceof HTMLElement&&o.target.outerHTML===l.outerHTML){if(["textarea","file"].includes(a.input))return;ba(),o.preventDefault()}}},_h=function(o){for(var a=o.target,l=Yr(),h=-1,g=0;g<l.length;g++)if(a===l[g]){h=g;break}o.shiftKey?is(h,-1):is(h,1),o.stopPropagation(),o.preventDefault()},Ch=function(o){var a=vn(),l=xe(),h=ht(),g=xt();if(!(!a||!l||!h||!g)){var b=[l,h,g];if(!(document.activeElement instanceof HTMLElement&&!b.includes(document.activeElement))){var R=Ca.includes(o)?"nextElementSibling":"previousElementSibling",x=document.activeElement;if(x){for(var K=0;K<a.children.length;K++){if(x=x[R],!x)return;if(x instanceof HTMLButtonElement&&ve(x))break}x instanceof HTMLButtonElement&&x.focus()}}}},Eh=function(o,a,l){pi(a.allowEscapeKey)&&(o.preventDefault(),l(Ot.esc))},Pt={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap},Ah=function(){var o=de(),a=Array.from(document.body.children);a.forEach(function(l){l.contains(o)||(l.hasAttribute("aria-hidden")&&l.setAttribute("data-previous-aria-hidden",l.getAttribute("aria-hidden")||""),l.setAttribute("aria-hidden","true"))})},Ea=function(){var o=Array.from(document.body.children);o.forEach(function(a){a.hasAttribute("data-previous-aria-hidden")?(a.setAttribute("aria-hidden",a.getAttribute("data-previous-aria-hidden")||""),a.removeAttribute("data-previous-aria-hidden")):a.removeAttribute("aria-hidden")})},Aa=typeof window<"u"&&!!window.GestureEvent,Ih=function(){if(Aa&&!je(document.body,m.iosfix)){var o=document.body.scrollTop;document.body.style.top="".concat(o*-1,"px"),N(document.body,m.iosfix),Dh()}},Dh=function(){var o=de();if(o){var a;o.ontouchstart=function(l){a=Th(l)},o.ontouchmove=function(l){a&&(l.preventDefault(),l.stopPropagation())}}},Th=function(o){var a=o.target,l=de(),h=qr();return!l||!h||Mh(o)||Sh(o)?!1:a===l||!ha(l)&&a instanceof HTMLElement&&a.tagName!=="INPUT"&&a.tagName!=="TEXTAREA"&&!(ha(h)&&h.contains(a))},Mh=function(o){return o.touches&&o.touches.length&&o.touches[0].touchType==="stylus"},Sh=function(o){return o.touches&&o.touches.length>1},Rh=function(){if(je(document.body,m.iosfix)){var o=parseInt(document.body.style.top,10);ke(document.body,m.iosfix),document.body.style.top="",document.body.scrollTop=o*-1}},xh=function(){var o=document.createElement("div");o.className=m["scrollbar-measure"],document.body.appendChild(o);var a=o.getBoundingClientRect().width-o.clientWidth;return document.body.removeChild(o),a},Nt=null,kh=function(o){Nt===null&&(document.body.scrollHeight>window.innerHeight||o==="scroll")&&(Nt=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight="".concat(Nt+xh(),"px"))},Oh=function(){Nt!==null&&(document.body.style.paddingRight="".concat(Nt,"px"),Nt=null)};function Ia(u,o,a,l){vi()?Ta(u,l):(wd(a).then(function(){return Ta(u,l)}),_a(S)),Aa?(o.setAttribute("style","display:none !important"),o.removeAttribute("class"),o.innerHTML=""):o.remove(),Xr()&&(Oh(),Rh(),Ea()),Ph()}function Ph(){ke([document.documentElement,document.body],[m.shown,m["height-auto"],m["no-backdrop"],m["toast-shown"]])}function qe(u){u=Fh(u);var o=Pt.swalPromiseResolve.get(this),a=Nh(this);this.isAwaitingPromise?u.isDismissed||(bn(this),o(u)):a&&o(u)}var Nh=function(o){var a=U();if(!a)return!1;var l=H.innerParams.get(o);if(!l||je(a,l.hideClass.popup))return!1;ke(a,l.showClass.popup),N(a,l.hideClass.popup);var h=de();return ke(h,l.showClass.backdrop),N(h,l.hideClass.backdrop),Lh(o,a,l),!0};function Da(u){var o=Pt.swalPromiseReject.get(this);bn(this),o&&o(u)}var bn=function(o){o.isAwaitingPromise&&(delete o.isAwaitingPromise,H.innerParams.get(o)||o._destroy())},Fh=function(o){return typeof o>"u"?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},o)},Lh=function(o,a,l){var h=de(),g=mt&&fa(a);typeof l.willClose=="function"&&l.willClose(a),g?Vh(o,a,h,l.returnFocus,l.didClose):Ia(o,h,l.returnFocus,l.didClose)},Vh=function(o,a,l,h,g){mt&&(S.swalCloseEventFinishedCallback=Ia.bind(null,o,l,h,g),a.addEventListener(mt,function(b){b.target===a&&(S.swalCloseEventFinishedCallback(),delete S.swalCloseEventFinishedCallback)}))},Ta=function(o,a){setTimeout(function(){typeof a=="function"&&a.bind(o.params)(),o._destroy&&o._destroy()})},Ft=function(o){var a=U();if(a||new Ai,a=U(),!!a){var l=kt();vi()?ae(gn()):jh(a,o),ne(l),a.setAttribute("data-loading","true"),a.setAttribute("aria-busy","true"),a.focus()}},jh=function(o,a){var l=vn(),h=kt();!l||!h||(!a&&ve(xe())&&(a=xe()),ne(l),a&&(ae(a),h.setAttribute("data-button-to-replace",a.className),l.insertBefore(h,a)),N([o,l],m.loading))},Uh=function(o,a){a.input==="select"||a.input==="radio"?Wh(o,a):["text","email","number","tel","textarea"].some(function(l){return l===a.input})&&(Wr(a.inputValue)||Gr(a.inputValue))&&(Ft(xe()),Gh(o,a))},Bh=function(o,a){var l=o.getInput();if(!l)return null;switch(a.input){case"checkbox":return $h(l);case"radio":return zh(l);case"file":return Hh(l);default:return a.inputAutoTrim?l.value.trim():l.value}},$h=function(o){return o.checked?1:0},zh=function(o){return o.checked?o.value:null},Hh=function(o){return o.files&&o.files.length?o.getAttribute("multiple")!==null?o.files:o.files[0]:null},Wh=function(o,a){var l=U();if(l){var h=function(b){a.input==="select"?qh(l,Ma(b),a):a.input==="radio"&&Kh(l,Ma(b),a)};Wr(a.inputOptions)||Gr(a.inputOptions)?(Ft(xe()),pn(a.inputOptions).then(function(g){o.hideLoading(),h(g)})):f(a.inputOptions)==="object"?h(a.inputOptions):dt("Unexpected type of inputOptions! Expected object, Map or Promise, got ".concat(f(a.inputOptions)))}},Gh=function(o,a){var l=o.getInput();l&&(ae(l),pn(a.inputValue).then(function(h){l.value=a.input==="number"?"".concat(parseFloat(h)||0):"".concat(h),ne(l),l.focus(),o.hideLoading()}).catch(function(h){dt("Error in inputValue promise: ".concat(h)),l.value="",ne(l),l.focus(),o.hideLoading()}))};function qh(u,o,a){var l=Ge(u,m.select);if(l){var h=function(b,R,x){var K=document.createElement("option");K.value=x,ge(K,R),K.selected=Sa(x,a.inputValue),b.appendChild(K)};o.forEach(function(g){var b=g[0],R=g[1];if(Array.isArray(R)){var x=document.createElement("optgroup");x.label=b,x.disabled=!1,l.appendChild(x),R.forEach(function(K){return h(x,K[1],K[0])})}else h(l,R,b)}),l.focus()}}function Kh(u,o,a){var l=Ge(u,m.radio);if(l){o.forEach(function(g){var b=g[0],R=g[1],x=document.createElement("input"),K=document.createElement("label");x.type="radio",x.name=m.radio,x.value=b,Sa(b,a.inputValue)&&(x.checked=!0);var _n=document.createElement("span");ge(_n,R),_n.className=m.label,K.appendChild(x),K.appendChild(_n),l.appendChild(K)});var h=l.querySelectorAll("input");h.length&&h[0].focus()}}var Ma=function u(o){var a=[];return o instanceof Map?o.forEach(function(l,h){var g=l;f(g)==="object"&&(g=u(g)),a.push([h,g])}):Object.keys(o).forEach(function(l){var h=o[l];f(h)==="object"&&(h=u(h)),a.push([l,h])}),a},Sa=function(o,a){return!!a&&a.toString()===o.toString()},bi=void 0,Zh=function(o){var a=H.innerParams.get(o);o.disableButtons(),a.input?Ra(o,"confirm"):ss(o,!0)},Yh=function(o){var a=H.innerParams.get(o);o.disableButtons(),a.returnInputValueOnDeny?Ra(o,"deny"):rs(o,!1)},Xh=function(o,a){o.disableButtons(),a(Ot.cancel)},Ra=function(o,a){var l=H.innerParams.get(o);if(!l.input){dt('The "input" parameter is needed to be set when using returnInputValueOn'.concat(Hr(a)));return}var h=o.getInput(),g=Bh(o,l);l.inputValidator?Qh(o,g,a):h&&!h.checkValidity()?(o.enableButtons(),o.showValidationMessage(l.validationMessage||h.validationMessage)):a==="deny"?rs(o,g):ss(o,g)},Qh=function(o,a,l){var h=H.innerParams.get(o);o.disableInput();var g=Promise.resolve().then(function(){return pn(h.inputValidator(a,h.validationMessage))});g.then(function(b){o.enableButtons(),o.enableInput(),b?o.showValidationMessage(b):l==="deny"?rs(o,a):ss(o,a)})},rs=function(o,a){var l=H.innerParams.get(o||bi);if(l.showLoaderOnDeny&&Ft(ht()),l.preDeny){o.isAwaitingPromise=!0;var h=Promise.resolve().then(function(){return pn(l.preDeny(a,l.validationMessage))});h.then(function(g){g===!1?(o.hideLoading(),bn(o)):o.close({isDenied:!0,value:typeof g>"u"?a:g})}).catch(function(g){return ka(o||bi,g)})}else o.close({isDenied:!0,value:a})},xa=function(o,a){o.close({isConfirmed:!0,value:a})},ka=function(o,a){o.rejectPromise(a)},ss=function(o,a){var l=H.innerParams.get(o||bi);if(l.showLoaderOnConfirm&&Ft(),l.preConfirm){o.resetValidationMessage(),o.isAwaitingPromise=!0;var h=Promise.resolve().then(function(){return pn(l.preConfirm(a,l.validationMessage))});h.then(function(g){ve(mi())||g===!1?(o.hideLoading(),bn(o)):xa(o,typeof g>"u"?a:g)}).catch(function(g){return ka(o||bi,g)})}else xa(o,a)};function _i(){var u=H.innerParams.get(this);if(u){var o=H.domCache.get(this);ae(o.loader),vi()?u.icon&&ne(gn()):Jh(o),ke([o.popup,o.actions],m.loading),o.popup.removeAttribute("aria-busy"),o.popup.removeAttribute("data-loading"),o.confirmButton.disabled=!1,o.denyButton.disabled=!1,o.cancelButton.disabled=!1}}var Jh=function(o){var a=o.popup.getElementsByClassName(o.loader.getAttribute("data-button-to-replace"));a.length?ne(a[0],"inline-block"):Sd()&&ae(o.actions)};function Oa(){var u=H.innerParams.get(this),o=H.domCache.get(this);return o?yi(o.popup,u.input):null}function Pa(u,o,a){var l=H.domCache.get(u);o.forEach(function(h){l[h].disabled=a})}function Na(u,o){var a=U();if(!(!a||!u))if(u.type==="radio")for(var l=a.querySelectorAll('[name="'.concat(m.radio,'"]')),h=0;h<l.length;h++)l[h].disabled=o;else u.disabled=o}function Fa(){Pa(this,["confirmButton","denyButton","cancelButton"],!1)}function La(){Pa(this,["confirmButton","denyButton","cancelButton"],!0)}function Va(){Na(this.getInput(),!1)}function ja(){Na(this.getInput(),!0)}function Ua(u){var o=H.domCache.get(this),a=H.innerParams.get(this);ge(o.validationMessage,u),o.validationMessage.className=m["validation-message"],a.customClass&&a.customClass.validationMessage&&N(o.validationMessage,a.customClass.validationMessage),ne(o.validationMessage);var l=this.getInput();l&&(l.setAttribute("aria-invalid","true"),l.setAttribute("aria-describedby",m["validation-message"]),la(l),N(l,m.inputerror))}function Ba(){var u=H.domCache.get(this);u.validationMessage&&ae(u.validationMessage);var o=this.getInput();o&&(o.removeAttribute("aria-invalid"),o.removeAttribute("aria-describedby"),ke(o,m.inputerror))}var Lt={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,animation:!0,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0},ef=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","willClose"],tf={},nf=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],$a=function(o){return Object.prototype.hasOwnProperty.call(Lt,o)},za=function(o){return ef.indexOf(o)!==-1},Ha=function(o){return tf[o]},rf=function(o){$a(o)||ue('Unknown parameter "'.concat(o,'"'))},sf=function(o){nf.includes(o)&&ue('The parameter "'.concat(o,'" is incompatible with toasts'))},of=function(o){var a=Ha(o);a&&Ed(o,a)},af=function(o){o.backdrop===!1&&o.allowOutsideClick&&ue('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`');for(var a in o)rf(a),o.toast&&sf(a),of(a)};function Wa(u){var o=U(),a=H.innerParams.get(this);if(!o||je(o,a.hideClass.popup)){ue("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");return}var l=cf(u),h=Object.assign({},a,l);wa(this,h),H.innerParams.set(this,h),Object.defineProperties(this,{params:{value:Object.assign({},this.params,u),writable:!1,enumerable:!0}})}var cf=function(o){var a={};return Object.keys(o).forEach(function(l){za(l)?a[l]=o[l]:ue("Invalid parameter to update: ".concat(l))}),a};function Ga(){var u=H.domCache.get(this),o=H.innerParams.get(this);if(!o){qa(this);return}u.popup&&S.swalCloseEventFinishedCallback&&(S.swalCloseEventFinishedCallback(),delete S.swalCloseEventFinishedCallback),typeof o.didDestroy=="function"&&o.didDestroy(),lf(this)}var lf=function(o){qa(o),delete o.params,delete S.keydownHandler,delete S.keydownTarget,delete S.currentInstance},qa=function(o){o.isAwaitingPromise?(os(H,o),o.isAwaitingPromise=!0):(os(Pt,o),os(H,o),delete o.isAwaitingPromise,delete o.disableButtons,delete o.enableButtons,delete o.getInput,delete o.disableInput,delete o.enableInput,delete o.hideLoading,delete o.disableLoading,delete o.showValidationMessage,delete o.resetValidationMessage,delete o.close,delete o.closePopup,delete o.closeModal,delete o.closeToast,delete o.rejectPromise,delete o.update,delete o._destroy)},os=function(o,a){for(var l in o)o[l].delete(a)},uf=Object.freeze({__proto__:null,_destroy:Ga,close:qe,closeModal:qe,closePopup:qe,closeToast:qe,disableButtons:La,disableInput:ja,disableLoading:_i,enableButtons:Fa,enableInput:Va,getInput:Oa,handleAwaitingPromise:bn,hideLoading:_i,rejectPromise:Da,resetValidationMessage:Ba,showValidationMessage:Ua,update:Wa}),df=function(o,a,l){o.toast?hf(o,a,l):(pf(a),mf(a),gf(o,a,l))},hf=function(o,a,l){a.popup.onclick=function(){o&&(ff(o)||o.timer||o.input)||l(Ot.close)}},ff=function(o){return!!(o.showConfirmButton||o.showDenyButton||o.showCancelButton||o.showCloseButton)},Ci=!1,pf=function(o){o.popup.onmousedown=function(){o.container.onmouseup=function(a){o.container.onmouseup=function(){},a.target===o.container&&(Ci=!0)}}},mf=function(o){o.container.onmousedown=function(){o.popup.onmouseup=function(a){o.popup.onmouseup=function(){},(a.target===o.popup||a.target instanceof HTMLElement&&o.popup.contains(a.target))&&(Ci=!0)}}},gf=function(o,a,l){a.container.onclick=function(h){if(Ci){Ci=!1;return}h.target===a.container&&pi(o.allowOutsideClick)&&l(Ot.backdrop)}},vf=function(o){return f(o)==="object"&&o.jquery},Ka=function(o){return o instanceof Element||vf(o)},yf=function(o){var a={};return f(o[0])==="object"&&!Ka(o[0])?Object.assign(a,o[0]):["title","html","icon"].forEach(function(l,h){var g=o[h];typeof g=="string"||Ka(g)?a[l]=g:g!==void 0&&dt("Unexpected type of ".concat(l,'! Expected "string" or "Element", got ').concat(f(g)))}),a};function wf(){for(var u=this,o=arguments.length,a=new Array(o),l=0;l<o;l++)a[l]=arguments[l];return i(u,a)}function bf(u){var o=function(a){O(l,a);function l(){return v(this,l),e(this,l,arguments)}return E(l,[{key:"_main",value:function(g,b){return ut(ee(l.prototype),"_main",this).call(this,g,Object.assign({},u,b))}}]),l}(this);return o}var _f=function(){return S.timeout&&S.timeout.getTimerLeft()},Za=function(){if(S.timeout)return Rd(),S.timeout.stop()},Ya=function(){if(S.timeout){var o=S.timeout.start();return Jr(o),o}},Cf=function(){var o=S.timeout;return o&&(o.running?Za():Ya())},Ef=function(o){if(S.timeout){var a=S.timeout.increase(o);return Jr(a,!0),a}},Af=function(){return!!(S.timeout&&S.timeout.isRunning())},Xa=!1,as={};function If(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"data-swal-template";as[u]=this,Xa||(document.body.addEventListener("click",Df),Xa=!0)}var Df=function(o){for(var a=o.target;a&&a!==document;a=a.parentNode)for(var l in as){var h=a.getAttribute(l);if(h){as[l].fire({template:h});return}}},Tf=Object.freeze({__proto__:null,argsToParams:yf,bindClickHandler:If,clickCancel:gh,clickConfirm:ba,clickDeny:mh,enableLoading:Ft,fire:wf,getActions:vn,getCancelButton:xt,getCloseButton:Zr,getConfirmButton:xe,getContainer:de,getDenyButton:ht,getFocusableElements:Yr,getFooter:ca,getHtmlContainer:qr,getIcon:gn,getIconContent:Ad,getImage:aa,getInputLabel:Id,getLoader:kt,getPopup:U,getProgressSteps:Kr,getTimerLeft:_f,getTimerProgressBar:gi,getTitle:oa,getValidationMessage:mi,increaseTimer:Ef,isDeprecatedParameter:Ha,isLoading:Td,isTimerRunning:Af,isUpdatableParameter:za,isValidParameter:$a,isVisible:ph,mixin:bf,resumeTimer:Ya,showLoading:Ft,stopTimer:Za,toggleTimer:Cf}),Mf=function(){function u(o,a){v(this,u),this.callback=o,this.remaining=a,this.running=!1,this.start()}return E(u,[{key:"start",value:function(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}},{key:"stop",value:function(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date().getTime()-this.started.getTime()),this.remaining}},{key:"increase",value:function(a){var l=this.running;return l&&this.stop(),this.remaining+=a,l&&this.start(),this.remaining}},{key:"getTimerLeft",value:function(){return this.running&&(this.stop(),this.start()),this.remaining}},{key:"isRunning",value:function(){return this.running}}]),u}(),Qa=["swal-title","swal-html","swal-footer"],Sf=function(o){var a=typeof o.template=="string"?document.querySelector(o.template):o.template;if(!a)return{};var l=a.content;Lf(l);var h=Object.assign(Rf(l),xf(l),kf(l),Of(l),Pf(l),Nf(l),Ff(l,Qa));return h},Rf=function(o){var a={},l=Array.from(o.querySelectorAll("swal-param"));return l.forEach(function(h){gt(h,["name","value"]);var g=h.getAttribute("name"),b=h.getAttribute("value");typeof Lt[g]=="boolean"?a[g]=b!=="false":f(Lt[g])==="object"?a[g]=JSON.parse(b):a[g]=b}),a},xf=function(o){var a={},l=Array.from(o.querySelectorAll("swal-function-param"));return l.forEach(function(h){var g=h.getAttribute("name"),b=h.getAttribute("value");a[g]=new Function("return ".concat(b))()}),a},kf=function(o){var a={},l=Array.from(o.querySelectorAll("swal-button"));return l.forEach(function(h){gt(h,["type","color","aria-label"]);var g=h.getAttribute("type");a["".concat(g,"ButtonText")]=h.innerHTML,a["show".concat(Hr(g),"Button")]=!0,h.hasAttribute("color")&&(a["".concat(g,"ButtonColor")]=h.getAttribute("color")),h.hasAttribute("aria-label")&&(a["".concat(g,"ButtonAriaLabel")]=h.getAttribute("aria-label"))}),a},Of=function(o){var a={},l=o.querySelector("swal-image");return l&&(gt(l,["src","width","height","alt"]),l.hasAttribute("src")&&(a.imageUrl=l.getAttribute("src")),l.hasAttribute("width")&&(a.imageWidth=l.getAttribute("width")),l.hasAttribute("height")&&(a.imageHeight=l.getAttribute("height")),l.hasAttribute("alt")&&(a.imageAlt=l.getAttribute("alt"))),a},Pf=function(o){var a={},l=o.querySelector("swal-icon");return l&&(gt(l,["type","color"]),l.hasAttribute("type")&&(a.icon=l.getAttribute("type")),l.hasAttribute("color")&&(a.iconColor=l.getAttribute("color")),a.iconHtml=l.innerHTML),a},Nf=function(o){var a={},l=o.querySelector("swal-input");l&&(gt(l,["type","label","placeholder","value"]),a.input=l.getAttribute("type")||"text",l.hasAttribute("label")&&(a.inputLabel=l.getAttribute("label")),l.hasAttribute("placeholder")&&(a.inputPlaceholder=l.getAttribute("placeholder")),l.hasAttribute("value")&&(a.inputValue=l.getAttribute("value")));var h=Array.from(o.querySelectorAll("swal-input-option"));return h.length&&(a.inputOptions={},h.forEach(function(g){gt(g,["value"]);var b=g.getAttribute("value"),R=g.innerHTML;a.inputOptions[b]=R})),a},Ff=function(o,a){var l={};for(var h in a){var g=a[h],b=o.querySelector(g);b&&(gt(b,[]),l[g.replace(/^swal-/,"")]=b.innerHTML.trim())}return l},Lf=function(o){var a=Qa.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(o.children).forEach(function(l){var h=l.tagName.toLowerCase();a.includes(h)||ue("Unrecognized element <".concat(h,">"))})},gt=function(o,a){Array.from(o.attributes).forEach(function(l){a.indexOf(l.name)===-1&&ue(['Unrecognized attribute "'.concat(l.name,'" on <').concat(o.tagName.toLowerCase(),">."),"".concat(a.length?"Allowed attributes are: ".concat(a.join(", ")):"To set the value, use HTML within the element.")])})},Ja=10,Vf=function(o){var a=de(),l=U();typeof o.willOpen=="function"&&o.willOpen(l);var h=window.getComputedStyle(document.body),g=h.overflowY;$f(a,l,o),setTimeout(function(){Uf(a,l)},Ja),Xr()&&(Bf(a,o.scrollbarPadding,g),Ah()),!vi()&&!S.previousActiveElement&&(S.previousActiveElement=document.activeElement),typeof o.didOpen=="function"&&setTimeout(function(){return o.didOpen(l)}),ke(a,m["no-transition"])},jf=function u(o){var a=U();if(!(o.target!==a||!mt)){var l=de();a.removeEventListener(mt,u),l.style.overflowY="auto"}},Uf=function(o,a){mt&&fa(a)?(o.style.overflowY="hidden",a.addEventListener(mt,jf)):o.style.overflowY="auto"},Bf=function(o,a,l){Ih(),a&&l!=="hidden"&&kh(l),setTimeout(function(){o.scrollTop=0})},$f=function(o,a,l){N(o,l.showClass.backdrop),l.animation?(a.style.setProperty("opacity","0","important"),ne(a,"grid"),setTimeout(function(){N(a,l.showClass.popup),a.style.removeProperty("opacity")},Ja)):ne(a,"grid"),N([document.documentElement,document.body],m.shown),l.heightAuto&&l.backdrop&&!l.toast&&N([document.documentElement,document.body],m["height-auto"])},ec={email:function(o,a){return/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(o)?Promise.resolve():Promise.resolve(a||"Invalid email address")},url:function(o,a){return/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(o)?Promise.resolve():Promise.resolve(a||"Invalid URL")}};function zf(u){u.inputValidator||(u.input==="email"&&(u.inputValidator=ec.email),u.input==="url"&&(u.inputValidator=ec.url))}function Hf(u){(!u.target||typeof u.target=="string"&&!document.querySelector(u.target)||typeof u.target!="string"&&!u.target.appendChild)&&(ue('Target parameter is not valid, defaulting to "body"'),u.target="body")}function Wf(u){zf(u),u.showLoaderOnConfirm&&!u.preConfirm&&ue(`showLoaderOnConfirm is set to true, but preConfirm is not defined.
showLoaderOnConfirm should be used together with preConfirm, see usage example:
https://sweetalert2.github.io/#ajax-request`),Hf(u),typeof u.title=="string"&&(u.title=u.title.split(`
`).join("<br />")),Ld(u)}var Oe,Ei=new WeakMap,Q=function(){function u(){if(v(this,u),gd(this,Ei,void 0),!(typeof window>"u")){Oe=this;for(var o=arguments.length,a=new Array(o),l=0;l<o;l++)a[l]=arguments[l];var h=Object.freeze(this.constructor.argsToParams(a));this.params=h,this.isAwaitingPromise=!1,t(Ei,this,this._main(Oe.params))}}return E(u,[{key:"_main",value:function(a){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(af(Object.assign({},l,a)),S.currentInstance){var h=Pt.swalPromiseResolve.get(S.currentInstance),g=S.currentInstance.isAwaitingPromise;S.currentInstance._destroy(),g||h({isDismissed:!0}),Xr()&&Ea()}S.currentInstance=Oe;var b=qf(a,l);Wf(b),Object.freeze(b),S.timeout&&(S.timeout.stop(),delete S.timeout),clearTimeout(S.restoreFocusTimeout);var R=Kf(Oe);return wa(Oe,b),H.innerParams.set(Oe,b),Gf(Oe,R,b)}},{key:"then",value:function(a){return r(Ei,this).then(a)}},{key:"finally",value:function(a){return r(Ei,this).finally(a)}}]),u}(),Gf=function(o,a,l){return new Promise(function(h,g){var b=function(x){o.close({isDismissed:!0,dismiss:x})};Pt.swalPromiseResolve.set(o,h),Pt.swalPromiseReject.set(o,g),a.confirmButton.onclick=function(){Zh(o)},a.denyButton.onclick=function(){Yh(o)},a.cancelButton.onclick=function(){Xh(o,b)},a.closeButton.onclick=function(){b(Ot.close)},df(l,a,b),vh(S,l,b),Uh(o,l),Vf(l),Zf(S,l,b),Yf(a,l),setTimeout(function(){a.container.scrollTop=0})})},qf=function(o,a){var l=Sf(o),h=Object.assign({},Lt,a,l,o);return h.showClass=Object.assign({},Lt.showClass,h.showClass),h.hideClass=Object.assign({},Lt.hideClass,h.hideClass),h.animation===!1&&(h.showClass={backdrop:"swal2-noanimation"},h.hideClass={}),h},Kf=function(o){var a={popup:U(),container:de(),actions:vn(),confirmButton:xe(),denyButton:ht(),cancelButton:xt(),loader:kt(),closeButton:Zr(),validationMessage:mi(),progressSteps:Kr()};return H.domCache.set(o,a),a},Zf=function(o,a,l){var h=gi();ae(h),a.timer&&(o.timeout=new Mf(function(){l("timer"),delete o.timeout},a.timer),a.timerProgressBar&&(ne(h),Ae(h,a,"timerProgressBar"),setTimeout(function(){o.timeout&&o.timeout.running&&Jr(a.timer)})))},Yf=function(o,a){if(!a.toast){if(!pi(a.allowEnterKey)){Qf();return}Xf(o,a)||is(-1,1)}},Xf=function(o,a){return a.focusDeny&&ve(o.denyButton)?(o.denyButton.focus(),!0):a.focusCancel&&ve(o.cancelButton)?(o.cancelButton.focus(),!0):a.focusConfirm&&ve(o.confirmButton)?(o.confirmButton.focus(),!0):!1},Qf=function(){document.activeElement instanceof HTMLElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur()};if(typeof window<"u"&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|by|xn--p1ai)$/)){var tc=new Date,nc=localStorage.getItem("swal-initiation");nc?(tc.getTime()-Date.parse(nc))/(1e3*60*60*24)>3&&setTimeout(function(){document.body.style.pointerEvents="none";var u=document.createElement("audio");u.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",u.loop=!0,document.body.appendChild(u),setTimeout(function(){u.play().catch(function(){})},2500)},500):localStorage.setItem("swal-initiation","".concat(tc))}Q.prototype.disableButtons=La,Q.prototype.enableButtons=Fa,Q.prototype.getInput=Oa,Q.prototype.disableInput=ja,Q.prototype.enableInput=Va,Q.prototype.hideLoading=_i,Q.prototype.disableLoading=_i,Q.prototype.showValidationMessage=Ua,Q.prototype.resetValidationMessage=Ba,Q.prototype.close=qe,Q.prototype.closePopup=qe,Q.prototype.closeModal=qe,Q.prototype.closeToast=qe,Q.prototype.rejectPromise=Da,Q.prototype.update=Wa,Q.prototype._destroy=Ga,Object.assign(Q,Tf),Object.keys(uf).forEach(function(u){Q[u]=function(){if(Oe&&Oe[u]){var o;return(o=Oe)[u].apply(o,arguments)}return null}}),Q.DismissReason=Ot,Q.version="11.10.7";var Ai=Q;return Ai.default=Ai,Ai});typeof Ve<"u"&&Ve.Sweetalert2&&(Ve.swal=Ve.sweetAlert=Ve.Swal=Ve.SweetAlert=Ve.Sweetalert2);typeof document<"u"&&function(n,e){var r=n.createElement("style");if(n.getElementsByTagName("head")[0].appendChild(r),r.styleSheet)r.styleSheet.disabled||(r.styleSheet.cssText=e);else try{r.innerHTML=e}catch{r.innerText=e}}(document,'.swal2-popup.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;background:#fff;box-shadow:0 0 1px rgba(0,0,0,.075),0 1px 2px rgba(0,0,0,.075),1px 2px 4px rgba(0,0,0,.075),1px 3px 8px rgba(0,0,0,.075),2px 4px 16px rgba(0,0,0,.075);pointer-events:all}.swal2-popup.swal2-toast>*{grid-column:2}.swal2-popup.swal2-toast .swal2-title{margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-popup.swal2-toast .swal2-loading{justify-content:center}.swal2-popup.swal2-toast .swal2-input{height:2em;margin:.5em;font-size:1em}.swal2-popup.swal2-toast .swal2-validation-message{font-size:1em}.swal2-popup.swal2-toast .swal2-footer{margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-popup.swal2-toast .swal2-close{grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-popup.swal2-toast .swal2-html-container{margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-popup.swal2-toast .swal2-html-container:empty{padding:0}.swal2-popup.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-popup.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-popup.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-popup.swal2-toast .swal2-actions{justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-popup.swal2-toast .swal2-styled{margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-popup.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-popup.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-popup.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}.swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}.swal2-popup.swal2-toast.swal2-show{animation:swal2-toast-show .5s}.swal2-popup.swal2-toast.swal2-hide{animation:swal2-toast-hide .1s forwards}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:.625em;overflow-x:hidden;transition:background-color .1s;-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:rgba(0,0,0,.4)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:32em;max-width:100%;padding:0 0 1.25em;border:none;border-radius:5px;background:#fff;color:#545454;font-family:inherit;font-size:1rem}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:.8em 1em 0;color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:center;width:auto;margin:1.25em auto 0;padding:0}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled[disabled]{opacity:.4}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled:hover{background-image:linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1))}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled:active{background-image:linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2))}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:box-shadow .1s;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm{border:0;border-radius:.25em;background:initial;background-color:#7066e0;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm:focus{box-shadow:0 0 0 3px rgba(112,102,224,.5)}div:where(.swal2-container) button:where(.swal2-styled).swal2-deny{border:0;border-radius:.25em;background:initial;background-color:#dc3741;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled).swal2-deny:focus{box-shadow:0 0 0 3px rgba(220,55,65,.5)}div:where(.swal2-container) button:where(.swal2-styled).swal2-cancel{border:0;border-radius:.25em;background:initial;background-color:#6e7881;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled).swal2-cancel:focus{box-shadow:0 0 0 3px rgba(110,120,129,.5)}div:where(.swal2-container) button:where(.swal2-styled).swal2-default-outline:focus{box-shadow:0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) button:where(.swal2-styled):focus{outline:none}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid #eee;color:inherit;font-size:1em;text-align:center}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:5px;border-bottom-left-radius:5px}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em}div:where(.swal2-container) button:where(.swal2-close){z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:color .1s,box-shadow .1s;border:none;border-radius:5px;background:rgba(0,0,0,0);color:#ccc;font-family:monospace;font-size:2.5em;cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:none;background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus{outline:none;box-shadow:inset 0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) .swal2-html-container{z-index:1;justify-content:center;margin:1em 1.6em .3em;padding:0;overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:border-color .1s,box-shadow .1s;border:1px solid #d9d9d9;border-radius:.1875em;background:rgba(0,0,0,0);box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(0,0,0,0);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:1px solid #b4dbed;outline:none;box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:#fff}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:rgba(0,0,0,0);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:rgba(0,0,0,0);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:#fff;color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:#f0f0f0;color:#666;font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:#add8e6;color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:#add8e6}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;border:0.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}div:where(.swal2-icon).swal2-warning{border-color:#facea8;color:#f8bb86}div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}div:where(.swal2-icon).swal2-info{border-color:#9de0f6;color:#3fc3ee}div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}div:where(.swal2-icon).swal2-question{border-color:#c9dae1;color:#87adbd}div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:swal2-show .3s}.swal2-hide{animation:swal2-hide .15s forwards}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px rgba(0,0,0,.4)}@media print{body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container{position:static !important}}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}')});var kn=class{},Ki=class{},Et=class n{constructor(e){this.normalizedNames=new Map,this.lazyUpdate=null,e?typeof e=="string"?this.lazyInit=()=>{this.headers=new Map,e.split(`
`).forEach(r=>{let t=r.indexOf(":");if(t>0){let i=r.slice(0,t),s=i.toLowerCase(),c=r.slice(t+1).trim();this.maybeSetNormalizedName(i,s),this.headers.has(s)?this.headers.get(s).push(c):this.headers.set(s,[c])}})}:typeof Headers<"u"&&e instanceof Headers?(this.headers=new Map,e.forEach((r,t)=>{this.setHeaderEntries(t,r)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(e).forEach(([r,t])=>{this.setHeaderEntries(r,t)})}:this.headers=new Map}has(e){return this.init(),this.headers.has(e.toLowerCase())}get(e){this.init();let r=this.headers.get(e.toLowerCase());return r&&r.length>0?r[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(e){return this.init(),this.headers.get(e.toLowerCase())||null}append(e,r){return this.clone({name:e,value:r,op:"a"})}set(e,r){return this.clone({name:e,value:r,op:"s"})}delete(e,r){return this.clone({name:e,value:r,op:"d"})}maybeSetNormalizedName(e,r){this.normalizedNames.has(r)||this.normalizedNames.set(r,e)}init(){this.lazyInit&&(this.lazyInit instanceof n?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(e=>this.applyUpdate(e)),this.lazyUpdate=null))}copyFrom(e){e.init(),Array.from(e.headers.keys()).forEach(r=>{this.headers.set(r,e.headers.get(r)),this.normalizedNames.set(r,e.normalizedNames.get(r))})}clone(e){let r=new n;return r.lazyInit=this.lazyInit&&this.lazyInit instanceof n?this.lazyInit:this,r.lazyUpdate=(this.lazyUpdate||[]).concat([e]),r}applyUpdate(e){let r=e.name.toLowerCase();switch(e.op){case"a":case"s":let t=e.value;if(typeof t=="string"&&(t=[t]),t.length===0)return;this.maybeSetNormalizedName(e.name,r);let i=(e.op==="a"?this.headers.get(r):void 0)||[];i.push(...t),this.headers.set(r,i);break;case"d":let s=e.value;if(!s)this.headers.delete(r),this.normalizedNames.delete(r);else{let c=this.headers.get(r);if(!c)return;c=c.filter(d=>s.indexOf(d)===-1),c.length===0?(this.headers.delete(r),this.normalizedNames.delete(r)):this.headers.set(r,c)}break}}setHeaderEntries(e,r){let t=(Array.isArray(r)?r:[r]).map(s=>s.toString()),i=e.toLowerCase();this.headers.set(i,t),this.maybeSetNormalizedName(e,i)}forEach(e){this.init(),Array.from(this.normalizedNames.keys()).forEach(r=>e(this.normalizedNames.get(r),this.headers.get(r)))}};var Ds=class{encodeKey(e){return Yc(e)}encodeValue(e){return Yc(e)}decodeKey(e){return decodeURIComponent(e)}decodeValue(e){return decodeURIComponent(e)}};function rp(n,e){let r=new Map;return n.length>0&&n.replace(/^\?/,"").split("&").forEach(i=>{let s=i.indexOf("="),[c,d]=s==-1?[e.decodeKey(i),""]:[e.decodeKey(i.slice(0,s)),e.decodeValue(i.slice(s+1))],p=r.get(c)||[];p.push(d),r.set(c,p)}),r}var sp=/%(\d[a-f0-9])/gi,op={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Yc(n){return encodeURIComponent(n).replace(sp,(e,r)=>op[r]??e)}function qi(n){return`${n}`}var nt=class n{constructor(e={}){if(this.updates=null,this.cloneFrom=null,this.encoder=e.encoder||new Ds,e.fromString){if(e.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=rp(e.fromString,this.encoder)}else e.fromObject?(this.map=new Map,Object.keys(e.fromObject).forEach(r=>{let t=e.fromObject[r],i=Array.isArray(t)?t.map(qi):[qi(t)];this.map.set(r,i)})):this.map=null}has(e){return this.init(),this.map.has(e)}get(e){this.init();let r=this.map.get(e);return r?r[0]:null}getAll(e){return this.init(),this.map.get(e)||null}keys(){return this.init(),Array.from(this.map.keys())}append(e,r){return this.clone({param:e,value:r,op:"a"})}appendAll(e){let r=[];return Object.keys(e).forEach(t=>{let i=e[t];Array.isArray(i)?i.forEach(s=>{r.push({param:t,value:s,op:"a"})}):r.push({param:t,value:i,op:"a"})}),this.clone(r)}set(e,r){return this.clone({param:e,value:r,op:"s"})}delete(e,r){return this.clone({param:e,value:r,op:"d"})}toString(){return this.init(),this.keys().map(e=>{let r=this.encoder.encodeKey(e);return this.map.get(e).map(t=>r+"="+this.encoder.encodeValue(t)).join("&")}).filter(e=>e!=="").join("&")}clone(e){let r=new n({encoder:this.encoder});return r.cloneFrom=this.cloneFrom||this,r.updates=(this.updates||[]).concat(e),r}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(e=>this.map.set(e,this.cloneFrom.map.get(e))),this.updates.forEach(e=>{switch(e.op){case"a":case"s":let r=(e.op==="a"?this.map.get(e.param):void 0)||[];r.push(qi(e.value)),this.map.set(e.param,r);break;case"d":if(e.value!==void 0){let t=this.map.get(e.param)||[],i=t.indexOf(qi(e.value));i!==-1&&t.splice(i,1),t.length>0?this.map.set(e.param,t):this.map.delete(e.param)}else{this.map.delete(e.param);break}}}),this.cloneFrom=this.updates=null)}};var Ts=class{constructor(){this.map=new Map}set(e,r){return this.map.set(e,r),this}get(e){return this.map.has(e)||this.map.set(e,e.defaultValue()),this.map.get(e)}delete(e){return this.map.delete(e),this}has(e){return this.map.has(e)}keys(){return this.map.keys()}};function ap(n){switch(n){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Xc(n){return typeof ArrayBuffer<"u"&&n instanceof ArrayBuffer}function Qc(n){return typeof Blob<"u"&&n instanceof Blob}function Jc(n){return typeof FormData<"u"&&n instanceof FormData}function cp(n){return typeof URLSearchParams<"u"&&n instanceof URLSearchParams}var xn=class n{constructor(e,r,t,i){this.url=r,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=e.toUpperCase();let s;if(ap(this.method)||i?(this.body=t!==void 0?t:null,s=i):s=t,s&&(this.reportProgress=!!s.reportProgress,this.withCredentials=!!s.withCredentials,s.responseType&&(this.responseType=s.responseType),s.headers&&(this.headers=s.headers),s.context&&(this.context=s.context),s.params&&(this.params=s.params),this.transferCache=s.transferCache),this.headers??=new Et,this.context??=new Ts,!this.params)this.params=new nt,this.urlWithParams=r;else{let c=this.params.toString();if(c.length===0)this.urlWithParams=r;else{let d=r.indexOf("?"),p=d===-1?"?":d<r.length-1?"&":"";this.urlWithParams=r+p+c}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Xc(this.body)||Qc(this.body)||Jc(this.body)||cp(this.body)?this.body:this.body instanceof nt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Jc(this.body)?null:Qc(this.body)?this.body.type||null:Xc(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof nt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(e={}){let r=e.method||this.method,t=e.url||this.url,i=e.responseType||this.responseType,s=e.transferCache??this.transferCache,c=e.body!==void 0?e.body:this.body,d=e.withCredentials??this.withCredentials,p=e.reportProgress??this.reportProgress,f=e.headers||this.headers,v=e.params||this.params,I=e.context??this.context;return e.setHeaders!==void 0&&(f=Object.keys(e.setHeaders).reduce((E,O)=>E.set(O,e.setHeaders[O]),f)),e.setParams&&(v=Object.keys(e.setParams).reduce((E,O)=>E.set(O,e.setParams[O]),v)),new n(r,t,c,{params:v,headers:f,context:I,reportProgress:p,responseType:i,withCredentials:d,transferCache:s})}},Zt=function(n){return n[n.Sent=0]="Sent",n[n.UploadProgress=1]="UploadProgress",n[n.ResponseHeader=2]="ResponseHeader",n[n.DownloadProgress=3]="DownloadProgress",n[n.Response=4]="Response",n[n.User=5]="User",n}(Zt||{}),On=class{constructor(e,r=Xi.Ok,t="OK"){this.headers=e.headers||new Et,this.status=e.status!==void 0?e.status:r,this.statusText=e.statusText||t,this.url=e.url||null,this.ok=this.status>=200&&this.status<300}},Ms=class n extends On{constructor(e={}){super(e),this.type=Zt.ResponseHeader}clone(e={}){return new n({headers:e.headers||this.headers,status:e.status!==void 0?e.status:this.status,statusText:e.statusText||this.statusText,url:e.url||this.url||void 0})}},Zi=class n extends On{constructor(e={}){super(e),this.type=Zt.Response,this.body=e.body!==void 0?e.body:null}clone(e={}){return new n({body:e.body!==void 0?e.body:this.body,headers:e.headers||this.headers,status:e.status!==void 0?e.status:this.status,statusText:e.statusText||this.statusText,url:e.url||this.url||void 0})}},Yi=class extends On{constructor(e){super(e,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${e.url||"(unknown url)"}`:this.message=`Http failure response for ${e.url||"(unknown url)"}: ${e.status} ${e.statusText}`,this.error=e.error||null}},Xi=function(n){return n[n.Continue=100]="Continue",n[n.SwitchingProtocols=101]="SwitchingProtocols",n[n.Processing=102]="Processing",n[n.EarlyHints=103]="EarlyHints",n[n.Ok=200]="Ok",n[n.Created=201]="Created",n[n.Accepted=202]="Accepted",n[n.NonAuthoritativeInformation=203]="NonAuthoritativeInformation",n[n.NoContent=204]="NoContent",n[n.ResetContent=205]="ResetContent",n[n.PartialContent=206]="PartialContent",n[n.MultiStatus=207]="MultiStatus",n[n.AlreadyReported=208]="AlreadyReported",n[n.ImUsed=226]="ImUsed",n[n.MultipleChoices=300]="MultipleChoices",n[n.MovedPermanently=301]="MovedPermanently",n[n.Found=302]="Found",n[n.SeeOther=303]="SeeOther",n[n.NotModified=304]="NotModified",n[n.UseProxy=305]="UseProxy",n[n.Unused=306]="Unused",n[n.TemporaryRedirect=307]="TemporaryRedirect",n[n.PermanentRedirect=308]="PermanentRedirect",n[n.BadRequest=400]="BadRequest",n[n.Unauthorized=401]="Unauthorized",n[n.PaymentRequired=402]="PaymentRequired",n[n.Forbidden=403]="Forbidden",n[n.NotFound=404]="NotFound",n[n.MethodNotAllowed=405]="MethodNotAllowed",n[n.NotAcceptable=406]="NotAcceptable",n[n.ProxyAuthenticationRequired=407]="ProxyAuthenticationRequired",n[n.RequestTimeout=408]="RequestTimeout",n[n.Conflict=409]="Conflict",n[n.Gone=410]="Gone",n[n.LengthRequired=411]="LengthRequired",n[n.PreconditionFailed=412]="PreconditionFailed",n[n.PayloadTooLarge=413]="PayloadTooLarge",n[n.UriTooLong=414]="UriTooLong",n[n.UnsupportedMediaType=415]="UnsupportedMediaType",n[n.RangeNotSatisfiable=416]="RangeNotSatisfiable",n[n.ExpectationFailed=417]="ExpectationFailed",n[n.ImATeapot=418]="ImATeapot",n[n.MisdirectedRequest=421]="MisdirectedRequest",n[n.UnprocessableEntity=422]="UnprocessableEntity",n[n.Locked=423]="Locked",n[n.FailedDependency=424]="FailedDependency",n[n.TooEarly=425]="TooEarly",n[n.UpgradeRequired=426]="UpgradeRequired",n[n.PreconditionRequired=428]="PreconditionRequired",n[n.TooManyRequests=429]="TooManyRequests",n[n.RequestHeaderFieldsTooLarge=431]="RequestHeaderFieldsTooLarge",n[n.UnavailableForLegalReasons=451]="UnavailableForLegalReasons",n[n.InternalServerError=500]="InternalServerError",n[n.NotImplemented=501]="NotImplemented",n[n.BadGateway=502]="BadGateway",n[n.ServiceUnavailable=503]="ServiceUnavailable",n[n.GatewayTimeout=504]="GatewayTimeout",n[n.HttpVersionNotSupported=505]="HttpVersionNotSupported",n[n.VariantAlsoNegotiates=506]="VariantAlsoNegotiates",n[n.InsufficientStorage=507]="InsufficientStorage",n[n.LoopDetected=508]="LoopDetected",n[n.NotExtended=510]="NotExtended",n[n.NetworkAuthenticationRequired=511]="NetworkAuthenticationRequired",n}(Xi||{});function Is(n,e){return{body:e,headers:n.headers,context:n.context,observe:n.observe,params:n.params,reportProgress:n.reportProgress,responseType:n.responseType,withCredentials:n.withCredentials,transferCache:n.transferCache}}var lp=(()=>{let e=class e{constructor(t){this.handler=t}request(t,i,s={}){let c;if(t instanceof xn)c=t;else{let f;s.headers instanceof Et?f=s.headers:f=new Et(s.headers);let v;s.params&&(s.params instanceof nt?v=s.params:v=new nt({fromObject:s.params})),c=new xn(t,i,s.body!==void 0?s.body:null,{headers:f,context:s.context,params:v,reportProgress:s.reportProgress,responseType:s.responseType||"json",withCredentials:s.withCredentials,transferCache:s.transferCache})}let d=D(c).pipe(Ze(f=>this.handler.handle(f)));if(t instanceof xn||s.observe==="events")return d;let p=d.pipe(Me(f=>f instanceof Zi));switch(s.observe||"body"){case"body":switch(c.responseType){case"arraybuffer":return p.pipe(k(f=>{if(f.body!==null&&!(f.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return f.body}));case"blob":return p.pipe(k(f=>{if(f.body!==null&&!(f.body instanceof Blob))throw new Error("Response is not a Blob.");return f.body}));case"text":return p.pipe(k(f=>{if(f.body!==null&&typeof f.body!="string")throw new Error("Response is not a string.");return f.body}));case"json":default:return p.pipe(k(f=>f.body))}case"response":return p;default:throw new Error(`Unreachable: unhandled observe type ${s.observe}}`)}}delete(t,i={}){return this.request("DELETE",t,i)}get(t,i={}){return this.request("GET",t,i)}head(t,i={}){return this.request("HEAD",t,i)}jsonp(t,i){return this.request("JSONP",t,{params:new nt().append(i,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,i={}){return this.request("OPTIONS",t,i)}patch(t,i,s={}){return this.request("PATCH",t,Is(s,i))}post(t,i,s={}){return this.request("POST",t,Is(s,i))}put(t,i,s={}){return this.request("PUT",t,Is(s,i))}};e.\u0275fac=function(i){return new(i||e)(w(kn))},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})();function il(n,e){return e(n)}function up(n,e){return(r,t)=>e.intercept(r,{handle:i=>n(i,t)})}function dp(n,e,r){return(t,i)=>Ne(r,()=>e(t,s=>n(s,i)))}var hp=new T(""),Ss=new T(""),fp=new T(""),pp=new T("");function mp(){let n=null;return(e,r)=>{n===null&&(n=(_(hp,{optional:!0})??[]).reduceRight(up,il));let t=_(Mn),i=t.add();return n(e,r).pipe(yt(()=>t.remove(i)))}}var el=(()=>{let e=class e extends kn{constructor(t,i){super(),this.backend=t,this.injector=i,this.chain=null,this.pendingTasks=_(Mn);let s=_(pp,{optional:!0});this.backend=s??t}handle(t){if(this.chain===null){let s=Array.from(new Set([...this.injector.get(Ss),...this.injector.get(fp,[])]));this.chain=s.reduceRight((c,d)=>dp(c,d,this.injector),il)}let i=this.pendingTasks.add();return this.chain(t,s=>this.backend.handle(s)).pipe(yt(()=>this.pendingTasks.remove(i)))}};e.\u0275fac=function(i){return new(i||e)(w(Ki),w(wt))},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})();var gp=/^\)\]\}',?\n/;function vp(n){return"responseURL"in n&&n.responseURL?n.responseURL:/^X-Request-URL:/m.test(n.getAllResponseHeaders())?n.getResponseHeader("X-Request-URL"):null}var tl=(()=>{let e=class e{constructor(t){this.xhrFactory=t}handle(t){if(t.method==="JSONP")throw new B(-2800,!1);let i=this.xhrFactory;return(i.\u0275loadImpl?ie(i.\u0275loadImpl()):D(null)).pipe(we(()=>new vt(c=>{let d=i.build();if(d.open(t.method,t.urlWithParams),t.withCredentials&&(d.withCredentials=!0),t.headers.forEach((L,P)=>d.setRequestHeader(L,P.join(","))),t.headers.has("Accept")||d.setRequestHeader("Accept","application/json, text/plain, */*"),!t.headers.has("Content-Type")){let L=t.detectContentTypeHeader();L!==null&&d.setRequestHeader("Content-Type",L)}if(t.responseType){let L=t.responseType.toLowerCase();d.responseType=L!=="json"?L:"text"}let p=t.serializeBody(),f=null,v=()=>{if(f!==null)return f;let L=d.statusText||"OK",P=new Et(d.getAllResponseHeaders()),pe=vp(d)||t.url;return f=new Ms({headers:P,status:d.status,statusText:L,url:pe}),f},I=()=>{let{headers:L,status:P,statusText:pe,url:ut}=v(),oe=null;P!==Xi.NoContent&&(oe=typeof d.response>"u"?d.responseText:d.response),P===0&&(P=oe?Xi.Ok:0);let fn=P>=200&&P<300;if(t.responseType==="json"&&typeof oe=="string"){let Br=oe;oe=oe.replace(gp,"");try{oe=oe!==""?JSON.parse(oe):null}catch($r){oe=Br,fn&&(fn=!1,oe={error:$r,text:oe})}}fn?(c.next(new Zi({body:oe,headers:L,status:P,statusText:pe,url:ut||void 0})),c.complete()):c.error(new Yi({error:oe,headers:L,status:P,statusText:pe,url:ut||void 0}))},E=L=>{let{url:P}=v(),pe=new Yi({error:L,status:d.status||0,statusText:d.statusText||"Unknown Error",url:P||void 0});c.error(pe)},O=!1,ee=L=>{O||(c.next(v()),O=!0);let P={type:Zt.DownloadProgress,loaded:L.loaded};L.lengthComputable&&(P.total=L.total),t.responseType==="text"&&d.responseText&&(P.partialText=d.responseText),c.next(P)},Y=L=>{let P={type:Zt.UploadProgress,loaded:L.loaded};L.lengthComputable&&(P.total=L.total),c.next(P)};return d.addEventListener("load",I),d.addEventListener("error",E),d.addEventListener("timeout",E),d.addEventListener("abort",E),t.reportProgress&&(d.addEventListener("progress",ee),p!==null&&d.upload&&d.upload.addEventListener("progress",Y)),d.send(p),c.next({type:Zt.Sent}),()=>{d.removeEventListener("error",E),d.removeEventListener("abort",E),d.removeEventListener("load",I),d.removeEventListener("timeout",E),t.reportProgress&&(d.removeEventListener("progress",ee),p!==null&&d.upload&&d.upload.removeEventListener("progress",Y)),d.readyState!==d.DONE&&d.abort()}})))}};e.\u0275fac=function(i){return new(i||e)(w(Gi))},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})(),rl=new T(""),yp="XSRF-TOKEN",wp=new T("",{providedIn:"root",factory:()=>yp}),bp="X-XSRF-TOKEN",_p=new T("",{providedIn:"root",factory:()=>bp}),Qi=class{},Cp=(()=>{let e=class e{constructor(t,i,s){this.doc=t,this.platform=i,this.cookieName=s,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=Hi(t,this.cookieName),this.lastCookieString=t),this.lastToken}};e.\u0275fac=function(i){return new(i||e)(w(z),w(Ue),w(wp))},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})();function Ep(n,e){let r=n.url.toLowerCase();if(!_(rl)||n.method==="GET"||n.method==="HEAD"||r.startsWith("http://")||r.startsWith("https://"))return e(n);let t=_(Qi).getToken(),i=_(_p);return t!=null&&!n.headers.has(i)&&(n=n.clone({headers:n.headers.set(i,t)})),e(n)}var sl=function(n){return n[n.Interceptors=0]="Interceptors",n[n.LegacyInterceptors=1]="LegacyInterceptors",n[n.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",n[n.NoXsrfProtection=3]="NoXsrfProtection",n[n.JsonpSupport=4]="JsonpSupport",n[n.RequestsMadeViaParent=5]="RequestsMadeViaParent",n[n.Fetch=6]="Fetch",n}(sl||{});function Ap(n,e){return{\u0275kind:n,\u0275providers:e}}function Ip(...n){let e=[lp,tl,el,{provide:kn,useExisting:el},{provide:Ki,useExisting:tl},{provide:Ss,useValue:Ep,multi:!0},{provide:rl,useValue:!0},{provide:Qi,useClass:Cp}];for(let r of n)e.push(...r.\u0275providers);return Si(e)}var nl=new T("");function Dp(){return Ap(sl.LegacyInterceptors,[{provide:nl,useFactory:mp},{provide:Ss,useExisting:nl,multi:!0}])}var Aw=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=q({type:e}),e.\u0275inj=G({providers:[Ip(Dp())]});let n=e;return n})();var ks=class extends $c{constructor(){super(...arguments),this.supportsDOMEvents=!0}},Os=class n extends ks{static makeCurrent(){Bc(new n)}onAndCancel(e,r,t){return e.addEventListener(r,t),()=>{e.removeEventListener(r,t)}}dispatchEvent(e,r){e.dispatchEvent(r)}remove(e){e.parentNode&&e.parentNode.removeChild(e)}createElement(e,r){return r=r||this.getDefaultDocument(),r.createElement(e)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(e){return e.nodeType===Node.ELEMENT_NODE}isShadowRoot(e){return e instanceof DocumentFragment}getGlobalEventTarget(e,r){return r==="window"?window:r==="document"?e:r==="body"?e.body:null}getBaseHref(e){let r=Mp();return r==null?null:Sp(r)}resetBaseElement(){Pn=null}getUserAgent(){return window.navigator.userAgent}getCookie(e){return Hi(document.cookie,e)}},Pn=null;function Mp(){return Pn=Pn||document.querySelector("base"),Pn?Pn.getAttribute("href"):null}function Sp(n){return new URL(n,document.baseURI).pathname}var Ps=class{addToWindow(e){Xe.getAngularTestability=(t,i=!0)=>{let s=e.findTestabilityInTree(t,i);if(s==null)throw new B(5103,!1);return s},Xe.getAllAngularTestabilities=()=>e.getAllTestabilities(),Xe.getAllAngularRootElements=()=>e.getAllRootElements();let r=t=>{let i=Xe.getAllAngularTestabilities(),s=i.length,c=function(){s--,s==0&&t()};i.forEach(d=>{d.whenStable(c)})};Xe.frameworkStabilizers||(Xe.frameworkStabilizers=[]),Xe.frameworkStabilizers.push(r)}findTestabilityInTree(e,r,t){if(r==null)return null;let i=e.getTestability(r);return i??(t?Ct().isShadowRoot(r)?this.findTestabilityInTree(e,r.host,!0):this.findTestabilityInTree(e,r.parentElement,!0):null)}},Rp=(()=>{let e=class e{build(){return new XMLHttpRequest}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})(),Ns=new T(""),ll=(()=>{let e=class e{constructor(t,i){this._zone=i,this._eventNameToPlugin=new Map,t.forEach(s=>{s.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,i,s){return this._findPluginFor(i).addEventListener(t,i,s)}getZone(){return this._zone}_findPluginFor(t){let i=this._eventNameToPlugin.get(t);if(i)return i;if(i=this._plugins.find(c=>c.supports(t)),!i)throw new B(5101,!1);return this._eventNameToPlugin.set(t,i),i}};e.\u0275fac=function(i){return new(i||e)(w(Ns),w($))},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})(),Ji=class{constructor(e){this._doc=e}},Rs="ng-app-id",ul=(()=>{let e=class e{constructor(t,i,s,c={}){this.doc=t,this.appId=i,this.nonce=s,this.platformId=c,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Wi(c),this.resetHostNodes()}addStyles(t){for(let i of t)this.changeUsageCount(i,1)===1&&this.onStyleAdded(i)}removeStyles(t){for(let i of t)this.changeUsageCount(i,-1)<=0&&this.onStyleRemoved(i)}ngOnDestroy(){let t=this.styleNodesInDOM;t&&(t.forEach(i=>i.remove()),t.clear());for(let i of this.getAllStyles())this.onStyleRemoved(i);this.resetHostNodes()}addHost(t){this.hostNodes.add(t);for(let i of this.getAllStyles())this.addStyleToHost(t,i)}removeHost(t){this.hostNodes.delete(t)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(t){for(let i of this.hostNodes)this.addStyleToHost(i,t)}onStyleRemoved(t){let i=this.styleRef;i.get(t)?.elements?.forEach(s=>s.remove()),i.delete(t)}collectServerRenderedStyles(){let t=this.doc.head?.querySelectorAll(`style[${Rs}="${this.appId}"]`);if(t?.length){let i=new Map;return t.forEach(s=>{s.textContent!=null&&i.set(s.textContent,s)}),i}return null}changeUsageCount(t,i){let s=this.styleRef;if(s.has(t)){let c=s.get(t);return c.usage+=i,c.usage}return s.set(t,{usage:i,elements:[]}),i}getStyleElement(t,i){let s=this.styleNodesInDOM,c=s?.get(i);if(c?.parentNode===t)return s.delete(i),c.removeAttribute(Rs),c;{let d=this.doc.createElement("style");return this.nonce&&d.setAttribute("nonce",this.nonce),d.textContent=i,this.platformIsServer&&d.setAttribute(Rs,this.appId),t.appendChild(d),d}}addStyleToHost(t,i){let s=this.getStyleElement(t,i),c=this.styleRef,d=c.get(i)?.elements;d?d.push(s):c.set(i,{elements:[s],usage:1})}resetHostNodes(){let t=this.hostNodes;t.clear(),t.add(this.doc.head)}};e.\u0275fac=function(i){return new(i||e)(w(z),w(zt),w(In,8),w(Ue))},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})(),xs={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},Vs=/%COMP%/g,dl="%COMP%",xp=`_nghost-${dl}`,kp=`_ngcontent-${dl}`,Op=!0,Pp=new T("",{providedIn:"root",factory:()=>Op});function Np(n){return kp.replace(Vs,n)}function Fp(n){return xp.replace(Vs,n)}function hl(n,e){return e.map(r=>r.replace(Vs,n))}var ol=(()=>{let e=class e{constructor(t,i,s,c,d,p,f,v=null){this.eventManager=t,this.sharedStylesHost=i,this.appId=s,this.removeStylesOnCompDestroy=c,this.doc=d,this.platformId=p,this.ngZone=f,this.nonce=v,this.rendererByCompId=new Map,this.platformIsServer=Wi(p),this.defaultRenderer=new Nn(t,d,f,this.platformIsServer)}createRenderer(t,i){if(!t||!i)return this.defaultRenderer;this.platformIsServer&&i.encapsulation===An.ShadowDom&&(i=W(y({},i),{encapsulation:An.Emulated}));let s=this.getOrCreateRenderer(t,i);return s instanceof er?s.applyToHost(t):s instanceof Fn&&s.applyStyles(),s}getOrCreateRenderer(t,i){let s=this.rendererByCompId,c=s.get(i.id);if(!c){let d=this.doc,p=this.ngZone,f=this.eventManager,v=this.sharedStylesHost,I=this.removeStylesOnCompDestroy,E=this.platformIsServer;switch(i.encapsulation){case An.Emulated:c=new er(f,v,i,this.appId,I,d,p,E);break;case An.ShadowDom:return new Fs(f,v,t,i,d,p,this.nonce,E);default:c=new Fn(f,v,i,I,d,p,E);break}s.set(i.id,c)}return c}ngOnDestroy(){this.rendererByCompId.clear()}};e.\u0275fac=function(i){return new(i||e)(w(ll),w(ul),w(zt),w(Pp),w(z),w(Ue),w($),w(In))},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})(),Nn=class{constructor(e,r,t,i){this.eventManager=e,this.doc=r,this.ngZone=t,this.platformIsServer=i,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(e,r){return r?this.doc.createElementNS(xs[r]||r,e):this.doc.createElement(e)}createComment(e){return this.doc.createComment(e)}createText(e){return this.doc.createTextNode(e)}appendChild(e,r){(al(e)?e.content:e).appendChild(r)}insertBefore(e,r,t){e&&(al(e)?e.content:e).insertBefore(r,t)}removeChild(e,r){e&&e.removeChild(r)}selectRootElement(e,r){let t=typeof e=="string"?this.doc.querySelector(e):e;if(!t)throw new B(-5104,!1);return r||(t.textContent=""),t}parentNode(e){return e.parentNode}nextSibling(e){return e.nextSibling}setAttribute(e,r,t,i){if(i){r=i+":"+r;let s=xs[i];s?e.setAttributeNS(s,r,t):e.setAttribute(r,t)}else e.setAttribute(r,t)}removeAttribute(e,r,t){if(t){let i=xs[t];i?e.removeAttributeNS(i,r):e.removeAttribute(`${t}:${r}`)}else e.removeAttribute(r)}addClass(e,r){e.classList.add(r)}removeClass(e,r){e.classList.remove(r)}setStyle(e,r,t,i){i&(Dn.DashCase|Dn.Important)?e.style.setProperty(r,t,i&Dn.Important?"important":""):e.style[r]=t}removeStyle(e,r,t){t&Dn.DashCase?e.style.removeProperty(r):e.style[r]=""}setProperty(e,r,t){e!=null&&(e[r]=t)}setValue(e,r){e.nodeValue=r}listen(e,r,t){if(typeof e=="string"&&(e=Ct().getGlobalEventTarget(this.doc,e),!e))throw new Error(`Unsupported event target ${e} for event ${r}`);return this.eventManager.addEventListener(e,r,this.decoratePreventDefault(t))}decoratePreventDefault(e){return r=>{if(r==="__ngUnwrap__")return e;(this.platformIsServer?this.ngZone.runGuarded(()=>e(r)):e(r))===!1&&r.preventDefault()}}};function al(n){return n.tagName==="TEMPLATE"&&n.content!==void 0}var Fs=class extends Nn{constructor(e,r,t,i,s,c,d,p){super(e,s,c,p),this.sharedStylesHost=r,this.hostEl=t,this.shadowRoot=t.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let f=hl(i.id,i.styles);for(let v of f){let I=document.createElement("style");d&&I.setAttribute("nonce",d),I.textContent=v,this.shadowRoot.appendChild(I)}}nodeOrShadowRoot(e){return e===this.hostEl?this.shadowRoot:e}appendChild(e,r){return super.appendChild(this.nodeOrShadowRoot(e),r)}insertBefore(e,r,t){return super.insertBefore(this.nodeOrShadowRoot(e),r,t)}removeChild(e,r){return super.removeChild(this.nodeOrShadowRoot(e),r)}parentNode(e){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(e)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Fn=class extends Nn{constructor(e,r,t,i,s,c,d,p){super(e,s,c,d),this.sharedStylesHost=r,this.removeStylesOnCompDestroy=i,this.styles=p?hl(p,t.styles):t.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},er=class extends Fn{constructor(e,r,t,i,s,c,d,p){let f=i+"-"+t.id;super(e,r,t,s,c,d,p,f),this.contentAttr=Np(f),this.hostAttr=Fp(f)}applyToHost(e){this.applyStyles(),this.setAttribute(e,this.hostAttr,"")}createElement(e,r){let t=super.createElement(e,r);return super.setAttribute(t,this.contentAttr,""),t}},Lp=(()=>{let e=class e extends Ji{constructor(t){super(t)}supports(t){return!0}addEventListener(t,i,s){return t.addEventListener(i,s,!1),()=>this.removeEventListener(t,i,s)}removeEventListener(t,i,s){return t.removeEventListener(i,s)}};e.\u0275fac=function(i){return new(i||e)(w(z))},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})(),cl=["alt","control","meta","shift"],Vp={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},jp={alt:n=>n.altKey,control:n=>n.ctrlKey,meta:n=>n.metaKey,shift:n=>n.shiftKey},Up=(()=>{let e=class e extends Ji{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,i,s){let c=e.parseEventName(i),d=e.eventCallback(c.fullKey,s,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Ct().onAndCancel(t,c.domEventName,d))}static parseEventName(t){let i=t.toLowerCase().split("."),s=i.shift();if(i.length===0||!(s==="keydown"||s==="keyup"))return null;let c=e._normalizeKey(i.pop()),d="",p=i.indexOf("code");if(p>-1&&(i.splice(p,1),d="code."),cl.forEach(v=>{let I=i.indexOf(v);I>-1&&(i.splice(I,1),d+=v+".")}),d+=c,i.length!=0||c.length===0)return null;let f={};return f.domEventName=s,f.fullKey=d,f}static matchEventFullKeyCode(t,i){let s=Vp[t.key]||t.key,c="";return i.indexOf("code.")>-1&&(s=t.code,c="code."),s==null||!s?!1:(s=s.toLowerCase(),s===" "?s="space":s==="."&&(s="dot"),cl.forEach(d=>{if(d!==s){let p=jp[d];p(t)&&(c+=d+".")}}),c+=s,c===i)}static eventCallback(t,i,s){return c=>{e.matchEventFullKeyCode(c,t)&&s.runGuarded(()=>i(c))}}static _normalizeKey(t){return t==="esc"?"escape":t}};e.\u0275fac=function(i){return new(i||e)(w(z))},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})();function Bp(){Os.makeCurrent()}function $p(){return new ms}function zp(){return mc(document),document}var Hp=[{provide:Ue,useValue:qc},{provide:gc,useValue:Bp,multi:!0},{provide:z,useFactory:zp,deps:[]}],Lw=Lc(Vc,"browser",Hp),Wp=new T(""),Gp=[{provide:ji,useClass:Ps,deps:[]},{provide:Nc,useClass:Ui,deps:[$,Cs,ji]},{provide:Ui,useClass:Ui,deps:[$,Cs,ji]}],qp=[{provide:fc,useValue:"root"},{provide:ms,useFactory:$p,deps:[]},{provide:Ns,useClass:Lp,multi:!0,deps:[z,$,Ue]},{provide:Ns,useClass:Up,multi:!0,deps:[z]},ol,ul,ll,{provide:Dc,useExisting:ol},{provide:Gi,useClass:Rp,deps:[]},[]],Vw=(()=>{let e=class e{constructor(t){}static withServerTransition(t){return{ngModule:e,providers:[{provide:zt,useValue:t.appId}]}}};e.\u0275fac=function(i){return new(i||e)(w(Wp,12))},e.\u0275mod=q({type:e}),e.\u0275inj=G({providers:[...qp,...Gp],imports:[Gc,jc]});let n=e;return n})();var fl=(()=>{let e=class e{constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}};e.\u0275fac=function(i){return new(i||e)(w(z))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();var Kp=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:function(i){let s=null;return i?s=new(i||e):s=w(Zp),s},providedIn:"root"});let n=e;return n})(),Zp=(()=>{let e=class e extends Kp{constructor(t){super(),this._doc=t}sanitize(t,i){if(i==null)return null;switch(t){case bt.NONE:return i;case bt.HTML:return Wt(i,"HTML")?Ht(i):Ec(this._doc,String(i)).toString();case bt.STYLE:return Wt(i,"Style")?Ht(i):i;case bt.SCRIPT:if(Wt(i,"Script"))return Ht(i);throw new B(5200,!1);case bt.URL:return Wt(i,"URL")?Ht(i):Cc(String(i));case bt.RESOURCE_URL:if(Wt(i,"ResourceURL"))return Ht(i);throw new B(5201,!1);default:throw new B(5202,!1)}}bypassSecurityTrustHtml(t){return vc(t)}bypassSecurityTrustStyle(t){return yc(t)}bypassSecurityTrustScript(t){return wc(t)}bypassSecurityTrustUrl(t){return bc(t)}bypassSecurityTrustResourceUrl(t){return _c(t)}};e.\u0275fac=function(i){return new(i||e)(w(z))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();var Cl=(()=>{let e=class e{constructor(t,i){this._renderer=t,this._elementRef=i,this.onChange=s=>{},this.onTouched=()=>{}}setProperty(t,i){this._renderer.setProperty(this._elementRef.nativeElement,t,i)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}};e.\u0275fac=function(i){return new(i||e)(C(Gt),C(re))},e.\u0275dir=V({type:e});let n=e;return n})(),El=(()=>{let e=class e extends Cl{};e.\u0275fac=(()=>{let t;return function(s){return(t||(t=Qe(e)))(s||e)}})(),e.\u0275dir=V({type:e,features:[ce]});let n=e;return n})(),$n=new T("");var Xp={provide:$n,useExisting:Se(()=>Al),multi:!0};function Qp(){let n=Ct()?Ct().getUserAgent():"";return/android (\d+)/.test(n.toLowerCase())}var Jp=new T(""),Al=(()=>{let e=class e extends Cl{constructor(t,i,s){super(t,i),this._compositionMode=s,this._composing=!1,this._compositionMode==null&&(this._compositionMode=!Qp())}writeValue(t){let i=t??"";this.setProperty("value",i)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}};e.\u0275fac=function(i){return new(i||e)(C(Gt),C(re),C(Jp,8))},e.\u0275dir=V({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(i,s){i&1&&Be("input",function(d){return s._handleInput(d.target.value)})("blur",function(){return s.onTouched()})("compositionstart",function(){return s._compositionStart()})("compositionend",function(d){return s._compositionEnd(d.target.value)})},features:[be([Xp]),ce]});let n=e;return n})();function it(n){return n==null||(typeof n=="string"||Array.isArray(n))&&n.length===0}function Il(n){return n!=null&&typeof n.length=="number"}var rt=new T(""),At=new T(""),em=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,pl=class{static min(e){return tm(e)}static max(e){return nm(e)}static required(e){return im(e)}static requiredTrue(e){return rm(e)}static email(e){return sm(e)}static minLength(e){return om(e)}static maxLength(e){return Dl(e)}static pattern(e){return am(e)}static nullValidator(e){return nr(e)}static compose(e){return kl(e)}static composeAsync(e){return Ol(e)}};function tm(n){return e=>{if(it(e.value)||it(n))return null;let r=parseFloat(e.value);return!isNaN(r)&&r<n?{min:{min:n,actual:e.value}}:null}}function nm(n){return e=>{if(it(e.value)||it(n))return null;let r=parseFloat(e.value);return!isNaN(r)&&r>n?{max:{max:n,actual:e.value}}:null}}function im(n){return it(n.value)?{required:!0}:null}function rm(n){return n.value===!0?null:{required:!0}}function sm(n){return it(n.value)||em.test(n.value)?null:{email:!0}}function om(n){return e=>it(e.value)||!Il(e.value)?null:e.value.length<n?{minlength:{requiredLength:n,actualLength:e.value.length}}:null}function Dl(n){return e=>Il(e.value)&&e.value.length>n?{maxlength:{requiredLength:n,actualLength:e.value.length}}:null}function am(n){if(!n)return nr;let e,r;return typeof n=="string"?(r="",n.charAt(0)!=="^"&&(r+="^"),r+=n,n.charAt(n.length-1)!=="$"&&(r+="$"),e=new RegExp(r)):(r=n.toString(),e=n),t=>{if(it(t.value))return null;let i=t.value;return e.test(i)?null:{pattern:{requiredPattern:r,actualValue:i}}}}function nr(n){return null}function Tl(n){return n!=null}function Ml(n){return Bi(n)?ie(n):n}function Sl(n){let e={};return n.forEach(r=>{e=r!=null?y(y({},e),r):e}),Object.keys(e).length===0?null:e}function Rl(n,e){return e.map(r=>r(n))}function cm(n){return!n.validate}function xl(n){return n.map(e=>cm(e)?e:r=>e.validate(r))}function kl(n){if(!n)return null;let e=n.filter(Tl);return e.length==0?null:function(r){return Sl(Rl(r,e))}}function $s(n){return n!=null?kl(xl(n)):null}function Ol(n){if(!n)return null;let e=n.filter(Tl);return e.length==0?null:function(r){let t=Rl(r,e).map(Ml);return oc(t).pipe(k(Sl))}}function zs(n){return n!=null?Ol(xl(n)):null}function ml(n,e){return n===null?[e]:Array.isArray(n)?[...n,e]:[n,e]}function Pl(n){return n._rawValidators}function Nl(n){return n._rawAsyncValidators}function js(n){return n?Array.isArray(n)?n:[n]:[]}function ir(n,e){return Array.isArray(n)?n.includes(e):n===e}function gl(n,e){let r=js(e);return js(n).forEach(i=>{ir(r,i)||r.push(i)}),r}function vl(n,e){return js(e).filter(r=>!ir(n,r))}var rr=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(e){this._rawValidators=e||[],this._composedValidatorFn=$s(this._rawValidators)}_setAsyncValidators(e){this._rawAsyncValidators=e||[],this._composedAsyncValidatorFn=zs(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(e){this._onDestroyCallbacks.push(e)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(e=>e()),this._onDestroyCallbacks=[]}reset(e=void 0){this.control&&this.control.reset(e)}hasError(e,r){return this.control?this.control.hasError(e,r):!1}getError(e,r){return this.control?this.control.getError(e,r):null}},le=class extends rr{get formDirective(){return null}get path(){return null}},ze=class extends rr{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}},sr=class{constructor(e){this._cd=e}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}},lm={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},tb=W(y({},lm),{"[class.ng-submitted]":"isSubmitted"}),nb=(()=>{let e=class e extends sr{constructor(t){super(t)}};e.\u0275fac=function(i){return new(i||e)(C(ze,2))},e.\u0275dir=V({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(i,s){i&2&&et("ng-untouched",s.isUntouched)("ng-touched",s.isTouched)("ng-pristine",s.isPristine)("ng-dirty",s.isDirty)("ng-valid",s.isValid)("ng-invalid",s.isInvalid)("ng-pending",s.isPending)},features:[ce]});let n=e;return n})(),ib=(()=>{let e=class e extends sr{constructor(t){super(t)}};e.\u0275fac=function(i){return new(i||e)(C(le,10))},e.\u0275dir=V({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(i,s){i&2&&et("ng-untouched",s.isUntouched)("ng-touched",s.isTouched)("ng-pristine",s.isPristine)("ng-dirty",s.isDirty)("ng-valid",s.isValid)("ng-invalid",s.isInvalid)("ng-pending",s.isPending)("ng-submitted",s.isSubmitted)},features:[ce]});let n=e;return n})();var Ln="VALID",tr="INVALID",Yt="PENDING",Vn="DISABLED";function Hs(n){return(lr(n)?n.validators:n)||null}function um(n){return Array.isArray(n)?$s(n):n||null}function Ws(n,e){return(lr(e)?e.asyncValidators:n)||null}function dm(n){return Array.isArray(n)?zs(n):n||null}function lr(n){return n!=null&&!Array.isArray(n)&&typeof n=="object"}function Fl(n,e,r){let t=n.controls;if(!(e?Object.keys(t):t).length)throw new B(1e3,"");if(!t[r])throw new B(1001,"")}function Ll(n,e,r){n._forEachChild((t,i)=>{if(r[i]===void 0)throw new B(1002,"")})}var Xt=class{constructor(e,r){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(e),this._assignAsyncValidators(r)}get validator(){return this._composedValidatorFn}set validator(e){this._rawValidators=this._composedValidatorFn=e}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(e){this._rawAsyncValidators=this._composedAsyncValidatorFn=e}get parent(){return this._parent}get valid(){return this.status===Ln}get invalid(){return this.status===tr}get pending(){return this.status==Yt}get disabled(){return this.status===Vn}get enabled(){return this.status!==Vn}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(e){this._assignValidators(e)}setAsyncValidators(e){this._assignAsyncValidators(e)}addValidators(e){this.setValidators(gl(e,this._rawValidators))}addAsyncValidators(e){this.setAsyncValidators(gl(e,this._rawAsyncValidators))}removeValidators(e){this.setValidators(vl(e,this._rawValidators))}removeAsyncValidators(e){this.setAsyncValidators(vl(e,this._rawAsyncValidators))}hasValidator(e){return ir(this._rawValidators,e)}hasAsyncValidator(e){return ir(this._rawAsyncValidators,e)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(e={}){this.touched=!0,this._parent&&!e.onlySelf&&this._parent.markAsTouched(e)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(e=>e.markAllAsTouched())}markAsUntouched(e={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(r=>{r.markAsUntouched({onlySelf:!0})}),this._parent&&!e.onlySelf&&this._parent._updateTouched(e)}markAsDirty(e={}){this.pristine=!1,this._parent&&!e.onlySelf&&this._parent.markAsDirty(e)}markAsPristine(e={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(r=>{r.markAsPristine({onlySelf:!0})}),this._parent&&!e.onlySelf&&this._parent._updatePristine(e)}markAsPending(e={}){this.status=Yt,e.emitEvent!==!1&&this.statusChanges.emit(this.status),this._parent&&!e.onlySelf&&this._parent.markAsPending(e)}disable(e={}){let r=this._parentMarkedDirty(e.onlySelf);this.status=Vn,this.errors=null,this._forEachChild(t=>{t.disable(W(y({},e),{onlySelf:!0}))}),this._updateValue(),e.emitEvent!==!1&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(W(y({},e),{skipPristineCheck:r})),this._onDisabledChange.forEach(t=>t(!0))}enable(e={}){let r=this._parentMarkedDirty(e.onlySelf);this.status=Ln,this._forEachChild(t=>{t.enable(W(y({},e),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent}),this._updateAncestors(W(y({},e),{skipPristineCheck:r})),this._onDisabledChange.forEach(t=>t(!1))}_updateAncestors(e){this._parent&&!e.onlySelf&&(this._parent.updateValueAndValidity(e),e.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(e){this._parent=e}getRawValue(){return this.value}updateValueAndValidity(e={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Ln||this.status===Yt)&&this._runAsyncValidator(e.emitEvent)),e.emitEvent!==!1&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.updateValueAndValidity(e)}_updateTreeValidity(e={emitEvent:!0}){this._forEachChild(r=>r._updateTreeValidity(e)),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Vn:Ln}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(e){if(this.asyncValidator){this.status=Yt,this._hasOwnPendingAsyncValidator=!0;let r=Ml(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(t=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(t,{emitEvent:e})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(e,r={}){this.errors=e,this._updateControlsErrors(r.emitEvent!==!1)}get(e){let r=e;return r==null||(Array.isArray(r)||(r=r.split(".")),r.length===0)?null:r.reduce((t,i)=>t&&t._find(i),this)}getError(e,r){let t=r?this.get(r):this;return t&&t.errors?t.errors[e]:null}hasError(e,r){return!!this.getError(e,r)}get root(){let e=this;for(;e._parent;)e=e._parent;return e}_updateControlsErrors(e){this.status=this._calculateStatus(),e&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(e)}_initObservables(){this.valueChanges=new Z,this.statusChanges=new Z}_calculateStatus(){return this._allControlsDisabled()?Vn:this.errors?tr:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Yt)?Yt:this._anyControlsHaveStatus(tr)?tr:Ln}_anyControlsHaveStatus(e){return this._anyControls(r=>r.status===e)}_anyControlsDirty(){return this._anyControls(e=>e.dirty)}_anyControlsTouched(){return this._anyControls(e=>e.touched)}_updatePristine(e={}){this.pristine=!this._anyControlsDirty(),this._parent&&!e.onlySelf&&this._parent._updatePristine(e)}_updateTouched(e={}){this.touched=this._anyControlsTouched(),this._parent&&!e.onlySelf&&this._parent._updateTouched(e)}_registerOnCollectionChange(e){this._onCollectionChange=e}_setUpdateStrategy(e){lr(e)&&e.updateOn!=null&&(this._updateOn=e.updateOn)}_parentMarkedDirty(e){let r=this._parent&&this._parent.dirty;return!e&&!!r&&!this._parent._anyControlsDirty()}_find(e){return null}_assignValidators(e){this._rawValidators=Array.isArray(e)?e.slice():e,this._composedValidatorFn=um(this._rawValidators)}_assignAsyncValidators(e){this._rawAsyncValidators=Array.isArray(e)?e.slice():e,this._composedAsyncValidatorFn=dm(this._rawAsyncValidators)}},Qt=class extends Xt{constructor(e,r,t){super(Hs(r),Ws(t,r)),this.controls=e,this._initObservables(),this._setUpdateStrategy(r),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(e,r){return this.controls[e]?this.controls[e]:(this.controls[e]=r,r.setParent(this),r._registerOnCollectionChange(this._onCollectionChange),r)}addControl(e,r,t={}){this.registerControl(e,r),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}removeControl(e,r={}){this.controls[e]&&this.controls[e]._registerOnCollectionChange(()=>{}),delete this.controls[e],this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}setControl(e,r,t={}){this.controls[e]&&this.controls[e]._registerOnCollectionChange(()=>{}),delete this.controls[e],r&&this.registerControl(e,r),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}contains(e){return this.controls.hasOwnProperty(e)&&this.controls[e].enabled}setValue(e,r={}){Ll(this,!0,e),Object.keys(e).forEach(t=>{Fl(this,!0,t),this.controls[t].setValue(e[t],{onlySelf:!0,emitEvent:r.emitEvent})}),this.updateValueAndValidity(r)}patchValue(e,r={}){e!=null&&(Object.keys(e).forEach(t=>{let i=this.controls[t];i&&i.patchValue(e[t],{onlySelf:!0,emitEvent:r.emitEvent})}),this.updateValueAndValidity(r))}reset(e={},r={}){this._forEachChild((t,i)=>{t.reset(e?e[i]:null,{onlySelf:!0,emitEvent:r.emitEvent})}),this._updatePristine(r),this._updateTouched(r),this.updateValueAndValidity(r)}getRawValue(){return this._reduceChildren({},(e,r,t)=>(e[t]=r.getRawValue(),e))}_syncPendingControls(){let e=this._reduceChildren(!1,(r,t)=>t._syncPendingControls()?!0:r);return e&&this.updateValueAndValidity({onlySelf:!0}),e}_forEachChild(e){Object.keys(this.controls).forEach(r=>{let t=this.controls[r];t&&e(t,r)})}_setUpControls(){this._forEachChild(e=>{e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(e){for(let[r,t]of Object.entries(this.controls))if(this.contains(r)&&e(t))return!0;return!1}_reduceValue(){let e={};return this._reduceChildren(e,(r,t,i)=>((t.enabled||this.disabled)&&(r[i]=t.value),r))}_reduceChildren(e,r){let t=e;return this._forEachChild((i,s)=>{t=r(t,i,s)}),t}_allControlsDisabled(){for(let e of Object.keys(this.controls))if(this.controls[e].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(e){return this.controls.hasOwnProperty(e)?this.controls[e]:null}};var Us=class extends Qt{};var Jt=new T("CallSetDisabledState",{providedIn:"root",factory:()=>ur}),ur="always";function dr(n,e){return[...e.path,n]}function Bn(n,e,r=ur){Gs(n,e),e.valueAccessor.writeValue(n.value),(n.disabled||r==="always")&&e.valueAccessor.setDisabledState?.(n.disabled),fm(n,e),mm(n,e),pm(n,e),hm(n,e)}function or(n,e,r=!0){let t=()=>{};e.valueAccessor&&(e.valueAccessor.registerOnChange(t),e.valueAccessor.registerOnTouched(t)),cr(n,e),n&&(e._invokeOnDestroyCallbacks(),n._registerOnCollectionChange(()=>{}))}function ar(n,e){n.forEach(r=>{r.registerOnValidatorChange&&r.registerOnValidatorChange(e)})}function hm(n,e){if(e.valueAccessor.setDisabledState){let r=t=>{e.valueAccessor.setDisabledState(t)};n.registerOnDisabledChange(r),e._registerOnDestroy(()=>{n._unregisterOnDisabledChange(r)})}}function Gs(n,e){let r=Pl(n);e.validator!==null?n.setValidators(ml(r,e.validator)):typeof r=="function"&&n.setValidators([r]);let t=Nl(n);e.asyncValidator!==null?n.setAsyncValidators(ml(t,e.asyncValidator)):typeof t=="function"&&n.setAsyncValidators([t]);let i=()=>n.updateValueAndValidity();ar(e._rawValidators,i),ar(e._rawAsyncValidators,i)}function cr(n,e){let r=!1;if(n!==null){if(e.validator!==null){let i=Pl(n);if(Array.isArray(i)&&i.length>0){let s=i.filter(c=>c!==e.validator);s.length!==i.length&&(r=!0,n.setValidators(s))}}if(e.asyncValidator!==null){let i=Nl(n);if(Array.isArray(i)&&i.length>0){let s=i.filter(c=>c!==e.asyncValidator);s.length!==i.length&&(r=!0,n.setAsyncValidators(s))}}}let t=()=>{};return ar(e._rawValidators,t),ar(e._rawAsyncValidators,t),r}function fm(n,e){e.valueAccessor.registerOnChange(r=>{n._pendingValue=r,n._pendingChange=!0,n._pendingDirty=!0,n.updateOn==="change"&&Vl(n,e)})}function pm(n,e){e.valueAccessor.registerOnTouched(()=>{n._pendingTouched=!0,n.updateOn==="blur"&&n._pendingChange&&Vl(n,e),n.updateOn!=="submit"&&n.markAsTouched()})}function Vl(n,e){n._pendingDirty&&n.markAsDirty(),n.setValue(n._pendingValue,{emitModelToViewChange:!1}),e.viewToModelUpdate(n._pendingValue),n._pendingChange=!1}function mm(n,e){let r=(t,i)=>{e.valueAccessor.writeValue(t),i&&e.viewToModelUpdate(t)};n.registerOnChange(r),e._registerOnDestroy(()=>{n._unregisterOnChange(r)})}function jl(n,e){n==null,Gs(n,e)}function gm(n,e){return cr(n,e)}function qs(n,e){if(!n.hasOwnProperty("model"))return!1;let r=n.model;return r.isFirstChange()?!0:!Object.is(e,r.currentValue)}function vm(n){return Object.getPrototypeOf(n.constructor)===El}function Ul(n,e){n._syncPendingControls(),e.forEach(r=>{let t=r.control;t.updateOn==="submit"&&t._pendingChange&&(r.viewToModelUpdate(t._pendingValue),t._pendingChange=!1)})}function Ks(n,e){if(!e)return null;Array.isArray(e);let r,t,i;return e.forEach(s=>{s.constructor===Al?r=s:vm(s)?t=s:i=s}),i||t||r||null}function ym(n,e){let r=n.indexOf(e);r>-1&&n.splice(r,1)}var wm={provide:le,useExisting:Se(()=>bm)},jn=Promise.resolve(),bm=(()=>{let e=class e extends le{constructor(t,i,s){super(),this.callSetDisabledState=s,this.submitted=!1,this._directives=new Set,this.ngSubmit=new Z,this.form=new Qt({},$s(t),zs(i))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(t){jn.then(()=>{let i=this._findContainer(t.path);t.control=i.registerControl(t.name,t.control),Bn(t.control,t,this.callSetDisabledState),t.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(t)})}getControl(t){return this.form.get(t.path)}removeControl(t){jn.then(()=>{let i=this._findContainer(t.path);i&&i.removeControl(t.name),this._directives.delete(t)})}addFormGroup(t){jn.then(()=>{let i=this._findContainer(t.path),s=new Qt({});jl(s,t),i.registerControl(t.name,s),s.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(t){jn.then(()=>{let i=this._findContainer(t.path);i&&i.removeControl(t.name)})}getFormGroup(t){return this.form.get(t.path)}updateModel(t,i){jn.then(()=>{this.form.get(t.path).setValue(i)})}setValue(t){this.control.setValue(t)}onSubmit(t){return this.submitted=!0,Ul(this.form,this._directives),this.ngSubmit.emit(t),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0){this.form.reset(t),this.submitted=!1}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(t){return t.pop(),t.length?this.form.get(t):this.form}};e.\u0275fac=function(i){return new(i||e)(C(rt,10),C(At,10),C(Jt,8))},e.\u0275dir=V({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(i,s){i&1&&Be("submit",function(d){return s.onSubmit(d)})("reset",function(){return s.onReset()})},inputs:{options:[F.None,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[be([wm]),ce]});let n=e;return n})();function yl(n,e){let r=n.indexOf(e);r>-1&&n.splice(r,1)}function wl(n){return typeof n=="object"&&n!==null&&Object.keys(n).length===2&&"value"in n&&"disabled"in n}var Un=class extends Xt{constructor(e=null,r,t){super(Hs(r),Ws(t,r)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(e),this._setUpdateStrategy(r),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),lr(r)&&(r.nonNullable||r.initialValueIsDefault)&&(wl(e)?this.defaultValue=e.value:this.defaultValue=e)}setValue(e,r={}){this.value=this._pendingValue=e,this._onChange.length&&r.emitModelToViewChange!==!1&&this._onChange.forEach(t=>t(this.value,r.emitViewToModelChange!==!1)),this.updateValueAndValidity(r)}patchValue(e,r={}){this.setValue(e,r)}reset(e=this.defaultValue,r={}){this._applyFormState(e),this.markAsPristine(r),this.markAsUntouched(r),this.setValue(this.value,r),this._pendingChange=!1}_updateValue(){}_anyControls(e){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(e){this._onChange.push(e)}_unregisterOnChange(e){yl(this._onChange,e)}registerOnDisabledChange(e){this._onDisabledChange.push(e)}_unregisterOnDisabledChange(e){yl(this._onDisabledChange,e)}_forEachChild(e){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(e){wl(e)?(this.value=this._pendingValue=e.value,e.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=e}};var _m=n=>n instanceof Un,Cm=(()=>{let e=class e extends le{ngOnInit(){this._checkParentType(),this.formDirective.addFormGroup(this)}ngOnDestroy(){this.formDirective&&this.formDirective.removeFormGroup(this)}get control(){return this.formDirective.getFormGroup(this)}get path(){return dr(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}};e.\u0275fac=(()=>{let t;return function(s){return(t||(t=Qe(e)))(s||e)}})(),e.\u0275dir=V({type:e,features:[ce]});let n=e;return n})();var Em={provide:ze,useExisting:Se(()=>Am)},bl=Promise.resolve(),Am=(()=>{let e=class e extends ze{constructor(t,i,s,c,d,p){super(),this._changeDetectorRef=d,this.callSetDisabledState=p,this.control=new Un,this._registered=!1,this.name="",this.update=new Z,this._parent=t,this._setValidators(i),this._setAsyncValidators(s),this.valueAccessor=Ks(this,c)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){let i=t.name.previousValue;this.formDirective.removeControl({name:i,path:this._getPath(i)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),qs(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){Bn(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(t){bl.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){let i=t.isDisabled.currentValue,s=i!==0&&_e(i);bl.then(()=>{s&&!this.control.disabled?this.control.disable():!s&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?dr(t,this._parent):[t]}};e.\u0275fac=function(i){return new(i||e)(C(le,9),C(rt,10),C(At,10),C($n,10),C(tt,8),C(Jt,8))},e.\u0275dir=V({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[F.None,"disabled","isDisabled"],model:[F.None,"ngModel","model"],options:[F.None,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[be([Em]),ce,Ie]});let n=e;return n})(),sb=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275dir=V({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]});let n=e;return n})(),Im={provide:$n,useExisting:Se(()=>Dm),multi:!0},Dm=(()=>{let e=class e extends El{writeValue(t){let i=t??"";this.setProperty("value",i)}registerOnChange(t){this.onChange=i=>{t(i==""?null:parseFloat(i))}}};e.\u0275fac=(()=>{let t;return function(s){return(t||(t=Qe(e)))(s||e)}})(),e.\u0275dir=V({type:e,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(i,s){i&1&&Be("input",function(d){return s.onChange(d.target.value)})("blur",function(){return s.onTouched()})},features:[be([Im]),ce]});let n=e;return n})();var Zs=new T(""),Tm={provide:ze,useExisting:Se(()=>Mm)},Mm=(()=>{let e=class e extends ze{set isDisabled(t){}constructor(t,i,s,c,d){super(),this._ngModelWarningConfig=c,this.callSetDisabledState=d,this.update=new Z,this._ngModelWarningSent=!1,this._setValidators(t),this._setAsyncValidators(i),this.valueAccessor=Ks(this,s)}ngOnChanges(t){if(this._isControlChanged(t)){let i=t.form.previousValue;i&&or(i,this,!1),Bn(this.form,this,this.callSetDisabledState),this.form.updateValueAndValidity({emitEvent:!1})}qs(t,this.viewModel)&&(this.form.setValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.form&&or(this.form,this,!1)}get path(){return[]}get control(){return this.form}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_isControlChanged(t){return t.hasOwnProperty("form")}};e._ngModelWarningSentOnce=!1,e.\u0275fac=function(i){return new(i||e)(C(rt,10),C(At,10),C($n,10),C(Zs,8),C(Jt,8))},e.\u0275dir=V({type:e,selectors:[["","formControl",""]],inputs:{form:[F.None,"formControl","form"],isDisabled:[F.None,"disabled","isDisabled"],model:[F.None,"ngModel","model"]},outputs:{update:"ngModelChange"},exportAs:["ngForm"],features:[be([Tm]),ce,Ie]});let n=e;return n})(),Sm={provide:le,useExisting:Se(()=>Bl)},Bl=(()=>{let e=class e extends le{constructor(t,i,s){super(),this.callSetDisabledState=s,this.submitted=!1,this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new Z,this._setValidators(t),this._setAsyncValidators(i)}ngOnChanges(t){this._checkFormPresent(),t.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(cr(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(t){let i=this.form.get(t.path);return Bn(i,t,this.callSetDisabledState),i.updateValueAndValidity({emitEvent:!1}),this.directives.push(t),i}getControl(t){return this.form.get(t.path)}removeControl(t){or(t.control||null,t,!1),ym(this.directives,t)}addFormGroup(t){this._setUpFormContainer(t)}removeFormGroup(t){this._cleanUpFormContainer(t)}getFormGroup(t){return this.form.get(t.path)}addFormArray(t){this._setUpFormContainer(t)}removeFormArray(t){this._cleanUpFormContainer(t)}getFormArray(t){return this.form.get(t.path)}updateModel(t,i){this.form.get(t.path).setValue(i)}onSubmit(t){return this.submitted=!0,Ul(this.form,this.directives),this.ngSubmit.emit(t),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0){this.form.reset(t),this.submitted=!1}_updateDomValue(){this.directives.forEach(t=>{let i=t.control,s=this.form.get(t.path);i!==s&&(or(i||null,t),_m(s)&&(Bn(s,t,this.callSetDisabledState),t.control=s))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(t){let i=this.form.get(t.path);jl(i,t),i.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(t){if(this.form){let i=this.form.get(t.path);i&&gm(i,t)&&i.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){Gs(this.form,this),this._oldForm&&cr(this._oldForm,this)}_checkFormPresent(){this.form}};e.\u0275fac=function(i){return new(i||e)(C(rt,10),C(At,10),C(Jt,8))},e.\u0275dir=V({type:e,selectors:[["","formGroup",""]],hostBindings:function(i,s){i&1&&Be("submit",function(d){return s.onSubmit(d)})("reset",function(){return s.onReset()})},inputs:{form:[F.None,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[be([Sm]),ce,Ie]});let n=e;return n})(),Rm={provide:le,useExisting:Se(()=>$l)},$l=(()=>{let e=class e extends Cm{constructor(t,i,s){super(),this.name=null,this._parent=t,this._setValidators(i),this._setAsyncValidators(s)}_checkParentType(){Hl(this._parent)}};e.\u0275fac=function(i){return new(i||e)(C(le,13),C(rt,10),C(At,10))},e.\u0275dir=V({type:e,selectors:[["","formGroupName",""]],inputs:{name:[F.None,"formGroupName","name"]},features:[be([Rm]),ce]});let n=e;return n})(),xm={provide:le,useExisting:Se(()=>zl)},zl=(()=>{let e=class e extends le{constructor(t,i,s){super(),this.name=null,this._parent=t,this._setValidators(i),this._setAsyncValidators(s)}ngOnInit(){this._checkParentType(),this.formDirective.addFormArray(this)}ngOnDestroy(){this.formDirective&&this.formDirective.removeFormArray(this)}get control(){return this.formDirective.getFormArray(this)}get formDirective(){return this._parent?this._parent.formDirective:null}get path(){return dr(this.name==null?this.name:this.name.toString(),this._parent)}_checkParentType(){Hl(this._parent)}};e.\u0275fac=function(i){return new(i||e)(C(le,13),C(rt,10),C(At,10))},e.\u0275dir=V({type:e,selectors:[["","formArrayName",""]],inputs:{name:[F.None,"formArrayName","name"]},features:[be([xm]),ce]});let n=e;return n})();function Hl(n){return!(n instanceof $l)&&!(n instanceof Bl)&&!(n instanceof zl)}var km={provide:ze,useExisting:Se(()=>Om)},Om=(()=>{let e=class e extends ze{set isDisabled(t){}constructor(t,i,s,c,d){super(),this._ngModelWarningConfig=d,this._added=!1,this.name=null,this.update=new Z,this._ngModelWarningSent=!1,this._parent=t,this._setValidators(i),this._setAsyncValidators(s),this.valueAccessor=Ks(this,c)}ngOnChanges(t){this._added||this._setUpControl(),qs(t,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}get path(){return dr(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}_setUpControl(){this._checkParentType(),this.control=this.formDirective.addControl(this),this._added=!0}};e._ngModelWarningSentOnce=!1,e.\u0275fac=function(i){return new(i||e)(C(le,13),C(rt,10),C(At,10),C($n,10),C(Zs,8))},e.\u0275dir=V({type:e,selectors:[["","formControlName",""]],inputs:{name:[F.None,"formControlName","name"],isDisabled:[F.None,"disabled","isDisabled"],model:[F.None,"ngModel","model"]},outputs:{update:"ngModelChange"},features:[be([km]),ce,Ie]});let n=e;return n})();function Pm(n){return typeof n=="number"?n:parseInt(n,10)}var Nm=(()=>{let e=class e{constructor(){this._validator=nr}ngOnChanges(t){if(this.inputName in t){let i=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(i),this._validator=this._enabled?this.createValidator(i):nr,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}};e.\u0275fac=function(i){return new(i||e)},e.\u0275dir=V({type:e,features:[Ie]});let n=e;return n})();var Fm={provide:rt,useExisting:Se(()=>Lm),multi:!0},Lm=(()=>{let e=class e extends Nm{constructor(){super(...arguments),this.inputName="maxlength",this.normalizeInput=t=>Pm(t),this.createValidator=t=>Dl(t)}};e.\u0275fac=(()=>{let t;return function(s){return(t||(t=Qe(e)))(s||e)}})(),e.\u0275dir=V({type:e,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(i,s){i&2&&_t("maxlength",s._enabled?s.maxlength:null)},inputs:{maxlength:"maxlength"},features:[be([Fm]),ce]});let n=e;return n})();var Wl=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=q({type:e}),e.\u0275inj=G({});let n=e;return n})(),Bs=class extends Xt{constructor(e,r,t){super(Hs(r),Ws(t,r)),this.controls=e,this._initObservables(),this._setUpdateStrategy(r),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}at(e){return this.controls[this._adjustIndex(e)]}push(e,r={}){this.controls.push(e),this._registerControl(e),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}insert(e,r,t={}){this.controls.splice(e,0,r),this._registerControl(r),this.updateValueAndValidity({emitEvent:t.emitEvent})}removeAt(e,r={}){let t=this._adjustIndex(e);t<0&&(t=0),this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),this.controls.splice(t,1),this.updateValueAndValidity({emitEvent:r.emitEvent})}setControl(e,r,t={}){let i=this._adjustIndex(e);i<0&&(i=0),this.controls[i]&&this.controls[i]._registerOnCollectionChange(()=>{}),this.controls.splice(i,1),r&&(this.controls.splice(i,0,r),this._registerControl(r)),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(e,r={}){Ll(this,!1,e),e.forEach((t,i)=>{Fl(this,!1,i),this.at(i).setValue(t,{onlySelf:!0,emitEvent:r.emitEvent})}),this.updateValueAndValidity(r)}patchValue(e,r={}){e!=null&&(e.forEach((t,i)=>{this.at(i)&&this.at(i).patchValue(t,{onlySelf:!0,emitEvent:r.emitEvent})}),this.updateValueAndValidity(r))}reset(e=[],r={}){this._forEachChild((t,i)=>{t.reset(e[i],{onlySelf:!0,emitEvent:r.emitEvent})}),this._updatePristine(r),this._updateTouched(r),this.updateValueAndValidity(r)}getRawValue(){return this.controls.map(e=>e.getRawValue())}clear(e={}){this.controls.length<1||(this._forEachChild(r=>r._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:e.emitEvent}))}_adjustIndex(e){return e<0?e+this.length:e}_syncPendingControls(){let e=this.controls.reduce((r,t)=>t._syncPendingControls()?!0:r,!1);return e&&this.updateValueAndValidity({onlySelf:!0}),e}_forEachChild(e){this.controls.forEach((r,t)=>{e(r,t)})}_updateValue(){this.value=this.controls.filter(e=>e.enabled||this.disabled).map(e=>e.value)}_anyControls(e){return this.controls.some(r=>r.enabled&&e(r))}_setUpControls(){this._forEachChild(e=>this._registerControl(e))}_allControlsDisabled(){for(let e of this.controls)if(e.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(e){e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange)}_find(e){return this.at(e)??null}};function _l(n){return!!n&&(n.asyncValidators!==void 0||n.validators!==void 0||n.updateOn!==void 0)}var ob=(()=>{let e=class e{constructor(){this.useNonNullable=!1}get nonNullable(){let t=new e;return t.useNonNullable=!0,t}group(t,i=null){let s=this._reduceControls(t),c={};return _l(i)?c=i:i!==null&&(c.validators=i.validator,c.asyncValidators=i.asyncValidator),new Qt(s,c)}record(t,i=null){let s=this._reduceControls(t);return new Us(s,i)}control(t,i,s){let c={};return this.useNonNullable?(_l(i)?c=i:(c.validators=i,c.asyncValidators=s),new Un(t,W(y({},c),{nonNullable:!0}))):new Un(t,i,s)}array(t,i,s){let c=t.map(d=>this._createControl(d));return new Bs(c,i,s)}_reduceControls(t){let i={};return Object.keys(t).forEach(s=>{i[s]=this._createControl(t[s])}),i}_createControl(t){if(t instanceof Un)return t;if(t instanceof Xt)return t;if(Array.isArray(t)){let i=t[0],s=t.length>1?t[1]:null,c=t.length>2?t[2]:null;return this.control(i,s,c)}else return this.control(t)}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();var ab=(()=>{let e=class e{static withConfig(t){return{ngModule:e,providers:[{provide:Jt,useValue:t.callSetDisabledState??ur}]}}};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=q({type:e}),e.\u0275inj=G({imports:[Wl]});let n=e;return n})(),cb=(()=>{let e=class e{static withConfig(t){return{ngModule:e,providers:[{provide:Zs,useValue:t.warnOnNgModelWithFormControl??"always"},{provide:Jt,useValue:t.callSetDisabledState??ur}]}}};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=q({type:e}),e.\u0275inj=G({imports:[Wl]});let n=e;return n})();var Xs;try{Xs=typeof Intl<"u"&&Intl.v8BreakIterator}catch{Xs=!1}var fe=(()=>{let e=class e{constructor(t){this._platformId=t,this.isBrowser=this._platformId?Kc(this._platformId):typeof document=="object"&&!!document,this.EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent),this.TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent),this.BLINK=this.isBrowser&&!!(window.chrome||Xs)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT,this.WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT,this.IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window),this.FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent),this.ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT,this.SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT}};e.\u0275fac=function(i){return new(i||e)(w(Ue))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();var en,Gl=["color","button","checkbox","date","datetime-local","email","file","hidden","image","month","number","password","radio","range","reset","search","submit","tel","text","time","url","week"];function gb(){if(en)return en;if(typeof document!="object"||!document)return en=new Set(Gl),en;let n=document.createElement("input");return en=new Set(Gl.filter(e=>(n.setAttribute("type",e),n.type===e))),en}var zn;function Vm(){if(zn==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>zn=!0}))}finally{zn=zn||!1}return zn}function tn(n){return Vm()?n:!!n.capture}var Hn=function(n){return n[n.NORMAL=0]="NORMAL",n[n.NEGATED=1]="NEGATED",n[n.INVERTED=2]="INVERTED",n}(Hn||{}),hr,It;function vb(){if(It==null){if(typeof document!="object"||!document||typeof Element!="function"||!Element)return It=!1,It;if("scrollBehavior"in document.documentElement.style)It=!0;else{let n=Element.prototype.scrollTo;n?It=!/\{\s*\[native code\]\s*\}/.test(n.toString()):It=!1}}return It}function yb(){if(typeof document!="object"||!document)return Hn.NORMAL;if(hr==null){let n=document.createElement("div"),e=n.style;n.dir="rtl",e.width="1px",e.overflow="auto",e.visibility="hidden",e.pointerEvents="none",e.position="absolute";let r=document.createElement("div"),t=r.style;t.width="2px",t.height="1px",n.appendChild(r),document.body.appendChild(n),hr=Hn.NORMAL,n.scrollLeft===0&&(n.scrollLeft=1,hr=n.scrollLeft===0?Hn.NEGATED:Hn.INVERTED),n.remove()}return hr}var Ys;function jm(){if(Ys==null){let n=typeof document<"u"?document.head:null;Ys=!!(n&&(n.createShadowRoot||n.attachShadow))}return Ys}function ql(n){if(jm()){let e=n.getRootNode?n.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&e instanceof ShadowRoot)return e}return null}function Kl(){let n=typeof document<"u"&&document?document.activeElement:null;for(;n&&n.shadowRoot;){let e=n.shadowRoot.activeElement;if(e===n)break;n=e}return n}function st(n){return n.composedPath?n.composedPath()[0]:n.target}function Zl(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}function fr(n,...e){return e.length?e.some(r=>n[r]):n.altKey||n.shiftKey||n.ctrlKey||n.metaKey}function Um(n){return n!=null&&`${n}`!="false"}function Js(n,e=0){return Bm(n)?Number(n):e}function Bm(n){return!isNaN(parseFloat(n))&&!isNaN(Number(n))}function eo(n){return Array.isArray(n)?n:[n]}function _b(n){return n==null?"":typeof n=="string"?n:`${n}px`}function He(n){return n instanceof re?n.nativeElement:n}function Cb(n,e=/\s+/){let r=[];if(n!=null){let t=Array.isArray(n)?n:`${n}`.split(e);for(let i of t){let s=`${i}`.trim();s&&r.push(s)}}return r}var Yl=(()=>{let e=class e{create(t){return typeof MutationObserver>"u"?null:new MutationObserver(t)}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),$m=(()=>{let e=class e{constructor(t){this._mutationObserverFactory=t,this._observedElements=new Map}ngOnDestroy(){this._observedElements.forEach((t,i)=>this._cleanupObserver(i))}observe(t){let i=He(t);return new vt(s=>{let d=this._observeElement(i).subscribe(s);return()=>{d.unsubscribe(),this._unobserveElement(i)}})}_observeElement(t){if(this._observedElements.has(t))this._observedElements.get(t).count++;else{let i=new J,s=this._mutationObserverFactory.create(c=>i.next(c));s&&s.observe(t,{characterData:!0,childList:!0,subtree:!0}),this._observedElements.set(t,{observer:s,stream:i,count:1})}return this._observedElements.get(t).stream}_unobserveElement(t){this._observedElements.has(t)&&(this._observedElements.get(t).count--,this._observedElements.get(t).count||this._cleanupObserver(t))}_cleanupObserver(t){if(this._observedElements.has(t)){let{observer:i,stream:s}=this._observedElements.get(t);i&&i.disconnect(),s.complete(),this._observedElements.delete(t)}}};e.\u0275fac=function(i){return new(i||e)(w(Yl))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),Ob=(()=>{let e=class e{get disabled(){return this._disabled}set disabled(t){this._disabled=t,this._disabled?this._unsubscribe():this._subscribe()}get debounce(){return this._debounce}set debounce(t){this._debounce=Js(t),this._subscribe()}constructor(t,i,s){this._contentObserver=t,this._elementRef=i,this._ngZone=s,this.event=new Z,this._disabled=!1,this._currentSubscription=null}ngAfterContentInit(){!this._currentSubscription&&!this.disabled&&this._subscribe()}ngOnDestroy(){this._unsubscribe()}_subscribe(){this._unsubscribe();let t=this._contentObserver.observe(this._elementRef);this._ngZone.runOutsideAngular(()=>{this._currentSubscription=(this.debounce?t.pipe(Ut(this.debounce)):t).subscribe(this.event)})}_unsubscribe(){this._currentSubscription?.unsubscribe()}};e.\u0275fac=function(i){return new(i||e)(C($m),C(re),C($))},e.\u0275dir=V({type:e,selectors:[["","cdkObserveContent",""]],inputs:{disabled:[F.HasDecoratorInputTransform,"cdkObserveContentDisabled","disabled",_e],debounce:"debounce"},outputs:{event:"cdkObserveContent"},exportAs:["cdkObserveContent"],standalone:!0,features:[Je]});let n=e;return n})(),Xl=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=q({type:e}),e.\u0275inj=G({providers:[Yl]});let n=e;return n})();var Ql=new Set,Dt,zm=(()=>{let e=class e{constructor(t,i){this._platform=t,this._nonce=i,this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):Wm}matchMedia(t){return(this._platform.WEBKIT||this._platform.BLINK)&&Hm(t,this._nonce),this._matchMedia(t)}};e.\u0275fac=function(i){return new(i||e)(w(fe),w(In,8))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();function Hm(n,e){if(!Ql.has(n))try{Dt||(Dt=document.createElement("style"),e&&(Dt.nonce=e),Dt.setAttribute("type","text/css"),document.head.appendChild(Dt)),Dt.sheet&&(Dt.sheet.insertRule(`@media ${n} {body{ }}`,0),Ql.add(n))}catch(r){console.error(r)}}function Wm(n){return{matches:n==="all"||n==="",media:n,addListener:()=>{},removeListener:()=>{}}}var eu=(()=>{let e=class e{constructor(t,i){this._mediaMatcher=t,this._zone=i,this._queries=new Map,this._destroySubject=new J}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(t){return Jl(eo(t)).some(s=>this._registerQuery(s).mql.matches)}observe(t){let s=Jl(eo(t)).map(d=>this._registerQuery(d).observable),c=Vt(s);return c=Di(c.pipe(Pe(1)),c.pipe(Ti(1),Ut(0))),c.pipe(k(d=>{let p={matches:!1,breakpoints:{}};return d.forEach(({matches:f,query:v})=>{p.matches=p.matches||f,p.breakpoints[v]=f}),p}))}_registerQuery(t){if(this._queries.has(t))return this._queries.get(t);let i=this._mediaMatcher.matchMedia(t),c={observable:new vt(d=>{let p=f=>this._zone.run(()=>d.next(f));return i.addListener(p),()=>{i.removeListener(p)}}).pipe(Mi(i),k(({matches:d})=>({query:t,matches:d})),Bt(this._destroySubject)),mql:i};return this._queries.set(t,c),c}};e.\u0275fac=function(i){return new(i||e)(w(zm),w($))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();function Jl(n){return n.map(e=>e.split(",")).reduce((e,r)=>e.concat(r)).map(e=>e.trim())}var au=" ";function ug(n,e,r){let t=vr(n,e);r=r.trim(),!t.some(i=>i.trim()===r)&&(t.push(r),n.setAttribute(e,t.join(au)))}function dg(n,e,r){let t=vr(n,e);r=r.trim();let i=t.filter(s=>s!==r);i.length?n.setAttribute(e,i.join(au)):n.removeAttribute(e)}function vr(n,e){return n.getAttribute(e)?.match(/\S+/g)??[]}var cu="cdk-describedby-message",pr="cdk-describedby-host",io=0,o0=(()=>{let e=class e{constructor(t,i){this._platform=i,this._messageRegistry=new Map,this._messagesContainer=null,this._id=`${io++}`,this._document=t,this._id=_(zt)+"-"+io++}describe(t,i,s){if(!this._canBeDescribed(t,i))return;let c=to(i,s);typeof i!="string"?(tu(i,this._id),this._messageRegistry.set(c,{messageElement:i,referenceCount:0})):this._messageRegistry.has(c)||this._createMessageElement(i,s),this._isElementDescribedByMessage(t,c)||this._addMessageReference(t,c)}removeDescription(t,i,s){if(!i||!this._isElementNode(t))return;let c=to(i,s);if(this._isElementDescribedByMessage(t,c)&&this._removeMessageReference(t,c),typeof i=="string"){let d=this._messageRegistry.get(c);d&&d.referenceCount===0&&this._deleteMessageElement(c)}this._messagesContainer?.childNodes.length===0&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){let t=this._document.querySelectorAll(`[${pr}="${this._id}"]`);for(let i=0;i<t.length;i++)this._removeCdkDescribedByReferenceIds(t[i]),t[i].removeAttribute(pr);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(t,i){let s=this._document.createElement("div");tu(s,this._id),s.textContent=t,i&&s.setAttribute("role",i),this._createMessagesContainer(),this._messagesContainer.appendChild(s),this._messageRegistry.set(to(t,i),{messageElement:s,referenceCount:0})}_deleteMessageElement(t){this._messageRegistry.get(t)?.messageElement?.remove(),this._messageRegistry.delete(t)}_createMessagesContainer(){if(this._messagesContainer)return;let t="cdk-describedby-message-container",i=this._document.querySelectorAll(`.${t}[platform="server"]`);for(let c=0;c<i.length;c++)i[c].remove();let s=this._document.createElement("div");s.style.visibility="hidden",s.classList.add(t),s.classList.add("cdk-visually-hidden"),this._platform&&!this._platform.isBrowser&&s.setAttribute("platform","server"),this._document.body.appendChild(s),this._messagesContainer=s}_removeCdkDescribedByReferenceIds(t){let i=vr(t,"aria-describedby").filter(s=>s.indexOf(cu)!=0);t.setAttribute("aria-describedby",i.join(" "))}_addMessageReference(t,i){let s=this._messageRegistry.get(i);ug(t,"aria-describedby",s.messageElement.id),t.setAttribute(pr,this._id),s.referenceCount++}_removeMessageReference(t,i){let s=this._messageRegistry.get(i);s.referenceCount--,dg(t,"aria-describedby",s.messageElement.id),t.removeAttribute(pr)}_isElementDescribedByMessage(t,i){let s=vr(t,"aria-describedby"),c=this._messageRegistry.get(i),d=c&&c.messageElement.id;return!!d&&s.indexOf(d)!=-1}_canBeDescribed(t,i){if(!this._isElementNode(t))return!1;if(i&&typeof i=="object")return!0;let s=i==null?"":`${i}`.trim(),c=t.getAttribute("aria-label");return s?!c||c.trim()!==s:!1}_isElementNode(t){return t.nodeType===this._document.ELEMENT_NODE}};e.\u0275fac=function(i){return new(i||e)(w(z),w(fe))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();function to(n,e){return typeof n=="string"?`${e||""}/${n}`:n}function tu(n,e){n.id||(n.id=`${cu}-${e}-${io++}`)}var yr=class{constructor(e){this._items=e,this._activeItemIndex=-1,this._activeItem=null,this._wrap=!1,this._letterKeyStream=new J,this._typeaheadSubscription=Ii.EMPTY,this._vertical=!0,this._allowedModifierKeys=[],this._homeAndEnd=!1,this._pageUpAndDown={enabled:!1,delta:10},this._skipPredicateFn=r=>r.disabled,this._pressedLetters=[],this.tabOut=new J,this.change=new J,e instanceof gs&&(this._itemChangesSubscription=e.changes.subscribe(r=>{if(this._activeItem){let i=r.toArray().indexOf(this._activeItem);i>-1&&i!==this._activeItemIndex&&(this._activeItemIndex=i)}}))}skipPredicate(e){return this._skipPredicateFn=e,this}withWrap(e=!0){return this._wrap=e,this}withVerticalOrientation(e=!0){return this._vertical=e,this}withHorizontalOrientation(e){return this._horizontal=e,this}withAllowedModifierKeys(e){return this._allowedModifierKeys=e,this}withTypeAhead(e=200){return this._typeaheadSubscription.unsubscribe(),this._typeaheadSubscription=this._letterKeyStream.pipe(te(r=>this._pressedLetters.push(r)),Ut(e),Me(()=>this._pressedLetters.length>0),k(()=>this._pressedLetters.join(""))).subscribe(r=>{let t=this._getItemsArray();for(let i=1;i<t.length+1;i++){let s=(this._activeItemIndex+i)%t.length,c=t[s];if(!this._skipPredicateFn(c)&&c.getLabel().toUpperCase().trim().indexOf(r)===0){this.setActiveItem(s);break}}this._pressedLetters=[]}),this}cancelTypeahead(){return this._pressedLetters=[],this}withHomeAndEnd(e=!0){return this._homeAndEnd=e,this}withPageUpDown(e=!0,r=10){return this._pageUpAndDown={enabled:e,delta:r},this}setActiveItem(e){let r=this._activeItem;this.updateActiveItem(e),this._activeItem!==r&&this.change.next(this._activeItemIndex)}onKeydown(e){let r=e.keyCode,i=["altKey","ctrlKey","metaKey","shiftKey"].every(s=>!e[s]||this._allowedModifierKeys.indexOf(s)>-1);switch(r){case 9:this.tabOut.next();return;case 40:if(this._vertical&&i){this.setNextItemActive();break}else return;case 38:if(this._vertical&&i){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&i){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&i){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&i){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&i){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&i){let s=this._activeItemIndex-this._pageUpAndDown.delta;this._setActiveItemByIndex(s>0?s:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&i){let s=this._activeItemIndex+this._pageUpAndDown.delta,c=this._getItemsArray().length;this._setActiveItemByIndex(s<c?s:c-1,-1);break}else return;default:(i||fr(e,"shiftKey"))&&(e.key&&e.key.length===1?this._letterKeyStream.next(e.key.toLocaleUpperCase()):(r>=65&&r<=90||r>=48&&r<=57)&&this._letterKeyStream.next(String.fromCharCode(r)));return}this._pressedLetters=[],e.preventDefault()}get activeItemIndex(){return this._activeItemIndex}get activeItem(){return this._activeItem}isTyping(){return this._pressedLetters.length>0}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._items.length-1,-1)}setNextItemActive(){this._activeItemIndex<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(e){let r=this._getItemsArray(),t=typeof e=="number"?e:r.indexOf(e),i=r[t];this._activeItem=i??null,this._activeItemIndex=t}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._letterKeyStream.complete(),this.tabOut.complete(),this.change.complete(),this._pressedLetters=[]}_setActiveItemByDelta(e){this._wrap?this._setActiveInWrapMode(e):this._setActiveInDefaultMode(e)}_setActiveInWrapMode(e){let r=this._getItemsArray();for(let t=1;t<=r.length;t++){let i=(this._activeItemIndex+e*t+r.length)%r.length,s=r[i];if(!this._skipPredicateFn(s)){this.setActiveItem(i);return}}}_setActiveInDefaultMode(e){this._setActiveItemByIndex(this._activeItemIndex+e,e)}_setActiveItemByIndex(e,r){let t=this._getItemsArray();if(t[e]){for(;this._skipPredicateFn(t[e]);)if(e+=r,!t[e])return;this.setActiveItem(e)}}_getItemsArray(){return this._items instanceof gs?this._items.toArray():this._items}},nu=class extends yr{setActiveItem(e){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(e),this.activeItem&&this.activeItem.setActiveStyles()}},iu=class extends yr{constructor(){super(...arguments),this._origin="program"}setFocusOrigin(e){return this._origin=e,this}setActiveItem(e){super.setActiveItem(e),this.activeItem&&this.activeItem.focus(this._origin)}};var hg=(()=>{let e=class e{constructor(t){this._platform=t}isDisabled(t){return t.hasAttribute("disabled")}isVisible(t){return pg(t)&&getComputedStyle(t).visibility==="visible"}isTabbable(t){if(!this._platform.isBrowser)return!1;let i=fg(Cg(t));if(i&&(ru(i)===-1||!this.isVisible(i)))return!1;let s=t.nodeName.toLowerCase(),c=ru(t);return t.hasAttribute("contenteditable")?c!==-1:s==="iframe"||s==="object"||this._platform.WEBKIT&&this._platform.IOS&&!bg(t)?!1:s==="audio"?t.hasAttribute("controls")?c!==-1:!1:s==="video"?c===-1?!1:c!==null?!0:this._platform.FIREFOX||t.hasAttribute("controls"):t.tabIndex>=0}isFocusable(t,i){return _g(t)&&!this.isDisabled(t)&&(i?.ignoreVisibility||this.isVisible(t))}};e.\u0275fac=function(i){return new(i||e)(w(fe))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();function fg(n){try{return n.frameElement}catch{return null}}function pg(n){return!!(n.offsetWidth||n.offsetHeight||typeof n.getClientRects=="function"&&n.getClientRects().length)}function mg(n){let e=n.nodeName.toLowerCase();return e==="input"||e==="select"||e==="button"||e==="textarea"}function gg(n){return yg(n)&&n.type=="hidden"}function vg(n){return wg(n)&&n.hasAttribute("href")}function yg(n){return n.nodeName.toLowerCase()=="input"}function wg(n){return n.nodeName.toLowerCase()=="a"}function lu(n){if(!n.hasAttribute("tabindex")||n.tabIndex===void 0)return!1;let e=n.getAttribute("tabindex");return!!(e&&!isNaN(parseInt(e,10)))}function ru(n){if(!lu(n))return null;let e=parseInt(n.getAttribute("tabindex")||"",10);return isNaN(e)?-1:e}function bg(n){let e=n.nodeName.toLowerCase(),r=e==="input"&&n.type;return r==="text"||r==="password"||e==="select"||e==="textarea"}function _g(n){return gg(n)?!1:mg(n)||vg(n)||n.hasAttribute("contenteditable")||lu(n)}function Cg(n){return n.ownerDocument&&n.ownerDocument.defaultView||window}var ro=class{get enabled(){return this._enabled}set enabled(e){this._enabled=e,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(e,this._startAnchor),this._toggleAnchorTabIndex(e,this._endAnchor))}constructor(e,r,t,i,s=!1){this._element=e,this._checker=r,this._ngZone=t,this._document=i,this._hasAttached=!1,this.startAnchorListener=()=>this.focusLastTabbableElement(),this.endAnchorListener=()=>this.focusFirstTabbableElement(),this._enabled=!0,s||this.attachAnchors()}destroy(){let e=this._startAnchor,r=this._endAnchor;e&&(e.removeEventListener("focus",this.startAnchorListener),e.remove()),r&&(r.removeEventListener("focus",this.endAnchorListener),r.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return this._hasAttached?!0:(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(e){return new Promise(r=>{this._executeOnStable(()=>r(this.focusInitialElement(e)))})}focusFirstTabbableElementWhenReady(e){return new Promise(r=>{this._executeOnStable(()=>r(this.focusFirstTabbableElement(e)))})}focusLastTabbableElementWhenReady(e){return new Promise(r=>{this._executeOnStable(()=>r(this.focusLastTabbableElement(e)))})}_getRegionBoundary(e){let r=this._element.querySelectorAll(`[cdk-focus-region-${e}], [cdkFocusRegion${e}], [cdk-focus-${e}]`);return e=="start"?r.length?r[0]:this._getFirstTabbableElement(this._element):r.length?r[r.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(e){let r=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(r){if(!this._checker.isFocusable(r)){let t=this._getFirstTabbableElement(r);return t?.focus(e),!!t}return r.focus(e),!0}return this.focusFirstTabbableElement(e)}focusFirstTabbableElement(e){let r=this._getRegionBoundary("start");return r&&r.focus(e),!!r}focusLastTabbableElement(e){let r=this._getRegionBoundary("end");return r&&r.focus(e),!!r}hasAttached(){return this._hasAttached}_getFirstTabbableElement(e){if(this._checker.isFocusable(e)&&this._checker.isTabbable(e))return e;let r=e.children;for(let t=0;t<r.length;t++){let i=r[t].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(r[t]):null;if(i)return i}return null}_getLastTabbableElement(e){if(this._checker.isFocusable(e)&&this._checker.isTabbable(e))return e;let r=e.children;for(let t=r.length-1;t>=0;t--){let i=r[t].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(r[t]):null;if(i)return i}return null}_createAnchor(){let e=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,e),e.classList.add("cdk-visually-hidden"),e.classList.add("cdk-focus-trap-anchor"),e.setAttribute("aria-hidden","true"),e}_toggleAnchorTabIndex(e,r){e?r.setAttribute("tabindex","0"):r.removeAttribute("tabindex")}toggleAnchors(e){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(e,this._startAnchor),this._toggleAnchorTabIndex(e,this._endAnchor))}_executeOnStable(e){this._ngZone.isStable?e():this._ngZone.onStable.pipe(Pe(1)).subscribe(e)}},Eg=(()=>{let e=class e{constructor(t,i,s){this._checker=t,this._ngZone=i,this._document=s}create(t,i=!1){return new ro(t,this._checker,this._ngZone,this._document,i)}};e.\u0275fac=function(i){return new(i||e)(w(hg),w($),w(z))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),a0=(()=>{let e=class e{get enabled(){return this.focusTrap?.enabled||!1}set enabled(t){this.focusTrap&&(this.focusTrap.enabled=t)}constructor(t,i,s){this._elementRef=t,this._focusTrapFactory=i,this._previouslyFocusedElement=null,_(fe).isBrowser&&(this.focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement,!0))}ngOnDestroy(){this.focusTrap?.destroy(),this._previouslyFocusedElement&&(this._previouslyFocusedElement.focus(),this._previouslyFocusedElement=null)}ngAfterContentInit(){this.focusTrap?.attachAnchors(),this.autoCapture&&this._captureFocus()}ngDoCheck(){this.focusTrap&&!this.focusTrap.hasAttached()&&this.focusTrap.attachAnchors()}ngOnChanges(t){let i=t.autoCapture;i&&!i.firstChange&&this.autoCapture&&this.focusTrap?.hasAttached()&&this._captureFocus()}_captureFocus(){this._previouslyFocusedElement=Kl(),this.focusTrap?.focusInitialElementWhenReady()}};e.\u0275fac=function(i){return new(i||e)(C(re),C(Eg),C(z))},e.\u0275dir=V({type:e,selectors:[["","cdkTrapFocus",""]],inputs:{enabled:[F.HasDecoratorInputTransform,"cdkTrapFocus","enabled",_e],autoCapture:[F.HasDecoratorInputTransform,"cdkTrapFocusAutoCapture","autoCapture",_e]},exportAs:["cdkTrapFocus"],standalone:!0,features:[Je,Ie]});let n=e;return n})();function so(n){return n.buttons===0||n.detail===0}function oo(n){let e=n.touches&&n.touches[0]||n.changedTouches&&n.changedTouches[0];return!!e&&e.identifier===-1&&(e.radiusX==null||e.radiusX===1)&&(e.radiusY==null||e.radiusY===1)}var Ag=new T("cdk-input-modality-detector-options"),Ig={ignoreKeys:[18,17,224,91,16]},uu=650,nn=tn({passive:!0,capture:!0}),Dg=(()=>{let e=class e{get mostRecentModality(){return this._modality.value}constructor(t,i,s,c){this._platform=t,this._mostRecentTarget=null,this._modality=new he(null),this._lastTouchMs=0,this._onKeydown=d=>{this._options?.ignoreKeys?.some(p=>p===d.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=st(d))},this._onMousedown=d=>{Date.now()-this._lastTouchMs<uu||(this._modality.next(so(d)?"keyboard":"mouse"),this._mostRecentTarget=st(d))},this._onTouchstart=d=>{if(oo(d)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=st(d)},this._options=y(y({},Ig),c),this.modalityDetected=this._modality.pipe(Ti(1)),this.modalityChanged=this.modalityDetected.pipe(cc()),t.isBrowser&&i.runOutsideAngular(()=>{s.addEventListener("keydown",this._onKeydown,nn),s.addEventListener("mousedown",this._onMousedown,nn),s.addEventListener("touchstart",this._onTouchstart,nn)})}ngOnDestroy(){this._modality.complete(),this._platform.isBrowser&&(document.removeEventListener("keydown",this._onKeydown,nn),document.removeEventListener("mousedown",this._onMousedown,nn),document.removeEventListener("touchstart",this._onTouchstart,nn))}};e.\u0275fac=function(i){return new(i||e)(w(fe),w($),w(z),w(Ag,8))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),Tg=new T("liveAnnouncerElement",{providedIn:"root",factory:Mg});function Mg(){return null}var Sg=new T("LIVE_ANNOUNCER_DEFAULT_OPTIONS"),Rg=0,c0=(()=>{let e=class e{constructor(t,i,s,c){this._ngZone=i,this._defaultOptions=c,this._document=s,this._liveElement=t||this._createLiveElement()}announce(t,...i){let s=this._defaultOptions,c,d;return i.length===1&&typeof i[0]=="number"?d=i[0]:[c,d]=i,this.clear(),clearTimeout(this._previousTimeout),c||(c=s&&s.politeness?s.politeness:"polite"),d==null&&s&&(d=s.duration),this._liveElement.setAttribute("aria-live",c),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(p=>this._currentResolve=p)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=t,typeof d=="number"&&(this._previousTimeout=setTimeout(()=>this.clear(),d)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){let t="cdk-live-announcer-element",i=this._document.getElementsByClassName(t),s=this._document.createElement("div");for(let c=0;c<i.length;c++)i[c].remove();return s.classList.add(t),s.classList.add("cdk-visually-hidden"),s.setAttribute("aria-atomic","true"),s.setAttribute("aria-live","polite"),s.id=`cdk-live-announcer-${Rg++}`,this._document.body.appendChild(s),s}_exposeAnnouncerToModals(t){let i=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let s=0;s<i.length;s++){let c=i[s],d=c.getAttribute("aria-owns");d?d.indexOf(t)===-1&&c.setAttribute("aria-owns",d+" "+t):c.setAttribute("aria-owns",t)}}};e.\u0275fac=function(i){return new(i||e)(w(Tg,8),w($),w(z),w(Sg,8))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();var gr=function(n){return n[n.IMMEDIATE=0]="IMMEDIATE",n[n.EVENTUAL=1]="EVENTUAL",n}(gr||{}),xg=new T("cdk-focus-monitor-default-options"),mr=tn({passive:!0,capture:!0}),kg=(()=>{let e=class e{constructor(t,i,s,c,d){this._ngZone=t,this._platform=i,this._inputModalityDetector=s,this._origin=null,this._windowFocused=!1,this._originFromTouchInteraction=!1,this._elementInfo=new Map,this._monitoredElementCount=0,this._rootNodeFocusListenerCount=new Map,this._windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=window.setTimeout(()=>this._windowFocused=!1)},this._stopInputModalityDetector=new J,this._rootNodeFocusAndBlurListener=p=>{let f=st(p);for(let v=f;v;v=v.parentElement)p.type==="focus"?this._onFocus(p,v):this._onBlur(p,v)},this._document=c,this._detectionMode=d?.detectionMode||gr.IMMEDIATE}monitor(t,i=!1){let s=He(t);if(!this._platform.isBrowser||s.nodeType!==1)return D();let c=ql(s)||this._getDocument(),d=this._elementInfo.get(s);if(d)return i&&(d.checkChildren=!0),d.subject;let p={checkChildren:i,subject:new J,rootNode:c};return this._elementInfo.set(s,p),this._registerGlobalListeners(p),p.subject}stopMonitoring(t){let i=He(t),s=this._elementInfo.get(i);s&&(s.subject.complete(),this._setClasses(i),this._elementInfo.delete(i),this._removeGlobalListeners(s))}focusVia(t,i,s){let c=He(t),d=this._getDocument().activeElement;c===d?this._getClosestElementsInfo(c).forEach(([p,f])=>this._originChanged(p,i,f)):(this._setOrigin(i),typeof c.focus=="function"&&c.focus(s))}ngOnDestroy(){this._elementInfo.forEach((t,i)=>this.stopMonitoring(i))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(t){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(t)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:t&&this._isLastInteractionFromInputLabel(t)?"mouse":"program"}_shouldBeAttributedToTouch(t){return this._detectionMode===gr.EVENTUAL||!!t?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(t,i){t.classList.toggle("cdk-focused",!!i),t.classList.toggle("cdk-touch-focused",i==="touch"),t.classList.toggle("cdk-keyboard-focused",i==="keyboard"),t.classList.toggle("cdk-mouse-focused",i==="mouse"),t.classList.toggle("cdk-program-focused",i==="program")}_setOrigin(t,i=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=t,this._originFromTouchInteraction=t==="touch"&&i,this._detectionMode===gr.IMMEDIATE){clearTimeout(this._originTimeoutId);let s=this._originFromTouchInteraction?uu:1;this._originTimeoutId=setTimeout(()=>this._origin=null,s)}})}_onFocus(t,i){let s=this._elementInfo.get(i),c=st(t);!s||!s.checkChildren&&i!==c||this._originChanged(i,this._getFocusOrigin(c),s)}_onBlur(t,i){let s=this._elementInfo.get(i);!s||s.checkChildren&&t.relatedTarget instanceof Node&&i.contains(t.relatedTarget)||(this._setClasses(i),this._emitOrigin(s,null))}_emitOrigin(t,i){t.subject.observers.length&&this._ngZone.run(()=>t.subject.next(i))}_registerGlobalListeners(t){if(!this._platform.isBrowser)return;let i=t.rootNode,s=this._rootNodeFocusListenerCount.get(i)||0;s||this._ngZone.runOutsideAngular(()=>{i.addEventListener("focus",this._rootNodeFocusAndBlurListener,mr),i.addEventListener("blur",this._rootNodeFocusAndBlurListener,mr)}),this._rootNodeFocusListenerCount.set(i,s+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(Bt(this._stopInputModalityDetector)).subscribe(c=>{this._setOrigin(c,!0)}))}_removeGlobalListeners(t){let i=t.rootNode;if(this._rootNodeFocusListenerCount.has(i)){let s=this._rootNodeFocusListenerCount.get(i);s>1?this._rootNodeFocusListenerCount.set(i,s-1):(i.removeEventListener("focus",this._rootNodeFocusAndBlurListener,mr),i.removeEventListener("blur",this._rootNodeFocusAndBlurListener,mr),this._rootNodeFocusListenerCount.delete(i))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(t,i,s){this._setClasses(t,i),this._emitOrigin(s,i),this._lastFocusOrigin=i}_getClosestElementsInfo(t){let i=[];return this._elementInfo.forEach((s,c)=>{(c===t||s.checkChildren&&c.contains(t))&&i.push([c,s])}),i}_isLastInteractionFromInputLabel(t){let{_mostRecentTarget:i,mostRecentModality:s}=this._inputModalityDetector;if(s!=="mouse"||!i||i===t||t.nodeName!=="INPUT"&&t.nodeName!=="TEXTAREA"||t.disabled)return!1;let c=t.labels;if(c){for(let d=0;d<c.length;d++)if(c[d].contains(i))return!0}return!1}};e.\u0275fac=function(i){return new(i||e)(w($),w(fe),w(Dg),w(z,8),w(xg,8))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),l0=(()=>{let e=class e{constructor(t,i){this._elementRef=t,this._focusMonitor=i,this._focusOrigin=null,this.cdkFocusChange=new Z}get focusOrigin(){return this._focusOrigin}ngAfterViewInit(){let t=this._elementRef.nativeElement;this._monitorSubscription=this._focusMonitor.monitor(t,t.nodeType===1&&t.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(i=>{this._focusOrigin=i,this.cdkFocusChange.emit(i)})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._monitorSubscription&&this._monitorSubscription.unsubscribe()}};e.\u0275fac=function(i){return new(i||e)(C(re),C(kg))},e.\u0275dir=V({type:e,selectors:[["","cdkMonitorElementFocus",""],["","cdkMonitorSubtreeFocus",""]],outputs:{cdkFocusChange:"cdkFocusChange"},exportAs:["cdkMonitorFocus"],standalone:!0});let n=e;return n})(),Tt=function(n){return n[n.NONE=0]="NONE",n[n.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",n[n.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",n}(Tt||{}),su="cdk-high-contrast-black-on-white",ou="cdk-high-contrast-white-on-black",no="cdk-high-contrast-active",ao=(()=>{let e=class e{constructor(t,i){this._platform=t,this._document=i,this._breakpointSubscription=_(eu).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return Tt.NONE;let t=this._document.createElement("div");t.style.backgroundColor="rgb(1,2,3)",t.style.position="absolute",this._document.body.appendChild(t);let i=this._document.defaultView||window,s=i&&i.getComputedStyle?i.getComputedStyle(t):null,c=(s&&s.backgroundColor||"").replace(/ /g,"");switch(t.remove(),c){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return Tt.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return Tt.BLACK_ON_WHITE}return Tt.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let t=this._document.body.classList;t.remove(no,su,ou),this._hasCheckedHighContrastMode=!0;let i=this.getHighContrastMode();i===Tt.BLACK_ON_WHITE?t.add(no,su):i===Tt.WHITE_ON_BLACK&&t.add(no,ou)}}};e.\u0275fac=function(i){return new(i||e)(w(fe),w(z))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),u0=(()=>{let e=class e{constructor(t){t._applyBodyHighContrastModeCssClasses()}};e.\u0275fac=function(i){return new(i||e)(w(ao))},e.\u0275mod=q({type:e}),e.\u0275inj=G({imports:[Xl]});let n=e;return n})();var Pg=new T("cdk-dir-doc",{providedIn:"root",factory:Ng});function Ng(){return _(z)}var Fg=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function Lg(n){let e=n?.toLowerCase()||"";return e==="auto"&&typeof navigator<"u"&&navigator?.language?Fg.test(navigator.language)?"rtl":"ltr":e==="rtl"?"rtl":"ltr"}var _0=(()=>{let e=class e{constructor(t){if(this.value="ltr",this.change=new Z,t){let i=t.body?t.body.dir:null,s=t.documentElement?t.documentElement.dir:null;this.value=Lg(i||s||"ltr")}}ngOnDestroy(){this.change.complete()}};e.\u0275fac=function(i){return new(i||e)(w(Pg,8))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();var co=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=q({type:e}),e.\u0275inj=G({});let n=e;return n})();var Ug=["text"],Bg=[[["mat-icon"]],"*"],$g=["mat-icon","*"];function zg(n,e){if(n&1&&qt(0,"mat-pseudo-checkbox",1),n&2){let r=Pi();ki("disabled",r.disabled)("state",r.selected?"checked":"unchecked")}}function Hg(n,e){if(n&1&&qt(0,"mat-pseudo-checkbox",3),n&2){let r=Pi();ki("disabled",r.disabled)}}function Wg(n,e){if(n&1&&(ws(0,"span",4),Oc(1),bs()),n&2){let r=Pi();Tn(),Pc("(",r.group.label,")")}}var Gg=["mat-internal-form-field",""],qg=["*"];var $0=(()=>{let e=class e{};e.STANDARD_CURVE="cubic-bezier(0.4,0.0,0.2,1)",e.DECELERATION_CURVE="cubic-bezier(0.0,0.0,0.2,1)",e.ACCELERATION_CURVE="cubic-bezier(0.4,0.0,1,1)",e.SHARP_CURVE="cubic-bezier(0.4,0.0,0.6,1)";let n=e;return n})(),z0=(()=>{let e=class e{};e.COMPLEX="375ms",e.ENTERING="225ms",e.EXITING="195ms";let n=e;return n})();function Kg(){return!0}var Zg=new T("mat-sanity-checks",{providedIn:"root",factory:Kg}),br=(()=>{let e=class e{constructor(t,i,s){this._sanityChecks=i,this._document=s,this._hasDoneGlobalChecks=!1,t._applyBodyHighContrastModeCssClasses(),this._hasDoneGlobalChecks||(this._hasDoneGlobalChecks=!0)}_checkIsEnabled(t){return Zl()?!1:typeof this._sanityChecks=="boolean"?this._sanityChecks:!!this._sanityChecks[t]}};e.\u0275fac=function(i){return new(i||e)(w(ao),w(Zg,8),w(z))},e.\u0275mod=q({type:e}),e.\u0275inj=G({imports:[co,co]});let n=e;return n})();var du=class{constructor(e,r,t,i,s){this._defaultMatcher=e,this.ngControl=r,this._parentFormGroup=t,this._parentForm=i,this._stateChanges=s,this.errorState=!1}updateErrorState(){let e=this.errorState,r=this._parentFormGroup||this._parentForm,t=this.matcher||this._defaultMatcher,i=this.ngControl?this.ngControl.control:null,s=t?.isErrorState(i,r)??!1;s!==e&&(this.errorState=s,this._stateChanges.next())}};var hu=new T("MAT_DATE_LOCALE",{providedIn:"root",factory:Yg});function Yg(){return _(Fc)}var _r=class{constructor(){this._localeChanges=new J,this.localeChanges=this._localeChanges}getValidDateOrNull(e){return this.isDateInstance(e)&&this.isValid(e)?e:null}deserialize(e){return e==null||this.isDateInstance(e)&&this.isValid(e)?e:this.invalid()}setLocale(e){this.locale=e,this._localeChanges.next()}compareDate(e,r){return this.getYear(e)-this.getYear(r)||this.getMonth(e)-this.getMonth(r)||this.getDate(e)-this.getDate(r)}sameDate(e,r){if(e&&r){let t=this.isValid(e),i=this.isValid(r);return t&&i?!this.compareDate(e,r):t==i}return e==r}clampDate(e,r,t){return r&&this.compareDate(e,r)<0?r:t&&this.compareDate(e,t)>0?t:e}},Xg=new T("mat-date-formats"),Qg=/^\d{4}-\d{2}-\d{2}(?:T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|(?:(?:\+|-)\d{2}:\d{2}))?)?$/;function lo(n,e){let r=Array(n);for(let t=0;t<n;t++)r[t]=e(t);return r}var Jg=(()=>{let e=class e extends _r{constructor(t){super(),this.useUtcForDisplay=!1,this._matDateLocale=_(hu,{optional:!0}),t!==void 0&&(this._matDateLocale=t),super.setLocale(this._matDateLocale)}getYear(t){return t.getFullYear()}getMonth(t){return t.getMonth()}getDate(t){return t.getDate()}getDayOfWeek(t){return t.getDay()}getMonthNames(t){let i=new Intl.DateTimeFormat(this.locale,{month:t,timeZone:"utc"});return lo(12,s=>this._format(i,new Date(2017,s,1)))}getDateNames(){let t=new Intl.DateTimeFormat(this.locale,{day:"numeric",timeZone:"utc"});return lo(31,i=>this._format(t,new Date(2017,0,i+1)))}getDayOfWeekNames(t){let i=new Intl.DateTimeFormat(this.locale,{weekday:t,timeZone:"utc"});return lo(7,s=>this._format(i,new Date(2017,0,s+1)))}getYearName(t){let i=new Intl.DateTimeFormat(this.locale,{year:"numeric",timeZone:"utc"});return this._format(i,t)}getFirstDayOfWeek(){return 0}getNumDaysInMonth(t){return this.getDate(this._createDateWithOverflow(this.getYear(t),this.getMonth(t)+1,0))}clone(t){return new Date(t.getTime())}createDate(t,i,s){let c=this._createDateWithOverflow(t,i,s);return c.getMonth()!=i,c}today(){return new Date}parse(t,i){return typeof t=="number"?new Date(t):t?new Date(Date.parse(t)):null}format(t,i){if(!this.isValid(t))throw Error("NativeDateAdapter: Cannot format invalid date.");let s=new Intl.DateTimeFormat(this.locale,W(y({},i),{timeZone:"utc"}));return this._format(s,t)}addCalendarYears(t,i){return this.addCalendarMonths(t,i*12)}addCalendarMonths(t,i){let s=this._createDateWithOverflow(this.getYear(t),this.getMonth(t)+i,this.getDate(t));return this.getMonth(s)!=((this.getMonth(t)+i)%12+12)%12&&(s=this._createDateWithOverflow(this.getYear(s),this.getMonth(s),0)),s}addCalendarDays(t,i){return this._createDateWithOverflow(this.getYear(t),this.getMonth(t),this.getDate(t)+i)}toIso8601(t){return[t.getUTCFullYear(),this._2digit(t.getUTCMonth()+1),this._2digit(t.getUTCDate())].join("-")}deserialize(t){if(typeof t=="string"){if(!t)return null;if(Qg.test(t)){let i=new Date(t);if(this.isValid(i))return i}}return super.deserialize(t)}isDateInstance(t){return t instanceof Date}isValid(t){return!isNaN(t.getTime())}invalid(){return new Date(NaN)}_createDateWithOverflow(t,i,s){let c=new Date;return c.setFullYear(t,i,s),c.setHours(0,0,0,0),c}_2digit(t){return("00"+t).slice(-2)}_format(t,i){let s=new Date;return s.setUTCFullYear(i.getFullYear(),i.getMonth(),i.getDate()),s.setUTCHours(i.getHours(),i.getMinutes(),i.getSeconds(),i.getMilliseconds()),t.format(s)}};e.\u0275fac=function(i){return new(i||e)(w(hu,8))},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})(),ev={parse:{dateInput:null},display:{dateInput:{year:"numeric",month:"numeric",day:"numeric"},monthYearLabel:{year:"numeric",month:"short"},dateA11yLabel:{year:"numeric",month:"long",day:"numeric"},monthYearA11yLabel:{year:"numeric",month:"long"}}};function H0(n=ev){return[{provide:_r,useClass:Jg},{provide:Xg,useValue:n}]}var W0=(()=>{let e=class e{isErrorState(t,i){return!!(t&&t.invalid&&(t.touched||i&&i.submitted))}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();var De=function(n){return n[n.FADING_IN=0]="FADING_IN",n[n.VISIBLE=1]="VISIBLE",n[n.FADING_OUT=2]="FADING_OUT",n[n.HIDDEN=3]="HIDDEN",n}(De||{}),fo=class{constructor(e,r,t,i=!1){this._renderer=e,this.element=r,this.config=t,this._animationForciblyDisabledThroughCss=i,this.state=De.HIDDEN}fadeOut(){this._renderer.fadeOutRipple(this)}},fu=tn({passive:!0,capture:!0}),po=class{constructor(){this._events=new Map,this._delegateEventHandler=e=>{let r=st(e);r&&this._events.get(e.type)?.forEach((t,i)=>{(i===r||i.contains(r))&&t.forEach(s=>s.handleEvent(e))})}}addHandler(e,r,t,i){let s=this._events.get(r);if(s){let c=s.get(t);c?c.add(i):s.set(t,new Set([i]))}else this._events.set(r,new Map([[t,new Set([i])]])),e.runOutsideAngular(()=>{document.addEventListener(r,this._delegateEventHandler,fu)})}removeHandler(e,r,t){let i=this._events.get(e);if(!i)return;let s=i.get(r);s&&(s.delete(t),s.size===0&&i.delete(r),i.size===0&&(this._events.delete(e),document.removeEventListener(e,this._delegateEventHandler,fu)))}},pu={enterDuration:225,exitDuration:150},tv=800,mu=tn({passive:!0,capture:!0}),gu=["mousedown","touchstart"],vu=["mouseup","mouseleave","touchend","touchcancel"],Wn=class Wn{constructor(e,r,t,i){this._target=e,this._ngZone=r,this._platform=i,this._isPointerDown=!1,this._activeRipples=new Map,this._pointerUpEventsRegistered=!1,i.isBrowser&&(this._containerElement=He(t))}fadeInRipple(e,r,t={}){let i=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),s=y(y({},pu),t.animation);t.centered&&(e=i.left+i.width/2,r=i.top+i.height/2);let c=t.radius||nv(e,r,i),d=e-i.left,p=r-i.top,f=s.enterDuration,v=document.createElement("div");v.classList.add("mat-ripple-element"),v.style.left=`${d-c}px`,v.style.top=`${p-c}px`,v.style.height=`${c*2}px`,v.style.width=`${c*2}px`,t.color!=null&&(v.style.backgroundColor=t.color),v.style.transitionDuration=`${f}ms`,this._containerElement.appendChild(v);let I=window.getComputedStyle(v),E=I.transitionProperty,O=I.transitionDuration,ee=E==="none"||O==="0s"||O==="0s, 0s"||i.width===0&&i.height===0,Y=new fo(this,v,t,ee);v.style.transform="scale3d(1, 1, 1)",Y.state=De.FADING_IN,t.persistent||(this._mostRecentTransientRipple=Y);let L=null;return!ee&&(f||s.exitDuration)&&this._ngZone.runOutsideAngular(()=>{let P=()=>this._finishRippleTransition(Y),pe=()=>this._destroyRipple(Y);v.addEventListener("transitionend",P),v.addEventListener("transitioncancel",pe),L={onTransitionEnd:P,onTransitionCancel:pe}}),this._activeRipples.set(Y,L),(ee||!f)&&this._finishRippleTransition(Y),Y}fadeOutRipple(e){if(e.state===De.FADING_OUT||e.state===De.HIDDEN)return;let r=e.element,t=y(y({},pu),e.config.animation);r.style.transitionDuration=`${t.exitDuration}ms`,r.style.opacity="0",e.state=De.FADING_OUT,(e._animationForciblyDisabledThroughCss||!t.exitDuration)&&this._finishRippleTransition(e)}fadeOutAll(){this._getActiveRipples().forEach(e=>e.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(e=>{e.config.persistent||e.fadeOut()})}setupTriggerEvents(e){let r=He(e);!this._platform.isBrowser||!r||r===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=r,gu.forEach(t=>{Wn._eventManager.addHandler(this._ngZone,t,r,this)}))}handleEvent(e){e.type==="mousedown"?this._onMousedown(e):e.type==="touchstart"?this._onTouchStart(e):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{vu.forEach(r=>{this._triggerElement.addEventListener(r,this,mu)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(e){e.state===De.FADING_IN?this._startFadeOutTransition(e):e.state===De.FADING_OUT&&this._destroyRipple(e)}_startFadeOutTransition(e){let r=e===this._mostRecentTransientRipple,{persistent:t}=e.config;e.state=De.VISIBLE,!t&&(!r||!this._isPointerDown)&&e.fadeOut()}_destroyRipple(e){let r=this._activeRipples.get(e)??null;this._activeRipples.delete(e),this._activeRipples.size||(this._containerRect=null),e===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),e.state=De.HIDDEN,r!==null&&(e.element.removeEventListener("transitionend",r.onTransitionEnd),e.element.removeEventListener("transitioncancel",r.onTransitionCancel)),e.element.remove()}_onMousedown(e){let r=so(e),t=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+tv;!this._target.rippleDisabled&&!r&&!t&&(this._isPointerDown=!0,this.fadeInRipple(e.clientX,e.clientY,this._target.rippleConfig))}_onTouchStart(e){if(!this._target.rippleDisabled&&!oo(e)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;let r=e.changedTouches;if(r)for(let t=0;t<r.length;t++)this.fadeInRipple(r[t].clientX,r[t].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(e=>{let r=e.state===De.VISIBLE||e.config.terminateOnPointerUp&&e.state===De.FADING_IN;!e.config.persistent&&r&&e.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){let e=this._triggerElement;e&&(gu.forEach(r=>Wn._eventManager.removeHandler(r,e,this)),this._pointerUpEventsRegistered&&vu.forEach(r=>e.removeEventListener(r,this,mu)))}};Wn._eventManager=new po;var mo=Wn;function nv(n,e,r){let t=Math.max(Math.abs(n-r.left),Math.abs(n-r.right)),i=Math.max(Math.abs(e-r.top),Math.abs(e-r.bottom));return Math.sqrt(t*t+i*i)}var _u=new T("mat-ripple-global-options"),Cu=(()=>{let e=class e{get disabled(){return this._disabled}set disabled(t){t&&this.fadeOutAllNonPersistent(),this._disabled=t,this._setupTriggerEventsIfEnabled()}get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(t){this._trigger=t,this._setupTriggerEventsIfEnabled()}constructor(t,i,s,c,d){this._elementRef=t,this._animationMode=d,this.radius=0,this._disabled=!1,this._isInitialized=!1,this._globalOptions=c||{},this._rippleRenderer=new mo(this,i,t,s)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:y(y(y({},this._globalOptions.animation),this._animationMode==="NoopAnimations"?{enterDuration:0,exitDuration:0}:{}),this.animation),terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(t,i=0,s){return typeof t=="number"?this._rippleRenderer.fadeInRipple(t,i,y(y({},this.rippleConfig),s)):this._rippleRenderer.fadeInRipple(0,0,y(y({},this.rippleConfig),t))}};e.\u0275fac=function(i){return new(i||e)(C(re),C($),C(fe),C(_u,8),C(xi,8))},e.\u0275dir=V({type:e,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(i,s){i&2&&et("mat-ripple-unbounded",s.unbounded)},inputs:{color:[F.None,"matRippleColor","color"],unbounded:[F.None,"matRippleUnbounded","unbounded"],centered:[F.None,"matRippleCentered","centered"],radius:[F.None,"matRippleRadius","radius"],animation:[F.None,"matRippleAnimation","animation"],disabled:[F.None,"matRippleDisabled","disabled"],trigger:[F.None,"matRippleTrigger","trigger"]},exportAs:["matRipple"],standalone:!0});let n=e;return n})(),iv=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=q({type:e}),e.\u0275inj=G({imports:[br,br]});let n=e;return n})(),rv=(()=>{let e=class e{constructor(t){this._animationMode=t,this.state="unchecked",this.disabled=!1,this.appearance="full"}};e.\u0275fac=function(i){return new(i||e)(C(xi,8))},e.\u0275cmp=$t({type:e,selectors:[["mat-pseudo-checkbox"]],hostAttrs:[1,"mat-pseudo-checkbox"],hostVars:12,hostBindings:function(i,s){i&2&&et("mat-pseudo-checkbox-indeterminate",s.state==="indeterminate")("mat-pseudo-checkbox-checked",s.state==="checked")("mat-pseudo-checkbox-disabled",s.disabled)("mat-pseudo-checkbox-minimal",s.appearance==="minimal")("mat-pseudo-checkbox-full",s.appearance==="full")("_mat-animation-noopable",s._animationMode==="NoopAnimations")},inputs:{state:"state",disabled:"disabled",appearance:"appearance"},standalone:!0,features:[Kt],decls:0,vars:0,template:function(i,s){},styles:['.mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:"";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color);border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color);border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}'],encapsulation:2,changeDetection:0});let n=e;return n})(),sv=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=q({type:e}),e.\u0275inj=G({imports:[br]});let n=e;return n})(),ov=new T("MAT_OPTION_PARENT_COMPONENT");var av=new T("MatOptgroup");var cv=0,go=class{constructor(e,r=!1){this.source=e,this.isUserInput=r}},G0=(()=>{let e=class e{get multiple(){return this._parent&&this._parent.multiple}get selected(){return this._selected}get disabled(){return this.group&&this.group.disabled||this._disabled}set disabled(t){this._disabled=t}get disableRipple(){return!!(this._parent&&this._parent.disableRipple)}get hideSingleSelectionIndicator(){return!!(this._parent&&this._parent.hideSingleSelectionIndicator)}constructor(t,i,s,c){this._element=t,this._changeDetectorRef=i,this._parent=s,this.group=c,this._selected=!1,this._active=!1,this._disabled=!1,this._mostRecentViewValue="",this.id=`mat-option-${cv++}`,this.onSelectionChange=new Z,this._stateChanges=new J}get active(){return this._active}get viewValue(){return(this._text?.nativeElement.textContent||"").trim()}select(t=!0){this._selected||(this._selected=!0,this._changeDetectorRef.markForCheck(),t&&this._emitSelectionChangeEvent())}deselect(t=!0){this._selected&&(this._selected=!1,this._changeDetectorRef.markForCheck(),t&&this._emitSelectionChangeEvent())}focus(t,i){let s=this._getHostElement();typeof s.focus=="function"&&s.focus(i)}setActiveStyles(){this._active||(this._active=!0,this._changeDetectorRef.markForCheck())}setInactiveStyles(){this._active&&(this._active=!1,this._changeDetectorRef.markForCheck())}getLabel(){return this.viewValue}_handleKeydown(t){(t.keyCode===13||t.keyCode===32)&&!fr(t)&&(this._selectViaInteraction(),t.preventDefault())}_selectViaInteraction(){this.disabled||(this._selected=this.multiple?!this._selected:!0,this._changeDetectorRef.markForCheck(),this._emitSelectionChangeEvent(!0))}_getTabIndex(){return this.disabled?"-1":"0"}_getHostElement(){return this._element.nativeElement}ngAfterViewChecked(){if(this._selected){let t=this.viewValue;t!==this._mostRecentViewValue&&(this._mostRecentViewValue&&this._stateChanges.next(),this._mostRecentViewValue=t)}}ngOnDestroy(){this._stateChanges.complete()}_emitSelectionChangeEvent(t=!1){this.onSelectionChange.emit(new go(this,t))}};e.\u0275fac=function(i){return new(i||e)(C(re),C(tt),C(ov,8),C(av,8))},e.\u0275cmp=$t({type:e,selectors:[["mat-option"]],viewQuery:function(i,s){if(i&1&&kc(Ug,7),i&2){let c;Fi(c=Li())&&(s._text=c.first)}},hostAttrs:["role","option",1,"mat-mdc-option","mdc-list-item"],hostVars:11,hostBindings:function(i,s){i&1&&Be("click",function(){return s._selectViaInteraction()})("keydown",function(d){return s._handleKeydown(d)}),i&2&&(Rc("id",s.id),_t("aria-selected",s.selected)("aria-disabled",s.disabled.toString()),et("mdc-list-item--selected",s.selected)("mat-mdc-option-multiple",s.multiple)("mat-mdc-option-active",s.active)("mdc-list-item--disabled",s.disabled))},inputs:{value:"value",id:"id",disabled:[F.HasDecoratorInputTransform,"disabled","disabled",_e]},outputs:{onSelectionChange:"onSelectionChange"},exportAs:["matOption"],standalone:!0,features:[Je,Kt],ngContentSelectors:$g,decls:8,vars:5,consts:[["text",""],["aria-hidden","true",1,"mat-mdc-option-pseudo-checkbox",3,"disabled","state"],[1,"mdc-list-item__primary-text"],["state","checked","aria-hidden","true","appearance","minimal",1,"mat-mdc-option-pseudo-checkbox",3,"disabled"],[1,"cdk-visually-hidden"],["aria-hidden","true","mat-ripple","",1,"mat-mdc-option-ripple","mat-mdc-focus-indicator",3,"matRippleTrigger","matRippleDisabled"]],template:function(i,s){i&1&&(_s(Bg),ys(0,zg,1,2,"mat-pseudo-checkbox",1),Ni(1),ws(2,"span",2,0),Ni(4,1),bs(),ys(5,Hg,1,1,"mat-pseudo-checkbox",3)(6,Wg,2,1,"span",4),qt(7,"div",5)),i&2&&(Oi(0,s.multiple?0:-1),Tn(5),Oi(5,!s.multiple&&s.selected&&!s.hideSingleSelectionIndicator?5:-1),Tn(),Oi(6,s.group&&s.group._inert?6:-1),Tn(),ki("matRippleTrigger",s._getHostElement())("matRippleDisabled",s.disabled||s.disableRipple))},dependencies:[rv,Cu],styles:['.mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:"";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:""}'],encapsulation:2,changeDetection:0});let n=e;return n})();function q0(n,e,r){if(r.length){let t=e.toArray(),i=r.toArray(),s=0;for(let c=0;c<n+1;c++)t[c].group&&t[c].group===i[s]&&s++;return s}return 0}function K0(n,e,r,t){return n<r?n:n+e>r+t?Math.max(0,n-t+e):r}var Z0=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=q({type:e}),e.\u0275inj=G({imports:[iv,br,sv]});let n=e;return n})(),yu={capture:!0},wu=["focus","click","mouseenter","touchstart"],uo="mat-ripple-loader-uninitialized",ho="mat-ripple-loader-class-name",bu="mat-ripple-loader-centered",wr="mat-ripple-loader-disabled",Y0=(()=>{let e=class e{constructor(){this._document=_(z,{optional:!0}),this._animationMode=_(xi,{optional:!0}),this._globalRippleOptions=_(_u,{optional:!0}),this._platform=_(fe),this._ngZone=_($),this._hosts=new Map,this._onInteraction=t=>{if(!(t.target instanceof HTMLElement))return;let s=t.target.closest(`[${uo}]`);s&&this._createRipple(s)},this._ngZone.runOutsideAngular(()=>{for(let t of wu)this._document?.addEventListener(t,this._onInteraction,yu)})}ngOnDestroy(){let t=this._hosts.keys();for(let i of t)this.destroyRipple(i);for(let i of wu)this._document?.removeEventListener(i,this._onInteraction,yu)}configureRipple(t,i){t.setAttribute(uo,""),(i.className||!t.hasAttribute(ho))&&t.setAttribute(ho,i.className||""),i.centered&&t.setAttribute(bu,""),i.disabled&&t.setAttribute(wr,"")}getRipple(t){return this._hosts.get(t)||this._createRipple(t)}setDisabled(t,i){let s=this._hosts.get(t);if(s){s.disabled=i;return}i?t.setAttribute(wr,""):t.removeAttribute(wr)}_createRipple(t){if(!this._document)return;let i=this._hosts.get(t);if(i)return i;t.querySelector(".mat-ripple")?.remove();let s=this._document.createElement("span");s.classList.add("mat-ripple",t.getAttribute(ho)),t.append(s);let c=new Cu(new re(s),this._ngZone,this._platform,this._globalRippleOptions?this._globalRippleOptions:void 0,this._animationMode?this._animationMode:void 0);return c._isInitialized=!0,c.trigger=t,c.centered=t.hasAttribute(bu),c.disabled=t.hasAttribute(wr),this.attachRipple(t,c),c}attachRipple(t,i){t.removeAttribute(uo),this._hosts.set(t,i)}destroyRipple(t){let i=this._hosts.get(t);i&&(i.ngOnDestroy(),this._hosts.delete(t))}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),X0=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=$t({type:e,selectors:[["div","mat-internal-form-field",""]],hostAttrs:[1,"mdc-form-field","mat-internal-form-field"],hostVars:2,hostBindings:function(i,s){i&2&&et("mdc-form-field--align-end",s.labelPosition==="before")},inputs:{labelPosition:"labelPosition"},standalone:!0,features:[Kt],attrs:Gg,ngContentSelectors:qg,decls:1,vars:0,template:function(i,s){i&1&&(_s(),Ni(0))},styles:[".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-form-field{font-family:var(--mdc-form-field-label-text-font);line-height:var(--mdc-form-field-label-text-line-height);font-size:var(--mdc-form-field-label-text-size);font-weight:var(--mdc-form-field-label-text-weight);letter-spacing:var(--mdc-form-field-label-text-tracking);color:var(--mdc-form-field-label-text-color)}.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}"],encapsulation:2,changeDetection:0});let n=e;return n})();var M="primary",oi=Symbol("RouteTitle"),_o=class{constructor(e){this.params=e||{}}has(e){return Object.prototype.hasOwnProperty.call(this.params,e)}get(e){if(this.has(e)){let r=this.params[e];return Array.isArray(r)?r[0]:r}return null}getAll(e){if(this.has(e)){let r=this.params[e];return Array.isArray(r)?r:[r]}return[]}get keys(){return Object.keys(this.params)}};function cn(n){return new _o(n)}function lv(n,e,r){let t=r.path.split("/");if(t.length>n.length||r.pathMatch==="full"&&(e.hasChildren()||t.length<n.length))return null;let i={};for(let s=0;s<t.length;s++){let c=t[s],d=n[s];if(c.startsWith(":"))i[c.substring(1)]=d;else if(c!==d.path)return null}return{consumed:n.slice(0,t.length),posParams:i}}function uv(n,e){if(n.length!==e.length)return!1;for(let r=0;r<n.length;++r)if(!Fe(n[r],e[r]))return!1;return!0}function Fe(n,e){let r=n?Co(n):void 0,t=e?Co(e):void 0;if(!r||!t||r.length!=t.length)return!1;let i;for(let s=0;s<r.length;s++)if(i=r[s],!ku(n[i],e[i]))return!1;return!0}function Co(n){return[...Object.keys(n),...Object.getOwnPropertySymbols(n)]}function ku(n,e){if(Array.isArray(n)&&Array.isArray(e)){if(n.length!==e.length)return!1;let r=[...n].sort(),t=[...e].sort();return r.every((i,s)=>t[s]===i)}else return n===e}function Ou(n){return n.length>0?n[n.length-1]:null}function lt(n){return rc(n)?n:Bi(n)?ie(Promise.resolve(n)):D(n)}var dv={exact:Nu,subset:Fu},Pu={exact:hv,subset:fv,ignored:()=>!0};function Eu(n,e,r){return dv[r.paths](n.root,e.root,r.matrixParams)&&Pu[r.queryParams](n.queryParams,e.queryParams)&&!(r.fragment==="exact"&&n.fragment!==e.fragment)}function hv(n,e){return Fe(n,e)}function Nu(n,e,r){if(!St(n.segments,e.segments)||!Ar(n.segments,e.segments,r)||n.numberOfChildren!==e.numberOfChildren)return!1;for(let t in e.children)if(!n.children[t]||!Nu(n.children[t],e.children[t],r))return!1;return!0}function fv(n,e){return Object.keys(e).length<=Object.keys(n).length&&Object.keys(e).every(r=>ku(n[r],e[r]))}function Fu(n,e,r){return Lu(n,e,e.segments,r)}function Lu(n,e,r,t){if(n.segments.length>r.length){let i=n.segments.slice(0,r.length);return!(!St(i,r)||e.hasChildren()||!Ar(i,r,t))}else if(n.segments.length===r.length){if(!St(n.segments,r)||!Ar(n.segments,r,t))return!1;for(let i in e.children)if(!n.children[i]||!Fu(n.children[i],e.children[i],t))return!1;return!0}else{let i=r.slice(0,n.segments.length),s=r.slice(n.segments.length);return!St(n.segments,i)||!Ar(n.segments,i,t)||!n.children[M]?!1:Lu(n.children[M],e,s,t)}}function Ar(n,e,r){return e.every((t,i)=>Pu[r](n[i].parameters,t.parameters))}var ot=class{constructor(e=new j([],{}),r={},t=null){this.root=e,this.queryParams=r,this.fragment=t}get queryParamMap(){return this._queryParamMap??=cn(this.queryParams),this._queryParamMap}toString(){return gv.serialize(this)}},j=class{constructor(e,r){this.segments=e,this.children=r,this.parent=null,Object.values(r).forEach(t=>t.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ir(this)}},Mt=class{constructor(e,r){this.path=e,this.parameters=r}get parameterMap(){return this._parameterMap??=cn(this.parameters),this._parameterMap}toString(){return ju(this)}};function pv(n,e){return St(n,e)&&n.every((r,t)=>Fe(r.parameters,e[t].parameters))}function St(n,e){return n.length!==e.length?!1:n.every((r,t)=>r.path===e[t].path)}function mv(n,e){let r=[];return Object.entries(n.children).forEach(([t,i])=>{t===M&&(r=r.concat(e(i,t)))}),Object.entries(n.children).forEach(([t,i])=>{t!==M&&(r=r.concat(e(i,t)))}),r}var ai=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:()=>new Qn,providedIn:"root"});let n=e;return n})(),Qn=class{parse(e){let r=new Ao(e);return new ot(r.parseRootSegment(),r.parseQueryParams(),r.parseFragment())}serialize(e){let r=`/${Gn(e.root,!0)}`,t=wv(e.queryParams),i=typeof e.fragment=="string"?`#${vv(e.fragment)}`:"";return`${r}${t}${i}`}},gv=new Qn;function Ir(n){return n.segments.map(e=>ju(e)).join("/")}function Gn(n,e){if(!n.hasChildren())return Ir(n);if(e){let r=n.children[M]?Gn(n.children[M],!1):"",t=[];return Object.entries(n.children).forEach(([i,s])=>{i!==M&&t.push(`${i}:${Gn(s,!1)}`)}),t.length>0?`${r}(${t.join("//")})`:r}else{let r=mv(n,(t,i)=>i===M?[Gn(n.children[M],!1)]:[`${i}:${Gn(t,!1)}`]);return Object.keys(n.children).length===1&&n.children[M]!=null?`${Ir(n)}/${r[0]}`:`${Ir(n)}/(${r.join("//")})`}}function Vu(n){return encodeURIComponent(n).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Cr(n){return Vu(n).replace(/%3B/gi,";")}function vv(n){return encodeURI(n)}function Eo(n){return Vu(n).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Dr(n){return decodeURIComponent(n)}function Au(n){return Dr(n.replace(/\+/g,"%20"))}function ju(n){return`${Eo(n.path)}${yv(n.parameters)}`}function yv(n){return Object.entries(n).map(([e,r])=>`;${Eo(e)}=${Eo(r)}`).join("")}function wv(n){let e=Object.entries(n).map(([r,t])=>Array.isArray(t)?t.map(i=>`${Cr(r)}=${Cr(i)}`).join("&"):`${Cr(r)}=${Cr(t)}`).filter(r=>r);return e.length?`?${e.join("&")}`:""}var bv=/^[^\/()?;#]+/;function vo(n){let e=n.match(bv);return e?e[0]:""}var _v=/^[^\/()?;=#]+/;function Cv(n){let e=n.match(_v);return e?e[0]:""}var Ev=/^[^=?&#]+/;function Av(n){let e=n.match(Ev);return e?e[0]:""}var Iv=/^[^&#]+/;function Dv(n){let e=n.match(Iv);return e?e[0]:""}var Ao=class{constructor(e){this.url=e,this.remaining=e}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new j([],{}):new j([],this.parseChildren())}parseQueryParams(){let e={};if(this.consumeOptional("?"))do this.parseQueryParam(e);while(this.consumeOptional("&"));return e}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let e=[];for(this.peekStartsWith("(")||e.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),e.push(this.parseSegment());let r={};this.peekStartsWith("/(")&&(this.capture("/"),r=this.parseParens(!0));let t={};return this.peekStartsWith("(")&&(t=this.parseParens(!1)),(e.length>0||Object.keys(r).length>0)&&(t[M]=new j(e,r)),t}parseSegment(){let e=vo(this.remaining);if(e===""&&this.peekStartsWith(";"))throw new B(4009,!1);return this.capture(e),new Mt(Dr(e),this.parseMatrixParams())}parseMatrixParams(){let e={};for(;this.consumeOptional(";");)this.parseParam(e);return e}parseParam(e){let r=Cv(this.remaining);if(!r)return;this.capture(r);let t="";if(this.consumeOptional("=")){let i=vo(this.remaining);i&&(t=i,this.capture(t))}e[Dr(r)]=Dr(t)}parseQueryParam(e){let r=Av(this.remaining);if(!r)return;this.capture(r);let t="";if(this.consumeOptional("=")){let c=Dv(this.remaining);c&&(t=c,this.capture(t))}let i=Au(r),s=Au(t);if(e.hasOwnProperty(i)){let c=e[i];Array.isArray(c)||(c=[c],e[i]=c),c.push(s)}else e[i]=s}parseParens(e){let r={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let t=vo(this.remaining),i=this.remaining[t.length];if(i!=="/"&&i!==")"&&i!==";")throw new B(4010,!1);let s;t.indexOf(":")>-1?(s=t.slice(0,t.indexOf(":")),this.capture(s),this.capture(":")):e&&(s=M);let c=this.parseChildren();r[s]=Object.keys(c).length===1?c[M]:new j([],c),this.consumeOptional("//")}return r}peekStartsWith(e){return this.remaining.startsWith(e)}consumeOptional(e){return this.peekStartsWith(e)?(this.remaining=this.remaining.substring(e.length),!0):!1}capture(e){if(!this.consumeOptional(e))throw new B(4011,!1)}};function Uu(n){return n.segments.length>0?new j([],{[M]:n}):n}function Bu(n){let e={};for(let[t,i]of Object.entries(n.children)){let s=Bu(i);if(t===M&&s.segments.length===0&&s.hasChildren())for(let[c,d]of Object.entries(s.children))e[c]=d;else(s.segments.length>0||s.hasChildren())&&(e[t]=s)}let r=new j(n.segments,e);return Tv(r)}function Tv(n){if(n.numberOfChildren===1&&n.children[M]){let e=n.children[M];return new j(n.segments.concat(e.segments),e.children)}return n}function ln(n){return n instanceof ot}function Mv(n,e,r=null,t=null){let i=$u(n);return zu(i,e,r,t)}function $u(n){let e;function r(s){let c={};for(let p of s.children){let f=r(p);c[p.outlet]=f}let d=new j(s.url,c);return s===n&&(e=d),d}let t=r(n.root),i=Uu(t);return e??i}function zu(n,e,r,t){let i=n;for(;i.parent;)i=i.parent;if(e.length===0)return yo(i,i,i,r,t);let s=Sv(e);if(s.toRoot())return yo(i,i,new j([],{}),r,t);let c=Rv(s,i,n),d=c.processChildren?Zn(c.segmentGroup,c.index,s.commands):Wu(c.segmentGroup,c.index,s.commands);return yo(i,c.segmentGroup,d,r,t)}function Tr(n){return typeof n=="object"&&n!=null&&!n.outlets&&!n.segmentPath}function Jn(n){return typeof n=="object"&&n!=null&&n.outlets}function yo(n,e,r,t,i){let s={};t&&Object.entries(t).forEach(([p,f])=>{s[p]=Array.isArray(f)?f.map(v=>`${v}`):`${f}`});let c;n===e?c=r:c=Hu(n,e,r);let d=Uu(Bu(c));return new ot(d,s,i)}function Hu(n,e,r){let t={};return Object.entries(n.children).forEach(([i,s])=>{s===e?t[i]=r:t[i]=Hu(s,e,r)}),new j(n.segments,t)}var Mr=class{constructor(e,r,t){if(this.isAbsolute=e,this.numberOfDoubleDots=r,this.commands=t,e&&t.length>0&&Tr(t[0]))throw new B(4003,!1);let i=t.find(Jn);if(i&&i!==Ou(t))throw new B(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function Sv(n){if(typeof n[0]=="string"&&n.length===1&&n[0]==="/")return new Mr(!0,0,n);let e=0,r=!1,t=n.reduce((i,s,c)=>{if(typeof s=="object"&&s!=null){if(s.outlets){let d={};return Object.entries(s.outlets).forEach(([p,f])=>{d[p]=typeof f=="string"?f.split("/"):f}),[...i,{outlets:d}]}if(s.segmentPath)return[...i,s.segmentPath]}return typeof s!="string"?[...i,s]:c===0?(s.split("/").forEach((d,p)=>{p==0&&d==="."||(p==0&&d===""?r=!0:d===".."?e++:d!=""&&i.push(d))}),i):[...i,s]},[]);return new Mr(r,e,t)}var on=class{constructor(e,r,t){this.segmentGroup=e,this.processChildren=r,this.index=t}};function Rv(n,e,r){if(n.isAbsolute)return new on(e,!0,0);if(!r)return new on(e,!1,NaN);if(r.parent===null)return new on(r,!0,0);let t=Tr(n.commands[0])?0:1,i=r.segments.length-1+t;return xv(r,i,n.numberOfDoubleDots)}function xv(n,e,r){let t=n,i=e,s=r;for(;s>i;){if(s-=i,t=t.parent,!t)throw new B(4005,!1);i=t.segments.length}return new on(t,!1,i-s)}function kv(n){return Jn(n[0])?n[0].outlets:{[M]:n}}function Wu(n,e,r){if(n??=new j([],{}),n.segments.length===0&&n.hasChildren())return Zn(n,e,r);let t=Ov(n,e,r),i=r.slice(t.commandIndex);if(t.match&&t.pathIndex<n.segments.length){let s=new j(n.segments.slice(0,t.pathIndex),{});return s.children[M]=new j(n.segments.slice(t.pathIndex),n.children),Zn(s,0,i)}else return t.match&&i.length===0?new j(n.segments,{}):t.match&&!n.hasChildren()?Io(n,e,r):t.match?Zn(n,0,i):Io(n,e,r)}function Zn(n,e,r){if(r.length===0)return new j(n.segments,{});{let t=kv(r),i={};if(Object.keys(t).some(s=>s!==M)&&n.children[M]&&n.numberOfChildren===1&&n.children[M].segments.length===0){let s=Zn(n.children[M],e,r);return new j(n.segments,s.children)}return Object.entries(t).forEach(([s,c])=>{typeof c=="string"&&(c=[c]),c!==null&&(i[s]=Wu(n.children[s],e,c))}),Object.entries(n.children).forEach(([s,c])=>{t[s]===void 0&&(i[s]=c)}),new j(n.segments,i)}}function Ov(n,e,r){let t=0,i=e,s={match:!1,pathIndex:0,commandIndex:0};for(;i<n.segments.length;){if(t>=r.length)return s;let c=n.segments[i],d=r[t];if(Jn(d))break;let p=`${d}`,f=t<r.length-1?r[t+1]:null;if(i>0&&p===void 0)break;if(p&&f&&typeof f=="object"&&f.outlets===void 0){if(!Du(p,f,c))return s;t+=2}else{if(!Du(p,{},c))return s;t++}i++}return{match:!0,pathIndex:i,commandIndex:t}}function Io(n,e,r){let t=n.segments.slice(0,e),i=0;for(;i<r.length;){let s=r[i];if(Jn(s)){let p=Pv(s.outlets);return new j(t,p)}if(i===0&&Tr(r[0])){let p=n.segments[e];t.push(new Mt(p.path,Iu(r[0]))),i++;continue}let c=Jn(s)?s.outlets[M]:`${s}`,d=i<r.length-1?r[i+1]:null;c&&d&&Tr(d)?(t.push(new Mt(c,Iu(d))),i+=2):(t.push(new Mt(c,{})),i++)}return new j(t,{})}function Pv(n){let e={};return Object.entries(n).forEach(([r,t])=>{typeof t=="string"&&(t=[t]),t!==null&&(e[r]=Io(new j([],{}),0,t))}),e}function Iu(n){let e={};return Object.entries(n).forEach(([r,t])=>e[r]=`${t}`),e}function Du(n,e,r){return n==r.path&&Fe(e,r.parameters)}var Yn="imperative",se=function(n){return n[n.NavigationStart=0]="NavigationStart",n[n.NavigationEnd=1]="NavigationEnd",n[n.NavigationCancel=2]="NavigationCancel",n[n.NavigationError=3]="NavigationError",n[n.RoutesRecognized=4]="RoutesRecognized",n[n.ResolveStart=5]="ResolveStart",n[n.ResolveEnd=6]="ResolveEnd",n[n.GuardsCheckStart=7]="GuardsCheckStart",n[n.GuardsCheckEnd=8]="GuardsCheckEnd",n[n.RouteConfigLoadStart=9]="RouteConfigLoadStart",n[n.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",n[n.ChildActivationStart=11]="ChildActivationStart",n[n.ChildActivationEnd=12]="ChildActivationEnd",n[n.ActivationStart=13]="ActivationStart",n[n.ActivationEnd=14]="ActivationEnd",n[n.Scroll=15]="Scroll",n[n.NavigationSkipped=16]="NavigationSkipped",n}(se||{}),Te=class{constructor(e,r){this.id=e,this.url=r}},un=class extends Te{constructor(e,r,t="imperative",i=null){super(e,r),this.type=se.NavigationStart,this.navigationTrigger=t,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Re=class extends Te{constructor(e,r,t){super(e,r),this.urlAfterRedirects=t,this.type=se.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Ee=function(n){return n[n.Redirect=0]="Redirect",n[n.SupersededByNewNavigation=1]="SupersededByNewNavigation",n[n.NoDataFromResolver=2]="NoDataFromResolver",n[n.GuardRejected=3]="GuardRejected",n}(Ee||{}),Sr=function(n){return n[n.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",n[n.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",n}(Sr||{}),at=class extends Te{constructor(e,r,t,i){super(e,r),this.reason=t,this.code=i,this.type=se.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},ct=class extends Te{constructor(e,r,t,i){super(e,r),this.reason=t,this.code=i,this.type=se.NavigationSkipped}},ei=class extends Te{constructor(e,r,t,i){super(e,r),this.error=t,this.target=i,this.type=se.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Rr=class extends Te{constructor(e,r,t,i){super(e,r),this.urlAfterRedirects=t,this.state=i,this.type=se.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Do=class extends Te{constructor(e,r,t,i){super(e,r),this.urlAfterRedirects=t,this.state=i,this.type=se.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},To=class extends Te{constructor(e,r,t,i,s){super(e,r),this.urlAfterRedirects=t,this.state=i,this.shouldActivate=s,this.type=se.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Mo=class extends Te{constructor(e,r,t,i){super(e,r),this.urlAfterRedirects=t,this.state=i,this.type=se.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},So=class extends Te{constructor(e,r,t,i){super(e,r),this.urlAfterRedirects=t,this.state=i,this.type=se.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ro=class{constructor(e){this.route=e,this.type=se.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},xo=class{constructor(e){this.route=e,this.type=se.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},ko=class{constructor(e){this.snapshot=e,this.type=se.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Oo=class{constructor(e){this.snapshot=e,this.type=se.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Po=class{constructor(e){this.snapshot=e,this.type=se.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},No=class{constructor(e){this.snapshot=e,this.type=se.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},xr=class{constructor(e,r,t){this.routerEvent=e,this.position=r,this.anchor=t,this.type=se.Scroll}toString(){let e=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${e}')`}},ti=class{},ni=class{constructor(e){this.url=e}};var Fo=class{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new ci,this.attachRef=null}},ci=(()=>{let e=class e{constructor(){this.contexts=new Map}onChildOutletCreated(t,i){let s=this.getOrCreateContext(t);s.outlet=i,this.contexts.set(t,s)}onChildOutletDestroyed(t){let i=this.getContext(t);i&&(i.outlet=null,i.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let i=this.getContext(t);return i||(i=new Fo,this.contexts.set(t,i)),i}getContext(t){return this.contexts.get(t)||null}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),kr=class{constructor(e){this._root=e}get root(){return this._root.value}parent(e){let r=this.pathFromRoot(e);return r.length>1?r[r.length-2]:null}children(e){let r=Lo(e,this._root);return r?r.children.map(t=>t.value):[]}firstChild(e){let r=Lo(e,this._root);return r&&r.children.length>0?r.children[0].value:null}siblings(e){let r=Vo(e,this._root);return r.length<2?[]:r[r.length-2].children.map(i=>i.value).filter(i=>i!==e)}pathFromRoot(e){return Vo(e,this._root).map(r=>r.value)}};function Lo(n,e){if(n===e.value)return e;for(let r of e.children){let t=Lo(n,r);if(t)return t}return null}function Vo(n,e){if(n===e.value)return[e];for(let r of e.children){let t=Vo(n,r);if(t.length)return t.unshift(e),t}return[]}var Ce=class{constructor(e,r){this.value=e,this.children=r}toString(){return`TreeNode(${this.value})`}};function sn(n){let e={};return n&&n.children.forEach(r=>e[r.value.outlet]=r),e}var Or=class extends kr{constructor(e,r){super(e),this.snapshot=r,Ko(this,e)}toString(){return this.snapshot.toString()}};function Gu(n){let e=Nv(n),r=new he([new Mt("",{})]),t=new he({}),i=new he({}),s=new he({}),c=new he(""),d=new Rt(r,t,s,c,i,M,n,e.root);return d.snapshot=e.root,new Or(new Ce(d,[]),e)}function Nv(n){let e={},r={},t={},i="",s=new ii([],e,t,i,r,M,n,null,{});return new Pr("",new Ce(s,[]))}var Rt=class{constructor(e,r,t,i,s,c,d,p){this.urlSubject=e,this.paramsSubject=r,this.queryParamsSubject=t,this.fragmentSubject=i,this.dataSubject=s,this.outlet=c,this.component=d,this._futureSnapshot=p,this.title=this.dataSubject?.pipe(k(f=>f[oi]))??D(void 0),this.url=e,this.params=r,this.queryParams=t,this.fragment=i,this.data=s}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(k(e=>cn(e))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(k(e=>cn(e))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function qo(n,e,r="emptyOnly"){let t,{routeConfig:i}=n;return e!==null&&(r==="always"||i?.path===""||!e.component&&!e.routeConfig?.loadComponent)?t={params:y(y({},e.params),n.params),data:y(y({},e.data),n.data),resolve:y(y(y(y({},n.data),e.data),i?.data),n._resolvedData)}:t={params:y({},n.params),data:y({},n.data),resolve:y(y({},n.data),n._resolvedData??{})},i&&Ku(i)&&(t.resolve[oi]=i.title),t}var ii=class{get title(){return this.data?.[oi]}constructor(e,r,t,i,s,c,d,p,f){this.url=e,this.params=r,this.queryParams=t,this.fragment=i,this.data=s,this.outlet=c,this.component=d,this.routeConfig=p,this._resolve=f}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=cn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=cn(this.queryParams),this._queryParamMap}toString(){let e=this.url.map(t=>t.toString()).join("/"),r=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${e}', path:'${r}')`}},Pr=class extends kr{constructor(e,r){super(r),this.url=e,Ko(this,r)}toString(){return qu(this._root)}};function Ko(n,e){e.value._routerState=n,e.children.forEach(r=>Ko(n,r))}function qu(n){let e=n.children.length>0?` { ${n.children.map(qu).join(", ")} } `:"";return`${n.value}${e}`}function wo(n){if(n.snapshot){let e=n.snapshot,r=n._futureSnapshot;n.snapshot=r,Fe(e.queryParams,r.queryParams)||n.queryParamsSubject.next(r.queryParams),e.fragment!==r.fragment&&n.fragmentSubject.next(r.fragment),Fe(e.params,r.params)||n.paramsSubject.next(r.params),uv(e.url,r.url)||n.urlSubject.next(r.url),Fe(e.data,r.data)||n.dataSubject.next(r.data)}else n.snapshot=n._futureSnapshot,n.dataSubject.next(n._futureSnapshot.data)}function jo(n,e){let r=Fe(n.params,e.params)&&pv(n.url,e.url),t=!n.parent!=!e.parent;return r&&!t&&(!n.parent||jo(n.parent,e.parent))}function Ku(n){return typeof n.title=="string"||n.title===null}var Fv=(()=>{let e=class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=M,this.activateEvents=new Z,this.deactivateEvents=new Z,this.attachEvents=new Z,this.detachEvents=new Z,this.parentContexts=_(ci),this.location=_(Mc),this.changeDetector=_(tt),this.environmentInjector=_(wt),this.inputBinder=_(jr,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(t){if(t.name){let{firstChange:i,previousValue:s}=t.name;if(i)return;this.isTrackedInParentContexts(s)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(s)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new B(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new B(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new B(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,i){this.activated=t,this._activatedRoute=i,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,i){if(this.isActivated)throw new B(4013,!1);this._activatedRoute=t;let s=this.location,d=t.snapshot.component,p=this.parentContexts.getOrCreateContext(this.name).children,f=new Uo(t,p,s.injector);this.activated=s.createComponent(d,{index:s.length,injector:f,environmentInjector:i??this.environmentInjector}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}};e.\u0275fac=function(i){return new(i||e)},e.\u0275dir=V({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[Ie]});let n=e;return n})(),Uo=class{constructor(e,r,t){this.route=e,this.childContexts=r,this.parent=t}get(e,r){return e===Rt?this.route:e===ci?this.childContexts:this.parent.get(e,r)}},jr=new T(""),Tu=(()=>{let e=class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:i}=t,s=Vt([i.queryParams,i.params,i.data]).pipe(we(([c,d,p],f)=>(p=y(y(y({},c),d),p),f===0?D(p):Promise.resolve(p)))).subscribe(c=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==i||i.component===null){this.unsubscribeFromRouteData(t);return}let d=Uc(i.component);if(!d){this.unsubscribeFromRouteData(t);return}for(let{templateName:p}of d.inputs)t.activatedComponentRef.setInput(p,c[p])});this.outletDataSubscriptions.set(t,s)}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})();function Lv(n,e,r){let t=ri(n,e._root,r?r._root:void 0);return new Or(t,e)}function ri(n,e,r){if(r&&n.shouldReuseRoute(e.value,r.value.snapshot)){let t=r.value;t._futureSnapshot=e.value;let i=Vv(n,e,r);return new Ce(t,i)}else{if(n.shouldAttach(e.value)){let s=n.retrieve(e.value);if(s!==null){let c=s.route;return c.value._futureSnapshot=e.value,c.children=e.children.map(d=>ri(n,d)),c}}let t=jv(e.value),i=e.children.map(s=>ri(n,s));return new Ce(t,i)}}function Vv(n,e,r){return e.children.map(t=>{for(let i of r.children)if(n.shouldReuseRoute(t.value,i.value.snapshot))return ri(n,t,i);return ri(n,t)})}function jv(n){return new Rt(new he(n.url),new he(n.params),new he(n.queryParams),new he(n.fragment),new he(n.data),n.outlet,n.component,n)}var Zu="ngNavigationCancelingError";function Yu(n,e){let{redirectTo:r,navigationBehaviorOptions:t}=ln(e)?{redirectTo:e,navigationBehaviorOptions:void 0}:e,i=Xu(!1,Ee.Redirect);return i.url=r,i.navigationBehaviorOptions=t,i}function Xu(n,e){let r=new Error(`NavigationCancelingError: ${n||""}`);return r[Zu]=!0,r.cancellationCode=e,r}function Uv(n){return Qu(n)&&ln(n.url)}function Qu(n){return!!n&&n[Zu]}var Bv=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=$t({type:e,selectors:[["ng-component"]],standalone:!0,features:[Kt],decls:1,vars:0,template:function(i,s){i&1&&qt(0,"router-outlet")},dependencies:[Fv],encapsulation:2});let n=e;return n})();function $v(n,e){return n.providers&&!n._injector&&(n._injector=vs(n.providers,e,`Route: ${n.path}`)),n._injector??e}function Zo(n){let e=n.children&&n.children.map(Zo),r=e?W(y({},n),{children:e}):y({},n);return!r.component&&!r.loadComponent&&(e||r.loadChildren)&&r.outlet&&r.outlet!==M&&(r.component=Bv),r}function Le(n){return n.outlet||M}function zv(n,e){let r=n.filter(t=>Le(t)===e);return r.push(...n.filter(t=>Le(t)!==e)),r}function li(n){if(!n)return null;if(n.routeConfig?._injector)return n.routeConfig._injector;for(let e=n.parent;e;e=e.parent){let r=e.routeConfig;if(r?._loadedInjector)return r._loadedInjector;if(r?._injector)return r._injector}return null}var Hv=(n,e,r,t)=>k(i=>(new Bo(e,i.targetRouterState,i.currentRouterState,r,t).activate(n),i)),Bo=class{constructor(e,r,t,i,s){this.routeReuseStrategy=e,this.futureState=r,this.currState=t,this.forwardEvent=i,this.inputBindingEnabled=s}activate(e){let r=this.futureState._root,t=this.currState?this.currState._root:null;this.deactivateChildRoutes(r,t,e),wo(this.futureState.root),this.activateChildRoutes(r,t,e)}deactivateChildRoutes(e,r,t){let i=sn(r);e.children.forEach(s=>{let c=s.value.outlet;this.deactivateRoutes(s,i[c],t),delete i[c]}),Object.values(i).forEach(s=>{this.deactivateRouteAndItsChildren(s,t)})}deactivateRoutes(e,r,t){let i=e.value,s=r?r.value:null;if(i===s)if(i.component){let c=t.getContext(i.outlet);c&&this.deactivateChildRoutes(e,r,c.children)}else this.deactivateChildRoutes(e,r,t);else s&&this.deactivateRouteAndItsChildren(r,t)}deactivateRouteAndItsChildren(e,r){e.value.component&&this.routeReuseStrategy.shouldDetach(e.value.snapshot)?this.detachAndStoreRouteSubtree(e,r):this.deactivateRouteAndOutlet(e,r)}detachAndStoreRouteSubtree(e,r){let t=r.getContext(e.value.outlet),i=t&&e.value.component?t.children:r,s=sn(e);for(let c of Object.values(s))this.deactivateRouteAndItsChildren(c,i);if(t&&t.outlet){let c=t.outlet.detach(),d=t.children.onOutletDeactivated();this.routeReuseStrategy.store(e.value.snapshot,{componentRef:c,route:e,contexts:d})}}deactivateRouteAndOutlet(e,r){let t=r.getContext(e.value.outlet),i=t&&e.value.component?t.children:r,s=sn(e);for(let c of Object.values(s))this.deactivateRouteAndItsChildren(c,i);t&&(t.outlet&&(t.outlet.deactivate(),t.children.onOutletDeactivated()),t.attachRef=null,t.route=null)}activateChildRoutes(e,r,t){let i=sn(r);e.children.forEach(s=>{this.activateRoutes(s,i[s.value.outlet],t),this.forwardEvent(new No(s.value.snapshot))}),e.children.length&&this.forwardEvent(new Oo(e.value.snapshot))}activateRoutes(e,r,t){let i=e.value,s=r?r.value:null;if(wo(i),i===s)if(i.component){let c=t.getOrCreateContext(i.outlet);this.activateChildRoutes(e,r,c.children)}else this.activateChildRoutes(e,r,t);else if(i.component){let c=t.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let d=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),c.children.onOutletReAttached(d.contexts),c.attachRef=d.componentRef,c.route=d.route.value,c.outlet&&c.outlet.attach(d.componentRef,d.route.value),wo(d.route.value),this.activateChildRoutes(e,null,c.children)}else{let d=li(i.snapshot);c.attachRef=null,c.route=i,c.injector=d,c.outlet&&c.outlet.activateWith(i,c.injector),this.activateChildRoutes(e,null,c.children)}}else this.activateChildRoutes(e,null,t)}},Nr=class{constructor(e){this.path=e,this.route=this.path[this.path.length-1]}},an=class{constructor(e,r){this.component=e,this.route=r}};function Wv(n,e,r){let t=n._root,i=e?e._root:null;return qn(t,i,r,[t.value])}function Gv(n){let e=n.routeConfig?n.routeConfig.canActivateChild:null;return!e||e.length===0?null:{node:n,guards:e}}function hn(n,e){let r=Symbol(),t=e.get(n,r);return t===r?typeof n=="function"&&!dc(n)?n:e.get(n):t}function qn(n,e,r,t,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=sn(e);return n.children.forEach(c=>{qv(c,s[c.value.outlet],r,t.concat([c.value]),i),delete s[c.value.outlet]}),Object.entries(s).forEach(([c,d])=>Xn(d,r.getContext(c),i)),i}function qv(n,e,r,t,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=n.value,c=e?e.value:null,d=r?r.getContext(n.value.outlet):null;if(c&&s.routeConfig===c.routeConfig){let p=Kv(c,s,s.routeConfig.runGuardsAndResolvers);p?i.canActivateChecks.push(new Nr(t)):(s.data=c.data,s._resolvedData=c._resolvedData),s.component?qn(n,e,d?d.children:null,t,i):qn(n,e,r,t,i),p&&d&&d.outlet&&d.outlet.isActivated&&i.canDeactivateChecks.push(new an(d.outlet.component,c))}else c&&Xn(e,d,i),i.canActivateChecks.push(new Nr(t)),s.component?qn(n,null,d?d.children:null,t,i):qn(n,null,r,t,i);return i}function Kv(n,e,r){if(typeof r=="function")return r(n,e);switch(r){case"pathParamsChange":return!St(n.url,e.url);case"pathParamsOrQueryParamsChange":return!St(n.url,e.url)||!Fe(n.queryParams,e.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!jo(n,e)||!Fe(n.queryParams,e.queryParams);case"paramsChange":default:return!jo(n,e)}}function Xn(n,e,r){let t=sn(n),i=n.value;Object.entries(t).forEach(([s,c])=>{i.component?e?Xn(c,e.children.getContext(s),r):Xn(c,null,r):Xn(c,e,r)}),i.component?e&&e.outlet&&e.outlet.isActivated?r.canDeactivateChecks.push(new an(e.outlet.component,i)):r.canDeactivateChecks.push(new an(null,i)):r.canDeactivateChecks.push(new an(null,i))}function ui(n){return typeof n=="function"}function Zv(n){return typeof n=="boolean"}function Yv(n){return n&&ui(n.canLoad)}function Xv(n){return n&&ui(n.canActivate)}function Qv(n){return n&&ui(n.canActivateChild)}function Jv(n){return n&&ui(n.canDeactivate)}function ey(n){return n&&ui(n.canMatch)}function Ju(n){return n instanceof sc||n?.name==="EmptyError"}var Er=Symbol("INITIAL_VALUE");function dn(){return we(n=>Vt(n.map(e=>e.pipe(Pe(1),Mi(Er)))).pipe(k(e=>{for(let r of e)if(r!==!0){if(r===Er)return Er;if(r===!1||r instanceof ot)return r}return!0}),Me(e=>e!==Er),Pe(1)))}function ty(n,e){return ye(r=>{let{targetSnapshot:t,currentSnapshot:i,guards:{canActivateChecks:s,canDeactivateChecks:c}}=r;return c.length===0&&s.length===0?D(W(y({},r),{guardsResult:!0})):ny(c,t,i,n).pipe(ye(d=>d&&Zv(d)?iy(t,s,n,e):D(d)),k(d=>W(y({},r),{guardsResult:d})))})}function ny(n,e,r,t){return ie(n).pipe(ye(i=>cy(i.component,i.route,r,e,t)),Ye(i=>i!==!0,!0))}function iy(n,e,r,t){return ie(e).pipe(Ze(i=>Di(sy(i.route.parent,t),ry(i.route,t),ay(n,i.path,r),oy(n,i.route,r))),Ye(i=>i!==!0,!0))}function ry(n,e){return n!==null&&e&&e(new Po(n)),D(!0)}function sy(n,e){return n!==null&&e&&e(new ko(n)),D(!0)}function oy(n,e,r){let t=e.routeConfig?e.routeConfig.canActivate:null;if(!t||t.length===0)return D(!0);let i=t.map(s=>us(()=>{let c=li(e)??r,d=hn(s,c),p=Xv(d)?d.canActivate(e,n):Ne(c,()=>d(e,n));return lt(p).pipe(Ye())}));return D(i).pipe(dn())}function ay(n,e,r){let t=e[e.length-1],s=e.slice(0,e.length-1).reverse().map(c=>Gv(c)).filter(c=>c!==null).map(c=>us(()=>{let d=c.guards.map(p=>{let f=li(c.node)??r,v=hn(p,f),I=Qv(v)?v.canActivateChild(t,n):Ne(f,()=>v(t,n));return lt(I).pipe(Ye())});return D(d).pipe(dn())}));return D(s).pipe(dn())}function cy(n,e,r,t,i){let s=e&&e.routeConfig?e.routeConfig.canDeactivate:null;if(!s||s.length===0)return D(!0);let c=s.map(d=>{let p=li(e)??i,f=hn(d,p),v=Jv(f)?f.canDeactivate(n,e,r,t):Ne(p,()=>f(n,e,r,t));return lt(v).pipe(Ye())});return D(c).pipe(dn())}function ly(n,e,r,t){let i=e.canLoad;if(i===void 0||i.length===0)return D(!0);let s=i.map(c=>{let d=hn(c,n),p=Yv(d)?d.canLoad(e,r):Ne(n,()=>d(e,r));return lt(p)});return D(s).pipe(dn(),ed(t))}function ed(n){return ic(te(e=>{if(ln(e))throw Yu(n,e)}),k(e=>e===!0))}function uy(n,e,r,t){let i=e.canMatch;if(!i||i.length===0)return D(!0);let s=i.map(c=>{let d=hn(c,n),p=ey(d)?d.canMatch(e,r):Ne(n,()=>d(e,r));return lt(p)});return D(s).pipe(dn(),ed(t))}var si=class{constructor(e){this.segmentGroup=e||null}},Fr=class extends Error{constructor(e){super(),this.urlTree=e}};function rn(n){return Cn(new si(n))}function dy(n){return Cn(new B(4e3,!1))}function hy(n){return Cn(Xu(!1,Ee.GuardRejected))}var $o=class{constructor(e,r){this.urlSerializer=e,this.urlTree=r}lineralizeSegments(e,r){let t=[],i=r.root;for(;;){if(t=t.concat(i.segments),i.numberOfChildren===0)return D(t);if(i.numberOfChildren>1||!i.children[M])return dy(e.redirectTo);i=i.children[M]}}applyRedirectCommands(e,r,t){let i=this.applyRedirectCreateUrlTree(r,this.urlSerializer.parse(r),e,t);if(r.startsWith("/"))throw new Fr(i);return i}applyRedirectCreateUrlTree(e,r,t,i){let s=this.createSegmentGroup(e,r.root,t,i);return new ot(s,this.createQueryParams(r.queryParams,this.urlTree.queryParams),r.fragment)}createQueryParams(e,r){let t={};return Object.entries(e).forEach(([i,s])=>{if(typeof s=="string"&&s.startsWith(":")){let d=s.substring(1);t[i]=r[d]}else t[i]=s}),t}createSegmentGroup(e,r,t,i){let s=this.createSegments(e,r.segments,t,i),c={};return Object.entries(r.children).forEach(([d,p])=>{c[d]=this.createSegmentGroup(e,p,t,i)}),new j(s,c)}createSegments(e,r,t,i){return r.map(s=>s.path.startsWith(":")?this.findPosParam(e,s,i):this.findOrReturn(s,t))}findPosParam(e,r,t){let i=t[r.path.substring(1)];if(!i)throw new B(4001,!1);return i}findOrReturn(e,r){let t=0;for(let i of r){if(i.path===e.path)return r.splice(t),i;t++}return e}},zo={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function fy(n,e,r,t,i){let s=Yo(n,e,r);return s.matched?(t=$v(e,t),uy(t,e,r,i).pipe(k(c=>c===!0?s:y({},zo)))):D(s)}function Yo(n,e,r){if(e.path==="**")return py(r);if(e.path==="")return e.pathMatch==="full"&&(n.hasChildren()||r.length>0)?y({},zo):{matched:!0,consumedSegments:[],remainingSegments:r,parameters:{},positionalParamSegments:{}};let i=(e.matcher||lv)(r,n,e);if(!i)return y({},zo);let s={};Object.entries(i.posParams??{}).forEach(([d,p])=>{s[d]=p.path});let c=i.consumed.length>0?y(y({},s),i.consumed[i.consumed.length-1].parameters):s;return{matched:!0,consumedSegments:i.consumed,remainingSegments:r.slice(i.consumed.length),parameters:c,positionalParamSegments:i.posParams??{}}}function py(n){return{matched:!0,parameters:n.length>0?Ou(n).parameters:{},consumedSegments:n,remainingSegments:[],positionalParamSegments:{}}}function Mu(n,e,r,t){return r.length>0&&vy(n,r,t)?{segmentGroup:new j(e,gy(t,new j(r,n.children))),slicedSegments:[]}:r.length===0&&yy(n,r,t)?{segmentGroup:new j(n.segments,my(n,r,t,n.children)),slicedSegments:r}:{segmentGroup:new j(n.segments,n.children),slicedSegments:r}}function my(n,e,r,t){let i={};for(let s of r)if(Ur(n,e,s)&&!t[Le(s)]){let c=new j([],{});i[Le(s)]=c}return y(y({},t),i)}function gy(n,e){let r={};r[M]=e;for(let t of n)if(t.path===""&&Le(t)!==M){let i=new j([],{});r[Le(t)]=i}return r}function vy(n,e,r){return r.some(t=>Ur(n,e,t)&&Le(t)!==M)}function yy(n,e,r){return r.some(t=>Ur(n,e,t))}function Ur(n,e,r){return(n.hasChildren()||e.length>0)&&r.pathMatch==="full"?!1:r.path===""}function wy(n,e,r,t){return Le(n)!==t&&(t===M||!Ur(e,r,n))?!1:Yo(e,n,r).matched}function by(n,e,r){return e.length===0&&!n.children[r]}var Ho=class{};function _y(n,e,r,t,i,s,c="emptyOnly"){return new Wo(n,e,r,t,i,c,s).recognize()}var Cy=31,Wo=class{constructor(e,r,t,i,s,c,d){this.injector=e,this.configLoader=r,this.rootComponentType=t,this.config=i,this.urlTree=s,this.paramsInheritanceStrategy=c,this.urlSerializer=d,this.applyRedirects=new $o(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(e){return new B(4002,`'${e.segmentGroup}'`)}recognize(){let e=Mu(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(e).pipe(k(r=>{let t=new ii([],Object.freeze({}),Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,{},M,this.rootComponentType,null,{}),i=new Ce(t,r),s=new Pr("",i),c=Mv(t,[],this.urlTree.queryParams,this.urlTree.fragment);return c.queryParams=this.urlTree.queryParams,s.url=this.urlSerializer.serialize(c),this.inheritParamsAndData(s._root,null),{state:s,tree:c}}))}match(e){return this.processSegmentGroup(this.injector,this.config,e,M).pipe(jt(t=>{if(t instanceof Fr)return this.urlTree=t.urlTree,this.match(t.urlTree.root);throw t instanceof si?this.noMatchError(t):t}))}inheritParamsAndData(e,r){let t=e.value,i=qo(t,r,this.paramsInheritanceStrategy);t.params=Object.freeze(i.params),t.data=Object.freeze(i.data),e.children.forEach(s=>this.inheritParamsAndData(s,t))}processSegmentGroup(e,r,t,i){return t.segments.length===0&&t.hasChildren()?this.processChildren(e,r,t):this.processSegment(e,r,t,t.segments,i,!0).pipe(k(s=>s instanceof Ce?[s]:[]))}processChildren(e,r,t){let i=[];for(let s of Object.keys(t.children))s==="primary"?i.unshift(s):i.push(s);return ie(i).pipe(Ze(s=>{let c=t.children[s],d=zv(r,s);return this.processSegmentGroup(e,d,c,s)}),uc((s,c)=>(s.push(...c),s)),ds(null),lc(),ye(s=>{if(s===null)return rn(t);let c=td(s);return Ey(c),D(c)}))}processSegment(e,r,t,i,s,c){return ie(r).pipe(Ze(d=>this.processSegmentAgainstRoute(d._injector??e,r,d,t,i,s,c).pipe(jt(p=>{if(p instanceof si)return D(null);throw p}))),Ye(d=>!!d),jt(d=>{if(Ju(d))return by(t,i,s)?D(new Ho):rn(t);throw d}))}processSegmentAgainstRoute(e,r,t,i,s,c,d){return wy(t,i,s,c)?t.redirectTo===void 0?this.matchSegmentAgainstRoute(e,i,t,s,c):this.allowRedirects&&d?this.expandSegmentAgainstRouteUsingRedirect(e,i,r,t,s,c):rn(i):rn(i)}expandSegmentAgainstRouteUsingRedirect(e,r,t,i,s,c){let{matched:d,consumedSegments:p,positionalParamSegments:f,remainingSegments:v}=Yo(r,i,s);if(!d)return rn(r);i.redirectTo.startsWith("/")&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Cy&&(this.allowRedirects=!1));let I=this.applyRedirects.applyRedirectCommands(p,i.redirectTo,f);return this.applyRedirects.lineralizeSegments(i,I).pipe(ye(E=>this.processSegment(e,t,r,E.concat(v),c,!1)))}matchSegmentAgainstRoute(e,r,t,i,s){let c=fy(r,t,i,e,this.urlSerializer);return t.path==="**"&&(r.children={}),c.pipe(we(d=>d.matched?(e=t._injector??e,this.getChildConfig(e,t,i).pipe(we(({routes:p})=>{let f=t._loadedInjector??e,{consumedSegments:v,remainingSegments:I,parameters:E}=d,O=new ii(v,E,Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,Iy(t),Le(t),t.component??t._loadedComponent??null,t,Dy(t)),{segmentGroup:ee,slicedSegments:Y}=Mu(r,v,I,p);if(Y.length===0&&ee.hasChildren())return this.processChildren(f,p,ee).pipe(k(P=>P===null?null:new Ce(O,P)));if(p.length===0&&Y.length===0)return D(new Ce(O,[]));let L=Le(t)===s;return this.processSegment(f,p,ee,Y,L?M:s,!0).pipe(k(P=>new Ce(O,P instanceof Ce?[P]:[])))}))):rn(r)))}getChildConfig(e,r,t){return r.children?D({routes:r.children,injector:e}):r.loadChildren?r._loadedRoutes!==void 0?D({routes:r._loadedRoutes,injector:r._loadedInjector}):ly(e,r,t,this.urlSerializer).pipe(ye(i=>i?this.configLoader.loadChildren(e,r).pipe(te(s=>{r._loadedRoutes=s.routes,r._loadedInjector=s.injector})):hy(r))):D({routes:[],injector:e})}};function Ey(n){n.sort((e,r)=>e.value.outlet===M?-1:r.value.outlet===M?1:e.value.outlet.localeCompare(r.value.outlet))}function Ay(n){let e=n.value.routeConfig;return e&&e.path===""}function td(n){let e=[],r=new Set;for(let t of n){if(!Ay(t)){e.push(t);continue}let i=e.find(s=>t.value.routeConfig===s.value.routeConfig);i!==void 0?(i.children.push(...t.children),r.add(i)):e.push(t)}for(let t of r){let i=td(t.children);e.push(new Ce(t.value,i))}return e.filter(t=>!r.has(t))}function Iy(n){return n.data||{}}function Dy(n){return n.resolve||{}}function Ty(n,e,r,t,i,s){return ye(c=>_y(n,e,r,t,c.extractedUrl,i,s).pipe(k(({state:d,tree:p})=>W(y({},c),{targetSnapshot:d,urlAfterRedirects:p}))))}function My(n,e){return ye(r=>{let{targetSnapshot:t,guards:{canActivateChecks:i}}=r;if(!i.length)return D(r);let s=new Set(i.map(p=>p.route)),c=new Set;for(let p of s)if(!c.has(p))for(let f of nd(p))c.add(f);let d=0;return ie(c).pipe(Ze(p=>s.has(p)?Sy(p,t,n,e):(p.data=qo(p,p.parent,n).resolve,D(void 0))),te(()=>d++),hs(1),ye(p=>d===c.size?D(r):Ke))})}function nd(n){let e=n.children.map(r=>nd(r)).flat();return[n,...e]}function Sy(n,e,r,t){let i=n.routeConfig,s=n._resolve;return i?.title!==void 0&&!Ku(i)&&(s[oi]=i.title),Ry(s,n,e,t).pipe(k(c=>(n._resolvedData=c,n.data=qo(n,n.parent,r).resolve,null)))}function Ry(n,e,r,t){let i=Co(n);if(i.length===0)return D({});let s={};return ie(i).pipe(ye(c=>xy(n[c],e,r,t).pipe(Ye(),te(d=>{s[c]=d}))),hs(1),ac(s),jt(c=>Ju(c)?Ke:Cn(c)))}function xy(n,e,r,t){let i=li(e)??t,s=hn(n,i),c=s.resolve?s.resolve(e,r):Ne(i,()=>s(e,r));return lt(c)}function bo(n){return we(e=>{let r=n(e);return r?ie(r).pipe(k(()=>e)):D(e)})}var id=(()=>{let e=class e{buildTitle(t){let i,s=t.root;for(;s!==void 0;)i=this.getResolvedTitleForRoute(s)??i,s=s.children.find(c=>c.outlet===M);return i}getResolvedTitleForRoute(t){return t.data[oi]}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:()=>_(ky),providedIn:"root"});let n=e;return n})(),ky=(()=>{let e=class e extends id{constructor(t){super(),this.title=t}updateTitle(t){let i=this.buildTitle(t);i!==void 0&&this.title.setTitle(i)}};e.\u0275fac=function(i){return new(i||e)(w(fl))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),di=new T("",{providedIn:"root",factory:()=>({})}),Lr=new T(""),Xo=(()=>{let e=class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=_(zi)}loadComponent(t){if(this.componentLoaders.get(t))return this.componentLoaders.get(t);if(t._loadedComponent)return D(t._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(t);let i=lt(t.loadComponent()).pipe(k(rd),te(c=>{this.onLoadEndListener&&this.onLoadEndListener(t),t._loadedComponent=c}),yt(()=>{this.componentLoaders.delete(t)})),s=new ls(i,()=>new J).pipe(cs());return this.componentLoaders.set(t,s),s}loadChildren(t,i){if(this.childrenLoaders.get(i))return this.childrenLoaders.get(i);if(i._loadedRoutes)return D({routes:i._loadedRoutes,injector:i._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(i);let c=Oy(i,this.compiler,t,this.onLoadEndListener).pipe(yt(()=>{this.childrenLoaders.delete(i)})),d=new ls(c,()=>new J).pipe(cs());return this.childrenLoaders.set(i,d),d}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();function Oy(n,e,r,t){return lt(n.loadChildren()).pipe(k(rd),ye(i=>i instanceof Sc||Array.isArray(i)?D(i):ie(e.compileModuleAsync(i))),k(i=>{t&&t(n);let s,c,d=!1;return Array.isArray(i)?(c=i,d=!0):(s=i.create(r).injector,c=s.get(Lr,[],{optional:!0,self:!0}).flat()),{routes:c.map(Zo),injector:s}}))}function Py(n){return n&&typeof n=="object"&&"default"in n}function rd(n){return Py(n)?n.default:n}var Qo=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:()=>_(Ny),providedIn:"root"});let n=e;return n})(),Ny=(()=>{let e=class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,i){return t}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),sd=new T(""),od=new T("");function Fy(n,e,r){let t=n.get(od),i=n.get(z);return n.get($).runOutsideAngular(()=>{if(!i.startViewTransition||t.skipNextTransition)return t.skipNextTransition=!1,Promise.resolve();let s,c=new Promise(f=>{s=f}),d=i.startViewTransition(()=>(s(),Ly(n))),{onViewTransitionCreated:p}=t;return p&&Ne(n,()=>p({transition:d,from:e,to:r})),c})}function Ly(n){return new Promise(e=>{Tc(e,{injector:n})})}var Jo=(()=>{let e=class e{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new J,this.transitionAbortSubject=new J,this.configLoader=_(Xo),this.environmentInjector=_(wt),this.urlSerializer=_(ai),this.rootContexts=_(ci),this.location=_(Rn),this.inputBindingEnabled=_(jr,{optional:!0})!==null,this.titleStrategy=_(id),this.options=_(di,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=_(Qo),this.createViewTransition=_(sd,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>D(void 0),this.rootComponentType=null;let t=s=>this.events.next(new Ro(s)),i=s=>this.events.next(new xo(s));this.configLoader.onLoadEndListener=i,this.configLoader.onLoadStartListener=t}complete(){this.transitions?.complete()}handleNavigationRequest(t){let i=++this.navigationId;this.transitions?.next(W(y(y({},this.transitions.value),t),{id:i}))}setupNavigations(t,i,s){return this.transitions=new he({id:0,currentUrlTree:i,currentRawUrl:i,extractedUrl:this.urlHandlingStrategy.extract(i),urlAfterRedirects:this.urlHandlingStrategy.extract(i),rawUrl:i,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:Yn,restoredState:null,currentSnapshot:s.snapshot,targetSnapshot:null,currentRouterState:s,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Me(c=>c.id!==0),k(c=>W(y({},c),{extractedUrl:this.urlHandlingStrategy.extract(c.rawUrl)})),we(c=>{let d=!1,p=!1;return D(c).pipe(we(f=>{if(this.navigationId>c.id)return this.cancelNavigationTransition(c,"",Ee.SupersededByNewNavigation),Ke;this.currentTransition=c,this.currentNavigation={id:f.id,initialUrl:f.rawUrl,extractedUrl:f.extractedUrl,trigger:f.source,extras:f.extras,previousNavigation:this.lastSuccessfulNavigation?W(y({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let v=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),I=f.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!v&&I!=="reload"){let E="";return this.events.next(new ct(f.id,this.urlSerializer.serialize(f.rawUrl),E,Sr.IgnoredSameUrlNavigation)),f.resolve(null),Ke}if(this.urlHandlingStrategy.shouldProcessUrl(f.rawUrl))return D(f).pipe(we(E=>{let O=this.transitions?.getValue();return this.events.next(new un(E.id,this.urlSerializer.serialize(E.extractedUrl),E.source,E.restoredState)),O!==this.transitions?.getValue()?Ke:Promise.resolve(E)}),Ty(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),te(E=>{c.targetSnapshot=E.targetSnapshot,c.urlAfterRedirects=E.urlAfterRedirects,this.currentNavigation=W(y({},this.currentNavigation),{finalUrl:E.urlAfterRedirects});let O=new Rr(E.id,this.urlSerializer.serialize(E.extractedUrl),this.urlSerializer.serialize(E.urlAfterRedirects),E.targetSnapshot);this.events.next(O)}));if(v&&this.urlHandlingStrategy.shouldProcessUrl(f.currentRawUrl)){let{id:E,extractedUrl:O,source:ee,restoredState:Y,extras:L}=f,P=new un(E,this.urlSerializer.serialize(O),ee,Y);this.events.next(P);let pe=Gu(this.rootComponentType).snapshot;return this.currentTransition=c=W(y({},f),{targetSnapshot:pe,urlAfterRedirects:O,extras:W(y({},L),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=O,D(c)}else{let E="";return this.events.next(new ct(f.id,this.urlSerializer.serialize(f.extractedUrl),E,Sr.IgnoredByUrlHandlingStrategy)),f.resolve(null),Ke}}),te(f=>{let v=new Do(f.id,this.urlSerializer.serialize(f.extractedUrl),this.urlSerializer.serialize(f.urlAfterRedirects),f.targetSnapshot);this.events.next(v)}),k(f=>(this.currentTransition=c=W(y({},f),{guards:Wv(f.targetSnapshot,f.currentSnapshot,this.rootContexts)}),c)),ty(this.environmentInjector,f=>this.events.next(f)),te(f=>{if(c.guardsResult=f.guardsResult,ln(f.guardsResult))throw Yu(this.urlSerializer,f.guardsResult);let v=new To(f.id,this.urlSerializer.serialize(f.extractedUrl),this.urlSerializer.serialize(f.urlAfterRedirects),f.targetSnapshot,!!f.guardsResult);this.events.next(v)}),Me(f=>f.guardsResult?!0:(this.cancelNavigationTransition(f,"",Ee.GuardRejected),!1)),bo(f=>{if(f.guards.canActivateChecks.length)return D(f).pipe(te(v=>{let I=new Mo(v.id,this.urlSerializer.serialize(v.extractedUrl),this.urlSerializer.serialize(v.urlAfterRedirects),v.targetSnapshot);this.events.next(I)}),we(v=>{let I=!1;return D(v).pipe(My(this.paramsInheritanceStrategy,this.environmentInjector),te({next:()=>I=!0,complete:()=>{I||this.cancelNavigationTransition(v,"",Ee.NoDataFromResolver)}}))}),te(v=>{let I=new So(v.id,this.urlSerializer.serialize(v.extractedUrl),this.urlSerializer.serialize(v.urlAfterRedirects),v.targetSnapshot);this.events.next(I)}))}),bo(f=>{let v=I=>{let E=[];I.routeConfig?.loadComponent&&!I.routeConfig._loadedComponent&&E.push(this.configLoader.loadComponent(I.routeConfig).pipe(te(O=>{I.component=O}),k(()=>{})));for(let O of I.children)E.push(...v(O));return E};return Vt(v(f.targetSnapshot.root)).pipe(ds(null),Pe(1))}),bo(()=>this.afterPreactivation()),we(()=>{let{currentSnapshot:f,targetSnapshot:v}=c,I=this.createViewTransition?.(this.environmentInjector,f.root,v.root);return I?ie(I).pipe(k(()=>c)):D(c)}),k(f=>{let v=Lv(t.routeReuseStrategy,f.targetSnapshot,f.currentRouterState);return this.currentTransition=c=W(y({},f),{targetRouterState:v}),this.currentNavigation.targetRouterState=v,c}),te(()=>{this.events.next(new ti)}),Hv(this.rootContexts,t.routeReuseStrategy,f=>this.events.next(f),this.inputBindingEnabled),Pe(1),te({next:f=>{d=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Re(f.id,this.urlSerializer.serialize(f.extractedUrl),this.urlSerializer.serialize(f.urlAfterRedirects))),this.titleStrategy?.updateTitle(f.targetRouterState.snapshot),f.resolve(!0)},complete:()=>{d=!0}}),Bt(this.transitionAbortSubject.pipe(te(f=>{throw f}))),yt(()=>{!d&&!p&&this.cancelNavigationTransition(c,"",Ee.SupersededByNewNavigation),this.currentTransition?.id===c.id&&(this.currentNavigation=null,this.currentTransition=null)}),jt(f=>{if(p=!0,Qu(f))this.events.next(new at(c.id,this.urlSerializer.serialize(c.extractedUrl),f.message,f.cancellationCode)),Uv(f)?this.events.next(new ni(f.url)):c.resolve(!1);else{this.events.next(new ei(c.id,this.urlSerializer.serialize(c.extractedUrl),f,c.targetSnapshot??void 0));try{c.resolve(t.errorHandler(f))}catch(v){this.options.resolveNavigationPromiseOnError?c.resolve(!1):c.reject(v)}}return Ke}))}))}cancelNavigationTransition(t,i,s){let c=new at(t.id,this.urlSerializer.serialize(t.extractedUrl),i,s);this.events.next(c),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){return this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))).toString()!==this.currentTransition?.extractedUrl.toString()&&!this.currentTransition?.extras.skipLocationChange}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();function Vy(n){return n!==Yn}var jy=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:()=>_(Uy),providedIn:"root"});let n=e;return n})(),Go=class{shouldDetach(e){return!1}store(e,r){}shouldAttach(e){return!1}retrieve(e){return null}shouldReuseRoute(e,r){return e.routeConfig===r.routeConfig}},Uy=(()=>{let e=class e extends Go{};e.\u0275fac=(()=>{let t;return function(s){return(t||(t=Qe(e)))(s||e)}})(),e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),ad=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:()=>_(By),providedIn:"root"});let n=e;return n})(),By=(()=>{let e=class e extends ad{constructor(){super(...arguments),this.location=_(Rn),this.urlSerializer=_(ai),this.options=_(di,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=_(Qo),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new ot,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=Gu(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(i=>{i.type==="popstate"&&t(i.url,i.state)})}handleRouterEvent(t,i){if(t instanceof un)this.stateMemento=this.createStateMemento();else if(t instanceof ct)this.rawUrlTree=i.initialUrl;else if(t instanceof Rr){if(this.urlUpdateStrategy==="eager"&&!i.extras.skipLocationChange){let s=this.urlHandlingStrategy.merge(i.finalUrl,i.initialUrl);this.setBrowserUrl(s,i)}}else t instanceof ti?(this.currentUrlTree=i.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(i.finalUrl,i.initialUrl),this.routerState=i.targetRouterState,this.urlUpdateStrategy==="deferred"&&(i.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,i))):t instanceof at&&(t.code===Ee.GuardRejected||t.code===Ee.NoDataFromResolver)?this.restoreHistory(i):t instanceof ei?this.restoreHistory(i,!0):t instanceof Re&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,i){let s=this.urlSerializer.serialize(t);if(this.location.isCurrentPathEqualTo(s)||i.extras.replaceUrl){let c=this.browserPageId,d=y(y({},i.extras.state),this.generateNgRouterState(i.id,c));this.location.replaceState(s,"",d)}else{let c=y(y({},i.extras.state),this.generateNgRouterState(i.id,this.browserPageId+1));this.location.go(s,"",c)}}restoreHistory(t,i=!1){if(this.canceledNavigationResolution==="computed"){let s=this.browserPageId,c=this.currentPageId-s;c!==0?this.location.historyGo(c):this.currentUrlTree===t.finalUrl&&c===0&&(this.resetState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(i&&this.resetState(t),this.resetUrlToCurrentUrlTree())}resetState(t){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,i){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:i}:{navigationId:t}}};e.\u0275fac=(()=>{let t;return function(s){return(t||(t=Qe(e)))(s||e)}})(),e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),Kn=function(n){return n[n.COMPLETE=0]="COMPLETE",n[n.FAILED=1]="FAILED",n[n.REDIRECTING=2]="REDIRECTING",n}(Kn||{});function cd(n,e){n.events.pipe(Me(r=>r instanceof Re||r instanceof at||r instanceof ei||r instanceof ct),k(r=>r instanceof Re||r instanceof ct?Kn.COMPLETE:(r instanceof at?r.code===Ee.Redirect||r.code===Ee.SupersededByNewNavigation:!1)?Kn.REDIRECTING:Kn.FAILED),Me(r=>r!==Kn.REDIRECTING),Pe(1)).subscribe(()=>{e()})}function $y(n){throw n}var zy={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},Hy={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},We=(()=>{let e=class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.isNgZoneEnabled=!1,this.console=_(Vi),this.stateManager=_(ad),this.options=_(di,{optional:!0})||{},this.pendingTasks=_(Mn),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=_(Jo),this.urlSerializer=_(ai),this.location=_(Rn),this.urlHandlingStrategy=_(Qo),this._events=new J,this.errorHandler=this.options.errorHandler||$y,this.navigated=!1,this.routeReuseStrategy=_(jy),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=_(Lr,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!_(jr,{optional:!0}),this.eventsSubscription=new Ii,this.isNgZoneEnabled=_($)instanceof $&&$.isInAngularZone(),this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(i=>{try{let s=this.navigationTransitions.currentTransition,c=this.navigationTransitions.currentNavigation;if(s!==null&&c!==null){if(this.stateManager.handleRouterEvent(i,c),i instanceof at&&i.code!==Ee.Redirect&&i.code!==Ee.SupersededByNewNavigation)this.navigated=!0;else if(i instanceof Re)this.navigated=!0;else if(i instanceof ni){let d=this.urlHandlingStrategy.merge(i.url,s.currentRawUrl),p={info:s.extras.info,skipLocationChange:s.extras.skipLocationChange,replaceUrl:this.urlUpdateStrategy==="eager"||Vy(s.source)};this.scheduleNavigation(d,Yn,null,p,{resolve:s.resolve,reject:s.reject,promise:s.promise})}}Gy(i)&&this._events.next(i)}catch(s){this.navigationTransitions.transitionAbortSubject.next(s)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Yn,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,i)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(t,"popstate",i)},0)})}navigateToSyncWithBrowser(t,i,s){let c={replaceUrl:!0},d=s?.navigationId?s:null;if(s){let f=y({},s);delete f.navigationId,delete f.\u0275routerPageId,Object.keys(f).length!==0&&(c.state=f)}let p=this.parseUrl(t);this.scheduleNavigation(p,i,d,c)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(Zo),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,i={}){let{relativeTo:s,queryParams:c,fragment:d,queryParamsHandling:p,preserveFragment:f}=i,v=f?this.currentUrlTree.fragment:d,I=null;switch(p){case"merge":I=y(y({},this.currentUrlTree.queryParams),c);break;case"preserve":I=this.currentUrlTree.queryParams;break;default:I=c||null}I!==null&&(I=this.removeEmptyProps(I));let E;try{let O=s?s.snapshot:this.routerState.snapshot.root;E=$u(O)}catch{(typeof t[0]!="string"||!t[0].startsWith("/"))&&(t=[]),E=this.currentUrlTree.root}return zu(E,t,I,v??null)}navigateByUrl(t,i={skipLocationChange:!1}){let s=ln(t)?t:this.parseUrl(t),c=this.urlHandlingStrategy.merge(s,this.rawUrlTree);return this.scheduleNavigation(c,Yn,null,i)}navigate(t,i={skipLocationChange:!1}){return Wy(t),this.navigateByUrl(this.createUrlTree(t,i),i)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,i){let s;if(i===!0?s=y({},zy):i===!1?s=y({},Hy):s=i,ln(t))return Eu(this.currentUrlTree,t,s);let c=this.parseUrl(t);return Eu(this.currentUrlTree,c,s)}removeEmptyProps(t){return Object.entries(t).reduce((i,[s,c])=>(c!=null&&(i[s]=c),i),{})}scheduleNavigation(t,i,s,c,d){if(this.disposed)return Promise.resolve(!1);let p,f,v;d?(p=d.resolve,f=d.reject,v=d.promise):v=new Promise((E,O)=>{p=E,f=O});let I=this.pendingTasks.add();return cd(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(I))}),this.navigationTransitions.handleNavigationRequest({source:i,restoredState:s,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:c,resolve:p,reject:f,promise:v,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),v.catch(E=>Promise.reject(E))}};e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();function Wy(n){for(let e=0;e<n.length;e++)if(n[e]==null)throw new B(4008,!1)}function Gy(n){return!(n instanceof ti)&&!(n instanceof ni)}var Su=(()=>{let e=class e{constructor(t,i,s,c,d,p){this.router=t,this.route=i,this.tabIndexAttribute=s,this.renderer=c,this.el=d,this.locationStrategy=p,this.href=null,this.commands=null,this.onChanges=new J,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1;let f=d.nativeElement.tagName?.toLowerCase();this.isAnchorElement=f==="a"||f==="area",this.isAnchorElement?this.subscription=t.events.subscribe(v=>{v instanceof Re&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(t){t!=null?(this.commands=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0")):(this.commands=null,this.setTabIndexIfNotOnNativeEl(null))}onClick(t,i,s,c,d){let p=this.urlTree;if(p===null||this.isAnchorElement&&(t!==0||i||s||c||d||typeof this.target=="string"&&this.target!="_self"))return!0;let f={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(p,f),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.href=t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t)):null;let i=this.href===null?null:Ac(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",i)}applyAttributeValue(t,i){let s=this.renderer,c=this.el.nativeElement;i!==null?s.setAttribute(c,t,i):s.removeAttribute(c,t)}get urlTree(){return this.commands===null?null:this.router.createUrlTree(this.commands,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}};e.\u0275fac=function(i){return new(i||e)(C(We),C(Rt),pc("tabindex"),C(Gt),C(re),C(Sn))},e.\u0275dir=V({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(i,s){i&1&&Be("click",function(d){return s.onClick(d.button,d.ctrlKey,d.shiftKey,d.altKey,d.metaKey)}),i&2&&_t("target",s.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[F.HasDecoratorInputTransform,"preserveFragment","preserveFragment",_e],skipLocationChange:[F.HasDecoratorInputTransform,"skipLocationChange","skipLocationChange",_e],replaceUrl:[F.HasDecoratorInputTransform,"replaceUrl","replaceUrl",_e],routerLink:"routerLink"},standalone:!0,features:[Je,Ie]});let n=e;return n})(),v_=(()=>{let e=class e{get isActive(){return this._isActive}constructor(t,i,s,c,d){this.router=t,this.element=i,this.renderer=s,this.cdr=c,this.link=d,this.classes=[],this._isActive=!1,this.routerLinkActiveOptions={exact:!1},this.isActiveChange=new Z,this.routerEventsSubscription=t.events.subscribe(p=>{p instanceof Re&&this.update()})}ngAfterContentInit(){D(this.links.changes,D(null)).pipe(En()).subscribe(t=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let t=[...this.links.toArray(),this.link].filter(i=>!!i).map(i=>i.onChanges);this.linkInputChangesSubscription=ie(t).pipe(En()).subscribe(i=>{this._isActive!==this.isLinkActive(this.router)(i)&&this.update()})}set routerLinkActive(t){let i=Array.isArray(t)?t:t.split(" ");this.classes=i.filter(s=>!!s)}ngOnChanges(t){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let t=this.hasActiveLinks();this._isActive!==t&&(this._isActive=t,this.cdr.markForCheck(),this.classes.forEach(i=>{t?this.renderer.addClass(this.element.nativeElement,i):this.renderer.removeClass(this.element.nativeElement,i)}),t&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this.isActiveChange.emit(t))})}isLinkActive(t){let i=qy(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return s=>{let c=s.urlTree;return c?t.isActive(c,i):!1}}hasActiveLinks(){let t=this.isLinkActive(this.router);return this.link&&t(this.link)||this.links.some(t)}};e.\u0275fac=function(i){return new(i||e)(C(We),C(re),C(Gt),C(tt),C(Su,8))},e.\u0275dir=V({type:e,selectors:[["","routerLinkActive",""]],contentQueries:function(i,s,c){if(i&1&&xc(c,Su,5),i&2){let d;Fi(d=Li())&&(s.links=d)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],standalone:!0,features:[Ie]});let n=e;return n})();function qy(n){return!!n.paths}var Vr=class{};var Ky=(()=>{let e=class e{constructor(t,i,s,c,d){this.router=t,this.injector=s,this.preloadingStrategy=c,this.loader=d}setUpPreloading(){this.subscription=this.router.events.pipe(Me(t=>t instanceof Re),Ze(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,i){let s=[];for(let c of i){c.providers&&!c._injector&&(c._injector=vs(c.providers,t,`Route: ${c.path}`));let d=c._injector??t,p=c._loadedInjector??d;(c.loadChildren&&!c._loadedRoutes&&c.canLoad===void 0||c.loadComponent&&!c._loadedComponent)&&s.push(this.preloadConfig(d,c)),(c.children||c._loadedRoutes)&&s.push(this.processRoutes(p,c.children??c._loadedRoutes))}return ie(s).pipe(En())}preloadConfig(t,i){return this.preloadingStrategy.preload(i,()=>{let s;i.loadChildren&&i.canLoad===void 0?s=this.loader.loadChildren(t,i):s=D(null);let c=s.pipe(ye(d=>d===null?D(void 0):(i._loadedRoutes=d.routes,i._loadedInjector=d.injector,this.processRoutes(d.injector??t,d.routes))));if(i.loadComponent&&!i._loadedComponent){let d=this.loader.loadComponent(i);return ie([c,d]).pipe(En())}else return c})}};e.\u0275fac=function(i){return new(i||e)(w(We),w(zi),w(wt),w(Vr),w(Xo))},e.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})(),ld=new T(""),Zy=(()=>{let e=class e{constructor(t,i,s,c,d={}){this.urlSerializer=t,this.transitions=i,this.viewportScroller=s,this.zone=c,this.options=d,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},d.scrollPositionRestoration||="disabled",d.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof un?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=t.navigationTrigger,this.restoredId=t.restoredState?t.restoredState.navigationId:0):t instanceof Re?(this.lastId=t.id,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.urlAfterRedirects).fragment)):t instanceof ct&&t.code===Sr.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof xr&&(t.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(t.position):t.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(t.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(t,i){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new xr(t,this.lastSource==="popstate"?this.store[this.restoredId]:null,i))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}};e.\u0275fac=function(i){Ic()},e.\u0275prov=A({token:e,factory:e.\u0275fac});let n=e;return n})();function Yy(n){return n.routerState.root}function hi(n,e){return{\u0275kind:n,\u0275providers:e}}function Xy(){let n=_(Ri);return e=>{let r=n.get($i);if(e!==r.components[0])return;let t=n.get(We),i=n.get(ud);n.get(ea)===1&&t.initialNavigation(),n.get(dd,null,fs.Optional)?.setUpPreloading(),n.get(ld,null,fs.Optional)?.init(),t.resetRootComponentType(r.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var ud=new T("",{factory:()=>new J}),ea=new T("",{providedIn:"root",factory:()=>1});function Qy(){return hi(2,[{provide:ea,useValue:0},{provide:Es,multi:!0,deps:[Ri],useFactory:e=>{let r=e.get(zc,Promise.resolve());return()=>r.then(()=>new Promise(t=>{let i=e.get(We),s=e.get(ud);cd(i,()=>{t(!0)}),e.get(Jo).afterPreactivation=()=>(t(!0),s.closed?D(void 0):s),i.initialNavigation()}))}}])}function Jy(){return hi(3,[{provide:Es,multi:!0,useFactory:()=>{let e=_(We);return()=>{e.setUpLocationChangeListener()}}},{provide:ea,useValue:2}])}var dd=new T("");function ew(n){return hi(0,[{provide:dd,useExisting:Ky},{provide:Vr,useExisting:n}])}function tw(){return hi(8,[Tu,{provide:jr,useExisting:Tu}])}function nw(n){let e=[{provide:sd,useValue:Fy},{provide:od,useValue:y({skipNextTransition:!!n?.skipInitialTransition},n)}];return hi(9,e)}var Ru=new T("ROUTER_FORROOT_GUARD"),iw=[Rn,{provide:ai,useClass:Qn},We,ci,{provide:Rt,useFactory:Yy,deps:[We]},Xo,[]],y_=(()=>{let e=class e{constructor(t){}static forRoot(t,i){return{ngModule:e,providers:[iw,[],{provide:Lr,multi:!0,useValue:t},{provide:Ru,useFactory:aw,deps:[[We,new ps,new hc]]},{provide:di,useValue:i||{}},i?.useHash?sw():ow(),rw(),i?.preloadingStrategy?ew(i.preloadingStrategy).\u0275providers:[],i?.initialNavigation?cw(i):[],i?.bindToComponentInputs?tw().\u0275providers:[],i?.enableViewTransitions?nw().\u0275providers:[],lw()]}}static forChild(t){return{ngModule:e,providers:[{provide:Lr,multi:!0,useValue:t}]}}};e.\u0275fac=function(i){return new(i||e)(w(Ru,8))},e.\u0275mod=q({type:e}),e.\u0275inj=G({});let n=e;return n})();function rw(){return{provide:ld,useFactory:()=>{let n=_(Zc),e=_($),r=_(di),t=_(Jo),i=_(ai);return r.scrollOffset&&n.setOffset(r.scrollOffset),new Zy(i,t,n,e,r)}}}function sw(){return{provide:Sn,useClass:Wc}}function ow(){return{provide:Sn,useClass:Hc}}function aw(n){return"guarded"}function cw(n){return[n.initialNavigation==="disabled"?Jy().\u0275providers:[],n.initialNavigation==="enabledBlocking"?Qy().\u0275providers:[]]}var xu=new T("");function lw(){return[{provide:xu,useFactory:Xy},{provide:As,multi:!0,useExisting:xu}]}var __={production:!1,apiUrl:"https://karpets.in/api",imageUrl:"https://karpets.in/public/all-files/"};export{nt as a,lp as b,Aw as c,ol as d,Lw as e,Vw as f,Kp as g,Rt as h,Fv as i,We as j,Su as k,v_ as l,y_ as m,$n as n,Al as o,rt as p,pl as q,ze as r,nb as s,ib as t,bm as u,Un as v,Am as w,sb as x,Dm as y,Mm as z,Bl as A,$l as B,zl as C,Om as D,Lm as E,ob as F,ab as G,cb as H,__ as I,uw as J,Um as K,Js as L,Bm as M,eo as N,_b as O,He as P,Cb as Q,fe as R,gb as S,tn as T,Hn as U,vb as V,yb as W,Kl as X,st as Y,Zl as Z,fr as _,Ob as $,Xl as aa,ug as ba,dg as ca,o0 as da,nu as ea,iu as fa,hg as ga,Eg as ha,a0 as ia,c0 as ja,kg as ka,l0 as la,u0 as ma,_0 as na,co as oa,$0 as pa,z0 as qa,br as ra,du as sa,hu as ta,_r as ua,Xg as va,H0 as wa,W0 as xa,Cu as ya,iv as za,ov as Aa,av as Ba,go as Ca,G0 as Da,q0 as Ea,K0 as Fa,Z0 as Ga,Y0 as Ha,X0 as Ia};
