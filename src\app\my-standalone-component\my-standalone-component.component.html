<button (click)="generatePDF()" *ngIf="!isPrint" class="no-print">
  Generate PDF
</button>
<section class="foot1" id="contentToConvert" style="margin-left: 50px;margin-right: 50px;">
  <div class="container">
    <div class="row" style="margin-bottom: 245px; ">
      <div class="col-md-12 " style="margin-top:30px !important;">
        <div>
          <img
            src="../../assets/auth-assets/images/Untitled-3.png"
            alt=""
            class="img-fluid"
          />
        </div>
      </div>

      <div class="mt-5">
        <div class="col-md-12 d-flex">
          <div class="col-md-8">
            <h5 class="font1 h5txt" >{{ _bill.customer }}</h5>
            <h5 class="font1 h5txt" >{{ _bill.street }},{{ _bill.zipCode }}</h5>
            <h5 class="font1 h5txt" >{{ _bill.country }}</h5>
          </div>
          <!-- <div class="col-md-4">
            <div class="">
              <h5 style="color: rgb(85, 84, 84)" class="fs-4">
                Liferanschrift:
              </h5>
              <h5 class="fs-4 mt-3">{{ _bill.customer }}</h5>
              <h5 class="fs-4">{{ _bill.street }},{{ _bill.zipCode }}</h5>
              <h5 class="fs-4">{{ _bill.country }}</h5>
            </div>
          </div> -->
        </div>
      </div>

      <div class="col-md-12 mt d-flex justify-content-between">
        <div class="card card1">
          <div class="hd-text">
            <h4 style="font-weight: bold; font-size: 27px" class="pl-4">
              RECHNUNG {{ _bill.billNo }}
            </h4>
          </div>
        </div>
        <div class="card card1 mt-5">
          <div class="text-hd-n">
            <p>
              Datum <br />
             <span style="margin-top: 10px !important; display: inline-block;"> {{ _bill.date }}</span>
            </p>
            <p>Seite <br />
             <span style="margin-top: 10px !important; display: inline-block;"> 1 VON 1</span>

              </p>
            <p>Auftrags-Nr. <br />
              <span style="margin-top: 10px !important; display: inline-block;"> 10125298</span>
              </p>
            <p>Kunden<br />
              <span style="margin-top: 10px !important; display: inline-block;">31001</span>
              </p>
            <p>SB <br />             <span style="margin-top: 10px !important; display: inline-block;"> HS</span>
              </p>
          </div>
        </div>
      </div>
      <div class="col-md-12 mt-3 d-flex justify-content-between">
        <div class=" card1 line1">
          <ul>
            <li class="textsize">Pos. ID-Nr</li>
            <li class="textsize">Artikelbezeichnung</li>
          </ul>
        </div>
        <div class=" card1 line1">
          <ul>
            <li class="textsize">Menge.</li>
            <li class="textsize">Einh.</li>

            <li class="textsize">Einzel-Pr.</li>
            <li class="textsize">Gesamt-Pr.</li>
          </ul>
        </div>
      </div>

         <span class="line"> </span>

      <div class="col-md-12 d-flex justify-content-between mt-1">

        <div class=" card1 line1">
          <ul>
            <li class="textsize">1</li>
            <li class="textsize">600</li>
            <li class="textsize">UMLAGERUNGSRECHNUNG {{ _bill.date }}</li>
          </ul>
        </div>
        <div class=" card1 bill line1">
          <ul>
            <li class="textsize">1,00</li>
            <li class="textsize">ST</li>
            <li class="textsize">
              {{ totalCalculation.totalAmount | number : "1.2-2" }}
            </li>
            <li class="textsize">
              {{ totalCalculation.totalAmount | number : "1.2-2" }}
            </li>
          </ul>
        </div>
      </div>



      <div class="d-flex">
        <div class="col-md-8"></div>
        <div
          class="col-md-4 d-flex justify-content-end"
          style="text-align: end; padding-right: 0px"
        >
          <div class="invoice-table">
            <table class="table-colour">
              <tr>
                <td class="font-weight-bold text-lg textsize">
                  Gesamtpreis Netto EUR
                </td>
                <td class="right-align line-above fw-bold textsize">
                  {{ totalCalculation.totalAmount | number : "1.2-2" }}
                </td>
              </tr>
              <tr>
                <td class="textsize">Profitieren</td>
                <td class="right-align textsize">
                  {{ totalCalculation.profit | number : "1.2-2" }}
                </td>
              </tr>
              <tr>
                <td class="textsize">zzgl. 19% MwSt EUR</td>
                <td class="right-align textsize">
                  {{ totalCalculation.gstAmt | number : "1.2-2" }}
                </td>
              </tr>
              <tr>
                <td class="fw-bold textsize">Gesamtpreis Brutto EUR</td>
                <td class="right-align line-above line-bottom fw-bold textsize">
                  {{ totalCalculation.grossAmt | number : "1.2-2" }}
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>

      <div class="col-md-12 mt-5 mb-5">
        <P>Ihr Ansprechpartner: OHNE KEINE BETREUUNG</P>
        <p>Wenn nicht gesondert angegeben, gilt: Rechnungsdatum gleich Liefer-/ Leistungsdatum.
        </p>
        <p>Die Lieferung erfolgt zu unseren Allgemainen Geschaftsbedingungen, einzusehen auf unserer Internetseite.</p>
        <p>
          Auf Wunsch senden wir Ihnen diese gern zu.
        </p>
        <h4 style="color: rgb(92, 92, 92); font-size: 20px; font-weight: bold;" class="mb-5">
          Bzgl der Entgeltminderungen verweisen wir auf die aktuellen Zahlungs-
          und Konditionsvereinbarungen.
        </h4>
      </div>

      <!-- <div class="col-md-12 space1"></div> -->
    </div>
    <div class="row">
      <!-- <span class="line"></span> -->
      <!-- <hr style="border: 1px solid; width: 100;  margin: 0px auto;"> -->
      <!-- <div
        class="col-md-12"

      > -->
        <!-- <div class="footer">
          <h6 class="textsize">Liefermenge:</h6>
          <h6 class="textsize">0,00 Stuck</h6>
          <h6 class="textsize">0,00 m2</h6>
          <h6 class="textsize">0,00 KG</h6>
        </div>
      </div>
      <span class="line"></span> -->

      <div class="col-md-12 mt-3 text-center">
        <img
          src="../../assets/auth-assets/images/Footer set file.png"
          alt=""
          class="img-fluid"
        />
      </div>
    </div>

    <div class="container mt-5">
      <div class="col-md-12 bb">
        <div class="card card1">
          <div class="second-page11 mt-5">
            <div class="second-page">
              <h5 style="color: rgb(85, 84, 84)" class="textsize ">
                Rechnungsempfanger :
              </h5>
              <h5 class="fs-4 h5txt" style="margin-bottom: 3px">
                {{ _bill.customer }}
              </h5>
              <h5 class="fs-4 h5txt" style="margin-bottom: 3px">
                {{ _bill.street }},{{ _bill.zipCode }}
              </h5>
              <h5 class="fs-4 h5txt" style="margin-bottom: 3px">
                {{ _bill.country }}
              </h5>
            </div>
            <div class="second-page1">
              <h5 style="color: rgb(85, 84, 84)" class="fs-4">
                Rechnungssteller:
              </h5>
              <h5 class="fs-4 h5txt" style="margin-bottom: 3px">K.O.T.I. GmbH</h5>
              <h5 class="fs-4 h5txt" style="margin-bottom: 3px">Stockum 2 a</h5>
              <h5 class="fs-4 h5txt" style="margin-bottom: 3px">48653 Coesfeld</h5>
              <h5 class="fs-4 h5txt"  style="margin-bottom: 3px">Deutschland</h5>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="container mt-3">
      <div class="row">
        <div class="col-md-12">
          <div class="card card1">
            <div class="card-text">
              <div class="second-text">
                <h4 style="font-weight: bold; font-size: 27px">
                  ANLAGE ZUR RECHNUNG {{ _bill.billNo }}
                </h4>
                <h5 class="fs-5" style="font-weight: bold">
                  Abrechnung der Umlagerung Vom {{ _bill.date2 }} bis {{ _bill.date }}
                </h5>
                <h5 class="fs-5" style="font-weight: bold">
                  Berechnet mit einem Aufschlag Von: 0%
                </h5>
              </div>
              <div class="second-text22">
                <div class="second-text2">
                  <h5 class="fs-5 textsize">Datum</h5>
                  <h5 class="fs-5 textsize">{{ _bill.date2 }}</h5>
                </div>
                <div class="second-text1">
                  <h5 class="fs-5 textsize">Seite</h5>
                  <h5 class="fs-5 textsize">1 Von </h5>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-12">
          <div class="counter-heading-text">
            <div class="counter-heading-text1">
              <h5 class="fs-5 textsize">Artikel-Nr.</h5>
            </div>
            <div class="counter-heading-text2">
              <h5 class="fs-5 textsize">Bezeichnung</h5>
            </div>
            <div class="counter-heading-text3">
              <p class="textsize" style="text-align: end">Menge Einh.</p>
            </div>
            <div class="counter-heading-text4">
              <p class="textsize" style="text-align: end">Einzel-Pr.</p>
            </div>
            <div class="counter-heading-text5">
              <p class="textsize" style="text-align: end">Gesamt-Pr.</p>
            </div>
          </div>
        </div>
        <span class="line"></span>

        <div class="col-md-12 mt-2 " *ngFor="let item of billDetails">
          <div class="counter-heading-text">
            <div class="counter-heading-text1">
              <h5 style="font-size: 18px">
                {{ item.carpetNo }}
              </h5>
            </div>
            <div class="counter-heading-text2">
              <!-- <h5>RG:{{item.challanNo}} vom  {{item.challanDate}} an  {{item.customer}}</h5> -->
              <div *ngIf="item.challanNo">
                <h5
                  style="
                    font-weight: bold;
                    font-size: 18px;
                    font-style: italic;
                    margin-bottom: 10px;
                  "
                >
                  RG:{{ item.challanNo }} vom {{ item.challanDate }} an
                  {{ item.customer }}
                </h5>
              </div>
              <P style="margin-bottom: 10px; font-size: 18px">{{
                item.qualityDesign
              }}</P>
              <p style="margin-bottom: 10px; font-size: 18px">
                {{ item.qualityCode }}, {{ item.colourCode }} {{ item.colour }}
              </p>
              <p style="margin-bottom: 10px; font-size: 18px">
                {{ item.size }} cm
              </p>
            </div>
            <div class="counter-heading-text3">
              <p style="margin-top: 35px; font-size: 18px; text-align: end">
                {{ item.area | number : "1.2-2" }} QM
              </p>
            </div>
            <div class="counter-heading-text4">
              <p style="margin-top: 35px; font-size: 18px; text-align: end">
                {{ item.evkPrice | number : "1.2-2" }}
              </p>
            </div>

            <div class="counter-heading-text5">
              <p style="margin-top: 35px; font-size: 18px; text-align: end">
                {{ item.amount | number : "1.2-2" }}
              </p>
            </div>
          </div>
        </div>

        <span class="mt-3"></span>

        <!-- last-counter -->
        <div class="col-md-12 last-counter1">
          <div class="col-md-8 mt-4">
            <h6 class="fs-4" style="font-weight: bolder">
              Umlagerungen von Filiale3 zu Filiale 1
            </h6>
          </div>
          <div
            class="col-md-4 d-flex justify-content-end"
            style="text-align: end; padding-right: 0px"
          >
            <div class="invoice-table">
              <table class="table-colour">
                <tr>
                  <td class="font-weight-bold text-lg">
                    Gesamtpreis Netto EUR
                  </td>
                  <td class="right-align fw-bold">
                    {{ totalCalculation.totalAmount | number : "1.2-2" }}
                  </td>
                </tr>
                <tr>
                  <td class="">Profitieren</td>
                  <td class="right-align">
                    {{ totalCalculation.profit | number : "1.2-2" }}
                  </td>
                </tr>
                <tr>
                  <td>zzgl. 19% MwSt EUR</td>
                  <td class="right-align">
                    {{ totalCalculation.gstAmt | number : "1.2-2" }}
                  </td>
                </tr>
                <tr>
                  <td class="fw-bold">Gesamtpreis Brutto EUR</td>
                  <td class="right-align line-above line-bottom fw-bold">
                    {{ totalCalculation.grossAmt | number : "1.2-2" }}
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
