<div class="container mt-5">
  <section>
    <fieldset>
      <legend><b>{{isUpdated?'Update':'Add'}} Map Rate</b></legend>
      <form [formGroup]="frmMapRate" (ngSubmit)="addMapRate()">
        <div class="row">
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Name</mat-label>
              <mat-select formControlName="name">
                <mat-option
                  *ngFor="let designer of designerList"
                  value="{{ designer._id }}"
                >
                  {{ designer.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>To Date</mat-label>
              <input
                matInput
                [matDatepicker]="picker"
                formControlName="toDate"
              />
              <mat-datepicker-toggle
                matIconSuffix
                [for]="picker"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>From Date</mat-label>
              <input
                matInput
                [matDatepicker]="picker1"
                formControlName="fromDate"
              />

              <mat-datepicker-toggle
                matIconSuffix
                [for]="picker1"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker1></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Quality</mat-label>
              <mat-select
                formControlName="quality"
                (valueChange)="design($event)"
              >
                <mat-option
                  *ngFor="let quality of qualityList"
                  value="{{ quality.id }}"
                >
                  {{ quality.quality }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Design</mat-label>
              <mat-select formControlName="design">
                <mat-option
                  *ngFor="let design of designList"
                  value="{{ design.id }}"
                >
                  {{ design.design }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Area</mat-label>
              <mat-select formControlName="area">
                <mat-option *ngFor="let area of areaList" value="{{ area }}">
                  {{ area }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Calculate Area</mat-label>
              <mat-select formControlName="calculate_area">
                <mat-option
                  *ngFor="let calcArea of calculateAreaList"
                  value="{{ calcArea }}"
                >
                  {{ calcArea }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Rate</mat-label>
              <input matInput placeholder="Rate" formControlName="rate" />
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3">
            <button *ngIf="!isUpdated; else updateTemplate" mat-flat-button color="primary">Save</button>
            <ng-template #updateTemplate>
              <button mat-flat-button color="primary" (click)="update()" type="button">Update</button>
            </ng-template>
          </div>
        </div>
      </form>
    </fieldset>
  </section>
</div>

<div class="container mt-4">
  <section>
    <fieldset>
      <legend><b> List</b></legend>
      <div class="row">
        <div class="col-12">
          <mat-form-field appearance="outline">
            <mat-label>Search</mat-label>
            <input
              matInput
              (keyup)="applyFilter($event)"
              placeholder="Ex. Jack"
              #input
            />
          </mat-form-field>

          <div class="mat-elevation-z8">
            <table mat-table [dataSource]="dataSource" matSort>
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Sr.No</th>
                <td mat-cell *matCellDef="let row">{{ row.index }}</td>
              </ng-container>

              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                <td mat-cell *matCellDef="let row">{{ row.name }}</td>
              </ng-container>

              <ng-container matColumnDef="toDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  To Date
                </th>
                <td mat-cell *matCellDef="let row">{{ row.toDate }}</td>
              </ng-container>

              <ng-container matColumnDef="fromDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  From Data
                </th>
                <td mat-cell *matCellDef="let row">{{ row.fromDate }}</td>
              </ng-container>

              <ng-container matColumnDef="quality">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Quality
                </th>
                <td mat-cell *matCellDef="let row">{{ row.quality }}</td>
              </ng-container>

              <ng-container matColumnDef="design">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Design
                </th>
                <td mat-cell *matCellDef="let row">{{ row.design }}</td>
              </ng-container>

              <ng-container matColumnDef="area">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Area</th>
                <td mat-cell *matCellDef="let row">{{ row.area }}</td>
              </ng-container>

              <ng-container matColumnDef="calculate_area">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Calculate Area
                </th>
                <td mat-cell *matCellDef="let row">{{ row.calculate_area }}</td>
              </ng-container>

              <ng-container matColumnDef="rate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Rate</th>
                <td mat-cell *matCellDef="let row">{{ row.rate }}</td>
              </ng-container>

              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Action
                </th>
                <td mat-cell *matCellDef="let row">
                  <a (click)="edit(row.id)"
                                  ><i
                                    class="fa fa-pencil-square-o fa-edit"
                                    title="Edit purchase details "
                                    aria-hidden="true"
                                  ></i
                                ></a>
                                &nbsp;
                                <a (click)="delete(row.id)"><i class="fa fa-trash-o" aria-hidden="true"></i></a>

                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>

              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="4">
                  No data matching the filter "{{ input.value }}"
                </td>
              </tr>
            </table>

            <mat-paginator
              [pageSizeOptions]="[5, 10, 25, 100]"
              aria-label="Select page of users"
            ></mat-paginator>
          </div>
        </div>
      </div>
    </fieldset>
  </section>
</div>
