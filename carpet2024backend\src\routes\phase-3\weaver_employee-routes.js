const express = require('express');

const weaverEmployeeController = require('../../controller/weaver_employee-controller');

const router = express.Router();


router.post('/employees',weaverEmployeeController.createEmployee);
router.get('/employees', weaverEmployeeController.getEmployees);
router.get('/employees/:id', weaverEmployeeController.getEmployeeById);
router.put('/employees/:id', weaverEmployeeController.updateEmployee);
router.delete('/employees/:id', weaverEmployeeController.deleteEmployee);


module.exports = router;