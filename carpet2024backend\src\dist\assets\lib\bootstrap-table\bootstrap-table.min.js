/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).BootstrapTable=e(t.jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var i=e(t);function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function r(t,e,i){return e&&a(t.prototype,e),i&&a(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==i)return;var n,o,a=[],r=!0,s=!1;try{for(i=i.call(t);!(r=(n=i.next()).done)&&(a.push(n.value),!e||a.length!==e);r=!0);}catch(t){s=!0,o=t}finally{try{r||null==i.return||i.return()}finally{if(s)throw o}}return a}(t,e)||c(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t){return function(t){if(Array.isArray(t))return h(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,e){if(t){if("string"==typeof t)return h(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function u(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=c(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return r=t.done,t},e:function(t){s=!0,a=t},f:function(){try{r||null==i.return||i.return()}finally{if(s)throw a}}}}var d="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},f=function(t){return t&&t.Math==Math&&t},p=f("object"==typeof globalThis&&globalThis)||f("object"==typeof window&&window)||f("object"==typeof self&&self)||f("object"==typeof d&&d)||function(){return this}()||Function("return this")(),g={},v=function(t){try{return!!t()}catch(t){return!0}},b=!v((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m=!v((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),y=m,w=Function.prototype.call,S=y?w.bind(w):function(){return w.apply(w,arguments)},x={},k={}.propertyIsEnumerable,O=Object.getOwnPropertyDescriptor,C=O&&!k.call({1:2},1);x.f=C?function(t){var e=O(this,t);return!!e&&e.enumerable}:k;var T,I,P=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},A=m,$=Function.prototype,R=$.call,E=A&&$.bind.bind(R,R),j=function(t){return A?E(t):function(){return R.apply(t,arguments)}},F=j,_=F({}.toString),D=F("".slice),N=function(t){return D(_(t),8,-1)},V=N,B=j,L=function(t){if("Function"===V(t))return B(t)},H=v,M=N,U=Object,z=L("".split),q=H((function(){return!U("z").propertyIsEnumerable(0)}))?function(t){return"String"==M(t)?z(t,""):U(t)}:U,W=function(t){return null==t},G=W,K=TypeError,Y=function(t){if(G(t))throw K("Can't call method on "+t);return t},J=q,X=Y,Q=function(t){return J(X(t))},Z="object"==typeof document&&document.all,tt={all:Z,IS_HTMLDDA:void 0===Z&&void 0!==Z},et=tt.all,it=tt.IS_HTMLDDA?function(t){return"function"==typeof t||t===et}:function(t){return"function"==typeof t},nt=it,ot=tt.all,at=tt.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:nt(t)||t===ot}:function(t){return"object"==typeof t?null!==t:nt(t)},rt=p,st=it,lt=function(t){return st(t)?t:void 0},ct=function(t,e){return arguments.length<2?lt(rt[t]):rt[t]&&rt[t][e]},ht=L({}.isPrototypeOf),ut=ct("navigator","userAgent")||"",dt=p,ft=ut,pt=dt.process,gt=dt.Deno,vt=pt&&pt.versions||gt&&gt.version,bt=vt&&vt.v8;bt&&(I=(T=bt.split("."))[0]>0&&T[0]<4?1:+(T[0]+T[1])),!I&&ft&&(!(T=ft.match(/Edge\/(\d+)/))||T[1]>=74)&&(T=ft.match(/Chrome\/(\d+)/))&&(I=+T[1]);var mt=I,yt=mt,wt=v,St=!!Object.getOwnPropertySymbols&&!wt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&yt&&yt<41})),xt=St&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,kt=ct,Ot=it,Ct=ht,Tt=Object,It=xt?function(t){return"symbol"==typeof t}:function(t){var e=kt("Symbol");return Ot(e)&&Ct(e.prototype,Tt(t))},Pt=String,At=function(t){try{return Pt(t)}catch(t){return"Object"}},$t=it,Rt=At,Et=TypeError,jt=function(t){if($t(t))return t;throw Et(Rt(t)+" is not a function")},Ft=jt,_t=W,Dt=function(t,e){var i=t[e];return _t(i)?void 0:Ft(i)},Nt=S,Vt=it,Bt=at,Lt=TypeError,Ht={exports:{}},Mt=p,Ut=Object.defineProperty,zt=function(t,e){try{Ut(Mt,t,{value:e,configurable:!0,writable:!0})}catch(i){Mt[t]=e}return e},qt=zt,Wt="__core-js_shared__",Gt=p[Wt]||qt(Wt,{}),Kt=Gt;(Ht.exports=function(t,e){return Kt[t]||(Kt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Yt=Y,Jt=Object,Xt=function(t){return Jt(Yt(t))},Qt=Xt,Zt=L({}.hasOwnProperty),te=Object.hasOwn||function(t,e){return Zt(Qt(t),e)},ee=L,ie=0,ne=Math.random(),oe=ee(1..toString),ae=function(t){return"Symbol("+(void 0===t?"":t)+")_"+oe(++ie+ne,36)},re=p,se=Ht.exports,le=te,ce=ae,he=St,ue=xt,de=se("wks"),fe=re.Symbol,pe=fe&&fe.for,ge=ue?fe:fe&&fe.withoutSetter||ce,ve=function(t){if(!le(de,t)||!he&&"string"!=typeof de[t]){var e="Symbol."+t;he&&le(fe,t)?de[t]=fe[t]:de[t]=ue&&pe?pe(e):ge(e)}return de[t]},be=S,me=at,ye=It,we=Dt,Se=function(t,e){var i,n;if("string"===e&&Vt(i=t.toString)&&!Bt(n=Nt(i,t)))return n;if(Vt(i=t.valueOf)&&!Bt(n=Nt(i,t)))return n;if("string"!==e&&Vt(i=t.toString)&&!Bt(n=Nt(i,t)))return n;throw Lt("Can't convert object to primitive value")},xe=TypeError,ke=ve("toPrimitive"),Oe=function(t,e){if(!me(t)||ye(t))return t;var i,n=we(t,ke);if(n){if(void 0===e&&(e="default"),i=be(n,t,e),!me(i)||ye(i))return i;throw xe("Can't convert object to primitive value")}return void 0===e&&(e="number"),Se(t,e)},Ce=Oe,Te=It,Ie=function(t){var e=Ce(t,"string");return Te(e)?e:e+""},Pe=at,Ae=p.document,$e=Pe(Ae)&&Pe(Ae.createElement),Re=function(t){return $e?Ae.createElement(t):{}},Ee=Re,je=!b&&!v((function(){return 7!=Object.defineProperty(Ee("div"),"a",{get:function(){return 7}}).a})),Fe=b,_e=S,De=x,Ne=P,Ve=Q,Be=Ie,Le=te,He=je,Me=Object.getOwnPropertyDescriptor;g.f=Fe?Me:function(t,e){if(t=Ve(t),e=Be(e),He)try{return Me(t,e)}catch(t){}if(Le(t,e))return Ne(!_e(De.f,t,e),t[e])};var Ue={},ze=b&&v((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),qe=at,We=String,Ge=TypeError,Ke=function(t){if(qe(t))return t;throw Ge(We(t)+" is not an object")},Ye=b,Je=je,Xe=ze,Qe=Ke,Ze=Ie,ti=TypeError,ei=Object.defineProperty,ii=Object.getOwnPropertyDescriptor,ni="enumerable",oi="configurable",ai="writable";Ue.f=Ye?Xe?function(t,e,i){if(Qe(t),e=Ze(e),Qe(i),"function"==typeof t&&"prototype"===e&&"value"in i&&ai in i&&!i.writable){var n=ii(t,e);n&&n.writable&&(t[e]=i.value,i={configurable:oi in i?i.configurable:n.configurable,enumerable:ni in i?i.enumerable:n.enumerable,writable:!1})}return ei(t,e,i)}:ei:function(t,e,i){if(Qe(t),e=Ze(e),Qe(i),Je)try{return ei(t,e,i)}catch(t){}if("get"in i||"set"in i)throw ti("Accessors not supported");return"value"in i&&(t[e]=i.value),t};var ri=Ue,si=P,li=b?function(t,e,i){return ri.f(t,e,si(1,i))}:function(t,e,i){return t[e]=i,t},ci={exports:{}},hi=b,ui=te,di=Function.prototype,fi=hi&&Object.getOwnPropertyDescriptor,pi=ui(di,"name"),gi={EXISTS:pi,PROPER:pi&&"something"===function(){}.name,CONFIGURABLE:pi&&(!hi||hi&&fi(di,"name").configurable)},vi=it,bi=Gt,mi=L(Function.toString);vi(bi.inspectSource)||(bi.inspectSource=function(t){return mi(t)});var yi,wi,Si,xi=bi.inspectSource,ki=it,Oi=p.WeakMap,Ci=ki(Oi)&&/native code/.test(String(Oi)),Ti=Ht.exports,Ii=ae,Pi=Ti("keys"),Ai=function(t){return Pi[t]||(Pi[t]=Ii(t))},$i={},Ri=Ci,Ei=p,ji=at,Fi=li,_i=te,Di=Gt,Ni=Ai,Vi=$i,Bi="Object already initialized",Li=Ei.TypeError,Hi=Ei.WeakMap;if(Ri||Di.state){var Mi=Di.state||(Di.state=new Hi);Mi.get=Mi.get,Mi.has=Mi.has,Mi.set=Mi.set,yi=function(t,e){if(Mi.has(t))throw Li(Bi);return e.facade=t,Mi.set(t,e),e},wi=function(t){return Mi.get(t)||{}},Si=function(t){return Mi.has(t)}}else{var Ui=Ni("state");Vi[Ui]=!0,yi=function(t,e){if(_i(t,Ui))throw Li(Bi);return e.facade=t,Fi(t,Ui,e),e},wi=function(t){return _i(t,Ui)?t[Ui]:{}},Si=function(t){return _i(t,Ui)}}var zi={set:yi,get:wi,has:Si,enforce:function(t){return Si(t)?wi(t):yi(t,{})},getterFor:function(t){return function(e){var i;if(!ji(e)||(i=wi(e)).type!==t)throw Li("Incompatible receiver, "+t+" required");return i}}},qi=v,Wi=it,Gi=te,Ki=b,Yi=gi.CONFIGURABLE,Ji=xi,Xi=zi.enforce,Qi=zi.get,Zi=Object.defineProperty,tn=Ki&&!qi((function(){return 8!==Zi((function(){}),"length",{value:8}).length})),en=String(String).split("String"),nn=ci.exports=function(t,e,i){"Symbol("===String(e).slice(0,7)&&(e="["+String(e).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),i&&i.getter&&(e="get "+e),i&&i.setter&&(e="set "+e),(!Gi(t,"name")||Yi&&t.name!==e)&&(Ki?Zi(t,"name",{value:e,configurable:!0}):t.name=e),tn&&i&&Gi(i,"arity")&&t.length!==i.arity&&Zi(t,"length",{value:i.arity});try{i&&Gi(i,"constructor")&&i.constructor?Ki&&Zi(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=Xi(t);return Gi(n,"source")||(n.source=en.join("string"==typeof e?e:"")),t};Function.prototype.toString=nn((function(){return Wi(this)&&Qi(this).source||Ji(this)}),"toString");var on=it,an=Ue,rn=ci.exports,sn=zt,ln=function(t,e,i,n){n||(n={});var o=n.enumerable,a=void 0!==n.name?n.name:e;if(on(i)&&rn(i,a,n),n.global)o?t[e]=i:sn(e,i);else{try{n.unsafe?t[e]&&(o=!0):delete t[e]}catch(t){}o?t[e]=i:an.f(t,e,{value:i,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return t},cn={},hn=Math.ceil,un=Math.floor,dn=Math.trunc||function(t){var e=+t;return(e>0?un:hn)(e)},fn=function(t){var e=+t;return e!=e||0===e?0:dn(e)},pn=fn,gn=Math.max,vn=Math.min,bn=function(t,e){var i=pn(t);return i<0?gn(i+e,0):vn(i,e)},mn=fn,yn=Math.min,wn=function(t){return t>0?yn(mn(t),9007199254740991):0},Sn=wn,xn=function(t){return Sn(t.length)},kn=Q,On=bn,Cn=xn,Tn=function(t){return function(e,i,n){var o,a=kn(e),r=Cn(a),s=On(n,r);if(t&&i!=i){for(;r>s;)if((o=a[s++])!=o)return!0}else for(;r>s;s++)if((t||s in a)&&a[s]===i)return t||s||0;return!t&&-1}},In={includes:Tn(!0),indexOf:Tn(!1)},Pn=te,An=Q,$n=In.indexOf,Rn=$i,En=L([].push),jn=function(t,e){var i,n=An(t),o=0,a=[];for(i in n)!Pn(Rn,i)&&Pn(n,i)&&En(a,i);for(;e.length>o;)Pn(n,i=e[o++])&&(~$n(a,i)||En(a,i));return a},Fn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],_n=jn,Dn=Fn.concat("length","prototype");cn.f=Object.getOwnPropertyNames||function(t){return _n(t,Dn)};var Nn={};Nn.f=Object.getOwnPropertySymbols;var Vn=ct,Bn=cn,Ln=Nn,Hn=Ke,Mn=L([].concat),Un=Vn("Reflect","ownKeys")||function(t){var e=Bn.f(Hn(t)),i=Ln.f;return i?Mn(e,i(t)):e},zn=te,qn=Un,Wn=g,Gn=Ue,Kn=v,Yn=it,Jn=/#|\.prototype\./,Xn=function(t,e){var i=Zn[Qn(t)];return i==eo||i!=to&&(Yn(e)?Kn(e):!!e)},Qn=Xn.normalize=function(t){return String(t).replace(Jn,".").toLowerCase()},Zn=Xn.data={},to=Xn.NATIVE="N",eo=Xn.POLYFILL="P",io=Xn,no=p,oo=g.f,ao=li,ro=ln,so=zt,lo=function(t,e,i){for(var n=qn(e),o=Gn.f,a=Wn.f,r=0;r<n.length;r++){var s=n[r];zn(t,s)||i&&zn(i,s)||o(t,s,a(e,s))}},co=io,ho=function(t,e){var i,n,o,a,r,s=t.target,l=t.global,c=t.stat;if(i=l?no:c?no[s]||so(s,{}):(no[s]||{}).prototype)for(n in e){if(a=e[n],o=t.dontCallGetSet?(r=oo(i,n))&&r.value:i[n],!co(l?n:s+(c?".":"#")+n,t.forced)&&void 0!==o){if(typeof a==typeof o)continue;lo(a,o)}(t.sham||o&&o.sham)&&ao(a,"sham",!0),ro(i,n,a,t)}},uo=jn,fo=Fn,po=Object.keys||function(t){return uo(t,fo)},go=b,vo=L,bo=S,mo=v,yo=po,wo=Nn,So=x,xo=Xt,ko=q,Oo=Object.assign,Co=Object.defineProperty,To=vo([].concat),Io=!Oo||mo((function(){if(go&&1!==Oo({b:1},Oo(Co({},"a",{enumerable:!0,get:function(){Co(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},i=Symbol(),n="abcdefghijklmnopqrst";return t[i]=7,n.split("").forEach((function(t){e[t]=t})),7!=Oo({},t)[i]||yo(Oo({},e)).join("")!=n}))?function(t,e){for(var i=xo(t),n=arguments.length,o=1,a=wo.f,r=So.f;n>o;)for(var s,l=ko(arguments[o++]),c=a?To(yo(l),a(l)):yo(l),h=c.length,u=0;h>u;)s=c[u++],go&&!bo(r,l,s)||(i[s]=l[s]);return i}:Oo,Po=Io;ho({target:"Object",stat:!0,arity:2,forced:Object.assign!==Po},{assign:Po});var Ao={};Ao[ve("toStringTag")]="z";var $o="[object z]"===String(Ao),Ro=$o,Eo=it,jo=N,Fo=ve("toStringTag"),_o=Object,Do="Arguments"==jo(function(){return arguments}()),No=Ro?jo:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=_o(t),Fo))?i:Do?jo(e):"Object"==(n=jo(e))&&Eo(e.callee)?"Arguments":n},Vo=No,Bo=String,Lo=function(t){if("Symbol"===Vo(t))throw TypeError("Cannot convert a Symbol value to a string");return Bo(t)},Ho="\t\n\v\f\r                　\u2028\u2029\ufeff",Mo=Y,Uo=Lo,zo=L("".replace),qo="[\t\n\v\f\r                　\u2028\u2029\ufeff]",Wo=RegExp("^"+qo+qo+"*"),Go=RegExp(qo+qo+"*$"),Ko=function(t){return function(e){var i=Uo(Mo(e));return 1&t&&(i=zo(i,Wo,"")),2&t&&(i=zo(i,Go,"")),i}},Yo={start:Ko(1),end:Ko(2),trim:Ko(3)},Jo=gi.PROPER,Xo=v,Qo=Ho,Zo=Yo.trim;ho({target:"String",proto:!0,forced:function(t){return Xo((function(){return!!Qo[t]()||"​᠎"!=="​᠎"[t]()||Jo&&Qo[t].name!==t}))}("trim")},{trim:function(){return Zo(this)}});var ta=v,ea=function(t,e){var i=[][t];return!!i&&ta((function(){i.call(null,e||function(){return 1},1)}))},ia=ho,na=q,oa=Q,aa=ea,ra=L([].join),sa=na!=Object,la=aa("join",",");ia({target:"Array",proto:!0,forced:sa||!la},{join:function(t){return ra(oa(this),void 0===t?",":t)}});var ca=Ke,ha=function(){var t=ca(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e},ua=v,da=p.RegExp,fa=ua((function(){var t=da("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),pa=fa||ua((function(){return!da("a","y").sticky})),ga={BROKEN_CARET:fa||ua((function(){var t=da("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:pa,UNSUPPORTED_Y:fa},va={},ba=b,ma=ze,ya=Ue,wa=Ke,Sa=Q,xa=po;va.f=ba&&!ma?Object.defineProperties:function(t,e){wa(t);for(var i,n=Sa(e),o=xa(e),a=o.length,r=0;a>r;)ya.f(t,i=o[r++],n[i]);return t};var ka,Oa=ct("document","documentElement"),Ca=Ke,Ta=va,Ia=Fn,Pa=$i,Aa=Oa,$a=Re,Ra=Ai("IE_PROTO"),Ea=function(){},ja=function(t){return"<script>"+t+"</"+"script>"},Fa=function(t){t.write(ja("")),t.close();var e=t.parentWindow.Object;return t=null,e},_a=function(){try{ka=new ActiveXObject("htmlfile")}catch(t){}var t,e;_a="undefined"!=typeof document?document.domain&&ka?Fa(ka):((e=$a("iframe")).style.display="none",Aa.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(ja("document.F=Object")),t.close(),t.F):Fa(ka);for(var i=Ia.length;i--;)delete _a.prototype[Ia[i]];return _a()};Pa[Ra]=!0;var Da=Object.create||function(t,e){var i;return null!==t?(Ea.prototype=Ca(t),i=new Ea,Ea.prototype=null,i[Ra]=t):i=_a(),void 0===e?i:Ta.f(i,e)},Na=v,Va=p.RegExp,Ba=Na((function(){var t=Va(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),La=v,Ha=p.RegExp,Ma=La((function(){var t=Ha("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),Ua=S,za=L,qa=Lo,Wa=ha,Ga=ga,Ka=Ht.exports,Ya=Da,Ja=zi.get,Xa=Ba,Qa=Ma,Za=Ka("native-string-replace",String.prototype.replace),tr=RegExp.prototype.exec,er=tr,ir=za("".charAt),nr=za("".indexOf),or=za("".replace),ar=za("".slice),rr=function(){var t=/a/,e=/b*/g;return Ua(tr,t,"a"),Ua(tr,e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),sr=Ga.BROKEN_CARET,lr=void 0!==/()??/.exec("")[1];(rr||lr||sr||Xa||Qa)&&(er=function(t){var e,i,n,o,a,r,s,l=this,c=Ja(l),h=qa(t),u=c.raw;if(u)return u.lastIndex=l.lastIndex,e=Ua(er,u,h),l.lastIndex=u.lastIndex,e;var d=c.groups,f=sr&&l.sticky,p=Ua(Wa,l),g=l.source,v=0,b=h;if(f&&(p=or(p,"y",""),-1===nr(p,"g")&&(p+="g"),b=ar(h,l.lastIndex),l.lastIndex>0&&(!l.multiline||l.multiline&&"\n"!==ir(h,l.lastIndex-1))&&(g="(?: "+g+")",b=" "+b,v++),i=new RegExp("^(?:"+g+")",p)),lr&&(i=new RegExp("^"+g+"$(?!\\s)",p)),rr&&(n=l.lastIndex),o=Ua(tr,f?i:l,b),f?o?(o.input=ar(o.input,v),o[0]=ar(o[0],v),o.index=l.lastIndex,l.lastIndex+=o[0].length):l.lastIndex=0:rr&&o&&(l.lastIndex=l.global?o.index+o[0].length:n),lr&&o&&o.length>1&&Ua(Za,o[0],i,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(o[a]=void 0)})),o&&d)for(o.groups=r=Ya(null),a=0;a<d.length;a++)r[(s=d[a])[0]]=o[s[1]];return o});var cr=er;ho({target:"RegExp",proto:!0,forced:/./.exec!==cr},{exec:cr});var hr=m,ur=Function.prototype,dr=ur.apply,fr=ur.call,pr="object"==typeof Reflect&&Reflect.apply||(hr?fr.bind(dr):function(){return fr.apply(dr,arguments)}),gr=L,vr=ln,br=cr,mr=v,yr=ve,wr=li,Sr=yr("species"),xr=RegExp.prototype,kr=function(t,e,i,n){var o=yr(t),a=!mr((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),r=a&&!mr((function(){var e=!1,i=/a/;return"split"===t&&((i={}).constructor={},i.constructor[Sr]=function(){return i},i.flags="",i[o]=/./[o]),i.exec=function(){return e=!0,null},i[o](""),!e}));if(!a||!r||i){var s=gr(/./[o]),l=e(o,""[t],(function(t,e,i,n,o){var r=gr(t),l=e.exec;return l===br||l===xr.exec?a&&!o?{done:!0,value:s(e,i,n)}:{done:!0,value:r(i,e,n)}:{done:!1}}));vr(String.prototype,t,l[0]),vr(xr,o,l[1])}n&&wr(xr[o],"sham",!0)},Or=at,Cr=N,Tr=ve("match"),Ir=function(t){var e;return Or(t)&&(void 0!==(e=t[Tr])?!!e:"RegExp"==Cr(t))},Pr=L,Ar=v,$r=it,Rr=No,Er=xi,jr=function(){},Fr=[],_r=ct("Reflect","construct"),Dr=/^\s*(?:class|function)\b/,Nr=Pr(Dr.exec),Vr=!Dr.exec(jr),Br=function(t){if(!$r(t))return!1;try{return _r(jr,Fr,t),!0}catch(t){return!1}},Lr=function(t){if(!$r(t))return!1;switch(Rr(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Vr||!!Nr(Dr,Er(t))}catch(t){return!0}};Lr.sham=!0;var Hr=!_r||Ar((function(){var t;return Br(Br.call)||!Br(Object)||!Br((function(){t=!0}))||t}))?Lr:Br,Mr=Hr,Ur=At,zr=TypeError,qr=Ke,Wr=function(t){if(Mr(t))return t;throw zr(Ur(t)+" is not a constructor")},Gr=W,Kr=ve("species"),Yr=L,Jr=fn,Xr=Lo,Qr=Y,Zr=Yr("".charAt),ts=Yr("".charCodeAt),es=Yr("".slice),is=function(t){return function(e,i){var n,o,a=Xr(Qr(e)),r=Jr(i),s=a.length;return r<0||r>=s?t?"":void 0:(n=ts(a,r))<55296||n>56319||r+1===s||(o=ts(a,r+1))<56320||o>57343?t?Zr(a,r):n:t?es(a,r,r+2):o-56320+(n-55296<<10)+65536}},ns={codeAt:is(!1),charAt:is(!0)}.charAt,os=function(t,e,i){return e+(i?ns(t,e).length:1)},as=Ie,rs=Ue,ss=P,ls=function(t,e,i){var n=as(e);n in t?rs.f(t,n,ss(0,i)):t[n]=i},cs=bn,hs=xn,us=ls,ds=Array,fs=Math.max,ps=function(t,e,i){for(var n=hs(t),o=cs(e,n),a=cs(void 0===i?n:i,n),r=ds(fs(a-o,0)),s=0;o<a;o++,s++)us(r,s,t[o]);return r.length=s,r},gs=S,vs=Ke,bs=it,ms=N,ys=cr,ws=TypeError,Ss=function(t,e){var i=t.exec;if(bs(i)){var n=gs(i,t,e);return null!==n&&vs(n),n}if("RegExp"===ms(t))return gs(ys,t,e);throw ws("RegExp#exec called on incompatible receiver")},xs=pr,ks=S,Os=L,Cs=kr,Ts=Ke,Is=W,Ps=Ir,As=Y,$s=function(t,e){var i,n=qr(t).constructor;return void 0===n||Gr(i=qr(n)[Kr])?e:Wr(i)},Rs=os,Es=wn,js=Lo,Fs=Dt,_s=ps,Ds=Ss,Ns=cr,Vs=v,Bs=ga.UNSUPPORTED_Y,Ls=4294967295,Hs=Math.min,Ms=[].push,Us=Os(/./.exec),zs=Os(Ms),qs=Os("".slice),Ws=!Vs((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var i="ab".split(t);return 2!==i.length||"a"!==i[0]||"b"!==i[1]}));Cs("split",(function(t,e,i){var n;return n="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,i){var n=js(As(this)),o=void 0===i?Ls:i>>>0;if(0===o)return[];if(void 0===t)return[n];if(!Ps(t))return ks(e,n,t,o);for(var a,r,s,l=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),h=0,u=new RegExp(t.source,c+"g");(a=ks(Ns,u,n))&&!((r=u.lastIndex)>h&&(zs(l,qs(n,h,a.index)),a.length>1&&a.index<n.length&&xs(Ms,l,_s(a,1)),s=a[0].length,h=r,l.length>=o));)u.lastIndex===a.index&&u.lastIndex++;return h===n.length?!s&&Us(u,"")||zs(l,""):zs(l,qs(n,h)),l.length>o?_s(l,0,o):l}:"0".split(void 0,0).length?function(t,i){return void 0===t&&0===i?[]:ks(e,this,t,i)}:e,[function(e,i){var o=As(this),a=Is(e)?void 0:Fs(e,t);return a?ks(a,e,o,i):ks(n,js(o),e,i)},function(t,o){var a=Ts(this),r=js(t),s=i(n,a,r,o,n!==e);if(s.done)return s.value;var l=$s(a,RegExp),c=a.unicode,h=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(Bs?"g":"y"),u=new l(Bs?"^(?:"+a.source+")":a,h),d=void 0===o?Ls:o>>>0;if(0===d)return[];if(0===r.length)return null===Ds(u,r)?[r]:[];for(var f=0,p=0,g=[];p<r.length;){u.lastIndex=Bs?0:p;var v,b=Ds(u,Bs?qs(r,p):r);if(null===b||(v=Hs(Es(u.lastIndex+(Bs?p:0)),r.length))===f)p=Rs(r,p,c);else{if(zs(g,qs(r,f,p)),g.length===d)return g;for(var m=1;m<=b.length-1;m++)if(zs(g,b[m]),g.length===d)return g;p=f=v}}return zs(g,qs(r,f)),g}]}),!Ws,Bs);var Gs=b,Ks=L,Ys=po,Js=Q,Xs=Ks(x.f),Qs=Ks([].push),Zs=function(t){return function(e){for(var i,n=Js(e),o=Ys(n),a=o.length,r=0,s=[];a>r;)i=o[r++],Gs&&!Xs(n,i)||Qs(s,t?[i,n[i]]:n[i]);return s}},tl={entries:Zs(!0),values:Zs(!1)}.entries;ho({target:"Object",stat:!0},{entries:function(t){return tl(t)}});var el=ve,il=Da,nl=Ue.f,ol=el("unscopables"),al=Array.prototype;null==al[ol]&&nl(al,ol,{configurable:!0,value:il(null)});var rl=function(t){al[ol][t]=!0},sl=In.includes,ll=rl;ho({target:"Array",proto:!0,forced:v((function(){return!Array(1).includes()}))},{includes:function(t){return sl(this,t,arguments.length>1?arguments[1]:void 0)}}),ll("includes");var cl=N,hl=Array.isArray||function(t){return"Array"==cl(t)},ul=TypeError,dl=function(t){if(t>9007199254740991)throw ul("Maximum allowed index exceeded");return t},fl=hl,pl=Hr,gl=at,vl=ve("species"),bl=Array,ml=function(t){var e;return fl(t)&&(e=t.constructor,(pl(e)&&(e===bl||fl(e.prototype))||gl(e)&&null===(e=e[vl]))&&(e=void 0)),void 0===e?bl:e},yl=function(t,e){return new(ml(t))(0===e?0:e)},wl=v,Sl=mt,xl=ve("species"),kl=function(t){return Sl>=51||!wl((function(){var e=[];return(e.constructor={})[xl]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Ol=ho,Cl=v,Tl=hl,Il=at,Pl=Xt,Al=xn,$l=dl,Rl=ls,El=yl,jl=kl,Fl=mt,_l=ve("isConcatSpreadable"),Dl=Fl>=51||!Cl((function(){var t=[];return t[_l]=!1,t.concat()[0]!==t})),Nl=jl("concat"),Vl=function(t){if(!Il(t))return!1;var e=t[_l];return void 0!==e?!!e:Tl(t)};Ol({target:"Array",proto:!0,arity:1,forced:!Dl||!Nl},{concat:function(t){var e,i,n,o,a,r=Pl(this),s=El(r,0),l=0;for(e=-1,n=arguments.length;e<n;e++)if(Vl(a=-1===e?r:arguments[e]))for(o=Al(a),$l(l+o),i=0;i<o;i++,l++)i in a&&Rl(s,l,a[i]);else $l(l+1),Rl(s,l++,a);return s.length=l,s}});var Bl=jt,Ll=m,Hl=L(L.bind),Ml=function(t,e){return Bl(t),void 0===e?t:Ll?Hl(t,e):function(){return t.apply(e,arguments)}},Ul=q,zl=Xt,ql=xn,Wl=yl,Gl=L([].push),Kl=function(t){var e=1==t,i=2==t,n=3==t,o=4==t,a=6==t,r=7==t,s=5==t||a;return function(l,c,h,u){for(var d,f,p=zl(l),g=Ul(p),v=Ml(c,h),b=ql(g),m=0,y=u||Wl,w=e?y(l,b):i||r?y(l,0):void 0;b>m;m++)if((s||m in g)&&(f=v(d=g[m],m,p),t))if(e)w[m]=f;else if(f)switch(t){case 3:return!0;case 5:return d;case 6:return m;case 2:Gl(w,d)}else switch(t){case 4:return!1;case 7:Gl(w,d)}return a?-1:n||o?o:w}},Yl={forEach:Kl(0),map:Kl(1),filter:Kl(2),some:Kl(3),every:Kl(4),find:Kl(5),findIndex:Kl(6),filterReject:Kl(7)},Jl=ho,Xl=Yl.find,Ql=rl,Zl="find",tc=!0;Zl in[]&&Array(1).find((function(){tc=!1})),Jl({target:"Array",proto:!0,forced:tc},{find:function(t){return Xl(this,t,arguments.length>1?arguments[1]:void 0)}}),Ql(Zl);var ec=No,ic=$o?{}.toString:function(){return"[object "+ec(this)+"]"};$o||ln(Object.prototype,"toString",ic,{unsafe:!0});var nc=Ir,oc=TypeError,ac=function(t){if(nc(t))throw oc("The method doesn't accept regular expressions");return t},rc=ve("match"),sc=function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[rc]=!1,"/./"[t](e)}catch(t){}}return!1},lc=ho,cc=ac,hc=Y,uc=Lo,dc=sc,fc=L("".indexOf);lc({target:"String",proto:!0,forced:!dc("includes")},{includes:function(t){return!!~fc(uc(hc(this)),uc(cc(t)),arguments.length>1?arguments[1]:void 0)}});var pc={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},gc=Re("span").classList,vc=gc&&gc.constructor&&gc.constructor.prototype,bc=vc===Object.prototype?void 0:vc,mc=Yl.forEach,yc=ea("forEach")?[].forEach:function(t){return mc(this,t,arguments.length>1?arguments[1]:void 0)},wc=p,Sc=pc,xc=bc,kc=yc,Oc=li,Cc=function(t){if(t&&t.forEach!==kc)try{Oc(t,"forEach",kc)}catch(e){t.forEach=kc}};for(var Tc in Sc)Sc[Tc]&&Cc(wc[Tc]&&wc[Tc].prototype);Cc(xc);var Ic=p,Pc=v,Ac=Lo,$c=Yo.trim,Rc=L("".charAt),Ec=Ic.parseFloat,jc=Ic.Symbol,Fc=jc&&jc.iterator,_c=1/Ec("\t\n\v\f\r                　\u2028\u2029\ufeff-0")!=-1/0||Fc&&!Pc((function(){Ec(Object(Fc))}))?function(t){var e=$c(Ac(t)),i=Ec(e);return 0===i&&"-"==Rc(e,0)?-0:i}:Ec;ho({global:!0,forced:parseFloat!=_c},{parseFloat:_c});var Dc=ho,Nc=In.indexOf,Vc=ea,Bc=L([].indexOf),Lc=!!Bc&&1/Bc([1],1,-0)<0,Hc=Vc("indexOf");Dc({target:"Array",proto:!0,forced:Lc||!Hc},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return Lc?Bc(this,t,e)||0:Nc(this,t,e)}});var Mc=At,Uc=TypeError,zc=function(t,e){if(!delete t[e])throw Uc("Cannot delete property "+Mc(e)+" of "+Mc(t))},qc=ps,Wc=Math.floor,Gc=function(t,e){var i=t.length,n=Wc(i/2);return i<8?Kc(t,e):Yc(t,Gc(qc(t,0,n),e),Gc(qc(t,n),e),e)},Kc=function(t,e){for(var i,n,o=t.length,a=1;a<o;){for(n=a,i=t[a];n&&e(t[n-1],i)>0;)t[n]=t[--n];n!==a++&&(t[n]=i)}return t},Yc=function(t,e,i,n){for(var o=e.length,a=i.length,r=0,s=0;r<o||s<a;)t[r+s]=r<o&&s<a?n(e[r],i[s])<=0?e[r++]:i[s++]:r<o?e[r++]:i[s++];return t},Jc=Gc,Xc=ut.match(/firefox\/(\d+)/i),Qc=!!Xc&&+Xc[1],Zc=/MSIE|Trident/.test(ut),th=ut.match(/AppleWebKit\/(\d+)\./),eh=!!th&&+th[1],ih=ho,nh=L,oh=jt,ah=Xt,rh=xn,sh=zc,lh=Lo,ch=v,hh=Jc,uh=ea,dh=Qc,fh=Zc,ph=mt,gh=eh,vh=[],bh=nh(vh.sort),mh=nh(vh.push),yh=ch((function(){vh.sort(void 0)})),wh=ch((function(){vh.sort(null)})),Sh=uh("sort"),xh=!ch((function(){if(ph)return ph<70;if(!(dh&&dh>3)){if(fh)return!0;if(gh)return gh<603;var t,e,i,n,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:i=3;break;case 68:case 71:i=4;break;default:i=2}for(n=0;n<47;n++)vh.push({k:e+n,v:i})}for(vh.sort((function(t,e){return e.v-t.v})),n=0;n<vh.length;n++)e=vh[n].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}}));ih({target:"Array",proto:!0,forced:yh||!wh||!Sh||!xh},{sort:function(t){void 0!==t&&oh(t);var e=ah(this);if(xh)return void 0===t?bh(e):bh(e,t);var i,n,o=[],a=rh(e);for(n=0;n<a;n++)n in e&&mh(o,e[n]);for(hh(o,function(t){return function(e,i){return void 0===i?-1:void 0===e?1:void 0!==t?+t(e,i)||0:lh(e)>lh(i)?1:-1}}(t)),i=rh(o),n=0;n<i;)e[n]=o[n++];for(;n<a;)sh(e,n++);return e}});var kh=L,Oh=Xt,Ch=Math.floor,Th=kh("".charAt),Ih=kh("".replace),Ph=kh("".slice),Ah=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,$h=/\$([$&'`]|\d{1,2})/g,Rh=pr,Eh=S,jh=L,Fh=kr,_h=v,Dh=Ke,Nh=it,Vh=W,Bh=fn,Lh=wn,Hh=Lo,Mh=Y,Uh=os,zh=Dt,qh=function(t,e,i,n,o,a){var r=i+t.length,s=n.length,l=$h;return void 0!==o&&(o=Oh(o),l=Ah),Ih(a,l,(function(a,l){var c;switch(Th(l,0)){case"$":return"$";case"&":return t;case"`":return Ph(e,0,i);case"'":return Ph(e,r);case"<":c=o[Ph(l,1,-1)];break;default:var h=+l;if(0===h)return a;if(h>s){var u=Ch(h/10);return 0===u?a:u<=s?void 0===n[u-1]?Th(l,1):n[u-1]+Th(l,1):a}c=n[h-1]}return void 0===c?"":c}))},Wh=Ss,Gh=ve("replace"),Kh=Math.max,Yh=Math.min,Jh=jh([].concat),Xh=jh([].push),Qh=jh("".indexOf),Zh=jh("".slice),tu="$0"==="a".replace(/./,"$0"),eu=!!/./[Gh]&&""===/./[Gh]("a","$0");Fh("replace",(function(t,e,i){var n=eu?"$":"$0";return[function(t,i){var n=Mh(this),o=Vh(t)?void 0:zh(t,Gh);return o?Eh(o,t,n,i):Eh(e,Hh(n),t,i)},function(t,o){var a=Dh(this),r=Hh(t);if("string"==typeof o&&-1===Qh(o,n)&&-1===Qh(o,"$<")){var s=i(e,a,r,o);if(s.done)return s.value}var l=Nh(o);l||(o=Hh(o));var c=a.global;if(c){var h=a.unicode;a.lastIndex=0}for(var u=[];;){var d=Wh(a,r);if(null===d)break;if(Xh(u,d),!c)break;""===Hh(d[0])&&(a.lastIndex=Uh(r,Lh(a.lastIndex),h))}for(var f,p="",g=0,v=0;v<u.length;v++){for(var b=Hh((d=u[v])[0]),m=Kh(Yh(Bh(d.index),r.length),0),y=[],w=1;w<d.length;w++)Xh(y,void 0===(f=d[w])?f:String(f));var S=d.groups;if(l){var x=Jh([b],y,m,r);void 0!==S&&Xh(x,S);var k=Hh(Rh(o,void 0,x))}else k=qh(b,r,m,y,S,o);m>=g&&(p+=Zh(r,g,m)+k,g=m+b.length)}return p+Zh(r,g)}]}),!!_h((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!tu||eu);var iu=Yl.filter;ho({target:"Array",proto:!0,forced:!kl("filter")},{filter:function(t){return iu(this,t,arguments.length>1?arguments[1]:void 0)}});var nu=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e},ou=S,au=Ke,ru=W,su=Y,lu=nu,cu=Lo,hu=Dt,uu=Ss;kr("search",(function(t,e,i){return[function(e){var i=su(this),n=ru(e)?void 0:hu(e,t);return n?ou(n,e,i):new RegExp(e)[t](cu(i))},function(t){var n=au(this),o=cu(t),a=i(e,n,o);if(a.done)return a.value;var r=n.lastIndex;lu(r,0)||(n.lastIndex=0);var s=uu(n,o);return lu(n.lastIndex,r)||(n.lastIndex=r),null===s?-1:s.index}]}));var du=p,fu=v,pu=L,gu=Lo,vu=Yo.trim,bu=Ho,mu=du.parseInt,yu=du.Symbol,wu=yu&&yu.iterator,Su=/^[+-]?0x/i,xu=pu(Su.exec),ku=8!==mu(bu+"08")||22!==mu(bu+"0x16")||wu&&!fu((function(){mu(Object(wu))}))?function(t,e){var i=vu(gu(t));return mu(i,e>>>0||(xu(Su,i)?16:10))}:mu;ho({global:!0,forced:parseInt!=ku},{parseInt:ku});var Ou=Yl.map;ho({target:"Array",proto:!0,forced:!kl("map")},{map:function(t){return Ou(this,t,arguments.length>1?arguments[1]:void 0)}});var Cu=ho,Tu=Yl.findIndex,Iu=rl,Pu="findIndex",Au=!0;Pu in[]&&Array(1).findIndex((function(){Au=!1})),Cu({target:"Array",proto:!0,forced:Au},{findIndex:function(t){return Tu(this,t,arguments.length>1?arguments[1]:void 0)}}),Iu(Pu);var $u=it,Ru=String,Eu=TypeError,ju=L,Fu=Ke,_u=function(t){if("object"==typeof t||$u(t))return t;throw Eu("Can't set "+Ru(t)+" as a prototype")},Du=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=ju(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(i,[]),e=i instanceof Array}catch(t){}return function(i,n){return Fu(i),_u(n),e?t(i,n):i.__proto__=n,i}}():void 0),Nu=it,Vu=at,Bu=Du,Lu=function(t,e,i){var n,o;return Bu&&Nu(n=e.constructor)&&n!==i&&Vu(o=n.prototype)&&o!==i.prototype&&Bu(t,o),t},Hu=S,Mu=te,Uu=ht,zu=ha,qu=RegExp.prototype,Wu=function(t){var e=t.flags;return void 0!==e||"flags"in qu||Mu(t,"flags")||!Uu(qu,t)?e:Hu(zu,t)},Gu=Ue.f,Ku=ct,Yu=Ue,Ju=b,Xu=ve("species"),Qu=b,Zu=p,td=L,ed=io,id=Lu,nd=li,od=cn.f,ad=ht,rd=Ir,sd=Lo,ld=Wu,cd=ga,hd=function(t,e,i){i in t||Gu(t,i,{configurable:!0,get:function(){return e[i]},set:function(t){e[i]=t}})},ud=ln,dd=v,fd=te,pd=zi.enforce,gd=function(t){var e=Ku(t),i=Yu.f;Ju&&e&&!e[Xu]&&i(e,Xu,{configurable:!0,get:function(){return this}})},vd=Ba,bd=Ma,md=ve("match"),yd=Zu.RegExp,wd=yd.prototype,Sd=Zu.SyntaxError,xd=td(wd.exec),kd=td("".charAt),Od=td("".replace),Cd=td("".indexOf),Td=td("".slice),Id=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,Pd=/a/g,Ad=/a/g,$d=new yd(Pd)!==Pd,Rd=cd.MISSED_STICKY,Ed=cd.UNSUPPORTED_Y,jd=Qu&&(!$d||Rd||vd||bd||dd((function(){return Ad[md]=!1,yd(Pd)!=Pd||yd(Ad)==Ad||"/a/i"!=yd(Pd,"i")})));if(ed("RegExp",jd)){for(var Fd=function(t,e){var i,n,o,a,r,s,l=ad(wd,this),c=rd(t),h=void 0===e,u=[],d=t;if(!l&&c&&h&&t.constructor===Fd)return t;if((c||ad(wd,t))&&(t=t.source,h&&(e=ld(d))),t=void 0===t?"":sd(t),e=void 0===e?"":sd(e),d=t,vd&&"dotAll"in Pd&&(n=!!e&&Cd(e,"s")>-1)&&(e=Od(e,/s/g,"")),i=e,Rd&&"sticky"in Pd&&(o=!!e&&Cd(e,"y")>-1)&&Ed&&(e=Od(e,/y/g,"")),bd&&(a=function(t){for(var e,i=t.length,n=0,o="",a=[],r={},s=!1,l=!1,c=0,h="";n<=i;n++){if("\\"===(e=kd(t,n)))e+=kd(t,++n);else if("]"===e)s=!1;else if(!s)switch(!0){case"["===e:s=!0;break;case"("===e:xd(Id,Td(t,n+1))&&(n+=2,l=!0),o+=e,c++;continue;case">"===e&&l:if(""===h||fd(r,h))throw new Sd("Invalid capture group name");r[h]=!0,a[a.length]=[h,c],l=!1,h="";continue}l?h+=e:o+=e}return[o,a]}(t),t=a[0],u=a[1]),r=id(yd(t,e),l?this:wd,Fd),(n||o||u.length)&&(s=pd(r),n&&(s.dotAll=!0,s.raw=Fd(function(t){for(var e,i=t.length,n=0,o="",a=!1;n<=i;n++)"\\"!==(e=kd(t,n))?a||"."!==e?("["===e?a=!0:"]"===e&&(a=!1),o+=e):o+="[\\s\\S]":o+=e+kd(t,++n);return o}(t),i)),o&&(s.sticky=!0),u.length&&(s.groups=u)),t!==d)try{nd(r,"source",""===d?"(?:)":d)}catch(t){}return r},_d=od(yd),Dd=0;_d.length>Dd;)hd(Fd,yd,_d[Dd++]);wd.constructor=Fd,Fd.prototype=wd,ud(Zu,"RegExp",Fd,{constructor:!0})}gd("RegExp");var Nd=gi.PROPER,Vd=ln,Bd=Ke,Ld=Lo,Hd=v,Md=Wu,Ud="toString",zd=RegExp.prototype.toString,qd=Hd((function(){return"/a/b"!=zd.call({source:"a",flags:"b"})})),Wd=Nd&&zd.name!=Ud;(qd||Wd)&&Vd(RegExp.prototype,Ud,(function(){var t=Bd(this);return"/"+Ld(t.source)+"/"+Ld(Md(t))}),{unsafe:!0});var Gd=L([].slice),Kd=ho,Yd=hl,Jd=Hr,Xd=at,Qd=bn,Zd=xn,tf=Q,ef=ls,nf=ve,of=Gd,af=kl("slice"),rf=nf("species"),sf=Array,lf=Math.max;Kd({target:"Array",proto:!0,forced:!af},{slice:function(t,e){var i,n,o,a=tf(this),r=Zd(a),s=Qd(t,r),l=Qd(void 0===e?r:e,r);if(Yd(a)&&(i=a.constructor,(Jd(i)&&(i===sf||Yd(i.prototype))||Xd(i)&&null===(i=i[rf]))&&(i=void 0),i===sf||void 0===i))return of(a,s,l);for(n=new(void 0===i?sf:i)(lf(l-s,0)),o=0;s<l;s++,o++)s in a&&ef(n,o,a[s]);return n.length=o,n}});var cf,hf,uf,df={},ff=!v((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),pf=te,gf=it,vf=Xt,bf=ff,mf=Ai("IE_PROTO"),yf=Object,wf=yf.prototype,Sf=bf?yf.getPrototypeOf:function(t){var e=vf(t);if(pf(e,mf))return e[mf];var i=e.constructor;return gf(i)&&e instanceof i?i.prototype:e instanceof yf?wf:null},xf=v,kf=it,Of=at,Cf=Sf,Tf=ln,If=ve("iterator"),Pf=!1;[].keys&&("next"in(uf=[].keys())?(hf=Cf(Cf(uf)))!==Object.prototype&&(cf=hf):Pf=!0);var Af=!Of(cf)||xf((function(){var t={};return cf[If].call(t)!==t}));Af&&(cf={}),kf(cf[If])||Tf(cf,If,(function(){return this}));var $f={IteratorPrototype:cf,BUGGY_SAFARI_ITERATORS:Pf},Rf=Ue.f,Ef=te,jf=ve("toStringTag"),Ff=function(t,e,i){t&&!i&&(t=t.prototype),t&&!Ef(t,jf)&&Rf(t,jf,{configurable:!0,value:e})},_f=$f.IteratorPrototype,Df=Da,Nf=P,Vf=Ff,Bf=df,Lf=function(){return this},Hf=ho,Mf=S,Uf=it,zf=function(t,e,i,n){var o=e+" Iterator";return t.prototype=Df(_f,{next:Nf(+!n,i)}),Vf(t,o,!1),Bf[o]=Lf,t},qf=Sf,Wf=Du,Gf=Ff,Kf=li,Yf=ln,Jf=df,Xf=gi.PROPER,Qf=gi.CONFIGURABLE,Zf=$f.IteratorPrototype,tp=$f.BUGGY_SAFARI_ITERATORS,ep=ve("iterator"),ip="keys",np="values",op="entries",ap=function(){return this},rp=Q,sp=rl,lp=df,cp=zi,hp=Ue.f,up=function(t,e,i,n,o,a,r){zf(i,e,n);var s,l,c,h=function(t){if(t===o&&g)return g;if(!tp&&t in f)return f[t];switch(t){case ip:case np:case op:return function(){return new i(this,t)}}return function(){return new i(this)}},u=e+" Iterator",d=!1,f=t.prototype,p=f[ep]||f["@@iterator"]||o&&f[o],g=!tp&&p||h(o),v="Array"==e&&f.entries||p;if(v&&(s=qf(v.call(new t)))!==Object.prototype&&s.next&&(qf(s)!==Zf&&(Wf?Wf(s,Zf):Uf(s[ep])||Yf(s,ep,ap)),Gf(s,u,!0)),Xf&&o==np&&p&&p.name!==np&&(Qf?Kf(f,"name",np):(d=!0,g=function(){return Mf(p,this)})),o)if(l={values:h(np),keys:a?g:h(ip),entries:h(op)},r)for(c in l)(tp||d||!(c in f))&&Yf(f,c,l[c]);else Hf({target:e,proto:!0,forced:tp||d},l);return f[ep]!==g&&Yf(f,ep,g,{name:o}),Jf[e]=g,l},dp=function(t,e){return{value:t,done:e}},fp=b,pp="Array Iterator",gp=cp.set,vp=cp.getterFor(pp),bp=up(Array,"Array",(function(t,e){gp(this,{type:pp,target:rp(t),index:0,kind:e})}),(function(){var t=vp(this),e=t.target,i=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,dp(void 0,!0)):dp("keys"==i?n:"values"==i?e[n]:[n,e[n]],!1)}),"values"),mp=lp.Arguments=lp.Array;if(sp("keys"),sp("values"),sp("entries"),fp&&"values"!==mp.name)try{hp(mp,"name",{value:"values"})}catch(t){}var yp=p,wp=pc,Sp=bc,xp=bp,kp=li,Op=ve,Cp=Op("iterator"),Tp=Op("toStringTag"),Ip=xp.values,Pp=function(t,e){if(t){if(t[Cp]!==Ip)try{kp(t,Cp,Ip)}catch(e){t[Cp]=Ip}if(t[Tp]||kp(t,Tp,e),wp[e])for(var i in xp)if(t[i]!==xp[i])try{kp(t,i,xp[i])}catch(e){t[i]=xp[i]}}};for(var Ap in wp)Pp(yp[Ap]&&yp[Ap].prototype,Ap);Pp(Sp,"DOMTokenList");var $p=b,Rp=hl,Ep=TypeError,jp=Object.getOwnPropertyDescriptor,Fp=$p&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}(),_p=ho,Dp=Xt,Np=bn,Vp=fn,Bp=xn,Lp=Fp?function(t,e){if(Rp(t)&&!jp(t,"length").writable)throw Ep("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e},Hp=dl,Mp=yl,Up=ls,zp=zc,qp=kl("splice"),Wp=Math.max,Gp=Math.min;_p({target:"Array",proto:!0,forced:!qp},{splice:function(t,e){var i,n,o,a,r,s,l=Dp(this),c=Bp(l),h=Np(t,c),u=arguments.length;for(0===u?i=n=0:1===u?(i=0,n=c-h):(i=u-2,n=Gp(Wp(Vp(e),0),c-h)),Hp(c+i-n),o=Mp(l,n),a=0;a<n;a++)(r=h+a)in l&&Up(o,a,l[r]);if(o.length=n,i<n){for(a=h;a<c-n;a++)s=a+i,(r=a+n)in l?l[s]=l[r]:zp(l,s);for(a=c;a>c-n+i;a--)zp(l,a-1)}else if(i>n)for(a=c-n;a>h;a--)s=a+i-1,(r=a+n-1)in l?l[s]=l[r]:zp(l,s);for(a=0;a<i;a++)l[a+h]=arguments[a+2];return Lp(l,c-n+i),o}});var Kp=L(1..valueOf),Yp=b,Jp=p,Xp=L,Qp=io,Zp=ln,tg=te,eg=Lu,ig=ht,ng=It,og=Oe,ag=v,rg=cn.f,sg=g.f,lg=Ue.f,cg=Kp,hg=Yo.trim,ug="Number",dg=Jp.Number,fg=dg.prototype,pg=Jp.TypeError,gg=Xp("".slice),vg=Xp("".charCodeAt),bg=function(t){var e=og(t,"number");return"bigint"==typeof e?e:mg(e)},mg=function(t){var e,i,n,o,a,r,s,l,c=og(t,"number");if(ng(c))throw pg("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=hg(c),43===(e=vg(c,0))||45===e){if(88===(i=vg(c,2))||120===i)return NaN}else if(48===e){switch(vg(c,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+c}for(r=(a=gg(c,2)).length,s=0;s<r;s++)if((l=vg(a,s))<48||l>o)return NaN;return parseInt(a,n)}return+c};if(Qp(ug,!dg(" 0o1")||!dg("0b1")||dg("+0x1"))){for(var yg,wg=function(t){var e=arguments.length<1?0:dg(bg(t)),i=this;return ig(fg,i)&&ag((function(){cg(i)}))?eg(Object(e),i,wg):e},Sg=Yp?rg(dg):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),xg=0;Sg.length>xg;xg++)tg(dg,yg=Sg[xg])&&!tg(wg,yg)&&lg(wg,yg,sg(dg,yg));wg.prototype=fg,fg.constructor=wg,Zp(Jp,ug,wg,{constructor:!0})}var kg=ho,Og=hl,Cg=L([].reverse),Tg=[1,2];kg({target:"Array",proto:!0,forced:String(Tg)===String(Tg.reverse())},{reverse:function(){return Og(this)&&(this.length=this.length),Cg(this)}});var Ig=Xt,Pg=po;ho({target:"Object",stat:!0,forced:v((function(){Pg(1)}))},{keys:function(t){return Pg(Ig(t))}});var Ag=S,$g=Ke,Rg=W,Eg=wn,jg=Lo,Fg=Y,_g=Dt,Dg=os,Ng=Ss;kr("match",(function(t,e,i){return[function(e){var i=Fg(this),n=Rg(e)?void 0:_g(e,t);return n?Ag(n,e,i):new RegExp(e)[t](jg(i))},function(t){var n=$g(this),o=jg(t),a=i(e,n,o);if(a.done)return a.value;if(!n.global)return Ng(n,o);var r=n.unicode;n.lastIndex=0;for(var s,l=[],c=0;null!==(s=Ng(n,o));){var h=jg(s[0]);l[c]=h,""===h&&(n.lastIndex=Dg(o,Eg(n.lastIndex),r)),c++}return 0===c?null:l}]}));var Vg,Bg=ho,Lg=L,Hg=g.f,Mg=wn,Ug=Lo,zg=ac,qg=Y,Wg=sc,Gg=Lg("".startsWith),Kg=Lg("".slice),Yg=Math.min,Jg=Wg("startsWith");Bg({target:"String",proto:!0,forced:!!(Jg||(Vg=Hg(String.prototype,"startsWith"),!Vg||Vg.writable))&&!Jg},{startsWith:function(t){var e=Ug(qg(this));zg(t);var i=Mg(Yg(arguments.length>1?arguments[1]:void 0,e.length)),n=Ug(t);return Gg?Gg(e,n,i):Kg(e,i,i+n.length)===n}});var Xg=ho,Qg=L,Zg=g.f,tv=wn,ev=Lo,iv=ac,nv=Y,ov=sc,av=Qg("".endsWith),rv=Qg("".slice),sv=Math.min,lv=ov("endsWith"),cv=!lv&&!!function(){var t=Zg(String.prototype,"endsWith");return t&&!t.writable}();Xg({target:"String",proto:!0,forced:!cv&&!lv},{endsWith:function(t){var e=ev(nv(this));iv(t);var i=arguments.length>1?arguments[1]:void 0,n=e.length,o=void 0===i?n:sv(tv(i),n),a=ev(t);return av?av(e,a,o):rv(e,o-a.length,o)===a}});var hv={getBootstrapVersion:function(){var t=5;try{var e=i.default.fn.dropdown.Constructor.VERSION;void 0!==e&&(t=parseInt(e,10))}catch(t){}try{var n=bootstrap.Tooltip.VERSION;void 0!==n&&(t=parseInt(n,10))}catch(t){}return t},getIconsPrefix:function(t){return{bootstrap3:"glyphicon",bootstrap4:"fa",bootstrap5:"bi","bootstrap-table":"icon",bulma:"fa",foundation:"fa",materialize:"material-icons",semantic:"fa"}[t]||"fa"},getIcons:function(t){return{glyphicon:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggleOff:"glyphicon-list-alt icon-list-alt",toggleOn:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus",fullscreen:"glyphicon-fullscreen",search:"glyphicon-search",clearSearch:"glyphicon-trash"},fa:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},bi:{paginationSwitchDown:"bi-caret-down-square",paginationSwitchUp:"bi-caret-up-square",refresh:"bi-arrow-clockwise",toggleOff:"bi-toggle-off",toggleOn:"bi-toggle-on",columns:"bi-list-ul",detailOpen:"bi-plus",detailClose:"bi-dash",fullscreen:"bi-arrows-move",search:"bi-search",clearSearch:"bi-trash"},icon:{paginationSwitchDown:"icon-arrow-up-circle",paginationSwitchUp:"icon-arrow-down-circle",refresh:"icon-refresh-cw",toggleOff:"icon-toggle-right",toggleOn:"icon-toggle-right",columns:"icon-list",detailOpen:"icon-plus",detailClose:"icon-minus",fullscreen:"icon-maximize",search:"icon-search",clearSearch:"icon-trash-2"},"material-icons":{paginationSwitchDown:"grid_on",paginationSwitchUp:"grid_off",refresh:"refresh",toggleOff:"tablet",toggleOn:"tablet_android",columns:"view_list",detailOpen:"add",detailClose:"remove",fullscreen:"fullscreen",sort:"sort",search:"search",clearSearch:"delete"}}[t]},getSearchInput:function(t){return"string"==typeof t.options.searchSelector?i.default(t.options.searchSelector):t.$toolbar.find(".search input")},sprintf:function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];var o=!0,a=0,r=t.replace(/%s/g,(function(){var t=i[a++];return void 0===t?(o=!1,""):t}));return o?r:""},isObject:function(t){return t instanceof Object&&!Array.isArray(t)},isEmptyObject:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return 0===Object.entries(t).length&&t.constructor===Object},isNumeric:function(t){return!isNaN(parseFloat(t))&&isFinite(t)},getFieldTitle:function(t,e){var i,n=u(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(o.field===e)return o.title}}catch(t){n.e(t)}finally{n.f()}return""},setFieldIndex:function(t){var e,i=0,n=[],o=u(t[0]);try{for(o.s();!(e=o.n()).done;){i+=e.value.colspan||1}}catch(t){o.e(t)}finally{o.f()}for(var a=0;a<t.length;a++){n[a]=[];for(var r=0;r<i;r++)n[a][r]=!1}for(var s=0;s<t.length;s++){var l,c=u(t[s]);try{for(c.s();!(l=c.n()).done;){var h=l.value,d=h.rowspan||1,f=h.colspan||1,p=n[s].indexOf(!1);h.colspanIndex=p,1===f?(h.fieldIndex=p,void 0===h.field&&(h.field=p)):h.colspanGroup=h.colspan;for(var g=0;g<d;g++)for(var v=0;v<f;v++)n[s+g][p+v]=!0}}catch(t){c.e(t)}finally{c.f()}}},normalizeAccent:function(t){return"string"!=typeof t?t:t.normalize("NFD").replace(/[\u0300-\u036f]/g,"")},updateFieldGroup:function(t,e){var i,n,o=(i=[]).concat.apply(i,l(t)),a=u(t);try{for(a.s();!(n=a.n()).done;){var r,s=u(n.value);try{for(s.s();!(r=s.n()).done;){var c=r.value;if(c.colspanGroup>1){for(var h=0,d=function(t){o.find((function(e){return e.fieldIndex===t})).visible&&h++},f=c.colspanIndex;f<c.colspanIndex+c.colspanGroup;f++)d(f);c.colspan=h,c.visible=h>0}}}catch(t){s.e(t)}finally{s.f()}}}catch(t){a.e(t)}finally{a.f()}if(!(t.length<2)){var p,g=u(e);try{var v=function(){var t=p.value,e=o.filter((function(e){return e.fieldIndex===t.fieldIndex}));if(e.length>1){var i,n=u(e);try{for(n.s();!(i=n.n()).done;){i.value.visible=t.visible}}catch(t){n.e(t)}finally{n.f()}}};for(g.s();!(p=g.n()).done;)v()}catch(t){g.e(t)}finally{g.f()}}},getScrollBarWidth:function(){if(void 0===this.cachedWidth){var t=i.default("<div/>").addClass("fixed-table-scroll-inner"),e=i.default("<div/>").addClass("fixed-table-scroll-outer");e.append(t),i.default("body").append(e);var n=t[0].offsetWidth;e.css("overflow","scroll");var o=t[0].offsetWidth;n===o&&(o=e[0].clientWidth),e.remove(),this.cachedWidth=n-o}return this.cachedWidth},calculateObjectValue:function(t,e,i,o){var a=e;if("string"==typeof e){var r=e.split(".");if(r.length>1){a=window;var s,c=u(r);try{for(c.s();!(s=c.n()).done;){a=a[s.value]}}catch(t){c.e(t)}finally{c.f()}}else a=window[e]}return null!==a&&"object"===n(a)?a:"function"==typeof a?a.apply(t,i||[]):!a&&"string"==typeof e&&i&&this.sprintf.apply(this,[e].concat(l(i)))?this.sprintf.apply(this,[e].concat(l(i))):o},compareObjects:function(t,e,i){var n=Object.keys(t),o=Object.keys(e);if(i&&n.length!==o.length)return!1;for(var a=0,r=n;a<r.length;a++){var s=r[a];if(o.includes(s)&&t[s]!==e[s])return!1}return!0},regexCompare:function(t,e){try{var i=e.match(/^\/(.*?)\/([gim]*)$/);if(-1!==t.toString().search(i?new RegExp(i[1],i[2]):new RegExp(e,"gim")))return!0}catch(t){return!1}return!1},escapeHTML:function(t){return t?t.toString().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;"):t},unescapeHTML:function(t){return"string"==typeof t&&t?t.toString().replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'"):t},removeHTML:function(t){return t?t.toString().replace(/(<([^>]+)>)/gi,"").replace(/&[#A-Za-z0-9]+;/gi,"").trim():t},getRealDataAttr:function(t){for(var e=0,i=Object.entries(t);e<i.length;e++){var n=s(i[e],2),o=n[0],a=n[1],r=o.split(/(?=[A-Z])/).join("-").toLowerCase();r!==o&&(t[r]=a,delete t[o])}return t},getItemField:function(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0,o=t;if(void 0!==n&&(i=n),"string"!=typeof e||t.hasOwnProperty(e))return i?this.escapeHTML(t[e]):t[e];var a,r=e.split("."),s=u(r);try{for(s.s();!(a=s.n()).done;){var l=a.value;o=o&&o[l]}}catch(t){s.e(t)}finally{s.f()}return i?this.escapeHTML(o):o},isIEBrowser:function(){return navigator.userAgent.includes("MSIE ")||/Trident.*rv:11\./.test(navigator.userAgent)},findIndex:function(t,e){var i,n=u(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(JSON.stringify(o)===JSON.stringify(e))return t.indexOf(o)}}catch(t){n.e(t)}finally{n.f()}return-1},trToData:function(t,e){var n=this,o=[],a=[];return e.each((function(e,r){var s=i.default(r),l={};l._id=s.attr("id"),l._class=s.attr("class"),l._data=n.getRealDataAttr(s.data()),l._style=s.attr("style"),s.find(">td,>th").each((function(o,r){for(var s=i.default(r),c=+s.attr("colspan")||1,h=+s.attr("rowspan")||1,u=o;a[e]&&a[e][u];u++);for(var d=u;d<u+c;d++)for(var f=e;f<e+h;f++)a[f]||(a[f]=[]),a[f][d]=!0;var p=t[u].field;l[p]=s.html().trim(),l["_".concat(p,"_id")]=s.attr("id"),l["_".concat(p,"_class")]=s.attr("class"),l["_".concat(p,"_rowspan")]=s.attr("rowspan"),l["_".concat(p,"_colspan")]=s.attr("colspan"),l["_".concat(p,"_title")]=s.attr("title"),l["_".concat(p,"_data")]=n.getRealDataAttr(s.data()),l["_".concat(p,"_style")]=s.attr("style")})),o.push(l)})),o},sort:function(t,e,i,n,o,a){if(null==t&&(t=""),null==e&&(e=""),n.sortStable&&t===e&&(t=o,e=a),this.isNumeric(t)&&this.isNumeric(e))return(t=parseFloat(t))<(e=parseFloat(e))?-1*i:t>e?i:0;if(n.sortEmptyLast){if(""===t)return 1;if(""===e)return-1}return t===e?0:("string"!=typeof t&&(t=t.toString()),-1===t.localeCompare(e)?-1*i:i)},getEventName:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e=e||"".concat(+new Date).concat(~~(1e6*Math.random())),"".concat(t,"-").concat(e)},hasDetailViewIcon:function(t){return t.detailView&&t.detailViewIcon&&!t.cardView},getDetailViewIndexOffset:function(t){return this.hasDetailViewIcon(t)&&"right"!==t.detailViewAlign?1:0},checkAutoMergeCells:function(t){var e,i=u(t);try{for(i.s();!(e=i.n()).done;)for(var n=e.value,o=0,a=Object.keys(n);o<a.length;o++){var r=a[o];if(r.startsWith("_")&&(r.endsWith("_rowspan")||r.endsWith("_colspan")))return!0}}catch(t){i.e(t)}finally{i.f()}return!1},deepCopy:function(t){return void 0===t?t:i.default.extend(!0,Array.isArray(t)?[]:{},t)},debounce:function(t,e,i){var n;return function(){var o=this,a=arguments,r=function(){n=null,i||t.apply(o,a)},s=i&&!n;clearTimeout(n),n=setTimeout(r,e),s&&t.apply(o,a)}}},uv=hv.getBootstrapVersion(),dv={3:{classes:{buttonsPrefix:"btn",buttons:"default",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"pull",inputGroup:"input-group",inputPrefix:"input-",input:"form-control",select:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],toolbarDropdownItem:'<li class="dropdown-item-marker" role="menuitem"><label>%s</label></li>',toolbarDropdownSeparator:'<li class="divider"></li>',pageDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],pageDropdownItem:'<li role="menuitem" class="%s"><a href="#">%s</a></li>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<span class="input-group-btn">%s</span></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},4:{classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",select:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},5:{classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",select:"form-select",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{dataToggle:"data-bs-toggle",toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s%s</div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}}}[uv],fv={height:void 0,classes:"table table-bordered table-hover",buttons:{},theadClasses:"",headerStyle:function(t){return{}},rowStyle:function(t,e){return{}},rowAttributes:function(t,e){return{}},undefinedText:"-",locale:void 0,virtualScroll:!1,virtualScrollItemHeight:void 0,sortable:!0,sortClass:void 0,silentSort:!0,sortEmptyLast:!1,sortName:void 0,sortOrder:void 0,sortReset:!1,sortStable:!1,sortResetPage:!1,rememberOrder:!1,serverSort:!0,customSort:void 0,columns:[[]],data:[],url:void 0,method:"get",cache:!0,contentType:"application/json",dataType:"json",ajax:void 0,ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},totalField:"total",totalNotFilteredField:"totalNotFiltered",dataField:"rows",footerField:"footer",pagination:!1,paginationParts:["pageInfo","pageSize","pageList"],showExtendedPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,totalNotFiltered:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",paginationSuccessivelySize:5,paginationPagesBySide:1,paginationUseIntermediate:!1,search:!1,searchHighlight:!1,searchOnEnterKey:!1,strictSearch:!1,regexSearch:!1,searchSelector:!1,visibleSearch:!1,showButtonIcons:!0,showButtonText:!1,showSearchButton:!1,showSearchClearButton:!1,trimOnSearch:!0,searchAlign:"right",searchTimeOut:500,searchText:"",customSearch:void 0,showHeader:!0,showFooter:!1,footerStyle:function(t){return{}},searchAccentNeutralise:!1,showColumns:!1,showColumnsToggleAll:!1,showColumnsSearch:!1,minimumCountColumns:1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,showFullscreen:!1,smartDisplay:!0,escape:!1,filterOptions:{filterAlgorithm:"and"},idField:void 0,selectItemName:"btSelectItem",clickToSelect:!1,ignoreClickToSelectOn:function(t){var e=t.tagName;return["A","BUTTON"].includes(e)},singleSelect:!1,checkboxHeader:!0,maintainMetaData:!1,multipleSelectRow:!1,uniqueId:void 0,cardView:!1,detailView:!1,detailViewIcon:!0,detailViewByClick:!1,detailViewAlign:"left",detailFormatter:function(t,e){return""},detailFilter:function(t,e){return!0},toolbar:void 0,toolbarAlign:"left",buttonsToolbar:void 0,buttonsAlign:"right",buttonsOrder:["paginationSwitch","refresh","toggle","fullscreen","columns"],buttonsPrefix:dv.classes.buttonsPrefix,buttonsClass:dv.classes.buttons,iconsPrefix:void 0,icons:{},iconSize:void 0,loadingFontSize:"auto",loadingTemplate:function(t){return'<span class="loading-wrap">\n      <span class="loading-text">'.concat(t,'</span>\n      <span class="animation-wrap"><span class="animation-dot"></span></span>\n      </span>\n    ')},onAll:function(t,e){return!1},onClickCell:function(t,e,i,n){return!1},onDblClickCell:function(t,e,i,n){return!1},onClickRow:function(t,e){return!1},onDblClickRow:function(t,e){return!1},onSort:function(t,e){return!1},onCheck:function(t){return!1},onUncheck:function(t){return!1},onCheckAll:function(t){return!1},onUncheckAll:function(t){return!1},onCheckSome:function(t){return!1},onUncheckSome:function(t){return!1},onLoadSuccess:function(t){return!1},onLoadError:function(t){return!1},onColumnSwitch:function(t,e){return!1},onColumnSwitchAll:function(t){return!1},onPageChange:function(t,e){return!1},onSearch:function(t){return!1},onToggle:function(t){return!1},onPreBody:function(t){return!1},onPostBody:function(){return!1},onPostHeader:function(){return!1},onPostFooter:function(){return!1},onExpandRow:function(t,e,i){return!1},onCollapseRow:function(t,e){return!1},onRefreshOptions:function(t){return!1},onRefresh:function(t){return!1},onResetView:function(){return!1},onScrollBody:function(){return!1},onTogglePagination:function(t){return!1},onVirtualScroll:function(t,e){return!1}},pv={formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(t){return"".concat(t," rows per page")},formatShowingRows:function(t,e,i,n){return void 0!==n&&n>0&&n>i?"Showing ".concat(t," to ").concat(e," of ").concat(i," rows (filtered from ").concat(n," total rows)"):"Showing ".concat(t," to ").concat(e," of ").concat(i," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatSearch:function(){return"Search"},formatClearSearch:function(){return"Clear Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"}},gv={field:void 0,title:void 0,titleTooltip:void 0,class:void 0,width:void 0,widthUnit:"px",rowspan:void 0,colspan:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,cellStyle:void 0,radio:!1,checkbox:!1,checkboxEnabled:!0,clickToSelect:!0,showSelectTitle:!1,sortable:!1,sortName:void 0,order:"asc",sorter:void 0,visible:!0,switchable:!0,cardVisible:!0,searchable:!0,formatter:void 0,footerFormatter:void 0,detailFormatter:void 0,searchFormatter:!0,searchHighlightFormatter:!1,escape:void 0,events:void 0};Object.assign(fv,pv);var vv={VERSION:"1.21.2",THEME:"bootstrap".concat(uv),CONSTANTS:dv,DEFAULTS:fv,COLUMN_DEFAULTS:gv,METHODS:["getOptions","refreshOptions","getData","getSelections","load","append","prepend","remove","removeAll","insertRow","updateRow","getRowByUniqueId","updateByUniqueId","removeByUniqueId","updateCell","updateCellByUniqueId","showRow","hideRow","getHiddenRows","showColumn","hideColumn","getVisibleColumns","getHiddenColumns","showAllColumns","hideAllColumns","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","destroy","resetView","showLoading","hideLoading","togglePagination","toggleFullscreen","toggleView","resetSearch","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","toggleDetailView","expandRow","collapseRow","expandRowByUniqueId","collapseRowByUniqueId","expandAllRows","collapseAllRows","updateColumnTitle","updateFormatText"],EVENTS:{"all.bs.table":"onAll","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","column-switch-all.bs.table":"onColumnSwitchAll","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","post-footer.bs.table":"onPostFooter","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh","scroll-body.bs.table":"onScrollBody","toggle-pagination.bs.table":"onTogglePagination","virtual-scroll.bs.table":"onVirtualScroll"},LOCALES:{en:pv,"en-US":pv}},bv=function(){function t(e){var i=this;o(this,t),this.rows=e.rows,this.scrollEl=e.scrollEl,this.contentEl=e.contentEl,this.callback=e.callback,this.itemHeight=e.itemHeight,this.cache={},this.scrollTop=this.scrollEl.scrollTop,this.initDOM(this.rows,e.fixedScroll),this.scrollEl.scrollTop=this.scrollTop,this.lastCluster=0;var n=function(){i.lastCluster!==(i.lastCluster=i.getNum())&&(i.initDOM(i.rows),i.callback(i.startIndex,i.endIndex))};this.scrollEl.addEventListener("scroll",n,!1),this.destroy=function(){i.contentEl.innerHtml="",i.scrollEl.removeEventListener("scroll",n,!1)}}return r(t,[{key:"initDOM",value:function(t,e){void 0===this.clusterHeight&&(this.cache.scrollTop=this.scrollEl.scrollTop,this.cache.data=this.contentEl.innerHTML=t[0]+t[0]+t[0],this.getRowsHeight(t));var i=this.initData(t,this.getNum(e)),n=i.rows.join(""),o=this.checkChanges("data",n),a=this.checkChanges("top",i.topOffset),r=this.checkChanges("bottom",i.bottomOffset),s=[];o&&a?(i.topOffset&&s.push(this.getExtra("top",i.topOffset)),s.push(n),i.bottomOffset&&s.push(this.getExtra("bottom",i.bottomOffset)),this.startIndex=i.start,this.endIndex=i.end,this.contentEl.innerHTML=s.join(""),e&&(this.contentEl.scrollTop=this.cache.scrollTop)):r&&(this.contentEl.lastChild.style.height="".concat(i.bottomOffset,"px"))}},{key:"getRowsHeight",value:function(){if(void 0===this.itemHeight){var t=this.contentEl.children,e=t[Math.floor(t.length/2)];this.itemHeight=e.offsetHeight}this.blockHeight=50*this.itemHeight,this.clusterRows=200,this.clusterHeight=4*this.blockHeight}},{key:"getNum",value:function(t){return this.scrollTop=t?this.cache.scrollTop:this.scrollEl.scrollTop,Math.floor(this.scrollTop/(this.clusterHeight-this.blockHeight))||0}},{key:"initData",value:function(t,e){if(t.length<50)return{topOffset:0,bottomOffset:0,rowsAbove:0,rows:t};var i=Math.max((this.clusterRows-50)*e,0),n=i+this.clusterRows,o=Math.max(i*this.itemHeight,0),a=Math.max((t.length-n)*this.itemHeight,0),r=[],s=i;o<1&&s++;for(var l=i;l<n;l++)t[l]&&r.push(t[l]);return{start:i,end:n,topOffset:o,bottomOffset:a,rowsAbove:s,rows:r}}},{key:"checkChanges",value:function(t,e){var i=e!==this.cache[t];return this.cache[t]=e,i}},{key:"getExtra",value:function(t,e){var i=document.createElement("tr");return i.className="virtual-scroll-".concat(t),e&&(i.style.height="".concat(e,"px")),i.outerHTML}}]),t}(),mv=function(){function t(e,n){o(this,t),this.options=n,this.$el=i.default(e),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0}return r(t,[{key:"init",value:function(){this.initConstants(),this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()}},{key:"initConstants",value:function(){var t=this.options;this.constants=vv.CONSTANTS,this.constants.theme=i.default.fn.bootstrapTable.theme,this.constants.dataToggle=this.constants.html.dataToggle||"data-toggle";var e=hv.getIconsPrefix(i.default.fn.bootstrapTable.theme),o=hv.getIcons(e);"string"==typeof t.icons&&(t.icons=hv.calculateObjectValue(null,t.icons)),t.iconsPrefix=t.iconsPrefix||i.default.fn.bootstrapTable.defaults.iconsPrefix||e,t.icons=Object.assign(o,i.default.fn.bootstrapTable.defaults.icons,t.icons);var a=t.buttonsPrefix?"".concat(t.buttonsPrefix,"-"):"";this.constants.buttonsClass=[t.buttonsPrefix,a+t.buttonsClass,hv.sprintf("".concat(a,"%s"),t.iconSize)].join(" ").trim(),this.buttons=hv.calculateObjectValue(this,t.buttons,[],{}),"object"!==n(this.buttons)&&(this.buttons={})}},{key:"initLocale",value:function(){if(this.options.locale){var e=i.default.fn.bootstrapTable.locales,n=this.options.locale.split(/-|_/);n[0]=n[0].toLowerCase(),n[1]&&(n[1]=n[1].toUpperCase());var o={};e[this.options.locale]?o=e[this.options.locale]:e[n.join("-")]?o=e[n.join("-")]:e[n[0]]&&(o=e[n[0]]);for(var a=0,r=Object.entries(o);a<r.length;a++){var l=s(r[a],2),c=l[0],h=l[1];this.options[c]===t.DEFAULTS[c]&&(this.options[c]=h)}}}},{key:"initContainer",value:function(){var t=["top","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination clearfix"></div>':"",e=["bottom","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination"></div>':"",n=hv.calculateObjectValue(this.options,this.options.loadingTemplate,[this.options.formatLoadingMessage()]);this.$container=i.default('\n      <div class="bootstrap-table '.concat(this.constants.theme,'">\n      <div class="fixed-table-toolbar"></div>\n      ').concat(t,'\n      <div class="fixed-table-container">\n      <div class="fixed-table-header"><table></table></div>\n      <div class="fixed-table-body">\n      <div class="fixed-table-loading">\n      ').concat(n,'\n      </div>\n      </div>\n      <div class="fixed-table-footer"></div>\n      </div>\n      ').concat(e,"\n      </div>\n    ")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$el.find("tfoot"),this.options.buttonsToolbar?this.$toolbar=i.default("body").find(this.options.buttonsToolbar):this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.$tableLoading.addClass(this.options.classes),this.options.height&&(this.$tableContainer.addClass("fixed-height"),this.options.showFooter&&this.$tableContainer.addClass("has-footer"),this.options.classes.split(" ").includes("table-bordered")&&(this.$tableBody.append('<div class="fixed-table-border"></div>'),this.$tableBorder=this.$tableBody.find(".fixed-table-border"),this.$tableLoading.addClass("fixed-table-border")),this.$tableFooter=this.$container.find(".fixed-table-footer"))}},{key:"initTable",value:function(){var e=this,n=[];if(this.$header=this.$el.find(">thead"),this.$header.length?this.options.theadClasses&&this.$header.addClass(this.options.theadClasses):this.$header=i.default('<thead class="'.concat(this.options.theadClasses,'"></thead>')).appendTo(this.$el),this._headerTrClasses=[],this._headerTrStyles=[],this.$header.find("tr").each((function(t,o){var a=i.default(o),r=[];a.find("th").each((function(t,e){var n=i.default(e);void 0!==n.data("field")&&n.data("field","".concat(n.data("field"))),r.push(i.default.extend({},{title:n.html(),class:n.attr("class"),titleTooltip:n.attr("title"),rowspan:n.attr("rowspan")?+n.attr("rowspan"):void 0,colspan:n.attr("colspan")?+n.attr("colspan"):void 0},n.data()))})),n.push(r),a.attr("class")&&e._headerTrClasses.push(a.attr("class")),a.attr("style")&&e._headerTrStyles.push(a.attr("style"))})),Array.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=i.default.extend(!0,[],n,this.options.columns),this.columns=[],this.fieldsColumnsIndex=[],hv.setFieldIndex(this.options.columns),this.options.columns.forEach((function(n,o){n.forEach((function(n,a){var r=i.default.extend({},t.COLUMN_DEFAULTS,n,{passed:n});void 0!==r.fieldIndex&&(e.columns[r.fieldIndex]=r,e.fieldsColumnsIndex[r.field]=r.fieldIndex),e.options.columns[o][a]=r}))})),!this.options.data.length){var o=hv.trToData(this.columns,this.$el.find(">tbody>tr"));o.length&&(this.options.data=o,this.fromHtml=!0)}this.options.pagination&&"server"!==this.options.sidePagination||(this.footerData=hv.trToData(this.columns,this.$el.find(">tfoot>tr"))),this.footerData&&this.$el.find("tfoot").html("<tr></tr>"),!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()}},{key:"initHeader",value:function(){var t=this,e={},n=[];this.header={fields:[],styles:[],classes:[],formatters:[],detailFormatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},hv.updateFieldGroup(this.options.columns,this.columns),this.options.columns.forEach((function(i,o){var a=[];a.push("<tr".concat(hv.sprintf(' class="%s"',t._headerTrClasses[o])," ").concat(hv.sprintf(' style="%s"',t._headerTrStyles[o]),">"));var r="";if(0===o&&hv.hasDetailViewIcon(t.options)){var l=t.options.columns.length>1?' rowspan="'.concat(t.options.columns.length,'"'):"";r='<th class="detail"'.concat(l,'>\n          <div class="fht-cell"></div>\n          </th>')}r&&"right"!==t.options.detailViewAlign&&a.push(r),i.forEach((function(i,n){var r=hv.sprintf(' class="%s"',i.class),l=i.widthUnit,c=parseFloat(i.width),h=i.halign?i.halign:i.align,u=hv.sprintf("text-align: %s; ",h),d=hv.sprintf("text-align: %s; ",i.align),f=hv.sprintf("vertical-align: %s; ",i.valign);if(f+=hv.sprintf("width: %s; ",!i.checkbox&&!i.radio||c?c?c+l:void 0:i.showSelectTitle?void 0:"36px"),void 0!==i.fieldIndex||i.visible){var p=hv.calculateObjectValue(null,t.options.headerStyle,[i]),g=[],v="";if(p&&p.css)for(var b=0,m=Object.entries(p.css);b<m.length;b++){var y=s(m[b],2),w=y[0],S=y[1];g.push("".concat(w,": ").concat(S))}if(p&&p.classes&&(v=hv.sprintf(' class="%s"',i.class?[i.class,p.classes].join(" "):p.classes)),void 0!==i.fieldIndex){if(t.header.fields[i.fieldIndex]=i.field,t.header.styles[i.fieldIndex]=d+f,t.header.classes[i.fieldIndex]=r,t.header.formatters[i.fieldIndex]=i.formatter,t.header.detailFormatters[i.fieldIndex]=i.detailFormatter,t.header.events[i.fieldIndex]=i.events,t.header.sorters[i.fieldIndex]=i.sorter,t.header.sortNames[i.fieldIndex]=i.sortName,t.header.cellStyles[i.fieldIndex]=i.cellStyle,t.header.searchables[i.fieldIndex]=i.searchable,!i.visible)return;if(t.options.cardView&&!i.cardVisible)return;e[i.field]=i}a.push("<th".concat(hv.sprintf(' title="%s"',i.titleTooltip)),i.checkbox||i.radio?hv.sprintf(' class="bs-checkbox %s"',i.class||""):v||r,hv.sprintf(' style="%s"',u+f+g.join("; ")),hv.sprintf(' rowspan="%s"',i.rowspan),hv.sprintf(' colspan="%s"',i.colspan),hv.sprintf(' data-field="%s"',i.field),0===n&&o>0?" data-not-first-th":"",">"),a.push(hv.sprintf('<div class="th-inner %s">',t.options.sortable&&i.sortable?"sortable".concat("center"===h?" sortable-center":""," both"):""));var x=t.options.escape?hv.escapeHTML(i.title):i.title,k=x;i.checkbox&&(x="",!t.options.singleSelect&&t.options.checkboxHeader&&(x='<label><input name="btSelectAll" type="checkbox" /><span></span></label>'),t.header.stateField=i.field),i.radio&&(x="",t.header.stateField=i.field),!x&&i.showSelectTitle&&(x+=k),a.push(x),a.push("</div>"),a.push('<div class="fht-cell"></div>'),a.push("</div>"),a.push("</th>")}})),r&&"right"===t.options.detailViewAlign&&a.push(r),a.push("</tr>"),a.length>3&&n.push(a.join(""))})),this.$header.html(n.join("")),this.$header.find("th[data-field]").each((function(t,n){i.default(n).data(e[i.default(n).data("field")])})),this.$container.off("click",".th-inner").on("click",".th-inner",(function(e){var n=i.default(e.currentTarget);if(t.options.detailView&&!n.parent().hasClass("bs-checkbox")&&n.closest(".bootstrap-table")[0]!==t.$container[0])return!1;t.options.sortable&&n.parent().data().sortable&&t.onSort(e)}));var o=hv.getEventName("resize.bootstrap-table",this.$el.attr("id"));i.default(window).off(o),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),i.default(window).on(o,(function(){return t.resetView()}))),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",(function(e){e.stopPropagation();var n=i.default(e.currentTarget).prop("checked");t[n?"checkAll":"uncheckAll"](),t.updateSelected()}))}},{key:"initData",value:function(t,e){"append"===e?this.options.data=this.options.data.concat(t):"prepend"===e?this.options.data=[].concat(t).concat(this.options.data):(t=t||hv.deepCopy(this.options.data),this.options.data=Array.isArray(t)?t:t[this.options.dataField]),this.data=l(this.options.data),this.options.sortReset&&(this.unsortedData=l(this.data)),"server"!==this.options.sidePagination&&this.initSort()}},{key:"initSort",value:function(){var t=this,e=this.options.sortName,i="desc"===this.options.sortOrder?-1:1,n=this.header.fields.indexOf(this.options.sortName),o=0;-1!==n?(this.options.sortStable&&this.data.forEach((function(t,e){t.hasOwnProperty("_position")||(t._position=e)})),this.options.customSort?hv.calculateObjectValue(this.options,this.options.customSort,[this.options.sortName,this.options.sortOrder,this.data]):this.data.sort((function(o,a){t.header.sortNames[n]&&(e=t.header.sortNames[n]);var r=hv.getItemField(o,e,t.options.escape),s=hv.getItemField(a,e,t.options.escape),l=hv.calculateObjectValue(t.header,t.header.sorters[n],[r,s,o,a]);return void 0!==l?t.options.sortStable&&0===l?i*(o._position-a._position):i*l:hv.sort(r,s,i,t.options,o._position,a._position)})),void 0!==this.options.sortClass&&(clearTimeout(o),o=setTimeout((function(){t.$el.removeClass(t.options.sortClass);var e=t.$header.find('[data-field="'.concat(t.options.sortName,'"]')).index();t.$el.find("tr td:nth-child(".concat(e+1,")")).addClass(t.options.sortClass)}),250))):this.options.sortReset&&(this.data=l(this.unsortedData))}},{key:"onSort",value:function(t){var e=t.type,n=t.currentTarget,o="keypress"===e?i.default(n):i.default(n).parent(),a=this.$header.find("th").eq(o.index());if(this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===o.data("field")){var r=this.options.sortOrder,s=this.columns[this.fieldsColumnsIndex[o.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[o.data("field")]].order;void 0===r?this.options.sortOrder="asc":"asc"===r?this.options.sortOrder=this.options.sortReset?"asc"===s?"desc":void 0:"desc":"desc"===this.options.sortOrder&&(this.options.sortOrder=this.options.sortReset?"desc"===s?"asc":void 0:"asc"),void 0===this.options.sortOrder&&(this.options.sortName=void 0)}else this.options.sortName=o.data("field"),this.options.rememberOrder?this.options.sortOrder="asc"===o.data("order")?"desc":"asc":this.options.sortOrder=this.columns[this.fieldsColumnsIndex[o.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[o.data("field")]].order;if(this.trigger("sort",this.options.sortName,this.options.sortOrder),o.add(a).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination&&this.options.serverSort)return this.options.pageNumber=1,void this.initServer(this.options.silentSort);this.options.pagination&&this.options.sortResetPage&&(this.options.pageNumber=1,this.initPagination()),this.initSort(),this.initBody()}},{key:"initToolbar",value:function(){var t,e=this,o=this.options,a=[],r=0,l=0;this.$toolbar.find(".bs-bars").children().length&&i.default("body").append(i.default(o.toolbar)),this.$toolbar.html(""),"string"!=typeof o.toolbar&&"object"!==n(o.toolbar)||i.default(hv.sprintf('<div class="bs-bars %s-%s"></div>',this.constants.classes.pull,o.toolbarAlign)).appendTo(this.$toolbar).append(i.default(o.toolbar)),a=['<div class="'.concat(["columns","columns-".concat(o.buttonsAlign),this.constants.classes.buttonsGroup,"".concat(this.constants.classes.pull,"-").concat(o.buttonsAlign)].join(" "),'">')],"string"==typeof o.buttonsOrder&&(o.buttonsOrder=o.buttonsOrder.replace(/\[|\]| |'/g,"").split(",")),this.buttons=Object.assign(this.buttons,{paginationSwitch:{text:o.pagination?o.formatPaginationSwitchUp():o.formatPaginationSwitchDown(),icon:o.pagination?o.icons.paginationSwitchDown:o.icons.paginationSwitchUp,render:!1,event:this.togglePagination,attributes:{"aria-label":o.formatPaginationSwitch(),title:o.formatPaginationSwitch()}},refresh:{text:o.formatRefresh(),icon:o.icons.refresh,render:!1,event:this.refresh,attributes:{"aria-label":o.formatRefresh(),title:o.formatRefresh()}},toggle:{text:o.formatToggleOn(),icon:o.icons.toggleOff,render:!1,event:this.toggleView,attributes:{"aria-label":o.formatToggleOn(),title:o.formatToggleOn()}},fullscreen:{text:o.formatFullscreen(),icon:o.icons.fullscreen,render:!1,event:this.toggleFullscreen,attributes:{"aria-label":o.formatFullscreen(),title:o.formatFullscreen()}},columns:{render:!1,html:function(){var t=[];if(t.push('<div class="keep-open '.concat(e.constants.classes.buttonsDropdown,'" title="').concat(o.formatColumns(),'">\n            <button class="').concat(e.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(e.constants.dataToggle,'="dropdown"\n            aria-label="').concat(o.formatColumns(),'" title="').concat(o.formatColumns(),'">\n            ').concat(o.showButtonIcons?hv.sprintf(e.constants.html.icon,o.iconsPrefix,o.icons.columns):"","\n            ").concat(o.showButtonText?o.formatColumns():"","\n            ").concat(e.constants.html.dropdownCaret,"\n            </button>\n            ").concat(e.constants.html.toolbarDropdown[0])),o.showColumnsSearch&&(t.push(hv.sprintf(e.constants.html.toolbarDropdownItem,hv.sprintf('<input type="text" class="%s" name="columnsSearch" placeholder="%s" autocomplete="off">',e.constants.classes.input,o.formatSearch()))),t.push(e.constants.html.toolbarDropdownSeparator)),o.showColumnsToggleAll){var i=e.getVisibleColumns().length===e.columns.filter((function(t){return!e.isSelectionColumn(t)})).length;t.push(hv.sprintf(e.constants.html.toolbarDropdownItem,hv.sprintf('<input type="checkbox" class="toggle-all" %s> <span>%s</span>',i?'checked="checked"':"",o.formatColumnsToggleAll()))),t.push(e.constants.html.toolbarDropdownSeparator)}var n=0;return e.columns.forEach((function(t){t.visible&&n++})),e.columns.forEach((function(i,a){if(!e.isSelectionColumn(i)&&(!o.cardView||i.cardVisible)){var r=i.visible?' checked="checked"':"",s=n<=o.minimumCountColumns&&r?' disabled="disabled"':"";i.switchable&&(t.push(hv.sprintf(e.constants.html.toolbarDropdownItem,hv.sprintf('<input type="checkbox" data-field="%s" value="%s"%s%s> <span>%s</span>',i.field,a,r,s,i.title))),l++)}})),t.push(e.constants.html.toolbarDropdown[1],"</div>"),t.join("")}}});for(var c={},h=0,d=Object.entries(this.buttons);h<d.length;h++){var f=s(d[h],2),p=f[0],g=f[1],v=void 0;if(g.hasOwnProperty("html"))"function"==typeof g.html?v=g.html():"string"==typeof g.html&&(v=g.html);else{if(v='<button class="'.concat(this.constants.buttonsClass,'" type="button" name="').concat(p,'"'),g.hasOwnProperty("attributes"))for(var b=0,m=Object.entries(g.attributes);b<m.length;b++){var y=s(m[b],2),w=y[0],S=y[1];v+=" ".concat(w,'="').concat(S,'"')}v+=">",o.showButtonIcons&&g.hasOwnProperty("icon")&&(v+="".concat(hv.sprintf(this.constants.html.icon,o.iconsPrefix,g.icon)," ")),o.showButtonText&&g.hasOwnProperty("text")&&(v+=g.text),v+="</button>"}c[p]=v;var x="show".concat(p.charAt(0).toUpperCase()).concat(p.substring(1)),k=o[x];!(!g.hasOwnProperty("render")||g.hasOwnProperty("render")&&g.render)||void 0!==k&&!0!==k||(o[x]=!0),o.buttonsOrder.includes(p)||o.buttonsOrder.push(p)}var O,C=u(o.buttonsOrder);try{for(C.s();!(O=C.n()).done;){var T=O.value;o["show".concat(T.charAt(0).toUpperCase()).concat(T.substring(1))]&&a.push(c[T])}}catch(t){C.e(t)}finally{C.f()}a.push("</div>"),(this.showToolbar||a.length>2)&&this.$toolbar.append(a.join(""));for(var I=0,P=Object.entries(this.buttons);I<P.length;I++){var A=s(P[I],2),$=A[0],R=A[1];if(R.hasOwnProperty("event")){if("function"==typeof R.event||"string"==typeof R.event)if("continue"===function(){var t="string"==typeof R.event?window[R.event]:R.event;return e.$toolbar.find('button[name="'.concat($,'"]')).off("click").on("click",(function(){return t.call(e)})),"continue"}())continue;for(var E=function(){var t=s(F[j],2),i=t[0],n=t[1],o="string"==typeof n?window[n]:n;e.$toolbar.find('button[name="'.concat($,'"]')).off(i).on(i,(function(){return o.call(e)}))},j=0,F=Object.entries(R.event);j<F.length;j++)E()}}if(o.showColumns){var _=(t=this.$toolbar.find(".keep-open")).find('input[type="checkbox"]:not(".toggle-all")'),D=t.find('input[type="checkbox"].toggle-all');if(l<=o.minimumCountColumns&&t.find("input").prop("disabled",!0),t.find("li, label").off("click").on("click",(function(t){t.stopImmediatePropagation()})),_.off("click").on("click",(function(t){var n=t.currentTarget,o=i.default(n);e._toggleColumn(o.val(),o.prop("checked"),!1),e.trigger("column-switch",o.data("field"),o.prop("checked")),D.prop("checked",_.filter(":checked").length===e.columns.filter((function(t){return!e.isSelectionColumn(t)})).length)})),D.off("click").on("click",(function(t){var n=t.currentTarget;e._toggleAllColumns(i.default(n).prop("checked")),e.trigger("column-switch-all",i.default(n).prop("checked"))})),o.showColumnsSearch){var N=t.find('[name="columnsSearch"]'),V=t.find(".dropdown-item-marker");N.on("keyup paste change",(function(t){var e=t.currentTarget,n=i.default(e).val().toLowerCase();V.show(),_.each((function(t,e){var o=i.default(e).parents(".dropdown-item-marker");o.text().toLowerCase().includes(n)||o.hide()}))}))}}var B=function(t){var i="keyup drop blur mouseup";t.off(i).on(i,(function(t){o.searchOnEnterKey&&13!==t.keyCode||[37,38,39,40].includes(t.keyCode)||(clearTimeout(r),r=setTimeout((function(){e.onSearch({currentTarget:t.currentTarget})}),o.searchTimeOut))}))};if((o.search||this.showSearchClearButton)&&"string"!=typeof o.searchSelector){a=[];var L=hv.sprintf(this.constants.html.searchButton,this.constants.buttonsClass,o.formatSearch(),o.showButtonIcons?hv.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.search):"",o.showButtonText?o.formatSearch():""),H=hv.sprintf(this.constants.html.searchClearButton,this.constants.buttonsClass,o.formatClearSearch(),o.showButtonIcons?hv.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.clearSearch):"",o.showButtonText?o.formatClearSearch():""),M='<input class="'.concat(this.constants.classes.input,"\n        ").concat(hv.sprintf(" %s%s",this.constants.classes.inputPrefix,o.iconSize),'\n        search-input" type="search" placeholder="').concat(o.formatSearch(),'" autocomplete="off">'),U=M;if(o.showSearchButton||o.showSearchClearButton){var z=(o.showSearchButton?L:"")+(o.showSearchClearButton?H:"");U=o.search?hv.sprintf(this.constants.html.inputGroup,M,z):z}a.push(hv.sprintf('\n        <div class="'.concat(this.constants.classes.pull,"-").concat(o.searchAlign," search ").concat(this.constants.classes.inputGroup,'">\n          %s\n        </div>\n      '),U)),this.$toolbar.append(a.join(""));var q=hv.getSearchInput(this);o.showSearchButton?(this.$toolbar.find(".search button[name=search]").off("click").on("click",(function(){clearTimeout(r),r=setTimeout((function(){e.onSearch({currentTarget:q})}),o.searchTimeOut)})),o.searchOnEnterKey&&B(q)):B(q),o.showSearchClearButton&&this.$toolbar.find(".search button[name=clearSearch]").click((function(){e.resetSearch()}))}else if("string"==typeof o.searchSelector){B(hv.getSearchInput(this))}}},{key:"onSearch",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.currentTarget,n=t.firedByInitSearchText,o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(void 0!==e&&i.default(e).length&&o){var a=i.default(e).val().trim();if(this.options.trimOnSearch&&i.default(e).val()!==a&&i.default(e).val(a),this.searchText===a)return;var r=hv.getSearchInput(this),s=e instanceof jQuery?e:i.default(e);(s.is(r)||s.hasClass("search-input"))&&(this.searchText=a,this.options.searchText=a)}n||this.options.cookie||(this.options.pageNumber=1),this.initSearch(),n?"client"===this.options.sidePagination&&this.updatePagination():this.updatePagination(),this.trigger("search",this.searchText)}},{key:"initSearch",value:function(){var t=this;if(this.filterOptions=this.filterOptions||this.options.filterOptions,"server"!==this.options.sidePagination){if(this.options.customSearch)return this.data=hv.calculateObjectValue(this.options,this.options.customSearch,[this.options.data,this.searchText,this.filterColumns]),this.options.sortReset&&(this.unsortedData=l(this.data)),void this.initSort();var e=this.searchText&&(this.fromHtml?hv.escapeHTML(this.searchText):this.searchText),i=e?e.toLowerCase():"",n=hv.isEmptyObject(this.filterColumns)?null:this.filterColumns;this.options.searchAccentNeutralise&&(i=hv.normalizeAccent(i)),"function"==typeof this.filterOptions.filterAlgorithm?this.data=this.options.data.filter((function(e){return t.filterOptions.filterAlgorithm.apply(null,[e,n])})):"string"==typeof this.filterOptions.filterAlgorithm&&(this.data=n?this.options.data.filter((function(e){var i=t.filterOptions.filterAlgorithm;if("and"===i){for(var o in n)if(Array.isArray(n[o])&&!n[o].includes(e[o])||!Array.isArray(n[o])&&e[o]!==n[o])return!1}else if("or"===i){var a=!1;for(var r in n)(Array.isArray(n[r])&&n[r].includes(e[r])||!Array.isArray(n[r])&&e[r]===n[r])&&(a=!0);return a}return!0})):l(this.options.data));var o=this.getVisibleFields();this.data=i?this.data.filter((function(n,a){for(var r=0;r<t.header.fields.length;r++)if(t.header.searchables[r]&&(!t.options.visibleSearch||-1!==o.indexOf(t.header.fields[r]))){var s=hv.isNumeric(t.header.fields[r])?parseInt(t.header.fields[r],10):t.header.fields[r],l=t.columns[t.fieldsColumnsIndex[s]],c=void 0;if("string"==typeof s){c=n;for(var h=s.split("."),u=0;u<h.length;u++){if(null===c[h[u]]){c=null;break}c=c[h[u]]}}else c=n[s];if(t.options.searchAccentNeutralise&&(c=hv.normalizeAccent(c)),l&&l.searchFormatter&&(c=hv.calculateObjectValue(l,t.header.formatters[r],[c,n,a,l.field],c)),"string"==typeof c||"number"==typeof c){if(t.options.strictSearch&&"".concat(c).toLowerCase()===i||t.options.regexSearch&&hv.regexCompare(c,e))return!0;var d=/(?:(<=|=>|=<|>=|>|<)(?:\s+)?(-?\d+)?|(-?\d+)?(\s+)?(<=|=>|=<|>=|>|<))/gm.exec(t.searchText),f=!1;if(d){var p=d[1]||"".concat(d[5],"l"),g=d[2]||d[3],v=parseInt(c,10),b=parseInt(g,10);switch(p){case">":case"<l":f=v>b;break;case"<":case">l":f=v<b;break;case"<=":case"=<":case">=l":case"=>l":f=v<=b;break;case">=":case"=>":case"<=l":case"=<l":f=v>=b}}if(f||"".concat(c).toLowerCase().includes(i))return!0}}return!1})):this.data,this.options.sortReset&&(this.unsortedData=l(this.data)),this.initSort()}}},{key:"initPagination",value:function(){var t=this,e=this.options;if(e.pagination){this.$pagination.show();var i,n,o,a,r,s,l,c=[],h=!1,u=this.getData({includeHiddenRows:!1}),d=e.pageList;if("string"==typeof d&&(d=d.replace(/\[|\]| /g,"").toLowerCase().split(",")),d=d.map((function(t){return"string"==typeof t?t.toLowerCase()===e.formatAllRows().toLowerCase()||["all","unlimited"].includes(t.toLowerCase())?e.formatAllRows():+t:t})),this.paginationParts=e.paginationParts,"string"==typeof this.paginationParts&&(this.paginationParts=this.paginationParts.replace(/\[|\]| |'/g,"").split(",")),"server"!==e.sidePagination&&(e.totalRows=u.length),this.totalPages=0,e.totalRows&&(e.pageSize===e.formatAllRows()&&(e.pageSize=e.totalRows,h=!0),this.totalPages=1+~~((e.totalRows-1)/e.pageSize),e.totalPages=this.totalPages),this.totalPages>0&&e.pageNumber>this.totalPages&&(e.pageNumber=this.totalPages),this.pageFrom=(e.pageNumber-1)*e.pageSize+1,this.pageTo=e.pageNumber*e.pageSize,this.pageTo>e.totalRows&&(this.pageTo=e.totalRows),this.options.pagination&&"server"!==this.options.sidePagination&&(this.options.totalNotFiltered=this.options.data.length),this.options.showExtendedPagination||(this.options.totalNotFiltered=void 0),(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&c.push('<div class="'.concat(this.constants.classes.pull,"-").concat(e.paginationDetailHAlign,' pagination-detail">')),this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")){var f=this.paginationParts.includes("pageInfoShort")?e.formatDetailPagination(e.totalRows):e.formatShowingRows(this.pageFrom,this.pageTo,e.totalRows,e.totalNotFiltered);c.push('<span class="pagination-info">\n      '.concat(f,"\n      </span>"))}if(this.paginationParts.includes("pageSize")){c.push('<div class="page-list">');var p=['<div class="'.concat(this.constants.classes.paginationDropdown,'">\n        <button class="').concat(this.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(this.constants.dataToggle,'="dropdown">\n        <span class="page-size">\n        ').concat(h?e.formatAllRows():e.pageSize,"\n        </span>\n        ").concat(this.constants.html.dropdownCaret,"\n        </button>\n        ").concat(this.constants.html.pageDropdown[0])];d.forEach((function(i,n){var o;(!e.smartDisplay||0===n||d[n-1]<e.totalRows||i===e.formatAllRows())&&(o=h?i===e.formatAllRows()?t.constants.classes.dropdownActive:"":i===e.pageSize?t.constants.classes.dropdownActive:"",p.push(hv.sprintf(t.constants.html.pageDropdownItem,o,i)))})),p.push("".concat(this.constants.html.pageDropdown[1],"</div>")),c.push(e.formatRecordsPerPage(p.join("")))}if((this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&c.push("</div></div>"),this.paginationParts.includes("pageList")){c.push('<div class="'.concat(this.constants.classes.pull,"-").concat(e.paginationHAlign,' pagination">'),hv.sprintf(this.constants.html.pagination[0],hv.sprintf(" pagination-%s",e.iconSize)),hv.sprintf(this.constants.html.paginationItem," page-pre",e.formatSRPaginationPreText(),e.paginationPreText)),this.totalPages<e.paginationSuccessivelySize?(n=1,o=this.totalPages):o=(n=e.pageNumber-e.paginationPagesBySide)+2*e.paginationPagesBySide,e.pageNumber<e.paginationSuccessivelySize-1&&(o=e.paginationSuccessivelySize),e.paginationSuccessivelySize>this.totalPages-n&&(n=n-(e.paginationSuccessivelySize-(this.totalPages-n))+1),n<1&&(n=1),o>this.totalPages&&(o=this.totalPages);var g=Math.round(e.paginationPagesBySide/2),v=function(i){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return hv.sprintf(t.constants.html.paginationItem,n+(i===e.pageNumber?" ".concat(t.constants.classes.paginationActive):""),e.formatSRPaginationPageText(i),i)};if(n>1){var b=e.paginationPagesBySide;for(b>=n&&(b=n-1),i=1;i<=b;i++)c.push(v(i));n-1===b+1?(i=n-1,c.push(v(i))):n-1>b&&(n-2*e.paginationPagesBySide>e.paginationPagesBySide&&e.paginationUseIntermediate?(i=Math.round((n-g)/2+g),c.push(v(i," page-intermediate"))):c.push(hv.sprintf(this.constants.html.paginationItem," page-first-separator disabled","","...")))}for(i=n;i<=o;i++)c.push(v(i));if(this.totalPages>o){var m=this.totalPages-(e.paginationPagesBySide-1);for(o>=m&&(m=o+1),o+1===m-1?(i=o+1,c.push(v(i))):m>o+1&&(this.totalPages-o>2*e.paginationPagesBySide&&e.paginationUseIntermediate?(i=Math.round((this.totalPages-g-o)/2+o),c.push(v(i," page-intermediate"))):c.push(hv.sprintf(this.constants.html.paginationItem," page-last-separator disabled","","..."))),i=m;i<=this.totalPages;i++)c.push(v(i))}c.push(hv.sprintf(this.constants.html.paginationItem," page-next",e.formatSRPaginationNextText(),e.paginationNextText)),c.push(this.constants.html.pagination[1],"</div>")}this.$pagination.html(c.join(""));var y=["bottom","both"].includes(e.paginationVAlign)?" ".concat(this.constants.classes.dropup):"";this.$pagination.last().find(".page-list > div").addClass(y),e.onlyInfoPagination||(a=this.$pagination.find(".page-list a"),r=this.$pagination.find(".page-pre"),s=this.$pagination.find(".page-next"),l=this.$pagination.find(".page-item").not(".page-next, .page-pre, .page-last-separator, .page-first-separator"),this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),e.smartDisplay&&(d.length<2||e.totalRows<=d[0])&&this.$pagination.find("div.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"](),e.paginationLoop||(1===e.pageNumber&&r.addClass("disabled"),e.pageNumber===this.totalPages&&s.addClass("disabled")),h&&(e.pageSize=e.formatAllRows()),a.off("click").on("click",(function(e){return t.onPageListChange(e)})),r.off("click").on("click",(function(e){return t.onPagePre(e)})),s.off("click").on("click",(function(e){return t.onPageNext(e)})),l.off("click").on("click",(function(e){return t.onPageNumber(e)})))}else this.$pagination.hide()}},{key:"updatePagination",value:function(t){t&&i.default(t.currentTarget).hasClass("disabled")||(this.options.maintainMetaData||this.resetRows(),this.initPagination(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize),"server"===this.options.sidePagination?this.initServer():this.initBody())}},{key:"onPageListChange",value:function(t){t.preventDefault();var e=i.default(t.currentTarget);return e.parent().addClass(this.constants.classes.dropdownActive).siblings().removeClass(this.constants.classes.dropdownActive),this.options.pageSize=e.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+e.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(t),!1}},{key:"onPagePre",value:function(t){if(!i.default(t.target).hasClass("disabled"))return t.preventDefault(),this.options.pageNumber-1==0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(t),!1}},{key:"onPageNext",value:function(t){if(!i.default(t.target).hasClass("disabled"))return t.preventDefault(),this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(t),!1}},{key:"onPageNumber",value:function(t){if(t.preventDefault(),this.options.pageNumber!==+i.default(t.currentTarget).text())return this.options.pageNumber=+i.default(t.currentTarget).text(),this.updatePagination(t),!1}},{key:"initRow",value:function(t,e,i,o){var a=this,r=[],l={},c=[],h="",u={},d=[];if(!(hv.findIndex(this.hiddenRows,t)>-1)){if((l=hv.calculateObjectValue(this.options,this.options.rowStyle,[t,e],l))&&l.css)for(var f=0,p=Object.entries(l.css);f<p.length;f++){var g=s(p[f],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}if(u=hv.calculateObjectValue(this.options,this.options.rowAttributes,[t,e],u))for(var m=0,y=Object.entries(u);m<y.length;m++){var w=s(y[m],2),S=w[0],x=w[1];d.push("".concat(S,'="').concat(hv.escapeHTML(x),'"'))}if(t._data&&!hv.isEmptyObject(t._data))for(var k=0,O=Object.entries(t._data);k<O.length;k++){var C=s(O[k],2),T=C[0],I=C[1];if("index"===T)return;h+=" data-".concat(T,"='").concat("object"===n(I)?JSON.stringify(I):I,"'")}r.push("<tr",hv.sprintf(" %s",d.length?d.join(" "):void 0),hv.sprintf(' id="%s"',Array.isArray(t)?void 0:t._id),hv.sprintf(' class="%s"',l.classes||(Array.isArray(t)?void 0:t._class)),hv.sprintf(' style="%s"',Array.isArray(t)?void 0:t._style),' data-index="'.concat(e,'"'),hv.sprintf(' data-uniqueid="%s"',hv.getItemField(t,this.options.uniqueId,!1)),hv.sprintf(' data-has-detail-view="%s"',this.options.detailView&&hv.calculateObjectValue(null,this.options.detailFilter,[e,t])?"true":void 0),hv.sprintf("%s",h),">"),this.options.cardView&&r.push('<td colspan="'.concat(this.header.fields.length,'"><div class="card-views">'));var P="";return hv.hasDetailViewIcon(this.options)&&(P="<td>",hv.calculateObjectValue(null,this.options.detailFilter,[e,t])&&(P+='\n          <a class="detail-icon" href="#">\n          '.concat(hv.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen),"\n          </a>\n        ")),P+="</td>"),P&&"right"!==this.options.detailViewAlign&&r.push(P),this.header.fields.forEach((function(i,n){var o=a.columns[n],l="",h=hv.getItemField(t,i,a.options.escape,o.escape),u="",d="",f={},p="",g=a.header.classes[n],v="",b="",m="",y="",w="",S="";if((!a.fromHtml&&!a.autoMergeCells||void 0!==h||o.checkbox||o.radio)&&o.visible&&(!a.options.cardView||o.cardVisible)){if(c.concat([a.header.styles[n]]).length&&(b+="".concat(c.concat([a.header.styles[n]]).join("; "))),t["_".concat(i,"_style")]&&(b+="".concat(t["_".concat(i,"_style")])),b&&(v=' style="'.concat(b,'"')),t["_".concat(i,"_id")]&&(p=hv.sprintf(' id="%s"',t["_".concat(i,"_id")])),t["_".concat(i,"_class")]&&(g=hv.sprintf(' class="%s"',t["_".concat(i,"_class")])),t["_".concat(i,"_rowspan")]&&(y=hv.sprintf(' rowspan="%s"',t["_".concat(i,"_rowspan")])),t["_".concat(i,"_colspan")]&&(w=hv.sprintf(' colspan="%s"',t["_".concat(i,"_colspan")])),t["_".concat(i,"_title")]&&(S=hv.sprintf(' title="%s"',t["_".concat(i,"_title")])),(f=hv.calculateObjectValue(a.header,a.header.cellStyles[n],[h,t,e,i],f)).classes&&(g=' class="'.concat(f.classes,'"')),f.css){for(var x=[],k=0,O=Object.entries(f.css);k<O.length;k++){var C=s(O[k],2),T=C[0],I=C[1];x.push("".concat(T,": ").concat(I))}v=' style="'.concat(x.concat(a.header.styles[n]).join("; "),'"')}if(u=hv.calculateObjectValue(o,a.header.formatters[n],[h,t,e,i],h),o.checkbox||o.radio||(u=null==u?a.options.undefinedText:u),o.searchable&&a.searchText&&a.options.searchHighlight&&!o.checkbox&&!o.radio){var P="",A=a.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");if(a.options.searchAccentNeutralise){var $=new RegExp("".concat(hv.normalizeAccent(A)),"gmi").exec(hv.normalizeAccent(u));$&&(A=u.substring($.index,$.index+A.length))}var R=new RegExp("(".concat(A,")"),"gim"),E="<mark>$1</mark>";if(u&&/<(?=.*? .*?\/ ?>|br|hr|input|!--|wbr)[a-z]+.*?>|<([a-z]+).*?<\/\1>/i.test(u)){var j=(new DOMParser).parseFromString(u.toString(),"text/html").documentElement.textContent,F=j.replace(R,E);j=j.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),P=u.replace(new RegExp("(>\\s*)(".concat(j,")(\\s*)"),"gm"),"$1".concat(F,"$3"))}else P=u.toString().replace(R,E);u=hv.calculateObjectValue(o,o.searchHighlightFormatter,[u,a.searchText],P)}if(t["_".concat(i,"_data")]&&!hv.isEmptyObject(t["_".concat(i,"_data")]))for(var _=0,D=Object.entries(t["_".concat(i,"_data")]);_<D.length;_++){var N=s(D[_],2),V=N[0],B=N[1];if("index"===V)return;m+=" data-".concat(V,'="').concat(B,'"')}if(o.checkbox||o.radio){d=o.checkbox?"checkbox":d,d=o.radio?"radio":d;var L=o.class||"",H=hv.isObject(u)&&u.hasOwnProperty("checked")?u.checked:(!0===u||h)&&!1!==u,M=!o.checkboxEnabled||u&&u.disabled;l=[a.options.cardView?'<div class="card-view '.concat(L,'">'):'<td class="bs-checkbox '.concat(L,'"').concat(g).concat(v,">"),'<label>\n            <input\n            data-index="'.concat(e,'"\n            name="').concat(a.options.selectItemName,'"\n            type="').concat(d,'"\n            ').concat(hv.sprintf('value="%s"',t[a.options.idField]),"\n            ").concat(hv.sprintf('checked="%s"',H?"checked":void 0),"\n            ").concat(hv.sprintf('disabled="%s"',M?"disabled":void 0)," />\n            <span></span>\n            </label>"),a.header.formatters[n]&&"string"==typeof u?u:"",a.options.cardView?"</div>":"</td>"].join(""),t[a.header.stateField]=!0===u||!!h||u&&u.checked}else if(a.options.cardView){var U=a.options.showHeader?'<span class="card-view-title '.concat(f.classes||"",'"').concat(v,">").concat(hv.getFieldTitle(a.columns,i),"</span>"):"";l='<div class="card-view">'.concat(U,'<span class="card-view-value ').concat(f.classes||"",'"').concat(v,">").concat(u,"</span></div>"),a.options.smartDisplay&&""===u&&(l='<div class="card-view"></div>')}else l="<td".concat(p).concat(g).concat(v).concat(m).concat(y).concat(w).concat(S,">").concat(u,"</td>");r.push(l)}})),P&&"right"===this.options.detailViewAlign&&r.push(P),this.options.cardView&&r.push("</div></td>"),r.push("</tr>"),r.join("")}}},{key:"initBody",value:function(t,e){var n=this,o=this.getData();this.trigger("pre-body",o),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=i.default("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=o.length);var a=[],r=i.default(document.createDocumentFragment()),s=!1,l=[];this.autoMergeCells=hv.checkAutoMergeCells(o.slice(this.pageFrom-1,this.pageTo));for(var c=this.pageFrom-1;c<this.pageTo;c++){var h=o[c],u=this.initRow(h,c,o,r);if(s=s||!!u,u&&"string"==typeof u){var d=this.options.uniqueId;if(d&&h.hasOwnProperty(d)){var f=h[d],p=this.$body.find(hv.sprintf('> tr[data-uniqueid="%s"][data-has-detail-view]',f)).next();p.is("tr.detail-view")&&(l.push(c),e&&f===e||(u+=p[0].outerHTML))}this.options.virtualScroll?a.push(u):r.append(u)}}s?this.options.virtualScroll?(this.virtualScroll&&this.virtualScroll.destroy(),this.virtualScroll=new bv({rows:a,fixedScroll:t,scrollEl:this.$tableBody[0],contentEl:this.$body[0],itemHeight:this.options.virtualScrollItemHeight,callback:function(t,e){n.fitHeader(),n.initBodyEvent(),n.trigger("virtual-scroll",t,e)}})):this.$body.html(r):this.$body.html('<tr class="no-records-found">'.concat(hv.sprintf('<td colspan="%s">%s</td>',this.getVisibleFields().length+hv.getDetailViewIndexOffset(this.options),this.options.formatNoMatches()),"</tr>")),l.forEach((function(t){n.expandRow(t)})),t||this.scrollTo(0),this.initBodyEvent(),this.initFooter(),this.resetView(),this.updateSelected(),"server"!==this.options.sidePagination&&(this.options.totalRows=o.length),this.trigger("post-body",o)}},{key:"initBodyEvent",value:function(){var t=this;this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",(function(e){var n=i.default(e.currentTarget);if(!(n.find(".detail-icon").length||n.index()-hv.getDetailViewIndexOffset(t.options)<0)){var o=n.parent(),a=i.default(e.target).parents(".card-views").children(),r=i.default(e.target).parents(".card-view"),s=o.data("index"),l=t.data[s],c=t.options.cardView?a.index(r):n[0].cellIndex,h=t.getVisibleFields()[c-hv.getDetailViewIndexOffset(t.options)],u=t.columns[t.fieldsColumnsIndex[h]],d=hv.getItemField(l,h,t.options.escape,u.escape);if(t.trigger("click"===e.type?"click-cell":"dbl-click-cell",h,d,l,n),t.trigger("click"===e.type?"click-row":"dbl-click-row",l,o,h),"click"===e.type&&t.options.clickToSelect&&u.clickToSelect&&!hv.calculateObjectValue(t.options,t.options.ignoreClickToSelectOn,[e.target])){var f=o.find(hv.sprintf('[name="%s"]',t.options.selectItemName));f.length&&f[0].click()}"click"===e.type&&t.options.detailViewByClick&&t.toggleDetailView(s,t.header.detailFormatters[t.fieldsColumnsIndex[h]])}})).off("mousedown").on("mousedown",(function(e){t.multipleSelectRowCtrlKey=e.ctrlKey||e.metaKey,t.multipleSelectRowShiftKey=e.shiftKey})),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",(function(e){return e.preventDefault(),t.toggleDetailView(i.default(e.currentTarget).parent().parent().data("index")),!1})),this.$selectItem=this.$body.find(hv.sprintf('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",(function(e){e.stopImmediatePropagation();var n=i.default(e.currentTarget);t._toggleCheck(n.prop("checked"),n.data("index"))})),this.header.events.forEach((function(e,n){var o=e;if(o){if("string"==typeof o&&(o=hv.calculateObjectValue(null,o)),!o)throw new Error("Unknown event in the scope: ".concat(e));var a=t.header.fields[n],r=t.getVisibleFields().indexOf(a);if(-1!==r){r+=hv.getDetailViewIndexOffset(t.options);var s=function(e){if(!o.hasOwnProperty(e))return"continue";var n=o[e];t.$body.find(">tr:not(.no-records-found)").each((function(o,s){var l=i.default(s),c=l.find(t.options.cardView?".card-views>.card-view":">td").eq(r),h=e.indexOf(" "),u=e.substring(0,h),d=e.substring(h+1);c.find(d).off(u).on(u,(function(e){var i=l.data("index"),o=t.data[i],r=o[a];n.apply(t,[e,r,o,i])}))}))};for(var l in o)s(l)}}}))}},{key:"initServer",value:function(t,e,n){var o=this,a={},r=this.header.fields.indexOf(this.options.sortName),s={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};if(this.header.sortNames[r]&&(s.sortName=this.header.sortNames[r]),this.options.pagination&&"server"===this.options.sidePagination&&(s.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,s.pageNumber=this.options.pageNumber),n||this.options.url||this.options.ajax){if("limit"===this.options.queryParamsType&&(s={search:s.searchText,sort:s.sortName,order:s.sortOrder},this.options.pagination&&"server"===this.options.sidePagination&&(s.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),s.limit=this.options.pageSize,0!==s.limit&&this.options.pageSize!==this.options.formatAllRows()||delete s.limit)),this.options.search&&"server"===this.options.sidePagination&&this.columns.filter((function(t){return!t.searchable})).length){s.searchable=[];var l,c=u(this.columns);try{for(c.s();!(l=c.n()).done;){var h=l.value;!h.checkbox&&h.searchable&&(this.options.visibleSearch&&h.visible||!this.options.visibleSearch)&&s.searchable.push(h.field)}}catch(t){c.e(t)}finally{c.f()}}if(hv.isEmptyObject(this.filterColumnsPartial)||(s.filter=JSON.stringify(this.filterColumnsPartial,null)),i.default.extend(s,e||{}),!1!==(a=hv.calculateObjectValue(this.options,this.options.queryParams,[s],a))){t||this.showLoading();var d=i.default.extend({},hv.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:n||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(a):a,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(e,i,n){var a=hv.calculateObjectValue(o.options,o.options.responseHandler,[e,n],e);o.load(a),o.trigger("load-success",a,n&&n.status,n),t||o.hideLoading(),"server"===o.options.sidePagination&&o.options.pageNumber>1&&a[o.options.totalField]>0&&!a[o.options.dataField].length&&o.updatePagination()},error:function(e){if(e&&0===e.status&&o._xhrAbort)o._xhrAbort=!1;else{var i=[];"server"===o.options.sidePagination&&((i={})[o.options.totalField]=0,i[o.options.dataField]=[]),o.load(i),o.trigger("load-error",e&&e.status,e),t||o.hideLoading()}}});return this.options.ajax?hv.calculateObjectValue(this,this.options.ajax,[d],null):(this._xhr&&4!==this._xhr.readyState&&(this._xhrAbort=!0,this._xhr.abort()),this._xhr=i.default.ajax(d)),a}}}},{key:"initSearchText",value:function(){if(this.options.search&&(this.searchText="",""!==this.options.searchText)){var t=hv.getSearchInput(this);t.val(this.options.searchText),this.onSearch({currentTarget:t,firedByInitSearchText:!0})}}},{key:"getCaret",value:function(){var t=this;this.$header.find("th").each((function(e,n){i.default(n).find(".sortable").removeClass("desc asc").addClass(i.default(n).data("field")===t.options.sortName?t.options.sortOrder:"both")}))}},{key:"updateSelected",value:function(){var t=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.each((function(t,e){i.default(e).closest("tr")[i.default(e).prop("checked")?"addClass":"removeClass"]("selected")}))}},{key:"updateRows",value:function(){var t=this;this.$selectItem.each((function(e,n){t.data[i.default(n).data("index")][t.header.stateField]=i.default(n).prop("checked")}))}},{key:"resetRows",value:function(){var t,e=u(this.data);try{for(e.s();!(t=e.n()).done;){var i=t.value;this.$selectAll.prop("checked",!1),this.$selectItem.prop("checked",!1),this.header.stateField&&(i[this.header.stateField]=!1)}}catch(t){e.e(t)}finally{e.f()}this.initHiddenRows()}},{key:"trigger",value:function(e){for(var n,o,a="".concat(e,".bs.table"),r=arguments.length,s=new Array(r>1?r-1:0),l=1;l<r;l++)s[l-1]=arguments[l];(n=this.options)[t.EVENTS[a]].apply(n,[].concat(s,[this])),this.$el.trigger(i.default.Event(a,{sender:this}),s),(o=this.options).onAll.apply(o,[a].concat([].concat(s,[this]))),this.$el.trigger(i.default.Event("all.bs.table",{sender:this}),[a,s])}},{key:"resetHeader",value:function(){var t=this;clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout((function(){return t.fitHeader()}),this.$el.is(":hidden")?100:0)}},{key:"fitHeader",value:function(){var t=this;if(this.$el.is(":hidden"))this.timeoutId_=setTimeout((function(){return t.fitHeader()}),100);else{var e=this.$tableBody.get(0),n=this.hasScrollBar&&e.scrollHeight>e.clientHeight+this.$header.outerHeight()?hv.getScrollBarWidth():0;this.$el.css("margin-top",-this.$header.outerHeight());var o=i.default(":focus");if(o.length>0){var a=o.parents("th");if(a.length>0){var r=a.attr("data-field");if(void 0!==r){var s=this.$header.find("[data-field='".concat(r,"']"));s.length>0&&s.find(":input").addClass("focus-temp")}}}this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),this.$tableLoading.css("width",this.$el.outerWidth());var l=i.default(".focus-temp:visible:eq(0)");l.length>0&&(l.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each((function(e,n){t.$header_.find(hv.sprintf('th[data-field="%s"]',i.default(n).data("field"))).data(i.default(n).data())}));for(var c=this.getVisibleFields(),h=this.$header_.find("th"),u=this.$body.find(">tr:not(.no-records-found,.virtual-scroll-top)").eq(0);u.length&&u.find('>td[colspan]:not([colspan="1"])').length;)u=u.next();var d=u.find("> *").length;u.find("> *").each((function(e,n){var o=i.default(n);if(hv.hasDetailViewIcon(t.options)&&(0===e&&"right"!==t.options.detailViewAlign||e===d-1&&"right"===t.options.detailViewAlign)){var a=h.filter(".detail"),r=a.innerWidth()-a.find(".fht-cell").width();a.find(".fht-cell").width(o.innerWidth()-r)}else{var s=e-hv.getDetailViewIndexOffset(t.options),l=t.$header_.find(hv.sprintf('th[data-field="%s"]',c[s]));l.length>1&&(l=i.default(h[o[0].cellIndex]));var u=l.innerWidth()-l.find(".fht-cell").width();l.find(".fht-cell").width(o.innerWidth()-u)}})),this.horizontalScroll(),this.trigger("post-header")}}},{key:"initFooter",value:function(){if(this.options.showFooter&&!this.options.cardView){var t=this.getData(),e=[],i="";hv.hasDetailViewIcon(this.options)&&(i='<th class="detail"><div class="th-inner"></div><div class="fht-cell"></div></th>'),i&&"right"!==this.options.detailViewAlign&&e.push(i);var n,o=u(this.columns);try{for(o.s();!(n=o.n()).done;){var a,r,l=n.value,c=[],h={},d=hv.sprintf(' class="%s"',l.class);if(!(!l.visible||this.footerData&&this.footerData.length>0&&!(l.field in this.footerData[0]))){if(this.options.cardView&&!l.cardVisible)return;if(a=hv.sprintf("text-align: %s; ",l.falign?l.falign:l.align),r=hv.sprintf("vertical-align: %s; ",l.valign),(h=hv.calculateObjectValue(null,this.options.footerStyle,[l]))&&h.css)for(var f=0,p=Object.entries(h.css);f<p.length;f++){var g=s(p[f],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}h&&h.classes&&(d=hv.sprintf(' class="%s"',l.class?[l.class,h.classes].join(" "):h.classes)),e.push("<th",d,hv.sprintf(' style="%s"',a+r+c.concat().join("; ")));var m=0;this.footerData&&this.footerData.length>0&&(m=this.footerData[0]["_".concat(l.field,"_colspan")]||0),m&&e.push(' colspan="'.concat(m,'" ')),e.push(">"),e.push('<div class="th-inner">');var y="";this.footerData&&this.footerData.length>0&&(y=this.footerData[0][l.field]||""),e.push(hv.calculateObjectValue(l,l.footerFormatter,[t,y],y)),e.push("</div>"),e.push('<div class="fht-cell"></div>'),e.push("</div>"),e.push("</th>")}}}catch(t){o.e(t)}finally{o.f()}i&&"right"===this.options.detailViewAlign&&e.push(i),this.options.height||this.$tableFooter.length||(this.$el.append("<tfoot><tr></tr></tfoot>"),this.$tableFooter=this.$el.find("tfoot")),this.$tableFooter.find("tr").length||this.$tableFooter.html("<table><thead><tr></tr></thead></table>"),this.$tableFooter.find("tr").html(e.join("")),this.trigger("post-footer",this.$tableFooter)}}},{key:"fitFooter",value:function(){var t=this;if(this.$el.is(":hidden"))setTimeout((function(){return t.fitFooter()}),100);else{var e=this.$tableBody.get(0),n=this.hasScrollBar&&e.scrollHeight>e.clientHeight+this.$header.outerHeight()?hv.getScrollBarWidth():0;this.$tableFooter.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).attr("class",this.$el.attr("class"));var o=this.$tableFooter.find("th"),a=this.$body.find(">tr:first-child:not(.no-records-found)");for(o.find(".fht-cell").width("auto");a.length&&a.find('>td[colspan]:not([colspan="1"])').length;)a=a.next();var r=a.find("> *").length;a.find("> *").each((function(e,n){var a=i.default(n);if(hv.hasDetailViewIcon(t.options)&&(0===e&&"left"===t.options.detailViewAlign||e===r-1&&"right"===t.options.detailViewAlign)){var s=o.filter(".detail"),l=s.innerWidth()-s.find(".fht-cell").width();s.find(".fht-cell").width(a.innerWidth()-l)}else{var c=o.eq(e),h=c.innerWidth()-c.find(".fht-cell").width();c.find(".fht-cell").width(a.innerWidth()-h)}})),this.horizontalScroll()}}},{key:"horizontalScroll",value:function(){var t=this;this.$tableBody.off("scroll").on("scroll",(function(){var e=t.$tableBody.scrollLeft();t.options.showHeader&&t.options.height&&t.$tableHeader.scrollLeft(e),t.options.showFooter&&!t.options.cardView&&t.$tableFooter.scrollLeft(e),t.trigger("scroll-body",t.$tableBody)}))}},{key:"getVisibleFields",value:function(){var t,e=[],i=u(this.header.fields);try{for(i.s();!(t=i.n()).done;){var n=t.value,o=this.columns[this.fieldsColumnsIndex[n]];o&&o.visible&&(!this.options.cardView||o.cardVisible)&&e.push(n)}}catch(t){i.e(t)}finally{i.f()}return e}},{key:"initHiddenRows",value:function(){this.hiddenRows=[]}},{key:"getOptions",value:function(){var t=i.default.extend({},this.options);return delete t.data,i.default.extend(!0,{},t)}},{key:"refreshOptions",value:function(t){hv.compareObjects(this.options,t,!0)||(this.options=i.default.extend(this.options,t),this.trigger("refresh-options",this.options),this.destroy(),this.init())}},{key:"getData",value:function(t){var e=this,i=this.options.data;if(!(this.searchText||this.options.customSearch||void 0!==this.options.sortName||this.enableCustomSort)&&hv.isEmptyObject(this.filterColumns)&&"function"!=typeof this.options.filterOptions.filterAlgorithm&&hv.isEmptyObject(this.filterColumnsPartial)||t&&t.unfiltered||(i=this.data),t&&t.useCurrentPage&&(i=i.slice(this.pageFrom-1,this.pageTo)),t&&!t.includeHiddenRows){var n=this.getHiddenRows();i=i.filter((function(t){return-1===hv.findIndex(n,t)}))}return t&&t.formatted&&i.forEach((function(t){for(var i=0,n=Object.entries(t);i<n.length;i++){var o=s(n[i],2),a=o[0],r=o[1],l=e.columns[e.fieldsColumnsIndex[a]];if(!l)return;t[a]=hv.calculateObjectValue(l,e.header.formatters[l.fieldIndex],[r,t,t.index,l.field],r)}})),i}},{key:"getSelections",value:function(){var t=this;return(this.options.maintainMetaData?this.options.data:this.data).filter((function(e){return!0===e[t.header.stateField]}))}},{key:"load",value:function(t){var e,i=t;this.options.pagination&&"server"===this.options.sidePagination&&(this.options.totalRows=i[this.options.totalField],this.options.totalNotFiltered=i[this.options.totalNotFilteredField],this.footerData=i[this.options.footerField]?[i[this.options.footerField]]:void 0),e=i.fixedScroll,i=Array.isArray(i)?i:i[this.options.dataField],this.initData(i),this.initSearch(),this.initPagination(),this.initBody(e)}},{key:"append",value:function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"prepend",value:function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"remove",value:function(t){for(var e=0,i=this.options.data.length-1;i>=0;i--){var n=this.options.data[i],o=hv.getItemField(n,t.field,this.options.escape,n.escape);void 0===o&&"$index"!==t.field||(!n.hasOwnProperty(t.field)&&"$index"===t.field&&t.values.includes(i)||t.values.includes(o))&&(e++,this.options.data.splice(i,1))}e&&("server"===this.options.sidePagination&&(this.options.totalRows-=e,this.data=l(this.options.data)),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"removeAll",value:function(){this.options.data.length>0&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"insertRow",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.options.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"updateRow",value:function(t){var e,n=u(Array.isArray(t)?t:[t]);try{for(n.s();!(e=n.n()).done;){var o=e.value;o.hasOwnProperty("index")&&o.hasOwnProperty("row")&&(o.hasOwnProperty("replace")&&o.replace?this.options.data[o.index]=o.row:i.default.extend(this.options.data[o.index],o.row))}}catch(t){n.e(t)}finally{n.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"getRowByUniqueId",value:function(t){var e,i,n=this.options.uniqueId,o=t,a=null;for(e=this.options.data.length-1;e>=0;e--){i=this.options.data[e];var r=hv.getItemField(i,n,this.options.escape,i.escape);if(void 0!==r&&("string"==typeof r?o=o.toString():"number"==typeof r&&(Number(r)===r&&r%1==0?o=parseInt(o,10):r===Number(r)&&0!==r&&(o=parseFloat(o))),r===o)){a=i;break}}return a}},{key:"updateByUniqueId",value:function(t){var e,n=null,o=u(Array.isArray(t)?t:[t]);try{for(o.s();!(e=o.n()).done;){var a=e.value;if(a.hasOwnProperty("id")&&a.hasOwnProperty("row")){var r=this.options.data.indexOf(this.getRowByUniqueId(a.id));-1!==r&&(a.hasOwnProperty("replace")&&a.replace?this.options.data[r]=a.row:i.default.extend(this.options.data[r],a.row),n=a.id)}}}catch(t){o.e(t)}finally{o.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0,n)}},{key:"removeByUniqueId",value:function(t){var e=this.options.data.length,i=this.getRowByUniqueId(t);i&&this.options.data.splice(this.options.data.indexOf(i),1),e!==this.options.data.length&&("server"===this.options.sidePagination&&(this.options.totalRows-=1,this.data=l(this.options.data)),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"_updateCellOnly",value:function(t,e){var n=this.initRow(this.options.data[e],e),o=this.getVisibleFields().indexOf(t);-1!==o&&(o+=hv.getDetailViewIndexOffset(this.options),this.$body.find(">tr[data-index=".concat(e,"]")).find(">td:eq(".concat(o,")")).replaceWith(i.default(n).find(">td:eq(".concat(o,")"))),this.initBodyEvent(),this.initFooter(),this.resetView(),this.updateSelected())}},{key:"updateCell",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.options.data[t.index][t.field]=t.value,!1!==t.reinit?(this.initSort(),this.initBody(!0)):this._updateCellOnly(t.field,t.index))}},{key:"updateCellByUniqueId",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){var i=t.id,n=t.field,o=t.value,a=e.options.data.indexOf(e.getRowByUniqueId(i));-1!==a&&(e.options.data[a][n]=o)})),!1!==t.reinit?(this.initSort(),this.initBody(!0)):this._updateCellOnly(t.field,this.options.data.indexOf(this.getRowByUniqueId(t.id)))}},{key:"showRow",value:function(t){this._toggleRow(t,!0)}},{key:"hideRow",value:function(t){this._toggleRow(t,!1)}},{key:"_toggleRow",value:function(t,e){var i;if(t.hasOwnProperty("index")?i=this.getData()[t.index]:t.hasOwnProperty("uniqueId")&&(i=this.getRowByUniqueId(t.uniqueId)),i){var n=hv.findIndex(this.hiddenRows,i);e||-1!==n?e&&n>-1&&this.hiddenRows.splice(n,1):this.hiddenRows.push(i),this.initBody(!0),this.initPagination()}}},{key:"getHiddenRows",value:function(t){if(t)return this.initHiddenRows(),this.initBody(!0),void this.initPagination();var e,i=[],n=u(this.getData());try{for(n.s();!(e=n.n()).done;){var o=e.value;this.hiddenRows.includes(o)&&i.push(o)}}catch(t){n.e(t)}finally{n.f()}return this.hiddenRows=i,i}},{key:"showColumn",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!0,!0)}))}},{key:"hideColumn",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!1,!0)}))}},{key:"_toggleColumn",value:function(t,e,i){if(-1!==t&&this.columns[t].visible!==e&&(this.columns[t].visible=e,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)){var n=this.$toolbar.find('.keep-open input:not(".toggle-all")').prop("disabled",!1);i&&n.filter(hv.sprintf('[value="%s"]',t)).prop("checked",e),n.filter(":checked").length<=this.options.minimumCountColumns&&n.filter(":checked").prop("disabled",!0)}}},{key:"getVisibleColumns",value:function(){var t=this;return this.columns.filter((function(e){return e.visible&&!t.isSelectionColumn(e)}))}},{key:"getHiddenColumns",value:function(){return this.columns.filter((function(t){return!t.visible}))}},{key:"isSelectionColumn",value:function(t){return t.radio||t.checkbox}},{key:"showAllColumns",value:function(){this._toggleAllColumns(!0)}},{key:"hideAllColumns",value:function(){this._toggleAllColumns(!1)}},{key:"_toggleAllColumns",value:function(t){var e,n=this,o=u(this.columns.slice().reverse());try{for(o.s();!(e=o.n()).done;){var a=e.value;if(a.switchable){if(!t&&this.options.showColumns&&this.getVisibleColumns().filter((function(t){return t.switchable})).length===this.options.minimumCountColumns)continue;a.visible=t}}}catch(t){o.e(t)}finally{o.f()}if(this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns){var r=this.$toolbar.find('.keep-open input[type="checkbox"]:not(".toggle-all")').prop("disabled",!1);t?r.prop("checked",t):r.get().reverse().forEach((function(e){r.filter(":checked").length>n.options.minimumCountColumns&&i.default(e).prop("checked",t)})),r.filter(":checked").length<=this.options.minimumCountColumns&&r.filter(":checked").prop("disabled",!0)}}},{key:"mergeCells",value:function(t){var e,i,n=t.index,o=this.getVisibleFields().indexOf(t.field),a=t.rowspan||1,r=t.colspan||1,s=this.$body.find(">tr[data-index]");o+=hv.getDetailViewIndexOffset(this.options);var l=s.eq(n).find(">td").eq(o);if(!(n<0||o<0||n>=this.data.length)){for(e=n;e<n+a;e++)for(i=o;i<o+r;i++)s.eq(e).find(">td").eq(i).hide();l.attr("rowspan",a).attr("colspan",r).show()}}},{key:"checkAll",value:function(){this._toggleCheckAll(!0)}},{key:"uncheckAll",value:function(){this._toggleCheckAll(!1)}},{key:"_toggleCheckAll",value:function(t){var e=this.getSelections();this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),this.updateSelected();var i=this.getSelections();t?this.trigger("check-all",i,e):this.trigger("uncheck-all",i,e)}},{key:"checkInvert",value:function(){var t=this.$selectItem.filter(":enabled"),e=t.filter(":checked");t.each((function(t,e){i.default(e).prop("checked",!i.default(e).prop("checked"))})),this.updateRows(),this.updateSelected(),this.trigger("uncheck-some",e),e=this.getSelections(),this.trigger("check-some",e)}},{key:"check",value:function(t){this._toggleCheck(!0,t)}},{key:"uncheck",value:function(t){this._toggleCheck(!1,t)}},{key:"_toggleCheck",value:function(t,e){var i=this.$selectItem.filter('[data-index="'.concat(e,'"]')),n=this.data[e];if(i.is(":radio")||this.options.singleSelect||this.options.multipleSelectRow&&!this.multipleSelectRowCtrlKey&&!this.multipleSelectRowShiftKey){var o,a=u(this.options.data);try{for(a.s();!(o=a.n()).done;){o.value[this.header.stateField]=!1}}catch(t){a.e(t)}finally{a.f()}this.$selectItem.filter(":checked").not(i).prop("checked",!1)}if(n[this.header.stateField]=t,this.options.multipleSelectRow){if(this.multipleSelectRowShiftKey&&this.multipleSelectRowLastSelectedIndex>=0)for(var r=s(this.multipleSelectRowLastSelectedIndex<e?[this.multipleSelectRowLastSelectedIndex,e]:[e,this.multipleSelectRowLastSelectedIndex],2),l=r[0],c=r[1],h=l+1;h<c;h++)this.data[h][this.header.stateField]=!0,this.$selectItem.filter('[data-index="'.concat(h,'"]')).prop("checked",!0);this.multipleSelectRowCtrlKey=!1,this.multipleSelectRowShiftKey=!1,this.multipleSelectRowLastSelectedIndex=t?e:-1}i.prop("checked",t),this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[e],i)}},{key:"checkBy",value:function(t){this._toggleCheckBy(!0,t)}},{key:"uncheckBy",value:function(t){this._toggleCheckBy(!1,t)}},{key:"_toggleCheckBy",value:function(t,e){var i=this;if(e.hasOwnProperty("field")&&e.hasOwnProperty("values")){var n=[];this.data.forEach((function(o,a){if(!o.hasOwnProperty(e.field))return!1;if(e.values.includes(o[e.field])){var r=i.$selectItem.filter(":enabled").filter(hv.sprintf('[data-index="%s"]',a)),s=!!e.hasOwnProperty("onlyCurrentPage")&&e.onlyCurrentPage;if(!(r=t?r.not(":checked"):r.filter(":checked")).length&&s)return;r.prop("checked",t),o[i.header.stateField]=t,n.push(o),i.trigger(t?"check":"uncheck",o,r)}})),this.updateSelected(),this.trigger(t?"check-some":"uncheck-some",n)}}},{key:"refresh",value:function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),this.trigger("refresh",this.initServer(t&&t.silent,t&&t.query,t&&t.url))}},{key:"destroy",value:function(){this.$el.insertBefore(this.$container),i.default(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"");var t=hv.getEventName("resize.bootstrap-table",this.$el.attr("id"));i.default(window).off(t)}},{key:"resetView",value:function(t){var e=0;if(t&&t.height&&(this.options.height=t.height),this.$tableContainer.toggleClass("has-card-view",this.options.cardView),this.options.height){var i=this.$tableBody.get(0);this.hasScrollBar=i.scrollWidth>i.clientWidth}if(!this.options.cardView&&this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),e+=this.$header.outerHeight(!0)+1):(this.$tableHeader.hide(),this.trigger("post-header")),!this.options.cardView&&this.options.showFooter&&(this.$tableFooter.show(),this.fitFooter(),this.options.height&&(e+=this.$tableFooter.outerHeight(!0))),this.$container.hasClass("fullscreen"))this.$tableContainer.css("height",""),this.$tableContainer.css("width","");else if(this.options.height){this.$tableBorder&&(this.$tableBorder.css("width",""),this.$tableBorder.css("height",""));var n=this.$toolbar.outerHeight(!0),o=this.$pagination.outerHeight(!0),a=this.options.height-n-o,r=this.$tableBody.find(">table"),s=r.outerHeight();if(this.$tableContainer.css("height","".concat(a,"px")),this.$tableBorder&&r.is(":visible")){var l=a-s-2;this.hasScrollBar&&(l-=hv.getScrollBarWidth()),this.$tableBorder.css("width","".concat(r.outerWidth(),"px")),this.$tableBorder.css("height","".concat(l,"px"))}}this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.getCaret(),this.$tableContainer.css("padding-bottom","".concat(e,"px"))),this.trigger("reset-view")}},{key:"showLoading",value:function(){this.$tableLoading.toggleClass("open",!0);var t=this.options.loadingFontSize;"auto"===this.options.loadingFontSize&&(t=.04*this.$tableLoading.width(),t=Math.max(12,t),t=Math.min(32,t),t="".concat(t,"px")),this.$tableLoading.find(".loading-text").css("font-size",t)}},{key:"hideLoading",value:function(){this.$tableLoading.toggleClass("open",!1)}},{key:"togglePagination",value:function(){this.options.pagination=!this.options.pagination;var t=this.options.showButtonIcons?this.options.pagination?this.options.icons.paginationSwitchDown:this.options.icons.paginationSwitchUp:"",e=this.options.showButtonText?this.options.pagination?this.options.formatPaginationSwitchUp():this.options.formatPaginationSwitchDown():"";this.$toolbar.find('button[name="paginationSwitch"]').html("".concat(hv.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)),this.updatePagination(),this.trigger("toggle-pagination",this.options.pagination)}},{key:"toggleFullscreen",value:function(){this.$el.closest(".bootstrap-table").toggleClass("fullscreen"),this.resetView()}},{key:"toggleView",value:function(){this.options.cardView=!this.options.cardView,this.initHeader();var t=this.options.showButtonIcons?this.options.cardView?this.options.icons.toggleOn:this.options.icons.toggleOff:"",e=this.options.showButtonText?this.options.cardView?this.options.formatToggleOff():this.options.formatToggleOn():"";this.$toolbar.find('button[name="toggle"]').html("".concat(hv.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)).attr("aria-label",e).attr("title",e),this.initBody(),this.trigger("toggle",this.options.cardView)}},{key:"resetSearch",value:function(t){var e=hv.getSearchInput(this),i=t||"";e.val(i),this.searchText=i,this.onSearch({currentTarget:e},!1)}},{key:"filterBy",value:function(t,e){this.filterOptions=hv.isEmptyObject(e)?this.options.filterOptions:i.default.extend(this.options.filterOptions,e),this.filterColumns=hv.isEmptyObject(t)?{}:t,this.options.pageNumber=1,this.initSearch(),this.updatePagination()}},{key:"scrollTo",value:function(t){var e={unit:"px",value:0};"object"===n(t)?e=Object.assign(e,t):"string"==typeof t&&"bottom"===t?e.value=this.$tableBody[0].scrollHeight:"string"!=typeof t&&"number"!=typeof t||(e.value=t);var o=e.value;"rows"===e.unit&&(o=0,this.$body.find("> tr:lt(".concat(e.value,")")).each((function(t,e){o+=i.default(e).outerHeight(!0)}))),this.$tableBody.scrollTop(o)}},{key:"getScrollPosition",value:function(){return this.$tableBody.scrollTop()}},{key:"selectPage",value:function(t){t>0&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())}},{key:"prevPage",value:function(){this.options.pageNumber>1&&(this.options.pageNumber--,this.updatePagination())}},{key:"nextPage",value:function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())}},{key:"toggleDetailView",value:function(t,e){this.$body.find(hv.sprintf('> tr[data-index="%s"]',t)).next().is("tr.detail-view")?this.collapseRow(t):this.expandRow(t,e),this.resetView()}},{key:"expandRow",value:function(t,e){var i=this.data[t],n=this.$body.find(hv.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));if(this.options.detailViewIcon&&n.find("a.detail-icon").html(hv.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailClose)),!n.next().is("tr.detail-view")){n.after(hv.sprintf('<tr class="detail-view"><td colspan="%s"></td></tr>',n.children("td").length));var o=n.next().find("td"),a=e||this.options.detailFormatter,r=hv.calculateObjectValue(this.options,a,[t,i,o],"");1===o.length&&o.append(r),this.trigger("expand-row",t,i,o)}}},{key:"expandRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.expandRow(this.data.indexOf(e))}},{key:"collapseRow",value:function(t){var e=this.data[t],i=this.$body.find(hv.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));i.next().is("tr.detail-view")&&(this.options.detailViewIcon&&i.find("a.detail-icon").html(hv.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen)),this.trigger("collapse-row",t,e,i.next()),i.next().remove())}},{key:"collapseRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.collapseRow(this.data.indexOf(e))}},{key:"expandAllRows",value:function(){for(var t=this.$body.find("> tr[data-index][data-has-detail-view]"),e=0;e<t.length;e++)this.expandRow(i.default(t[e]).data("index"))}},{key:"collapseAllRows",value:function(){for(var t=this.$body.find("> tr[data-index][data-has-detail-view]"),e=0;e<t.length;e++)this.collapseRow(i.default(t[e]).data("index"))}},{key:"updateColumnTitle",value:function(t){t.hasOwnProperty("field")&&t.hasOwnProperty("title")&&(this.columns[this.fieldsColumnsIndex[t.field]].title=this.options.escape?hv.escapeHTML(t.title):t.title,this.columns[this.fieldsColumnsIndex[t.field]].visible&&(this.$header.find("th[data-field]").each((function(e,n){if(i.default(n).data("field")===t.field)return i.default(i.default(n).find(".th-inner")[0]).text(t.title),!1})),this.resetView()))}},{key:"updateFormatText",value:function(t,e){/^format/.test(t)&&this.options[t]&&("string"==typeof e?this.options[t]=function(){return e}:"function"==typeof e&&(this.options[t]=e),this.initToolbar(),this.initPagination(),this.initBody())}}]),t}();return mv.VERSION=vv.VERSION,mv.DEFAULTS=vv.DEFAULTS,mv.LOCALES=vv.LOCALES,mv.COLUMN_DEFAULTS=vv.COLUMN_DEFAULTS,mv.METHODS=vv.METHODS,mv.EVENTS=vv.EVENTS,i.default.BootstrapTable=mv,i.default.fn.bootstrapTable=function(t){for(var e=arguments.length,o=new Array(e>1?e-1:0),a=1;a<e;a++)o[a-1]=arguments[a];var r;return this.each((function(e,a){var s=i.default(a).data("bootstrap.table"),l=i.default.extend(!0,{},mv.DEFAULTS,i.default(a).data(),"object"===n(t)&&t);if("string"==typeof t){var c;if(!vv.METHODS.includes(t))throw new Error("Unknown method: ".concat(t));if(!s)return;r=(c=s)[t].apply(c,o),"destroy"===t&&i.default(a).removeData("bootstrap.table")}s||(s=new i.default.BootstrapTable(a,l),i.default(a).data("bootstrap.table",s),s.init())})),void 0===r?this:r},i.default.fn.bootstrapTable.Constructor=mv,i.default.fn.bootstrapTable.theme=vv.THEME,i.default.fn.bootstrapTable.VERSION=vv.VERSION,i.default.fn.bootstrapTable.defaults=mv.DEFAULTS,i.default.fn.bootstrapTable.columnDefaults=mv.COLUMN_DEFAULTS,i.default.fn.bootstrapTable.events=mv.EVENTS,i.default.fn.bootstrapTable.locales=mv.LOCALES,i.default.fn.bootstrapTable.methods=mv.METHODS,i.default.fn.bootstrapTable.utils=hv,i.default((function(){i.default('[data-toggle="table"]').bootstrapTable()})),mv}));
