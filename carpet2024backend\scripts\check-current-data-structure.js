// Script to check current data structure and identify missing fields
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function main() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get sample records to see current structure
    const samples = await collection.find({ receiveNo: { $regex: /^K-/ } }).limit(5).toArray();
    
    console.log('\n📋 Current K- Records Structure:');
    console.log('='.repeat(80));
    
    samples.forEach((record, index) => {
      console.log(`\n${index + 1}. Record: ${record.receiveNo}`);
      console.log('   Fields present:');
      Object.keys(record).forEach(key => {
        if (key !== '_id' && key !== '__v') {
          console.log(`     ${key}: ${typeof record[key]} = ${JSON.stringify(record[key]).substring(0, 100)}`);
        }
      });
      console.log('   ' + '-'.repeat(70));
    });
    
    // Check what fields are missing that should be there
    const allFields = new Set();
    const allRecords = await collection.find({ receiveNo: { $regex: /^K-/ } }).toArray();
    
    allRecords.forEach(record => {
      Object.keys(record).forEach(key => {
        if (key !== '_id' && key !== '__v') {
          allFields.add(key);
        }
      });
    });
    
    console.log('\n📊 All Fields Found in K- Records:');
    console.log('='.repeat(50));
    Array.from(allFields).sort().forEach(field => {
      console.log(`   ✓ ${field}`);
    });
    
    // Check if we have the expected fields
    const expectedFields = [
      'K', 'receivingDate', 'issueNo', 'weaverNumber', 'receiveNo', 
      'area', 'amount', 'pcs', 'weaverName', 'quality', 'design', 
      'colour', 'colour2', 'size', 'carpetNo', 'createdAt', 'updatedAt'
    ];
    
    console.log('\n📋 Expected vs Present Fields:');
    console.log('='.repeat(50));
    expectedFields.forEach(field => {
      const present = allFields.has(field);
      console.log(`   ${present ? '✅' : '❌'} ${field}`);
    });
    
    // Check for any records that might have additional data
    const recordsWithExtraFields = await collection.find({
      receiveNo: { $regex: /^K-/ },
      $or: [
        { weaverName: { $exists: true } },
        { quality: { $exists: true } },
        { design: { $exists: true } },
        { colour: { $exists: true } },
        { colour2: { $exists: true } },
        { size: { $exists: true } },
        { carpetNo: { $exists: true } }
      ]
    }).limit(10).toArray();
    
    console.log(`\n📊 Records with additional fields: ${recordsWithExtraFields.length}`);
    
    if (recordsWithExtraFields.length > 0) {
      console.log('\n📋 Sample records with additional fields:');
      recordsWithExtraFields.slice(0, 3).forEach((record, index) => {
        console.log(`\n${index + 1}. ${record.receiveNo}:`);
        console.log(`   weaverName: ${record.weaverName || 'N/A'}`);
        console.log(`   quality: ${record.quality || 'N/A'}`);
        console.log(`   design: ${record.design || 'N/A'}`);
        console.log(`   colour: ${record.colour || 'N/A'}`);
        console.log(`   colour2: ${record.colour2 || 'N/A'}`);
        console.log(`   size: ${record.size || 'N/A'}`);
        console.log(`   carpetNo: ${record.carpetNo || 'N/A'}`);
      });
    }
    
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

main();
