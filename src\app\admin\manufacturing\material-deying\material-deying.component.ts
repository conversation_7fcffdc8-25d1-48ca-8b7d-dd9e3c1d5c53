import { Component, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

@Component({
  selector: 'app-material-deying',
  templateUrl: './material-deying.component.html',
  styleUrl: './material-deying.component.css'
})
export class MaterialDeyingComponent {
  Name = 'optoin1';
  Group = 'option2';
  Item = 'option3';
  WoolQuality = 'option1';
  Colours ='option2';

  
  displayedColumns: string[] = [
    'SrNo',
    'ChallanNo',
    'Date',
    'Name',
    'Group',
    'Item',
    'WoolQuality',
    'Count',
    'Colour',
    'PartyName',
    'LotNo',
    'Weight',
    'Rate',
    'Amount',
    'Action',
  ];
  dataSource = new MatTableDataSource<any>;
  totalSizeInYaard: any;
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }
}
