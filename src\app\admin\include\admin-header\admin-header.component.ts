import { DOCUMENT, Location } from '@angular/common';
import { Component, EventEmitter, Inject, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter, Subscription } from 'rxjs';

@Component({
  selector: 'app-admin-header',
  templateUrl: './admin-header.component.html',
  styleUrl: './admin-header.component.css',
})
export class AdminHeaderComponent implements OnInit {
  @Input() isOpen: boolean = true;
  @Output() toggle = new EventEmitter<any>();
  routerSubscription!: Subscription;
  toggleSideBar() {
    this.toggle.emit();
  }
  currentModule: any;
  currentForm!: string;
  isDarkTheme: boolean = false;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    // private location: Location,
    @Inject(DOCUMENT) private _document: Document
  ) {}
  refreshRoute: any;
  ngOnInit(): void {
    // Initialize theme from sessionStorage
    this.initializeTheme();

    // this.routerSubscription = this.router.events
    // .pipe(filter(event => event instanceof NavigationEnd))
    // .subscribe(() => {
    //   let currentRoute = this.activatedRoute.root;

    //   while (currentRoute.firstChild) {
    //     currentRoute = currentRoute.firstChild;
    //   }

    //   const routeData = currentRoute.snapshot.data;

    //   if (routeData && routeData['title'] === 'Admin') {
    //     this.currentModule = '';
    //   } else {
    //     this.currentModule = currentRoute.snapshot.routeConfig?.path || '';

    //   }
    // });
  }
  // ngOnDestroy() {
  //   if (this.routerSubscription) {
  //     this.routerSubscription.unsubscribe();
  //   }
  // }
  // goBack(): void {
  //   this.location.back();

  // }
  refresh(): void {
    window.location.reload();
  }

  initializeTheme(): void {
    // Get saved theme from sessionStorage
    const savedTheme = sessionStorage.getItem('themeMode');

    // Get system preference
    const systemPrefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    const defaultTheme = systemPrefersDark ? 'dark' : 'light';

    // Use saved theme or default to system preference
    const themeToApply = savedTheme || defaultTheme;

    // Apply the theme
    document.documentElement.setAttribute('data-bs-theme', themeToApply);
    sessionStorage.setItem('themeMode', themeToApply);

    // Update component state
    this.isDarkTheme = themeToApply === 'dark';

    console.log('Theme initialized to:', themeToApply);
  }

  toggleTheme(): void {
    const documentElement = document.documentElement;
    const currentTheme = documentElement.getAttribute('data-bs-theme');
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';

    // Set the new theme
    documentElement.setAttribute('data-bs-theme', newTheme);

    // Save to sessionStorage
    sessionStorage.setItem('themeMode', newTheme);

    // Update component state
    this.isDarkTheme = newTheme === 'dark';

    console.log('Theme toggled to:', newTheme);
  }
}
