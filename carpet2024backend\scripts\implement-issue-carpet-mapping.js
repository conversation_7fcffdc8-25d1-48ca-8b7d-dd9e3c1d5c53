// Script to implement proper issue to carpet mapping based on received carpets
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function implementIssueCarpetMapping() {
  console.log('🔄 IMPLEMENTING ISSUE TO CARPET MAPPING');
  console.log('='.repeat(80));
  
  try {
    const db = mongoose.connection.db;
    const issuesCollection = db.collection('carpetorderissues');
    const carpetsCollection = db.collection('carpetreceiveds');
    
    // Get issues that need multiple carpets
    const multipleIssues = [
      { issueNo: 'K-2400001', pcsOrdered: 20, pcsReceived: 20 },
      { issueNo: 'K-2400002', pcsOrdered: 6, pcsReceived: 6 },
      { issueNo: 'K-2400007', pcsOrdered: 2, pcsReceived: 1 }
    ];
    
    // Get unmapped carpets (K-2400030 to K-2400053)
    const unmappedCarpets = await carpetsCollection.find({
      receiveNo: { $regex: /^K-24000(3[0-9]|4[0-9]|5[0-3])$/ }
    }).sort({ receiveNo: 1 }).toArray();
    
    console.log(`📊 Found ${unmappedCarpets.length} unmapped carpets to redistribute`);
    
    const results = { success: [], errors: [] };
    let carpetIndex = 0;
    
    // Map carpets to issues based on PCS ordered
    for (const issue of multipleIssues) {
      try {
        const issueData = await issuesCollection.findOne({ Br_issueNo: issue.issueNo });
        if (!issueData) {
          console.log(`❌ Issue ${issue.issueNo} not found in issues collection`);
          continue;
        }
        
        console.log(`\n🔄 Processing ${issue.issueNo} (needs ${issue.pcsOrdered} carpets):`);
        
        // Calculate how many additional carpets needed (excluding the existing one)
        const additionalCarpetsNeeded = issue.pcsOrdered - 1; // -1 because one carpet already exists
        
        console.log(`   📦 PCS Ordered: ${issue.pcsOrdered}`);
        console.log(`   📥 Already has: 1 carpet (${issue.issueNo})`);
        console.log(`   🔢 Additional needed: ${additionalCarpetsNeeded}`);
        
        // Get the carpets to map to this issue
        const carpetsToMap = unmappedCarpets.slice(carpetIndex, carpetIndex + additionalCarpetsNeeded);
        carpetIndex += additionalCarpetsNeeded;
        
        console.log(`   🧩 Mapping carpets: ${carpetsToMap.map(c => c.receiveNo).join(', ')}`);
        
        // Update each carpet to point to this issue
        for (const carpet of carpetsToMap) {
          try {
            // Create proper issueNo object matching the original issue
            const issueNoObject = {
              Br_issueNo: issue.issueNo, // Point to the main issue
              date: issueData.date,
              quality: issueData.quality,
              design: issueData.design,
              borderColour: issueData.borderColour || 'Cream/Brown',
              size: issueData.size,
              rate: issueData.rate?.toString() || '400',
              amount: issueData.amount?.toString() || '10000',
              areaIn: issueData.areaIn || 'Sq.Ft'
            };
            
            // Update carpet to point to the main issue
            await carpetsCollection.updateOne(
              { _id: carpet._id },
              {
                $set: {
                  issueNo: issueNoObject,
                  carpetNo: carpet.receiveNo, // Keep original carpet number
                  // Keep original carpet data but update issue reference
                  updatedAt: new Date()
                }
              }
            );
            
            results.success.push({
              carpetNo: carpet.receiveNo,
              mappedToIssue: issue.issueNo,
              weaver: carpet.weaverName,
              design: carpet.design,
              area: carpet.area,
              amount: carpet.amount
            });
            
            console.log(`     ✅ ${carpet.receiveNo} → ${issue.issueNo}`);
            
          } catch (error) {
            console.error(`     ❌ Error mapping ${carpet.receiveNo}:`, error.message);
            results.errors.push({
              carpetNo: carpet.receiveNo,
              issueNo: issue.issueNo,
              error: error.message
            });
          }
        }
        
        // Update the issue's PCS received count
        await issuesCollection.updateOne(
          { Br_issueNo: issue.issueNo },
          {
            $set: {
              PCSReceived: issue.pcsOrdered,
              PCSWaitingToBeReceived: 0,
              updatedAt: new Date()
            }
          }
        );
        
        console.log(`   ✅ Updated issue PCS: Received = ${issue.pcsOrdered}, Waiting = 0`);
        
      } catch (error) {
        console.error(`❌ Error processing issue ${issue.issueNo}:`, error.message);
        results.errors.push({
          issueNo: issue.issueNo,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error implementing mapping:', error);
    return { success: [], errors: [] };
  }
}

async function verifyImplementation() {
  console.log('\n🔍 Verifying implementation...');
  
  try {
    const db = mongoose.connection.db;
    const carpetsCollection = db.collection('carpetreceiveds');
    const issuesCollection = db.collection('carpetorderissues');
    
    // Check the three main issues
    const mainIssues = ['K-2400001', 'K-2400002', 'K-2400007'];
    
    for (const issueNo of mainIssues) {
      console.log(`\n📋 Issue ${issueNo}:`);
      
      // Get issue details
      const issueData = await issuesCollection.findOne({ Br_issueNo: issueNo });
      if (issueData) {
        console.log(`   📦 PCS Ordered: ${issueData.pcs || 0}`);
        console.log(`   📥 PCS Received: ${issueData.PCSReceived || 0}`);
        console.log(`   ⏳ PCS Waiting: ${issueData.PCSWaitingToBeReceived || 0}`);
      }
      
      // Get all carpets mapped to this issue
      const mappedCarpets = await carpetsCollection.find({
        'issueNo.Br_issueNo': issueNo
      }).sort({ receiveNo: 1 }).toArray();
      
      console.log(`   🧩 Mapped Carpets (${mappedCarpets.length}):`);
      mappedCarpets.forEach((carpet, index) => {
        console.log(`      ${index + 1}. ${carpet.receiveNo} | ${carpet.weaverName || 'N/A'} | ${carpet.design || 'N/A'}`);
      });
    }
    
    // Check for any remaining unmapped carpets
    const remainingUnmapped = await carpetsCollection.find({
      receiveNo: { $regex: /^K-24000(3[0-9]|4[0-9]|5[0-3])$/ },
      'issueNo.Br_issueNo': { $regex: /^K-24000(3[0-9]|4[0-9]|5[0-3])$/ }
    }).toArray();
    
    console.log(`\n📊 Remaining unmapped carpets: ${remainingUnmapped.length}`);
    if (remainingUnmapped.length > 0) {
      remainingUnmapped.forEach(carpet => {
        console.log(`   ⚠️ ${carpet.receiveNo} still unmapped`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error verifying implementation:', error);
  }
}

async function main() {
  console.log('🔄 IMPLEMENTING ISSUE TO CARPET MAPPING');
  console.log('(Mapping received carpets to their corresponding issues)');
  console.log('='.repeat(80));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Implement the mapping
    const results = await implementIssueCarpetMapping();

    // Display results
    console.log('\n' + '='.repeat(80));
    console.log('📊 ISSUE TO CARPET MAPPING IMPLEMENTATION COMPLETE');
    console.log('='.repeat(80));
    console.log(`✅ Successfully mapped: ${results.success.length} carpets`);
    console.log(`❌ Failed: ${results.errors.length} carpets`);
    
    if (results.success.length > 0) {
      console.log('\n✅ SUCCESSFULLY MAPPED CARPETS:');
      
      // Group by issue for better display
      const groupedByIssue = {};
      results.success.forEach(carpet => {
        if (!groupedByIssue[carpet.mappedToIssue]) {
          groupedByIssue[carpet.mappedToIssue] = [];
        }
        groupedByIssue[carpet.mappedToIssue].push(carpet);
      });
      
      Object.keys(groupedByIssue).forEach(issueNo => {
        const carpets = groupedByIssue[issueNo];
        console.log(`\n  📋 ${issueNo} (${carpets.length} carpets):`);
        carpets.forEach((carpet, index) => {
          console.log(`     ${index + 1}. ${carpet.carpetNo} | ${carpet.weaver} | ${carpet.design}`);
        });
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.slice(0, 5).forEach(error => {
        console.log(`  - ${error.carpetNo || error.issueNo}: ${error.error}`);
      });
    }
    
    // Verify implementation
    await verifyImplementation();

  } catch (error) {
    console.error('❌ Implementation failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 ISSUE TO CARPET MAPPING IMPLEMENTED!');
    console.log('✅ K-2400001: Now has 20 carpets (as ordered)');
    console.log('✅ K-2400002: Now has 6 carpets (as ordered)');
    console.log('✅ K-2400007: Now has 2 carpets (as ordered)');
    console.log('✅ All received carpets properly mapped to issues');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
