const express = require('express');
const router = express.Router();
const colourcodeDeyingController = require('../../controller/colourDeying-controller');

// Create a new colourcodeDeying
router.post('/colourcodeDeying', colourcodeDeyingController.createColourcodeDeying);

// Get all colourcodeDeyings
router.get('/colourcodeDeying', colourcodeDeyingController.getAllColourcodeDeyings);

// Get a colourcodeDeying by ID
router.get('/colourcodeDeying/:id', colourcodeDeyingController.getColourcodeDeyingById);

// Update a colourcodeDeying by ID
router.put('/colourcodeDeying/:id', colourcodeDeyingController.updateColourcodeDeying);

// Delete a colourcodeDeying by ID
router.delete('/colourcodeDeying/:id', colourcodeDeyingController.deleteColourcodeDeying);

// Route to update only a specific field
router.patch('/colourcodeDeying/:id/field', colourcodeDeyingController.updateField);

// Route to delete only a specific field
router.patch('/colourcodeDeying/:id/delete-field', colourcodeDeyingController.deleteField);

module.exports = router;
