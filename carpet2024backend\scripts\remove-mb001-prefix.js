// Script to remove MB001 prefix and show only K-format weaver names
const mongoose = require('mongoose');

// Import models
const Branch = require('../src/model/phase-3/manageBranch');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function removeMB001Prefix() {
  console.log('🔄 REMOVING MB001 PREFIX FROM WEAVER DISPLAY');
  console.log('='.repeat(60));
  
  try {
    // Find the Main Branch and update its branchCode to empty or null
    const mainBranch = await Branch.findOne({ branchName: 'Main Branch' });
    
    if (mainBranch) {
      console.log(`📊 Found Main Branch with code: ${mainBranch.branchCode}`);
      
      // Update branch code to empty string so frontend won't show MB001 prefix
      await Branch.findByIdAndUpdate(mainBranch._id, {
        branchCode: '' // Remove the branch code
      });
      
      console.log('✅ Updated Main Branch branchCode to empty');
      
      // Verify the update
      const updatedBranch = await Branch.findById(mainBranch._id);
      console.log(`✅ Verified: New branchCode = "${updatedBranch.branchCode}"`);
      
      return { success: true, oldCode: mainBranch.branchCode, newCode: updatedBranch.branchCode };
    } else {
      console.log('❌ Main Branch not found');
      return { success: false, error: 'Main Branch not found' };
    }
    
  } catch (error) {
    console.error('❌ Error removing MB001 prefix:', error);
    return { success: false, error: error.message };
  }
}

async function verifyWeaverDisplay() {
  console.log('\n🔍 Verifying weaver display without MB001...');
  
  try {
    const db = mongoose.connection.db;
    
    // Get sample records with populated data
    const pipeline = [
      { $limit: 10 },
      {
        $lookup: {
          from: 'weaver_employees',
          localField: 'weaverNumber',
          foreignField: '_id',
          as: 'weaverData'
        }
      },
      {
        $lookup: {
          from: 'branches',
          localField: 'K',
          foreignField: '_id',
          as: 'branchData'
        }
      }
    ];
    
    const samples = await db.collection('carpetreceiveds').aggregate(pipeline).toArray();
    
    console.log('\n📋 Sample records with K-format only:');
    samples.forEach((record, index) => {
      const weaver = record.weaverData?.[0];
      const branch = record.branchData?.[0];
      
      // Simulate frontend display logic
      const weaverDisplay = branch?.branchCode ? 
        `${branch.branchCode} - ${weaver?.name || record.weaverName}` : 
        (weaver?.name || record.weaverName);
        
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver Display: ${weaverDisplay || 'N/A'}`);
      console.log(`   Branch Code: "${branch?.branchCode || 'empty'}"`);
      console.log(`   Weaver Name: ${weaver?.name || record.weaverName || 'N/A'}`);
      console.log('   ' + '-'.repeat(50));
    });
    
  } catch (error) {
    console.error('❌ Error verifying weaver display:', error);
  }
}

async function main() {
  console.log('🔄 REMOVING MB001 PREFIX FROM WEAVER DISPLAY');
  console.log('(Show only K-Rahila, K-Jeet Narayan, etc.)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Remove MB001 prefix
    const result = await removeMB001Prefix();

    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 MB001 PREFIX REMOVAL COMPLETE');
    console.log('='.repeat(60));
    
    if (result.success) {
      console.log(`✅ Successfully removed MB001 prefix`);
      console.log(`   Old Branch Code: "${result.oldCode}"`);
      console.log(`   New Branch Code: "${result.newCode}"`);
    } else {
      console.log(`❌ Failed to remove MB001 prefix: ${result.error}`);
    }
    
    // Verify weaver display
    await verifyWeaverDisplay();

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 MB001 PREFIX REMOVED!');
    console.log('✅ Frontend will now show only K-format names');
    console.log('✅ K-Rahila, K-Jeet Narayan, K-Yaseen, etc.');
    console.log('✅ No more MB001 prefix!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
