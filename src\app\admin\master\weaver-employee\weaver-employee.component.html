<!-- Start Content-->
<div style="width: 80%" class="mt-4">
  <section style="margin: 10px">
    <fieldset>
      <legend><b>
        {{isUpdated?'Update ':''}}Contractor / Staff / Finisher Ledger Account</b></legend>
      <form [formGroup]="frmWeaverEmployee" (ngSubmit)="addWeaver()">
        <div class="row">

          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>branch</mat-label>
              <mat-select formControlName="branch">
                <mat-option *ngFor="let val of branchList" [value]="val.id">
                  {{val.branchCode }} - {{val.branchName}}
                </mat-option>
              </mat-select>
            </mat-form-field>

          </div>

          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Group</mat-label>
              <mat-select formControlName="groupName">
                <mat-option *ngFor="let val of groupList" [value]="val">{{
                  val
                }}</mat-option>
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('groupName')?.invalid && (frmWeaverEmployee.get('groupName')?.dirty || frmWeaverEmployee.get('groupName')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('groupName')?.errors?.['required']">
               <span class="errText"> Please select a group type.</span>
              </div>

            </div>
          </div>

          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Name</mat-label>
              <input matInput placeholder="Name" formControlName="name" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('name')?.invalid && (frmWeaverEmployee.get('name')?.dirty || frmWeaverEmployee.get('name')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('name')?.errors?.['required']">
               <span class="errText"> Name is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('name')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Address</mat-label>
              <textarea
                matInput
                placeholder="Ex. 100 Main St"
                formControlName="address"
              >
1600 Amphitheatre Pkwy</textarea
              >
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('address')?.invalid && (frmWeaverEmployee.get('address')?.dirty || frmWeaverEmployee.get('address')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('address')?.errors?.['required']">
               <span class="errText"> Address is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('address')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Zipcode</mat-label>
              <input matInput placeholder="Zipcode" formControlName="zipCode" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('zipCode')?.invalid && (frmWeaverEmployee.get('zipCode')?.dirty || frmWeaverEmployee.get('zipCode')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('zipCode')?.errors?.['required']">
               <span class="errText"> Zip code is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('zipCode')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Country</mat-label>
              <input matInput placeholder="Country" formControlName="country" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('country')?.invalid && (frmWeaverEmployee.get('country')?.dirty || frmWeaverEmployee.get('country')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('country')?.errors?.['required']">
               <span class="errText"> Select country.</span>
              </div>

            </div>
          </div>
          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Phone No.</mat-label>
              <input matInput placeholder="Phone No." formControlName="contactNo" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('contactNo')?.invalid && (frmWeaverEmployee.get('contactNo')?.dirty || frmWeaverEmployee.get('contactNo')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('contactNo')?.errors?.['required']">
               <span class="text-danger"> Contact number must be 10 digits long.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('contactNo')?.errors?.['pattern']">

                <span class="text-danger"> Invalid contact number </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Email</mat-label>
              <input
                type="email"
                matInput
                placeholder="Ex. <EMAIL>"
                formControlName="emailId"
              />
            </mat-form-field>

            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('emailId')?.invalid && (frmWeaverEmployee.get('emailId')?.dirty || frmWeaverEmployee.get('emailId')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('emailId')?.errors?.['required']">
               <span class="text-danger"> Email is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('emailId')?.errors?.['pattern']">

                <span class="text-danger"> Invalid email format  </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Enter your password</mat-label>
              <input
                matInput
                [type]="hide ? 'password' : 'text'"
                formControlName="password"
              />
              <button
                mat-icon-button
                matSuffix
                (click)="hide = !hide"
                [attr.aria-label]="'Hide password'"
                [attr.aria-pressed]="hide"
              >
                <mat-icon>{{
                  hide ? "visibility_off" : "visibility"
                }}</mat-icon>
              </button>
            </mat-form-field>

            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('password')?.invalid && (frmWeaverEmployee.get('password')?.dirty || frmWeaverEmployee.get('password')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('password')?.errors?.['required']">
               <span class="errText"> Password is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('password')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>TDS (%)</mat-label>
              <input
                matInput
                placeholder="Tax Deducted at Source"
                formControlName="tds"
                (focus)="onPercentageFieldFocus('tds')"
                (blur)="onPercentageFieldBlur('tds')"
              />
            </mat-form-field>

            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('tds')?.invalid && (frmWeaverEmployee.get('tds')?.dirty || frmWeaverEmployee.get('tds')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('tds')?.errors?.['required']">
               <span class="errText">TDS is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('tds')?.errors?.['min'] || frmWeaverEmployee.get('tds')?.errors?.['max']">
                <span class="errText">TDS must be between 0 and 100%</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('tds')?.errors?.['pattern']">
                <span class="errText">Please enter a valid number</span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Commission (%)</mat-label>
              <input
                matInput
                placeholder="Commission"
                formControlName="commission"
                (focus)="onPercentageFieldFocus('commission')"
                (blur)="onPercentageFieldBlur('commission')"
              />
            </mat-form-field>

            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('commission')?.invalid && (frmWeaverEmployee.get('commission')?.dirty || frmWeaverEmployee.get('commission')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('commission')?.errors?.['required']">
               <span class="errText">Commission is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('commission')?.errors?.['min'] || frmWeaverEmployee.get('commission')?.errors?.['max']">
                <span class="errText">Commission must be between 0 and 100%</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('commission')?.errors?.['pattern']">
                <span class="errText">Please enter a valid number</span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-sm-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Bank Name</mat-label>
              <input

                matInput
                placeholder="bankName"
                formControlName="bankName"
              />
            </mat-form-field>

            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('bankName')?.invalid && (frmWeaverEmployee.get('bankName')?.dirty || frmWeaverEmployee.get('bankName')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('bankName')?.errors?.['required']">
               <span class="errText"> Bank name is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('bankName')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>

          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Bank Account No</mat-label>
              <input

                matInput
                placeholder="bankAccountNo"
                formControlName="bankAccountNo"
              />
            </mat-form-field>

            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('bankAccountNo')?.invalid && (frmWeaverEmployee.get('bankAccountNo')?.dirty || frmWeaverEmployee.get('bankAccountNo')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('bankAccountNo')?.errors?.['required']">
               <span class="errText"> Bank account No is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('bankAccountNo')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>

          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Ifsc Code</mat-label>
              <input

                matInput
                placeholder="ifscCode"
                formControlName="ifscCode"
              />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('ifscCode')?.invalid && (frmWeaverEmployee.get('ifscCode')?.dirty || frmWeaverEmployee.get('ifscCode')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('ifscCode')?.errors?.['required']">
               <span class="errText"> Ifsc Code No is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('ifscCode')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>

          <div class="mb-2 col-sm-6">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Aadhar No.</mat-label>
              <input
                matInput
                placeholder="Aadhar No."
                formControlName="aadhaarNo"
              />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmWeaverEmployee.get('aadhaarNo')?.invalid && (frmWeaverEmployee.get('aadhaarNo')?.dirty || frmWeaverEmployee.get('aadhaarNo')?.touched)">
              <div *ngIf="frmWeaverEmployee.get('aadhaarNo')?.errors?.['required']">
               <span class="errText"> Aadhar No is required.</span>
              </div>
              <div *ngIf="frmWeaverEmployee.get('aadhaarNo')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-sm-6">
            <label for="file" class="form-label"
              >Upload Aadhar Card( .Pdf )</label
            >
            <input
              type="file"
              id="aadharFile"
              class="form-control"
              (change)="handleFileInput($event)"
            />
          </div>
          <div class="mb-2 col-sm-6">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Pan Card No.</mat-label>
              <input
                matInput
                placeholder="Pan Card No."
                formControlName="panNo"
              />
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-6">
            <label for="file" class="form-label">Upload Pan Card( .Pdf )</label>
            <input
              type="file"
              id="panFile"
              class="form-control"
              (change)="handleFileInput($event)"
            />
          </div>
          <div class="mb-2 col-sm-6">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>E.P.F.O No.</mat-label>
              <input
                matInput
                placeholder="E.P.F.O No."
                formControlName="epfoNo"
              />
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-6">
            <label for="file" class="form-label"
              >Upload E.P.F.O Sheet( .Pdf )</label
            >
            <input
              type="file"
              id="epfoFile"
              class="form-control"
              (change)="handleFileInput($event)"
            />
          </div>
          <div class="mb-2 col-sm-6">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>E.S.I.C No.</mat-label>
              <input
                matInput
                placeholder="E.S.I.C No."
                formControlName="esicNo"
              />
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-6">
            <label for="file" class="form-label"
              >Upload E.S.I.C Sheet( .Pdf )</label
            >
            <input
              type="file"
              id="esicFile"
              class="form-control"
              (change)="handleFileInput($event)"
            />
          </div>

          <div class="mb-3">
            <button *ngIf="!isUpdated; else updateTemplate" mat-flat-button color="primary" [disabled]="!frmWeaverEmployee.valid">Add</button>
            <ng-template #updateTemplate>
              <button mat-flat-button color="primary" (click)="updateWeaver()" [disabled]="!frmWeaverEmployee.valid" type="button">Update</button>
            </ng-template>
          </div>

        </div>
      </form>
    </fieldset>
  </section>
</div>

<div style="width: 80%" class="conatiner mt-4">
  <section>
    <fieldset>
      <legend><b>List</b></legend>
      <div class="row">
        <div class="col-sm-12" >
          <mat-form-field appearance="outline">
            <mat-label>Search</mat-label>
            <input
              matInput
              (keyup)="applyFilter($event)"
              placeholder="Ex. Jack"
              #input
            />
          </mat-form-field>

          <div class="mat-elevation-z8" class="Scrollbar">
            <table mat-table [dataSource]="dataSource" matSort style="width: max-content;">
              <!-- ID Column -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>No.</th>
                <td mat-cell *matCellDef="let row">{{ row.index }}</td>
              </ng-container>


              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                <td mat-cell *matCellDef="let row">

                  <a (click)="edit(row)"
            ><i
              class="fa fa-pencil-square-o fa-edit"
              title="Edit Challan "
              aria-hidden="true"
            ></i
          ></a>
          &nbsp;
          <a (click)="delete(row.id)"><i class="fa fa-trash-o" aria-hidden="true"></i></a>
                </td>
              </ng-container>

             <!-- Branch Name -->

              <ng-container matColumnDef="branch">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Branch
                </th>
                <td mat-cell *matCellDef="let row">{{row.branch?.branchCode
                }}- {{row.branch?.branchName
                }} </td>
              </ng-container>
              <!-- Group Name Column -->
              <ng-container matColumnDef="groupName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Group Name
                </th>
                <td mat-cell *matCellDef="let row">{{ row.groupName }}</td>
              </ng-container>

              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                <td mat-cell *matCellDef="let row">{{ row.name }}</td>
              </ng-container>

              <!-- Address Column -->
              <ng-container matColumnDef="address">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Address
                </th>
                <td mat-cell *matCellDef="let row">{{ row.address }}</td>
              </ng-container>
              <!-- Zipcode Column -->

              <ng-container matColumnDef="zipCode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Zipcode
                </th>
                <td mat-cell *matCellDef="let row">{{ row.zipCode }}</td>
              </ng-container>
              <!-- Country Column -->
              <ng-container matColumnDef="country">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Country
                </th>
                <td mat-cell *matCellDef="let row">{{ row.country }}</td>
              </ng-container>

<!-- Country Column -->
<ng-container matColumnDef="tds">
  <th mat-header-cell *matHeaderCellDef mat-sort-header>
    TDS (%)
  </th>
  <td mat-cell *matCellDef="let row">{{ row.tds }}</td>
</ng-container>
<!-- Country Column -->
<ng-container matColumnDef="commission">
  <th mat-header-cell *matHeaderCellDef mat-sort-header>
    Commission (%)
  </th>
  <td mat-cell *matCellDef="let row">{{ row.commission }}</td>
</ng-container>


              <ng-container matColumnDef="bankName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Bank Name
                </th>
                <td mat-cell *matCellDef="let row">{{ row.bankName }}</td>
              </ng-container>
              <ng-container matColumnDef="ifscCode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Ifsc Code
                </th>
                <td mat-cell *matCellDef="let row">{{ row.ifscCode }}</td>
              </ng-container>
              <ng-container matColumnDef="bankAccountNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Bank Account No
                </th>
                <td mat-cell *matCellDef="let row">{{ row.bankAccountNo }}</td>
              </ng-container>




              <!-- Phone No. Column -->
              <ng-container matColumnDef="contactNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Phone No.
                </th>
                <td mat-cell *matCellDef="let row">{{ row.contactNo }}</td>
              </ng-container>
              <!--Aadhar No. Column -->
              <ng-container matColumnDef="aadharNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Aadhar No.
                </th>
                <td mat-cell *matCellDef="let row">{{ row.aadhaarNo }}</td>
              </ng-container>
              <!-- Aadhar Image Column -->
              <ng-container matColumnDef="aadhaarDetails">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Aadhar Image
                </th>
                <td mat-cell *matCellDef="let row">
                  <ng-container *ngIf="row.aadhaarDetails">
                   <a (click)="viewPdf(row.aadhaarDetails)"> <img  width="26px"
                    src="../../../../assets/images/pdf-icon-64.png"
                    alt="Aadhar Image"
                  /></a>
                  </ng-container>
                </td>
              </ng-container>
              <!-- Pan No. Column -->
              <ng-container matColumnDef="panNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Pan No.
                </th>
                <td mat-cell *matCellDef="let row">{{ row.panNo }}</td>
              </ng-container>
              <!-- Pan Image Column -->
              <ng-container matColumnDef="panDetails">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Pan Image
                </th>
                <td mat-cell *matCellDef="let row">
                  <ng-container *ngIf="row.panDetails">
                    <a (click)="viewPdf(row.panDetails)"><img  width="26px"
                      src="../../../../assets/images/pdf-icon-64.png"
                      alt="pan Image"
                    /></a>
                  </ng-container>
                </td>
              </ng-container>
              <!-- Email Column -->
              <ng-container matColumnDef="emailId">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
                <td mat-cell *matCellDef="let row" class="no-capitalize">{{ row.emailId }}</td>
              </ng-container>
              <!-- E.S.I.C. No. Column -->
              <ng-container matColumnDef="esicNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  E.S.I.C. No.
                </th>
                <td mat-cell *matCellDef="let row">{{ row.esicNo }}</td>
              </ng-container>
              <!--E.P.F.O. No  Column -->
              <ng-container matColumnDef="epfoNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  E.P.F.O. No
                </th>
                <td mat-cell *matCellDef="let row">{{ row.epfoNo }}</td>
              </ng-container>
              <!-- ISIC sheet Column -->
              <ng-container matColumnDef="esicDetails">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  I.S.I.C. Sheet
                </th>
                <td mat-cell *matCellDef="let row">

                  <ng-container *ngIf="row.esicDetails">
                    <a (click)="viewPdf(row.esicDetails)"><img  width="26px"
                    src="../../../../assets/images/pdf-icon-64.png"
                    alt="esic Image"
                  />
                    </a>
                  </ng-container>

                </td>
              </ng-container>
              <!-- EPFO Sheet Column -->
              <ng-container matColumnDef="epfoDetails">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  E.P.F.O. Sheet
                </th>
                <td mat-cell *matCellDef="let row">

                  <ng-container *ngIf="row.epfoDetails">
                    <a (click)="viewPdf(row.epfoDetails)"><img width="26px"
                      src="../../../../assets/images/pdf-icon-64.png"
                      alt="epfo Image"
                    /></a>
                  </ng-container>
                </td>
              </ng-container>
              <!-- Action Column -->
              <ng-container matColumnDef="Action">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Action
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Action }}</td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>

              <!-- Row shown when there is no matching data. -->
              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="4">
                  No data matching the filter "{{ input.value }}"
                </td>
              </tr>
            </table>


          </div>
          <mat-paginator
          [pageSizeOptions]="[5, 10, 15, 25, 50, 100]"
          aria-label="Select page of users"
        ></mat-paginator>
        </div>
      </div>
    </fieldset>
  </section>
</div>
