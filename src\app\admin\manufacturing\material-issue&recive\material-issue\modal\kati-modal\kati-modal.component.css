/* Ensure table cells have proper height for complete form fields */
.mat-mdc-cell {
  padding: 2px 8px !important;
  height: 40px !important; /* Balanced height for complete form field */
  vertical-align: middle !important;
  line-height: 1.2 !important;
  margin-bottom: 0 !important;
}

/* Adjust form field height and padding */
.mat-mdc-cell .mat-mdc-form-field {
  height: 36px !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* .mat-mdc-cell .mat-mdc-text-field-wrapper {
  height: 30px !important;
  padding: 0 !important;
 
} */

.mat-mdc-cell .mat-mdc-form-field-infix {
  padding: 4px 0 !important; /* Balanced padding */
  min-height: 28px !important; /* Adjusted min-height */
  border-top: none !important;
}

.mat-mdc-cell input {
  font-size: 14px !important;
  padding: 4px 8px !important; /* Compact padding */
  height: 28px !important; /* Compact height */
  line-height: 1.2 !important;
  margin: 0 !important;
}



/* Remove ALL bottom spacing */
/* .mat-mdc-cell .mat-mdc-form-field-wrapper {
  padding-bottom: 0 !important;
  margin-bottom: 0px !important;
} */

/* Force form field to be contained */
.mat-mdc-cell .mat-mdc-form-field {
  position: relative !important;
  max-height: 36px !important;
}



/* Ensure the form field outline is fully visible */
.mat-mdc-cell .mat-mdc-form-field-outline {
  height: 100% !important;
}

/* Ensure row height accommodates the form fields */
.mat-mdc-row {
  height: 40px !important; /* Match cell height */
  min-height: 40px !important;
  max-height: 40px !important;
  border-bottom: 1px solid #e0e0e0;
}

/* Remove ex-width class styling if no longer needed */
.ex-width {
  width: auto; /* Reset width */
}

/* Optional: Adjust scroll container if needed */
.scroll-container {
  scroll-behavior: smooth !important;
  overflow-x: scroll !important;
  overflow-y: scroll !important;
  max-height: none !important;
}

/* Custom Input Styles */
.custom-input {
  width: 100%;
  height: 36px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  background-color: white;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

.custom-input:focus {
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.custom-input:hover {
  border-color: #999;
}

.custom-input::placeholder {
  color: #999;
  font-style: italic;
}

.custom-input:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}