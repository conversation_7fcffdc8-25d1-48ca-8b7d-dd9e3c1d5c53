const Color = require('../model/phase-1/color');

class ColorRepository {
  async createColor(colorData) {
    try {
      const color = new Color(colorData);
      await color.save();
      return color;
    } catch (error) {
      throw error;
    }
  }

  async getColorById(colorId) {
    try {
      const color = await Color.findById(colorId);
      return color;
    } catch (error) {
      throw error;
    }
  }

  async getAllColors() {
    try {
      const colors = await Color.find().populate('quality','quality').exec();
      return colors;
    } catch (error) {
      throw error;
    }
  }

  async updateColor(colorId, colorData) {
    try {
      const color = await Color.findByIdAndUpdate(colorId, colorData, { new: true });
    } catch (error) {
      throw error;
    }
  }

  async deleteColor(colorId) {
    try {
      const color = await Color.findByIdAndDelete(colorId);
      return color;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new ColorRepository();
