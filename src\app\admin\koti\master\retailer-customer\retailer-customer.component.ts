import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MyErrorStateMatcher } from '../importer-detail/importer-detail.component';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { ImporterService } from '../../../../services/importer.service';
import Swal from 'sweetalert2';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

export interface UserData {
  index: number;
  id: string;
  bankName: string;
  accountNumber: string;
  bankSwiftCode: string;
  bankAddress: string;
  bankEmail: string;
  bankContactNumber: string;
  retailerCode: string;
  retailerStoreName: string;
  contactPerson: string;
  contactPersonNumber: string;
  address: string;
  zipcode: string;
  country: string;
  contactNumber: string;
  email: string;
  password: string;


}



const ELEMENT_DATA: UserData[] = [];





@Component({
  selector: 'app-retailer-customer',
  templateUrl: './retailer-customer.component.html',
  styleUrl: './retailer-customer.component.css'
})
export class RetailerCustomerComponent implements OnInit {
  Country = 'option'
  NoofStores = 'option1'

  emailFormControl = new FormControl('', [Validators.required, Validators.email]);
  isOpenDialog:boolean=false;
  matcher = new MyErrorStateMatcher();

  displayedColumns: string[] = ['action','id', 'retailerCode', 'retailerStoreName','contactPerson', 'address', 'zipcode', 'country',

    'email', 'bankName', 'accountNumber', 'bankSwiftCode', 'bankAddress', 'bankEmail', 'bankContactNumber',
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);
  countryList: string[] = ['Germnany', 'India'];
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  constructor(private _fb: FormBuilder, private _service: ImporterService,  @Optional() public dialogRef: MatDialogRef<RetailerCustomerComponent>,
  @Optional() @Inject(MAT_DIALOG_DATA) public data: any) { }

  frmRetailer !: FormGroup;

  ngOnInit(): void {

    this.frmRetailer = this._fb.group({
      retailerCode: [],
      retailerStoreName: [],
      contactPerson: [],
      contactPersonNumber: [],
      address: [],
      zipcode: [],
      country: [],
      contactNumber: [],
      email: [],
      password: [],
      bankName: [],
      accountNumber: [],
      bankSwiftCode: [],
      bankAddress: [],
      bankEmail: [],
      bankContactNumber: [],
    })
    this.getsRetailerList();
    this.dialog()
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();


  }

  addRetailer() {
    console.log(this.frmRetailer.value);
    let formData = this.frmRetailer.value;
    let data = {
      retailerCode: formData.retailerCode,
      retailerStoreName: formData.retailerStoreName,
      contactPerson: formData.contactPerson,
      contactPersonNumber: formData.contactPersonNumber,
      address: formData.address,
      zipcode: formData.zipcode,
      country: formData.country,
      contactNumber: formData.contactNumber,
      email: formData.email,
      password: formData.password,
      bankDetails: {
        bankName: formData.bankName,
        accountNumber: formData.accountNumber,
        bankSwiftCode: formData.bankSwiftCode,
        bankAddress: formData.bankAddress,
        bankEmail: formData.bankEmail,
        bankContactNumber: formData.bankContactNumber,
      }
    }

    this._service.addRetailer(data).subscribe((resp: any) => {
      Swal.fire(
        'Success!',
        'Retailer details has been saved successfully',
        'success'
      )
      this.getsRetailerList();
    }, (error) => {
      Swal.fire(
        'warning!',
        'Failed',
        'warning'
      )

    })
  }
  getsRetailerList() {
    this._service.getRetailerList().subscribe((resp: any) => {
      console.log('retailer list ', resp);

      if (resp) {
        ELEMENT_DATA.length = 0;
        resp.map((x: any, i: number) => {

          ELEMENT_DATA.push({
            bankName: x.bankDetails.bankName,
            accountNumber: x.bankDetails.accountNumber,
            bankSwiftCode: x.bankDetails.bankSwiftCode,
            bankAddress: x.bankDetails.bankAddress,
            bankEmail: x.bankDetails.bankEmail,
            bankContactNumber: x.bankDetails.bankContactNumber,
            id: x._id,
            index: i + 1,
            retailerCode: x.retailerCode,
            retailerStoreName: x.retailerStoreName,
            contactPerson: x.contactPerson,
            contactPersonNumber: x.contactPersonNumber,
            address: x.address,
            zipcode: x.zipcode,
            country: x.country,
            contactNumber: x.contactNumber,
            email: x.email,
            password: x.password,
          })
        })
        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.ngAfterViewInit();


        return;
      }
    })
  }
  dialog() {
    debugger;
    if (this.dialogRef) {
      debugger;
      this.isOpenDialog = true;
console.log(this.dialogRef);

    }
  }
}
