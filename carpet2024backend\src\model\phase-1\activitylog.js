const mongoose = require('mongoose');

const activityLogSchema = new mongoose.Schema({
  timestamp: { type: Date, default: Date.now },
  message: { type: String, required: true },
  oldData: { type: mongoose.Schema.Types.Mixed, default: null },
  newData: { type: mongoose.Schema.Types.Mixed, default: null }
});

const ActivityLog = mongoose.model('ActivityLog', activityLogSchema);

async function createActivityLog({ message, oldData, newData }) {
  try {
    const log = new ActivityLog({ message, oldData, newData });
    const savedLog = await log.save();
    return savedLog;
  } catch (error) {
    console.error('Error creating activity log:', error);
    throw error;
  }
}

module.exports = { ActivityLog, createActivityLog };
