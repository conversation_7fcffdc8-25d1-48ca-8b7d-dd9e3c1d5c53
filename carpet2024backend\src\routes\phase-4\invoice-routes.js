// routes/invoiceRoutes.js
const express = require('express');
const router = express.Router();
const invoiceController = require('../../controller/manifacturing/invoice-controller');

// Define routes for invoices
router.post('/', invoiceController.createInvoice);
router.get('/getAll/all', invoiceController.getAllInvoices);
router.get('/get/:id', invoiceController.getInvoiceById);
router.put('/update/:id', invoiceController.updateInvoice);
router.delete('/delete/:id', invoiceController.deleteInvoice);

module.exports = router;
