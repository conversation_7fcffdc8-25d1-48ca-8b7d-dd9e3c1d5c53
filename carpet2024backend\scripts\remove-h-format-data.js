// Script to remove all H- format imported data (keeping K- records safe)
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function removeHFormatData() {
  console.log('🔄 REMOVING H- FORMAT DATA');
  console.log('='.repeat(60));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // First, check what we have
    const totalBefore = await collection.countDocuments();
    const kRecordsBefore = await collection.countDocuments({ receiveNo: { $regex: /^K-/ } });
    const hRecordsBefore = await collection.countDocuments({ receiveNo: { $regex: /^H-/ } });
    
    console.log(`📊 Before removal:`);
    console.log(`   Total records: ${totalBefore}`);
    console.log(`   K- records: ${kRecordsBefore} (will be kept)`);
    console.log(`   H- records: ${hRecordsBefore} (will be removed)`);
    
    // Get list of H- records to be deleted (for logging)
    const hRecordsToDelete = await collection.find(
      { receiveNo: { $regex: /^H-/ } },
      { receiveNo: 1, weaverName: 1 }
    ).toArray();
    
    console.log('\n📋 H- records to be deleted:');
    hRecordsToDelete.slice(0, 10).forEach((record, index) => {
      console.log(`   ${index + 1}. ${record.receiveNo} - ${record.weaverName || 'N/A'}`);
    });
    if (hRecordsToDelete.length > 10) {
      console.log(`   ... and ${hRecordsToDelete.length - 10} more H- records`);
    }
    
    // Delete all H- format records
    const deleteResult = await collection.deleteMany({
      receiveNo: { $regex: /^H-/ }
    });
    
    console.log(`\n✅ Deleted ${deleteResult.deletedCount} H- records`);
    
    // Verify after deletion
    const totalAfter = await collection.countDocuments();
    const kRecordsAfter = await collection.countDocuments({ receiveNo: { $regex: /^K-/ } });
    const hRecordsAfter = await collection.countDocuments({ receiveNo: { $regex: /^H-/ } });
    
    console.log(`\n📊 After removal:`);
    console.log(`   Total records: ${totalAfter}`);
    console.log(`   K- records: ${kRecordsAfter} (preserved)`);
    console.log(`   H- records: ${hRecordsAfter} (should be 0)`);
    
    return {
      deletedCount: deleteResult.deletedCount,
      beforeCounts: { total: totalBefore, k: kRecordsBefore, h: hRecordsBefore },
      afterCounts: { total: totalAfter, k: kRecordsAfter, h: hRecordsAfter }
    };
    
  } catch (error) {
    console.error('❌ Error removing H- format data:', error);
    return null;
  }
}

async function verifyKRecordsIntact() {
  console.log('\n🔍 Verifying K- records are intact...');
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get sample K- records to verify they're still there
    const kSamples = await collection.find({ receiveNo: { $regex: /^K-/ } }).limit(10).toArray();
    
    console.log('\n📋 Sample K- records (should be intact):');
    kSamples.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver: ${record.weaverName || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ${record.amount || 'N/A'}`);
      console.log('   ' + '-'.repeat(50));
    });
    
    // Check distinct K- weaver names
    const kWeavers = await collection.distinct('weaverName', { receiveNo: { $regex: /^K-/ } });
    console.log(`\n👥 K- Format Weaver Names (${kWeavers.length}) - All Preserved:`);
    kWeavers.forEach((weaver, index) => {
      console.log(`   ${index + 1}. ${weaver}`);
    });
    
  } catch (error) {
    console.error('❌ Error verifying K- records:', error);
  }
}

async function main() {
  console.log('🔄 REMOVING H- FORMAT IMPORTED DATA');
  console.log('(Keeping all K- records safe and intact)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Remove H- format data
    const result = await removeHFormatData();

    if (result) {
      // Display results
      console.log('\n' + '='.repeat(60));
      console.log('📊 H- FORMAT DATA REMOVAL COMPLETE');
      console.log('='.repeat(60));
      console.log(`✅ Successfully deleted: ${result.deletedCount} H- records`);
      console.log(`✅ K- records preserved: ${result.afterCounts.k} (unchanged)`);
      console.log(`✅ Total records now: ${result.afterCounts.total}`);
      
      if (result.afterCounts.h === 0) {
        console.log('✅ All H- records successfully removed');
      } else {
        console.log(`⚠️ Warning: ${result.afterCounts.h} H- records still remain`);
      }
    }
    
    // Verify K- records are intact
    await verifyKRecordsIntact();

  } catch (error) {
    console.error('❌ Removal failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 H- FORMAT DATA REMOVED SUCCESSFULLY!');
    console.log('✅ All H- records deleted');
    console.log('✅ K- records (53) remain safe and intact');
    console.log('✅ Database back to original K- format only');
    console.log('✅ Clean weaver names preserved: Rahila, Jeet Narayan, etc.');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
