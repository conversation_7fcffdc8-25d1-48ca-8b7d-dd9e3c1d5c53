// routes/roleRoutes.js
const express = require('express');
const roleController = require('../../controller/role-controller');

const router = express.Router();

// Role routes
router.post('/create-role',  roleController.createRole); //done
router.get('/get-role/:id',  roleController.getRoleById); //done
router.get('/get-all-roles',  roleController.getAllRoles); //done
router.get('/get-role-by-name',  roleController.getRoleByName); //done
router.put('/update-role/:id',  roleController.updateRole); //done
router.delete('/delete-role/:id',  roleController.deleteRole); //done

module.exports = router;
