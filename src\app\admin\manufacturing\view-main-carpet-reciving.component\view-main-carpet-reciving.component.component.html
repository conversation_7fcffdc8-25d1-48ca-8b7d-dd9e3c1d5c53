<div class="container mt-4">
  <section >
      <fieldset>
          <legend><b>View Carpet Receiving</b></legend>
<div class="row">

  <div class="d-flex justify-content-between">

  <div class="col-md-3">
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>Filter</mat-label>
      <input matInput (keyup)="applyFilter($event)" placeholder="Search...">
    </mat-form-field>
  </div>

  <!-- Weaver Filter -->
<div class="col-md-3">
  <mat-form-field appearance="outline">
    <mat-label>Weaver</mat-label>
    <mat-select [(ngModel)]="selectedWeaver" (selectionChange)="applyAllFilters()">
      <mat-option value="">All</mat-option>
      <mat-option *ngFor="let weaver of weaverList" [value]="weaver">{{weaver}}</mat-option>
    </mat-select>
  </mat-form-field>
</div>

 <!-- Year Filter -->
<div class="col-md-2">
  <mat-form-field appearance="outline">
    <mat-label>Year</mat-label>
    <mat-select [(ngModel)]="selectedYear" (selectionChange)="applyAllFilters()">
      <mat-option value="">All</mat-option>
      <mat-option *ngFor="let year of availableYears" [value]="year">{{ year }}</mat-option>
    </mat-select>
  </mat-form-field>
</div>

<!-- Month Filter -->
<div class="col-md-2">
  <mat-form-field appearance="outline">
    <mat-label>Month</mat-label>
    <mat-select [(ngModel)]="selectedMonth" (selectionChange)="applyAllFilters()">
      <mat-option *ngFor="let month of availableMonths" [value]="month.value">{{ month.label }}</mat-option>
    </mat-select>
  </mat-form-field>
</div>

  <div class="col-md-2">
  <mat-form-field appearance="outline" class="w-100">
    <mat-label>Area In</mat-label>
    <mat-select [(value)]="areaUnit">
      <mat-option value="">All</mat-option>
      <mat-option value="Sq.Feet">Sq.Feet</mat-option>
      <mat-option value="Sq.Yard">Sq.Yard</mat-option>
    </mat-select>
  </mat-form-field>
</div>


  <!-- <div class="col-md-1 ">
 <button mat-icon-button class="fa-solid fa-circle-arrow-right text-primary-emphasis color fs-2" (click)="viewCarpetDetails()">
                   
                  </button>
</div> -->

</div>

  <div class="col-md-12" style="overflow: auto;" class="scroll-container">
    <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8" style="width: max-content;">
              <!-- SrNo Column (without sorting) -->
              <ng-container matColumnDef="SrNo">
                <th mat-header-cell *matHeaderCellDef>Sr.No</th>
                <td mat-cell *matCellDef="let element">{{element.SrNo}}</td>
              </ng-container>

              <!-- CarpetNo Column (with sorting) -->
              <ng-container matColumnDef="CarpetNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Carpet No</th>
                <td mat-cell *matCellDef="let element">{{element.CarpetNo}}</td>
              </ng-container>

              <!-- CDate Column -->
              <ng-container matColumnDef="CDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>C Date</th>
                <td mat-cell *matCellDef="let element">{{element.CDate}}</td>
              </ng-container>

              <!-- IssueNo Column -->
              <ng-container matColumnDef="IssueNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Issue No</th>
                <td mat-cell *matCellDef="let element">{{element.IssueNo}}</td>
              </ng-container>

              <!-- IDate Column -->
              <ng-container matColumnDef="IDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>I Date</th>
                <td mat-cell *matCellDef="let element">{{element.IDate}}</td>
              </ng-container>

              <!-- Weaver Column -->
              <ng-container matColumnDef="Weaver">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Weaver</th>
                <td mat-cell *matCellDef="let element">{{element.Weaver}}</td>
              </ng-container>

              <!-- Quality Column -->
              <ng-container matColumnDef="Quality">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
                <td mat-cell *matCellDef="let element">{{element.Quality}}</td>
              </ng-container>

              <!-- Design Column -->
              <ng-container matColumnDef="Design">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Design</th>
                <td mat-cell *matCellDef="let element">{{element.Design}}</td>
              </ng-container>

              <!-- Color Column -->
              <ng-container matColumnDef="Color">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Color</th>
                <td mat-cell *matCellDef="let element">{{element.Color}}</td>
              </ng-container>

              <!-- Size Column -->
              <ng-container matColumnDef="Size">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Size</th>
                <td mat-cell *matCellDef="let element">{{element.Size}}</td>
              </ng-container>

              <!-- Pcs Column -->
              <ng-container matColumnDef="Pcs">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Pcs</th>
                <td mat-cell *matCellDef="let element">{{element.Pcs}}</td>
              </ng-container>

              <!-- Area Column -->
              <ng-container matColumnDef="Area">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ getAreaHeader() }}</th>
                <td mat-cell *matCellDef="let element">{{ getAreaValue(element) }}</td>
              </ng-container>

              <!-- Rate Column -->
              <ng-container matColumnDef="Rate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Rate</th>
                <td mat-cell *matCellDef="let element">{{element.Rate | number:'1.2-2'}}</td>
              </ng-container>

              <!-- Amount Column -->
              <ng-container matColumnDef="Amount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Amount</th>
                <td mat-cell *matCellDef="let element">{{element.Amount | number:'1.0-0' }}.00</td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let element">
                  <button mat-icon-button color="primary" (click)="openCarpetBorderDialog(element)" matTooltip="Measure Border">
                    <img src="assets/images/icons/measuring-tape.png" alt="Measuring Tape" width="28" height="28" style="object-fit:contain;" />
                  </button>
                  <button mat-icon-button color="warn" (click)="deleteCarpet(element.CarpetNo)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
    <!-- No Data Row -->
            <tr class="mat-row" *matNoDataRow>
              <td class="mat-cell" colspan="15">No data matching the filter</td>
            </tr>
         
  <mat-paginator [pageSizeOptions]="[50, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>

</div>

</fieldset>
  </section>
  </div>


