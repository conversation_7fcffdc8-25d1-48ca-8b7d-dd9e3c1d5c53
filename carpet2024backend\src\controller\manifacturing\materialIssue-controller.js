const materialIssueService = require('../../services/manifacturing/materialIssue-service');

const createMaterialIssue = async (req, res) => {
  try {
    console.log('🔥 Controller: Material Issue API called!');
    console.log('🔍 Request body:', JSON.stringify(req.body, null, 2));

    const materialIssueData = req.body;
    console.log('🔍 Processing material issue data...');
    const newMaterialIssue = await materialIssueService.createMaterialIssue(materialIssueData);

    if (newMaterialIssue) {
      res.status(201).json(newMaterialIssue);
    }
  } catch (error) {
    console.error('Controller: Error creating material issue:', error.message);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to create material issue',
      error: error.message
    });
  }
};

const getAllMaterialIssues = async (req, res) => {
  try {
    console.log('Controller: Fetching all material issues');

    const materialIssues = await materialIssueService.getAllMaterialIssues();

    res.status(200).json(materialIssues);
  } catch (error) {
    console.error('Controller: Error fetching material issues:', error.message);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch material issues',
      error: error.message
    });
  }
};

const getMaterialIssueById = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Controller: Fetching material issue by ID:', id);

    const materialIssue = await materialIssueService.getMaterialIssueById(id);

    res.status(200).json(materialIssue);
  } catch (error) {
    console.error('Controller: Error fetching material issue by ID:', error.message);
    const statusCode = error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message || 'Failed to fetch material issue',
      error: error.message
    });
  }
};

const updateMaterialIssue = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    console.log('Controller: Updating material issue:', id, 'with data:', updateData);

    const updatedMaterialIssue = await materialIssueService.updateMaterialIssue(id, updateData);

    res.status(200).json(updatedMaterialIssue);
  } catch (error) {
    console.error('Controller: Error updating material issue:', error.message);
    const statusCode = error.message.includes('not found') ? 404 : 400;
    res.status(statusCode).json({
      success: false,
      message: error.message || 'Failed to update material issue',
      error: error.message
    });
  }
};

const updateMaterialIssueField = async (req, res) => {
  try {
    const { id } = req.params;
    const { field, value } = req.body;
    console.log('Controller: Updating material issue field:', id, field, value);

    const updatedMaterialIssue = await materialIssueService.updateMaterialIssueField(id, field, value);

    res.status(200).json(updatedMaterialIssue);
  } catch (error) {
    console.error('Controller: Error updating material issue field:', error.message);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to update material issue field',
      error: error.message
    });
  }
};

const deleteMaterialIssueField = async (req, res) => {
  try {
    const { id } = req.params;
    const { field } = req.body;
    console.log('Controller: Deleting material issue field:', id, field);

    const updatedMaterialIssue = await materialIssueService.deleteMaterialIssueField(id, field);

    res.status(200).json(updatedMaterialIssue);
  } catch (error) {
    console.error('Controller: Error deleting material issue field:', error.message);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to delete material issue field',
      error: error.message
    });
  }
};

const deleteMaterialIssue = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Controller: Deleting material issue:', id);

    const deletedMaterialIssue = await materialIssueService.deleteMaterialIssue(id);

    res.status(200).json(deletedMaterialIssue);
  } catch (error) {
    console.error('Controller: Error deleting material issue:', error.message);
    const statusCode = error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message || 'Failed to delete material issue',
      error: error.message
    });
  }
};

const getMaterialIssuesByChallanNo = async (req, res) => {
  try {
    const { challanNo } = req.params;
    console.log('Controller: Fetching material issue by challan number:', challanNo);

    const materialIssue = await materialIssueService.getMaterialIssuesByChallanNo(challanNo);

    res.status(200).json(materialIssue);
  } catch (error) {
    console.error('Controller: Error fetching material issue by challan number:', error.message);
    const statusCode = error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message || 'Failed to fetch material issue',
      error: error.message
    });
  }
};

const getMaterialIssuesByWeaver = async (req, res) => {
  try {
    const { weaver } = req.params;
    console.log('Controller: Fetching material issues by weaver:', weaver);

    const materialIssues = await materialIssueService.getMaterialIssuesByWeaver(weaver);

    res.status(200).json(materialIssues);
  } catch (error) {
    console.error('Controller: Error fetching material issues by weaver:', error.message);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch material issues',
      error: error.message
    });
  }
};

const getMaterialIssuesByDateRange = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    console.log('Controller: Fetching material issues by date range:', startDate, 'to', endDate);

    const materialIssues = await materialIssueService.getMaterialIssuesByDateRange(startDate, endDate);

    res.status(200).json(materialIssues);
  } catch (error) {
    console.error('Controller: Error fetching material issues by date range:', error.message);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch material issues',
      error: error.message
    });
  }
};

const getMaterialIssuesByIssueNo = async (req, res) => {
  try {
    const { issueNoId } = req.params;
    console.log('Controller: Fetching material issues by issue number:', issueNoId);

    const materialIssues = await materialIssueService.getMaterialIssuesByIssueNo(issueNoId);

    res.status(200).json(materialIssues);
  } catch (error) {
    console.error('Controller: Error fetching material issues by issue number:', error.message);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch material issues',
      error: error.message
    });
  }
};

module.exports = {
  createMaterialIssue,
  getAllMaterialIssues,
  getMaterialIssueById,
  updateMaterialIssue,
  updateMaterialIssueField,
  deleteMaterialIssueField,
  deleteMaterialIssue,
  getMaterialIssuesByChallanNo,
  getMaterialIssuesByWeaver,
  getMaterialIssuesByDateRange,
  getMaterialIssuesByIssueNo
};
