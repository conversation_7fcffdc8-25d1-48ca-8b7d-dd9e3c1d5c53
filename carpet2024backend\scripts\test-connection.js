// Simple script to test MongoDB connection
const mongoose = require('mongoose');

const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function testConnection() {
  console.log('🔄 Testing MongoDB connection...');
  
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 15000,
      connectTimeoutMS: 10000
    });
    
    console.log('✅ Connected to MongoDB Atlas successfully!');
    
    // Test a simple query
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    const count = await collection.countDocuments();
    console.log(`📊 Total records in carpetreceiveds: ${count}`);
    
    // Count H- records
    const hCount = await collection.countDocuments({ receiveNo: { $regex: /^H-/ } });
    console.log(`📊 H- format records: ${hCount}`);
    
    await mongoose.connection.close();
    console.log('🔌 Connection closed successfully');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
  }
}

testConnection();
