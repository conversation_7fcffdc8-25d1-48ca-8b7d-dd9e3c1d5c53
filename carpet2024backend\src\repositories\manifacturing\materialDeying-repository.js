// repositories/materialDeyingRepository.js
const MaterialDeying = require('../../model/phase-4/materialDeying');

class MaterialDeyingRepository {
  async create(data) {
    const materialDeying = new MaterialDeying(data);
    return await materialDeying.save();
  }

  async findById(id) {
    return await MaterialDeying.findById(id)
      .populate('name')
      .populate('group')
      .populate('item')
      .populate('woolQuality')
      .populate('colour')
      .populate('partyName')
      .exec();
  }

  async findAll() {
    return await MaterialDeying.find()
      .populate('name')
      .populate('group')
      .populate('item')
      .populate('woolQuality')
      .populate('colour')
      .populate('partyName')
      .exec();
  }

  async update(id, data) {
    return await MaterialDeying.findByIdAndUpdate(id, data, { new: true });
  }

  async delete(id) {
    return await MaterialDeying.findByIdAndDelete(id);
  }
}

module.exports = new MaterialDeyingRepository();
