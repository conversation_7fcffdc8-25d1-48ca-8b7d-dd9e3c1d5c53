<div class="container mt-5">
    <section>
        <fieldset>
            <legend><b>{{isUpdated?'Update':'Add'}} Deying Rate</b></legend>
            <form [formGroup]="frmDyeingRate" (ngSubmit)="addDyng()">
              <div class="row">
                <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline">
                    <mat-label>Dyers Name</mat-label>
                    <mat-select formControlName="name" >
                      <mat-option *ngFor="let dyers of dyersList" value="{{dyers._id}}">
                        {{ dyers.name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline" class="ex-width">
                        <mat-label>To Date</mat-label>
                        <input matInput [matDatepicker]="picker" formControlName="toDate">
                        <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                      </mat-form-field>
                </div>

                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline" class="ex-width">
                        <mat-label>From Date</mat-label>
                        <input matInput [matDatepicker]="picker1"  formControlName="fromDate" >

                        <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
                        <mat-datepicker #picker1></mat-datepicker>
                      </mat-form-field>

                </div>


                <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline">
                    <mat-label>Material</mat-label>
                    <mat-select formControlName="material" (valueChange)="onCount($event)">
                      <mat-option *ngFor="let group of groupList" value="{{group.id}}">
                        {{ group.Group }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline">
                    <mat-label>Quality</mat-label>
                    <mat-select formControlName="quality" (valueChange)="onQuality($event)">
                      <mat-option *ngFor="let quality of qualityList" value="{{quality.id}}">
                        {{ quality.quality }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline">
                    <mat-label>Cout</mat-label>
                    <mat-select formControlName="count" (valueChange)="selectCount($event)">
                      <mat-option *ngFor="let count of countList" value="{{count.count}}">
                        {{ count.count }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline">
                    <mat-label>Color</mat-label>
                    <mat-select formControlName="color" (valueChange)="onColor($event)">
                      <mat-option *ngFor="let color of colorList" value="{{color.id}}">
                        {{ color.newColor }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline">
                    <mat-label>Yarn Color</mat-label>
                    <mat-select formControlName="yarnColor">
                      <mat-option *ngFor="let yarnColor of yarnColorList" value="{{yarnColor._id}}">
                        {{ yarnColor.Color }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
                <div class="mb-2 col-md-2">
                  <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Company Color Code</mat-label>
                    <input matInput placeholder="Company Color Code" formControlName="companyColorCode"/>
                  </mat-form-field>
                </div>
                <div class="mb-2 col-md-1">
                  <input
                    class="Clr"
                    [style.background]="color"
                  />
                </div>
                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline" class="ex-width">
                        <mat-label>Rate</mat-label>
                        <input matInput placeholder="Rate" formControlName="rate"/>
                      </mat-form-field>

                </div>

                <div class="mb-2 col-md-3">
                  <button *ngIf="!isUpdated; else updateTemplate" mat-flat-button color="primary">Save</button>
                  <ng-template #updateTemplate>
                    <button mat-flat-button color="primary" (click)="update()" type="button">Update</button>
                  </ng-template>
                </div>
            </div>
            </form>
        </fieldset>
    </section>
</div>

<div class="container mt-4">
    <section>
        <fieldset>
            <legend><b> List</b></legend>
            <div class="row">
                <div class="col-4">
                    <mat-form-field appearance="outline">
                        <mat-label>Search</mat-label>
                        <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Jack" #input>
                      </mat-form-field>
                </div>
                <div class="col-12">
                      <div class="mat-elevation-z8" style="overflow: auto;">
                        <table mat-table [dataSource]="dataSource" matSort>

                          <!-- SrNo Column -->
                          <ng-container matColumnDef="id">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Sr.No</th>
                            <td mat-cell *matCellDef="let row"> {{row.index}} </td>
                          </ng-container>
                          <!-- Name Column -->
                          <ng-container matColumnDef="name">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                            <td mat-cell *matCellDef="let row"> {{row.name}} </td>
                          </ng-container>
                          <ng-container matColumnDef="yarnColor">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Yarn Color</th>
                            <td mat-cell *matCellDef="let row"> {{row.yarnColor}} </td>
                          </ng-container>



                          <ng-container matColumnDef="color">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Color</th>
                            <td mat-cell *matCellDef="let row"> {{row.color}}</td>
                          </ng-container>
                          <ng-container matColumnDef="companyColorCode">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Comp Color</th>
                            <td mat-cell *matCellDef="let row">
                              <span
                    class="color-box"
                    [style.background-color]="row.colorCode"
                  ></span> {{row.companyColorCode}}</td>
                          </ng-container>




                          <!-- ToDate Column -->
                          <ng-container matColumnDef="toDate">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>To Date</th>
                            <td mat-cell *matCellDef="let row"> {{row.toDate}}</td>
                          </ng-container>

                          <!-- FromData Column -->
                          <ng-container matColumnDef="fromDate">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>From Data</th>
                            <td mat-cell *matCellDef="let row"> {{row.fromDate}} </td>
                          </ng-container>

                          <!--Material Column -->
                          <ng-container matColumnDef="material">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Material</th>
                            <td mat-cell *matCellDef="let row"> {{row.material}} </td>
                          </ng-container>

                         <!-- Quality Column -->
                         <ng-container matColumnDef="quality">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
                            <td mat-cell *matCellDef="let row"> {{row.quality}} </td>
                          </ng-container>

                             <!-- Count Column -->
                             <ng-container matColumnDef="count">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Count</th>
                                <td mat-cell *matCellDef="let row"> {{row.count}} </td>
                              </ng-container>
                             <!-- CalculateArea Column -->

                             <!-- Rate Column -->
                             <ng-container matColumnDef="rate">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Rate</th>
                                <td mat-cell *matCellDef="let row"> {{row.rate}} </td>
                              </ng-container>

                                <!-- Action Column -->
                             <ng-container matColumnDef="action">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                                <td mat-cell *matCellDef="let row">
                                  <a (click)="edit(row.id)"
                                  ><i
                                    class="fa fa-pencil-square-o fa-edit"
                                    title="Edit purchase details "
                                    aria-hidden="true"
                                  ></i
                                ></a>
                                &nbsp;
                                <a (click)="delete(row.id)"><i class="fa fa-trash-o" aria-hidden="true"></i></a>
  </td>
                              </ng-container>
                                 <!-- Area Column -->
                          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                          <!-- Row shown when there is no matching data. -->
                          <tr class="mat-row" *matNoDataRow>
                            <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>
                          </tr>
                        </table>

                        <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>
                </div>


            </div>
            </div>
        </fieldset>
    </section>
 </div>
