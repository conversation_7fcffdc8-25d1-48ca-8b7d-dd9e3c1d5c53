 <div class="container mt-4">
    <section>
        <fieldset>
            <legend><b> {{isUpdated?'Update':'Add'}} Colour Details </b></legend>
          <form [formGroup]="frmColourCode" (ngSubmit)="addColourDetails()">
            <div class="row">
              <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Quality</mat-label>
                      <mat-select formControlName="quality">
                        @for (qty of qualityList; track qty) {
                          <mat-option [value]="qty.id">{{ qty.quality }}</mat-option>
                          }
                      </mat-select>
                  </mat-form-field>

              </div>

              <div class="mb-2 col-md-3">

                <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Colour</mat-label>
                    <input matInput placeholder="Colour" formControlName="newColor">
                </mat-form-field>

            </div>
            <div class="mb-2 col-md-3">

              <mat-form-field appearance="outline" class="ex-width">
                  <mat-label>Company Colour Code</mat-label>
                  <input matInput placeholder="Company Colour Code" formControlName="companyColorCode">
              </mat-form-field>

          </div>
              <div class="mb-2 col-md-3">
                <mat-form-field appearance="outline" class="ex-width">
                  <mat-label>Colour Code</mat-label>
                  <input matInput placeholder="Colour Code" [value]="color" [(colorPicker)]="color" formControlName="colorCode">
              </mat-form-field>


              </div>


              <div class="mb-2 col-md-1">
                <!-- [style.background]="color2" [(colorPicker)]="color2" -->
                <input  class="Clr"[(colorPicker)]="color" [style.background]="color"/>
              </div>
              <div class="mb-2 col-md-3">

                <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Remarks</mat-label>
                    <input matInput placeholder="Remarks" formControlName="remark">
                </mat-form-field>

            </div>
              <div class="mt-2 col-md-3">

                  <button *ngIf="!isUpdated; else updateBtn" mat-flat-button color="primary">Add</button>

                  <ng-template #updateBtn>
                  <button  mat-flat-button type="button" color="primary" (click)="update()">Update</button>
                  </ng-template>
              </div>
          </div>
          </form>
        </fieldset>
    </section>
 </div>
 <div class="container mt-5">
    <section>
        <fieldset style="min-height:200px;">
            <legend><b>List</b></legend>

            <div class="row">

              <div class="col-md-4 ">
                <mat-form-field appearance="outline">
                  <mat-label>Search</mat-label>
                  <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Jack" #input>
                </mat-form-field>
              </div>
          <!-- <div class="col-md-3 mt-2">
            <button mat-flat-button type="button" color="primary" (click)="convertPdf()">Export To Pdf</button>
          </div> -->

          <!-- <div class="col-md-3 mt-2">
            <button mat-flat-button type="button" color="primary" (click)="convertExcel()">Export To Excel</button>
          </div> -->
          <div class="col-md-3 mt-2">
            <button mat-flat-button type="button" color="primary" (click)="convertPdf()">Export To Pdf</button>
          </div>

                <div class=" mb-2 col-12">

                      <div class="mat-elevation-z8">
                        <table mat-table [dataSource]="dataSource" matSort>

                          <!-- Sr. No. Column -->
                          <ng-container matColumnDef="id">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Sr. No. </th>
                            <td mat-cell *matCellDef="let row; let i = index"> {{i+1}} </td>
                          </ng-container>

                          <!-- Quality Column -->
                          <ng-container matColumnDef="quality">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
                            <td mat-cell *matCellDef="let row"> {{row.quality}}</td>
                          </ng-container>
                          <ng-container matColumnDef="companyColorCode">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Color Code</th>
                            <td mat-cell *matCellDef="let row"> {{row.companyColorCode}}</td>
                          </ng-container>

                          <!-- Colour Column -->
                          <ng-container matColumnDef="newColor">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Colour</th>
                            <td mat-cell *matCellDef="let row"> {{row.newColor}} </td>
                          </ng-container>

                          <!-- Colour Code Column -->
                          <ng-container matColumnDef="colorCode">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Comp Code</th>
                            <td mat-cell *matCellDef="let row">  <span class="color-box" [style.background-color]="row.colorCode"></span> </td>
                          </ng-container>

                         <!-- Colour Picker Column -->
                         <!-- <ng-container matColumnDef="ColourPicker">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Colour Picker</th>
                            <td mat-cell *matCellDef="let row"> {{row.ColourPicker}} </td>
                          </ng-container> -->
                         <!-- Remarksr Column -->
                         <ng-container matColumnDef="remark">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Remarks</th>
                            <td mat-cell *matCellDef="let row"> {{row.remark}} </td>
                          </ng-container>

                         <!-- Action Column -->
                              <ng-container matColumnDef="action">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                                <td mat-cell *matCellDef="let row"> <a (click)="edit(row.id)"
                                  ><i
                                    class="fa fa-pencil-square-o fa-edit"
                                    title="Edit purchase details "
                                    aria-hidden="true"
                                  ></i
                                ></a>
                                &nbsp;
                                <a (click)="delete(row.id)"><i class="fa fa-trash-o" aria-hidden="true"></i></a> </td>
                              </ng-container>
                                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                          <!-- Row shown when there is no matching data. -->
                          <tr class="mat-row" *matNoDataRow>
                            <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>
                          </tr>
                        </table>

                        <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>
                      </div>


                </div>
            </div>
        </fieldset>
    </section>
 </div>
