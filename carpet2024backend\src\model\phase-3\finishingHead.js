const mongoose = require('mongoose');

const finishingHeadSchema = new mongoose.Schema({
  work: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  toDate: {
    type: Date,
    required: true
  },
  fromDate: {
    type: Date,
    required: true
  },
  quality: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AddQuality',
    required: true
  },
  design: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AddDesign',
    required: true
  },
  rate: {
    type: String,
    required: true
  }
});

const finishingHead = mongoose.model('finishingHead', finishingHeadSchema);

module.exports = finishingHead;
