import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { SweetalertService } from '../../../services/sweetalert.service';
import { jsPDF } from 'jspdf';
import { ExportDataServiceService } from '../../../services/export-data-service.service';
import 'jspdf-autotable';

export interface UserData {
  id: string;
  index: number;
  newColor: string;
  colorCode: string;
  remark: string;
  quality: string;
  companyColorCode: string;
}

const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-color-code-details',
  templateUrl: './color-code-details.component.html',
  styleUrl: './color-code-details.component.css',
})
export class ColorCodeDetailsComponent implements OnInit, AfterViewInit {
  Quality = 'option1';
  Colour = 'option2';
  // color: any;

  editedId: string | undefined;
  isUpdated: boolean = false;
  pdfData: any;

  constructor(
    private services: MasterService,
    private fb: FormBuilder,
    private alert: SweetalertService,
    private exportDataServiceService: ExportDataServiceService
  ) {}
  public arrayColors: any = {
    color: '#e920e9',
  };
  public color: string = '';
  displayedColumns: string[] = [
    'id',
    'quality',
    'newColor',
    'companyColorCode',
    'remark',
    'colorCode',

    'action',
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);
  frmColourCode!: FormGroup;
  ngOnInit(): void {
    this.frmColourCode = this.fb.group({
      newColor: [],
      colorCode: [],
      companyColorCode: [],
      remark: [],
      quality: [],
    });
    this.qualitys();
    this.getsColor();
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  qualityList: any = [];
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  qualitys() {
    this.services.getqualityList().subscribe({
      next: (value: any) => {
        value.map((v: any) => {
          this.qualityList.push({
            quality: v.quality,
            id: v._id,
          });
          this.qualityList.sort((a: any, b: any) => {
            let numA = this.extractFirstNumber(a.quality);
            let numB = this.extractFirstNumber(b.quality);
            return numA - numB;
          });
        });
      },
      error: (err) => {},
    });
  }
  extractFirstNumber(str: any) {
    let parts = str.split('x').map((part: any) => parseInt(part.trim(), 10));
    return parts[0];
  }
  addColourDetails() {
    console.log(this.color);
    console.log(this.frmColourCode.value);
    let formData = this.frmColourCode.value;

    formData.colorCode = this.color;
    this.services.addColourDetails(formData).subscribe({
      next: (value) => {
        if (value) {
          this.alert.success('success', 'Color details saved successfully');
          this.getsColor();
          this.clear();
        }
      },
      error: (err) => {
        this.alert.error('Failed !', 'Something went wrong');
      },
    });
  }
  getsColor() {
    this.services.getsColourCodeDetails().subscribe({
      next: (value: any) => {
        if (value) {
          ELEMENT_DATA.length = 0;
          debugger;
          value.map((v: any, i: number) => {
            ELEMENT_DATA.push({
              id: v._id,
              index: i + 1,
              newColor: v.newColor,
              colorCode: v.colorCode,
              remark: v.remark,
              companyColorCode: v.companyColorCode,
              quality: v.quality?.quality,
            });
          });
          debugger;

          // Function to extract the numeric part of the color code
          const getColorCodeNumber = (code: any) =>
            parseInt(code.match(/\d+/)[0]);

          // Function to parse quality
          const parseQuality = (quality: any) => {
            const [width, height] = quality.split(' x ').map(Number);
            return width * height;
          };

          // Sorting the data
          const sortedData = ELEMENT_DATA.sort((a: any, b: any) => {
            // Sort by quality
            const qualityDiff =
              parseQuality(a.quality) - parseQuality(b.quality);
            if (qualityDiff !== 0) return qualityDiff;

            // Sort by newColor
            const colorDiff = a.newColor.localeCompare(
              b.newColor.toLowerCase()
            );
            if (colorDiff !== 0) return colorDiff;

            // Sort by numeric part of colorCode
            return (
              getColorCodeNumber(a.companyColorCode) -
              getColorCodeNumber(b.companyColorCode)
            );
          });
          debugger;
          this.dataSource = new MatTableDataSource(sortedData);
          this.ngAfterViewInit();
          return;
        }
      },
    });
  }

  // getSerialNumber(index:number){
  //   return index + 1
  // }

  edit(id: string) {
    this.services.getColourCodeDetail(id).subscribe({
      next: (value: any) => {
        if (value) {
          this.editedId = id;
          this.isUpdated = true;
          this.color = value.colorCode;
          this.frmColourCode.patchValue(value);
        }
      },
    });
  }
  update() {
    console.log(this.frmColourCode.value);
    let formData = this.frmColourCode.value;
    formData.colorCode = this.color;
    this.services.updateColorCodeDetails(this.editedId, formData).subscribe({
      next: (value) => {
        this.alert.success('success', 'Color details update successfully');
        this.getsColor();
        this.clear();
      },
      error: (err) => {
        this.alert.error('Failed !', 'Something went wrong');
      },
    });
  }
  delete(id: string) {
    this.alert
      .confirm({
        title: 'Delete Item',
        text: 'Are you sure you want to delete this item?',
        icon: 'warning',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'No, keep it',
      })
      .then((confi: any) => {
        if (confi.isConfirmed) {
          this.services.deleteColourDetail(id).subscribe({
            next: (value) => {
              this.alert.success(
                'success',
                'Color details has been deleted successfully'
              );
              this.getsColor();
            },
            error: (err) => {
              this.alert.error('Failed !', 'Something went wrong');
            },
          });
        }
      });
  }

  clear() {
    this.frmColourCode.reset();

    this.isUpdated = false;
  }

  datas: any[] = [];

  convertPdf(): void {
    debugger;
    this.datas = this.dataSource.filteredData;
    this.generatePDF(this.datas);
  }
  capitalizeWords = (str: string) => {
    debugger;
    if (!str) {
      return null;
    }
    return str.replace(/\b\w/g, (char) => char.toUpperCase());
  };
  generatePDF(data: any) {

    const doc = new jsPDF();


    doc.setFontSize(18);
    doc.text('Colour Details', 105, 10, { align: 'center' });


    const columns = [
      'SrNo.',
      'Quality',
      'Remark',
      'New Color',
      'Company Color Code',
      'Color Code',

    ];

    const rows = data.map((item: any, i: number) => [
      i + 1,
      item.quality,
      this.capitalizeWords(item.remark),
      this.capitalizeWords(item.newColor),
      item.companyColorCode,
      item.colorCode,

    ]);


    (doc as any).autoTable({
      head: [columns],
      body: rows,
      theme: 'plain',

      didParseCell: (data: any) => {

        if (data.column.index === 5 && data.cell.raw) {
          const colorCode = data.cell.raw;


          if (/^#[0-9A-F]{6}$/i.test(colorCode)) {

            data.cell.styles.fillColor = this.hexToRgbArray(colorCode);
            data.cell.text = '';
          }
        }
      },
      styles: {
        textColor: [0, 0, 0],
        fontSize: 10,
        lineWidth: 0.1,
        lineColor: [0, 0, 0],
      },
      headStyles: {
        fillColor: '#e6e48d',
        lineWidth: 0.1,
        lineColor: [0, 0, 0],
      },


    });


    doc.save('unique_data.pdf');
  }


  hexToRgbArray(hex: string) {

    hex = hex.replace('#', '');
    const bigint = parseInt(hex, 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;

    return [r, g, b];
  }



  convertExcel() {
    const sortedData = this.dataSource.filteredData;

    let excelData = sortedData.map((v: any, i: number) => ({
      newColor: v.newColor,
      colorCode: v.colorCode,
      remark: v.remark,
      companyColorCode: v.companyColorCode,
      quality: v.quality,
    }));
    this.exportDataServiceService.exportBillExcelFile(excelData);
  }
}
