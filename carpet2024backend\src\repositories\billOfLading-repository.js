const BillOfLading = require('../model/phase-1/billOfLading');

class BillOfLadingRepository {
  async getAllBillOfLadings() {
    try {
      return await BillOfLading.find();
    } catch (error) {
      throw error;
    }
  }

  async getBillOfLadingById(id) {
    try {
      return await BillOfLading.findById(id);
    } catch (error) {
      throw error;
    }
  }

  async createBillOfLading(billOfLadingData) {
    try {
      const billOfLading = new BillOfLading(billOfLadingData);
      await billOfLading.save();
      return billOfLading;
    } catch (error) {
      throw error;
    }
  }

  async updateBillOfLading(id, updatedData) {
    try {
      return await BillOfLading.findByIdAndUpdate(id, updatedData, { new: true });
    } catch (error) {
      throw error;
    }
  }

  async deleteBillOfLading(id) {
    try {
      return await BillOfLading.findByIdAndDelete(id);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new BillOfLadingRepository();

