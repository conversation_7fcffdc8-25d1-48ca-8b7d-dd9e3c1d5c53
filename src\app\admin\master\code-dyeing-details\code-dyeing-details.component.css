fieldset {
    font-family: sans-serif;
    border: 2px solid #1F497D;
    background: #ffffff;
    border-radius: 5px;
    padding: 15px;
  }

  fieldset legend {
    background: #ffffff;
    color: #000000;
    padding: 5px 10px ;
    font-size: 20px;
    border-radius: 5px;
    /* box-shadow: 0 0 0 5px #ddd; */
    margin-left: 20px;
  }

  legend {
    float: left;
    width: auto;
    padding: 0;
    margin-top: -32px;

    margin-bottom: 0.5rem;
    font-size: calc(1.275rem + .3vw);
    line-height: inherit;
  }
  .ex-width{
    width:100%;
  }
  .Clr{
    width:60px;
    height:60px;
    border-radius: 50%;
}

.color-box {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid #ddd; /* Optional: Add a border to make the box stand out */
}
.line-with-text {
  display: flex;
  align-items: center;
  text-align: center;
  width: 100%;
  border-bottom: 1px solid #000;
  margin: 20px 0;
}

.line-with-text span {
  background-color: #fff; /* Background color to match the page background */
  padding: 0 10px; /* Padding to provide space around the text */
  position: relative;
  top: 0.5em;
}
.frmarr{
  max-height: 380px;
  overflow: auto;
}
.fa-list{
  color: green;
}




/* your-component.component.css */
.fixed-border-table {
  width: 100%;
  border-collapse: collapse;
}

.fixed-border-table th,
.fixed-border-table td {
  border: 1px solid black;
  padding: 8px;
  text-align: left;
}

.fixed-border-table th {
  color: white;
    background-color: #000000;
}

