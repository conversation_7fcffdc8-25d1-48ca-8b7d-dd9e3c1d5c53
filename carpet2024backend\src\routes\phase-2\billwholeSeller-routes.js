const express = require('express');
const BillForWholesellerController = require('../../controller/billwholeseller-controller');
const checkDuplicateBillNo  = require('../../middleware/bill-middleware');
const router = express.Router();
const billForWholesellerController = new BillForWholesellerController();


 const { logRequestResponse, logDataChanges } = require('../../middleware/logger'); // Import middleware functions

// Apply logger middleware to log request and response
//  router.use(logRequestResponse);

// // Apply data changes logger middleware to log data updates, creations, deletions, and retrievals
//  router.use(logDataChanges);
router.post('/BillForWholeseller', checkDuplicateBillNo , billForWholesellerController.createBill.bind(billForWholesellerController));
router.get('/BillForWholeseller', billForWholesellerController.getAllBills.bind(billForWholesellerController));
router.get('/BillForWholeseller/:id', billForWholesellerController.getBillById.bind(billForWholesellerController));
router.put('/BillForWholeseller/:id', billForWholesellerController.updateBill.bind(billForWholesellerController));
router.delete('/BillForWholeseller/:id', billForWholesellerController.deleteBill.bind(billForWholesellerController));
router.post('/carpet-details', billForWholesellerController.getCarpetDetails.bind(billForWholesellerController));
module.exports = router;
