// impoterService.js
const impoterRepository = require('../repositories/impoter-invoice-repository');

async function createImpoterDetail(data) {
    return impoterRepository.createImpoterDetail(data);
}

async function getImpoterDetailById(id) {
    return impoterRepository.getImpoterDetailById(id);
}
async function getAllImpoterDetails() {
    return impoterRepository.getAllImpoterDetails(); // Retrieve all impoter details
}

async function updateImpoterDetail(id, data) {
    return impoterRepository.updateImpoterDetail(id, data);
}

async function deleteImpoterDetail(id) {
    return impoterRepository.deleteImpoterDetail(id);
}


module.exports = {
    createImpoterDetail,
    getImpoterDetailById,
    getAllImpoterDetails,
    updateImpoterDetail,
    deleteImpoterDetail
};
