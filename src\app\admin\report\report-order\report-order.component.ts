import { Component, OnInit } from '@angular/core';
import { ImporterService } from '../../../services/importer.service';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { ActivatedRoute } from '@angular/router';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { SweetalertService } from '../../../services/sweetalert.service';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-report-order',
  templateUrl: './report-order.component.html',
  styleUrl: './report-order.component.css'
})
export class ReportOrderComponent implements OnInit {
  constructor(
    private _service: ImporterService,
    private customeService: CustomeServiceService,
    private activeRoute: ActivatedRoute,
    private ngxLoader: NgxUiLoaderService,
    private pdfService: SweetalertService
  ) {

  }
  allBills: any;
  billId: string = '';
  _isChallan: boolean = false;
  ngOnInit(): void {
    debugger
    // this.ngxLoader.start();
    this.billId = this.activeRoute.snapshot.paramMap.get('id') || '';
    this.initializeData(this.billId);
  }

  initializeData(id: string) {
    // this.ngxLoader.start();
    Promise.all([
      this.getBills(),
      this.getChallans(),
      this.getContainerReceived(),
      this.getImporterName(),
    ]).then(()=>{
      let _bill = id.split(' ');
      if (_bill[1] === 'print') {
        this.viewBill(_bill[0]);
      } else {
        // this.viewChallan(_bill[0]);
      }
       this.ngxLoader.stop()
    }).catch((error)=>{console.log('something went wrong')})


  }


 async getBills() :Promise<any>{
  const resp = await firstValueFrom(this._service.getsBill());
  this.allBills = resp;
}
allChallans: any;
allContainerStock: any;



async getChallans(): Promise<void> {
  const resp = await firstValueFrom(this._service.getAllChallan());
  this.allChallans = resp;
}

async getContainerReceived(): Promise<void> {
  const resp = await firstValueFrom(this._service.getAllContainerRecieved());
  this.allContainerStock = resp;
}
allImporterDetails:any;
async getImporterName(){
  const resp = await firstValueFrom(this._service.getsWholesalerList());
  this.allImporterDetails = resp;
}
billDetails: any = [];
_bill: any = {};
viewBill(id: any) {
  // this.ngxLoader.start();
  let getChallans = this.allBills.find((x: any) => x._id === id.toString());

  let getImporter = this.allImporterDetails.find((name:any)=> name.customerName===getChallans.wholesellerName)

  console.log(getImporter);
    this._bill = {
    billNo: getChallans.billNo,
    date: this.customeService.convertDate(getChallans.chooseAdate),
    customer: getChallans.wholesellerName,
    street:getImporter.address,
    // city:getImporter.impoter.address.city,
    // state:getImporter.impoter.address.state,
    zipCode:getImporter.zipCode,
    country:getImporter.country
  };
  getChallans.challanNo.forEach((element: any) => {
    let getChallanDetails = this.allChallans.find(
      (x: any) => x.challanNo === element.challanNumber
    );

    getChallanDetails.carpetList.forEach((elem: any) => {
      // Find the item details in allContainerStock
      let matchedContainers = this.allContainerStock.filter(
        (container: any) =>
          container.containerItem.some(
            (item: any) => parseInt(item.GerCarpetNo) === elem.barcodeNo
          )
      );
      matchedContainers.forEach((container: any) => {
        container.containerItem.forEach((item: any) => {
          if (parseInt(item.GerCarpetNo) === elem.barcodeNo) {
            let date = this.customeService.convertDate(
              getChallanDetails.chooseAdate
            );
            const includeChallanDetails = this.billDetails.some(
              (a: any) => a.challanNo == getChallanDetails.challanNo
            );
debugger
            this.billDetails.push({
              challanNo: includeChallanDetails
                ? undefined
                : getChallanDetails.challanNo,
              challanDate: includeChallanDetails ? undefined : date,
              customer: includeChallanDetails
                ? undefined
                : getChallanDetails.retailerOutlet,
              carpetNo: item.GerCarpetNo,
              qualityDesign: item.QualityDesign,
              colour: item.Color,
              colourCode: item.CCode,
              qualityCode: item.QCode,
              size: elem.size ? elem.size : item.Size,
              area: elem.area ? elem.area : item.Area,
              evkPrice: elem.evkPrice ? elem.evkPrice : item.EvKPrice,
              amount:
                parseFloat(elem.evkPrice ? elem.evkPrice : item.EvKPrice) *
                parseFloat(elem.area ? elem.area : item.Area),
              invoiceNo: item.InvoiceNo,
              saleStatus: item.status,
            });
          }
        });
      });
    });
  });
debugger
  this.calculation(this.billDetails);
  console.log(this.billDetails);
  this.ngxLoader.stop();
}

totalCalculation = {
  totalAmount: 0,
  // totalArea: 0,
  // totalEvkPrice: 0,
  profit:0,
  grossAmt:0,
  gstAmt:0
};
 total=0;
 profit=0;
 gst=0;
calculation(data: any) {
  debugger
  data.forEach((element: any) => {
    this.total =this.total + parseFloat(element.area) * parseFloat(element.evkPrice)
  });
  this.profit = this.total/100*13;
    this.gst=( this.total +this.profit) /100*19;
    this.totalCalculation = {
      totalAmount:this.total,
      profit:this.profit,
      gstAmt:this.gst,
      grossAmt:this.total +this.profit+this.gst
    };
  console.log(this.totalCalculation);

  debugger;
}
isPrint:boolean=false;



setPageMargins() {
  const pages = document.querySelectorAll('.page');
  pages.forEach((page, index) => {
    if (index === 0) {
      debugger
      // No margin-top for the first page
      (page as HTMLElement).style.marginTop = '0';
      window.print();
    } else {
      // Margin-top for subsequent pages
      (page as HTMLElement).style.marginTop = '500px';
      window.print();
    }
  });
}

printPage() {
  this.setPageMargins()

}
}
