fieldset {
    font-family: sans-serif;
    border: 2px solid #1F497D;
    background: #ffffff;
    border-radius: 5px;
    padding: 5px;
  }
  
  fieldset legend {
    background: #ffffff;
    color: #000000;
    padding: 5px 10px ;
    font-size: 20px;
    border-radius: 5px;
    /* box-shadow: 0 0 0 5px #ddd; */
    margin-left: 20px;
  }
  
  legend {
    float: left;
    width: auto;
    padding: 0;
    margin-top: -32px;
    margin-bottom: 0.5rem;
    font-size: calc(1.275rem + .3vw);
    line-height: inherit;
  }
  .ex-width{
    width:100%;
  }
  .space{
    display: flex;
    justify-content:space-between;
  
  }
  
.mat-mdc-header-cell {
  background-color: #f5f5f5 !important;
  color: #1F497D;
  font-weight: bold;
  font-size: 14px;
}

.mat-mdc-row:nth-child(even) {
  background-color: #f9f9f9;
}




.mat-mdc-row:hover {
  background-color: #f0f0f0;
}

/* Add a subtle border between rows */
.mat-mdc-row {
  border-bottom: 1px solid #e0e0e0;
}



 
  .color:hover {
color: #003d66 !important;
transform: scale(1.1,1.1);
  }

  .mat-column-Size {
    text-align: center !important;
  }
  .mat-column-Quality{
    text-align: center !important;
  }

  /* Additional styles for material issue view */
  .mat-form-field {
    margin-bottom: 10px;
  }

  .mat-icon-button {
    margin: 0 2px;
  }

  .mat-icon-button[color="primary"] {
    color: #1976d2;
  }

  .mat-icon-button[color="warn"] {
    color: #f44336;
  }

  .filter-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
  }

  .clear-filters-btn {
    height: 56px;
    margin-top: 8px;
  }

  .mat-table {
    min-width: 1400px;
  }

  .mat-cell, .mat-header-cell {
    padding: 8px 12px;
  }

  .material-column {
    font-size: 12px;
    max-width: 120px;
    word-wrap: break-word;
  }

.search-width{
    width: 255px;
}
  .issues-width{
    width: 175px;
  }

  .fromDate-width{
    width: 190px;
  }

  .toDate-width{
    width: 190px;
  }
  .clearB-width{
    width: 150px;
  }

.clearB-width button:hover{
    background-color: #b71500;
    transform: scale(1.02,1.02);
  }


  /* Style for the paginator */
.mat-mdc-paginator {
  background-color: #f5f5f5;

}

.scroll-container{
    scroll-behavior: smooth !important;
    overflow-x: scroll !important;
    overflow-y: scroll !important;
  }
 
.scroll-container::-webkit-scrollbar {
    height: 9px !important;
    width: 9px !important;
  }
.scroll-container::-webkit-scrollbar-thumb {
    background: #808080;
  }