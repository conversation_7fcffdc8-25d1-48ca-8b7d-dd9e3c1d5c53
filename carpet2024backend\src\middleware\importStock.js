const ContainerReceived = require("../model/phase-1/container-rvc");

const slugify = require("slugify");
const readXlsxFile = require("read-excel-file/node");
const excel = require("exceljs");
const path = require("path");
const { count } = require("console");
const fs = require("fs");
const  ContainerData =  require('../model/phase-1/container-rvc')
const importExistingStock = async (req, res) => {
  try {
    const filePath = path.resolve(
      __dirname,
      `../../public/all-pdf/${req.file.filename}`
    );

    readXlsxFile(filePath).then(async (rows) => {
      // Skip header row
      rows.shift();


    //   const productsArray=[]
    
    //  // Promise.all().then(() => console.log("All updates complete")).catch(err => console.error("Error in updates:", err));
    
    //  let existingData = await ContainerData.find();
      
    //  rows.forEach((row) => {
    //   // Filter rows that do not match the GerCarpetNo
    //   let item = existingData[0].containerItem.find(i=>i.GerCarpetNo==row[0]);
    //   if(!item){
    //     productsArray.push({
    //       GerCarpetNo: row[0],
    //       Date: row[1],
    //       QualityDesign: row[2],
    //       Color: row[4],
    //       CCode: row[5],
    //       QCode: row[3],
    //       Size: row[6] + " X " + row[7],
    //       SCore: row[8],
    //       Area: row[9],
    //       Rate: row[10],  // Assuming you want the 10th index, update if necessary
    //       EvKPrice: row[12],
    //       Amount: row[11], // Assuming you want the 11th index, update if necessary
    //       InvoiceNo: row[13],
    //       // ImporterCode: 'O/B'
    //     });
    //     console.log(row)
    //   }else{
    //     console.log(row[0])
    //   }
    
      
    // });
    // console.log(productsArray);
    
      const products = rows.map((row) => ({

        GerCarpetNo: row[0],
        Date:row[1],
        QualityDesign: row[2],        
        Color: row[4],
        CCode: row[5],
        QCode: row[3],
        Size: row[6] + " X " + row[7],
        SCore: row[8],
        Area: row[9],
        Rate: row[""],
        EvKPrice: row[12],
        Amount: row[10],
        InvoiceNo: row[13],
        // ImporterCode:'O/B'
      }));
      //   let isExist = await productModal.findOne({ InvoiceNo: products[0].InvoiceNo, impoterName: products[0].impoterName });

      try {
        if (products.length > 0) {
          const insertedProducts = new ContainerReceived({
            impoterName: "O/B",
            expensesAmount: 0,
            totalArea: 0,
            espPrice: 0,
            blPdf: "",
            containerItem: products,
          });
          let _data = await insertedProducts.save();

          // console.log('Bulk data uploaded successfully:', insertedProducts);
          res.status(200).send({
            message: "Bulk data uploaded successfully",
            success: true,
            data: _data
          });
        } else {
          fs.unlink(filePath, (err) => {
            if (err) {
              console.error("Error deleting file:", err);
              return;
            }
            console.log("File deleted successfully");
          });
          res.status(200).send("something went wrong");
        }
      } catch (error) {
        // console.error('Error uploading bulk data:', error);
        res.status(500).send("Error uploading bulk data");
      }
    });
  } catch (error) {
    // console.error('Error:', error);
    res.status(500).send({
      message: "Could not upload the file: " + req.file.originalname,
    });
  }
};

const { CreateChallan } = require("../model/phase-2/createChallan");

// const inportExistingChallan = async (req, resp) => {
//     try {
//         const filePath = path.resolve(__dirname, `../../public/all-pdf/${req.file.filename}`);

//         let rows = await readXlsxFile(filePath);

//         // Skip header row
//         rows.shift();

//         for (const element of rows) {
//             const exCarpetList = rows
//                 .filter(row => row[0] === element[0])
//                 .map(elem => ({
//                     barcodeNo: elem[4],
//                     amount: elem[6],
//                     status: elem[3].toLowerCase()
//                 }));

//             const _challanData = new Challan({
//                 // group: '', // Replace with actual group value
//                 wholeseller: element[2],
//                 chooseAdate: element[1],
//                 RetailerName: element[5],
//                 challanNo: element[0],
//                 carpetList: exCarpetList
//             });

//             const _savedData = await _challanData.save();

//             if (_savedData) {
//                 for (const el of _savedData.carpetList) {
//                     await ContainerReceived.updateOne(
//                         { "containerItem.GerCarpetNo": el.barcodeNo },
//                         { $set: { "containerItem.$.status": el.status } }
//                     );
//                 }

//             }
//             rows = rows.filter(x => x[0] !== element[0])
//         }

//         resp.status(200).json({ success: true, msg: "Data imported successfully" });
//     } catch (error) {
//         resp.status(400).json({ success: false, msg: error.message });
//     }
// };
const inportExistingChallan = async (req, resp) => {
  try {
    const filePath = path.resolve(
      __dirname,
      `../../public/all-pdf/${req.file.filename}`
    );
    const rows = await readXlsxFile(filePath);
    // Skip header row
    rows.shift();

    const uniqueChallanNumbers = new Set(); // To store unique challan numbers

    for (const element of rows) {
      const challanNo = element[0];
      if (!uniqueChallanNumbers.has(challanNo)) {
        uniqueChallanNumbers.add(challanNo);

        const exCarpetList = rows
          .filter((row) => row[0] === challanNo)
          .map((elem) => ({
            barcodeNo: elem[4],
            amount: elem[6],
            status: elem[3].toLowerCase(),
          }));

        const _challanData = new CreateChallan({
          group: "", // Replace with actual group value
          wholeseller: element[2],
          chooseAdate: element[1],
          retailerOutlet: element[5],
          challanNo: challanNo,
          carpetList: exCarpetList,
        });

        await _challanData.save();

        // Update status in ContainerReceived collection
        for (const el of exCarpetList) {
          await ContainerReceived.updateOne(
            { "containerItem.GerCarpetNo": el.barcodeNo },
            { $set: { "containerItem.$.status": el.status } }
          );
        }
      }
    }

    resp.status(200).json({ success: true, msg: "Data imported successfully" });
  } catch (error) {
    resp.status(400).json({ success: false, msg: error.message });
  }
};

module.exports = { importExistingStock, inportExistingChallan };
