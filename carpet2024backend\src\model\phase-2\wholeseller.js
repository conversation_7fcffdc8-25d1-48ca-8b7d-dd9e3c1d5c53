const mongoose = require('mongoose');
//const bcrypt = require('bcrypt');

//const saltRounds = 10;

const wholeSellerSchema = new mongoose.Schema({
    customerCode: {
        type: String,
       // required: true
    },
    customerName: {
        type: String,
       // required: true
    },
    address: String,
    zipCode: String,
    country: String,
    contactNumber: String,
    email: {
        type: String,
        // required: true
    },
    bankDetails: {
        bankName: {
            type: String,
            // required: true
        },
        email: {
            type: String,
            // required: true
        },
        password: {
            type: String,
            // required: true
        },
        bankAddress: String,
        accountNumber: String,
        swiftCode: String,
        contactNumber: String
    }
}, { timestamps: true });

// Hash password before saving
// wholeSellerSchema.pre('save', async function(next) {
//     if (!this.isModified('bankDetails.password')) return next();
//     try {
//         const salt = await bcrypt.genSalt(saltRounds);
//         this.bankDetails.password = await bcrypt.hash(this.bankDetails.password, salt);
//         next();
//     } catch (error) {
//         next(error);
//     }
// });

// Method to compare passwords
// wholeSellerSchema.methods.comparePassword = async function(candidatePassword) {
//     try {
//         return await bcrypt.compare(candidatePassword, this.bankDetails.password);
//     } catch (error) {
//         throw new Error(error);
//     }
// };

const WholeSeller = mongoose.model('WholeSeller', wholeSellerSchema);

module.exports = WholeSeller;
