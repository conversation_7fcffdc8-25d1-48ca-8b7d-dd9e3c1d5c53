<div class="row">
  <div class="col-md-3 p">
    <mat-radio-group
      aria-label="Select an option"
      [(ngModel)]="selectedOption"
      (change)="onOptionChange()"
    >
      <mat-radio-button value="all">All</mat-radio-button>
      <mat-radio-button value="pending">Pending</mat-radio-button>
    </mat-radio-group>
  </div>
  <div class="col-md-2">
    <mat-form-field appearance="outline">
      <mat-label>Select Year</mat-label>
      <mat-select (valueChange)="getMonth($event)">
        <mat-option value="All">All</mat-option>
        <mat-option *ngFor="let year of years" [value]="year.year">{{
          year.year
        }}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div class="col-md-2">
    <mat-form-field appearance="outline">
      <mat-label>Select Month</mat-label>

      <mat-select (valueChange)="getMonthName($event)">
        <mat-option value="All">All</mat-option>
        <mat-option *ngFor="let month of months" [value]="month.id">{{
          month.month
        }}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <!-- <div class="col-md-3">
    <mat-form-field appearance="outline">
      <mat-label>Select Customer</mat-label>
      <mat-select >
        <mat-option *ngFor="let cust of customerList" [value]="cust">{{
          cust
        }}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div class="col-md-3">
    <mat-form-field appearance="outline">
      <mat-label>Select ChallanNo</mat-label>
      <mat-select >
        <mat-option *ngFor="let challanNo of challanNoList" [value]="challanNo">{{challanNo}}</mat-option>
      </mat-select>
    </mat-form-field>
  </div> -->
  <div class="col-md-2">
    <mat-form-field appearance="outline">
      <mat-label>Search</mat-label>
      <input
        matInput
        (keyup)="applyFilter($event)"
        placeholder="Search"
        #input
      />
    </mat-form-field>
  </div>
  <div class="col-md-3">
    <button type="button" mat-flat-button color="primary" (click)="logDataSourceData()" >
      <i class="fa fa-file-excel-o" aria-hidden="true"></i> Export To Excel
    </button>
    <!-- (click)="logDataSourceData()" -->
  </div>
  <div class="col-md-12">
    <table
      mat-table
      [dataSource]="dataSource"
      class="mat-elevation-z8"
      matSort
      style="width: max-content"
    >
      <!--- Note that these columns can be defined in any order.
            The actual rendered columns are set as a property on the row definition" -->

      <!-- Position Column -->
      <ng-container matColumnDef="id">
        <th mat-header-cell mat-sort-header *matHeaderCellDef>No.</th>
        <td mat-cell *matCellDef="let element">{{ element.index }}</td>
      </ng-container>

      <!-- Name Column -->
      <ng-container matColumnDef="challanNo">
        <th mat-header-cell mat-sort-header *matHeaderCellDef>Challan No</th>
        <td mat-cell *matCellDef="let element">{{ element.challanNo }}</td>
      </ng-container>

      <ng-container matColumnDef="retailerOutlet">
        <th mat-header-cell *matHeaderCellDef>Retailer Outlet</th>
        <td mat-cell *matCellDef="let element">{{ element.retailerOutlet }}</td>
      </ng-container>
      <!-- Weight Column -->
      <ng-container matColumnDef="date">
        <th mat-header-cell *matHeaderCellDef>Date</th>
        <td mat-cell *matCellDef="let element">{{ element.date }}</td>
      </ng-container>

      <ng-container matColumnDef="pcs">
        <th mat-header-cell *matHeaderCellDef>No. of Pcs</th>
        <td mat-cell *matCellDef="let element">{{ element.pcs }}</td>
      </ng-container>
      <ng-container matColumnDef="area">
        <th mat-header-cell *matHeaderCellDef>Area</th>
        <td mat-cell *matCellDef="let element">{{ element.area }}</td>
      </ng-container>
      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef>Amount</th>
        <td mat-cell *matCellDef="let element">{{ element.amount }}</td>
      </ng-container>

      <!-- Symbol Column -->
      <ng-container matColumnDef="wholeseller">
        <th mat-header-cell *matHeaderCellDef>Wholeseller</th>
        <td mat-cell *matCellDef="let element">{{ element.wholeseller }}</td>
      </ng-container>
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef>Action</th>
        <td mat-cell *matCellDef="let element">
          <a routerLink="../report-sale/{{ element.id }} challan"
            ><i class="fa fa-eye" aria-hidden="true"></i
          ></a>
          &nbsp;
          <a routerLink="../new-challan-sale/{{ element.id }}"
            ><i
              class="fa fa-pencil-square-o fa-edit"
              title="Edit Challan "
              aria-hidden="true"
            ></i
          ></a>
          &nbsp;
          <a (click)="deleteChallan(element.id)"
            ><i class="fa fa-trash-o" aria-hidden="true"></i
          ></a>
        </td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
    <mat-paginator
      [length]="100"
      [pageSize]="25"
      [pageSizeOptions]="[25, 100,150,200,250,300]"
      aria-label="Select page"
    >
    </mat-paginator>
  </div>
</div>
