/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},y={},g={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!g.call({1:2},1);y.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:g;var h,m,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),x=T("".slice),E=function(t){return x(A(t),8,-1)},C=E,F=P,R=function(t){if("Function"===C(t))return F(t)},M=a,k=E,D=Object,I=R("".split),L=M((function(){return!D("z").propertyIsEnumerable(0)}))?function(t){return"String"==k(t)?I(t,""):D(t)}:D,H=function(t){return null==t},N=H,_=TypeError,z=function(t){if(N(t))throw _("Can't call method on "+t);return t},G=L,q=z,B=function(t){return G(q(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},Y=W.all,J=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===Y}:function(t){return"function"==typeof t},K=J,Q=W.all,V=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:K(t)||t===Q}:function(t){return"object"==typeof t?null!==t:K(t)},X=i,$=J,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(X[t]):X[t]&&X[t][n]},nt=R({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(m=(h=at.split("."))[0]>0&&h[0]<4?1:+(h[0]+h[1])),!m&&et&&(!(h=et.match(/Edge\/(\d+)/))||h[1]>=74)&&(h=et.match(/Chrome\/(\d+)/))&&(m=+h[1]);var ct=m,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,yt=tt,gt=J,bt=nt,dt=Object,ht=pt?function(t){return"symbol"==typeof t}:function(t){var n=yt("Symbol");return gt(n)&&bt(n.prototype,dt(t))},mt=String,vt=J,St=function(t){try{return mt(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=H,Pt=p,Tt=J,At=V,xt=TypeError,Et={exports:{}},Ct=i,Ft=Object.defineProperty,Rt=function(t,n){try{Ft(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Mt=Rt,kt="__core-js_shared__",Dt=i[kt]||Mt(kt,{}),It=Dt;(Et.exports=function(t,n){return It[t]||(It[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Lt=z,Ht=Object,Nt=function(t){return Ht(Lt(t))},_t=Nt,zt=R({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return zt(_t(t),n)},qt=R,Bt=0,Ut=Math.random(),Wt=qt(1..toString),Yt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Bt+Ut,36)},Jt=i,Kt=Et.exports,Qt=Gt,Vt=Yt,Xt=st,$t=pt,Zt=Kt("wks"),tn=Jt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Vt,en=function(t){if(!Qt(Zt,t)||!Xt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Xt&&Qt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=V,an=ht,cn=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},yn=ht,gn=function(t){var n=pn(t,"string");return yn(n)?n:n+""},bn=V,dn=i.document,hn=bn(dn)&&bn(dn.createElement),mn=function(t){return hn?dn.createElement(t):{}},vn=!c&&!a((function(){return 7!=Object.defineProperty(mn("div"),"a",{get:function(){return 7}}).a})),Sn=c,wn=p,On=y,jn=v,Pn=B,Tn=gn,An=Gt,xn=vn,En=Object.getOwnPropertyDescriptor;u.f=Sn?En:function(t,n){if(t=Pn(t),n=Tn(n),xn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var Cn={},Fn=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Rn=V,Mn=String,kn=TypeError,Dn=function(t){if(Rn(t))return t;throw kn(Mn(t)+" is not an object")},In=c,Ln=vn,Hn=Fn,Nn=Dn,_n=gn,zn=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Un="configurable",Wn="writable";Cn.f=In?Hn?function(t,n,r){if(Nn(t),n=_n(n),Nn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(Nn(t),n=_n(n),Nn(r),Ln)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw zn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Yn=Cn,Jn=v,Kn=c?function(t,n,r){return Yn.f(t,n,Jn(1,r))}:function(t,n,r){return t[n]=r,t},Qn={exports:{}},Vn=c,Xn=Gt,$n=Function.prototype,Zn=Vn&&Object.getOwnPropertyDescriptor,tr=Xn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Vn||Vn&&Zn($n,"name").configurable)},rr=J,er=Dt,or=R(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=J,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,yr=Yt,gr=pr("keys"),br={},dr=sr,hr=i,mr=V,vr=Kn,Sr=Gt,wr=Dt,Or=function(t){return gr[t]||(gr[t]=yr(t))},jr=br,Pr="Object already initialized",Tr=hr.TypeError,Ar=hr.WeakMap;if(dr||wr.state){var xr=wr.state||(wr.state=new Ar);xr.get=xr.get,xr.has=xr.has,xr.set=xr.set,ir=function(t,n){if(xr.has(t))throw Tr(Pr);return n.facade=t,xr.set(t,n),n},ur=function(t){return xr.get(t)||{}},ar=function(t){return xr.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(Sr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return Sr(t,Er)?t[Er]:{}},ar=function(t){return Sr(t,Er)}}var Cr={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!mr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=a,Rr=J,Mr=Gt,kr=c,Dr=nr.CONFIGURABLE,Ir=cr,Lr=Cr.enforce,Hr=Cr.get,Nr=Object.defineProperty,_r=kr&&!Fr((function(){return 8!==Nr((function(){}),"length",{value:8}).length})),zr=String(String).split("String"),Gr=Qn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Mr(t,"name")||Dr&&t.name!==n)&&(kr?Nr(t,"name",{value:n,configurable:!0}):t.name=n),_r&&r&&Mr(r,"arity")&&t.length!==r.arity&&Nr(t,"length",{value:r.arity});try{r&&Mr(r,"constructor")&&r.constructor?kr&&Nr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Lr(t);return Mr(e,"source")||(e.source=zr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Rr(this)&&Hr(this).source||Ir(this)}),"toString");var qr=J,Br=Cn,Ur=Qn.exports,Wr=Rt,Yr={},Jr=Math.ceil,Kr=Math.floor,Qr=Math.trunc||function(t){var n=+t;return(n>0?Kr:Jr)(n)},Vr=function(t){var n=+t;return n!=n||0===n?0:Qr(n)},Xr=Vr,$r=Math.max,Zr=Math.min,te=Vr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Xr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=Gt,le=B,se=ce.indexOf,pe=br,ye=R([].push),ge=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ye(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ye(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Yr.f=Object.getOwnPropertyNames||function(t){return ge(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var he=tt,me=Yr,ve=de,Se=Dn,we=R([].concat),Oe=he("Reflect","ownKeys")||function(t){var n=me.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},je=Gt,Pe=Oe,Te=u,Ae=Cn,xe=a,Ee=J,Ce=/#|\.prototype\./,Fe=function(t,n){var r=Me[Re(t)];return r==De||r!=ke&&(Ee(n)?xe(n):!!n)},Re=Fe.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Me=Fe.data={},ke=Fe.NATIVE="N",De=Fe.POLYFILL="P",Ie=Fe,Le=i,He=u.f,Ne=Kn,_e=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},ze=Rt,Ge=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var a=e[u];je(t,a)||r&&je(r,a)||o(t,a,i(n,a))}},qe=Ie,Be=E,Ue=Array.isArray||function(t){return"Array"==Be(t)},We=TypeError,Ye=gn,Je=Cn,Ke=v,Qe={};Qe[en("toStringTag")]="z";var Ve="[object z]"===String(Qe),Xe=J,$e=E,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=R,eo=a,oo=J,io=Ve?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Xe(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),yo=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},go=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};go.sham=!0;var bo=!fo||eo((function(){var t;return yo(yo.call)||!yo(Object)||!yo((function(){t=!0}))||t}))?go:yo,ho=Ue,mo=bo,vo=V,So=en("species"),wo=Array,Oo=function(t){var n;return ho(t)&&(n=t.constructor,(mo(n)&&(n===wo||ho(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=a,Po=ct,To=en("species"),Ao=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?Le:f?Le[a]||ze(a,{}):(Le[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=He(r,e))&&u.value:r[e],!qe(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&Ne(i,"sham",!0),_e(r,e,i,t)}},xo=a,Eo=Ue,Co=V,Fo=Nt,Ro=ee,Mo=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},ko=function(t,n,r){var e=Ye(n);e in t?Je.f(t,e,Ke(0,r)):t[e]=r},Do=function(t,n){return new(Oo(t))(0===n?0:n)},Io=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=ct,Ho=en("isConcatSpreadable"),No=Lo>=51||!xo((function(){var t=[];return t[Ho]=!1,t.concat()[0]!==t})),_o=Io("concat"),zo=function(t){if(!Co(t))return!1;var n=t[Ho];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!No||!_o},{concat:function(t){var n,r,e,o,i,u=Fo(this),a=Do(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(zo(i=-1===n?u:arguments[n]))for(o=Ro(i),Mo(c+o),r=0;r<o;r++,c++)r in i&&ko(a,c,i[r]);else Mo(c+1),ko(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["tr-TR"]=r.default.fn.bootstrapTable.locales.tr={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Yükleniyor, lütfen bekleyin"},formatRecordsPerPage:function(t){return"Sayfa başına ".concat(t," kayıt.")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"".concat(r," kayıttan ").concat(t,"-").concat(n," arası gösteriliyor (filtered from ").concat(e," total rows)."):"".concat(r," kayıttan ").concat(t,"-").concat(n," arası gösteriliyor.")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Ara"},formatNoMatches:function(){return"Eşleşen kayıt bulunamadı."},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Yenile"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Sütunlar"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Tüm Satırlar"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["tr-TR"])}));
