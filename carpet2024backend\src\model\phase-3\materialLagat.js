const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const materialLagatSchema = new Schema({
  todate: {
    type: Date,
    required: true,
  },
  fromdate: {
    type: Date,
    required: true,
  },
  quality: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AddQuality',
  },
  AddDesign: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AddDesign',
  },
  Color: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Color',
  },
   katiDescription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RawMaterialGroup',
    required: true,
  },
   katiLagat: {
    type: String,
    required: true,
  },
  katiRatePerkg: {
    type: String,
    required: true,
  },
   tanaDescription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RawMaterialGroup',
    required: true,
  },
  tanaLagat: {
    type: String,
    required: true,
  },
  tanaRatePerkg: {
    type: String,
    required: true,
  },
   sootDescription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RawMaterialGroup',
    required: true,
  },
  sootLagat: {
    type: String,
    required: true,
  },
  sootRatePerkg: {
    type: String,
    required: true,
  },
  tharriDescription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RawMaterialGroup',
    required: true,
  },
  tharriLagat: {
    type: String,
    required: true,
  },
  tharriRatePerkg: {
    type: String,
    required: true,
  },
  silkDescription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RawMaterialGroup',
    required: true,
  },
  silkLagat: {
    type: String,
    required: true,
  },
  silkRatePerkg: {
    type: String,
    required: true,
  },
 
  item: [{
    description: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'RawMaterialGroup',
      required: true,
    },
    lagat: {
      type: String,
      required: true,
    },
    ratePerkg: {
      type: String,
      required: true,
    },
  }],
}, {
  timestamps: true,
});

const MaterialLagat = mongoose.model('MaterialLagat', materialLagatSchema);

module.exports = MaterialLagat;
