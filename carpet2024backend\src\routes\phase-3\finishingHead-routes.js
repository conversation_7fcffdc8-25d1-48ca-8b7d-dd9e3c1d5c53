const express = require('express');
const router = express.Router();
const finishingHeadController = require('../../controller/finishingHead-controller');

// Create a finishing head
router.post('/finishingHeads', finishingHeadController.createFinishingHead);

// Get all finishing heads
router.get('/finishingHeads', finishingHeadController.getAllFinishingHeads);

// Get a finishing head by ID
router.get('/finishingHeads/:id', finishingHeadController.getFinishingHeadById);

// Update a finishing head by ID
router.put('/finishingHeads/:id', finishingHeadController.updateFinishingHead);

// Delete a finishing head by ID
router.delete('/finishingHeads/:id', finishingHeadController.deleteFinishingHead);

module.exports = router;
