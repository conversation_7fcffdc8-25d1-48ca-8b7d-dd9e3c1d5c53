
const userRepository = require('../repositories/user-repository');

async function checkUserExistence(req, res, next) {
  try {
    const userId = req.params.id; 
    const user = await userRepository.getUserById(userId);

    if (user) {
      req.user = user;
      next();
    } else {
      res.status(404).json({ error: 'User not found' });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
// const jwt = require('jsonwebtoken');
// const userRepository = require('../repositories/user-repository');

// async function generateToken(user) {
//   return jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
// }

// async function verifyToken(token) {
//   return jwt.verify(token, process.env.JWT_SECRET);
// }

// async function checkUserExistence(req, res, next) {
//   try {
//     const userId = req.params.id; 
//     const user = await userRepository.getUserById(userId);

//     if (user) {
//       req.user = user;
//       next();
//     } else {
//       res.status(404).json({ error: 'User not found' });
//     }
//   } catch (error) {
//     console.error(error);
//     res.status(500).json({ error: 'Internal Server Error' });
//   }
// }

// async function authenticateUser(req, res, next) {
//   try {
//     const token = req.headers.authorization.split(' ')[1];
//     const decodedToken = await verifyToken(token);
//     req.userData = { userId: decodedToken.userId };
//     next();
//   } catch (error) {
//     console.error(error);
//     res.status(401).json({ error: 'Authentication failed' });
//   }
// }

// module.exports = {
//   generateToken,
//   verifyToken,
//   checkUserExistence,
//   authenticateUser,
// };

module.exports = {
  checkUserExistence,
};

