const express = require('express');
const router = express.Router();
const impoterDetailsController = require('../../controller/impoter-invoice-controller');

router.post('/create-impoter-invoice',  impoterDetailsController.createImpoterDetail);
router.get('/getAll-impoter-invoice', impoterDetailsController.getAllImpoterDetails);
router.get('/get-impoter-invoice/:id', impoterDetailsController.getImpoterDetailById);
router.put('/update-impoter-invoice/:id', impoterDetailsController.updateImpoterDetail);
router.delete('/delete-impoter-invoice/:id', impoterDetailsController.deleteImpoterDetail);

module.exports = router;
