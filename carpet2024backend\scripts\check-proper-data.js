// Simple script to check the restored proper data
const mongoose = require('mongoose');
const CarpetReceived = require('../src/model/phase-4/carpetReceived');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function main() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ Connected to MongoDB');
    
    // Get total count
    const totalCount = await CarpetReceived.countDocuments();
    console.log(`📊 Total carpet received records: ${totalCount}`);
    
    // Get sample records with all fields
    const samples = await CarpetReceived.find()
      .limit(10)
      .populate('weaverNumber', 'name weaverName')
      .populate({
        path: 'issueNo',
        populate: [
          { path: 'quality', select: 'quality' },
          { path: 'design', select: 'design' }
        ]
      })
      .lean();
    
    console.log('\n📋 Sample records with proper data:');
    samples.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver: ${record.weaverName || 'N/A'}`);
      console.log(`   Quality: ${record.quality || (record.issueNo?.quality?.quality) || 'N/A'}`);
      console.log(`   Design: ${record.design || (record.issueNo?.design?.design) || 'N/A'}`);
      console.log(`   Size: ${record.size || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ${record.amount || 'N/A'}`);
      console.log(`   Date: ${record.receivingDate?.toISOString()?.split('T')[0] || 'N/A'}`);
      console.log(`   Border Color: ${record.issueNo?.borderColour || 'N/A'}`);
      console.log('   ' + '-'.repeat(60));
    });
    
    // Check distinct values
    const distinctDesigns = await CarpetReceived.distinct('design');
    console.log(`\n🎨 Distinct Designs: ${distinctDesigns.filter(d => d).join(', ') || 'None found'}`);
    
    const distinctWeavers = await CarpetReceived.distinct('weaverName');
    console.log(`\n👥 Distinct Weavers: ${distinctWeavers.filter(w => w).join(', ') || 'None found'}`);
    
    const distinctQualities = await CarpetReceived.distinct('quality');
    console.log(`\n⭐ Distinct Qualities: ${distinctQualities.filter(q => q).join(', ') || 'None found'}`);
    
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

main();
