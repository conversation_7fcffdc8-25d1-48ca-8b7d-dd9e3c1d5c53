body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
.invoice-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    page-break-after: always;
}
.header,
.footer {
    text-align: center;
    margin-bottom: 20px;
}
.header img {
    max-width: 150px;
}
.info-table,
.details-table {
    width: 100%;
    margin-bottom: 20px;
}
.info-table td {
    vertical-align: top;
}
.info-table .left,
.info-table .right {
    width: 50%;
}
.details-table,
.totals-table {
    border-collapse: collapse;
}
.details-table th,
.details-table td,
.totals-table th,
.totals-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}
.details-table th {
    background-color: #f4f4f4;
}
.totals {
    text-align: right;
}
.totals td {
    padding-right: 10px;
}
.contact-info {
    text-align: center;
    font-size: 0.8em;
    margin-top: 20px;
} 


 
 