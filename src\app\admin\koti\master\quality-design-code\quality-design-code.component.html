<div class="px-3">

    <!-- Start Content-->
    <div class="container-fluid vh-100">

        <!-- start page title -->
        <div class="py-3 py-lg-4">
            <div class="row">
                <div class="col-lg-6">
                    <h4 class="page-title mb-0">Quality & Design</h4>
                </div>

            </div>
            <hr>

            <!-- Form row -->
            <div class="row">
                <div class="col-xl-12">
                    <div class="card">
                        <div class="card-body">



                            <form [formGroup]="frmQualityDesign" (ngSubmit)="addQualityDesign()">
                                <div class="row">
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline">
                                            <mat-label>Old Quality Design</mat-label>
                                            <mat-select formControlName="oldQualityandDesign">
                                                @for (qd of uniqueQDArray; track qd) {
                                                <mat-option [value]="qd">{{qd}}</mat-option>
                                                }
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>New Quality And Desgin</mat-label>
                                            <input matInput placeholder="New Quality And Desgin"
                                                formControlName="newQualityandDesign" />
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label> Quality And Desgin Code</mat-label>
                                            <input matInput placeholder="Quality And Desgin Code"
                                                formControlName="qualityCodeandDesign" />
                                        </mat-form-field>
                                    </div>
                                </div>






                                <div class="row">
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline">
                                            <mat-label>Old Colour</mat-label>
                                            <mat-select formControlName="oldColor">
                                                @for (colour of uniqueColoursArray; track colour) {
                                                <mat-option [value]="colour">{{colour}}</mat-option>
                                                }
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label> New color</mat-label>
                                            <input matInput placeholder="Old color" formControlName="newColor" />
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Color Code</mat-label>
                                            <input matInput placeholder="Color Code" formControlName="colorCode" />
                                        </mat-form-field>


                                    </div>
                                </div>




                                <button mat-flat-button color="primary">Save</button>
                                <button type="button" mat-flat-button color="danger" (click)="closeDialogWithData()">Close</button>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- end col -->


                <!-- end col -->
            </div>
            <!-- end row -->

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">

                            <mat-form-field>
                                <mat-label>Search</mat-label>
                                <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Rit" #input>
                            </mat-form-field>

                            <div class="mat-elevation-z8">
                                <table mat-table [dataSource]="dataSource" matSort>

                                    <!-- ID Column -->
                                    <ng-container matColumnDef="id">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Sr. No. </th>
                                        <td mat-cell *matCellDef="let row"> {{row.index}} </td>
                                    </ng-container>

                                    <!-- Name Column -->
                                    <ng-container matColumnDef="colorCode">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Color Code </th>
                                        <td mat-cell *matCellDef="let row"> {{row.colorCode}}</td>
                                    </ng-container>

                                    <!-- Position Column -->
                                    <ng-container matColumnDef="newColor">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>New Color</th>
                                        <td mat-cell *matCellDef="let row"> {{row.newColor}} </td>
                                    </ng-container>

                                    <!-- Office Column -->
                                    <ng-container matColumnDef="oldColor">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Old Color</th>
                                        <td mat-cell *matCellDef="let row"> {{row.oldColor}} </td>
                                    </ng-container>

                                    <!-- Age Column -->
                                    <ng-container matColumnDef="qualityCodeandDesign">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Quality Design Code </th>
                                        <td mat-cell *matCellDef="let row"> {{row.qualityCodeandDesign}} </td>
                                    </ng-container>

                                    <!-- Satart Date Column -->
                                    <ng-container matColumnDef="newQualityandDesign">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>New Quality Design</th>
                                        <td mat-cell *matCellDef="let row"> {{row.newQualityandDesign}} </td>
                                    </ng-container>

                                    <!-- Salary Column -->
                                    <ng-container matColumnDef="oldQualityandDesign">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Old Quality Design</th>
                                        <td mat-cell *matCellDef="let row"> {{row.oldQualityandDesign}} </td>
                                    </ng-container>
                                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                                    <!-- Row shown when there is no matching data. -->
                                    <tr class="mat-row" *matNoDataRow>
                                        <td class="mat-cell" colspan="4">No data matching the search "{{input.value}}"
                                        </td>
                                    </tr>
                                </table>

                                <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]"
                                    aria-label="Select page of users"></mat-paginator>
                            </div>

                        </div> <!-- end card body-->
                    </div> <!-- end card -->
                </div><!-- end col-->
            </div>
            <!-- end row-->


            <!-- end page title -->

        </div> <!-- container -->

    </div> <!-- content -->
</div>