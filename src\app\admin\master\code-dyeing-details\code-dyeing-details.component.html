<div class="container mt-4">
  <section>
    <fieldset>
      <legend>
        <b> {{ isUpdated ? "Update" : "Add" }} Colour Code Deying Details </b>
      </legend>
      <form [formGroup]="frmDyeingDetail" (ngSubmit)="addDyiengDetail()">
        <div class="row">
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Quality</mat-label>
              <mat-select
                formControlName="quality"
                (valueChange)="onQuality($event)"
              >
                <mat-option
                  *ngFor="let quality of qualityList"
                  value="{{ quality.id }}"
                >
                  {{ quality.quality }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmDyeingDetail.get('quality')?.invalid && (frmDyeingDetail.get('quality')?.dirty || frmDyeingDetail.get('quality')?.touched)">
              <div *ngIf="frmDyeingDetail.get('quality')?.errors?.['required']">
               <span class="errText"> Please select a quality type.</span>
              </div>

            </div>
          </div>
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Color</mat-label>
              <mat-select
                formControlName="Color"
                (valueChange)="onColor($event)"
              >
                <mat-option
                  *ngFor="let color of colorList"
                  value="{{ color._id }}"
                >
                  {{ color.newColor }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmDyeingDetail.get('Color')?.invalid && (frmDyeingDetail.get('Color')?.dirty || frmDyeingDetail.get('Color')?.touched)">
              <div *ngIf="frmDyeingDetail.get('Color')?.errors?.['required']">
               <span class="errText"> Please select a color type.</span>
              </div>

            </div>
          </div>
          <div class="mb-2 col-md-2">
          <mat-form-field appearance="outline">
            <mat-label>Comp Color Code</mat-label>
            <input matInput placeholder="Company Color Code" readonly formControlName="companyColorCode">

          </mat-form-field>
          <div class="erroDiv" *ngIf="frmDyeingDetail.get('companyColorCode')?.invalid && (frmDyeingDetail.get('companyColorCode')?.dirty || frmDyeingDetail.get('companyColorCode')?.touched)">
            <div *ngIf="frmDyeingDetail.get('companyColorCode')?.errors?.['required']">
             <span class="errText"> Please select a color type.</span>
            </div>

          </div>
          </div>

          <!-- <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Colour Code</mat-label>
              <input
                matInput
                placeholder="Colour Code"
                [value]="color"
                formControlName="colourcode"
                [(colorPicker)]="color"
              />
            </mat-form-field>
          </div> -->
          <div class="mb-2 col-md-1">
            <input
              class="Clr"
              [style.background]="color"
            />
          </div>
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Material</mat-label>
              <mat-select
                formControlName="material"
                (valueChange)="onItem($event)"
              >
                <mat-option
                  *ngFor="let group of groupList"
                  value="{{ group._id }}"
                >
                  {{ group.Group }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmDyeingDetail.get('material')?.invalid && (frmDyeingDetail.get('material')?.dirty || frmDyeingDetail.get('material')?.touched)">
              <div *ngIf="frmDyeingDetail.get('material')?.errors?.['required']">
               <span class="errText"> Please select a material type.</span>
              </div>

            </div>
          </div>
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Item</mat-label>
              <mat-select
                formControlName="item"
                (valueChange)="onCount($event)"
              >
                <mat-option
                  *ngFor="let item of itemList"
                  value="{{ item.Item }}"
                >
                  {{ item.Item }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmDyeingDetail.get('item')?.invalid && (frmDyeingDetail.get('item')?.dirty || frmDyeingDetail.get('item')?.touched)">
              <div *ngIf="frmDyeingDetail.get('item')?.errors?.['required']">
               <span class="errText"> Please select a item type.</span>
              </div>

            </div>
          </div>
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Cout</mat-label>
              <mat-select
                formControlName="count"
                (valueChange)="selectCount($event)"
              >
                <mat-option
                  *ngFor="let count of countList"
                  value="{{ count.Count }}"
                >
                  {{ count.Count }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmDyeingDetail.get('count')?.invalid && (frmDyeingDetail.get('count')?.dirty || frmDyeingDetail.get('count')?.touched)">
              <div *ngIf="frmDyeingDetail.get('count')?.errors?.['required']">
               <span class="errText"> Please select a count type.</span>
              </div>

            </div>
          </div>
          <!-- <div class="mt-2 col-md-3">
            <button
              *ngIf="!isUpdated; else updateBtn"
              mat-flat-button
              color="primary"
            >
              Save
            </button>

            <ng-template #updateBtn>
              <button
                mat-flat-button
                type="button"
                color="primary"
                (click)="update()"
              >
                Update
              </button>
            </ng-template>
          </div> -->
        </div>
        <div class="line-with-text">
          <span>Add Multiple Recipe</span>
        </div>

        <div formArrayName="chemicals" class="frmarr">
          <div
            class="row"
            *ngFor="let recipe of chemicals.controls; let i = index"
            [formGroupName]="i"
          >
            <div class="col-md-2">
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>chemical</mat-label>
                <input
                  matInput
                  placeholder="chemical"
                  formControlName="chemical"
                />
              </mat-form-field>
            </div>

            <div class="col-md-1">
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>weight</mat-label>
                <input
                  matInput
                  placeholder="weight1"
                  formControlName="weight"
                />
              </mat-form-field>
            </div>

            <div class="col-md-2">
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>vendorName</mat-label>
                <input
                  matInput
                  placeholder="vendorName1"
                  formControlName="vendorName"
                />
              </mat-form-field>
            </div>

            <div class="col-md-2">
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>dyesName</mat-label>
                <input
                  matInput
                  placeholder="dyesName"
                  formControlName="dyesName"
                />
              </mat-form-field>
            </div>

            <div class="col-md-1">
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>weight</mat-label>
                <input
                  matInput
                  placeholder="weight2"
                  formControlName="weight2"
                />
              </mat-form-field>
            </div>

            <div class="col-md-2">
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>vendorName</mat-label>
                <input
                  matInput
                  placeholder="vendorName2"
                  formControlName="vendorName2"
                />
              </mat-form-field>
            </div>

            <div class="col-md-1">
              <button
                mat-flat-button
                type="button"
                color="primary"
                (click)="removeRecipe(i)"
              >
                Remove
              </button>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <button
            mat-flat-button
            type="button"
            color="primary"
            (click)="addRecipe()" [disabled]="!frmDyeingDetail.valid"
          >
            Add
          </button>
          &nbsp;
          <button
            *ngIf="!isUpdated; else updateBtn"
            mat-flat-button
            color="primary"  [disabled]="!frmDyeingDetail.valid"
          >
            Save
          </button>

          <ng-template #updateBtn>
            <button
              mat-flat-button
              type="button"
              color="primary"
              (click)="update()" [disabled]="!frmDyeingDetail.valid"
            >
              Update
            </button>
          </ng-template>
        </div>
      </form>
    </fieldset>
  </section>
</div>

<div class="container mt-5">
  <section>
    <fieldset style="min-height: 200px">
      <legend><b>List</b></legend>

      <div class="row">
        <div class="mb-2 col-12">
          <mat-form-field appearance="outline">
            <mat-label>Search</mat-label>
            <input
              matInput
              (keyup)="applyFilter($event)"
              placeholder="Ex. Jack"
              #input
            />
          </mat-form-field>

          <div class="mat-elevation-z8" style="overflow: auto;">
            <table mat-table [dataSource]="dataSource" class="fixed-border-table" matSort style="width: max-content;">
              <!-- Sr. No. Column -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Sr. No.
                </th>
                <td mat-cell *matCellDef="let row">{{ row.index }}</td>
              </ng-container>

              <!-- Quality Column -->
              <ng-container matColumnDef="quality">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Quality
                </th>
                <td mat-cell *matCellDef="let row">{{ row.quality }}</td>
              </ng-container>

              <!-- Colour Column -->
              <ng-container matColumnDef="colour">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Colour
                </th>
                <td mat-cell *matCellDef="let row">{{ row.colour }}</td>
              </ng-container>
              <ng-container matColumnDef="companyColorCode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Comp Color Code
                </th>
                <td mat-cell *matCellDef="let row">
                  <span
                    class="color-box"
                    [style.background-color]="row.colourCode"
                  ></span>
                  {{ row.companyColorCode }}</td>
              </ng-container>

              <!-- Colour Code Column -->
              <!-- <ng-container matColumnDef="colourCode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Colour Code
                </th>
                <td mat-cell *matCellDef="let row">
                  <span
                    class="color-box"
                    [style.background-color]="row.colourCode"
                  ></span>
                  {{ row.colourCode }}
                </td>
              </ng-container> -->

              <!-- Material Column -->
              <ng-container matColumnDef="material">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Material
                </th>
                <td mat-cell *matCellDef="let row">{{ row.material }}</td>
              </ng-container>
              <!-- Item Column -->
              <ng-container matColumnDef="item">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Item</th>
                <td mat-cell *matCellDef="let row">{{ row.item }}</td>
              </ng-container>
              <!-- Count Column -->
              <ng-container matColumnDef="count">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Count</th>
                <td mat-cell *matCellDef="let row">{{ row.count }}</td>
              </ng-container>

              <!-- Action Column -->
              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Action
                </th>
                <td mat-cell *matCellDef="let row">
                  <a
                    (click)="view(row.id)"
                    data-bs-toggle="modal"
                    data-bs-target="#addControlModal"
                  >
                    <i class="fa fa-list" aria-hidden="true"></i>
                  </a>
&nbsp;
                  <a (click)="edit(row.id)"
                    ><i
                      class="fa fa-pencil-square-o fa-edit"
                      title="Edit purchase details "
                      aria-hidden="true"
                    ></i
                  ></a>
                  &nbsp;
                  <a (click)="delete(row.id)"
                    ><i class="fa fa-trash-o" aria-hidden="true"></i
                  ></a>
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>

              <!-- Row shown when there is no matching data. -->
              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="4">
                  No data matching the filter "{{ input.value }}"
                </td>
              </tr>
            </table>

            <mat-paginator
              [pageSizeOptions]="[5, 10, 25, 100]"
              aria-label="Select page of users"
            ></mat-paginator>
          </div>
        </div>
      </div>
    </fieldset>
  </section>
</div>

<div
  class="modal fade"
  id="addControlModal"
  tabindex="-1"
  aria-labelledby="addControlModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content" style="width: max-content;">
      <div class="modal-header">
        <h4 class="modal-title" id="addControlModalLabel">Recipe List</h4>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <table class="table fixed-border-table">
          <thead class="thead-dark">
            <tr>
              <th scope="col">Sr. No.</th>
              <th scope="col">chemical</th>
              <th scope="col">weight</th>
              <th scope="col">vendorName</th>
              <th scope="col">dyesName</th>
              <th scope="col">weight</th>
              <th scope="col">vendorName</th>
            </tr>
          </thead>
          <tbody>
           <tr *ngFor="let item of viewData; let i =index">
            <td>{{i+1}}</td>
            <td>{{item.chemical}}</td>
            <td>{{item.weight}}</td>
            <td>{{item.vendorName}}</td>
            <td>{{item.dyesName}}</td>
            <td>{{item.weight2}}</td>
            <td>{{item.vendorName2}}</td>
           </tr>
          </tbody>
        </table>


      </div>
    </div>
  </div>
</div>
