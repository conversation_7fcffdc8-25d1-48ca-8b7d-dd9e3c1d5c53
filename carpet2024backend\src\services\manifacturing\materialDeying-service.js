// services/materialDeyingService.js
const materialDeyingRepository = require('../../repositories/manifacturing/materialDeying-repository');

class MaterialDeyingService {
    
  async create(data) {
    return await materialDeyingRepository.create(data);
  }

  async findById(id) {
    return await materialDeyingRepository.findById(id);
  }

  async findAll() {
    return await materialDeyingRepository.findAll();
  }

  async update(id, data) {
    return await materialDeyingRepository.update(id, data);
  }

  async delete(id) {
    return await materialDeyingRepository.delete(id);
  }
}

module.exports = new MaterialDeyingService();
