// services/sizeService.js
const sizeRepository = require('../repositories/sizeMaster-repository');

class SizeService {
  async createSize(sizeData) {
    // Add any business logic here
    return await sizeRepository.create(sizeData);
  }

  async getSizeById(id) {
    return await sizeRepository.findById(id);
  }

  async getAllSizes() {
    return await sizeRepository.getAllSizes();
  }

  async updateSize(id, sizeData) {
    return await sizeRepository.updateSize(id, sizeData);
  }

  async deleteSize(Id) {
    return await sizeRepository.deleteSize(Id);
  }
}

module.exports = new SizeService();
