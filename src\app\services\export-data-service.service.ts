import { Injectable } from '@angular/core';
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';
import * as _  from 'lodash';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';
@Injectable({
  providedIn: 'root'
})
export class ExportDataServiceService {

  constructor() { }

  public exportAsExcelFile(json: any[], excelFileName: string): void {

    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(json);
    console.log('worksheet',worksheet);
    const workbook: XLSX.WorkBook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    //const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
    this.saveAsExcelFile(excelBuffer, excelFileName);
  }

  private saveAsExcelFile(buffer: any, fileName: string): void { 
    const data: Blob = new Blob([buffer], {
      type: EXCEL_TYPE
    });
    debugger
    FileSaver.default(data, fileName + '_export_' + new Date().getTime() + EXCEL_EXTENSION);
  }


  

  exportBillExcelFile(excelData: any[]): void {
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Current Page Data');

    // Apply bold formatting and border to the total row
    const totalRowIndex = excelData.length; // Index of the total row
    const rangeRef = worksheet['!ref'];
    if (rangeRef) {
      const range = XLSX.utils.decode_range(rangeRef);
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellAddress = { c: C, r: totalRowIndex - 1 }; // 0-based index
        const cellRef = XLSX.utils.encode_cell(cellAddress);
        if (!worksheet[cellRef]) continue;
        if (!worksheet[cellRef].s) worksheet[cellRef].s = {};

        // Apply font style
        worksheet[cellRef].s.font = {
          bold: true,
          name: 'Arial',
          sz: 12,
          color: { rgb: 'FF0000' } // Optional: Change the color to red
        };

        // Apply border style
        worksheet[cellRef].s.border = {
          top: { style: 'medium', color: { rgb: '000000' } },
          bottom: { style: 'medium', color: { rgb: '000000' } },
          left: { style: 'medium', color: { rgb: '000000' } },
          right: { style: 'medium', color: { rgb: '000000' } }
        };
      }
    }

    XLSX.writeFile(workbook, 'CurrentPageData.xlsx');
  }


}
