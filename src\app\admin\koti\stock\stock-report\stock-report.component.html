<section class="mt-3">
  <div class="container">
    <div class="row">
      <fieldset>
        <legend><b>Stock</b></legend>
        <div class="col-md-12">
          <div style="overflow-y: auto;overflow-x: auto">
            <table
              mat-table
              [dataSource]="dataSource"
              class="mat-elevation-z8"
              style="width: max-content"
            >
              <ng-container matColumnDef="index">
                <th mat-header-cell *matHeaderCellDef>No.</th>
                <td mat-cell *matCellDef="let element; let i = index">
                  {{ i + 1 }}
                </td>
              </ng-container>

              <ng-container matColumnDef="Amount">
                <th mat-header-cell *matHeaderCellDef>Amount</th>
                <td mat-cell *matCellDef="let element">{{ element.Amount }}</td>
              </ng-container>

              <ng-container matColumnDef="Area">
                <th mat-header-cell *matHeaderCellDef>Area</th>
                <td mat-cell *matCellDef="let element">{{ element.Area }}</td>
              </ng-container>

              <ng-container matColumnDef="BillChooseAdate">
                <th mat-header-cell *matHeaderCellDef>BillChooseAdate</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.BillChooseAdate }}
                </td>
              </ng-container>

              <ng-container matColumnDef="BillCustomer">
                <th mat-header-cell *matHeaderCellDef>BillCustomer</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.BillCustomer }}
                </td>
              </ng-container>

              <ng-container matColumnDef="BillNo">
                <th mat-header-cell *matHeaderCellDef>BillNo</th>
                <td mat-cell *matCellDef="let element">{{ element.BillNo }}</td>
              </ng-container>

              <ng-container matColumnDef="CCode">
                <th mat-header-cell *matHeaderCellDef>CCode</th>
                <td mat-cell *matCellDef="let element">{{ element.CCode }}</td>
              </ng-container>

              <ng-container matColumnDef="ChallanNo">
                <th mat-header-cell *matHeaderCellDef>ChallanNo</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.ChallanNo }}
                </td>
              </ng-container>

              <ng-container matColumnDef="ChooseAdate">
                <th mat-header-cell *matHeaderCellDef>ChooseAdate</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.ChooseAdate }}
                </td>
              </ng-container>

              <ng-container matColumnDef="Color">
                <th mat-header-cell *matHeaderCellDef>Color</th>
                <td mat-cell *matCellDef="let element">{{ element.Color }}</td>
              </ng-container>

              <ng-container matColumnDef="ContainerDate">
                <th mat-header-cell *matHeaderCellDef>ContainerDate</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.ContainerDate }}
                </td>
              </ng-container>

              <ng-container matColumnDef="ContainerStatus">
                <th mat-header-cell *matHeaderCellDef>ContainerStatus</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.ContainerStatus }}
                </td>
              </ng-container>

              <ng-container matColumnDef="EvKPrice">
                <th mat-header-cell *matHeaderCellDef>EvKPrice</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.EvKPrice }}
                </td>
              </ng-container>

              <ng-container matColumnDef="GerCarpetNo">
                <th mat-header-cell *matHeaderCellDef>GerCarpetNo</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.GerCarpetNo }}
                </td>
              </ng-container>

              <ng-container matColumnDef="InvoiceNo">
                <th mat-header-cell *matHeaderCellDef>InvoiceNo</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.InvoiceNo }}
                </td>
              </ng-container>

              <ng-container matColumnDef="IsBillDeleted">
                <th mat-header-cell *matHeaderCellDef>IsBillDeleted</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.IsBillDeleted }}
                </td>
              </ng-container>

              <ng-container matColumnDef="MatchedBarcodeNo">
                <th mat-header-cell *matHeaderCellDef>MatchedBarcodeNo</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.MatchedBarcodeNo }}
                </td>
              </ng-container>

              <ng-container matColumnDef="MatchedStatus">
                <th mat-header-cell *matHeaderCellDef>MatchedStatus</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.MatchedStatus }}
                </td>
              </ng-container>

              <ng-container matColumnDef="QCode">
                <th mat-header-cell *matHeaderCellDef>QCode</th>
                <td mat-cell *matCellDef="let element">{{ element.QCode }}</td>
              </ng-container>

              <ng-container matColumnDef="QualityDesign">
                <th mat-header-cell *matHeaderCellDef>QualityDesign</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.QualityDesign }}
                </td>
              </ng-container>

              <ng-container matColumnDef="RetailerOutlet">
                <th mat-header-cell *matHeaderCellDef>RetailerOutlet</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.RetailerOutlet }}
                </td>
              </ng-container>

              <ng-container matColumnDef="SCore">
                <th mat-header-cell *matHeaderCellDef>SCore</th>
                <td mat-cell *matCellDef="let element">{{ element.SCore }}</td>
              </ng-container>

              <ng-container matColumnDef="Size">
                <th mat-header-cell *matHeaderCellDef>Size</th>
                <td mat-cell *matCellDef="let element">{{ element.Size }}</td>
              </ng-container>

              <ng-container matColumnDef="Wholeseller">
                <th mat-header-cell *matHeaderCellDef>Wholeseller</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.Wholeseller }}
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            </table>
          </div>
          <mat-paginator
          #paginator
            [length]="pagination.totalResults"
            [pageSize]="pagination.pageSize"
            [pageSizeOptions]="[10, 25, 50,100]"
            (page)="pageChanged($event)"
            showFirstLastButtons
          >
          </mat-paginator>

          <!-- <mat-paginator
      #paginator
      (page)="pageChanged($event)"
      [length]="pagination.totalResults"
      [pageSize]="pagination.pageSize"
      [pageIndex]="pagination.currentPage"
      [pageSizeOptions]="[5, 10, 20]"
      showFirstLastButtons
      aria-label="Select page of data">
</mat-paginator> -->
        </div>
      </fieldset>
    </div>
  </div>
</section>
