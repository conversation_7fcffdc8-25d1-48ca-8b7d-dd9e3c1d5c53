const express = require('express');
const colorController = require('../../controller/color-controller');
const colorMiddleware = require('../../middleware/color-middleware');

const router = express.Router();

router.post('/create-color', colorMiddleware.validateColorData, colorController.createColor); //done
router.get('/get-all-colors', colorController.getAllColors); //done
router.get('/get-color/:id', colorController.getColorById); //done
router.put('/update-color/:id', colorMiddleware.validateColorData, colorController.updateColor); //done
router.delete('/delete-color/:id', colorController.deleteColor); //done

module.exports = router;
