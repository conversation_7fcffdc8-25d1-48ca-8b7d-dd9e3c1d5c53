/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

.bootstrap-table .table>tbody>tr.group-by.collapsed,.bootstrap-table .table>tbody>tr.group-by.expanded{cursor:pointer}.bootstrap-table .table>tbody>tr.hidden{display:none}