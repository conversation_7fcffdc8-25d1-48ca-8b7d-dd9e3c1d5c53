const mongoose = require('mongoose');

const mapMasterSchema = new mongoose.Schema({
  
 quality: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'AddQuality'
  },
  addDesign: {
   // type:String,
   // required:true
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AddDesign'
  },

 groundColour:{
   type: mongoose.Schema.Types.ObjectId,
    ref: 'Color'
 },
 borderColour:{
   type: mongoose.Schema.Types.ObjectId,
    ref: 'Color'
 },
 sizeMaster:{
   type: mongoose.Schema.Types.ObjectId,
    ref: 'SizeMaster'
 },
 uploadedFile :{
    type: String, // or you can use Buffer for binary data, but String is typical for file paths/URLs
    //required: true
  }
  });
    
  const mapMaster = mongoose.model('mapMaster', mapMasterSchema);
  
  module.exports = mapMaster;
  