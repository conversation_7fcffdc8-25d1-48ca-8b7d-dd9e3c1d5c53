const BillForWholeseller = require("../model/phase-2/billWholeseller");

const checkDuplicateBillNo = async (req, res, next) => {
  try {
    const { billNo } = req.body;
    
    if (!billNo) {
      return res.status(400).json({ message: "billNo is required" });
    }

    const existingBill = await BillForWholeseller.findOne({ billNo });

    if (existingBill) {
      return res.status(409).json({ message: "Bill number already exists" });
    }

    next();
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = checkDuplicateBillNo;
