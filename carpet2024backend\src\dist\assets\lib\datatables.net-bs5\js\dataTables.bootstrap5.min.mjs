/*! DataTables Bootstrap 5 integration
 * 2020 SpryMedia Ltd - datatables.net/license
 */
import $ from"jquery";import DataTable from"datatables.net";$.extend(!0,DataTable.defaults,{dom:"<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>><'row dt-row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",renderer:"bootstrap"}),$.extend(DataTable.ext.classes,{sWrapper:"dataTables_wrapper dt-bootstrap5",sFilterInput:"form-control form-control-sm",sLengthSelect:"form-select form-select-sm",sProcessing:"dataTables_processing card",sPageButton:"paginate_button page-item"}),DataTable.ext.renderer.pageButton.bootstrap=function(o,a,d,e,n,c){function p(a,e){function t(a){a.preventDefault(),$(a.currentTarget).hasClass("disabled")||g.page()==a.data.action||g.page(a.data.action).draw("page")}for(var l,r,s=0,i=e.length;s<i;s++)if(l=e[s],Array.isArray(l))p(a,l);else{switch(u=b="",l){case"ellipsis":b="&#x2026;",u="disabled";break;case"first":b=f.sFirst,u=l+(0<n?"":" disabled");break;case"previous":b=f.sPrevious,u=l+(0<n?"":" disabled");break;case"next":b=f.sNext,u=l+(n<c-1?"":" disabled");break;case"last":b=f.sLast,u=l+(n<c-1?"":" disabled");break;default:b=l+1,u=n===l?"active":""}b&&(r=-1!==u.indexOf("disabled"),r=$("<li>",{class:m.sPageButton+" "+u,id:0===d&&"string"==typeof l?o.sTableId+"_"+l:null}).append($("<a>",{href:r?null:"#","aria-controls":o.sTableId,"aria-disabled":r?"true":null,"aria-label":x[l],"aria-role":"link","aria-current":"active"===u?"page":null,"data-dt-idx":l,tabindex:o.iTabIndex,class:"page-link"}).html(b)).appendTo(a),o.oApi._fnBindAction(r,{action:l},t))}}var b,u,t,g=new DataTable.Api(o),m=o.oClasses,f=o.oLanguage.oPaginate,x=o.oLanguage.oAria.paginate||{},a=$(a);try{t=a.find(document.activeElement).data("dt-idx")}catch(a){}var l=a.children("ul.pagination");l.length?l.empty():l=a.html("<ul/>").children("ul").addClass("pagination"),p(l,e),void 0!==t&&a.find("[data-dt-idx="+t+"]").trigger("focus")};export default DataTable;