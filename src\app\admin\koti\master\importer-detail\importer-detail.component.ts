import { Component, OnInit } from '@angular/core';

import { MatTableDataSource, } from '@angular/material/table';
import { AfterViewInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';

import { ErrorStateMatcher } from '@angular/material/core';
import { FormBuilder, FormControl, FormGroup, FormGroupDirective, NgForm, Validators } from '@angular/forms';
import { ImporterService } from '../../../../services/importer.service';
import { response } from 'express';
import Swal from 'sweetalert2';



export class MyErrorStateMatcher implements ErrorStateMatcher {
  isErrorState(control: FormControl | null, form: FormGroupDirective | NgForm | null): boolean {
    const isSubmitted = form && form.submitted;
    return !!(control && control.invalid && (control.dirty || control.touched || isSubmitted));
  }
}



export interface UserData {
  //  id: string;
  // name: string;
  // progress: string;
  // fruit: string;
  index: number;
  id: string;
  customerName: string;
  customerCode: string;
  email: string;
  password: string;
  // street: string;
  // city: string;
  address: string;
  // state: string;
  zipCode: string;
  country: string;
  contactNo:string;
  accountNumber: string;
  bankName: string;
  // branch: string;
  swiftCode: string;
  ifscCode:string;

  bankEmail: string; bankAddress: string;
  // bankContactNo: string;
   
  bankZipCode: string;
  gstnNo: string;

}


const ELEMENT_DATA: UserData[] = [
//   'id' ,
//    'customerName', 
//    , 'customerCode' , 'phoneNo' , 'email' , 'password' ,
//   'zipCode',
//   'country',
//   'accountNumber',
//   'bankName',
//   'bankAddress',
//   'bankZipCode',
//   'ifscCode',
//   'bankEmail',
//   'bankContactNo',
//   'gstnNo',
//   'street',
// 'city',
// 'branch',
// 'state'

];
@Component({
  selector: 'app-importer-detail',
  templateUrl: './importer-detail.component.html',
  styleUrl: './importer-detail.component.css'
})


export class ImporterDetailComponent implements AfterViewInit, OnInit {

  frmImporterDetail!: FormGroup
  hide = true;
  getsData:any;



  ngOnInit(): void {

    this.frmImporterDetail = this.frmBuilder.group({
      customerName: [],
      customerCode: [],
      address: [],
      country: [],
      zipCode: [],
      contactNo: [],
      email: [],
      password: [],
      accountNumber: [],
      bankAddress: [],
      bankName: [],
      bankEmail: [],
      // bankContactNo: [],
      swiftCode: [],
      ifscCode:[],
      gstnNo: [],
      bankZipCode: [],
      countryOfBelonging: [],
      // branch: []
    });
    this.getAllImporter()
    // this.dataSource = new MatTableDataSource(ELEMENT_DATA);
  }

  emailFormControl = new FormControl('', [Validators.required, Validators.email]);

  matcher = new MyErrorStateMatcher();

  selected = 'option2';
  selected2 = 'option2';
  countrys = 'option2';



  foods: any[] = ['Germany'  ];



  displayedColumns: any[] = [
    'id',
    'customerName',
    'customerCode',
    'contactNo',
    'email',
    'password',
    'address',
    'zipCode',
    'country',
    'accountNumber',
    'bankName',
    'bankAddress',
    'bankZipCode',
    'swiftCode',
    'ifscCode',
    'bankEmail',
    // 'bankContactNo',
    'gstnNo',
    'action'
  ];
  dataSource = new MatTableDataSource(ELEMENT_DATA);

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  receiveData: any


  constructor(private frmBuilder: FormBuilder, private importerService: ImporterService) {
    this.receiveData = this.importerService.getData()

    // Create 100 users
    // const users = Array.from({ length: 100 }, (_, k) => createNewUser(k + 1));

    // Assign the data to the data source for the table to render

  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addOnPriceData: any

  getData() {
    this.addOnPriceData = this.importerService.getData();
    console.log(this.addOnPriceData)
  }

  importerDetail() {
     
    this.getData();
    let frmData = this.frmImporterDetail.value
    let address = frmData.address.split(',');
    let importerData = {
      address: {
        customerName: frmData.customerName,
        customerCode: frmData.customerCode,
        email: frmData.email,
        password: frmData.password,
        street: address[0],
        city: address[1],
        state: address[2],
        // street: frmData.street,
        // city:frmData.city,
        // state:frmData.state,
        zipCode: frmData.zipCode,
        country: frmData.country,
        contactNo:frmData.contactNo
      },
      bankData: {
        accountNumber: frmData.accountNumber,
        bankName: frmData.bankName,
        // branch: frmData.branch,
        swiftCode: frmData.swiftCode,
        ifscCode:frmData.ifscCode,
        bankEmail: frmData.bankEmail,
        bankAddress:frmData.bankAddress,
        bankZipCode:frmData.bankZipCode
        // bankContactNo: frmData.bankContactNo

      },
      // addOnPrice: {
      //   toDate: this.addOnPriceData.toDate,
      //   fromDate: this.addOnPriceData.fromDate,
      //   quality: this.addOnPriceData.quality,
      //   design: this.addOnPriceData.design,
      //   orderPrice: this.addOnPriceData.orderPrice
      // },
      // phoneNo: frmData.contactNo,
      gstnNo: frmData.gstnNo,
      // countryOfBelonging: frmData.countryOfBelonging
    }
    console.log(importerData)

    this.importerService.createImporter(importerData).subscribe((res: any) => {
      
      Swal.fire(
        'Success!',
        'Data has been saved successfully',
        'success'
      )
      console.log(res)
    })


  


  }

  getAllImporter(){
    debugger
    this.importerService.getAllImporter().subscribe((res:any)=>{
      this.getsData = res 
      // console.log(this.getsData)
      
       ELEMENT_DATA.length = 0;
       res.map((x:any,ind:number)=>{
        debugger
        let val = x.impoter;

      ELEMENT_DATA.push(
        {
          index: ind + 1,
          id: val._id,
          customerName: val.address.customerName,
          customerCode: val.address.customerCode,

          country: val.address.country,
          zipCode: val.address.zipCode,
          email: val.address.email,
          password: val.address.password,
          // street: ,
          address:val.address.street+','+val.address.city+','+ val.address.state+',', 
                   accountNumber: val.bankData.accountNumber,
          bankName: val.bankData.bankName,
          // branch: val.bankData.branch,
          swiftCode: val.bankData.swiftCode,
          ifscCode:val.bankData.ifscCode,
          bankEmail: val.bankData.bankEmail,
          bankAddress: val.bankData.bankAddress,
          // bankContactNo: val.bankData.bankContactNo,
          contactNo: val.address.contactNo,
          bankZipCode: val.bankData.bankZipCode,
          gstnNo: val.gstnNo
        }

      )}
      )

      

      this.dataSource = new MatTableDataSource(ELEMENT_DATA);
      this.ngAfterViewInit();
      return;
      
    })
  }

  getImporter(id:any){
    this.importerService.getImporter(id).subscribe((res:any)=>{
      console.log(res)

      this.frmImporterDetail.patchValue({
        customerName:res.address.customerName,
        customerCode: res.address.customerCode,
        address:  res.address.street+','+res.address.city+','+ res.address.state+',', 
        // accountNumber: val.bankData.accountNumber,,
        country: res.address.country,
        zipCode:res.address.zipCode,
        contactNo:res.address.contactNo,
        email: res.address.email,
        password: res.address.password,
        accountNumber:res.bankData.accountNumber,
        bankAddress: res.bankData.bankAddress,
        bankName: res.bankData.bankName,
        bankEmail: res.bankData.bankEmail,
        // bankContactNo: [,
        swiftCode: res.bankData.swiftCode,
        ifscCode:res.bankData.ifscCode,
        gstnNo: res.gstnNo,
        bankZipCode: '',
        countryOfBelonging: '',
        // branch: []
      });


    })
  }

  
deleteImporter(id:any){

  Swal.fire({
    title: "Are you sure?",
    text: "You won't be able to revert this!",
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    confirmButtonText: "Yes, delete it!"
  }).then((result) => {
    if (result.isConfirmed) {

      this.importerService.deleteImporter(id).subscribe((resp:any)=>{
        debugger
        Swal.fire({
          title: "Deleted!",
          text: resp.message,
          icon: "success"
        });
        this.getAllImporter();
      })
      
    }
  });

}
}




/** Builds and returns a new User. */
// function createNewUser(id: number): UserData {
//   const name =
//     NAMES[Math.round(Math.random() * (NAMES.length - 1))] +
//     ' ' +
//     NAMES[Math.round(Math.random() * (NAMES.length - 1))].charAt(0) +
//     '.';

//   return {
//     id: id.toString(),
//     name: name,
//     progress: Math.round(Math.random() * 100).toString(),
//     fruit: FRUITS[Math.round(Math.random() * (FRUITS.length - 1))],
//   }






// }


