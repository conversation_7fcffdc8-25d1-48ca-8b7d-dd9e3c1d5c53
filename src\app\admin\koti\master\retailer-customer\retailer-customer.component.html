<div class="px-3">

    <!-- Start Content-->
    <div class="container-fluid vh-100">

        <!-- start page title -->
        <div class="py-3 py-lg-4">
            <div class="row" *ngIf="!isOpenDialog">
                <div class="col-lg-6">
                    <h2 class="page-title mb-0">Retailer</h2>
                </div>

            </div>


            <!-- Form row -->
            <div class="row" *ngIf="!isOpenDialog">
                <div class="col-xl-12">
                    <div class="card">
                        <div class="card-body">


                            <form [formGroup]="frmRetailer" (ngSubmit)="addRetailer()">
                                <div class="row">
                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>Retailer Code</mat-label>
                                            <input matInput placeholder="Retailer Name" formControlName="retailerCode">

                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>Retailer Store Name</mat-label>
                                            <input matInput placeholder="Retailer code"
                                                formControlName="retailerStoreName">
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>Contact Person</mat-label>
                                            <input matInput placeholder="Contact Person"
                                                formControlName="contactPerson">
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>Contact Person Number</mat-label>
                                            <input matInput placeholder="Contact Person Number"
                                                formControlName="contactPersonNumber">
                                        </mat-form-field>
                                    </div>






                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label> Address </mat-label>
                                            <textarea matInput placeholder="Address"
                                                formControlName="address">Ex 1600 amp</textarea>
                                        </mat-form-field>

                                    </div>

                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>Zipcode</mat-label>
                                            <input matInput placeholder="Zipcode" formControlName="zipcode">

                                        </mat-form-field>

                                    </div>
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline">
                                            <mat-label>Country</mat-label>
                                            <mat-select formControlName="country">

                                                @for (country of countryList; track country) {
                                                <mat-option [value]="country">{{country}}</mat-option>
                                                }
                                            </mat-select>
                                        </mat-form-field>

                                    </div>
                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>Contact Number</mat-label>
                                            <input matInput placeholder="Contact Number"
                                                formControlName="contactNumber">

                                        </mat-form-field>
                                    </div>

                                    <div class="mb-2 col-md-4">

                                        <mat-form-field class="full-width" appearance="outline">
                                            <mat-label>Email</mat-label>
                                            <input type="email" matInput formControlName="email"
                                                [errorStateMatcher]="matcher" placeholder="Ex. <EMAIL>">

                                            @if (emailFormControl.hasError('email') &&
                                            !emailFormControl.hasError('required')) {
                                            <mat-error>Please enter a valid email address</mat-error>
                                            }
                                            @if (emailFormControl.hasError('required')) {
                                            <mat-error>Email is <strong>required</strong></mat-error>
                                            }
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>Password </mat-label>
                                            <input matInput placeholder="Password" formControlName="password">

                                        </mat-form-field>
                                    </div>

                                </div>

                                <hr>
                                <h2>Bank Details</h2>
                                <div class="row">


                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>Bank Name </mat-label>
                                            <input matInput placeholder="Bank Name" formControlName="bankName">

                                        </mat-form-field>
                                    </div>

                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>Account Number </mat-label>
                                            <input matInput placeholder="Account Number"
                                                formControlName="accountNumber">

                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label> Bank Swift Code </mat-label>
                                            <input matInput placeholder="Bank Swift Code"
                                                formControlName="bankSwiftCode">

                                        </mat-form-field>
                                    </div>

                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>Bank Address </mat-label>
                                            <input matInput placeholder="Bank Address" formControlName="bankAddress">

                                        </mat-form-field>
                                    </div>




                                    <div class="mb-2 col-md-4">

                                        <mat-form-field class="full-width" appearance="outline">
                                            <mat-label>Bank Email</mat-label>
                                            <input type="email" matInput formControlName="bankEmail"
                                                [errorStateMatcher]="matcher" placeholder="Ex. <EMAIL>">

                                            @if (emailFormControl.hasError('email') &&
                                            !emailFormControl.hasError('required')) {
                                            <mat-error>Please enter a valid email address</mat-error>
                                            }
                                            @if (emailFormControl.hasError('required')) {
                                            <mat-error>Email is <strong>required</strong></mat-error>
                                            }
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label> Bank Contact number </mat-label>
                                            <input matInput placeholder="Bank Contact number"
                                                formControlName="bankContactNumber">

                                        </mat-form-field>
                                    </div>
                                </div>






                                <button type="submit" class="btn btn-primary">Add</button>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- end col -->


                <!-- end col -->
            </div>
            <!-- end row -->

            <div class="row">
                <div class="col-12" style="overflow: auto;">
                    <div class="card">
                        <div class="card-body">
                            <mat-form-field>
                                <mat-label>Filter</mat-label>
                                <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Mia" #input>
                            </mat-form-field>

                            <div class="mat-elevation-z8">
                                <table mat-table [dataSource]="dataSource" matSort>

                                    <!-- ID Column -->
                                    <ng-container matColumnDef="id">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>NO.</th>
                                        <td mat-cell *matCellDef="let row"> {{row.index}} </td>
                                    </ng-container>
                                    <!-- ID Column -->
                                    <ng-container matColumnDef="retailerCode">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Code</th>
                                        <td mat-cell *matCellDef="let row"> {{row.retailerCode}} </td>
                                    </ng-container>









                                    <ng-container matColumnDef="email">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
                                        <td mat-cell *matCellDef="let row"> {{row.email}}</td>
                                    </ng-container>






                                    <ng-container matColumnDef="bankName">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank</th>
                                        <td mat-cell *matCellDef="let row"> {{row.bankName}}</td>
                                    </ng-container>

                                    <ng-container matColumnDef="accountNumber">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Account Number</th>
                                        <td mat-cell *matCellDef="let row"> {{row.accountNumber}}</td>
                                    </ng-container>


                                    <ng-container matColumnDef="bankSwiftCode">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Swift Code</th>
                                        <td mat-cell *matCellDef="let row"> {{row.bankSwiftCode}}</td>
                                    </ng-container>

                                    <ng-container matColumnDef="bankAddress">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Address</th>
                                        <td mat-cell *matCellDef="let row"> {{row.bankAddress}}</td>
                                    </ng-container>

                                    <ng-container matColumnDef="bankEmail">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Email</th>
                                        <td mat-cell *matCellDef="let row"> {{row.bankEmail}}</td>
                                    </ng-container>

                                    <ng-container matColumnDef="bankContactNumber">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Contact Number</th>
                                        <td mat-cell *matCellDef="let row"> {{row.bankContactNumber}}</td>
                                    </ng-container>

                                    <!-- Progress Column -->
                                    <ng-container matColumnDef="retailerStoreName">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                                        <td mat-cell *matCellDef="let row"> {{row.retailerStoreName}}</td>
                                    </ng-container>

                                    <!-- Name Column -->
                                    <ng-container matColumnDef="contactPerson">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Contact No </th>
                                        <td mat-cell *matCellDef="let row"> {{row.contactPerson}} </td>
                                    </ng-container>

                                    <!-- Fruit Column -->
                                    <ng-container matColumnDef="address">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> address </th>
                                        <td mat-cell *matCellDef="let row"> {{row.address}} </td>
                                    </ng-container>
                                    <!-- Fruit Column -->
                                    <ng-container matColumnDef="zipcode">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Zip code </th>
                                        <td mat-cell *matCellDef="let row"> {{row.zipcode}} </td>
                                    </ng-container>
                                    <!-- Fruit Column -->
                                    <ng-container matColumnDef="country">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Country </th>
                                        <td mat-cell *matCellDef="let row"> {{row.country}} </td>
                                    </ng-container>


                                    <ng-container matColumnDef="action">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Action </th>
                                        <td mat-cell *matCellDef="let row"> <a routerLink="../importer-order-price-list/{{row.id}}+retailer" > Add Price</a> </td>
                                    </ng-container>
                                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                                    <!-- Row shown when there is no matching data. -->
                                    <tr class="mat-row" *matNoDataRow>
                                        <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"
                                        </td>
                                    </tr>
                                </table>


                            </div>
                            <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]"
                                aria-label="Select page of users"></mat-paginator>
                        </div> <!-- end card body-->
                    </div> <!-- end card -->
                </div><!-- end col-->
            </div>
            <!-- end row-->





        </div>
        <!-- end page title -->

    </div> <!-- container -->

</div> <!-- content -->
