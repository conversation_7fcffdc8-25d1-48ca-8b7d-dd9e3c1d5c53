const mongoose = require("mongoose");

const createChallanSchema = new mongoose.Schema(
  {
    group: {
      type: String,
      required: false,
    },
    wholeseller: {
      type: String,
    },
    retailerOutlet: {
      type: String,
    },
    chooseAdate: {
      type: Date,
      required: true,
    },
    RetailerName: {
      type: String,
    },
    challanNo: {
      type: String,
      required: true,
      unique:true

    },
    saleRetailer: {
      type: String,
      required: false,
    },
    carpetList: [
      {
        barcodeNo: {
          type: Number,
          required: true,
        },
        amount: {
          type: Number,
          // required: true,
        },
        size: {
          type: String,
          // required: true,
        },
        area: {
          type: Number,
          // required: true,
        },
        evkPrice: {
          type: Number,
          // required: true,
        },
        status: { type: String, default: "sale" },
        isDeleted:{type:Boolean, default:false}
      },
    ],
    isDeleted: { type: Boolean, default: false },
    isBillCreated: { type: Boolean, default: false },
    total: { type: Number, required: false }, // Added total
    profit: { type: Number, required: false }, // Added profit
    gst: { type: Number, required: false }, // Added GST 19%
    discount: { type: Number, required: false }, // Added discount
  },
  { timestamps: true }
);

const deletedChallanHistorySchema = new mongoose.Schema(
  {
    group: {
      type: String,
      required: false,
    },
    wholeseller: {
      type: String,
    },
    retailerOutlet: {
      type: String,
    },
    chooseAdate: {
      type: Date,
      required: true,
    },
    RetailerName: {
      type: String,
    },
    challanNo: {
      type: String,
      required: true,
      unique: true,
    },
    saleRetailer: {
      type: String,
      required: false,
    },
    carpetList: [
      {
        barcodeNo: {
          type: Number,
          required: true,
        },
        amount: {
          type: Number,
          // required: true,
        },
        size: {
          type: String,
          required: true, // Changed to String to match createChallanSchema
        },
        area: {
          type: Number,
          // required: true,
        },
        evkPrice: {
          type: Number,
          // required: true,
        },
        status: { type: String, default: "sale" },
        isDeleted:{type:Boolean, default:false}
      },
    ],
    total: { type: Number, required: false }, // Added total
    profit: { type: Number, required: false }, // Added profit
    gst: { type: Number, required: false }, // Added GST 19%
    discount: { type: Number, required: false }, // Added discount
  },
  { timestamps: true }
);

const CreateChallan = mongoose.model("CreateChallan", createChallanSchema);

const deletedChallanHistory = mongoose.model(
  "deletedChallanHistory",
  deletedChallanHistorySchema
);

module.exports = { CreateChallan, deletedChallanHistory };
