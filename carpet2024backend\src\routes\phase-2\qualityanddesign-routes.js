// routes.js

const express = require('express');
const router = express.Router();
const qualityAndDesignController = require('../../controller/qulaityanddesign-controller');

router.post('/craete-qualityanddesign', qualityAndDesignController.createQualityAndDesign);
router.get('/get-qualityanddesign/:id', qualityAndDesignController.getQualityAndDesignById);
router.get('/getAll-qualityanddesign', qualityAndDesignController.getAllQualityAndDesigns);
router.put('/update-qualityanddesign/:id', qualityAndDesignController.updateQualityAndDesign);
router.delete('/delete-qualityanddesign/:id', qualityAndDesignController.deleteQualityAndDesign);

module.exports = router;
