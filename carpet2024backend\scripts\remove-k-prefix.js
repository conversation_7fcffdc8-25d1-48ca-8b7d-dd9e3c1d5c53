// Script to remove K- prefix and show only weaver names (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, etc.)
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function removeKPrefix() {
  console.log('🔄 REMOVING K- PREFIX FROM WEAVER NAMES');
  console.log('='.repeat(60));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get all records
    const allRecords = await collection.find({}).toArray();
    console.log(`📊 Found ${allRecords.length} records to update`);
    
    const results = { success: [], errors: [] };
    
    // Update each record's weaverName to remove K- prefix
    for (let i = 0; i < allRecords.length; i++) {
      try {
        const record = allRecords[i];
        let currentWeaverName = record.weaverName || '';
        
        // Remove K- or K prefix
        let newWeaverName = currentWeaverName;
        if (currentWeaverName.startsWith('K-')) {
          newWeaverName = currentWeaverName.substring(2); // Remove "K-"
        } else if (currentWeaverName.startsWith('K ')) {
          newWeaverName = currentWeaverName.substring(2); // Remove "K "
        }
        
        // Update the record
        await collection.updateOne(
          { _id: record._id },
          { 
            $set: { 
              weaverName: newWeaverName 
            } 
          }
        );
        
        results.success.push({
          receiveNo: record.receiveNo,
          oldWeaver: currentWeaverName,
          newWeaver: newWeaverName
        });
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Updated ${i + 1}/${allRecords.length} records`);
        }
        
      } catch (error) {
        console.error(`❌ Error updating record ${i + 1}:`, error.message);
        results.errors.push({
          receiveNo: allRecords[i]?.receiveNo || `Record ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error removing K- prefix:', error);
    return { success: [], errors: [] };
  }
}

async function updateWeaverEmployees() {
  console.log('\n🔄 UPDATING WEAVER EMPLOYEE NAMES');
  console.log('='.repeat(60));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('weaver_employees');
    
    // Get all weaver employees
    const allWeavers = await collection.find({}).toArray();
    console.log(`📊 Found ${allWeavers.length} weaver employees to update`);
    
    const results = { success: [], errors: [] };
    
    // Update each weaver's name to remove K- prefix
    for (let i = 0; i < allWeavers.length; i++) {
      try {
        const weaver = allWeavers[i];
        let currentName = weaver.name || '';
        
        // Remove K- or K prefix
        let newName = currentName;
        if (currentName.startsWith('K-')) {
          newName = currentName.substring(2); // Remove "K-"
        } else if (currentName.startsWith('K ')) {
          newName = currentName.substring(2); // Remove "K "
        }
        
        // Update the weaver
        await collection.updateOne(
          { _id: weaver._id },
          { 
            $set: { 
              name: newName 
            } 
          }
        );
        
        results.success.push({
          id: weaver._id,
          oldName: currentName,
          newName: newName
        });
        
      } catch (error) {
        console.error(`❌ Error updating weaver ${i + 1}:`, error.message);
        results.errors.push({
          id: allWeavers[i]?._id || `Weaver ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error updating weaver employee names:', error);
    return { success: [], errors: [] };
  }
}

async function verifyCleanNames() {
  console.log('\n🔍 Verifying clean weaver names...');
  
  try {
    const db = mongoose.connection.db;
    
    // Get sample carpet received records
    const samples = await db.collection('carpetreceiveds').find({}).limit(15).toArray();
    
    console.log('\n📋 Sample carpet received records with clean names:');
    samples.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver Name: ${record.weaverName || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ${record.amount || 'N/A'}`);
      console.log('   ' + '-'.repeat(50));
    });
    
    // Check distinct weaver names
    const distinctWeavers = await db.collection('carpetreceiveds').distinct('weaverName');
    console.log(`\n👥 All clean weaver names (${distinctWeavers.length}):`);
    distinctWeavers.forEach((weaver, index) => {
      console.log(`   ${index + 1}. ${weaver}`);
    });
    
  } catch (error) {
    console.error('❌ Error verifying clean names:', error);
  }
}

async function main() {
  console.log('🔄 REMOVING K- PREFIX FROM ALL WEAVER NAMES');
  console.log('(Show only: Rahila, Jeet Narayan, Yaseen, etc.)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Remove K- prefix from carpet received records
    const carpetResults = await removeKPrefix();

    // Update weaver employee names
    const weaverResults = await updateWeaverEmployees();

    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 K- PREFIX REMOVAL COMPLETE');
    console.log('='.repeat(60));
    console.log(`✅ Carpet Received Records Updated: ${carpetResults.success.length}`);
    console.log(`✅ Weaver Employees Updated: ${weaverResults.success.length}`);
    console.log(`❌ Total Errors: ${carpetResults.errors.length + weaverResults.errors.length}`);
    
    if (carpetResults.success.length > 0) {
      console.log('\n✅ SAMPLE UPDATED RECORDS:');
      carpetResults.success.slice(0, 15).forEach(record => {
        console.log(`  - ${record.receiveNo}: "${record.oldWeaver}" → "${record.newWeaver}"`);
      });
    }
    
    // Verify clean names
    await verifyCleanNames();

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 K- PREFIX REMOVED COMPLETELY!');
    console.log('✅ All weaver names now clean');
    console.log('✅ Frontend will show: Rahila, Jeet Narayan, Yaseen, Shabana, etc.');
    console.log('✅ No prefixes - only clean names!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
