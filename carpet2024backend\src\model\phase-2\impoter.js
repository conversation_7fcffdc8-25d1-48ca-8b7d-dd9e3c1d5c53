const mongoose = require("mongoose");

const addressSchema = new mongoose.Schema({
  customerName: String,
  customerCode: String,
  email: String,
  password: String,
  userAddress: String,
  street: String,
  city: String,
  state: String,
  zipCode: String,
  country: String,
  contactNo: String,
});

const bankDataSchema = new mongoose.Schema({
  accountNumber: String,
  bankAddress: String,
  bankName: String,
  branch: String,
  swiftCode: String,
  ifscCode: String,
  bankEmail: String,
  bankContactNo: String,
});

const addOnPriceSchema = new mongoose.Schema({
  toDate: String,
  fromDate: String,
  quality: String,
  design: String,
  orderPrice: Number,
});


const ImpoterSchema = new mongoose.Schema({
  address: {
    type: addressSchema,
    // required:true,
  },
  bankData: {
    type: bankDataSchema,
    // required:true,
  },

  contactNo: {
    type: String,
    // required:true,
  },
  alternateContactNo: {
    type: String,
  },
  gstnNo: {
    type: String,
    // required:true,
  },
  countryOfBelonging: {
    type: String,
    // required:true,
  },
  addOnPrice: {
    type: [addOnPriceSchema],
    required:true,
    default: [],
  },

});

const addOnPriceUpdateHistoryScehma = new mongoose.Schema({
  importer_id: { type: String },
  addOnPrice_id: { type: String },
  oldAddOnPrice: { type: addOnPriceSchema }, // Using addOnPriceSchema here
  newAddOnPrice: { type: addOnPriceSchema }, // Using addOnPriceSchema here
  createdAt: { type: Date, default: Date.now } // Corrected Date.now()
});

ImpoterSchema.methods.addOrUpdateAddOnPrice = function (newPrices) {
  const updatedPrices = [];
  newPrices.forEach((newPrice) => {
    const existingIndex = this.addOnPrice.findIndex(
      (price) =>
        price.toDate === newPrice.toDate &&
        price.fromDate === newPrice.fromDate &&
        price.quality === newPrice.quality &&
        price.design === newPrice.design
    );

    if (existingIndex !== -1) {
      // If the price already exists, update it with the new data and add to updatedPrices
      const updatedPrice = Object.assign({}, this.addOnPrice[existingIndex], newPrice);
      updatedPrices.push(updatedPrice);
      // Update the existing price with the new data
      Object.assign(this.addOnPrice[existingIndex], newPrice);
    } else {
      // If the price does not exist, add it and add to updatedPrices
      this.addOnPrice.push(newPrice);
      updatedPrices.push(newPrice);
    }
  });

  // Return both the old and updated data
  return { oldPrices: this.addOnPrice, updatedPrices };
};





const Impoter = mongoose.model("Impoter", ImpoterSchema);

const UpdateHistoryModel = mongoose.model("addOnPriceUpdateHistory",addOnPriceUpdateHistoryScehma);

module.exports ={ Impoter,UpdateHistoryModel };
