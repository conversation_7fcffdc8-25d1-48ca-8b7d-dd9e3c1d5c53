const mongoose = require('mongoose');

const mapRateSchema = new mongoose.Schema({
  name: {
    type: mongoose.Schema.Types.ObjectId,
    ref:'WeaverEmployee',
    required: true
  },
  toDate: {
    type: Date,
    required: true
  },
  fromDate: {
    type: Date,
    required: true
  },
  quality: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AddQuality',
    required: true
  },
 design : {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AddDesign',
    required: true
  },
  area: {
    type: String,
    required: true
  },
  calculate_area: {
    type: String,
    required: true
  },
  rate: {
    type: String,
    required: true
  }
});

const mapRate = mongoose.model('mapRate', mapRateSchema);

module.exports = mapRate;
