<div class="container mt-4">


  <div class="row">
    <fieldset >
      <legend><b>Add New Order</b></legend>
      <div class="container">
        <form [formGroup]="frmOrderItem" (ngSubmit)="addItems()">
          <div class="row">
            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Quality</mat-label>
                <mat-select
                  (valueChange)="selectQuality($event)"
                  formControlName="quality"
                >
                  @for (quality of qualityList; track quality) {
                  <mat-option [value]="quality.quality">{{
                    quality.quality
                  }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
            </div>

            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Design </mat-label>
                <input
                  matInput
                  placeholder=" Design"
                  readonly
                  formControlName="design"
                />
              </mat-form-field>
            </div>
            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Ground Color</mat-label>
                <input
                  matInput
                  placeholder="Ground "
                  readonly
                  formControlName="ground"
                />
              </mat-form-field>
            </div>

            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Size</mat-label>
                <mat-select
                  formControlName="size"
                  (valueChange)="selectSize($event)"
                >
                  @for (size of sizeList; track size) {
                  <mat-option [value]="size.id">{{ size.size }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
            </div>

            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Khap Size</mat-label>
                <input
                  matInput
                  placeholder="   Khap Size "
                  formControlName="khapSize"
                />
              </mat-form-field>
            </div>

            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Type</mat-label>
                <mat-select  formControlName="type">
                  <mat-option value="option1">Normal(NR)</mat-option>
                  <mat-option value="option2">Round(RD)</mat-option>
                  <mat-option value="option3">Ovel(OV)</mat-option>
                  <mat-option value="option4">Octagent(OT)</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="mb-2 col-md-1">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Total Pcs</mat-label>
                <input
                  matInput
                  formControlName="pcs"

                />
              </mat-form-field>
            </div>
            <div class="mb-2 col-md-1">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Pcs Pending</mat-label>
                <input
                  matInput
                  formControlName="PcsesPending"
                  readonly
                />
              </mat-form-field>
            </div>
            <div class="mb-2 col-md-1">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Pcs Assigned</mat-label>
                <input
                  matInput
                  formControlName="PcsesAssigned"
                  readonly
                />
              </mat-form-field>
            </div>
            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Area</mat-label>
                <input
                  matInput
                  placeholder=" Area "
                  formControlName="totalArea"
                />
              </mat-form-field>
            </div>

            <div class="mt-2 col-md-3">
              <button mat-flat-button color="primary">Save & Next</button>
            </div>
          </div>
        </form>
      </div>
    </fieldset>
  </div>
</div>

<div class="container mt-4">
  <section>
    <fieldset>
      <legend><b> Order List</b></legend>
      <div class="row">
        <div class="col-md-12">
          <mat-form-field appearance="outline" class="exwidth">
            <mat-label>Search</mat-label>
            <input
              matInput
              (keyup)="applyFilter($event)"
              placeholder="Ex. Jack"
              #input
            />
          </mat-form-field>

          <div class="mat-elevation-z8">
            <table mat-table [dataSource]="dataSource" matSort>
              <!-- No Column -->
              <ng-container matColumnDef="SrNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Sr. No.
                </th>
                <td mat-cell *matCellDef="let row">{{ row.SrNo }}</td>
              </ng-container>
              <!-- ProductionQuality Column -->
              <ng-container matColumnDef="Quality">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Quality
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Quality }}</td>
              </ng-container>

              <!-- Design Column -->
              <ng-container matColumnDef="Design">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Design
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Design }}</td>
              </ng-container>

              <!-- Name Column -->
              <ng-container matColumnDef="GroundBorder">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Ground/Border
                </th>
                <td mat-cell *matCellDef="let row">{{ row.GroundBorder }}</td>
              </ng-container>

              <ng-container matColumnDef="size">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>size</th>
                <td mat-cell *matCellDef="let row">{{ row.size }}</td>
              </ng-container>

              <!-- Fruit Column -->
              <ng-container matColumnDef="KhapSize">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Khap Size
                </th>
                <td mat-cell *matCellDef="let row">{{ row.KhapSize }}</td>
              </ng-container>
              <!-- Name Column -->
              <ng-container matColumnDef="Pcs">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Total Pcs</th>
                <td mat-cell *matCellDef="let row">{{ row.Pcs }}</td>
              </ng-container>

              <ng-container matColumnDef="PcsesPending">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Pcs Pending</th>
                <td mat-cell *matCellDef="let row">{{ row.PcsesPending }}</td>
              </ng-container>

              <ng-container matColumnDef="PcsesAssigned">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Pcs Assigned</th>
                <td mat-cell *matCellDef="let row">{{ row.PcsesAssigned }}</td>
              </ng-container>

              <ng-container matColumnDef="Area">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Area</th>
                <td mat-cell *matCellDef="let row">{{ row.Area }}</td>
              </ng-container>

              <ng-container matColumnDef="Action">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Action
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Action }}</td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>

              <!-- Row shown when there is no matching data. -->
              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="4">
                  No data matching the filter "{{ input.value }}"
                </td>
              </tr>
            </table>

            <mat-paginator
              [pageSizeOptions]="[5, 10, 25, 100]"
              aria-label="Select page of users"
            ></mat-paginator>
          </div>
        </div>
      </div>
    </fieldset>
  </section>
</div>
