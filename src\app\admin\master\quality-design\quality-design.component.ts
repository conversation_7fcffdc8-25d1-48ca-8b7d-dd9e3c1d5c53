import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-quality-design',
  templateUrl: './quality-design.component.html',
  styleUrl: './quality-design.component.css'
})
export class QualityDesignComponent implements OnInit {
  qualityForm: FormGroup;
  data: any; 
  editId: any;
  isEditMode :boolean= false;
  constructor(public _fb: FormBuilder, public masterservice: MasterService) {
    this.qualityForm = this._fb.group({
      quality: [],
      exportQuality: [],
      qualityInInch: []

    })
  }

  qualityFormSubmit() {
    if (this.qualityForm.valid)
      // console.log( this.editId);
      if (this.isEditMode && this.editId) {
        let frmData = this.qualityForm.value
        this.masterservice.updatequality(this.editId,frmData).subscribe({
          next: () => {
            Swal.fire({
              title: "Success",
              text: "Successfully Updated!",
              icon: "success"
            });
            this.getqualityList();
            this.resetForm();
          },
          error: () => {
            Swal.fire({
              icon: "error",
              title: "Failed ! ",
              text: "Something went wrong!",
            });
          }
        })
      }
      else {

        this.masterservice.addquality(this.qualityForm.value).subscribe({
          next: () => {

            Swal.fire({
              title: "Success",
              text: "Successfully Added!!",
              icon: "success"
            });
            this.getqualityList();
            this.resetForm()
          },
          error: () => {
            Swal.fire({
              title: "Failed !",
              text: "Something went wrong ?",
              icon: "error"
            });
          }
        })
      }

  }
  editquality(data: any) {
    this.isEditMode = true;
    this.editId = data._id;
    this.qualityForm.patchValue(data);
  }
  resetForm() {
    this.qualityForm.reset();
    this.isEditMode = false;
  }
  getSerialNumber(index: number): number {
    if (!this.paginator) {
      return index + 1;
    }
    return index + 1 + this.paginator.pageIndex * this.paginator.pageSize;
  }

  getqualityList() {
    this.masterservice.getqualityList().subscribe({
      next: (res) => {
        this.dataSource = new MatTableDataSource(res);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      },
      error: console.log,
    });
  }
  deleteConformation(id: any) {
    Swal.fire({
      icon: "warning",
      text: "Do you want to Delete this Data ?",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (result.isConfirmed) {
        this.deletequality(id)
      }
      else if (result.isDenied) {
        Swal.fire('Changes are not save', '', 'info')
      }
    })
  }
  deletequality(id: any) {
    this.masterservice.deletequality(id).subscribe({
      next: (res) => {

        Swal.fire('Deleted !', 'Your data has been deleted.', 'success')
        this.getqualityList();
      },
      error: () => {
        // alert('Error !');
        Swal.fire({
          title: "Failed !",
          text: "Something went wrong ?",
          icon: "error"
        });
      }
    });

  }

  displayedColumns: string[] = ['id', 'quality', 'exportQuality','qualityInInch', 'action'];
  dataSource = new MatTableDataSource<any>

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngOnInit(): void {
    this.getqualityList()

  }
  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
}
