import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment.development';

@Injectable({
  providedIn: 'root'
})
export class ManufactureService {
  apiUrl: any = environment.apiUrl;
  phaseOne: any = `${this.apiUrl}/phase-one`;
  phaseTwo: any = `${this.apiUrl}/phase-two`;
  phaseThree: any = `${this.apiUrl}/phase-three`;
  phaseFour:any=`${this.apiUrl}/phase-four`;
  constructor(private http:HttpClient) { }

  // Here service for buyerOrder

 addBuyerOrder(data:any){
 return this.http.post(`${this.phaseFour}/buyerOrder/BuyerOrder`,data)

 }

 getAllBuyerOrder(){
  return this.http.get(`${this.phaseFour}/buyerOrder/BuyerOrder`)
 }

 getBuyerOrder(id:any){
  return this.http.get(`${this.phaseFour}/buyerOrder/BuyerOrder/${id}`)
 }

 updateBuyerOrder(data:any,id:any){
  return this.http.put(`${this.phaseFour}/buyerOrder/BuyerOrder/${id}`,data)
 }

 deleteBuyerOrder(id:any){
 return this.http.delete(`${this.phaseFour}/buyerOrder/BuyerOrder/${id}`)
 }
 deleteBuyerOrderItem(id:any){
 return this.http.delete(`${this.phaseFour}/buyerOrder/delete-item-buyerOrder/${id}`)
 }

postOrderIssueList(data: any) {
  return this.http.post(`${this.phaseFour}/carpetOrder/carpetOrderissues`, data);
}

getsOrderIssueList() {
  return this.http.get(`${this.phaseFour}/carpetOrder/carpetOrderissues`);
}

getPopulatedOrderIssueList() {
  return this.http.get(`${this.phaseFour}/carpetOrder/getpopulate`);
}

getOrderIssue(id: string) {
  return this.http.get(`${this.phaseFour}/carpetOrder/carpetOrderissues/${id}`);
}
// updateOrderIssue(id:string, data:any){
//   debugger
//   return this.http.patch(`${this.phaseFour}/carpetOrder/carpetOrderissues/${id}`,data);
// }
updateOrderIssue(id: string, data: any) {
  console.log('Service: Sending update request with data:', data);
  // Don't set Content-Type header for FormData - let browser set it automatically
  return this.http.patch(`${this.phaseFour}/carpetOrder/carpetOrderissues/${id}`, data);
}

// deleteIssueOrder(id:string,data:any){
//   return this.http.delete(`${this.phaseFour}/carpetOrder/delete-carpetOrderissues/${id}`,data)
// }
// deleteIssueOrder(id: string) {
//   return this.http.delete(`${this.phaseFour}/carpetOrder/delete-carpetOrderissues/${id}`);
// }
deleteIssueOrder(id: string, data?: any) {
  const options = {
    body: data, // Send data in the request body
  };
  return this.http.delete(`${this.phaseFour}/carpetOrder/delete-carpetOrderissues/${id}`, options);
}

// Material Issue API methods
addMaterialIssue(data: any) {
  console.log('Material Issue API Request:', data);
  return this.http.post(`${this.phaseFour}/materialIssue`, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

getAllMaterialIssues() {
  return this.http.get(`${this.phaseFour}/materialIssue`);
}

getMaterialIssue(id: any) {
  return this.http.get(`${this.phaseFour}/materialIssue/${id}`);
}

updateMaterialIssue(data: any, id: any) {
  return this.http.put(`${this.phaseFour}/materialIssue/${id}`, data);
}

deleteMaterialIssue(id: any) {
  return this.http.delete(`${this.phaseFour}/materialIssue/${id}`);
}

getMaterialIssuesByIssueNo(issueNoId: any) {
  return this.http.get(`${this.phaseFour}/materialIssue/issueNo/${issueNoId}`);
}

// Carpet Receiving API methods
addCarpetReceiving(data: any) {
  console.log('API Request:', data);

  return this.http.post(`${this.apiUrl}/phase-four/carpetRecevied/carpetRecevied`, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
}
// addCarpetReceiving(data: any) {
//   return this.http.post(`${this.apiUrl}/phase-four/carpetRecevied/carpetRecevied`, data, {
//     headers: {
//       'Content-Type': 'application/json'
//     }
//   });
// }

getAllCarpetReceiving() {
  return this.http.get(`${this.apiUrl}/phase-four/carpetRecevied/carpetRecevied`);
}


// getAllCarpetReceiving() {
//   return this.http.get(`${this.apiUrl}/phase-four/carpetReceived/carpetReceived`);
// }

getLastCarpetNo() {
  return this.http.get(`${this.apiUrl}/phase-four/carpetRecevied/carpetRecevied/lastCarpetNo`);
}

// Get received carpets by issue IDs
getReceivedCarpetsByIssueIds(issueIds: string[]) {
  return this.http.post(`${this.apiUrl}/phase-four/carpetReceived/carpetRecevied/by-issues`, { issueIds });
}

}
