const AddContainerDespatch = require('../model/phase-1/containerD');

class AddContainerDespatchRepository {
  async createContainerDespatch(containerDespatchData) {
    try {
      return await AddContainerDespatch.create(containerDespatchData);
    } catch (error) {
      throw error;
    }
  }

  async getContainerDespatchById(containerDespatchId) {
    try {
      return await AddContainerDespatch.findById(containerDespatchId);
    } catch (error) {
      throw error;
    }
  }

  async getAllContainerDespatches() {
    try {
      return await AddContainerDespatch.find();
    } catch (error) {
      throw error;
    }
  }

  async updateContainerDespatch(containerDespatchId, containerDespatchData) {
    try {
      return await AddContainerDespatch.findByIdAndUpdate(containerDespatchId, containerDespatchData, { new: true });
    } catch (error) {
      throw error;
    }
  }

  async deleteContainerDespatch(containerDespatchId) {
    try {
      return await AddContainerDespatch.findByIdAndDelete(containerDespatchId);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new AddContainerDespatchRepository();
