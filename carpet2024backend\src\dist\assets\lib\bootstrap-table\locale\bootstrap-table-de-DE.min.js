/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var e=n(t),r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof r&&r)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},m={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,y=b&&!m.call({1:2},1);g.f=y?function(t){var n=b(this,t);return!!n&&n.enumerable}:m;var d,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),E=T("".slice),x=function(t){return E(A(t),8,-1)},D=x,F=P,C=function(t){if("Function"===D(t))return F(t)},M=c,L=x,N=Object,R=C("".split),Z=M((function(){return!N("z").propertyIsEnumerable(0)}))?function(t){return"String"==L(t)?R(t,""):N(t)}:N,k=function(t){return null==t},I=k,z=TypeError,_=function(t){if(I(t))throw z("Can't call method on "+t);return t},B=Z,V=_,G=function(t){return B(V(t))},q="object"==typeof document&&document.all,H={all:q,IS_HTMLDDA:void 0===q&&void 0!==q},U=H.all,K=H.IS_HTMLDDA?function(t){return"function"==typeof t||t===U}:function(t){return"function"==typeof t},W=K,J=H.all,Q=H.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:W(t)||t===J}:function(t){return"object"==typeof t?null!==t:W(t)},X=i,Y=K,$=function(t){return Y(t)?t:void 0},tt=function(t,n){return arguments.length<2?$(X[t]):X[t]&&X[t][n]},nt=C({}.isPrototypeOf),et=i,rt=tt("navigator","userAgent")||"",ot=et.process,it=et.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(h=(d=ct.split("."))[0]>0&&d[0]<4?1:+(d[0]+d[1])),!h&&rt&&(!(d=rt.match(/Edge\/(\d+)/))||d[1]>=74)&&(d=rt.match(/Chrome\/(\d+)/))&&(h=+d[1]);var at=h,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,mt=K,bt=nt,yt=Object,dt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return mt(n)&&bt(n.prototype,yt(t))},ht=String,vt=K,St=function(t){try{return ht(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=k,Pt=p,Tt=K,At=Q,Et=TypeError,xt={exports:{}},Dt=i,Ft=Object.defineProperty,Ct=function(t,n){try{Ft(Dt,t,{value:n,configurable:!0,writable:!0})}catch(e){Dt[t]=n}return n},Mt=Ct,Lt="__core-js_shared__",Nt=i[Lt]||Mt(Lt,{}),Rt=Nt;(xt.exports=function(t,n){return Rt[t]||(Rt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Zt=_,kt=Object,It=function(t){return kt(Zt(t))},zt=It,_t=C({}.hasOwnProperty),Bt=Object.hasOwn||function(t,n){return _t(zt(t),n)},Vt=C,Gt=0,qt=Math.random(),Ht=Vt(1..toString),Ut=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Ht(++Gt+qt,36)},Kt=i,Wt=xt.exports,Jt=Bt,Qt=Ut,Xt=st,Yt=pt,$t=Wt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,en=Yt?tn:tn&&tn.withoutSetter||Qt,rn=function(t){if(!Jt($t,t)||!Xt&&"string"!=typeof $t[t]){var n="Symbol."+t;Xt&&Jt(tn,t)?$t[t]=tn[t]:$t[t]=Yt&&nn?nn(n):en(n)}return $t[t]},on=p,un=Q,cn=dt,an=function(t,n){var e=t[n];return jt(e)?void 0:Ot(e)},fn=function(t,n){var e,r;if("string"===n&&Tt(e=t.toString)&&!At(r=Pt(e,t)))return r;if(Tt(e=t.valueOf)&&!At(r=Pt(e,t)))return r;if("string"!==n&&Tt(e=t.toString)&&!At(r=Pt(e,t)))return r;throw Et("Can't convert object to primitive value")},ln=TypeError,sn=rn("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var e,r=an(t,sn);if(r){if(void 0===n&&(n="default"),e=on(r,t,n),!un(e)||cn(e))return e;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=dt,mn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},bn=Q,yn=i.document,dn=bn(yn)&&bn(yn.createElement),hn=function(t){return dn?yn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),Sn=a,wn=p,On=g,jn=v,Pn=G,Tn=mn,An=Bt,En=vn,xn=Object.getOwnPropertyDescriptor;u.f=Sn?xn:function(t,n){if(t=Pn(t),n=Tn(n),En)try{return xn(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var Dn={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Cn=Q,Mn=String,Ln=TypeError,Nn=function(t){if(Cn(t))return t;throw Ln(Mn(t)+" is not an object")},Rn=a,Zn=vn,kn=Fn,In=Nn,zn=mn,_n=TypeError,Bn=Object.defineProperty,Vn=Object.getOwnPropertyDescriptor,Gn="enumerable",qn="configurable",Hn="writable";Dn.f=Rn?kn?function(t,n,e){if(In(t),n=zn(n),In(e),"function"==typeof t&&"prototype"===n&&"value"in e&&Hn in e&&!e.writable){var r=Vn(t,n);r&&r.writable&&(t[n]=e.value,e={configurable:qn in e?e.configurable:r.configurable,enumerable:Gn in e?e.enumerable:r.enumerable,writable:!1})}return Bn(t,n,e)}:Bn:function(t,n,e){if(In(t),n=zn(n),In(e),Zn)try{return Bn(t,n,e)}catch(t){}if("get"in e||"set"in e)throw _n("Accessors not supported");return"value"in e&&(t[n]=e.value),t};var Un=Dn,Kn=v,Wn=a?function(t,n,e){return Un.f(t,n,Kn(1,e))}:function(t,n,e){return t[n]=e,t},Jn={exports:{}},Qn=a,Xn=Bt,Yn=Function.prototype,$n=Qn&&Object.getOwnPropertyDescriptor,te=Xn(Yn,"name"),ne={EXISTS:te,PROPER:te&&"something"===function(){}.name,CONFIGURABLE:te&&(!Qn||Qn&&$n(Yn,"name").configurable)},ee=K,re=Nt,oe=C(Function.toString);ee(re.inspectSource)||(re.inspectSource=function(t){return oe(t)});var ie,ue,ce,ae=re.inspectSource,fe=K,le=i.WeakMap,se=fe(le)&&/native code/.test(String(le)),pe=xt.exports,ge=Ut,me=pe("keys"),be={},ye=se,de=i,he=Q,ve=Wn,Se=Bt,we=Nt,Oe=function(t){return me[t]||(me[t]=ge(t))},je=be,Pe="Object already initialized",Te=de.TypeError,Ae=de.WeakMap;if(ye||we.state){var Ee=we.state||(we.state=new Ae);Ee.get=Ee.get,Ee.has=Ee.has,Ee.set=Ee.set,ie=function(t,n){if(Ee.has(t))throw Te(Pe);return n.facade=t,Ee.set(t,n),n},ue=function(t){return Ee.get(t)||{}},ce=function(t){return Ee.has(t)}}else{var xe=Oe("state");je[xe]=!0,ie=function(t,n){if(Se(t,xe))throw Te(Pe);return n.facade=t,ve(t,xe,n),n},ue=function(t){return Se(t,xe)?t[xe]:{}},ce=function(t){return Se(t,xe)}}var De={set:ie,get:ue,has:ce,enforce:function(t){return ce(t)?ue(t):ie(t,{})},getterFor:function(t){return function(n){var e;if(!he(n)||(e=ue(n)).type!==t)throw Te("Incompatible receiver, "+t+" required");return e}}},Fe=c,Ce=K,Me=Bt,Le=a,Ne=ne.CONFIGURABLE,Re=ae,Ze=De.enforce,ke=De.get,Ie=Object.defineProperty,ze=Le&&!Fe((function(){return 8!==Ie((function(){}),"length",{value:8}).length})),_e=String(String).split("String"),Be=Jn.exports=function(t,n,e){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),e&&e.getter&&(n="get "+n),e&&e.setter&&(n="set "+n),(!Me(t,"name")||Ne&&t.name!==n)&&(Le?Ie(t,"name",{value:n,configurable:!0}):t.name=n),ze&&e&&Me(e,"arity")&&t.length!==e.arity&&Ie(t,"length",{value:e.arity});try{e&&Me(e,"constructor")&&e.constructor?Le&&Ie(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=Ze(t);return Me(r,"source")||(r.source=_e.join("string"==typeof n?n:"")),t};Function.prototype.toString=Be((function(){return Ce(this)&&ke(this).source||Re(this)}),"toString");var Ve=K,Ge=Dn,qe=Jn.exports,He=Ct,Ue={},Ke=Math.ceil,We=Math.floor,Je=Math.trunc||function(t){var n=+t;return(n>0?We:Ke)(n)},Qe=function(t){var n=+t;return n!=n||0===n?0:Je(n)},Xe=Qe,Ye=Math.max,$e=Math.min,tr=Qe,nr=Math.min,er=function(t){return t>0?nr(tr(t),9007199254740991):0},rr=function(t){return er(t.length)},or=G,ir=function(t,n){var e=Xe(t);return e<0?Ye(e+n,0):$e(e,n)},ur=rr,cr=function(t){return function(n,e,r){var o,i=or(n),u=ur(i),c=ir(r,u);if(t&&e!=e){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===e)return t||c||0;return!t&&-1}},ar={includes:cr(!0),indexOf:cr(!1)},fr=Bt,lr=G,sr=ar.indexOf,pr=be,gr=C([].push),mr=function(t,n){var e,r=lr(t),o=0,i=[];for(e in r)!fr(pr,e)&&fr(r,e)&&gr(i,e);for(;n.length>o;)fr(r,e=n[o++])&&(~sr(i,e)||gr(i,e));return i},br=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Ue.f=Object.getOwnPropertyNames||function(t){return mr(t,br)};var yr={};yr.f=Object.getOwnPropertySymbols;var dr=tt,hr=Ue,vr=yr,Sr=Nn,wr=C([].concat),Or=dr("Reflect","ownKeys")||function(t){var n=hr.f(Sr(t)),e=vr.f;return e?wr(n,e(t)):n},jr=Bt,Pr=Or,Tr=u,Ar=Dn,Er=c,xr=K,Dr=/#|\.prototype\./,Fr=function(t,n){var e=Mr[Cr(t)];return e==Nr||e!=Lr&&(xr(n)?Er(n):!!n)},Cr=Fr.normalize=function(t){return String(t).replace(Dr,".").toLowerCase()},Mr=Fr.data={},Lr=Fr.NATIVE="N",Nr=Fr.POLYFILL="P",Rr=Fr,Zr=i,kr=u.f,Ir=Wn,zr=function(t,n,e,r){r||(r={});var o=r.enumerable,i=void 0!==r.name?r.name:n;if(Ve(e)&&qe(e,i,r),r.global)o?t[n]=e:He(n,e);else{try{r.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=e:Ge.f(t,n,{value:e,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return t},_r=Ct,Br=function(t,n,e){for(var r=Pr(n),o=Ar.f,i=Tr.f,u=0;u<r.length;u++){var c=r[u];jr(t,c)||e&&jr(e,c)||o(t,c,i(n,c))}},Vr=Rr,Gr=x,qr=Array.isArray||function(t){return"Array"==Gr(t)},Hr=TypeError,Ur=mn,Kr=Dn,Wr=v,Jr={};Jr[rn("toStringTag")]="z";var Qr="[object z]"===String(Jr),Xr=K,Yr=x,$r=rn("toStringTag"),to=Object,no="Arguments"==Yr(function(){return arguments}()),eo=C,ro=c,oo=K,io=Qr?Yr:function(t){var n,e,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,n){try{return t[n]}catch(t){}}(n=to(t),$r))?e:no?Yr(n):"Object"==(r=Yr(n))&&Xr(n.callee)?"Arguments":r},uo=ae,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=eo(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},mo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};mo.sham=!0;var bo=!fo||ro((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?mo:go,yo=qr,ho=bo,vo=Q,So=rn("species"),wo=Array,Oo=function(t){var n;return yo(t)&&(n=t.constructor,(ho(n)&&(n===wo||yo(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=c,Po=at,To=rn("species"),Ao=function(t,n){var e,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(e=a?Zr:f?Zr[c]||_r(c,{}):(Zr[c]||{}).prototype)for(r in n){if(i=n[r],o=t.dontCallGetSet?(u=kr(e,r))&&u.value:e[r],!Vr(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Br(i,o)}(t.sham||o&&o.sham)&&Ir(i,"sham",!0),zr(e,r,i,t)}},Eo=c,xo=qr,Do=Q,Fo=It,Co=rr,Mo=function(t){if(t>9007199254740991)throw Hr("Maximum allowed index exceeded");return t},Lo=function(t,n,e){var r=Ur(n);r in t?Kr.f(t,r,Wr(0,e)):t[r]=e},No=function(t,n){return new(Oo(t))(0===n?0:n)},Ro=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Zo=at,ko=rn("isConcatSpreadable"),Io=Zo>=51||!Eo((function(){var t=[];return t[ko]=!1,t.concat()[0]!==t})),zo=Ro("concat"),_o=function(t){if(!Do(t))return!1;var n=t[ko];return void 0!==n?!!n:xo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!Io||!zo},{concat:function(t){var n,e,r,o,i,u=Fo(this),c=No(u,0),a=0;for(n=-1,r=arguments.length;n<r;n++)if(_o(i=-1===n?u:arguments[n]))for(o=Co(i),Mo(a+o),e=0;e<o;e++,a++)e in i&&Lo(c,a,i[e]);else Mo(a+1),Lo(c,a++,i);return c.length=a,c}}),e.default.fn.bootstrapTable.locales["de-DE"]=e.default.fn.bootstrapTable.locales.de={formatCopyRows:function(){return"Zeilen kopieren"},formatPrint:function(){return"Drucken"},formatLoadingMessage:function(){return"Lade, bitte warten"},formatRecordsPerPage:function(t){return"".concat(t," Zeilen pro Seite.")},formatShowingRows:function(t,n,e,r){return void 0!==r&&r>0&&r>e?"Zeige Zeile ".concat(t," bis ").concat(n," von ").concat(e," Zeile").concat(e>1?"n":""," (Gefiltert von ").concat(r," Zeile").concat(r>1?"n":"",")"):"Zeige Zeile ".concat(t," bis ").concat(n," von ").concat(e," Zeile").concat(e>1?"n":"",".")},formatSRPaginationPreText:function(){return"Vorherige Seite"},formatSRPaginationPageText:function(t){return"Zu Seite ".concat(t)},formatSRPaginationNextText:function(){return"Nächste Seite"},formatDetailPagination:function(t){return"Zeige ".concat(t," Zeile").concat(t>1?"n":"",".")},formatClearSearch:function(){return"Lösche Filter"},formatSearch:function(){return"Suchen"},formatNoMatches:function(){return"Keine passenden Ergebnisse gefunden"},formatPaginationSwitch:function(){return"Verstecke/Zeige Nummerierung"},formatPaginationSwitchDown:function(){return"Zeige Nummerierung"},formatPaginationSwitchUp:function(){return"Verstecke Nummerierung"},formatRefresh:function(){return"Neu laden"},formatToggleOn:function(){return"Normale Ansicht"},formatToggleOff:function(){return"Kartenansicht"},formatColumns:function(){return"Spalten"},formatColumnsToggleAll:function(){return"Alle umschalten"},formatFullscreen:function(){return"Vollbild"},formatAllRows:function(){return"Alle"},formatAutoRefresh:function(){return"Automatisches Neuladen"},formatExport:function(){return"Datenexport"},formatJumpTo:function(){return"Springen"},formatAdvancedSearch:function(){return"Erweiterte Suche"},formatAdvancedCloseButton:function(){return"Schließen"},formatFilterControlSwitch:function(){return"Verstecke/Zeige Filter"},formatFilterControlSwitchHide:function(){return"Verstecke Filter"},formatFilterControlSwitchShow:function(){return"Zeige Filter"},formatAddLevel:function(){return"Ebene hinzufügen"},formatCancel:function(){return"Abbrechen"},formatColumn:function(){return"Spalte"},formatDeleteLevel:function(){return"Ebene entfernen"},formatDuplicateAlertTitle:function(){return"Doppelte Einträge gefunden!"},formatDuplicateAlertDescription:function(){return"Bitte doppelte Spalten entfenen oder ändern"},formatMultipleSort:function(){return"Mehrfachsortierung"},formatOrder:function(){return"Reihenfolge"},formatSort:function(){return"Sortieren"},formatSortBy:function(){return"Sortieren nach"},formatThenBy:function(){return"anschließend"},formatSortOrders:function(){return{asc:"Aufsteigend",desc:"Absteigend"}}},e.default.extend(e.default.fn.bootstrapTable.defaults,e.default.fn.bootstrapTable.locales["de-DE"])}));
