/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},y={},g={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!g.call({1:2},1);y.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:g;var v,h,m=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},w=f,S=Function.prototype,O=S.call,j=w&&S.bind.bind(O,O),P=function(t){return w?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),x=T("".slice),E=function(t){return x(A(t),8,-1)},C=E,F=P,M=function(t){if("Function"===C(t))return F(t)},R=c,I=E,k=Object,D=M("".split),L=R((function(){return!k("z").propertyIsEnumerable(0)}))?function(t){return"String"==I(t)?D(t,""):k(t)}:k,N=function(t){return null==t},H=N,_=TypeError,z=function(t){if(H(t))throw _("Can't call method on "+t);return t},G=L,q=z,B=function(t){return G(q(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},K=W.all,V=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===K}:function(t){return"function"==typeof t},J=V,Q=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:J(t)||t===Q}:function(t){return"object"==typeof t?null!==t:J(t)},Y=i,$=V,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=M({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(h=(v=ct.split("."))[0]>0&&v[0]<4?1:+(v[0]+v[1])),!h&&et&&(!(v=et.match(/Edge\/(\d+)/))||v[1]>=74)&&(v=et.match(/Chrome\/(\d+)/))&&(h=+v[1]);var at=h,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,yt=tt,gt=V,bt=nt,dt=Object,vt=pt?function(t){return"symbol"==typeof t}:function(t){var n=yt("Symbol");return gt(n)&&bt(n.prototype,dt(t))},ht=String,mt=V,wt=function(t){try{return ht(t)}catch(t){return"Object"}},St=TypeError,Ot=function(t){if(mt(t))return t;throw St(wt(t)+" is not a function")},jt=N,Pt=p,Tt=V,At=X,xt=TypeError,Et={exports:{}},Ct=i,Ft=Object.defineProperty,Mt=function(t,n){try{Ft(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Rt=Mt,It="__core-js_shared__",kt=i[It]||Rt(It,{}),Dt=kt;(Et.exports=function(t,n){return Dt[t]||(Dt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Lt=z,Nt=Object,Ht=function(t){return Nt(Lt(t))},_t=Ht,zt=M({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return zt(_t(t),n)},qt=M,Bt=0,Ut=Math.random(),Wt=qt(1..toString),Kt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Bt+Ut,36)},Vt=i,Jt=Et.exports,Qt=Gt,Xt=Kt,Yt=st,$t=pt,Zt=Jt("wks"),tn=Vt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Qt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Qt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,cn=vt,an=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},yn=vt,gn=function(t){var n=pn(t,"string");return yn(n)?n:n+""},bn=X,dn=i.document,vn=bn(dn)&&bn(dn.createElement),hn=function(t){return vn?dn.createElement(t):{}},mn=!a&&!c((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),wn=a,Sn=p,On=y,jn=m,Pn=B,Tn=gn,An=Gt,xn=mn,En=Object.getOwnPropertyDescriptor;u.f=wn?En:function(t,n){if(t=Pn(t),n=Tn(n),xn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!Sn(On.f,t,n),t[n])};var Cn={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mn=X,Rn=String,In=TypeError,kn=function(t){if(Mn(t))return t;throw In(Rn(t)+" is not an object")},Dn=a,Ln=mn,Nn=Fn,Hn=kn,_n=gn,zn=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Un="configurable",Wn="writable";Cn.f=Dn?Nn?function(t,n,r){if(Hn(t),n=_n(n),Hn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(Hn(t),n=_n(n),Hn(r),Ln)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw zn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Kn=Cn,Vn=m,Jn=a?function(t,n,r){return Kn.f(t,n,Vn(1,r))}:function(t,n,r){return t[n]=r,t},Qn={exports:{}},Xn=a,Yn=Gt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=V,er=kt,or=M(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=V,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,yr=Kt,gr=pr("keys"),br={},dr=sr,vr=i,hr=X,mr=Jn,wr=Gt,Sr=kt,Or=function(t){return gr[t]||(gr[t]=yr(t))},jr=br,Pr="Object already initialized",Tr=vr.TypeError,Ar=vr.WeakMap;if(dr||Sr.state){var xr=Sr.state||(Sr.state=new Ar);xr.get=xr.get,xr.has=xr.has,xr.set=xr.set,ir=function(t,n){if(xr.has(t))throw Tr(Pr);return n.facade=t,xr.set(t,n),n},ur=function(t){return xr.get(t)||{}},cr=function(t){return xr.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(wr(t,Er))throw Tr(Pr);return n.facade=t,mr(t,Er,n),n},ur=function(t){return wr(t,Er)?t[Er]:{}},cr=function(t){return wr(t,Er)}}var Cr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=c,Mr=V,Rr=Gt,Ir=a,kr=nr.CONFIGURABLE,Dr=ar,Lr=Cr.enforce,Nr=Cr.get,Hr=Object.defineProperty,_r=Ir&&!Fr((function(){return 8!==Hr((function(){}),"length",{value:8}).length})),zr=String(String).split("String"),Gr=Qn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Rr(t,"name")||kr&&t.name!==n)&&(Ir?Hr(t,"name",{value:n,configurable:!0}):t.name=n),_r&&r&&Rr(r,"arity")&&t.length!==r.arity&&Hr(t,"length",{value:r.arity});try{r&&Rr(r,"constructor")&&r.constructor?Ir&&Hr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Lr(t);return Rr(e,"source")||(e.source=zr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Mr(this)&&Nr(this).source||Dr(this)}),"toString");var qr=V,Br=Cn,Ur=Qn.exports,Wr=Mt,Kr={},Vr=Math.ceil,Jr=Math.floor,Qr=Math.trunc||function(t){var n=+t;return(n>0?Jr:Vr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Qr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=Gt,le=B,se=ae.indexOf,pe=br,ye=M([].push),ge=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ye(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ye(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Kr.f=Object.getOwnPropertyNames||function(t){return ge(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var ve=tt,he=Kr,me=de,we=kn,Se=M([].concat),Oe=ve("Reflect","ownKeys")||function(t){var n=he.f(we(t)),r=me.f;return r?Se(n,r(t)):n},je=Gt,Pe=Oe,Te=u,Ae=Cn,xe=c,Ee=V,Ce=/#|\.prototype\./,Fe=function(t,n){var r=Re[Me(t)];return r==ke||r!=Ie&&(Ee(n)?xe(n):!!n)},Me=Fe.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Re=Fe.data={},Ie=Fe.NATIVE="N",ke=Fe.POLYFILL="P",De=Fe,Le=i,Ne=u.f,He=Jn,_e=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},ze=Mt,Ge=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var c=e[u];je(t,c)||r&&je(r,c)||o(t,c,i(n,c))}},qe=De,Be=E,Ue=Array.isArray||function(t){return"Array"==Be(t)},We=TypeError,Ke=gn,Ve=Cn,Je=m,Qe={};Qe[en("toStringTag")]="z";var Xe="[object z]"===String(Qe),Ye=V,$e=E,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=M,eo=c,oo=V,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),yo=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},go=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};go.sham=!0;var bo=!fo||eo((function(){var t;return yo(yo.call)||!yo(Object)||!yo((function(){t=!0}))||t}))?go:yo,vo=Ue,ho=bo,mo=X,wo=en("species"),So=Array,Oo=function(t){var n;return vo(t)&&(n=t.constructor,(ho(n)&&(n===So||vo(n.prototype))||mo(n)&&null===(n=n[wo]))&&(n=void 0)),void 0===n?So:n},jo=c,Po=at,To=en("species"),Ao=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Le:f?Le[c]||ze(c,{}):(Le[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Ne(r,e))&&u.value:r[e],!qe(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&He(i,"sham",!0),_e(r,e,i,t)}},xo=c,Eo=Ue,Co=X,Fo=Ht,Mo=ee,Ro=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Io=function(t,n,r){var e=Ke(n);e in t?Ve.f(t,e,Je(0,r)):t[e]=r},ko=function(t,n){return new(Oo(t))(0===n?0:n)},Do=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=at,No=en("isConcatSpreadable"),Ho=Lo>=51||!xo((function(){var t=[];return t[No]=!1,t.concat()[0]!==t})),_o=Do("concat"),zo=function(t){if(!Co(t))return!1;var n=t[No];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!Ho||!_o},{concat:function(t){var n,r,e,o,i,u=Fo(this),c=ko(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(zo(i=-1===n?u:arguments[n]))for(o=Mo(i),Ro(a+o),r=0;r<o;r++,a++)r in i&&Io(c,a,i[r]);else Ro(a+1),Io(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["fi-FI"]=r.default.fn.bootstrapTable.locales.fi={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Ladataan, ole hyvä ja odota"},formatRecordsPerPage:function(t){return"".concat(t," riviä sivulla")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Näytetään rivit ".concat(t," - ").concat(n," / ").concat(r," (filtered from ").concat(e," total rows)"):"Näytetään rivit ".concat(t," - ").concat(n," / ").concat(r)},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Poista suodattimet"},formatSearch:function(){return"Hae"},formatNoMatches:function(){return"Hakuehtoja vastaavia tuloksia ei löytynyt"},formatPaginationSwitch:function(){return"Näytä/Piilota sivutus"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Päivitä"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Sarakkeet"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Kaikki"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Vie tiedot"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fi-FI"])}));
