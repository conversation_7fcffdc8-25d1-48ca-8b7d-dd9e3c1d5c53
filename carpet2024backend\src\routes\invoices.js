const express = require('express');
const router = express.Router();
const { MongoClient, ObjectId } = require('mongodb');

// MongoDB connection string
const uri = "mongodb+srv://infosarthaktech:<EMAIL>/test?retryWrites=true&w=majority";

// Create a new invoice directly in MongoDB
router.post('/', async (req, res) => {
  const client = new MongoClient(uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    serverSelectionTimeoutMS: 5000 // Timeout after 5s instead of 30s
  });

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB successfully');

    const database = req.body.database || 'test';
    const collection = req.body.collection || 'invoices';

    console.log(`Using database: ${database}, collection: ${collection}`);

    // Remove database and collection fields from the data to be saved
    const invoiceData = { ...req.body };
    delete invoiceData.database;
    delete invoiceData.collection;

    // Add timestamp
    invoiceData.createdAt = new Date();

    console.log('Saving invoice data to MongoDB...');
    const result = await client.db(database).collection(collection).insertOne(invoiceData);

    console.log(`Invoice saved to MongoDB with ID: ${result.insertedId}`);

    res.status(201).json({
      _id: result.insertedId,
      message: 'Invoice saved successfully to MongoDB'
    });
  } catch (error) {
    console.error('Error saving invoice to MongoDB:', error);

    // Check for specific MongoDB connection errors
    if (error.name === 'MongoServerSelectionError') {
      console.error('Could not connect to MongoDB server. Please check your connection string and network.');
    }

    res.status(500).json({
      message: 'Error saving invoice to MongoDB',
      error: error.message,
      stack: error.stack
    });
  } finally {
    try {
      await client.close();
      console.log('MongoDB connection closed');
    } catch (err) {
      console.error('Error closing MongoDB connection:', err);
    }
  }
});

// Get all invoices from MongoDB
router.get('/', async (req, res) => {
  const client = new MongoClient(uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    serverSelectionTimeoutMS: 5000 // Timeout after 5s instead of 30s
  });

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB successfully');

    const database = req.query.database || 'test';
    const collection = req.query.collection || 'invoices';

    console.log(`Fetching invoices from database: ${database}, collection: ${collection}`);
    const invoices = await client.db(database).collection(collection).find({}).toArray();

    console.log(`Found ${invoices.length} invoices`);
    res.status(200).json(invoices);
  } catch (error) {
    console.error('Error fetching invoices from MongoDB:', error);

    // Check for specific MongoDB connection errors
    if (error.name === 'MongoServerSelectionError') {
      console.error('Could not connect to MongoDB server. Please check your connection string and network.');
    }

    res.status(500).json({
      message: 'Error fetching invoices from MongoDB',
      error: error.message,
      stack: error.stack
    });
  } finally {
    try {
      await client.close();
      console.log('MongoDB connection closed');
    } catch (err) {
      console.error('Error closing MongoDB connection:', err);
    }
  }
});

// Get invoice by ID from MongoDB
router.get('/:id', async (req, res) => {
  const client = new MongoClient(uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    serverSelectionTimeoutMS: 5000 // Timeout after 5s instead of 30s
  });

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB successfully');

    const database = req.query.database || 'test';
    const collection = req.query.collection || 'invoices';

    console.log(`Fetching invoice with ID: ${req.params.id} from database: ${database}, collection: ${collection}`);
    const invoice = await client.db(database).collection(collection).findOne({ _id: new ObjectId(req.params.id) });

    if (!invoice) {
      console.log(`Invoice with ID: ${req.params.id} not found`);
      return res.status(404).json({ message: 'Invoice not found' });
    }

    console.log(`Found invoice with ID: ${req.params.id}`);
    res.status(200).json(invoice);
  } catch (error) {
    console.error('Error fetching invoice from MongoDB:', error);

    // Check for specific MongoDB connection errors
    if (error.name === 'MongoServerSelectionError') {
      console.error('Could not connect to MongoDB server. Please check your connection string and network.');
    }

    res.status(500).json({
      message: 'Error fetching invoice from MongoDB',
      error: error.message,
      stack: error.stack
    });
  } finally {
    try {
      await client.close();
      console.log('MongoDB connection closed');
    } catch (err) {
      console.error('Error closing MongoDB connection:', err);
    }
  }
});

module.exports = router;
