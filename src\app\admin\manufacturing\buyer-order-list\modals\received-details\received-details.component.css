fieldset {
  font-family: sans-serif;
  border: 2px solid #1F497D;
  background: #ffffff;
  border-radius: 5px;
  padding: 15px;
}

fieldset legend {
  background: #ffffff;
  color: #000000;
  padding: 5px 10px ;
  font-size: 20px;
  border-radius: 5px;
  /* box-shadow: 0 0 0 5px #ddd; */
  margin-left: 20px;
}

legend {
  float: left;
  width: auto;
  padding: 0;
  margin-top: -32px;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + .3vw);
  line-height: inherit;
}
.ex-width{
  width:100%;
}
.space{
  display: flex;
  justify-content:space-between;

}
.mat-mdc-header-cell {
  background-color: #f5f5f5;
  color: #1F497D;
  font-weight: bold;
  font-size: 14px;
}

.mat-mdc-row:nth-child(even) {
  background-color: #f9f9f9;
}

.mat-mdc-row:hover {
  background-color: #f0f0f0;
}

/* Styling for amount and rate columns */
.mat-column-amount,
.mat-column-rate {
 
  text-align: right !important;
  padding-right: 24px !important;
}

/* Add some padding to all cells for better spacing */
.mat-mdc-cell,
.mat-mdc-header-cell {
  padding: 8px 16px !important;
}

/* Add a subtle border between rows */
.mat-mdc-row {
  border-bottom: 1px solid #e0e0e0;
}

/* Style for the paginator */
.mat-mdc-paginator {
  background-color: #f5f5f5;

}

.scroll-container{
    scroll-behavior: smooth !important;
    overflow-x: scroll !important;
    overflow-y: scroll !important;
  }
 
.scroll-container::-webkit-scrollbar {
    height: 9px !important;
    width: 9px !important;
  }
.scroll-container::-webkit-scrollbar-thumb {
    background: #808080;
  }


.title {
    color: #1f497d;
    display: flex;
    justify-content: center;
    text-decoration: underline;
}

  .mat-column-size {
    text-align: center !important;
  }
  .mat-column-quality{
    text-align: center !important;
  }