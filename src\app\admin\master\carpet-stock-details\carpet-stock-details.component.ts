import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialog } from '@angular/material/dialog';
import { StockModelDialogComponent } from '../stock-model-dialog/stock-model-dialog.component';
export interface PeriodicElement {
  index: number;
  GerCarpetNo: string;
  QualityDesign: string;
  Color: string;
  CCode: string;
  QCode: string;
  Size: string;
  SCore: string;
  Area: string;
  EvKPrice: string;
  Amount: string;
  InvoiceNo: string;
  status: string;
  id: string;
  Date: string;
}

const ELEMENT_DATA: PeriodicElement[] = [];
@Component({
  selector: 'app-carpet-stock-details',
  templateUrl: './carpet-stock-details.component.html',
  styleUrl: './carpet-stock-details.component.css',
})
export class CarpetStockDetailsComponent implements OnInit {
  constructor(
    private activeRoute: Router,
    private customeService: CustomeServiceService,
    public matDig: MatDialog
  ) {}
  data: any[] | undefined;
  displayedColumns: string[] = [
    'id',
    'GerCarpetNo',
    'QualityDesign',
    'QCode',
    'Color',
    'CCode',

    'Size',
    'SCore',
    'Area',
    'EvKPrice',
    'Amount',
    'InvoiceNo',

    'Date',
    'select',
  ];
  dataSource = new MatTableDataSource<PeriodicElement>(ELEMENT_DATA);
  ngOnInit(): void {
    this.getCarpetData();
  }
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  selection = new SelectionModel<PeriodicElement>(true, []);
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }
  getCarpetData() {
    let data = this.customeService.getData();
    debugger;
    ELEMENT_DATA.length=0;
    data.map((v: any, i: number) => {
      debugger;

      let date = this.customeService.convertDate(v.Date);
      ELEMENT_DATA.push({
        index: i + 1,
        GerCarpetNo: v.GerCarpetNo,
        QualityDesign: v.QualityDesign,
        Color: v.Color,
        CCode: v.CCode,
        QCode: v.QCode,
        Size: v.Size,
        SCore: v.SCore,
        Area: v.Area,
        EvKPrice: v.EvKPrice,
        Amount: v.Amount,
        InvoiceNo: v.InvoiceNo,
        status: v.status,
        id: v._id,
        Date: date,
      });
    });

    this.dataSource = new MatTableDataSource(ELEMENT_DATA);
    this.ngAfterViewInit();
    return;
    debugger;
  }

  async masterToggle() {
  await  this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

 async isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  async logSelection(row: PeriodicElement) {
    console.log('Selected row:', row);

    let dialogRef = this.matDig.open(StockModelDialogComponent, {
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      debugger;
console.log('result result result',result)
    });
  }
}
