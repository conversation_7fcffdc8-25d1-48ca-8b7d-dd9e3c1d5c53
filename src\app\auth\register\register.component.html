
<div class="bglogin row vh-100 d-flex justify-content-center align-items-center" >

  <div class="col-md-4 card card-sign">
    <div class="card-header">

      <h3 class="card-title">Rachin Export</h3>
      <p class="card-text">Welcome back! Please signin to continue.</p>
    </div><!-- card-header -->
   <form [formGroup]="frmRegister" (ngSubmit)="register()">
    <div class="card-body">
      <div class="mb-4">
        <label class="form-label">Name</label>
        <input type="text" class="form-control" placeholder="Enter your name" formControlName="name">
      </div>
      <div class="mb-4">
        <label class="form-label">E-mail</label>
        <input type="text" class="form-control" placeholder="Enter your email address" formControlName="email">
      </div>
      <div class="mb-4">
        <label class="form-label d-flex justify-content-between">Password </label>
        <input type="password" class="form-control" placeholder="Enter your password" formControlName="password">
      </div>
      <div class="mb-4">
        <input type="checkbox" name="Remember" id=""> Remember me


      </div>
      <!-- <button  class="btn btn-primary btn-sign">Sign Up</button> -->
<button class="btn btn-primary">Sign Up</button>
      <div class="mb-4 mt-2">
        <label class="form-label d-flex "> <a routerLink="/login">Sign In</a></label>

      </div>


    </div>
   </form><!-- card-body -->

  </div><!-- card -->

  </div>
