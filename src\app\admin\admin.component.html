<!-- Begin page -->
<div class="layout-wrapper">

      
    
    <app-admin-sidebar-new [isOpen]="sideOpen" (toggle)="toggleSideBar()"></app-admin-sidebar-new>
    
    <!-- ============================================================== -->
    <!-- Start Page Content here -->
    <!-- ============================================================== -->

    <div class="page-content">

        <!-- ========== Topbar Start ========== -->
        <app-admin-header [isOpen]="sideOpen" (toggle)="toggleSideBar1()"></app-admin-header>
        <!-- ========== Topbar End ========== -->
        <div class="scroll-page">
        <div class="px-3">
            <router-outlet></router-outlet>
            <ngx-ui-loader></ngx-ui-loader>
        </div> <!-- content -->

        <!-- Footer Start -->
        <app-admin-footer></app-admin-footer>
        <!-- end Footer -->
    </div>

    </div>

    <!-- ============================================================== -->
    <!-- End Page content -->
    <!-- ============================================================== -->

 
<!-- END wrapper --></div>