.quality-modal-container {
  width: 100%;
  max-width: 1000px;
}

.modal-title {
  color: #1f497d;
  text-align: center;
  text-decoration: underline;
  margin-bottom: 20px;
}

.modal-content {
  padding: 20px;
 
}

.quality-comparison-container {
  display: flex;
  gap: 20px;
  height: 100%;
}

.new-quality-section,
.old-quality-section {
  flex: 1;
}

.section-card {
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  color: #1f497d;
  font-size: 16px;
  font-weight: 600;
}

/* New Quality Section Styles */
.quality-inputs {
  max-height: 400px;
  overflow-y: auto;
}

.quality-input-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 3px;
  margin-top: 5px;
}

.input-group {
  display: flex;
  gap: 10px;
  flex: 1;
}

.quality-input,
.design-input {
  flex: 1;
}

.remove-btn {
  flex-shrink: 0;
}

.add-quality-section {
  margin-top: 15px;
  text-align: center;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Old Quality Section Styles */
.old-quality-table-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.old-quality-table {
  width: 100%;
}

.old-quality-table th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #333;
}

.old-quality-table td,
.old-quality-table th {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.no-data-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-style: italic;
}

/* Modal Actions */
.modal-actions {
  padding: 20px;
  border-top: 1px solid #e0e0e0;
}

.cancel-btn {
  margin-right: 10px;
}

.save-btn {
  background-color: #1f497d;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .quality-comparison-container {
    flex-direction: column;
  }

  .modal-content {
    padding: 15px;
  }

  .quality-modal-container {
    max-width: 95vw;
  }
}

/* Scrollbar Styling */
.quality-inputs::-webkit-scrollbar,
.old-quality-table-container::-webkit-scrollbar {
  width: 6px;
}

.quality-inputs::-webkit-scrollbar-track,
.old-quality-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.quality-inputs::-webkit-scrollbar-thumb,
.old-quality-table-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.quality-inputs::-webkit-scrollbar-thumb:hover,
.old-quality-table-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Form Field Styling */
mat-form-field {
  width: 100%;
}

mat-label {
  color: #333;
}

/* Table Row Hover Effect */
.old-quality-table tr:hover {
  background-color: #f9f9f9;
}

/* Icon Styling */
mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}