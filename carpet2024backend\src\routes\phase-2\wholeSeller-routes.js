// routes/wholeSellerRoutes.js

const express = require('express');
const router = express.Router();
const wholeSellerController = require('../../controller/wholesellerController');
// Create
router.post('/create-wholeseller', wholeSellerController.createWholeSeller);
// Read
router.get('/get-wholeseller/:id', wholeSellerController.getWholeSellerById);
router.get('/getAll-wholeseller', wholeSellerController.getAllWholeSellers);
// Update
router.put('/update-wholeseller/:id', wholeSellerController.updateWholeSeller);
// Delete
router.delete('/delete-wholeseller/:id', wholeSellerController.deleteWholeSeller);

module.exports = router;
