// middleware/validateSizeData.js
const validateSizeData = (req, res, next) => {
    const { sizeInYard, areaInYard, areaInfeet, khapSize, sizeinMeter, sqMeter, srNo } = req.body;
    
    if (!sizeInYard || !areaInYard || !areaInfeet || !khapSize || !sizeinMeter || !sqMeter || srNo === undefined) {
      return res.status(400).json({ error: 'Invalid size data - Missing fields' });
    }
  
    // Additional validation checks can be added here
    // For example, you might want to ensure the unit values are correct
    const unitEnum = ['cm', 'ft'];
    if (!unitEnum.includes(areaInfeet) || !unitEnum.includes(sizeinMeter) || !unitEnum.includes(sqMeter)) {
      return res.status(400).json({ error: 'Invalid size data - Invalid unit of measurement' });
    }
  
    next();
  };
  
  module.exports = validateSizeData;
  