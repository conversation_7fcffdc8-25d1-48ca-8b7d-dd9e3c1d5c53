// Script to find detailed mapping of which carpets belong to which issue numbers
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function getDetailedIssueCarpetMapping() {
  console.log('🔍 DETAILED ISSUE TO CARPET MAPPING');
  console.log('='.repeat(80));
  
  try {
    const db = mongoose.connection.db;
    
    // Get actual issues from carpetorderissues collection
    const issuesCollection = db.collection('carpetorderissues');
    const actualIssues = await issuesCollection.find({}).sort({ Br_issueNo: 1 }).toArray();
    
    // Get all carpet records
    const carpetsCollection = db.collection('carpetreceiveds');
    const allCarpets = await carpetsCollection.find({ receiveNo: { $regex: /^K-/ } }).sort({ receiveNo: 1 }).toArray();
    
    console.log(`📊 Total actual issues: ${actualIssues.length}`);
    console.log(`📊 Total carpet records: ${allCarpets.length}`);
    
    // Create detailed mapping
    const issueMapping = {};
    
    // Initialize mapping with actual issues
    actualIssues.forEach(issue => {
      const issueNo = issue.Br_issueNo;
      issueMapping[issueNo] = {
        issueDetails: {
          issueNo: issueNo,
          date: issue.date,
          weaver: issue.weaver,
          quality: issue.quality,
          design: issue.design,
          size: issue.size,
          rate: issue.rate,
          amount: issue.amount,
          area: issue.area,
          pcs: issue.pcs,
          pcsReceived: issue.PCSReceived || 0,
          pcsWaiting: issue.PCSWaitingToBeReceived || 0
        },
        carpets: []
      };
    });
    
    // Map carpets to issues
    allCarpets.forEach(carpet => {
      const carpetIssueNo = carpet.issueNo?.Br_issueNo || carpet.receiveNo;
      
      if (issueMapping[carpetIssueNo]) {
        // Carpet belongs to an actual issue
        issueMapping[carpetIssueNo].carpets.push({
          carpetNo: carpet.receiveNo,
          carpetWeaver: carpet.weaverName,
          carpetDesign: carpet.design,
          carpetQuality: carpet.quality,
          carpetArea: carpet.area,
          carpetAmount: carpet.amount,
          carpetRate: carpet.issueNo?.rate,
          receivingDate: carpet.receivingDate,
          hasActualIssue: true
        });
      } else {
        // Carpet doesn't have a corresponding issue
        if (!issueMapping['NO_ISSUE']) {
          issueMapping['NO_ISSUE'] = {
            issueDetails: {
              issueNo: 'NO_ISSUE',
              note: 'Carpets without corresponding issues'
            },
            carpets: []
          };
        }
        
        issueMapping['NO_ISSUE'].carpets.push({
          carpetNo: carpet.receiveNo,
          carpetWeaver: carpet.weaverName,
          carpetDesign: carpet.design,
          carpetQuality: carpet.quality,
          carpetArea: carpet.area,
          carpetAmount: carpet.amount,
          carpetRate: carpet.issueNo?.rate,
          receivingDate: carpet.receivingDate,
          hasActualIssue: false
        });
      }
    });
    
    // Display detailed mapping
    console.log('\n📋 DETAILED ISSUE TO CARPET MAPPING:');
    console.log('='.repeat(100));
    
    const sortedIssues = Object.keys(issueMapping).sort((a, b) => {
      if (a === 'NO_ISSUE') return 1;
      if (b === 'NO_ISSUE') return -1;
      return a.localeCompare(b);
    });
    
    sortedIssues.forEach(issueNo => {
      const mapping = issueMapping[issueNo];
      const issue = mapping.issueDetails;
      const carpets = mapping.carpets;
      
      if (issueNo === 'NO_ISSUE') {
        console.log(`\n❌ ${issueNo} (${carpets.length} carpets):`);
        console.log('   📝 These carpets don\'t have corresponding issues in carpetorderissues collection');
      } else {
        console.log(`\n✅ ${issueNo} (${carpets.length} carpets):`);
        console.log(`   📝 Issue Details:`);
        console.log(`      Date: ${issue.date ? new Date(issue.date).toISOString().split('T')[0] : 'N/A'}`);
        console.log(`      Weaver: ${issue.weaver?.name || issue.weaver || 'N/A'}`);
        console.log(`      Quality: ${issue.quality?.quality || issue.quality || 'N/A'}`);
        console.log(`      Design: ${issue.design?.design || issue.design || 'N/A'}`);
        console.log(`      Size: ${issue.size?.sizeInYard || issue.size || 'N/A'}`);
        console.log(`      Rate: ₹${issue.rate || 'N/A'}/Sq.Ft`);
        console.log(`      Amount: ₹${issue.amount || 'N/A'}`);
        console.log(`      Area: ${issue.area || 'N/A'}`);
        console.log(`      PCS Ordered: ${issue.pcs || 'N/A'}`);
        console.log(`      PCS Received: ${issue.pcsReceived || 0}`);
        console.log(`      PCS Waiting: ${issue.pcsWaiting || 0}`);
      }
      
      console.log(`   🧩 Carpets (${carpets.length}):`);
      carpets.forEach((carpet, index) => {
        console.log(`      ${index + 1}. ${carpet.carpetNo}`);
        console.log(`         Weaver: ${carpet.carpetWeaver || 'N/A'}`);
        console.log(`         Design: ${carpet.carpetDesign || 'N/A'} | Quality: ${carpet.carpetQuality || 'N/A'}`);
        console.log(`         Area: ${carpet.carpetArea || 'N/A'} | Amount: ₹${carpet.carpetAmount || 'N/A'}`);
        console.log(`         Rate: ₹${carpet.carpetRate || 'N/A'}/Sq.Ft`);
        console.log(`         Received: ${carpet.receivingDate ? new Date(carpet.receivingDate).toISOString().split('T')[0] : 'N/A'}`);
      });
      
      console.log('   ' + '-'.repeat(90));
    });
    
    // Summary statistics
    console.log('\n📊 MAPPING SUMMARY:');
    console.log('='.repeat(50));
    
    let totalCarpetsWithIssues = 0;
    let totalCarpetsWithoutIssues = 0;
    let issuesWithMultipleCarpets = 0;
    let issuesWithNoCarpets = 0;
    
    sortedIssues.forEach(issueNo => {
      const carpetCount = issueMapping[issueNo].carpets.length;
      
      if (issueNo === 'NO_ISSUE') {
        totalCarpetsWithoutIssues = carpetCount;
      } else {
        totalCarpetsWithIssues += carpetCount;
        if (carpetCount > 1) issuesWithMultipleCarpets++;
        if (carpetCount === 0) issuesWithNoCarpets++;
      }
    });
    
    console.log(`📊 Total issues with carpets: ${actualIssues.length}`);
    console.log(`📊 Carpets with actual issues: ${totalCarpetsWithIssues}`);
    console.log(`📊 Carpets without actual issues: ${totalCarpetsWithoutIssues}`);
    console.log(`📊 Issues with multiple carpets: ${issuesWithMultipleCarpets}`);
    console.log(`📊 Issues with no carpets: ${issuesWithNoCarpets}`);
    
    return issueMapping;
    
  } catch (error) {
    console.error('❌ Error getting detailed mapping:', error);
    return null;
  }
}

async function main() {
  console.log('🔍 FINDING DETAILED ISSUE TO CARPET MAPPING');
  console.log('(Which carpets belong to which issue numbers)');
  console.log('='.repeat(80));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Get detailed mapping
    const mapping = await getDetailedIssueCarpetMapping();

    if (mapping) {
      console.log('\n🎯 ANALYSIS COMPLETE!');
      console.log('✅ Found detailed mapping of issues to carpets');
      console.log('✅ Identified carpets with and without actual issues');
      console.log('✅ Ready for next steps based on your requirements');
    }

  } catch (error) {
    console.error('❌ Analysis failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 DETAILED MAPPING ANALYSIS COMPLETE!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
