import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ContainerReceivedComponent } from './container-received.component';


describe('ContainerReceivedComponent', () => {
  let component: ContainerReceivedComponent;
  let fixture: ComponentFixture<ContainerReceivedComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ContainerReceivedComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(ContainerReceivedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
