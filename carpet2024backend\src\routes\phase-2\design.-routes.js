const express = require('express');
const AddDesignController = require('../../controller/design-controller');
const router = express.Router();
const addDesignController = new AddDesignController();
const uploadFiles = require('../../middleware/allfile-middleware'); // Import the modified upload middleware

router.post('/add-design', uploadFiles, (req, res) => addDesignController.createDesign(req, res));
router.get('/designs', (req, res) => addDesignController.getAllDesigns(req, res));
router.get('/designs/:id', (req, res) => addDesignController.getDesignById(req, res));
router.put('/designs/:id', uploadFiles, (req, res) => addDesignController.updateDesign(req, res));
router.delete('/designs/:id', (req, res) => addDesignController.deleteDesign(req, res));

module.exports = router;
