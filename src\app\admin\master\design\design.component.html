<div class="container mt-4" *ngIf="isOpenDialog">
  <section>
    <fieldset>
      <legend><b>Add Design</b></legend>
      <form [formGroup]="designform" (ngSubmit)="designFormSubmit()">
        <div class="row">
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label> Production Quality</mat-label>
              <mat-select formControlName="productQuality" (valueChange)="selectedQuality($event)">
                <mat-option
                  *ngFor="let val of qualityList"
                  [value]="val.id"
                  >{{ val.quality }}</mat-option
                >
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Design</mat-label>
              <input formControlName="design" matInput placeholder="Design" />
            </mat-form-field>
          </div>

            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label> Ground</mat-label>
                <mat-select formControlName="ground">
                  <mat-option
                    *ngFor="let val of groundList"
                    [value]="val._id"
                    >{{ val.newColor }}</mat-option
                  >
                </mat-select>
              </mat-form-field>
            </div>


            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Border</mat-label>
                <mat-select formControlName="border">
                  <mat-option
                    *ngFor="let val of groundList"
                    [value]="val._id"
                    >{{ val.newColor }}</mat-option
                  >
                </mat-select>
              </mat-form-field>
            </div>
            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>No of colors</mat-label>
                <input formControlName="noOfcolour" (blur)="handleBlurEvent($event)"  matInput placeholder="No of colors" />
              </mat-form-field>
            </div>
          <div class="mb-2 col-md-3">
            <label class="form-label"><b>Upload File (.pdf)</b> </label
            ><input
              type="file"
              (change)="file($event)"
              accept=".pdf"
              class="form-control"
            />
          </div>

          <div formArrayName="colors" class="scrollable-container">
            <div *ngFor="let color of colors.controls; let i = index" [formGroupName]="i">

              <div class="row">
                 <div class="col-md-6">
                  <mat-form-field appearance="outline" class="ex-width">
                    <mat-label> Color</mat-label>
                    <mat-select formControlName="colorName">
                      <mat-option
                        *ngFor="let val of colorsList"
                        [value]="val._id"
                        >{{ val.newColor|titlecase  }} - {{val.companyColorCode}} - {{val.remark|titlecase }}</mat-option
                      >
                    </mat-select>
                  </mat-form-field>


              </div>


              <div class="col-md-3">
                <mat-form-field appearance="outline" class="ex-width">
                  <mat-label>Lagat</mat-label>
                  <input formControlName="lagat" type="number" matInput placeholder="Lagat" (blur)="handleCalc(i)"/>
                </mat-form-field>
              </div>
              <div class="col-md-3" style="margin-top: 11px;">
                <button type="button" mat-flat-button color="primary" (click)="removeColor(i)">Remove</button>
              </div>

              </div>

            </div>
          </div>
          <div class="mb-2 mt-4 col-md-2">
            <button mat-flat-button color="primary" type="submit" [disabled]="!designform.valid" >
              {{ isEditMode ? "Update" : "Add" }}
            </button>
          </div>
          <div class="mb-2 mt-4 col-md-3">Total Lagat: {{ totalLagat  | number: '1.3-3'}}</div>
        </div>
      </form>
    </fieldset>
  </section>
</div>
<div class="container mt-4">
  <section>
    <fieldset>
      <legend><b>List</b></legend>
      <div class="row">
        <div class="col-md-3"> <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input
            matInput
            (keyup)="applyFilter($event)"
            placeholder="Ex. Jack"
            #input

          />
        </mat-form-field></div>
        <div class="col-12">


          <div class="mat-table-container">
            <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8" >
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="sticky-header">
                  Sr.No.
                </th>
                <td mat-cell *matCellDef="let row; let i = index">
                  {{ getSerialNumber(i) }}
                </td>
              </ng-container>

              <ng-container matColumnDef="productQuality">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="sticky-header">
                 Quality
                </th>
                <td mat-cell *matCellDef="let row">{{ row.productQuality }}</td>
              </ng-container>

              <ng-container matColumnDef="design">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="sticky-header">
                  Design
                </th>
                <td mat-cell *matCellDef="let row">{{ row.design }}</td>
              </ng-container>

              <ng-container matColumnDef="ground">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="sticky-header">
                  Ground
                </th>
                <td mat-cell *matCellDef="let row">{{ row.ground }}</td>
              </ng-container>
              <ng-container matColumnDef="noOfcolour">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="sticky-header">
                  No Of Colour
                </th>
                <td mat-cell *matCellDef="let row">{{ row.noOfcolour }}</td>
              </ng-container>

              <ng-container matColumnDef="border">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="sticky-header">
                  Border
                </th>
                <td mat-cell *matCellDef="let row">{{ row.border }}</td>
              </ng-container>
              <ng-container matColumnDef="file">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="sticky-header">File</th>
                <td mat-cell *matCellDef="let row">
                  <!-- <img  src="{{row.uploadedFile}}" width="25px" alt="Image"> -->
                  <a (click)="readPdf(row.uploadedFile)"
                    ><img
                      src="../../../../assets/images/pdf-icon-64.png"
                      width="25px"
                      alt="Image"
                  /></a>
                </td>
              </ng-container>

              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="sticky-header">
                  Action
                </th>
                <td mat-cell *matCellDef="let row">
                  <a
                    (click)="view(row._id)"
                    data-bs-toggle="modal"
                    data-bs-target="#addControlModal"
                  >

                    <i class="fa fa-list" aria-hidden="true"></i>
                  </a>
                  <!-- <button
                    mat-icon-button
                    color="primary"
                    (click)="editDesign(row)"
                  > -->&nbsp;
                  <a (click)="editDesign(row)"><i
                    class="fa fa-pencil-square-o fa-edit"
                    title="Edit purchase details "
                    aria-hidden="true"
                  ></i
                ></a>
                    <!-- <mat-icon>edit</mat-icon>
                  </button>
                  <button
                    mat-icon-button
                    color="warn"

                  > -->
                  &nbsp;
                 <a (click)="deleteConformation(row._id)"> <i class="fa fa-trash-o" aria-hidden="true"></i
                  ></a>
                    <!-- <mat-icon>delete</mat-icon>
                  </button> -->
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr
                mat-row
                *matRowDef="let row; columns: displayedColumns"
                (click)="selectedRow(row)"
              ></tr>

              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="4">
                  No data matching the filter "{{ input.value }}"
                </td>
              </tr>
            </table>


          </div>
          <mat-paginator
          [pageSizeOptions]="[ 10, 25, 100]"
          aria-label="Select page of users"
        ></mat-paginator>
        </div>
      </div>
    </fieldset>
  </section>
</div>





<div
  class="modal fade"
  id="addControlModal"
  tabindex="-1"
  aria-labelledby="addControlModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content" >
      <div class="modal-header">
        <h4 class="modal-title" id="addControlModalLabel">Color List</h4>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <table class="table fixed-border-table">
          <thead class="thead-dark">
            <tr>
              <th scope="col">Sr. No.</th>
              <th scope="col">Color</th>
              <th scope="col">Lagat</th>
            </tr>
          </thead>
          <tbody>
<tr *ngFor="let item of _viewLagat?.colourLagats ; let i = index">
  <td>{{i+1}}</td>
  <td>{{ item.colour.newColor}} - {{item.colour.companyColorCode}} - {{item.colour.remark}}</td>
  <td class="text-end">{{item.lagat | number: '1.3-3'}}</td>
</tr>
<tr class="text-end" style="background-color: aliceblue; font-weight: bold;"><td colspan="3">Total lagat : {{_viewTotalLagat}}</td></tr>
          </tbody>
        </table>


      </div>
    </div>
  </div>
</div>
