/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,d=Object.getOwnPropertyDescriptor,b=d&&!y.call({1:2},1);g.f=b?function(t){var n=d(this,t);return!!n&&n.enumerable}:y;var m,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),x=T("".slice),E=function(t){return x(A(t),8,-1)},C=E,F=P,R=function(t){if("Function"===C(t))return F(t)},M=c,k=E,D=Object,I=R("".split),L=M((function(){return!D("z").propertyIsEnumerable(0)}))?function(t){return"String"==k(t)?I(t,""):D(t)}:D,N=function(t){return null==t},_=N,z=TypeError,H=function(t){if(_(t))throw z("Can't call method on "+t);return t},G=L,q=H,B=function(t){return G(q(t))},K="object"==typeof document&&document.all,U={all:K,IS_HTMLDDA:void 0===K&&void 0!==K},V=U.all,W=U.IS_HTMLDDA?function(t){return"function"==typeof t||t===V}:function(t){return"function"==typeof t},J=W,Q=U.all,X=U.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:J(t)||t===Q}:function(t){return"object"==typeof t?null!==t:J(t)},Y=i,$=W,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=R({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(h=(m=ct.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!h&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(h=+m[1]);var at=h,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=W,dt=nt,bt=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&dt(n.prototype,bt(t))},ht=String,vt=W,St=function(t){try{return ht(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=N,Pt=p,Tt=W,At=X,xt=TypeError,Et={exports:{}},Ct=i,Ft=Object.defineProperty,Rt=function(t,n){try{Ft(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Mt=Rt,kt="__core-js_shared__",Dt=i[kt]||Mt(kt,{}),It=Dt;(Et.exports=function(t,n){return It[t]||(It[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Lt=H,Nt=Object,_t=function(t){return Nt(Lt(t))},zt=_t,Ht=R({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return Ht(zt(t),n)},qt=R,Bt=0,Kt=Math.random(),Ut=qt(1..toString),Vt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Ut(++Bt+Kt,36)},Wt=i,Jt=Et.exports,Qt=Gt,Xt=Vt,Yt=st,$t=pt,Zt=Jt("wks"),tn=Wt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Qt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Qt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,cn=mt,an=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=mt,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},dn=X,bn=i.document,mn=dn(bn)&&dn(bn.createElement),hn=function(t){return mn?bn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),Sn=a,wn=p,On=g,jn=v,Pn=B,Tn=yn,An=Gt,xn=vn,En=Object.getOwnPropertyDescriptor;u.f=Sn?En:function(t,n){if(t=Pn(t),n=Tn(n),xn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var Cn={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Rn=X,Mn=String,kn=TypeError,Dn=function(t){if(Rn(t))return t;throw kn(Mn(t)+" is not an object")},In=a,Ln=vn,Nn=Fn,_n=Dn,zn=yn,Hn=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Kn="configurable",Un="writable";Cn.f=In?Nn?function(t,n,r){if(_n(t),n=zn(n),_n(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Un in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Kn in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(_n(t),n=zn(n),_n(r),Ln)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Hn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Vn=Cn,Wn=v,Jn=a?function(t,n,r){return Vn.f(t,n,Wn(1,r))}:function(t,n,r){return t[n]=r,t},Qn={exports:{}},Xn=a,Yn=Gt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=W,er=Dt,or=R(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=W,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,gr=Vt,yr=pr("keys"),dr={},br=sr,mr=i,hr=X,vr=Jn,Sr=Gt,wr=Dt,Or=function(t){return yr[t]||(yr[t]=gr(t))},jr=dr,Pr="Object already initialized",Tr=mr.TypeError,Ar=mr.WeakMap;if(br||wr.state){var xr=wr.state||(wr.state=new Ar);xr.get=xr.get,xr.has=xr.has,xr.set=xr.set,ir=function(t,n){if(xr.has(t))throw Tr(Pr);return n.facade=t,xr.set(t,n),n},ur=function(t){return xr.get(t)||{}},cr=function(t){return xr.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(Sr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return Sr(t,Er)?t[Er]:{}},cr=function(t){return Sr(t,Er)}}var Cr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=c,Rr=W,Mr=Gt,kr=a,Dr=nr.CONFIGURABLE,Ir=ar,Lr=Cr.enforce,Nr=Cr.get,_r=Object.defineProperty,zr=kr&&!Fr((function(){return 8!==_r((function(){}),"length",{value:8}).length})),Hr=String(String).split("String"),Gr=Qn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Mr(t,"name")||Dr&&t.name!==n)&&(kr?_r(t,"name",{value:n,configurable:!0}):t.name=n),zr&&r&&Mr(r,"arity")&&t.length!==r.arity&&_r(t,"length",{value:r.arity});try{r&&Mr(r,"constructor")&&r.constructor?kr&&_r(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Lr(t);return Mr(e,"source")||(e.source=Hr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Rr(this)&&Nr(this).source||Ir(this)}),"toString");var qr=W,Br=Cn,Kr=Qn.exports,Ur=Rt,Vr={},Wr=Math.ceil,Jr=Math.floor,Qr=Math.trunc||function(t){var n=+t;return(n>0?Jr:Wr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Qr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=Gt,le=B,se=ae.indexOf,pe=dr,ge=R([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},de=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Vr.f=Object.getOwnPropertyNames||function(t){return ye(t,de)};var be={};be.f=Object.getOwnPropertySymbols;var me=tt,he=Vr,ve=be,Se=Dn,we=R([].concat),Oe=me("Reflect","ownKeys")||function(t){var n=he.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},je=Gt,Pe=Oe,Te=u,Ae=Cn,xe=c,Ee=W,Ce=/#|\.prototype\./,Fe=function(t,n){var r=Me[Re(t)];return r==De||r!=ke&&(Ee(n)?xe(n):!!n)},Re=Fe.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Me=Fe.data={},ke=Fe.NATIVE="N",De=Fe.POLYFILL="P",Ie=Fe,Le=i,Ne=u.f,_e=Jn,ze=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Kr(r,i,e),e.global)o?t[n]=r:Ur(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},He=Rt,Ge=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var c=e[u];je(t,c)||r&&je(r,c)||o(t,c,i(n,c))}},qe=Ie,Be=E,Ke=Array.isArray||function(t){return"Array"==Be(t)},Ue=TypeError,Ve=yn,We=Cn,Je=v,Qe={};Qe[en("toStringTag")]="z";var Xe="[object z]"===String(Qe),Ye=W,$e=E,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=R,eo=c,oo=W,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,mo=Ke,ho=bo,vo=X,So=en("species"),wo=Array,Oo=function(t){var n;return mo(t)&&(n=t.constructor,(ho(n)&&(n===wo||mo(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=c,Po=at,To=en("species"),Ao=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Le:f?Le[c]||He(c,{}):(Le[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Ne(r,e))&&u.value:r[e],!qe(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&_e(i,"sham",!0),ze(r,e,i,t)}},xo=c,Eo=Ke,Co=X,Fo=_t,Ro=ee,Mo=function(t){if(t>9007199254740991)throw Ue("Maximum allowed index exceeded");return t},ko=function(t,n,r){var e=Ve(n);e in t?We.f(t,e,Je(0,r)):t[e]=r},Do=function(t,n){return new(Oo(t))(0===n?0:n)},Io=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=at,No=en("isConcatSpreadable"),_o=Lo>=51||!xo((function(){var t=[];return t[No]=!1,t.concat()[0]!==t})),zo=Io("concat"),Ho=function(t){if(!Co(t))return!1;var n=t[No];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!_o||!zo},{concat:function(t){var n,r,e,o,i,u=Fo(this),c=Do(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(Ho(i=-1===n?u:arguments[n]))for(o=Ro(i),Mo(a+o),r=0;r<o;r++,a++)r in i&&ko(c,a,i[r]);else Mo(a+1),ko(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["da-DK"]=r.default.fn.bootstrapTable.locales.da={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Indlæser, vent venligst"},formatRecordsPerPage:function(t){return"".concat(t," poster pr side")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Viser ".concat(t," til ").concat(n," af ").concat(r," række").concat(r>1?"r":""," (filtered from ").concat(e," total rows)"):"Viser ".concat(t," til ").concat(n," af ").concat(r," række").concat(r>1?"r":"")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Viser ".concat(t," række").concat(t>1?"r":"")},formatClearSearch:function(){return"Ryd filtre"},formatSearch:function(){return"Søg"},formatNoMatches:function(){return"Ingen poster fundet"},formatPaginationSwitch:function(){return"Skjul/vis nummerering"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Opdater"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolonner"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Alle"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Eksporter"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["da-DK"])}));
