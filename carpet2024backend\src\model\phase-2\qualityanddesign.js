// qualityandDesignModel.js

const mongoose = require('mongoose');

const qualityandDesignSchema = new mongoose.Schema({
  oldQualityandDesign: {
    type: String,
    required: true
  },
  newQualityandDesign: {
    type: String,
    required: true
  },
  qualityCodeandDesign: {
    type: String,
    required: true
  },
  oldColor: {
    type: String,
    required: true
  },
  newColor: {
    type: String,
    required: true
  },
  colorCode: {
    type: String,
    required: true
  }
});

const QualityandDesign = mongoose.model('QualityandDesign', qualityandDesignSchema);

module.exports = QualityandDesign;
