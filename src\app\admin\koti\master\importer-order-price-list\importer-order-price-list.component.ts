import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ImporterService } from '../../../../services/importer.service';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { get } from 'http';
import { CustomeServiceService } from '../../../../services/custome-service.service';

export interface PeriodicElement {
  toDate: string;
  fromDate: string;
  quality: string;
  design: string;
  orderPrice: string;
  id: string;
  index: number;
}

const ELEMENT_DATA2: PeriodicElement[] = [];
@Component({
  selector: 'app-importer-order-price-list',
  templateUrl: './importer-order-price-list.component.html',
  styleUrl: './importer-order-price-list.component.css'
})
export class ImporterOrderPriceListComponent implements OnInit {

  frmImporterOrderPrice!: FormGroup
  activeId: any;
  displayedColumns: string[] = ['id', 'toDate', 'fromDate', 'quality', 'design', 'orderPrice'];
  dataSource = new MatTableDataSource(ELEMENT_DATA2);
  qualityList: string[] = [];
  designList: string[] = [];
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  ngOnInit(): void {
    this.frmImporterOrderPrice = this.frmBuilder.group({
      toDate: [],
      fromDate: [],
      quality: [],
      design: [],
      orderPrice: [],
    });

    this.activeId = this.activateRoute.snapshot.paramMap.get('id') || '';
    this.getAllImporterPrice();
    this.getQualityDesignList();
    debugger
  }

  constructor(private frmBuilder: FormBuilder, private customerService:CustomeServiceService,private importerService: ImporterService, private activateRoute: ActivatedRoute, private _router: Router) {

  }


  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  importerOrderPrice() {
    let formData = this.frmImporterOrderPrice.value;
    let addOnPrice: any = []

    const data = {
      addOnPrice:{
          toDate: formData.toDate,
          fromDate: formData.fromDate,
          quality: formData.quality,
          design: formData.design,
          orderPrice: formData.orderPrice,
        }
        // You can uncomment and add more objects here if needed

    };
    let id = this.activeId.split('+');
    let getsId = '';
    if (id[1] === 'retailer') {
      getsId = id[0];
      this.importerService.updateRetailer(data, getsId).subscribe((resp: any) => {
        debugger
        Swal.fire(
          'Success!',
          'Price has been saved successfully',
          'success'
        )
        this.getAllImporterPrice();
      }, (error) => {
        Swal.fire(
          'Warning!',
          `Something went wrong`,
          'warning'
        )
      })
    } else {
      getsId = id[0]
      this.importerService.addImporterPrice(getsId,data).subscribe((resp: any) => {
        debugger
        Swal.fire(
          'Success!',
          'Price has been saved successfully',
          'success'
        )
        this.getAllImporterPrice();
      }, (error) => {
        Swal.fire(
          'Warning!',
          `Something went wrong`,
          'warning'
        )
      })
    }
    debugger


    //  this._router.navigate(['/admin/importer-detail'])


  }

  priceList: any = [];

  getAllImporterPrice() {
    let id = this.activeId.split('+');
    let getsId = '';
    if (id[1] != 'retailer') {
      this.importerService.getAllImporter().subscribe((resp: any) => {

        console.log(resp)

        this.priceList = resp.filter((x: any) => x.impoter._id === id[0]);

        let prices = this.priceList[0].impoter.addOnPrice;
        if (prices) {
          ELEMENT_DATA2.length = 0;
          prices.map((val: any, ind: number) => {
            ELEMENT_DATA2.push({
              id: val._id,
              index: ind + 1,
              toDate: val.toDate,
              fromDate: val.fromDate,
              quality: val.quality,
              design: val.design,
              orderPrice: val.orderPrice,
            })
          })

          this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
          this.ngAfterViewInit();
          return;
        }

        debugger
      })
    } else {
      this.importerService.getRetailerList().subscribe((resp: any) => {

        console.log(resp)
        debugger
        this.priceList = resp.filter((x: any) => x._id === id[0]);

        let prices = this.priceList[0].addOnPrice;
        if (prices) {
          ELEMENT_DATA2.length = 0;
          prices.map((val: any, ind: number) => {

            let toDate = this.customerService.convertDate(val.toDate);
            let fromDate= this.customerService.convertDate(val.fromDate);
            ELEMENT_DATA2.push({
              id: val._id,
              index: ind + 1,
              toDate: toDate,
              fromDate: fromDate,
              quality: val.quality,
              design: val.design,
              orderPrice: val.orderPrice,
            })
          })

          this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
          this.ngAfterViewInit();
          return;
        }

        debugger
      })
    }
  }


  getQualityDesignList() {
    this.importerService.getAllQualityDesign().subscribe((resp: any) => {
      if (resp) {
        resp.map((x: any) => {
          let _data = x.newQualityandDesign.split(' ');
          this.qualityList.push(_data[0]);
          this.designList.push(_data[1]);

          this.qualityList = this.qualityList.reduce((acc:any, currentValue:any) => {
            if (!acc.includes(currentValue)) {
                acc.push(currentValue);
            }
            return acc;
        }, []);

        this.designList = this.designList.reduce((acc:any, currentValue:any) => {
          if (!acc.includes(currentValue)) {
              acc.push(currentValue);
          }
          return acc;
      }, []);
        })
      }
    })
  }
}
