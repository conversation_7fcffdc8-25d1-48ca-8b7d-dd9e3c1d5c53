import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment.development';

@Injectable({
  providedIn: 'root'
})
export class AuthServicesService {
  apiUrl: any = environment.apiUrl;
  phaseOne: any = `${this.apiUrl}/phase-one/user`;
  constructor(private http : HttpClient) { }
register(data:any){
  return this.http.post(`${this.phaseOne}/create-user`,data);
}
login(data:any){
  return this.http.post(`${this.phaseOne}/loginUser`,data);
}

}
