import { Component, OnInit, ViewChild } from '@angular/core';
import { ImporterService } from '../../../../services/importer.service';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import Swal from 'sweetalert2';

export interface PeriodicElement {
  index: number;
  id: string;
  impotererNo: string;
  impotererName: string;
  invoiceNo: string;
  areaOfUnit: string;
  currency: string;
  quantity: string;
  totalArea: string;
  amount: string;
}

const ELEMENT_DATA: PeriodicElement[] = [];
@Component({
  selector: 'app-view-importer-invoice',
  templateUrl: './view-importer-invoice.component.html',
  styleUrl: './view-importer-invoice.component.css'
})
export class ViewImporterInvoiceComponent implements OnInit {
  displayedColumns: string[] = ['id', 'impotererNo', 'impotererName', 'invoiceNo', 'areaOfUnit', 'currency',
    'quantity',
    'totalArea',
    'amount', 'action'];



  ngOnInit(): void {
    this.getAllImporter();
  }
  dataSource = new MatTableDataSource(ELEMENT_DATA);
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  constructor(private _services: ImporterService) { }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  getAllImporter() {
    this._services.getAllImporterInvoice().subscribe((resp: any) => {

      ELEMENT_DATA.length = 0;
      resp.map((val: any, ind: any) => {
        ELEMENT_DATA.push({
          id: val._id,
          index: ind + 1,
          impotererNo: val.impotererNo,
          impotererName: val.impotererName,
          invoiceNo: val.invoiceNo,
          areaOfUnit: val.areaOfUnit,
          currency: val.currency,
          quantity: val.quantity,
          totalArea: val.totalArea,
          amount: val.amount,
        });
        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.ngAfterViewInit();
        return;
      })
    })
  }


  deleteImporterInvoice(id: any) {

    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!"
    }).then((result) => {
      if (result.isConfirmed) {

        this._services.deleteImporterInvoice(id).subscribe((resp: any) => {
          debugger
          Swal.fire({
            title: "Deleted!",
            text: 'Deleted successfully',
            icon: "success"
          });
          this.getAllImporter();
        })

      }
    });

  }

}
