var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Russian = {
    weekdays: {
        shorthand: ["Вс", "Пн", "Вт", "Ср", "Чт", "Пт", "Сб"],
        longhand: [
            "Воскресенье",
            "Понедельник",
            "В<PERSON><PERSON><PERSON><PERSON>и<PERSON>",
            "Среда",
            "Четверг",
            "Пятница",
            "Суббота",
        ],
    },
    months: {
        shorthand: [
            "Янв",
            "Фев",
            "Март",
            "Апр",
            "Май",
            "Июнь",
            "Июль",
            "Авг",
            "Сен",
            "Окт",
            "Ноя",
            "Дек",
        ],
        longhand: [
            "Январь",
            "Февраль",
            "Март",
            "Апрель",
            "<PERSON>а<PERSON>",
            "<PERSON>юнь",
            "Июль",
            "Август",
            "Сентябрь",
            "Октябрь",
            "Но<PERSON>брь",
            "Декабрь",
        ],
    },
    firstDayOfWeek: 1,
    ordinal: function () {
        return "";
    },
    rangeSeparator: " — ",
    weekAbbreviation: "Нед.",
    scrollTitle: "Прокрутите для увеличения",
    toggleTitle: "Нажмите для переключения",
    amPM: ["ДП", "ПП"],
    yearAriaLabel: "Год",
    time_24hr: true,
};
fp.l10ns.ru = Russian;
export default fp.l10ns;
