
<button (click)="printPage()">print</button>

<div class="page-header" >
 <div class="container">
  <div class="row">
    <div class="card card1">
      <div class="second-page11 mt-5">
        <div class="second-page">
          <h5 style="color: rgb(85, 84, 84)" class="textsize ">
            Rechnungsempfanger :
          </h5>
          <h5 class="fs-4 h5txt" style="margin-bottom: 0px">
            {{ _bill.customer }}
          </h5>
          <h5 class="fs-4 h5txt" style="margin-bottom: 0px">
            {{ _bill.street }},{{ _bill.zipCode }}
          </h5>
          <h5 class="fs-4 h5txt" style="margin-bottom: 0px">
            {{ _bill.country }}
          </h5>
        </div>
        <div class="second-page1">
          <h5 style="color: rgb(85, 84, 84)" class="fs-4">
            Rechnungssteller:
          </h5>
          <h5 class="fs-4 h5txt" style="margin-bottom: 0px">K.O.T.I. GmbH</h5>
          <h5 class="fs-4 h5txt" style="margin-bottom: 0px">Stockum 2 a</h5>
          <h5 class="fs-4 h5txt" style="margin-bottom: 0px">48653 Coesfeld</h5>
          <h5 class="fs-4 h5txt"  style="margin-bottom: 0px">Deutschland</h5>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12">
      <div class="card card1">
        <div class="card-text">
          <div class="second-text">
            <h4 style="font-weight: bold; font-size: x-large">
              ANLAGE ZUR RECHNUNG {{ _bill.billNo }}
            </h4>
            <h5 class="fs-5" style="font-weight: bold">
              Abrechnung der Umlagerung Vom 01.01.2018 bis 31.01.2018
            </h5>
            <h5 class="fs-5" style="font-weight: bold">
              Berechnet mit einem Aufschlag Von: 0%
            </h5>
          </div>
          <div class="second-text22">
            <div class="second-text2">
              <h5 class="fs-5 textsize">Datum</h5>
              <h5 class="fs-5 textsize">01.02.2018</h5>
            </div>
            <div class="second-text1">
              <h5 class="fs-5 textsize">Seite</h5>
              <h5 class="fs-5 textsize">1 Von 5</h5>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<div class="row">
  <div class="col-md-12">
    <div class="counter-heading-text">
      <div class="counter-heading-text1">
        <h5 class="fs-5 textsize">Artikel-Nr.</h5>
      </div>
      <div class="counter-heading-text2">
        <h5 class="fs-5 textsize">Bezeichnung</h5>
      </div>
      <div class="counter-heading-text3">
        <p class="textsize" style="text-align: end">Menge Einh.</p>
      </div>
      <div class="counter-heading-text4">
        <p class="textsize" style="text-align: end">Einzel-Pr.</p>
      </div>
      <div class="counter-heading-text5">
        <p class="textsize" style="text-align: end">Gesamt-Pr.</p>
      </div>
    </div>
  </div>
  <span class="line"></span>

</div>
 </div>
  <br />
  <button type="button" onClick="window.print()" style="background: pink">
    PRINT ME!
  </button>
</div>

<div class="page-footer">

  <div class="row">
    <div class="col-md-12 last-counter1">
      <div class="col-md-8">
        <h6 class="fs-4" style="font-weight: bolder">
          Umlagerungen von Filiale3 zu Filiale 1
        </h6>
      </div>
      <div
        class="col-md-4 d-flex justify-content-end"
        style="text-align: end; padding-right: 0px"
      >
        <div class="invoice-table">
          <table class="table-colour">
            <tr>
              <td class="font-weight-bold text-lg">
                Gesamtpreis Netto EUR
              </td>
              <td class="right-align fw-bold">
                {{ totalCalculation.totalAmount | number : "1.2-2" }}
              </td>
            </tr>
            <tr>
              <td class="">Profitieren</td>
              <td class="right-align">
                {{ totalCalculation.profit | number : "1.2-2" }}
              </td>
            </tr>
            <tr>
              <td>zzgl. 19% MwSt EUR</td>
              <td class="right-align">
                {{ totalCalculation.gstAmt | number : "1.2-2" }}
              </td>
            </tr>
            <tr>
              <td class="fw-bold">Gesamtpreis Brutto EUR</td>
              <td class="right-align line-above line-bottom fw-bold">
                {{ totalCalculation.grossAmt | number : "1.2-2" }}
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>

</div>

<table >
  <thead>
    <tr>
      <td>
        <!--place holder for the fixed-position header-->
        <div class="page-header-space"></div>
      </td>
    </tr>
  </thead>

  <tbody>
    <tr>
      <td>
        <!--*** CONTENT GOES HERE ***-->
        <div class="page">
          <!-- First Page Content -->
          <div class="col-md-12 " *ngFor="let item of billDetails">
            <div class="counter-heading-text">
              <div class="counter-heading-text1">
                <h5 style="font-size: 18px">
                  {{ item.carpetNo }}
                </h5>
              </div>
              <div class="counter-heading-text2">
                <!-- <h5>RG:{{item.challanNo}} vom  {{item.challanDate}} an  {{item.customer}}</h5> -->
                <div *ngIf="item.challanNo">
                  <h5
                    style="
                      font-weight: bold;
                      font-size: 18px;
                      font-style: italic;
                      margin-bottom: 10px;
                    "
                  >
                    RG:{{ item.challanNo }} vom {{ item.challanDate }} an
                    {{ item.customer }}
                  </h5>
                </div>
                <P style="margin-bottom: 10px; font-size: 18px">{{
                  item.qualityDesign
                }}</P>
                <p style="margin-bottom: 10px; font-size: 18px">
                  {{ item.qualityCode }}, {{ item.colourCode }} {{ item.colour }}
                </p>
                <p style="margin-bottom: 10px; font-size: 18px">
                  {{ item.size }} cm
                </p>
              </div>
              <div class="counter-heading-text3">
                <p style="margin-top: 35px; font-size: 18px; text-align: end">
                  {{ item.area | number : "1.2-2" }} QM
                </p>
              </div>
              <div class="counter-heading-text4">
                <p style="margin-top: 35px; font-size: 18px; text-align: end">
                  {{ item.evkPrice | number : "1.2-2" }}
                </p>
              </div>

              <div class="counter-heading-text5">
                <p style="margin-top: 35px; font-size: 18px; text-align: end">
                  {{ item.amount | number : "1.2-2" }}
                </p>
              </div>
            </div>
          </div>
        </div>

      </td>
    </tr>
  </tbody>

  <tfoot>
    <tr>
      <td>
        <!--place holder for the fixed-position footer-->
        <div class="page-footer-space"></div>
      </td>
    </tr>
  </tfoot>
</table>
