import { Component } from '@angular/core';
import { AfterViewInit, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ImporterService } from '../../../../services/importer.service';

export interface UserData {
  No: number;
  id: string;
  containerNo: string;
  expenseAmount: string;
  // uploadPdf: string;
  totalArea: string;
  EspPrice: string;
}

const ELEMENT_DATA2: UserData[] = [];
@Component({
  selector: 'app-container-received-report',
  templateUrl: './container-received-report.component.html',
  styleUrl: './container-received-report.component.css'
})
export class ContainerReceivedReportComponent implements OnInit {



  displayedColumns: string[] = ['No', 'containerNo', 'expenseAmount', 'totalArea', 'EspPrice', 'Action'];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA2);
  constructor(private _services: ImporterService) { }

  ngOnInit(): void {
    this.getLists();
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;



  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  getLists() {
    this._services.getAllContainerRecieved().subscribe((resp: any) => {
      console.log(resp);
      if (resp) {
        debugger
        ELEMENT_DATA2.length = 0;
        resp.map((x: any, i: number) => {
          ELEMENT_DATA2.push({
            No: i + 1,
            id: x._id,
            containerNo: x.impoterName,
            expenseAmount: x.expensesAmount,            
            totalArea: x.totalArea,
            EspPrice: x.espPrice
          })
        })
        this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
        this.ngAfterViewInit();
        return;
      }
    })
  }
}
