/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!y.call({1:2},1);g.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:y;var m,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},w=f,S=Function.prototype,O=S.call,j=w&&S.bind.bind(O,O),P=function(t){return w?j(t):function(){return O.apply(t,arguments)}},T=P,E=T({}.toString),k=T("".slice),x=function(t){return k(E(t),8,-1)},A=x,C=P,F=function(t){if("Function"===A(t))return C(t)},M=a,R=x,z=Object,D=F("".split),I=M((function(){return!z("z").propertyIsEnumerable(0)}))?function(t){return"String"==R(t)?D(t,""):z(t)}:z,L=function(t){return null==t},N=L,_=TypeError,H=function(t){if(N(t))throw _("Can't call method on "+t);return t},G=I,U=H,B=function(t){return G(U(t))},q="object"==typeof document&&document.all,W={all:q,IS_HTMLDDA:void 0===q&&void 0!==q},J=W.all,K=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===J}:function(t){return"function"==typeof t},Q=K,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,Z=K,$=function(t){return Z(t)?t:void 0},tt=function(t,n){return arguments.length<2?$(Y[t]):Y[t]&&Y[t][n]},nt=F({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(h=(m=at.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!h&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(h=+m[1]);var ct=h,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=K,bt=nt,dt=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&bt(n.prototype,dt(t))},ht=String,vt=K,wt=function(t){try{return ht(t)}catch(t){return"Object"}},St=TypeError,Ot=function(t){if(vt(t))return t;throw St(wt(t)+" is not a function")},jt=L,Pt=p,Tt=K,Et=X,kt=TypeError,xt={exports:{}},At=i,Ct=Object.defineProperty,Ft=function(t,n){try{Ct(At,t,{value:n,configurable:!0,writable:!0})}catch(r){At[t]=n}return n},Mt=Ft,Rt="__core-js_shared__",zt=i[Rt]||Mt(Rt,{}),Dt=zt;(xt.exports=function(t,n){return Dt[t]||(Dt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var It=H,Lt=Object,Nt=function(t){return Lt(It(t))},_t=Nt,Ht=F({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return Ht(_t(t),n)},Ut=F,Bt=0,qt=Math.random(),Wt=Ut(1..toString),Jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Bt+qt,36)},Kt=i,Qt=xt.exports,Vt=Gt,Xt=Jt,Yt=st,Zt=pt,$t=Qt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=Zt?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt($t,t)||!Yt&&"string"!=typeof $t[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?$t[t]=tn[t]:$t[t]=Zt&&nn?nn(n):rn(n)}return $t[t]},on=p,un=X,an=mt,cn=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!Et(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!Et(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!Et(e=Pt(r,t)))return e;throw kt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=mt,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},bn=X,dn=i.document,mn=bn(dn)&&bn(dn.createElement),hn=function(t){return mn?dn.createElement(t):{}},vn=!c&&!a((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),wn=c,Sn=p,On=g,jn=v,Pn=B,Tn=yn,En=Gt,kn=vn,xn=Object.getOwnPropertyDescriptor;u.f=wn?xn:function(t,n){if(t=Pn(t),n=Tn(n),kn)try{return xn(t,n)}catch(t){}if(En(t,n))return jn(!Sn(On.f,t,n),t[n])};var An={},Cn=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Fn=X,Mn=String,Rn=TypeError,zn=function(t){if(Fn(t))return t;throw Rn(Mn(t)+" is not an object")},Dn=c,In=vn,Ln=Cn,Nn=zn,_n=yn,Hn=TypeError,Gn=Object.defineProperty,Un=Object.getOwnPropertyDescriptor,Bn="enumerable",qn="configurable",Wn="writable";An.f=Dn?Ln?function(t,n,r){if(Nn(t),n=_n(n),Nn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=Un(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:qn in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(Nn(t),n=_n(n),Nn(r),In)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Hn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Jn=An,Kn=v,Qn=c?function(t,n,r){return Jn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=c,Yn=Gt,Zn=Function.prototype,$n=Xn&&Object.getOwnPropertyDescriptor,tr=Yn(Zn,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&$n(Zn,"name").configurable)},rr=K,er=zt,or=F(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=xt.exports,gr=Jt,yr=pr("keys"),br={},dr=sr,mr=i,hr=X,vr=Qn,wr=Gt,Sr=zt,Or=function(t){return yr[t]||(yr[t]=gr(t))},jr=br,Pr="Object already initialized",Tr=mr.TypeError,Er=mr.WeakMap;if(dr||Sr.state){var kr=Sr.state||(Sr.state=new Er);kr.get=kr.get,kr.has=kr.has,kr.set=kr.set,ir=function(t,n){if(kr.has(t))throw Tr(Pr);return n.facade=t,kr.set(t,n),n},ur=function(t){return kr.get(t)||{}},ar=function(t){return kr.has(t)}}else{var xr=Or("state");jr[xr]=!0,ir=function(t,n){if(wr(t,xr))throw Tr(Pr);return n.facade=t,vr(t,xr,n),n},ur=function(t){return wr(t,xr)?t[xr]:{}},ar=function(t){return wr(t,xr)}}var Ar={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Cr=a,Fr=K,Mr=Gt,Rr=c,zr=nr.CONFIGURABLE,Dr=cr,Ir=Ar.enforce,Lr=Ar.get,Nr=Object.defineProperty,_r=Rr&&!Cr((function(){return 8!==Nr((function(){}),"length",{value:8}).length})),Hr=String(String).split("String"),Gr=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Mr(t,"name")||zr&&t.name!==n)&&(Rr?Nr(t,"name",{value:n,configurable:!0}):t.name=n),_r&&r&&Mr(r,"arity")&&t.length!==r.arity&&Nr(t,"length",{value:r.arity});try{r&&Mr(r,"constructor")&&r.constructor?Rr&&Nr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Ir(t);return Mr(e,"source")||(e.source=Hr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Fr(this)&&Lr(this).source||Dr(this)}),"toString");var Ur=K,Br=An,qr=Vn.exports,Wr=Ft,Jr={},Kr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Kr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,Zr=Math.max,$r=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?Zr(r+n,0):$r(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=Gt,le=B,se=ce.indexOf,pe=br,ge=F([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Jr.f=Object.getOwnPropertyNames||function(t){return ye(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var me=tt,he=Jr,ve=de,we=zn,Se=F([].concat),Oe=me("Reflect","ownKeys")||function(t){var n=he.f(we(t)),r=ve.f;return r?Se(n,r(t)):n},je=Gt,Pe=Oe,Te=u,Ee=An,ke=a,xe=K,Ae=/#|\.prototype\./,Ce=function(t,n){var r=Me[Fe(t)];return r==ze||r!=Re&&(xe(n)?ke(n):!!n)},Fe=Ce.normalize=function(t){return String(t).replace(Ae,".").toLowerCase()},Me=Ce.data={},Re=Ce.NATIVE="N",ze=Ce.POLYFILL="P",De=Ce,Ie=i,Le=u.f,Ne=Qn,_e=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Ur(r)&&qr(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},He=Ft,Ge=function(t,n,r){for(var e=Pe(n),o=Ee.f,i=Te.f,u=0;u<e.length;u++){var a=e[u];je(t,a)||r&&je(r,a)||o(t,a,i(n,a))}},Ue=De,Be=x,qe=Array.isArray||function(t){return"Array"==Be(t)},We=TypeError,Je=yn,Ke=An,Qe=v,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=K,Ze=x,$e=en("toStringTag"),to=Object,no="Arguments"==Ze(function(){return arguments}()),ro=F,eo=a,oo=K,io=Xe?Ze:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),$e))?r:no?Ze(n):"Object"==(e=Ze(n))&&Ye(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),go=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,mo=qe,ho=bo,vo=X,wo=en("species"),So=Array,Oo=function(t){var n;return mo(t)&&(n=t.constructor,(ho(n)&&(n===So||mo(n.prototype))||vo(n)&&null===(n=n[wo]))&&(n=void 0)),void 0===n?So:n},jo=a,Po=ct,To=en("species"),Eo=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?Ie:f?Ie[a]||He(a,{}):(Ie[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Le(r,e))&&u.value:r[e],!Ue(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&Ne(i,"sham",!0),_e(r,e,i,t)}},ko=a,xo=qe,Ao=X,Co=Nt,Fo=ee,Mo=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Ro=function(t,n,r){var e=Je(n);e in t?Ke.f(t,e,Qe(0,r)):t[e]=r},zo=function(t,n){return new(Oo(t))(0===n?0:n)},Do=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Io=ct,Lo=en("isConcatSpreadable"),No=Io>=51||!ko((function(){var t=[];return t[Lo]=!1,t.concat()[0]!==t})),_o=Do("concat"),Ho=function(t){if(!Ao(t))return!1;var n=t[Lo];return void 0!==n?!!n:xo(t)};Eo({target:"Array",proto:!0,arity:1,forced:!No||!_o},{concat:function(t){var n,r,e,o,i,u=Co(this),a=zo(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(Ho(i=-1===n?u:arguments[n]))for(o=Fo(i),Mo(c+o),r=0;r<o;r++,c++)r in i&&Ro(a,c,i[r]);else Mo(c+1),Ro(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["eu-EU"]=r.default.fn.bootstrapTable.locales.eu={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Itxaron mesedez"},formatRecordsPerPage:function(t){return"".concat(t," emaitza orriko.")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"".concat(r," erregistroetatik ").concat(t,"etik ").concat(n,"erakoak erakusten (filtered from ").concat(e," total rows)"):"".concat(r," erregistroetatik ").concat(t,"etik ").concat(n,"erakoak erakusten.")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Bilatu"},formatNoMatches:function(){return"Ez da emaitzarik aurkitu"},formatPaginationSwitch:function(){return"Ezkutatu/Erakutsi orrikatzea"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Eguneratu"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Zutabeak"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Guztiak"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["eu-EU"])}));
