{"name": "carpet-backend", "version": "1.0.0", "description": "", "main": "src/app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon src/app.js", "dev": "nodemon src/app.js"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "csvtojson": "^2.0.10", "dotenv": "^16.4.5", "ejs": "^3.1.10", "exceljs": "^4.4.0", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.15.0", "mongoose": "^8.1.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemon": "^3.0.3", "read-excel-file": "^5.7.1", "request": "^2.88.2", "serve-static": "^1.15.0", "slugify": "^1.6.6", "winston": "^3.13.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.11.30"}}