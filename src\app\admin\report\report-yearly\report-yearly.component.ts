import { Component, OnInit, ViewChild } from '@angular/core';
import { ImporterService } from '../../../services/importer.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { CustomeServiceService } from '../../../services/custome-service.service';
import Swal from 'sweetalert2';
import { firstValueFrom } from 'rxjs';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { profile } from 'node:console';
import { ExportDataServiceService } from '../../../services/export-data-service.service';

const monthNames: any = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];
export interface PeriodicElement {
  id: string;
  index: number;
  billNo: string;
  date: string;
  pcs: number;
  area: number;
  amount: number;
  gst: number;
  customer: string;
  totalAmount: number;
  profit: number;
}

const ELEMENT_DATA: PeriodicElement[] = [];
@Component({
  selector: 'app-report-yearly',
  templateUrl: './report-yearly.component.html',
  styleUrl: './report-yearly.component.css',
})
export class ReportYearlyComponent implements OnInit {
  displayedColumns: string[] = [
    'id',
    'billNo',
    'date',
    'customer',
    'pcs',
    'area',

    'amount',
    'profit',
    'gst',
    'totalAmount',
    'action',
  ];
  dataSource = new MatTableDataSource(ELEMENT_DATA);
  dataArray: any = [];
  years: any = [];
  months: any = [];
  yearVal = {};
  excelArray: any = [];
  constructor(
    private _service: ImporterService,
    private customeService: CustomeServiceService,
    private ngxLoader: NgxUiLoaderService,
    private excelService: ExportDataServiceService
  ) {}
  allBills: any;
  ngOnInit(): void {
    this.resolveAllPromise();
    // this.getChallans();
    // this.getContainerReceived();
    // setTimeout(() => {

    // }, 5000);
  }
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  calculations = {
    totalPcs: 0,
    totalArea: 0,
    totalGst: 0,
    totalAmount: 0,
    grossTotalAmount: 0,
  };

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async getBills(): Promise<void> {
    this.calculations = {
      totalPcs: 0,
      totalArea: 0,
      totalGst: 0,
      totalAmount: 0,
      grossTotalAmount: 0,
    };
    const resp = await firstValueFrom(this._service.getsBill());

    if (Array.isArray(resp)) {
      this.allBills = resp;
      ELEMENT_DATA.length = 0;
      let pcs: number = 0;
      let amount: number = 0;
      let area: number = 0;
      let count = 1;
      resp.forEach((bilData: any, ind: number) => {
        if (bilData.isBillDeleted !== true) {
          pcs = 0;
          amount = 0;
          area = 0;
          let date = this.customeService.convertDate(bilData.chooseAdate);


          const dateObject = new Date(bilData.chooseAdate);
          const year = dateObject.getFullYear();
          let exist = this.dataArray.some((y: any) => y.year === year);
          if (!exist) {
            this.dataArray.push({ year });
          }

          bilData.challanNo.forEach((x: any) => {
            let challanData = this.allChallans.find(
              (cNo: any) => cNo.challanNo === x.challanNumber
            );


            challanData?.carpetList.forEach((element: any, i: number) => {


              this.allContainerStock.forEach((ele: any) => {
                ele.containerItem.forEach((el: any) => {
                  if (element.barcodeNo == el.GerCarpetNo && element.isDeleted !=true) {

                    let areas = parseFloat(
                      element.area ? element.area : el.Area
                    );
                    let evkPrice = parseFloat(
                      element.evkPrice ? element.evkPrice : el.EvKPrice
                    );
                    let itemAmount = areas * evkPrice;

                    pcs++;

                    if (element.status === 'sale') {
                      area += areas;
                      amount += itemAmount;
                    } else if (element.status === 'return') {
                      area -= Math.abs(areas);
                      amount -= Math.abs(itemAmount);
                      // area -= areas;
                      // amount -= itemAmount;
                    }
                  }
                });
              });


            });
          });
          amount=bilData.discount?amount-bilData.discount:amount;

          let profit = (amount * 13) / 100;
          let gst = ((amount + profit) * 19) / 100;
          let tltAmount = amount + profit + gst;
          ELEMENT_DATA.push({
            id: bilData._id,
            index: 0,
            billNo: bilData.billNo,
            date: date,
            customer: bilData.wholesellerName,
            pcs: pcs,
            area: area,
            gst: gst,
            amount: amount,
            profit: profit,
            totalAmount: tltAmount,
          });
        }
      });


      this.sortData();
      this.dataSource = new MatTableDataSource(ELEMENT_DATA);
      this.dataSource.filteredData.forEach((x: any) => {
        this.calculations = {
          totalPcs: this.calculations.totalPcs + x.pcs,
          totalArea: this.calculations.totalArea + x.area,
          totalGst: this.calculations.totalGst + parseFloat(x.gst),
          totalAmount: this.calculations.totalAmount + x.amount,
          grossTotalAmount: this.calculations.grossTotalAmount + x.totalAmount,
        };
      });


      this.ngAfterViewInit();
      this.yearList();
    } else {
      console.error('Response from getBills is not an array:', resp);
    }
  }


  allChallans: any;
  allContainerStock: any;

  async getChallans(): Promise<void> {
    // const resp = await firstValueFrom(this._service.getAllChallan());

    // this.allChallans = resp;
    try {
      const resp = await firstValueFrom(this._service.getAllChallan());
      this.allChallans = resp;
    } catch (error) {
      console.error('Error fetching challans:', error);
      throw error;  // Re-throw to be caught in the calling function
    }
  }

  async getContainerReceived(): Promise<void> {
    // const resp = await firstValueFrom(this._service.getAllContainerRecieved());
    // this.allContainerStock = resp;
    try {
      const resp = await firstValueFrom(this._service.getAllContainerRecieved());
      this.allContainerStock = resp;
    } catch (error) {
      console.error('Error fetching container received:', error);
      throw error;  // Re-throw to be caught in the calling function
    }
  }



  deleteChallan(id: string) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to delete this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        this._service.deleteWholesellerBill(id).subscribe((resp: any) => {


          Swal.fire({
            title: 'Deleted!',
            text: 'Bill has been deleted',
            icon: 'success',
          });
          this.getBills();
        });
      }
    });
  }

  yearList() {
    if (this.dataArray) {
      const sortedArray = this.dataArray.sort(
        (a: any, b: any) => parseInt(b.year) - parseInt(a.year)
      );
      // Removing duplicate years
      this.years = sortedArray.filter((value: any, index: any, array: any) => {
        return index === 0 || value.year !== array[index - 1].year;
      });


    }
  }
  getMonth(data: any) {
    if (data !== 'All') {
      let year = { year: data };
      this.yearVal = data;
      let monthArray: any = [];

      if (this.allChallans) {
        this.allChallans.map((val: any) => {
          const dateObject = new Date(val.chooseAdate);
          const month = dateObject.getMonth(); // Months are zero-indexed, so add 1

          const year = dateObject.getFullYear();
          monthArray.push({
            month: monthNames[month],
            id: month,
          });
        });
        const sortedArray = monthArray.sort(
          (a: any, b: any) => parseInt(a.id) - parseInt(b.id)
        );

        // Removing duplicate years
        this.months = sortedArray.filter(
          (value: any, index: any, array: any) => {
            return index === 0 || value.month !== array[index - 1].month;
          }
        );
      }

    } else {
      this.getChallans();
    }
  }
  getMonthName(monthName: any) {
    if (monthName !== 'All') {
      let month = parseInt(monthName);
      ++month;
      let mt = month.toString().padStart(2, '0');
      let data = {
        year: this.yearVal,
        month: parseInt(mt),
      };
      this.getMonthlyReport(data);
    } else {
      let data = {
        year: this.yearVal,
        month: null,
      };
      this.getMonthlyReport(data);
    }
  }

  getMonthlyReport(dates: any) {
    this.calculations = {
      totalPcs: 0,
      totalArea: 0,
      totalGst: 0,
      totalAmount: 0,
      grossTotalAmount: 0,
    };
    ELEMENT_DATA.length = 0;

    let pcs: number = 0;
    let amount: number = 0;
    let area: number = 0;
    let profit: number = 0;
    let gst: number = 0;
    let count = 1;

    let dataArray = [];

    if (dates.year) {
      dataArray = this.allBills.filter((item: any) => {
        const date = new Date(item.chooseAdate);
        return dates.month
          ? date.getFullYear() === dates.year &&
              date.getMonth() + 1 === dates.month &&
              !item.isBillDeleted
          : date.getFullYear() === dates.year && !item.isBillDeleted;
      });
    } else {
      dataArray = this.allBills.filter((item: any) => !item.isBillDeleted);
    }
debugger
    dataArray.forEach((element: any) => {
      pcs = 0;
      amount = 0;
      area = 0;
      profit = 0;
      gst = 0;

      let date = this.customeService.convertDate(element.chooseAdate);

      element.challanNo.forEach((x: any) => {
        let challanData = this.allChallans.find(
          (cNo: any) => cNo.challanNo === x.challanNumber
        );
        if (!challanData || !challanData.carpetList) {
          console.error(`Challan data or carpet list is undefined for challan number: ${x.challanNumber}`);
          return; // Skip to the next iteration if carpetList is undefined
        }
        challanData.carpetList.forEach((carpet: any) => {
          this.allContainerStock.forEach((container: any) => {
            container.containerItem.forEach((item: any) => {


              if (carpet.barcodeNo == item.GerCarpetNo && carpet.isDeleted!=true   ) {
                const carpetArea = parseFloat(
                  carpet.area ? carpet.area : item.Area
                );
                const carpetPrice = parseFloat(
                  carpet.evkPrice ? carpet.evkPrice : item.EvKPrice
                );
                const carpetAmount = carpetArea * carpetPrice;
                pcs++;

                if (carpet.status != 'return') {
                  area += carpetArea;
                  amount += carpetAmount;
                } else {
                  area -= Math.abs(carpetArea);
                  amount -= Math.abs(carpetAmount);
                }
              }
            });
          });
        });
      });
amount=element?.discount?amount-element.discount:amount;
      profit = (amount * 13) / 100;
      gst = ((amount + profit) * 19) / 100;

      ELEMENT_DATA.push({
        id: element._id,
        index: 0,
        billNo: element.billNo,
        date: date,
        customer: element.wholesellerName,
        pcs: pcs,
        area: area,
        gst: gst,
        profit: profit,
        amount: amount,
        totalAmount: amount + profit + gst,
      });
    });
    this.sortData();
    this.dataSource = new MatTableDataSource(ELEMENT_DATA);
    this.dataSource.filteredData.forEach((x: any) => {
      this.calculations = {
        totalPcs: this.calculations.totalPcs + x.pcs,
        totalArea: this.calculations.totalArea + x.area,
        totalGst: this.calculations.totalGst + parseFloat(x.gst),
        totalAmount: this.calculations.totalAmount + x.amount,
        grossTotalAmount: this.calculations.grossTotalAmount + x.totalAmount,
      };
    });

    this.ngAfterViewInit();

    return;
  }

  resolveAllPromise() {
    this.ngxLoader.start();
    Promise.all([this.getChallans(), this.getContainerReceived()])
      .then(() => {
        this.getBills();
        this.ngxLoader.stop();
      })
      .catch((erro) => {
        Swal.fire({
          title: 'warning!',
          text: 'Error',
          icon: 'warning',
        });
      });
  }

  sortData() {
    ELEMENT_DATA.sort((a, b) => {
      if (b.billNo === a.billNo) {
        return this.compareDates(b.date, a.date);
      } else {
        return b.billNo.localeCompare(a.billNo);
      }
    });
    ELEMENT_DATA.forEach((element, index) => {
      element.index = index + 1;
    });
  }
  compareDates(date1: string, date2: string): number {
    const [day1, month1, year1] = date1.split('.').map(Number);
    const [day2, month2, year2] = date2.split('.').map(Number);
    const d1 = new Date(year1, month1 - 1, day1);
    const d2 = new Date(year2, month2 - 1, day2);
    return d1.getTime() - d2.getTime();
  }



  logAndExportCurrentPageData(): void {
    ;
    if (this.sort && this.paginator) {
      const sortedData = this.dataSource.filteredData

      const pageIndex = this.paginator.pageIndex;
      const pageSize = this.paginator.pageSize;
      const startIndex = pageIndex * pageSize;
      const endIndex = startIndex + pageSize;
      const currentPageData = sortedData.slice(startIndex, endIndex);



      let excelData  = sortedData.map((x:any,i:number)=>({
        // srNo:i+1,
        billNo:x.billNo,
        date:x.date,
        customer:x.customer,
        pcs:x.pcs,
        area:x.area.toFixed(2),
        amount:x.amount.toFixed(2),
        profit:x.profit.toFixed(2),
        gst:x.gst.toFixed(2),

        totalAmount:x.totalAmount.toFixed(2),
      }))
      const totalPcs = excelData.reduce((sum, item) => sum + parseFloat(item.pcs), 0);
      const totalArea = excelData.reduce((sum, item) => sum + parseFloat(item.area), 0);
      const totalGst = excelData.reduce((sum, item) => sum + parseFloat(item.gst), 0);
      const totalProfit = excelData.reduce((sum, item) => sum +parseFloat(item.profit), 0);
      const totalAmount = excelData.reduce((sum, item) => sum + parseFloat(item.amount), 0);
      const totalTotalAmount = excelData.reduce((sum, item) => sum + parseFloat(item.totalAmount), 0);


excelData = excelData.sort((a,b)=>a.billNo-b.billNo);

console.log(excelData);

      excelData.push({
        // srNo: 0,
        billNo: '',
        date: '',
        customer: '',
        pcs: '',
        area: '',
        gst: '',
        profit: '',
        amount: '',
        totalAmount: '',
      });
      // Add totals row
      excelData.push({
        // srNo: 0,
        billNo: '',
        date: '',
        customer: 'Total',
        pcs: totalPcs,
        area: totalArea,
        gst: totalGst,
        profit: totalProfit,
        amount: totalAmount,
        totalAmount: totalTotalAmount,
      });
      debugger
      this.excelService.exportBillExcelFile(excelData);
    } else {
      console.error('Paginator or Sort is not initialized.');
    }
  }


}
