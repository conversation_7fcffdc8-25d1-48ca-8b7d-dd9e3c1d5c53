const mongoose = require("mongoose");

const BillForretailerSchema = new mongoose.Schema(
  {
    billNo: {
      type: String,
      required: true,
    },
    chooseAdate: {
      type: String,
      required: true,
    },
    challanNo: {
      type: [String], // Make it an array of strings
      required: true,
    },
    wholesellerName: {
      type: String,
      required: true,
    },
    isBillDeleted: { type: Boolean, default: false },
  },
  { timestamps: true }
);

const BillForretailer = mongoose.model(
  "BillForretailer",
  BillForretailerSchema
);

module.exports = BillForretailer;
