const express = require('express');
const buyerOrderController = require('../../controller/manifacturing/buyerorder-controller');
const router = express.Router();

router.post('/buyerOrder', buyerOrderController.createBuyerOrder);
router.get('/buyerOrder/:id', buyerOrderController.getBuyerOrderById);
router.get('/buyerOrder', buyerOrderController.getAllBuyerOrders);
router.put('/buyerOrder/:id', buyerOrderController.updateBuyerOrder);
router.delete('/buyerOrder/:id', buyerOrderController.deleteBuyerOrder);
router.delete('/delete-item-buyerOrder/:id', buyerOrderController.deleteItem);
router.patch('/buyerOrder/updateField/:id', buyerOrderController.updateBuyerOrderField);
router.patch('/buyerOrder/deleteField/:id', buyerOrderController.deleteBuyerOrderField);
router.get('/:id/calculate', buyerOrderController.calculateFields);

module.exports = router;
