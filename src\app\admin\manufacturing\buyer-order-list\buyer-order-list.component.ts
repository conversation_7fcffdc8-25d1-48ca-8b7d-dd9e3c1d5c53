import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { ManufactureService } from '../../../services/manufacture.service';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { AssignedDetailsComponent } from './modals/assigned-details/assigned-details.component';
import { ReceivedDetailsComponent } from './modals/received-details/received-details.component';

export interface UserData {
  id: string;
  name: string;
  progress: string;
  fruit: string;
}

const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-buyer-order-list',
  templateUrl: './buyer-order-list.component.html',
  styleUrl: './buyer-order-list.component.css'
})



export class BuyerOrderListComponent implements OnInit{

  viewId:any;
  viewData:any;


  ngOnInit(): void {


     this.viewId = this.activedRoute.snapshot.paramMap.get('id') || '';
     if(this.viewId){
      this.getAllList()
     }
      // this.editedId = this.edit(this.editedId)
  }


constructor(
  private customeServicex:CustomeServiceService,
  private manufactureService:ManufactureService,
  private router:Router,
  private activedRoute:ActivatedRoute,
  private CustomeServiceService:CustomeServiceService,
  private dialog: MatDialog
){}


  displayedColumns: string[] = ['SrNo',
    'Quality',
    'Design',
    'GroundBorder',
    'size',
    'khapSize',
    'Pcs',
    'PcsesPending',
    'PcsesAssigned',
    'PcsesReceived',
    'Area',];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }



   viewAllData:any []= [];
   orderDate:any
  getAllList(){
    this.manufactureService.getAllBuyerOrder().subscribe((res:any)=>{
      this.viewData= res.find((x:any) => x._id === this.viewId);
      this.orderDate = this.CustomeServiceService.convertDate(this.viewData.orderDate)
      // Fetch all issues for this order
      this.manufactureService.getsOrderIssueList().subscribe((issuesRaw: any) => {
        const issues = Array.isArray(issuesRaw) ? issuesRaw : (issuesRaw?.data || []);
        this.viewAllData = [];

        // Process each item and calculate received pieces from issues
        this.viewAllData = this.viewData.items.map((x:any,i:number) => {
          // Find all issues for this item
          const itemIssues = (issues as any[]).filter((issue: any) =>
            (issue.buyerOrder === this.viewData._id || issue.buyerOrder?._id === this.viewData._id) &&
            (issue.itemId === x._id || issue.itemId?._id === x._id)
          );

          // Calculate received pieces from PCSReceived field in issues
          const pcsesReceived = itemIssues.reduce((total: number, issue: any) => {
            return total + (issue.PCSReceived || 0);
          }, 0);

          return {
            index: i + 1,
            _id: x._id,
            issueIds: itemIssues.map((issue: any) => issue._id),
            issues: itemIssues,
            quality:x.quality?.quality,
            Design:x.design?.design,
            GroundBorder:x.groundColour,
            size:x.sizeId?.sizeInYard,
            KhapSize:x.khapSize,
            Pcs:x.pcs,
            PcsesPending: x.PcsesPending !== undefined ? x.PcsesPending : x.pcs,
            PcsesAssigned:x.PcsesAssigned || 0,
            PcsesReceived: pcsesReceived,
            Area:x.totalArea,
          };
        });

        this.dataSource = new MatTableDataSource(this.viewAllData);
        this.ngAfterViewInit();
      });
    })
  }


  getSerialNumber(index:number){
    return index + 1
  }

  openAssignedDetails(row: any) {
    // Pass all issues for this item to the dialog
    this.dialog.open(AssignedDetailsComponent, {
      width: '900px',
      data: { issues: row.issues }
    });
  }


  openReceivedDetails(row: any){
    // Fetch received carpet details for this item's issues
    if (row.issueIds && row.issueIds.length > 0) {
      this.manufactureService.getReceivedCarpetsByIssueIds(row.issueIds).subscribe({
        next: (receivedCarpets: any) => {
          this.dialog.open(ReceivedDetailsComponent, {
            width: '1100px',
            data: { receivedCarpets: receivedCarpets }
          });
        },
        error: (error) => {
          console.error('Error fetching received carpet details:', error);
          this.dialog.open(ReceivedDetailsComponent, {
            width: '1100px',
            data: { receivedCarpets: [] }
          });
        }
      });
    } else {
      this.dialog.open(ReceivedDetailsComponent, {
        width: '1100px',
        data: { receivedCarpets: [] }
      });
    }
  }



}
