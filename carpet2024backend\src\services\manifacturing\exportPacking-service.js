const exportPackingRepository = require('../../repositories/manifacturing/exportPacking-repository');

const createExportPacking = async (data) => {
  return await exportPackingRepository.createExportPacking(data);
};

const getAllExportPackings = async () => {
  return await exportPackingRepository.getAllExportPackings();
};

const getExportPackingById = async (id) => {
  const packingList = await exportPackingRepository.getExportPackingById(id);
  if (!packingList) {
    throw new Error('Export packing list not found');
  }
  return packingList;
};

const updateExportPacking = async (id, data) => {
  const updatedPackingList = await exportPackingRepository.updateExportPacking(id, data);
  if (!updatedPackingList) {
    throw new Error('Export packing list not found');
  }
  return updatedPackingList;
};

const deleteExportPacking = async (id) => {
  const deletedPackingList = await exportPackingRepository.deleteExportPacking(id);
  if (!deletedPackingList) {
    throw new Error('Export packing list not found');
  }
  return deletedPackingList;
};

const getByInvoiceNo = async (invoiceNo) => {
  return await exportPackingRepository.getByInvoiceNo(invoiceNo);
};

module.exports = {
  createExportPacking,
  getAllExportPackings,
  getExportPackingById,
  updateExportPacking,
  deleteExportPacking,
  getByInvoiceNo
};