import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { ImporterService } from '../../../../services/importer.service';
import { CustomeServiceService } from '../../../../services/custome-service.service';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { error } from 'node:console';
import Swal from 'sweetalert2';
import { firstValueFrom } from 'rxjs';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { ExportDataServiceService } from '../../../../services/export-data-service.service';
const monthNames: any = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];
export interface Challans {
  challanNo: string;
  date: string;
  wholeseller: string;
  retailerOutlet: string;
  id: string;
  index: number;
  pcs: number;
  area: string;
  amount: string;
}

const ELEMENT_DATA2: Challans[] = [];
@Component({
  selector: 'app-challan-report',
  templateUrl: './challan-report.component.html',
  styleUrl: './challan-report.component.css',
})
export class ChallanReportComponent implements OnInit {
  displayedColumns: string[] = [
    'id',
    'challanNo',
    'date',
    'wholeseller',
    'retailerOutlet',
    'pcs',
    'area',
    'amount',
    'action',
  ];
  dataSource = new MatTableDataSource(ELEMENT_DATA2);
  allChallans: any;
  allStock: any;

  constructor(
    private _service: ImporterService,
    private customeService: CustomeServiceService,
    private ngxLoader: NgxUiLoaderService,
    private excelService: ExportDataServiceService
  ) {}
  allBills: any;

  selectedOption: string = 'pending'; // Default selected option

  onOptionChange() {

    console.log('Selected Option:', this.selectedOption);
    this.getChallans(this.selectedOption);
  }

  ngOnInit(): void {
    this.resolvePromise(this.selectedOption);
    // this.getAllStock();
    // setTimeout(() => {
    //   this.getChallans();
    // }, 2000);
  }
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  years: any = [];
  months: any = [];
  yearVal: any = {};
  customerList: any = [];
  challanNoList: any = [];

  dataArray: any = [];
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  async getAllStock() {
    let resp = await firstValueFrom(this._service.getAllContainerRecieved());
    this.allStock = resp;
  }
  excelArray:any=[];
  async getChallans(data: string) {
    let resp = await firstValueFrom(this._service.getChallans(data));
    this.allChallans = resp;
    if (Array.isArray(resp)) {
      ELEMENT_DATA2.length = 0;
      resp.forEach((element: any, ind: number) => {
        const dateObject = new Date(element.chooseAdate);
        const year = dateObject.getFullYear();
        let exist = this.dataArray.some((y: any) => y.year === year);
        if (!exist) {
          this.dataArray.push({ year });
        }

        let date = this.customeService.convertDate(element.chooseAdate);
        let totalCalc = this.calculate(element);
        if (element.isDeleted !== true) {
debugger

          ELEMENT_DATA2.push({
            challanNo: element.challanNo,
            date: date,
            wholeseller: element.wholeseller
              ? element.wholeseller
              : element.RetailerName,
            retailerOutlet: element.retailerOutlet,
            id: element._id,
            pcs: totalCalc.pcs,
            area: totalCalc.area.toFixed(2),
            amount: totalCalc.amount.toFixed(2),
            index: 0,
          });
        }
      });
      this.sortData();
      this.dataSource = new MatTableDataSource(ELEMENT_DATA2);

      this.ngAfterViewInit();
      this.yearList();
      return;
    }
  }
  yearList() {
    if (this.dataArray) {
      const sortedArray = this.dataArray.sort(
        (a: any, b: any) => parseInt(b.year) - parseInt(a.year)
      );

      // Removing duplicate years
      this.years = sortedArray.filter((value: any, index: any, array: any) => {
        return index === 0 || value.year !== array[index - 1].year;
      });

      console.log(this.years);
    }
  }
  getMonth(data: any) {
    if (data !== 'All') {
      let year = { year: data };
      this.yearVal = data;
      let monthArray: any = [];

      if (this.allChallans) {
        this.allChallans.map((val: any) => {
          const dateObject = new Date(val.chooseAdate);
          const month = dateObject.getMonth(); // Months are zero-indexed, so add 1

          const year = dateObject.getFullYear();
          monthArray.push({
            month: monthNames[month],
            id: month,
          });
        });
        const sortedArray = monthArray.sort(
          (a: any, b: any) => parseInt(a.id) - parseInt(b.id)
        );

        // Removing duplicate years
        this.months = sortedArray.filter(
          (value: any, index: any, array: any) => {
            return index === 0 || value.month !== array[index - 1].month;
          }
        );
      }
      console.log(this.months);
    } else {
      this.getChallans(this.selectedOption);
    }
  }

  getMonthName(monthName: any) {

    if (monthName !== 'All') {
      let month = parseInt(monthName);
      ++month;
      let mt = month.toString().padStart(2, '0');
      let data = {
        year: this.yearVal,
        month: parseInt(mt),
      };
      this.getMonthlyReport(data);
    } else {
      let data = {
        year: this.yearVal,
        month: null,
      };
      this.getMonthlyReport(data);
    }

  }

  getMonthlyReport(dates: any) {
    this.customerList.length = 0;
    this.challanNoList.length = 0;
    let challans = [...this.allChallans];
    ELEMENT_DATA2.length = 0;
    challans.forEach((element: any, ind: number) => {
      let selectedDate = new Date(element.chooseAdate)
        .toISOString()
        .slice(0, 7);
      if (dates.month != null) {
        let formattedMonth = String(dates.month).padStart(2, '0');
        let formattedDate = dates.year + '-' + formattedMonth;
        if (selectedDate === formattedDate) {
          this.customerList.push(element.wholeseller);
          this.challanNoList.push(element.challanNo);
          let date = this.customeService.convertDate(element.chooseAdate);
          let totalCalc = this.calculate(element);

          ELEMENT_DATA2.push({
            challanNo: element.challanNo,
            date: date,
            wholeseller: element.wholeseller,
            retailerOutlet: element.retailerOutlet,
            id: element._id,
            pcs: totalCalc.pcs,
            area: totalCalc.area.toFixed(2),
            amount: totalCalc.amount.toFixed(2),
            index: 0,
          });
        }
      } else {
        if (selectedDate.split('-')[0] === dates.year.toString()) {
          this.customerList.push(element.wholeseller);
          this.challanNoList.push(element.challanNo);
          let date = this.customeService.convertDate(element.chooseAdate);
          let totalCalc = this.calculate(element);
          ELEMENT_DATA2.push({
            challanNo: element.challanNo,
            date: date,
            wholeseller: element.wholeseller,
            retailerOutlet: element.retailerOutlet,
            id: element._id,
            pcs: totalCalc.pcs,
            area: totalCalc.area.toFixed(2),
            amount: totalCalc.amount.toFixed(2),
            index: ind + 1,
          });
        }
      }
    });
    console.log('elementdate', ELEMENT_DATA2);

    // Log the sorted data
    console.log(ELEMENT_DATA2);
    this.sortData();
    this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
    this.ngAfterViewInit();

    this.customerList = this.customerList.filter(
      (value: any, index: number, self: any[]) => {
        return self.indexOf(value) === index;
      }
    );
    return;
  }
  sortData() {
    ELEMENT_DATA2.sort((a, b) => {
      if (a.challanNo === b.challanNo) {
        return this.compareDates(a.date, b.date);
      } else {
        return a.challanNo.localeCompare(b.challanNo);
      }
    });
    ELEMENT_DATA2.forEach((element, index) => {
      element.index = index + 1;
    });
  }
  compareDates(date1: string, date2: string): number {
    const [day1, month1, year1] = date1.split('.').map(Number);
    const [day2, month2, year2] = date2.split('.').map(Number);
    const d1 = new Date(year1, month1 - 1, day1);
    const d2 = new Date(year2, month2 - 1, day2);
    return d2.getTime() - d1.getTime();
  }
  calculate(element: any) {
    let area = 0;
    let amount = 0;
    let pcs = 0;

    element.carpetList.forEach((el: any) => {
      this.allStock.forEach((x: any) => {
        x.containerItem.forEach((y: any) => {
          let yarea = parseFloat(y.Area);
          let elarea = parseFloat(el.area);
          if (parseInt(y.GerCarpetNo) === parseInt(el.barcodeNo) && el.isDeleted!=true) {

            if (el.status !== 'return') {
              area += parseFloat(el.area ? el.area : y.Area);
              amount += el.amount
                ? parseFloat(el.amount)
                : parseFloat(el.area ? el.area : y.Area) *
                  parseFloat(el.evkPrice ? el.evkPrice : y.EvKPrice);
            } else {
              el.area
                ? (area = parseFloat(el.area ? el.area : y.Area))
                : (area -= parseFloat(el.area ? el.area : y.Area));
              amount += el.amount
                ? parseFloat(el.amount)
                : parseFloat(el.area ? el.area : -yarea) *
                  parseFloat(el.evkPrice ? el.evkPrice : y.EvKPrice);
            }
            pcs++;
          }
        });
      });


        this.excelArray.push({
          challanNo: element.challanNo,
          date: element.chooseAdate,
          wholeseller: element.wholeseller ? element.wholeseller : element.RetailerName,
          barcode: el.barcodeNo,
          type: el.status,
          retailerOutlet: element.retailerOutlet,
          amount: el.amount,
          size: el.size,
          area: el.area
        });

    });

    let totalCalculation = {
      area: area,
      amount: amount,
      pcs: pcs,
    };

    return totalCalculation;

    //     element.carpetList.forEach((el: any) => {
    //       this.allStock.forEach((x: any) => {
    //         x.containerItem.forEach((y: any) => {
    // let yarea = parseFloat(y.Area);
    // let elarea =parseFloat(el.area);
    //           if (parseInt(y.GerCarpetNo) === parseInt(el.barcodeNo)){
    //             (area =  el.status!='return'?area + parseFloat(el.area?el.area:y.Area):area *el.area?-elarea:-yarea),

    //                 amount = (el.status!=='return')?amount + el.amount?el.amount: parseFloat(el.area?el.area:y.Area) * parseFloat(el.evkPrice?el.evkPrice:y.EvKPrice):
    //                 amount +el.amount?el.amount: (el.area?-elarea:-yarea )* parseFloat(el.evkPrice?el.evkPrice:y.EvKPrice),
    //                 pcs++;
    //           }
    //           // if (parseInt(y.GerCarpetNo) === parseInt(el.barcodeNo) && el.status!=='return') {
    //           //
    //           //   (area = area + parseFloat(el.area?el.area:y.Area)),

    //           //     (amount = amount + parseFloat(el.area?el.area:y.Area) * parseFloat(el.evkPrice?el.evkPrice:y.EvKPrice)),
    //           //     pcs++;
    //           // }else{

    //           // }
    //         });
    //       });
    //     });
    //     let totalCalculation = {};
    //     return (totalCalculation = {
    //       area: area,
    //       amount: amount,
    //       pcs: pcs,
    //     });
  }
  deleteChallan(id: string) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be delete to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        this._service.deleteChallan(id, 'undefined').subscribe(
          (resp: any) => {

            if (resp.message === 'Successfully delete challan') {
              Swal.fire({
                title: 'Deleted!',
                text: 'Your challan has been deleted.',
                icon: 'success',
              });
              this.getChallans(this.selectedOption);
            }
          },
          (err) => {

            Swal.fire({
              title: 'warning!',
              text: err.error.message,
              icon: 'warning',
            });
          }
        );
      }
    });
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  resolvePromise(data: string) {
    this.ngxLoader.start();
    Promise.all([this.getAllStock()])
      .then(() => {
        this.getChallans(data);
        this.ngxLoader.stop();
      })
      .catch((error) => {
        Swal.fire({
          title: 'Warning',
          text: 'Something went wrong!',
          icon: 'warning',
        });
      });
  }

  exportAsXLSX(): void {
    this.excelService.exportAsExcelFile(
      this.dataSource.filteredData,
      'export-to-excel'
    );
  }
  logDataSourceData(): void {
debugger

if (this.sort && this.paginator) {
  const sortedData = this.dataSource.sortData(this.dataSource.filteredData, this.sort);
  const pageIndex = this.paginator.pageIndex;
  const pageSize = this.paginator.pageSize;
  const startIndex = pageIndex * pageSize;
  const endIndex = startIndex + pageSize;
  const currentPageData = sortedData.slice(startIndex, endIndex);

  console.log(currentPageData);

  // Get the set of challanNo values in the current page data
  const currentPageChallanNos = new Set(currentPageData.map(x => x.challanNo));

  // Filter the excelArray based on the current page's challanNo values
  const excelData = this.excelArray.filter((item:any) => currentPageChallanNos.has(item.challanNo));

  // Map the data to the desired structure for the Excel file
  const mappedExcelData = excelData.map((x:any, i:number) => ({
    srNo: i + 1,
    challanNo: x.challanNo,
    date: x.date,
    wholeseller: x.wholeseller,
    barcode: x.barcode,
    type: x.type,
    retailerOutlet: x.retailerOutlet,



  }));
  this.excelService.exportAsExcelFile(mappedExcelData,'export-to-excel');
} else {
  console.error('Paginator or Sort is not initialized.');
}
  }
}
