/*! Select for DataTables 1.6.0
 * 2015-2023 SpryMedia Ltd - datatables.net/license/mit
 */
import $ from"jquery";import DataTable from"datatables.net";function cellRange(n,e,t){function l(t,l){l<t&&(e=l,l=t,t=e);var e,s=!1;return n.columns(":visible").indexes().filter(function(e){return e===t&&(s=!0),e===l?!(s=!1):s})}function s(t,l){var e,s=n.rows({search:"applied"}).indexes(),c=(s.indexOf(t)>s.indexOf(l)&&(e=l,l=t,t=e),!1);return s.filter(function(e){return e===t&&(c=!0),e===l?!(c=!1):c})}var c,t=n.cells({selected:!0}).any()||t?(c=l(t.column,e.column),s(t.row,e.row)):(c=l(0,e.column),s(0,e.row)),t=n.cells(t,c).flatten();n.cells(e,{selected:!0}).any()?n.cells(t).deselect():n.cells(t).select()}function disableMouseSelection(e){var t=e.settings()[0]._select.selector;$(e.table().container()).off("mousedown.dtSelect",t).off("mouseup.dtSelect",t).off("click.dtSelect",t),$("body").off("click.dtSelect"+_safeId(e.table().node()))}function enableMouseSelection(a){var o,t=$(a.table().container()),l=a.settings()[0],s=l._select.selector;t.on("mousedown.dtSelect",s,function(e){(e.shiftKey||e.metaKey||e.ctrlKey)&&t.css("-moz-user-select","none").one("selectstart.dtSelect",s,function(){return!1}),window.getSelection&&(o=window.getSelection())}).on("mouseup.dtSelect",s,function(){t.css("-moz-user-select","")}).on("click.dtSelect",s,function(e){var t,l=a.select.items();if(o){var s=window.getSelection();if((!s.anchorNode||$(s.anchorNode).closest("table")[0]===a.table().node())&&s!==o)return}var c,s=a.settings()[0],n=a.settings()[0].oClasses.sWrapper.trim().replace(/ +/g,".");$(e.target).closest("div."+n)[0]==a.table().container()&&(n=a.cell($(e.target).closest("td, th"))).any()&&(c=$.Event("user-select.dt"),eventTrigger(a,c,[l,n,e]),c.isDefaultPrevented()||(c=n.index(),"row"===l?(t=c.row,typeSelect(e,a,s,"row",t)):"column"===l?(t=n.index().column,typeSelect(e,a,s,"column",t)):"cell"===l&&(t=n.index(),typeSelect(e,a,s,"cell",t)),s._select_lastCell=c))}),$("body").on("click.dtSelect"+_safeId(a.table().node()),function(e){var t;!l._select.blurable||$(e.target).parents().filter(a.table().container()).length||0===$(e.target).parents("html").length||$(e.target).parents("div.DTE").length||(t=$.Event("select-blur.dt"),eventTrigger(a,t,[e.target,e]),t.isDefaultPrevented()||clear(l,!0))})}function eventTrigger(e,t,l,s){s&&!e.flatten().length||("string"==typeof t&&(t+=".dt"),l.unshift(e),$(e.table().node()).trigger(t,l))}function info(s){var c,n,a,o,e=s.settings()[0];e._select.info&&e.aanFeatures.i&&"api"!==s.select.style()&&(c=s.rows({selected:!0}).flatten().length,n=s.columns({selected:!0}).flatten().length,a=s.cells({selected:!0}).flatten().length,o=function(e,t,l){e.append($('<span class="select-item"/>').append(s.i18n("select."+t+"s",{_:"%d "+t+"s selected",0:"",1:"1 "+t+" selected"},l)))},$.each(e.aanFeatures.i,function(e,t){t=$(t);var l=$('<span class="select-info"/>'),s=(o(l,"row",c),o(l,"column",n),o(l,"cell",a),t.children("span.select-info"));s.length&&s.remove(),""!==l.text()&&t.append(l)}))}function init(a){var c=new DataTable.Api(a);a._select_init=!0,a.aoRowCreatedCallback.push({fn:function(e,t,l){var s,c,n=a.aoData[l];for(n._select_selected&&$(e).addClass(a._select.className),s=0,c=a.aoColumns.length;s<c;s++)(a.aoColumns[s]._select_selected||n._selected_cells&&n._selected_cells[s])&&$(n.anCells[s]).addClass(a._select.className)},sName:"select-deferRender"}),c.on("preXhr.dt.dtSelect",function(e,t){var l,s;t===c.settings()[0]&&(l=c.rows({selected:!0}).ids(!0).filter(function(e){return void 0!==e}),s=c.cells({selected:!0}).eq(0).map(function(e){var t=c.row(e.row).id(!0);return t?{row:t,column:e.column}:void 0}).filter(function(e){return void 0!==e}),c.one("draw.dt.dtSelect",function(){c.rows(l).select(),s.any()&&s.each(function(e){c.cells(e.row,e.column).select()})}))}),c.on("draw.dtSelect.dt select.dtSelect.dt deselect.dtSelect.dt info.dt",function(){info(c),c.state.save()}),c.on("destroy.dtSelect",function(){$(c.rows({selected:!0}).nodes()).removeClass(c.settings()[0]._select.className),disableMouseSelection(c),c.off(".dtSelect"),$("body").off(".dtSelect"+_safeId(c.table().node()))})}function rowColumnRange(e,t,l,s){var c,n=e[t+"s"]({search:"applied"}).indexes(),s=$.inArray(s,n),a=$.inArray(l,n);e[t+"s"]({selected:!0}).any()||-1!==s?(a<s&&(c=a,a=s,s=c),n.splice(a+1,n.length),n.splice(0,s)):n.splice($.inArray(l,n)+1,n.length),e[t](l,{selected:!0}).any()?(n.splice($.inArray(l,n),1),e[t+"s"](n).deselect()):e[t+"s"](n).select()}function clear(e,t){!t&&"single"!==e._select.style||((t=new DataTable.Api(e)).rows({selected:!0}).deselect(),t.columns({selected:!0}).deselect(),t.cells({selected:!0}).deselect())}function typeSelect(e,t,l,s,c){var n=t.select.style(),a=t.select.toggleable(),o=t[s](c,{selected:!0}).any();o&&!a||("os"===n?e.ctrlKey||e.metaKey?t[s](c).select(!o):e.shiftKey?"cell"===s?cellRange(t,c,l._select_lastCell||null):rowColumnRange(t,s,c,l._select_lastCell?l._select_lastCell[s]:null):(a=t[s+"s"]({selected:!0}),o&&1===a.flatten().length?t[s](c).deselect():(a.deselect(),t[s](c).select())):"multi+shift"==n&&e.shiftKey?"cell"===s?cellRange(t,c,l._select_lastCell||null):rowColumnRange(t,s,c,l._select_lastCell?l._select_lastCell[s]:null):t[s](c).select(!o))}function _safeId(e){return e.id.replace(/[^a-zA-Z0-9\-\_]/g,"-")}DataTable.select={},DataTable.select.version="1.6.0",DataTable.select.init=function(c){var e,t,l,s,n,a,o,i,r,u,d,f=c.settings()[0];f._select||(e=c.state.loaded(),t=function(e,t,l){if(null!==l&&void 0!==l.select){if(c.rows({selected:!0}).any()&&c.rows().deselect(),void 0!==l.select.rows&&c.rows(l.select.rows).select(),c.columns({selected:!0}).any()&&c.columns().deselect(),void 0!==l.select.columns&&c.columns(l.select.columns).select(),c.cells({selected:!0}).any()&&c.cells().deselect(),void 0!==l.select.cells)for(var s=0;s<l.select.cells.length;s++)c.cell(l.select.cells[s].row,l.select.cells[s].column).select();c.state.save()}},c.on("stateSaveParams",function(e,t,l){l.select={},l.select.rows=c.rows({selected:!0}).ids(!0).toArray(),l.select.columns=c.columns({selected:!0})[0],l.select.cells=c.cells({selected:!0})[0].map(function(e){return{row:c.row(e.row).id(!0),column:e.column}})}).on("stateLoadParams",t).one("init",function(){t(0,0,e)}),s=f.oInit.select,l=DataTable.defaults.select,l=void 0===s?l:s,s="row",i=o=!(a=!(n="api")),r="td, th",d=!(u="selected"),f._select={},!0===l?(n="os",d=!0):"string"==typeof l?(n=l,d=!0):$.isPlainObject(l)&&(void 0!==l.blurable&&(a=l.blurable),void 0!==l.toggleable&&(o=l.toggleable),void 0!==l.info&&(i=l.info),void 0!==l.items&&(s=l.items),d=(n=void 0!==l.style?l.style:"os",!0),void 0!==l.selector&&(r=l.selector),void 0!==l.className&&(u=l.className)),c.select.selector(r),c.select.items(s),c.select.style(n),c.select.blurable(a),c.select.toggleable(o),c.select.info(i),f._select.className=u,$.fn.dataTable.ext.order["select-checkbox"]=function(t,e){return this.api().column(e,{order:"index"}).nodes().map(function(e){return"row"===t._select.items?$(e).parent().hasClass(t._select.className):"cell"===t._select.items&&$(e).hasClass(t._select.className)})},!d&&$(c.table().node()).hasClass("selectable")&&c.select.style("os"))},$.each([{type:"row",prop:"aoData"},{type:"column",prop:"aoColumns"}],function(e,i){DataTable.ext.selector[i.type].push(function(e,t,l){var s,c=t.selected,n=[];if(!0!==c&&!1!==c)return l;for(var a=0,o=l.length;a<o;a++)s=e[i.prop][l[a]],(!0===c&&!0===s._select_selected||!1===c&&!s._select_selected)&&n.push(l[a]);return n})}),DataTable.ext.selector.cell.push(function(e,t,l){var s,c=t.selected,n=[];if(void 0===c)return l;for(var a=0,o=l.length;a<o;a++)s=e.aoData[l[a].row],(!0!==c||!s._selected_cells||!0!==s._selected_cells[l[a].column])&&(!1!==c||s._selected_cells&&s._selected_cells[l[a].column])||n.push(l[a]);return n});var apiRegister=DataTable.Api.register,apiRegisterPlural=DataTable.Api.registerPlural;function i18n(t,l){return function(e){return e.i18n("buttons."+t,l)}}function namespacedEvents(e){e=e._eventNamespace;return"draw.dt.DT"+e+" select.dt.DT"+e+" deselect.dt.DT"+e}function enabled(e,t){return!(-1===$.inArray("rows",t.limitTo)||!e.rows({selected:!0}).any())||(!(-1===$.inArray("columns",t.limitTo)||!e.columns({selected:!0}).any())||!(-1===$.inArray("cells",t.limitTo)||!e.cells({selected:!0}).any()))}apiRegister("select()",function(){return this.iterator("table",function(e){DataTable.select.init(new DataTable.Api(e))})}),apiRegister("select.blurable()",function(t){return void 0===t?this.context[0]._select.blurable:this.iterator("table",function(e){e._select.blurable=t})}),apiRegister("select.toggleable()",function(t){return void 0===t?this.context[0]._select.toggleable:this.iterator("table",function(e){e._select.toggleable=t})}),apiRegister("select.info()",function(t){return void 0===t?this.context[0]._select.info:this.iterator("table",function(e){e._select.info=t})}),apiRegister("select.items()",function(t){return void 0===t?this.context[0]._select.items:this.iterator("table",function(e){e._select.items=t,eventTrigger(new DataTable.Api(e),"selectItems",[t])})}),apiRegister("select.style()",function(l){return void 0===l?this.context[0]._select.style:this.iterator("table",function(e){e._select||DataTable.select.init(new DataTable.Api(e)),e._select_init||init(e),e._select.style=l;var t=new DataTable.Api(e);disableMouseSelection(t),"api"!==l&&enableMouseSelection(t),eventTrigger(new DataTable.Api(e),"selectStyle",[l])})}),apiRegister("select.selector()",function(t){return void 0===t?this.context[0]._select.selector:this.iterator("table",function(e){disableMouseSelection(new DataTable.Api(e)),e._select.selector=t,"api"!==e._select.style&&enableMouseSelection(new DataTable.Api(e))})}),apiRegisterPlural("rows().select()","row().select()",function(e){var l=this;return!1===e?this.deselect():(this.iterator("row",function(e,t){clear(e),e.aoData[t]._select_selected=!0,$(e.aoData[t].nTr).addClass(e._select.className)}),this.iterator("table",function(e,t){eventTrigger(l,"select",["row",l[t]],!0)}),this)}),apiRegister("row().selected()",function(){var e=this.context[0];return!!(e&&this.length&&e.aoData[this[0]]&&e.aoData[this[0]]._select_selected)}),apiRegisterPlural("columns().select()","column().select()",function(e){var l=this;return!1===e?this.deselect():(this.iterator("column",function(e,t){clear(e),e.aoColumns[t]._select_selected=!0;t=new DataTable.Api(e).column(t);$(t.header()).addClass(e._select.className),$(t.footer()).addClass(e._select.className),t.nodes().to$().addClass(e._select.className)}),this.iterator("table",function(e,t){eventTrigger(l,"select",["column",l[t]],!0)}),this)}),apiRegister("column().selected()",function(){var e=this.context[0];return!!(e&&this.length&&e.aoColumns[this[0]]&&e.aoColumns[this[0]]._select_selected)}),apiRegisterPlural("cells().select()","cell().select()",function(e){var l=this;return!1===e?this.deselect():(this.iterator("cell",function(e,t,l){clear(e);t=e.aoData[t];void 0===t._selected_cells&&(t._selected_cells=[]),t._selected_cells[l]=!0,t.anCells&&$(t.anCells[l]).addClass(e._select.className)}),this.iterator("table",function(e,t){eventTrigger(l,"select",["cell",l.cells(l[t]).indexes().toArray()],!0)}),this)}),apiRegister("cell().selected()",function(){var e=this.context[0];if(e&&this.length){e=e.aoData[this[0][0].row];if(e&&e._selected_cells&&e._selected_cells[this[0][0].column])return!0}return!1}),apiRegisterPlural("rows().deselect()","row().deselect()",function(){var l=this;return this.iterator("row",function(e,t){e.aoData[t]._select_selected=!1,e._select_lastCell=null,$(e.aoData[t].nTr).removeClass(e._select.className)}),this.iterator("table",function(e,t){eventTrigger(l,"deselect",["row",l[t]],!0)}),this}),apiRegisterPlural("columns().deselect()","column().deselect()",function(){var l=this;return this.iterator("column",function(s,e){s.aoColumns[e]._select_selected=!1;var t=new DataTable.Api(s),l=t.column(e);$(l.header()).removeClass(s._select.className),$(l.footer()).removeClass(s._select.className),t.cells(null,e).indexes().each(function(e){var t=s.aoData[e.row],l=t._selected_cells;!t.anCells||l&&l[e.column]||$(t.anCells[e.column]).removeClass(s._select.className)})}),this.iterator("table",function(e,t){eventTrigger(l,"deselect",["column",l[t]],!0)}),this}),apiRegisterPlural("cells().deselect()","cell().deselect()",function(){var l=this;return this.iterator("cell",function(e,t,l){t=e.aoData[t];void 0!==t._selected_cells&&(t._selected_cells[l]=!1),t.anCells&&!e.aoColumns[l]._select_selected&&$(t.anCells[l]).removeClass(e._select.className)}),this.iterator("table",function(e,t){eventTrigger(l,"deselect",["cell",l[t]],!0)}),this});var _buttonNamespace=0;$.extend(DataTable.ext.buttons,{selected:{text:i18n("selected","Selected"),className:"buttons-selected",limitTo:["rows","columns","cells"],init:function(e,t,l){var s=this;l._eventNamespace=".select"+_buttonNamespace++,e.on(namespacedEvents(l),function(){s.enable(enabled(e,l))}),this.disable()},destroy:function(e,t,l){e.off(l._eventNamespace)}},selectedSingle:{text:i18n("selectedSingle","Selected single"),className:"buttons-selected-single",init:function(t,e,l){var s=this;l._eventNamespace=".select"+_buttonNamespace++,t.on(namespacedEvents(l),function(){var e=t.rows({selected:!0}).flatten().length+t.columns({selected:!0}).flatten().length+t.cells({selected:!0}).flatten().length;s.enable(1===e)}),this.disable()},destroy:function(e,t,l){e.off(l._eventNamespace)}},selectAll:{text:i18n("selectAll","Select all"),className:"buttons-select-all",action:function(){var e=this.select.items();this[e+"s"]().select()}},selectNone:{text:i18n("selectNone","Deselect all"),className:"buttons-select-none",action:function(){clear(this.settings()[0],!0)},init:function(t,e,l){var s=this;l._eventNamespace=".select"+_buttonNamespace++,t.on(namespacedEvents(l),function(){var e=t.rows({selected:!0}).flatten().length+t.columns({selected:!0}).flatten().length+t.cells({selected:!0}).flatten().length;s.enable(0<e)}),this.disable()},destroy:function(e,t,l){e.off(l._eventNamespace)}},showSelected:{text:i18n("showSelected","Show only selected"),className:"buttons-show-selected",action:function(e,s,t,l){var c;l._filter?(-1!==(c=DataTable.ext.search.indexOf(l._filter))&&(DataTable.ext.search.splice(c,1),l._filter=null),this.active(!1)):(l._filter=c=function(e,t,l){return e!==s.settings()[0]||e.aoData[l]._select_selected},DataTable.ext.search.push(c),this.active(!0)),s.draw()}}}),$.each(["Row","Column","Cell"],function(e,t){var c=t.toLowerCase();DataTable.ext.buttons["select"+t+"s"]={text:i18n("select"+t+"s","Select "+c+"s"),className:"buttons-select-"+c+"s",action:function(){this.select.items(c)},init:function(e){var s=this;e.on("selectItems.dt.DT",function(e,t,l){s.active(l===c)})}}}),$.fn.DataTable.select=DataTable.select,$(document).on("preInit.dt.dtSelect",function(e,t){"dt"===e.namespace&&DataTable.select.init(new DataTable.Api(t))});export default DataTable;