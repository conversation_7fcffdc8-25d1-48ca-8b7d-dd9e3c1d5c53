<div class="px-3">
  <!-- Start Content-->
  <div class="container-fluid vh-100">
    <!-- start page title -->
    <div class="py-3 py-lg-4">
      <div class="row">
        <div class="col-lg-6">
          <h4 class="page-title mb-0">Bar Code Details</h4>
        </div>
      </div>
      <hr />

      <!-- Form row -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <mat-radio-group
                    aria-label="Select an option"
                    [(ngModel)]="selectedOption"
                    (change)="selectOption()"
                  >
                    <mat-radio-button value="single">Single</mat-radio-button>
                    <mat-radio-button value="all">All</mat-radio-button>
                  </mat-radio-group>
                </div>
              </div>
              <form [formGroup]="frmCrapetDetails" (ngSubmit)="sendData()">
                <div class="row">
                  @if(selectedOption!=='single'){

                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline">
                      <mat-label>From date</mat-label>
                      <input
                        matInput
                        [matDatepicker]="picker1"
                        formControlName="fromDate"
                      />
                      <mat-hint>MM/DD/YYYY</mat-hint>
                      <mat-datepicker-toggle
                        matIconSuffix
                        [for]="picker1"
                      ></mat-datepicker-toggle>
                      <mat-datepicker #picker1></mat-datepicker>
                    </mat-form-field>
                  </div>
                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline">
                      <mat-label>To date</mat-label>
                      <input
                        matInput
                        [matDatepicker]="picker"
                        formControlName="toDate"
                      />
                      <mat-hint>MM/DD/YYYY</mat-hint>
                      <mat-datepicker-toggle
                        matIconSuffix
                        [for]="picker"
                      ></mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                  </div>
                  }@else{
                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline">
                      <mat-label>Bar Code</mat-label>
                      <input
                        matInput
                        placeholder="Placeholder"
                        formControlName="inputBcr"
                      />
                    </mat-form-field>
                  </div>
                  }
                  <div class="mb-2 col-md-4" style="padding-top: 10px">
                    <button mat-flat-button color="primary"><i class="fa fa-search" aria-hidden="true"></i>Search</button>
&nbsp;
                    <button
                      type="button"
                      mat-flat-button
                      color="primary"
                      (click)="exportAsXLSX()"
                    >
                    <i class="fa fa-file-excel-o" aria-hidden="true"></i> Export To Excel
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
          <!-- end col -->

          <!-- end col -->
        </div>

        <!-- end row -->

        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-body">
                <div class="table-responsive">
                  <table
                    mat-table
                    [dataSource]="dataSource"
                    class="mat-elevation-z8"
                  >
                    <ng-container matColumnDef="index">
                      <th mat-header-cell *matHeaderCellDef>No.</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.index }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="challanNo">
                      <th mat-header-cell *matHeaderCellDef>Challan No</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.challanNo }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="challanDate">
                      <th mat-header-cell *matHeaderCellDef>Date</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.challanDate }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="challanCustomer">
                      <th mat-header-cell *matHeaderCellDef>Customer</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.challanCustomer }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="challanStatus">
                      <th mat-header-cell *matHeaderCellDef>Status</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.challanStatus }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="gerCarpetNo">
                      <th mat-header-cell *matHeaderCellDef>Carpet No</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.gerCarpetNo }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="qualityDesign">
                      <th mat-header-cell *matHeaderCellDef>Quality Design</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.qualityDesign }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="color">
                      <th mat-header-cell *matHeaderCellDef>Color</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.color }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="cCode">
                      <th mat-header-cell *matHeaderCellDef>Color Code</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.cCode }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="qCode">
                      <th mat-header-cell *matHeaderCellDef>Quality Code</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.qCode }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="size">
                      <th mat-header-cell *matHeaderCellDef>Size</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.size }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="sCode">
                      <th mat-header-cell *matHeaderCellDef>Size Code</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.sCode }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="area">
                      <th mat-header-cell *matHeaderCellDef>Area</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.area }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="evkPrice">
                      <th mat-header-cell *matHeaderCellDef>Evk Price</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.evkPrice }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="amount">
                      <th mat-header-cell *matHeaderCellDef>Amount</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.amount }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="billNo">
                      <th mat-header-cell *matHeaderCellDef>Bill No</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.billNo }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="billDate">
                      <th mat-header-cell *matHeaderCellDef>Date</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.billDate }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="billCustomer">
                      <th mat-header-cell *matHeaderCellDef>Customer</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.billCustomer }}
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr
                      mat-row
                      *matRowDef="let row; columns: displayedColumns"
                    ></tr>
                  </table>
                </div>
                <mat-paginator
                  [length]="100"
                  [pageSize]="10"
                  [pageSizeOptions]="[5, 10, 25, 100]"
                  aria-label="Select page"
                >
                </mat-paginator>
              </div>
              <!-- end card body-->
            </div>
            <!-- end card -->
          </div>
          <!-- end col-->
        </div>
        <!-- end row-->
      </div>
      <!-- end page title -->
    </div>
    <!-- container -->
  </div>
  <!-- content -->
</div>
