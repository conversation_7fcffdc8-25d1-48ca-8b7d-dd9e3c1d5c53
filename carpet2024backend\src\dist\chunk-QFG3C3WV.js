import{a as Ku}from"./chunk-3FUPWI55.js";import{a as ye,b as <PERSON>u}from"./chunk-7RV22KDK.js";import{I as pc,J as Qu,a as rc,b as oc,g as ac,h as sc,i as lc,j as cc,k as Aa,l as uc,t as hc,u as fc,x as dc}from"./chunk-CATJFVJK.js";import{$ as Wl,A as _o,Aa as Vl,Cb as Is,Ec as Zl,Gb as Co,Ib as yi,Lc as Ql,Mc as tc,Nc as ka,Oc as ec,Pc as nc,Rb as js,Sa as $l,Sb as xt,Tb as Yl,Ub as Jl,Uc as ic,Xa as He,Ya as Ri,_ as Bi,ba as _a,bc as Xl,cc as Pa,da as Ca,g as xo,hc as Ts,ja as ni,ka as Hl,mb as zn,ob as Ae,pb as nr,qa as Gl,qb as <PERSON>,rb as <PERSON>s,uc as Kl,xb as st,yb as rt,zb as Kt}from"./chunk-YIUCZFK7.js";import{a as Rr,b as ql,d as zl,f as Xu,g as Ul}from"./chunk-P2VZOJAX.js";function ja(r,n){n===void 0&&(n={});var t=Ih();t.p(r);var i=jh(r,n,2,4);return Eh(i,n),Th(i,i.length-4,t.d()),i}function jc(r,n){return Nh((Dh(r),r.subarray(2,-4)),n)}var an,sn,Ao,Fa,Ia,zs,Nc,Oc,Mc,Us,Fc,Lh,kc,Ws,wi,fe,ii,qi,fe,fe,fe,fe,So,fe,Ph,kh,Ah,Sh,Bs,Wn,Rs,Gs,Ic,Nh,xi,Po,qs,Hs,Ac,ko,Ma,Sc,Oh,Mh,Fh,Ih,jh,Th,Eh,Dh,Tc=zl(()=>{"use strict";an=Uint8Array,sn=Uint16Array,Ao=Uint32Array,Fa=new an([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),Ia=new an([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),zs=new an([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Nc=function(r,n){for(var t=new sn(31),i=0;i<31;++i)t[i]=n+=1<<r[i-1];for(var s=new Ao(t[30]),i=1;i<30;++i)for(var a=t[i];a<t[i+1];++a)s[a]=a-t[i]<<5|i;return[t,s]},Oc=Nc(Fa,2),Mc=Oc[0],Us=Oc[1];Mc[28]=258,Us[258]=28;Fc=Nc(Ia,0),Lh=Fc[0],kc=Fc[1],Ws=new sn(32768);for(fe=0;fe<32768;++fe)wi=(fe&43690)>>>1|(fe&21845)<<1,wi=(wi&52428)>>>2|(wi&13107)<<2,wi=(wi&61680)>>>4|(wi&3855)<<4,Ws[fe]=((wi&65280)>>>8|(wi&255)<<8)>>>1;ii=function(r,n,t){for(var i=r.length,s=0,a=new sn(n);s<i;++s)++a[r[s]-1];var c=new sn(n);for(s=0;s<n;++s)c[s]=c[s-1]+a[s-1]<<1;var h;if(t){h=new sn(1<<n);var f=15-n;for(s=0;s<i;++s)if(r[s])for(var g=s<<4|r[s],b=n-r[s],C=c[r[s]-1]++<<b,P=C|(1<<b)-1;C<=P;++C)h[Ws[C]>>>f]=g}else for(h=new sn(i),s=0;s<i;++s)h[s]=Ws[c[r[s]-1]++]>>>15-r[s];return h},qi=new an(288);for(fe=0;fe<144;++fe)qi[fe]=8;for(fe=144;fe<256;++fe)qi[fe]=9;for(fe=256;fe<280;++fe)qi[fe]=7;for(fe=280;fe<288;++fe)qi[fe]=8;So=new an(32);for(fe=0;fe<32;++fe)So[fe]=5;Ph=ii(qi,9,0),kh=ii(qi,9,1),Ah=ii(So,5,0),Sh=ii(So,5,1),Bs=function(r){for(var n=r[0],t=1;t<r.length;++t)r[t]>n&&(n=r[t]);return n},Wn=function(r,n,t){var i=n/8>>0;return(r[i]|r[i+1]<<8)>>>(n&7)&t},Rs=function(r,n){var t=n/8>>0;return(r[t]|r[t+1]<<8|r[t+2]<<16)>>>(n&7)},Gs=function(r){return(r/8>>0)+(r&7&&1)},Ic=function(r,n,t){(n==null||n<0)&&(n=0),(t==null||t>r.length)&&(t=r.length);var i=new(r instanceof sn?sn:r instanceof Ao?Ao:an)(t-n);return i.set(r.subarray(n,t)),i},Nh=function(r,n,t){var i=r.length,s=!n||t,a=!t||t.i;t||(t={}),n||(n=new an(i*3));var c=function($){var Q=n.length;if($>Q){var et=new an(Math.max(Q*2,$));et.set(n),n=et}},h=t.f||0,f=t.p||0,g=t.b||0,b=t.l,C=t.d,P=t.m,p=t.n,I=i*8;do{if(!b){t.f=h=Wn(r,f,1);var O=Wn(r,f+1,3);if(f+=3,O)if(O==1)b=kh,C=Sh,P=9,p=5;else if(O==2){var J=Wn(r,f,31)+257,lt=Wn(r,f+10,15)+4,ht=J+Wn(r,f+5,31)+1;f+=14;for(var _t=new an(ht),tt=new an(19),R=0;R<lt;++R)tt[zs[R]]=Wn(r,f+R*3,7);f+=lt*3;var vt=Bs(tt),gt=(1<<vt)-1;if(!a&&f+ht*(vt+7)>I)break;for(var N=ii(tt,vt,1),R=0;R<ht;){var A=N[Wn(r,f,gt)];f+=A&15;var D=A>>>4;if(D<16)_t[R++]=D;else{var z=0,B=0;for(D==16?(B=3+Wn(r,f,3),f+=2,z=_t[R-1]):D==17?(B=3+Wn(r,f,7),f+=3):D==18&&(B=11+Wn(r,f,127),f+=7);B--;)_t[R++]=z}}var at=_t.subarray(0,J),nt=_t.subarray(J);P=Bs(at),p=Bs(nt),b=ii(at,P,1),C=ii(nt,p,1)}else throw"invalid block type";else{var D=Gs(f)+4,k=r[D-4]|r[D-3]<<8,j=D+k;if(j>i){if(a)throw"unexpected EOF";break}s&&c(g+k),n.set(r.subarray(D,j),g),t.b=g+=k,t.p=f=j*8;continue}if(f>I)throw"unexpected EOF"}s&&c(g+131072);for(var ft=(1<<P)-1,Z=(1<<p)-1,pt=P+p+18;a||f+pt<I;){var z=b[Rs(r,f)&ft],dt=z>>>4;if(f+=z&15,f>I)throw"unexpected EOF";if(!z)throw"invalid length/literal";if(dt<256)n[g++]=dt;else if(dt==256){b=null;break}else{var Mt=dt-254;if(dt>264){var R=dt-257,x=Fa[R];Mt=Wn(r,f,(1<<x)-1)+Mc[R],f+=x}var F=C[Rs(r,f)&Z],T=F>>>4;if(!F)throw"invalid distance";f+=F&15;var nt=Lh[T];if(T>3){var x=Ia[T];nt+=Rs(r,f)&(1<<x)-1,f+=x}if(f>I)throw"unexpected EOF";s&&c(g+131072);for(var H=g+Mt;g<H;g+=4)n[g]=n[g-nt],n[g+1]=n[g+1-nt],n[g+2]=n[g+2-nt],n[g+3]=n[g+3-nt];g=H}}t.l=b,t.p=f,t.b=g,b&&(h=1,t.m=P,t.d=C,t.n=p)}while(!h);return g==n.length?n:Ic(n,0,g)},xi=function(r,n,t){t<<=n&7;var i=n/8>>0;r[i]|=t,r[i+1]|=t>>>8},Po=function(r,n,t){t<<=n&7;var i=n/8>>0;r[i]|=t,r[i+1]|=t>>>8,r[i+2]|=t>>>16},qs=function(r,n){for(var t=[],i=0;i<r.length;++i)r[i]&&t.push({s:i,f:r[i]});var s=t.length,a=t.slice();if(!s)return[new an(0),0];if(s==1){var c=new an(t[0].s+1);return c[t[0].s]=1,[c,1]}t.sort(function(ht,_t){return ht.f-_t.f}),t.push({s:-1,f:25001});var h=t[0],f=t[1],g=0,b=1,C=2;for(t[0]={s:-1,f:h.f+f.f,l:h,r:f};b!=s-1;)h=t[t[g].f<t[C].f?g++:C++],f=t[g!=b&&t[g].f<t[C].f?g++:C++],t[b++]={s:-1,f:h.f+f.f,l:h,r:f};for(var P=a[0].s,i=1;i<s;++i)a[i].s>P&&(P=a[i].s);var p=new sn(P+1),I=Hs(t[b-1],p,0);if(I>n){var i=0,O=0,D=I-n,k=1<<D;for(a.sort(function(_t,tt){return p[tt.s]-p[_t.s]||_t.f-tt.f});i<s;++i){var j=a[i].s;if(p[j]>n)O+=k-(1<<I-p[j]),p[j]=n;else break}for(O>>>=D;O>0;){var J=a[i].s;p[J]<n?O-=1<<n-p[J]++-1:++i}for(;i>=0&&O;--i){var lt=a[i].s;p[lt]==n&&(--p[lt],++O)}I=n}return[new an(p),I]},Hs=function(r,n,t){return r.s==-1?Math.max(Hs(r.l,n,t+1),Hs(r.r,n,t+1)):n[r.s]=t},Ac=function(r){for(var n=r.length;n&&!r[--n];);for(var t=new sn(++n),i=0,s=r[0],a=1,c=function(f){t[i++]=f},h=1;h<=n;++h)if(r[h]==s&&h!=n)++a;else{if(!s&&a>2){for(;a>138;a-=138)c(32754);a>2&&(c(a>10?a-11<<5|28690:a-3<<5|12305),a=0)}else if(a>3){for(c(s),--a;a>6;a-=6)c(8304);a>2&&(c(a-3<<5|8208),a=0)}for(;a--;)c(s);a=1,s=r[h]}return[t.subarray(0,i),n]},ko=function(r,n){for(var t=0,i=0;i<n.length;++i)t+=r[i]*n[i];return t},Ma=function(r,n,t){var i=t.length,s=Gs(n+2);r[s]=i&255,r[s+1]=i>>>8,r[s+2]=r[s]^255,r[s+3]=r[s+1]^255;for(var a=0;a<i;++a)r[s+a+4]=t[a];return(s+4+i)*8},Sc=function(r,n,t,i,s,a,c,h,f,g,b){xi(n,b++,t),++s[256];for(var C=qs(s,15),P=C[0],p=C[1],I=qs(a,15),O=I[0],D=I[1],k=Ac(P),j=k[0],J=k[1],lt=Ac(O),ht=lt[0],_t=lt[1],tt=new sn(19),R=0;R<j.length;++R)tt[j[R]&31]++;for(var R=0;R<ht.length;++R)tt[ht[R]&31]++;for(var vt=qs(tt,7),gt=vt[0],N=vt[1],A=19;A>4&&!gt[zs[A-1]];--A);var z=g+5<<3,B=ko(s,qi)+ko(a,So)+c,at=ko(s,P)+ko(a,O)+c+14+3*A+ko(tt,gt)+(2*tt[16]+3*tt[17]+7*tt[18]);if(z<=B&&z<=at)return Ma(n,b,r.subarray(f,f+g));var nt,ft,Z,pt;if(xi(n,b,1+(at<B)),b+=2,at<B){nt=ii(P,p,0),ft=P,Z=ii(O,D,0),pt=O;var dt=ii(gt,N,0);xi(n,b,J-257),xi(n,b+5,_t-1),xi(n,b+10,A-4),b+=14;for(var R=0;R<A;++R)xi(n,b+3*R,gt[zs[R]]);b+=3*A;for(var Mt=[j,ht],x=0;x<2;++x)for(var F=Mt[x],R=0;R<F.length;++R){var T=F[R]&31;xi(n,b,dt[T]),b+=gt[T],T>15&&(xi(n,b,F[R]>>>5&127),b+=F[R]>>>12)}}else nt=Ph,ft=qi,Z=Ah,pt=So;for(var R=0;R<h;++R)if(i[R]>255){var T=i[R]>>>18&31;Po(n,b,nt[T+257]),b+=ft[T+257],T>7&&(xi(n,b,i[R]>>>23&31),b+=Fa[T]);var H=i[R]&31;Po(n,b,Z[H]),b+=pt[H],H>3&&(Po(n,b,i[R]>>>5&8191),b+=Ia[H])}else Po(n,b,nt[i[R]]),b+=ft[i[R]];return Po(n,b,nt[256]),b+ft[256]},Oh=new Ao([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Mh=new an(0),Fh=function(r,n,t,i,s,a){var c=r.length,h=new an(i+c+5*(1+Math.floor(c/7e3))+s),f=h.subarray(i,h.length-s),g=0;if(!n||c<8)for(var b=0;b<=c;b+=65535){var C=b+65535;C<c?g=Ma(f,g,r.subarray(b,C)):(f[b]=a,g=Ma(f,g,r.subarray(b,c)))}else{for(var P=Oh[n-1],p=P>>>13,I=P&8191,O=(1<<t)-1,D=new sn(32768),k=new sn(O+1),j=Math.ceil(t/3),J=2*j,lt=function(Gt){return(r[Gt]^r[Gt+1]<<j^r[Gt+2]<<J)&O},ht=new Ao(25e3),_t=new sn(288),tt=new sn(32),R=0,vt=0,b=0,gt=0,N=0,A=0;b<c;++b){var z=lt(b),B=b&32767,at=k[z];if(D[B]=at,k[z]=B,N<=b){var nt=c-b;if((R>7e3||gt>24576)&&nt>423){g=Sc(r,f,0,ht,_t,tt,vt,gt,A,b-A,g),gt=R=vt=0,A=b;for(var ft=0;ft<286;++ft)_t[ft]=0;for(var ft=0;ft<30;++ft)tt[ft]=0}var Z=2,pt=0,dt=I,Mt=B-at&32767;if(nt>2&&z==lt(b-Mt))for(var x=Math.min(p,nt)-1,F=Math.min(32767,b),T=Math.min(258,nt);Mt<=F&&--dt&&B!=at;){if(r[b+Z]==r[b+Z-Mt]){for(var H=0;H<T&&r[b+H]==r[b+H-Mt];++H);if(H>Z){if(Z=H,pt=Mt,H>x)break;for(var $=Math.min(Mt,H-2),Q=0,ft=0;ft<$;++ft){var et=b-Mt+ft+32768&32767,it=D[et],kt=et-it+32768&32767;kt>Q&&(Q=kt,at=et)}}}B=at,at=D[B],Mt+=B-at+32768&32767}if(pt){ht[gt++]=268435456|Us[Z]<<18|kc[pt];var Pt=Us[Z]&31,jt=kc[pt]&31;vt+=Fa[Pt]+Ia[jt],++_t[257+Pt],++tt[jt],N=b+Z,++R}else ht[gt++]=r[b],++_t[r[b]]}}g=Sc(r,f,a,ht,_t,tt,vt,gt,A,b-A,g),a||(g=Ma(f,g,Mh))}return Ic(h,0,i+Gs(g)+s)},Ih=function(){var r=1,n=0;return{p:function(t){for(var i=r,s=n,a=t.length,c=0;c!=a;){for(var h=Math.min(c+5552,a);c<h;++c)i+=t[c],s+=i;i%=65521,s%=65521}r=i,n=s},d:function(){return(r>>>8<<16|(n&255)<<8|n>>>8)+((r&255)<<23)*2}}},jh=function(r,n,t,i,s){return Fh(r,n.level==null?6:n.level,n.mem==null?Math.ceil(Math.max(8,Math.min(13,Math.log(r.length)))*1.5):12+n.mem,t,i,!s)},Th=function(r,n,t){for(;t;++n)r[n]=t,t>>>=8},Eh=function(r,n){var t=n.level,i=t==0?0:t<6?1:t==9?3:2;r[0]=120,r[1]=i<<6|(i?32-2*i:1)},Dh=function(r){if((r[0]&15)!=8||r[0]>>>4>7||(r[0]<<8|r[1])%31)throw"invalid zlib data";if(r[1]&32)throw"invalid zlib data: preset dictionaries not supported"}});var x1={};Xu(x1,{AcroForm:()=>Zh,AcroFormAppearance:()=>Bt,AcroFormButton:()=>Ge,AcroFormCheckBox:()=>Fo,AcroFormChoiceField:()=>ar,AcroFormComboBox:()=>lr,AcroFormEditBox:()=>Oo,AcroFormListBox:()=>sr,AcroFormPasswordField:()=>Io,AcroFormPushButton:()=>Mo,AcroFormRadioButton:()=>cr,AcroFormTextField:()=>Hi,GState:()=>jo,ShadingPattern:()=>Ui,TilingPattern:()=>or,default:()=>ol,jsPDF:()=>Wt});function Vs(){Vt.console&&typeof Vt.console.log=="function"&&Vt.console.log.apply(Vt.console,arguments)}function $s(r,n,t){var i=new XMLHttpRequest;i.open("GET",r),i.responseType="blob",i.onload=function(){rr(i.response,n,t)},i.onerror=function(){_e.error("could not download file")},i.send()}function Ec(r){var n=new XMLHttpRequest;n.open("HEAD",r,!1);try{n.send()}catch{}return n.status>=200&&n.status<=299}function Ta(r){try{r.dispatchEvent(new MouseEvent("click"))}catch{var n=document.createEvent("MouseEvents");n.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),r.dispatchEvent(n)}}function Qc(r){var n;r=r||"",this.ok=!1,r.charAt(0)=="#"&&(r=r.substr(1,6)),r={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[r=(r=r.replace(/ /g,"")).toLowerCase()]||r;for(var t=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(h){return[parseInt(h[1]),parseInt(h[2]),parseInt(h[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(h){return[parseInt(h[1],16),parseInt(h[2],16),parseInt(h[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(h){return[parseInt(h[1]+h[1],16),parseInt(h[2]+h[2],16),parseInt(h[3]+h[3],16)]}}],i=0;i<t.length;i++){var s=t[i].re,a=t[i].process,c=s.exec(r);c&&(n=a(c),this.r=n[0],this.g=n[1],this.b=n[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var h=this.r.toString(16),f=this.g.toString(16),g=this.b.toString(16);return h.length==1&&(h="0"+h),f.length==1&&(f="0"+f),g.length==1&&(g="0"+g),"#"+h+f+g}}function Ys(r,n){var t=r[0],i=r[1],s=r[2],a=r[3];t=tn(t,i,s,a,n[0],7,-680876936),a=tn(a,t,i,s,n[1],12,-389564586),s=tn(s,a,t,i,n[2],17,606105819),i=tn(i,s,a,t,n[3],22,-1044525330),t=tn(t,i,s,a,n[4],7,-176418897),a=tn(a,t,i,s,n[5],12,1200080426),s=tn(s,a,t,i,n[6],17,-1473231341),i=tn(i,s,a,t,n[7],22,-45705983),t=tn(t,i,s,a,n[8],7,1770035416),a=tn(a,t,i,s,n[9],12,-1958414417),s=tn(s,a,t,i,n[10],17,-42063),i=tn(i,s,a,t,n[11],22,-1990404162),t=tn(t,i,s,a,n[12],7,1804603682),a=tn(a,t,i,s,n[13],12,-40341101),s=tn(s,a,t,i,n[14],17,-1502002290),t=en(t,i=tn(i,s,a,t,n[15],22,1236535329),s,a,n[1],5,-165796510),a=en(a,t,i,s,n[6],9,-1069501632),s=en(s,a,t,i,n[11],14,643717713),i=en(i,s,a,t,n[0],20,-373897302),t=en(t,i,s,a,n[5],5,-701558691),a=en(a,t,i,s,n[10],9,38016083),s=en(s,a,t,i,n[15],14,-660478335),i=en(i,s,a,t,n[4],20,-405537848),t=en(t,i,s,a,n[9],5,568446438),a=en(a,t,i,s,n[14],9,-1019803690),s=en(s,a,t,i,n[3],14,-187363961),i=en(i,s,a,t,n[8],20,1163531501),t=en(t,i,s,a,n[13],5,-1444681467),a=en(a,t,i,s,n[2],9,-51403784),s=en(s,a,t,i,n[7],14,1735328473),t=nn(t,i=en(i,s,a,t,n[12],20,-1926607734),s,a,n[5],4,-378558),a=nn(a,t,i,s,n[8],11,-2022574463),s=nn(s,a,t,i,n[11],16,1839030562),i=nn(i,s,a,t,n[14],23,-35309556),t=nn(t,i,s,a,n[1],4,-1530992060),a=nn(a,t,i,s,n[4],11,1272893353),s=nn(s,a,t,i,n[7],16,-155497632),i=nn(i,s,a,t,n[10],23,-1094730640),t=nn(t,i,s,a,n[13],4,681279174),a=nn(a,t,i,s,n[0],11,-358537222),s=nn(s,a,t,i,n[3],16,-722521979),i=nn(i,s,a,t,n[6],23,76029189),t=nn(t,i,s,a,n[9],4,-640364487),a=nn(a,t,i,s,n[12],11,-421815835),s=nn(s,a,t,i,n[15],16,530742520),t=rn(t,i=nn(i,s,a,t,n[2],23,-995338651),s,a,n[0],6,-198630844),a=rn(a,t,i,s,n[7],10,1126891415),s=rn(s,a,t,i,n[14],15,-1416354905),i=rn(i,s,a,t,n[5],21,-57434055),t=rn(t,i,s,a,n[12],6,1700485571),a=rn(a,t,i,s,n[3],10,-1894986606),s=rn(s,a,t,i,n[10],15,-1051523),i=rn(i,s,a,t,n[1],21,-2054922799),t=rn(t,i,s,a,n[8],6,1873313359),a=rn(a,t,i,s,n[15],10,-30611744),s=rn(s,a,t,i,n[6],15,-1560198380),i=rn(i,s,a,t,n[13],21,1309151649),t=rn(t,i,s,a,n[4],6,-145523070),a=rn(a,t,i,s,n[11],10,-1120210379),s=rn(s,a,t,i,n[2],15,718787259),i=rn(i,s,a,t,n[9],21,-343485551),r[0]=Wi(t,r[0]),r[1]=Wi(i,r[1]),r[2]=Wi(s,r[2]),r[3]=Wi(a,r[3])}function qa(r,n,t,i,s,a){return n=Wi(Wi(n,r),Wi(i,a)),Wi(n<<s|n>>>32-s,t)}function tn(r,n,t,i,s,a,c){return qa(n&t|~n&i,r,n,s,a,c)}function en(r,n,t,i,s,a,c){return qa(n&i|t&~i,r,n,s,a,c)}function nn(r,n,t,i,s,a,c){return qa(n^t^i,r,n,s,a,c)}function rn(r,n,t,i,s,a,c){return qa(t^(n|~i),r,n,s,a,c)}function tu(r){var n,t=r.length,i=[1732584193,-271733879,-1732584194,271733878];for(n=64;n<=r.length;n+=64)Ys(i,Bh(r.substring(n-64,n)));r=r.substring(n-64);var s=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(n=0;n<r.length;n++)s[n>>2]|=r.charCodeAt(n)<<(n%4<<3);if(s[n>>2]|=128<<(n%4<<3),n>55)for(Ys(i,s),n=0;n<16;n++)s[n]=0;return s[14]=8*t,Ys(i,s),i}function Bh(r){var n,t=[];for(n=0;n<64;n+=4)t[n>>2]=r.charCodeAt(n)+(r.charCodeAt(n+1)<<8)+(r.charCodeAt(n+2)<<16)+(r.charCodeAt(n+3)<<24);return t}function Rh(r){for(var n="",t=0;t<4;t++)n+=Dc[r>>8*t+4&15]+Dc[r>>8*t&15];return n}function qh(r){return String.fromCharCode((255&r)>>0,(65280&r)>>8,(16711680&r)>>16,(4278190080&r)>>24)}function Qs(r){return tu(r).map(qh).join("")}function Wi(r,n){if(zh){var t=(65535&r)+(65535&n);return(r>>16)+(n>>16)+(t>>16)<<16|65535&t}return r+n&4294967295}function tl(r,n){var t,i,s,a;if(r!==t){for(var c=(s=r,a=1+(256/r.length>>0),new Array(a+1).join(s)),h=[],f=0;f<256;f++)h[f]=f;var g=0;for(f=0;f<256;f++){var b=h[f];g=(g+b+c.charCodeAt(f))%256,h[f]=h[g],h[g]=b}t=r,i=h}else h=i;var C=n.length,P=0,p=0,I="";for(f=0;f<C;f++)p=(p+(b=h[P=(P+1)%256]))%256,h[P]=h[p],h[p]=b,c=h[(h[P]+h[p])%256],I+=String.fromCharCode(n.charCodeAt(f)^c);return I}function Wr(r,n,t,i){this.v=1,this.r=2;var s=192;r.forEach(function(h){if(Bc.perm!==void 0)throw new Error("Invalid permission: "+h);s+=Bc[h]}),this.padding="(\xBFN^Nu\x8AAd\0NV\xFF\xFA\b..\0\xB6\xD0h>\x80/\f\xA9\xFEdSiz";var a=(n+this.padding).substr(0,32),c=(t+this.padding).substr(0,32);this.O=this.processOwnerPassword(a,c),this.P=-(1+(255^s)),this.encryptionKey=Qs(a+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(i)).substr(0,5),this.U=tl(this.encryptionKey,this.padding)}function Hr(r){if(/[^\u0000-\u00ff]/.test(r))throw new Error("Invalid PDF Name Object: "+r+", Only accept ASCII characters.");for(var n="",t=r.length,i=0;i<t;i++){var s=r.charCodeAt(i);s<33||s===35||s===37||s===40||s===41||s===47||s===60||s===62||s===91||s===93||s===123||s===125||s>126?n+="#"+("0"+s.toString(16)).slice(-2):n+=r[i]}return n}function Rc(r){if(ye(r)!=="object")throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var n={};this.subscribe=function(t,i,s){if(s=s||!1,typeof t!="string"||typeof i!="function"||typeof s!="boolean")throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");n.hasOwnProperty(t)||(n[t]={});var a=Math.random().toString(35);return n[t][a]=[i,!!s],a},this.unsubscribe=function(t){for(var i in n)if(n[i][t])return delete n[i][t],Object.keys(n[i]).length===0&&delete n[i],!0;return!1},this.publish=function(t){if(n.hasOwnProperty(t)){var i=Array.prototype.slice.call(arguments,1),s=[];for(var a in n[t]){var c=n[t][a];try{c[0].apply(r,i)}catch(h){Vt.console&&_e.error("jsPDF PubSub Error",h.message,h)}c[1]&&s.push(a)}s.length&&s.forEach(this.unsubscribe)}},this.getTopics=function(){return n}}function jo(r){if(!(this instanceof jo))return new jo(r);var n="opacity,stroke-opacity".split(",");for(var t in r)r.hasOwnProperty(t)&&n.indexOf(t)>=0&&(this[t]=r[t]);this.id="",this.objectNumber=-1}function eu(r,n){this.gState=r,this.matrix=n,this.id="",this.objectNumber=-1}function Ui(r,n,t,i,s){if(!(this instanceof Ui))return new Ui(r,n,t,i,s);this.type=r==="axial"?2:3,this.coords=n,this.colors=t,eu.call(this,i,s)}function or(r,n,t,i,s){if(!(this instanceof or))return new or(r,n,t,i,s);this.boundingBox=r,this.xStep=n,this.yStep=t,this.stream="",this.cloneIndex=0,eu.call(this,i,s)}function Wt(r){var n,t=typeof arguments[0]=="string"?arguments[0]:"p",i=arguments[1],s=arguments[2],a=arguments[3],c=[],h=1,f=16,g="S",b=null;ye(r=r||{})==="object"&&(t=r.orientation,i=r.unit||i,s=r.format||s,a=r.compress||r.compressPdf||a,(b=r.encryption||null)!==null&&(b.userPassword=b.userPassword||"",b.ownerPassword=b.ownerPassword||"",b.userPermissions=b.userPermissions||[]),h=typeof r.userUnit=="number"?Math.abs(r.userUnit):1,r.precision!==void 0&&(n=r.precision),r.floatPrecision!==void 0&&(f=r.floatPrecision),g=r.defaultPathOperation||"S"),c=r.filters||(a===!0?["FlateEncode"]:c),i=i||"mm",t=(""+(t||"P")).toLowerCase();var C=r.putOnlyUsedFonts||!1,P={},p={internal:{},__private__:{}};p.__private__.PubSub=Rc;var I="1.3",O=p.__private__.getPdfVersion=function(){return I};p.__private__.setPdfVersion=function(l){I=l};var D={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};p.__private__.getPageFormats=function(){return D};var k=p.__private__.getPageFormat=function(l){return D[l]};s=s||"a4";var j={COMPAT:"compat",ADVANCED:"advanced"},J=j.COMPAT;function lt(){this.saveGraphicsState(),E(new Ht(It,0,0,-It,0,Pi()*It).toString()+" cm"),this.setFontSize(this.getFontSize()/It),g="n",J=j.ADVANCED}function ht(){this.restoreGraphicsState(),g="S",J=j.COMPAT}var _t=p.__private__.combineFontStyleAndFontWeight=function(l,v){if(l=="bold"&&v=="normal"||l=="bold"&&v==400||l=="normal"&&v=="italic"||l=="bold"&&v=="italic")throw new Error("Invalid Combination of fontweight and fontstyle");return v&&(l=v==400||v==="normal"?l==="italic"?"italic":"normal":v!=700&&v!=="bold"||l!=="normal"?(v==700?"bold":v)+""+l:"bold"),l};p.advancedAPI=function(l){var v=J===j.COMPAT;return v&&lt.call(this),typeof l!="function"||(l(this),v&&ht.call(this)),this},p.compatAPI=function(l){var v=J===j.ADVANCED;return v&&ht.call(this),typeof l!="function"||(l(this),v&&lt.call(this)),this},p.isAdvancedAPI=function(){return J===j.ADVANCED};var tt,R=function(l){if(J!==j.ADVANCED)throw new Error(l+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},vt=p.roundToPrecision=p.__private__.roundToPrecision=function(l,v){var M=n||v;if(isNaN(l)||isNaN(M))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return l.toFixed(M).replace(/0+$/,"")};tt=p.hpf=p.__private__.hpf=typeof f=="number"?function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(l,f)}:f==="smart"?function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(l,l>-1&&l<1?16:5)}:function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(l,16)};var gt=p.f2=p.__private__.f2=function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.f2");return vt(l,2)},N=p.__private__.f3=function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.f3");return vt(l,3)},A=p.scale=p.__private__.scale=function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.scale");return J===j.COMPAT?l*It:J===j.ADVANCED?l:void 0},z=function(l){return J===j.COMPAT?Pi()-l:J===j.ADVANCED?l:void 0},B=function(l){return A(z(l))};p.__private__.setPrecision=p.setPrecision=function(l){typeof parseInt(l,10)=="number"&&(n=parseInt(l,10))};var at,nt="00000000000000000000000000000000",ft=p.__private__.getFileId=function(){return nt},Z=p.__private__.setFileId=function(l){return nt=l!==void 0&&/^[a-fA-F0-9]{32}$/.test(l)?l.toUpperCase():nt.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),b!==null&&(Ze=new Wr(b.userPermissions,b.userPassword,b.ownerPassword,nt)),nt};p.setFileId=function(l){return Z(l),this},p.getFileId=function(){return ft()};var pt=p.__private__.convertDateToPDFDate=function(l){var v=l.getTimezoneOffset(),M=v<0?"+":"-",q=Math.floor(Math.abs(v/60)),Y=Math.abs(v%60),ct=[M,T(q),"'",T(Y),"'"].join("");return["D:",l.getFullYear(),T(l.getMonth()+1),T(l.getDate()),T(l.getHours()),T(l.getMinutes()),T(l.getSeconds()),ct].join("")},dt=p.__private__.convertPDFDateToDate=function(l){var v=parseInt(l.substr(2,4),10),M=parseInt(l.substr(6,2),10)-1,q=parseInt(l.substr(8,2),10),Y=parseInt(l.substr(10,2),10),ct=parseInt(l.substr(12,2),10),Ct=parseInt(l.substr(14,2),10);return new Date(v,M,q,Y,ct,Ct,0)},Mt=p.__private__.setCreationDate=function(l){var v;if(l===void 0&&(l=new Date),l instanceof Date)v=pt(l);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(l))throw new Error("Invalid argument passed to jsPDF.setCreationDate");v=l}return at=v},x=p.__private__.getCreationDate=function(l){var v=at;return l==="jsDate"&&(v=dt(at)),v};p.setCreationDate=function(l){return Mt(l),this},p.getCreationDate=function(l){return x(l)};var F,T=p.__private__.padd2=function(l){return("0"+parseInt(l)).slice(-2)},H=p.__private__.padd2Hex=function(l){return("00"+(l=l.toString())).substr(l.length)},$=0,Q=[],et=[],it=0,kt=[],Pt=[],jt=!1,Dt=et,Gt=function(){$=0,it=0,et=[],Q=[],kt=[],li=Be(),Nn=Be()};p.__private__.setCustomOutputDestination=function(l){jt=!0,Dt=l};var ut=function(l){jt||(Dt=l)};p.__private__.resetCustomOutputDestination=function(){jt=!1,Dt=et};var E=p.__private__.out=function(l){return l=l.toString(),it+=l.length+1,Dt.push(l),Dt},Qt=p.__private__.write=function(l){return E(arguments.length===1?l.toString():Array.prototype.join.call(arguments," "))},Rt=p.__private__.getArrayBuffer=function(l){for(var v=l.length,M=new ArrayBuffer(v),q=new Uint8Array(M);v--;)q[v]=l.charCodeAt(v);return M},Lt=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];p.__private__.getStandardFonts=function(){return Lt};var At=r.fontSize||16;p.__private__.setFontSize=p.setFontSize=function(l){return At=J===j.ADVANCED?l/It:l,this};var Ft,Ot=p.__private__.getFontSize=p.getFontSize=function(){return J===j.COMPAT?At:At*It},qt=r.R2L||!1;p.__private__.setR2L=p.setR2L=function(l){return qt=l,this},p.__private__.getR2L=p.getR2L=function(){return qt};var Jt,ne=p.__private__.setZoomMode=function(l){var v=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(l))Ft=l;else if(isNaN(l)){if(v.indexOf(l)===-1)throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+l+'" is not recognized.');Ft=l}else Ft=parseInt(l,10)};p.__private__.getZoomMode=function(){return Ft};var re,le=p.__private__.setPageMode=function(l){if([void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(l)==-1)throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+l+'" is not recognized.');Jt=l};p.__private__.getPageMode=function(){return Jt};var ve=p.__private__.setLayoutMode=function(l){if([void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(l)==-1)throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+l+'" is not recognized.');re=l};p.__private__.getLayoutMode=function(){return re},p.__private__.setDisplayMode=p.setDisplayMode=function(l,v,M){return ne(l),ve(v),le(M),this};var $t={title:"",subject:"",author:"",keywords:"",creator:""};p.__private__.getDocumentProperty=function(l){if(Object.keys($t).indexOf(l)===-1)throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return $t[l]},p.__private__.getDocumentProperties=function(){return $t},p.__private__.setDocumentProperties=p.setProperties=p.setDocumentProperties=function(l){for(var v in $t)$t.hasOwnProperty(v)&&l[v]&&($t[v]=l[v]);return this},p.__private__.setDocumentProperty=function(l,v){if(Object.keys($t).indexOf(l)===-1)throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return $t[l]=v};var oe,It,Ke,ue,kn,we={},Pe={},$n=[],de={},Vi={},Se={},An={},si=null,Ne=0,Xt=[],pe=new Rc(p),$i=r.hotfixes||[],Je={},Yn={},Jn=[],Ht=function l(v,M,q,Y,ct,Ct){if(!(this instanceof l))return new l(v,M,q,Y,ct,Ct);isNaN(v)&&(v=1),isNaN(M)&&(M=0),isNaN(q)&&(q=0),isNaN(Y)&&(Y=1),isNaN(ct)&&(ct=0),isNaN(Ct)&&(Ct=0),this._matrix=[v,M,q,Y,ct,Ct]};Object.defineProperty(Ht.prototype,"sx",{get:function(){return this._matrix[0]},set:function(l){this._matrix[0]=l}}),Object.defineProperty(Ht.prototype,"shy",{get:function(){return this._matrix[1]},set:function(l){this._matrix[1]=l}}),Object.defineProperty(Ht.prototype,"shx",{get:function(){return this._matrix[2]},set:function(l){this._matrix[2]=l}}),Object.defineProperty(Ht.prototype,"sy",{get:function(){return this._matrix[3]},set:function(l){this._matrix[3]=l}}),Object.defineProperty(Ht.prototype,"tx",{get:function(){return this._matrix[4]},set:function(l){this._matrix[4]=l}}),Object.defineProperty(Ht.prototype,"ty",{get:function(){return this._matrix[5]},set:function(l){this._matrix[5]=l}}),Object.defineProperty(Ht.prototype,"a",{get:function(){return this._matrix[0]},set:function(l){this._matrix[0]=l}}),Object.defineProperty(Ht.prototype,"b",{get:function(){return this._matrix[1]},set:function(l){this._matrix[1]=l}}),Object.defineProperty(Ht.prototype,"c",{get:function(){return this._matrix[2]},set:function(l){this._matrix[2]=l}}),Object.defineProperty(Ht.prototype,"d",{get:function(){return this._matrix[3]},set:function(l){this._matrix[3]=l}}),Object.defineProperty(Ht.prototype,"e",{get:function(){return this._matrix[4]},set:function(l){this._matrix[4]=l}}),Object.defineProperty(Ht.prototype,"f",{get:function(){return this._matrix[5]},set:function(l){this._matrix[5]=l}}),Object.defineProperty(Ht.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(Ht.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(Ht.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(Ht.prototype,"isIdentity",{get:function(){return this.sx===1&&this.shy===0&&this.shx===0&&this.sy===1&&this.tx===0&&this.ty===0}}),Ht.prototype.join=function(l){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(tt).join(l)},Ht.prototype.multiply=function(l){var v=l.sx*this.sx+l.shy*this.shx,M=l.sx*this.shy+l.shy*this.sy,q=l.shx*this.sx+l.sy*this.shx,Y=l.shx*this.shy+l.sy*this.sy,ct=l.tx*this.sx+l.ty*this.shx+this.tx,Ct=l.tx*this.shy+l.ty*this.sy+this.ty;return new Ht(v,M,q,Y,ct,Ct)},Ht.prototype.decompose=function(){var l=this.sx,v=this.shy,M=this.shx,q=this.sy,Y=this.tx,ct=this.ty,Ct=Math.sqrt(l*l+v*v),Tt=(l/=Ct)*M+(v/=Ct)*q;M-=l*Tt,q-=v*Tt;var zt=Math.sqrt(M*M+q*q);return Tt/=zt,l*(q/=zt)<v*(M/=zt)&&(l=-l,v=-v,Tt=-Tt,Ct=-Ct),{scale:new Ht(Ct,0,0,zt,0,0),translate:new Ht(1,0,0,1,Y,ct),rotate:new Ht(l,v,-v,l,0,0),skew:new Ht(1,0,Tt,1,0,0)}},Ht.prototype.toString=function(l){return this.join(" ")},Ht.prototype.inversed=function(){var l=this.sx,v=this.shy,M=this.shx,q=this.sy,Y=this.tx,ct=this.ty,Ct=1/(l*q-v*M),Tt=q*Ct,zt=-v*Ct,te=-M*Ct,ie=l*Ct;return new Ht(Tt,zt,te,ie,-Tt*Y-te*ct,-zt*Y-ie*ct)},Ht.prototype.applyToPoint=function(l){var v=l.x*this.sx+l.y*this.shx+this.tx,M=l.x*this.shy+l.y*this.sy+this.ty;return new Lr(v,M)},Ht.prototype.applyToRectangle=function(l){var v=this.applyToPoint(l),M=this.applyToPoint(new Lr(l.x+l.w,l.y+l.h));return new eo(v.x,v.y,M.x-v.x,M.y-v.y)},Ht.prototype.clone=function(){var l=this.sx,v=this.shy,M=this.shx,q=this.sy,Y=this.tx,ct=this.ty;return new Ht(l,v,M,q,Y,ct)},p.Matrix=Ht;var Sn=p.matrixMult=function(l,v){return v.multiply(l)},Xn=new Ht(1,0,0,1,0,0);p.unitMatrix=p.identityMatrix=Xn;var ln=function(l,v){if(!Vi[l]){var M=(v instanceof Ui?"Sh":"P")+(Object.keys(de).length+1).toString(10);v.id=M,Vi[l]=M,de[M]=v,pe.publish("addPattern",v)}};p.ShadingPattern=Ui,p.TilingPattern=or,p.addShadingPattern=function(l,v){return R("addShadingPattern()"),ln(l,v),this},p.beginTilingPattern=function(l){R("beginTilingPattern()"),Qo(l.boundingBox[0],l.boundingBox[1],l.boundingBox[2]-l.boundingBox[0],l.boundingBox[3]-l.boundingBox[1],l.matrix)},p.endTilingPattern=function(l,v){R("endTilingPattern()"),v.stream=Pt[F].join(`
`),ln(l,v),pe.publish("endTilingPattern",v),Jn.pop().restore()};var Ue=p.__private__.newObject=function(){var l=Be();return gn(l,!0),l},Be=p.__private__.newObjectDeferred=function(){return $++,Q[$]=function(){return it},$},gn=function(l,v){return v=typeof v=="boolean"&&v,Q[l]=it,v&&E(l+" 0 obj"),l},hr=p.__private__.newAdditionalObject=function(){var l={objId:Be(),content:""};return kt.push(l),l},li=Be(),Nn=Be(),On=p.__private__.decodeColorString=function(l){var v=l.split(" ");if(v.length!==2||v[1]!=="g"&&v[1]!=="G")v.length===5&&(v[4]==="k"||v[4]==="K")&&(v=[(1-v[0])*(1-v[3]),(1-v[1])*(1-v[3]),(1-v[2])*(1-v[3]),"r"]);else{var M=parseFloat(v[0]);v=[M,M,M,"r"]}for(var q="#",Y=0;Y<3;Y++)q+=("0"+Math.floor(255*parseFloat(v[Y])).toString(16)).slice(-2);return q},Mn=p.__private__.encodeColorString=function(l){var v;typeof l=="string"&&(l={ch1:l});var M=l.ch1,q=l.ch2,Y=l.ch3,ct=l.ch4,Ct=l.pdfColorType==="draw"?["G","RG","K"]:["g","rg","k"];if(typeof M=="string"&&M.charAt(0)!=="#"){var Tt=new Qc(M);if(Tt.ok)M=Tt.toHex();else if(!/^\d*\.?\d*$/.test(M))throw new Error('Invalid color "'+M+'" passed to jsPDF.encodeColorString.')}if(typeof M=="string"&&/^#[0-9A-Fa-f]{3}$/.test(M)&&(M="#"+M[1]+M[1]+M[2]+M[2]+M[3]+M[3]),typeof M=="string"&&/^#[0-9A-Fa-f]{6}$/.test(M)){var zt=parseInt(M.substr(1),16);M=zt>>16&255,q=zt>>8&255,Y=255&zt}if(q===void 0||ct===void 0&&M===q&&q===Y)if(typeof M=="string")v=M+" "+Ct[0];else switch(l.precision){case 2:v=gt(M/255)+" "+Ct[0];break;case 3:default:v=N(M/255)+" "+Ct[0]}else if(ct===void 0||ye(ct)==="object"){if(ct&&!isNaN(ct.a)&&ct.a===0)return v=["1.","1.","1.",Ct[1]].join(" ");if(typeof M=="string")v=[M,q,Y,Ct[1]].join(" ");else switch(l.precision){case 2:v=[gt(M/255),gt(q/255),gt(Y/255),Ct[1]].join(" ");break;default:case 3:v=[N(M/255),N(q/255),N(Y/255),Ct[1]].join(" ")}}else if(typeof M=="string")v=[M,q,Y,ct,Ct[2]].join(" ");else switch(l.precision){case 2:v=[gt(M),gt(q),gt(Y),gt(ct),Ct[2]].join(" ");break;case 3:default:v=[N(M),N(q),N(Y),N(ct),Ct[2]].join(" ")}return v},Kn=p.__private__.getFilters=function(){return c},wn=p.__private__.putStream=function(l){var v=(l=l||{}).data||"",M=l.filters||Kn(),q=l.alreadyAppliedFilters||[],Y=l.addLength1||!1,ct=v.length,Ct=l.objectId,Tt=function(Qe){return Qe};if(b!==null&&Ct===void 0)throw new Error("ObjectId must be passed to putStream for file encryption");b!==null&&(Tt=Ze.encryptor(Ct,0));var zt={};M===!0&&(M=["FlateEncode"]);var te=l.additionalKeyValues||[],ie=(zt=Wt.API.processDataByFilters!==void 0?Wt.API.processDataByFilters(v,M):{data:v,reverseChain:[]}).reverseChain+(Array.isArray(q)?q.join(" "):q.toString());if(zt.data.length!==0&&(te.push({key:"Length",value:zt.data.length}),Y===!0&&te.push({key:"Length1",value:ct})),ie.length!=0)if(ie.split("/").length-1==1)te.push({key:"Filter",value:ie});else{te.push({key:"Filter",value:"["+ie+"]"});for(var se=0;se<te.length;se+=1)if(te[se].key==="DecodeParms"){for(var ke=[],Oe=0;Oe<zt.reverseChain.split("/").length-1;Oe+=1)ke.push("null");ke.push(te[se].value),te[se].value="["+ke.join(" ")+"]"}}E("<<");for(var Re=0;Re<te.length;Re++)E("/"+te[Re].key+" "+te[Re].value);E(">>"),zt.data.length!==0&&(E("stream"),E(Tt(zt.data)),E("endstream"))},Zn=p.__private__.putPage=function(l){var v=l.number,M=l.data,q=l.objId,Y=l.contentsObjId;gn(q,!0),E("<</Type /Page"),E("/Parent "+l.rootDictionaryObjId+" 0 R"),E("/Resources "+l.resourceDictionaryObjId+" 0 R"),E("/MediaBox ["+parseFloat(tt(l.mediaBox.bottomLeftX))+" "+parseFloat(tt(l.mediaBox.bottomLeftY))+" "+tt(l.mediaBox.topRightX)+" "+tt(l.mediaBox.topRightY)+"]"),l.cropBox!==null&&E("/CropBox ["+tt(l.cropBox.bottomLeftX)+" "+tt(l.cropBox.bottomLeftY)+" "+tt(l.cropBox.topRightX)+" "+tt(l.cropBox.topRightY)+"]"),l.bleedBox!==null&&E("/BleedBox ["+tt(l.bleedBox.bottomLeftX)+" "+tt(l.bleedBox.bottomLeftY)+" "+tt(l.bleedBox.topRightX)+" "+tt(l.bleedBox.topRightY)+"]"),l.trimBox!==null&&E("/TrimBox ["+tt(l.trimBox.bottomLeftX)+" "+tt(l.trimBox.bottomLeftY)+" "+tt(l.trimBox.topRightX)+" "+tt(l.trimBox.topRightY)+"]"),l.artBox!==null&&E("/ArtBox ["+tt(l.artBox.bottomLeftX)+" "+tt(l.artBox.bottomLeftY)+" "+tt(l.artBox.topRightX)+" "+tt(l.artBox.topRightY)+"]"),typeof l.userUnit=="number"&&l.userUnit!==1&&E("/UserUnit "+l.userUnit),pe.publish("putPage",{objId:q,pageContext:Xt[v],pageNumber:v,page:M}),E("/Contents "+Y+" 0 R"),E(">>"),E("endobj");var ct=M.join(`
`);return J===j.ADVANCED&&(ct+=`
Q`),gn(Y,!0),wn({data:ct,filters:Kn(),objectId:Y}),E("endobj"),q},Yi=p.__private__.putPages=function(){var l,v,M=[];for(l=1;l<=Ne;l++)Xt[l].objId=Be(),Xt[l].contentsObjId=Be();for(l=1;l<=Ne;l++)M.push(Zn({number:l,data:Pt[l],objId:Xt[l].objId,contentsObjId:Xt[l].contentsObjId,mediaBox:Xt[l].mediaBox,cropBox:Xt[l].cropBox,bleedBox:Xt[l].bleedBox,trimBox:Xt[l].trimBox,artBox:Xt[l].artBox,userUnit:Xt[l].userUnit,rootDictionaryObjId:li,resourceDictionaryObjId:Nn}));gn(li,!0),E("<</Type /Pages");var q="/Kids [";for(v=0;v<Ne;v++)q+=M[v]+" 0 R ";E(q+"]"),E("/Count "+Ne),E(">>"),E("endobj"),pe.publish("postPutPages")},fr=function(l){pe.publish("putFont",{font:l,out:E,newObject:Ue,putStream:wn}),l.isAlreadyPutted!==!0&&(l.objectNumber=Ue(),E("<<"),E("/Type /Font"),E("/BaseFont /"+Hr(l.postScriptName)),E("/Subtype /Type1"),typeof l.encoding=="string"&&E("/Encoding /"+l.encoding),E("/FirstChar 32"),E("/LastChar 255"),E(">>"),E("endobj"))},dr=function(){for(var l in we)we.hasOwnProperty(l)&&(C===!1||C===!0&&P.hasOwnProperty(l))&&fr(we[l])},pr=function(l){l.objectNumber=Ue();var v=[];v.push({key:"Type",value:"/XObject"}),v.push({key:"Subtype",value:"/Form"}),v.push({key:"BBox",value:"["+[tt(l.x),tt(l.y),tt(l.x+l.width),tt(l.y+l.height)].join(" ")+"]"}),v.push({key:"Matrix",value:"["+l.matrix.toString()+"]"});var M=l.pages[1].join(`
`);wn({data:M,additionalKeyValues:v,objectId:l.objectNumber}),E("endobj")},gr=function(){for(var l in Je)Je.hasOwnProperty(l)&&pr(Je[l])},To=function(l,v){var M,q=[],Y=1/(v-1);for(M=0;M<1;M+=Y)q.push(M);if(q.push(1),l[0].offset!=0){var ct={offset:0,color:l[0].color};l.unshift(ct)}if(l[l.length-1].offset!=1){var Ct={offset:1,color:l[l.length-1].color};l.push(Ct)}for(var Tt="",zt=0,te=0;te<q.length;te++){for(M=q[te];M>l[zt+1].offset;)zt++;var ie=l[zt].offset,se=(M-ie)/(l[zt+1].offset-ie),ke=l[zt].color,Oe=l[zt+1].color;Tt+=H(Math.round((1-se)*ke[0]+se*Oe[0]).toString(16))+H(Math.round((1-se)*ke[1]+se*Oe[1]).toString(16))+H(Math.round((1-se)*ke[2]+se*Oe[2]).toString(16))}return Tt.trim()},Ua=function(l,v){v||(v=21);var M=Ue(),q=To(l.colors,v),Y=[];Y.push({key:"FunctionType",value:"0"}),Y.push({key:"Domain",value:"[0.0 1.0]"}),Y.push({key:"Size",value:"["+v+"]"}),Y.push({key:"BitsPerSample",value:"8"}),Y.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),Y.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),wn({data:q,additionalKeyValues:Y,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:M}),E("endobj"),l.objectNumber=Ue(),E("<< /ShadingType "+l.type),E("/ColorSpace /DeviceRGB");var ct="/Coords ["+tt(parseFloat(l.coords[0]))+" "+tt(parseFloat(l.coords[1]))+" ";l.type===2?ct+=tt(parseFloat(l.coords[2]))+" "+tt(parseFloat(l.coords[3])):ct+=tt(parseFloat(l.coords[2]))+" "+tt(parseFloat(l.coords[3]))+" "+tt(parseFloat(l.coords[4]))+" "+tt(parseFloat(l.coords[5])),E(ct+="]"),l.matrix&&E("/Matrix ["+l.matrix.toString()+"]"),E("/Function "+M+" 0 R"),E("/Extend [true true]"),E(">>"),E("endobj")},Wa=function(l,v){var M=Be(),q=Ue();v.push({resourcesOid:M,objectOid:q}),l.objectNumber=q;var Y=[];Y.push({key:"Type",value:"/Pattern"}),Y.push({key:"PatternType",value:"1"}),Y.push({key:"PaintType",value:"1"}),Y.push({key:"TilingType",value:"1"}),Y.push({key:"BBox",value:"["+l.boundingBox.map(tt).join(" ")+"]"}),Y.push({key:"XStep",value:tt(l.xStep)}),Y.push({key:"YStep",value:tt(l.yStep)}),Y.push({key:"Resources",value:M+" 0 R"}),l.matrix&&Y.push({key:"Matrix",value:"["+l.matrix.toString()+"]"}),wn({data:l.stream,additionalKeyValues:Y,objectId:l.objectNumber}),E("endobj")},mr=function(l){var v;for(v in de)de.hasOwnProperty(v)&&(de[v]instanceof Ui?Ua(de[v]):de[v]instanceof or&&Wa(de[v],l))},Eo=function(l){for(var v in l.objectNumber=Ue(),E("<<"),l)switch(v){case"opacity":E("/ca "+gt(l[v]));break;case"stroke-opacity":E("/CA "+gt(l[v]))}E(">>"),E("endobj")},Ha=function(){var l;for(l in Se)Se.hasOwnProperty(l)&&Eo(Se[l])},Gr=function(){for(var l in E("/XObject <<"),Je)Je.hasOwnProperty(l)&&Je[l].objectNumber>=0&&E("/"+l+" "+Je[l].objectNumber+" 0 R");pe.publish("putXobjectDict"),E(">>")},Ga=function(){Ze.oid=Ue(),E("<<"),E("/Filter /Standard"),E("/V "+Ze.v),E("/R "+Ze.r),E("/U <"+Ze.toHexString(Ze.U)+">"),E("/O <"+Ze.toHexString(Ze.O)+">"),E("/P "+Ze.P),E(">>"),E("endobj")},Do=function(){for(var l in E("/Font <<"),we)we.hasOwnProperty(l)&&(C===!1||C===!0&&P.hasOwnProperty(l))&&E("/"+l+" "+we[l].objectNumber+" 0 R");E(">>")},Va=function(){if(Object.keys(de).length>0){for(var l in E("/Shading <<"),de)de.hasOwnProperty(l)&&de[l]instanceof Ui&&de[l].objectNumber>=0&&E("/"+l+" "+de[l].objectNumber+" 0 R");pe.publish("putShadingPatternDict"),E(">>")}},vr=function(l){if(Object.keys(de).length>0){for(var v in E("/Pattern <<"),de)de.hasOwnProperty(v)&&de[v]instanceof p.TilingPattern&&de[v].objectNumber>=0&&de[v].objectNumber<l&&E("/"+v+" "+de[v].objectNumber+" 0 R");pe.publish("putTilingPatternDict"),E(">>")}},$a=function(){if(Object.keys(Se).length>0){var l;for(l in E("/ExtGState <<"),Se)Se.hasOwnProperty(l)&&Se[l].objectNumber>=0&&E("/"+l+" "+Se[l].objectNumber+" 0 R");pe.publish("putGStateDict"),E(">>")}},Ie=function(l){gn(l.resourcesOid,!0),E("<<"),E("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),Do(),Va(),vr(l.objectOid),$a(),Gr(),E(">>"),E("endobj")},Bo=function(){var l=[];dr(),Ha(),gr(),mr(l),pe.publish("putResources"),l.forEach(Ie),Ie({resourcesOid:Nn,objectOid:Number.MAX_SAFE_INTEGER}),pe.publish("postPutResources")},Ro=function(){pe.publish("putAdditionalObjects");for(var l=0;l<kt.length;l++){var v=kt[l];gn(v.objId,!0),E(v.content),E("endobj")}pe.publish("postPutAdditionalObjects")},qo=function(l){Pe[l.fontName]=Pe[l.fontName]||{},Pe[l.fontName][l.fontStyle]=l.id},Vr=function(l,v,M,q,Y){var ct={id:"F"+(Object.keys(we).length+1).toString(10),postScriptName:l,fontName:v,fontStyle:M,encoding:q,isStandardFont:Y||!1,metadata:{}};return pe.publish("addFont",{font:ct,instance:this}),we[ct.id]=ct,qo(ct),ct.id},Ya=function(l){for(var v=0,M=Lt.length;v<M;v++){var q=Vr.call(this,l[v][0],l[v][1],l[v][2],Lt[v][3],!0);C===!1&&(P[q]=!0);var Y=l[v][0].split("-");qo({id:q,fontName:Y[0],fontStyle:Y[1]||""})}pe.publish("addFonts",{fonts:we,dictionary:Pe})},Fn=function(l){return l.foo=function(){try{return l.apply(this,arguments)}catch(q){var v=q.stack||"";~v.indexOf(" at ")&&(v=v.split(" at ")[1]);var M="Error in function "+v.split(`
`)[0].split("<")[0]+": "+q.message;if(!Vt.console)throw new Error(M);Vt.console.error(M,q),Vt.alert&&alert(M)}},l.foo.bar=l,l.foo},br=function(l,v){var M,q,Y,ct,Ct,Tt,zt,te,ie;if(Y=(v=v||{}).sourceEncoding||"Unicode",Ct=v.outputEncoding,(v.autoencode||Ct)&&we[oe].metadata&&we[oe].metadata[Y]&&we[oe].metadata[Y].encoding&&(ct=we[oe].metadata[Y].encoding,!Ct&&we[oe].encoding&&(Ct=we[oe].encoding),!Ct&&ct.codePages&&(Ct=ct.codePages[0]),typeof Ct=="string"&&(Ct=ct[Ct]),Ct)){for(zt=!1,Tt=[],M=0,q=l.length;M<q;M++)(te=Ct[l.charCodeAt(M)])?Tt.push(String.fromCharCode(te)):Tt.push(l[M]),Tt[M].charCodeAt(0)>>8&&(zt=!0);l=Tt.join("")}for(M=l.length;zt===void 0&&M!==0;)l.charCodeAt(M-1)>>8&&(zt=!0),M--;if(!zt)return l;for(Tt=v.noBOM?[]:[254,255],M=0,q=l.length;M<q;M++){if((ie=(te=l.charCodeAt(M))>>8)>>8)throw new Error("Character at position "+M+" of string '"+l+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");Tt.push(ie),Tt.push(te-(ie<<8))}return String.fromCharCode.apply(void 0,Tt)},cn=p.__private__.pdfEscape=p.pdfEscape=function(l,v){return br(l,v).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},$r=p.__private__.beginPage=function(l){Pt[++Ne]=[],Xt[Ne]={objId:0,contentsObjId:0,userUnit:Number(h),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(l[0]),topRightY:Number(l[1])}},Uo(Ne),ut(Pt[F])},zo=function(l,v){var M,q,Y;switch(t=v||t,typeof l=="string"&&(M=k(l.toLowerCase()),Array.isArray(M)&&(q=M[0],Y=M[1])),Array.isArray(l)&&(q=l[0]*It,Y=l[1]*It),isNaN(q)&&(q=s[0],Y=s[1]),(q>14400||Y>14400)&&(_e.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),q=Math.min(14400,q),Y=Math.min(14400,Y)),s=[q,Y],t.substr(0,1)){case"l":Y>q&&(s=[Y,q]);break;case"p":q>Y&&(s=[Y,q])}$r(s),Yo(Kr),E(In),Qr!==0&&E(Qr+" J"),to!==0&&E(to+" j"),pe.publish("addPage",{pageNumber:Ne})},Ja=function(l){l>0&&l<=Ne&&(Pt.splice(l,1),Xt.splice(l,1),Ne--,F>Ne&&(F=Ne),this.setPage(F))},Uo=function(l){l>0&&l<=Ne&&(F=l)},Xa=p.__private__.getNumberOfPages=p.getNumberOfPages=function(){return Pt.length-1},Wo=function(l,v,M){var q,Y=void 0;return M=M||{},l=l!==void 0?l:we[oe].fontName,v=v!==void 0?v:we[oe].fontStyle,q=l.toLowerCase(),Pe[q]!==void 0&&Pe[q][v]!==void 0?Y=Pe[q][v]:Pe[l]!==void 0&&Pe[l][v]!==void 0?Y=Pe[l][v]:M.disableWarning===!1&&_e.warn("Unable to look up font label for font '"+l+"', '"+v+"'. Refer to getFontList() for available fonts."),Y||M.noFallback||(Y=Pe.times[v])==null&&(Y=Pe.times.normal),Y},Ka=p.__private__.putInfo=function(){var l=Ue(),v=function(q){return q};for(var M in b!==null&&(v=Ze.encryptor(l,0)),E("<<"),E("/Producer ("+cn(v("jsPDF "+Wt.version))+")"),$t)$t.hasOwnProperty(M)&&$t[M]&&E("/"+M.substr(0,1).toUpperCase()+M.substr(1)+" ("+cn(v($t[M]))+")");E("/CreationDate ("+cn(v(at))+")"),E(">>"),E("endobj")},Yr=p.__private__.putCatalog=function(l){var v=(l=l||{}).rootDictionaryObjId||li;switch(Ue(),E("<<"),E("/Type /Catalog"),E("/Pages "+v+" 0 R"),Ft||(Ft="fullwidth"),Ft){case"fullwidth":E("/OpenAction [3 0 R /FitH null]");break;case"fullheight":E("/OpenAction [3 0 R /FitV null]");break;case"fullpage":E("/OpenAction [3 0 R /Fit]");break;case"original":E("/OpenAction [3 0 R /XYZ null null 1]");break;default:var M=""+Ft;M.substr(M.length-1)==="%"&&(Ft=parseInt(Ft)/100),typeof Ft=="number"&&E("/OpenAction [3 0 R /XYZ null null "+gt(Ft)+"]")}switch(re||(re="continuous"),re){case"continuous":E("/PageLayout /OneColumn");break;case"single":E("/PageLayout /SinglePage");break;case"two":case"twoleft":E("/PageLayout /TwoColumnLeft");break;case"tworight":E("/PageLayout /TwoColumnRight")}Jt&&E("/PageMode /"+Jt),pe.publish("putCatalog"),E(">>"),E("endobj")},Za=p.__private__.putTrailer=function(){E("trailer"),E("<<"),E("/Size "+($+1)),E("/Root "+$+" 0 R"),E("/Info "+($-1)+" 0 R"),b!==null&&E("/Encrypt "+Ze.oid+" 0 R"),E("/ID [ <"+nt+"> <"+nt+"> ]"),E(">>")},Qa=p.__private__.putHeader=function(){E("%PDF-"+I),E("%\xBA\xDF\xAC\xE0")},ts=p.__private__.putXRef=function(){var l="0000000000";E("xref"),E("0 "+($+1)),E("0000000000 65535 f ");for(var v=1;v<=$;v++)typeof Q[v]=="function"?E((l+Q[v]()).slice(-10)+" 00000 n "):Q[v]!==void 0?E((l+Q[v]).slice(-10)+" 00000 n "):E("0000000000 00000 n ")},ci=p.__private__.buildDocument=function(){Gt(),ut(et),pe.publish("buildDocument"),Qa(),Yi(),Ro(),Bo(),b!==null&&Ga(),Ka(),Yr();var l=it;return ts(),Za(),E("startxref"),E(""+l),E("%%EOF"),ut(Pt[F]),et.join(`
`)},yr=p.__private__.getBlob=function(l){return new Blob([Rt(l)],{type:"application/pdf"})},wr=p.output=p.__private__.output=Fn(function(l,v){switch(typeof(v=v||{})=="string"?v={filename:v}:v.filename=v.filename||"generated.pdf",l){case void 0:return ci();case"save":p.save(v.filename);break;case"arraybuffer":return Rt(ci());case"blob":return yr(ci());case"bloburi":case"bloburl":if(Vt.URL!==void 0&&typeof Vt.URL.createObjectURL=="function")return Vt.URL&&Vt.URL.createObjectURL(yr(ci()))||void 0;_e.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var M="",q=ci();try{M=Zs(q)}catch{M=Zs(unescape(encodeURIComponent(q)))}return"data:application/pdf;filename="+v.filename+";base64,"+M;case"pdfobjectnewwindow":if(Object.prototype.toString.call(Vt)==="[object Window]"){var Y="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",ct=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';v.pdfObjectUrl&&(Y=v.pdfObjectUrl,ct="");var Ct='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+Y+'"'+ct+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(v)+");<\/script></body></html>",Tt=Vt.open();return Tt!==null&&Tt.document.write(Ct),Tt}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if(Object.prototype.toString.call(Vt)==="[object Window]"){var zt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(v.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+v.filename+'" width="500px" height="400px" /></body></html>',te=Vt.open();if(te!==null){te.document.write(zt);var ie=this;te.document.documentElement.querySelector("#pdfViewer").onload=function(){te.document.title=v.filename,te.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(ie.output("bloburl"))}}return te}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if(Object.prototype.toString.call(Vt)!=="[object Window]")throw new Error("The option dataurlnewwindow just works in a browser-environment.");var se='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",v)+'"></iframe></body></html>',ke=Vt.open();if(ke!==null&&(ke.document.write(se),ke.document.title=v.filename),ke||typeof safari>"u")return ke;break;case"datauri":case"dataurl":return Vt.document.location.href=this.output("datauristring",v);default:return null}}),Ho=function(l){return Array.isArray($i)===!0&&$i.indexOf(l)>-1};switch(i){case"pt":It=1;break;case"mm":It=72/25.4;break;case"cm":It=72/2.54;break;case"in":It=72;break;case"px":It=Ho("px_scaling")==1?.75:96/72;break;case"pc":case"em":It=12;break;case"ex":It=6;break;default:if(typeof i!="number")throw new Error("Invalid unit: "+i);It=i}var Ze=null;Mt(),Z();var es=function(l){return b!==null?Ze.encryptor(l,0):function(v){return v}},Go=p.__private__.getPageInfo=p.getPageInfo=function(l){if(isNaN(l)||l%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Xt[l].objId,pageNumber:l,pageContext:Xt[l]}},Yt=p.__private__.getPageInfoByObjId=function(l){if(isNaN(l)||l%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var v in Xt)if(Xt[v].objId===l)break;return Go(v)},ns=p.__private__.getCurrentPageInfo=p.getCurrentPageInfo=function(){return{objId:Xt[F].objId,pageNumber:F,pageContext:Xt[F]}};p.addPage=function(){return zo.apply(this,arguments),this},p.setPage=function(){return Uo.apply(this,arguments),ut.call(this,Pt[F]),this},p.insertPage=function(l){return this.addPage(),this.movePage(F,l),this},p.movePage=function(l,v){var M,q;if(l>v){M=Pt[l],q=Xt[l];for(var Y=l;Y>v;Y--)Pt[Y]=Pt[Y-1],Xt[Y]=Xt[Y-1];Pt[v]=M,Xt[v]=q,this.setPage(v)}else if(l<v){M=Pt[l],q=Xt[l];for(var ct=l;ct<v;ct++)Pt[ct]=Pt[ct+1],Xt[ct]=Xt[ct+1];Pt[v]=M,Xt[v]=q,this.setPage(v)}return this},p.deletePage=function(){return Ja.apply(this,arguments),this},p.__private__.text=p.text=function(l,v,M,q,Y){var ct,Ct,Tt,zt,te,ie,se,ke,Oe,Re=(q=q||{}).scope||this;if(typeof l=="number"&&typeof v=="number"&&(typeof M=="string"||Array.isArray(M))){var Qe=M;M=v,v=l,l=Qe}if(arguments[3]instanceof Ht?(R("The transform parameter of text() with a Matrix value"),Oe=Y):(Tt=arguments[4],zt=arguments[5],ye(se=arguments[3])==="object"&&se!==null||(typeof Tt=="string"&&(zt=Tt,Tt=null),typeof se=="string"&&(zt=se,se=null),typeof se=="number"&&(Tt=se,se=null),q={flags:se,angle:Tt,align:zt})),isNaN(v)||isNaN(M)||l==null)throw new Error("Invalid arguments passed to jsPDF.text");if(l.length===0)return Re;var Ve="",jn=!1,mn=typeof q.lineHeightFactor=="number"?q.lineHeightFactor:Xi,hi=Re.internal.scaleFactor;function ta(Ce){return Ce=Ce.split("	").join(Array(q.TabLen||9).join(" ")),cn(Ce,se)}function oo(Ce){for(var Le,je=Ce.concat(),We=[],gi=je.length;gi--;)typeof(Le=je.shift())=="string"?We.push(Le):Array.isArray(Ce)&&(Le.length===1||Le[1]===void 0&&Le[2]===void 0)?We.push(Le[0]):We.push([Le[0],Le[1],Le[2]]);return We}function ao(Ce,Le){var je;if(typeof Ce=="string")je=Le(Ce)[0];else if(Array.isArray(Ce)){for(var We,gi,Oi=Ce.concat(),jr=[],na=Oi.length;na--;)typeof(We=Oi.shift())=="string"?jr.push(Le(We)[0]):Array.isArray(We)&&typeof We[0]=="string"&&(gi=Le(We[0],We[1],We[2]),jr.push([gi[0],gi[1],gi[2]]));je=jr}return je}var kr=!1,so=!0;if(typeof l=="string")kr=!0;else if(Array.isArray(l)){var lo=l.concat();Ct=[];for(var Ar,un=lo.length;un--;)(typeof(Ar=lo.shift())!="string"||Array.isArray(Ar)&&typeof Ar[0]!="string")&&(so=!1);kr=so}if(kr===!1)throw new Error('Type of text must be string or Array. "'+l+'" is not recognized.');typeof l=="string"&&(l=l.match(/[\r?\n]/)?l.split(/\r\n|\r|\n/g):[l]);var Sr=At/Re.internal.scaleFactor,Nr=Sr*(mn-1);switch(q.baseline){case"bottom":M-=Nr;break;case"top":M+=Sr-Nr;break;case"hanging":M+=Sr-2*Nr;break;case"middle":M+=Sr/2-Nr}if((ie=q.maxWidth||0)>0&&(typeof l=="string"?l=Re.splitTextToSize(l,ie):Object.prototype.toString.call(l)==="[object Array]"&&(l=l.reduce(function(Ce,Le){return Ce.concat(Re.splitTextToSize(Le,ie))},[]))),ct={text:l,x:v,y:M,options:q,mutex:{pdfEscape:cn,activeFontKey:oe,fonts:we,activeFontSize:At}},pe.publish("preProcessText",ct),l=ct.text,Tt=(q=ct.options).angle,!(Oe instanceof Ht)&&Tt&&typeof Tt=="number"){Tt*=Math.PI/180,q.rotationDirection===0&&(Tt=-Tt),J===j.ADVANCED&&(Tt=-Tt);var Or=Math.cos(Tt),co=Math.sin(Tt);Oe=new Ht(Or,co,-co,Or,0,0)}else Tt&&Tt instanceof Ht&&(Oe=Tt);J!==j.ADVANCED||Oe||(Oe=Xn),(te=q.charSpace||Cr)!==void 0&&(Ve+=tt(A(te))+` Tc
`,this.setCharSpace(this.getCharSpace()||0)),(ke=q.horizontalScale)!==void 0&&(Ve+=tt(100*ke)+` Tz
`),q.lang;var hn=-1,fs=q.renderingMode!==void 0?q.renderingMode:q.stroke,uo=Re.internal.getCurrentPageInfo().pageContext;switch(fs){case 0:case!1:case"fill":hn=0;break;case 1:case!0:case"stroke":hn=1;break;case 2:case"fillThenStroke":hn=2;break;case 3:case"invisible":hn=3;break;case 4:case"fillAndAddForClipping":hn=4;break;case 5:case"strokeAndAddPathForClipping":hn=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":hn=6;break;case 7:case"addToPathForClipping":hn=7}var ea=uo.usedRenderingMode!==void 0?uo.usedRenderingMode:-1;hn!==-1?Ve+=hn+` Tr
`:ea!==-1&&(Ve+=`0 Tr
`),hn!==-1&&(uo.usedRenderingMode=hn),zt=q.align||"left";var Tn,ho=At*mn,ds=Re.internal.pageSize.getWidth(),ps=we[oe];te=q.charSpace||Cr,ie=q.maxWidth||0,se=Object.assign({autoencode:!0,noBOM:!0},q.flags);var ki=[];if(Object.prototype.toString.call(l)==="[object Array]"){var xn;Ct=oo(l),zt!=="left"&&(Tn=Ct.map(function(Ce){return Re.getStringUnitWidth(Ce,{font:ps,charSpace:te,fontSize:At,doKerning:!1})*At/hi}));var En,Ai=0;if(zt==="right"){v-=Tn[0],l=[],un=Ct.length;for(var ti=0;ti<un;ti++)ti===0?(En=ui(v),xn=Ci(M)):(En=A(Ai-Tn[ti]),xn=-ho),l.push([Ct[ti],En,xn]),Ai=Tn[ti]}else if(zt==="center"){v-=Tn[0]/2,l=[],un=Ct.length;for(var fi=0;fi<un;fi++)fi===0?(En=ui(v),xn=Ci(M)):(En=A((Ai-Tn[fi])/2),xn=-ho),l.push([Ct[fi],En,xn]),Ai=Tn[fi]}else if(zt==="left"){l=[],un=Ct.length;for(var Mr=0;Mr<un;Mr++)l.push(Ct[Mr])}else{if(zt!=="justify")throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');l=[],un=Ct.length,ie=ie!==0?ie:ds;for(var Dn=0;Dn<un;Dn++)xn=Dn===0?Ci(M):-ho,En=Dn===0?ui(v):0,Dn<un-1?ki.push(tt(A((ie-Tn[Dn])/(Ct[Dn].split(" ").length-1)))):ki.push(0),l.push([Ct[Dn],En,xn])}}var Fr=typeof q.R2L=="boolean"?q.R2L:qt;Fr===!0&&(l=ao(l,function(Ce,Le,je){return[Ce.split("").reverse().join(""),Le,je]})),ct={text:l,x:v,y:M,options:q,mutex:{pdfEscape:cn,activeFontKey:oe,fonts:we,activeFontSize:At}},pe.publish("postProcessText",ct),l=ct.text,jn=ct.mutex.isHex||!1;var fo=we[oe].encoding;fo!=="WinAnsiEncoding"&&fo!=="StandardEncoding"||(l=ao(l,function(Ce,Le,je){return[ta(Ce),Le,je]})),Ct=oo(l),l=[];for(var Si,Bn,di,Qi=0,Ir=1,tr=Array.isArray(Ct[0])?Ir:Qi,Ni="",po=function(Ce,Le,je){var We="";return je instanceof Ht?(je=typeof q.angle=="number"?Sn(je,new Ht(1,0,0,1,Ce,Le)):Sn(new Ht(1,0,0,1,Ce,Le),je),J===j.ADVANCED&&(je=Sn(new Ht(1,0,0,-1,0,0),je)),We=je.join(" ")+` Tm
`):We=tt(Ce)+" "+tt(Le)+` Td
`,We},_n=0;_n<Ct.length;_n++){switch(Ni="",tr){case Ir:di=(jn?"<":"(")+Ct[_n][0]+(jn?">":")"),Si=parseFloat(Ct[_n][1]),Bn=parseFloat(Ct[_n][2]);break;case Qi:di=(jn?"<":"(")+Ct[_n]+(jn?">":")"),Si=ui(v),Bn=Ci(M)}ki!==void 0&&ki[_n]!==void 0&&(Ni=ki[_n]+` Tw
`),_n===0?l.push(Ni+po(Si,Bn,Oe)+di):tr===Qi?l.push(Ni+di):tr===Ir&&l.push(Ni+po(Si,Bn,Oe)+di)}l=tr===Qi?l.join(` Tj
T* `):l.join(` Tj
`),l+=` Tj
`;var pi=`BT
/`;return pi+=oe+" "+At+` Tf
`,pi+=tt(At*mn)+` TL
`,pi+=Ki+`
`,pi+=Ve,pi+=l,E(pi+="ET"),P[oe]=!0,Re};var is=p.__private__.clip=p.clip=function(l){return E(l==="evenodd"?"W*":"W"),this};p.clipEvenOdd=function(){return is("evenodd")},p.__private__.discardPath=p.discardPath=function(){return E("n"),this};var Qn=p.__private__.isValidStyle=function(l){var v=!1;return[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(l)!==-1&&(v=!0),v};p.__private__.setDefaultPathOperation=p.setDefaultPathOperation=function(l){return Qn(l)&&(g=l),this};var Vo=p.__private__.getStyle=p.getStyle=function(l){var v=g;switch(l){case"D":case"S":v="S";break;case"F":v="f";break;case"FD":case"DF":v="B";break;case"f":case"f*":case"B":case"B*":v=l}return v},$o=p.close=function(){return E("h"),this};p.stroke=function(){return E("S"),this},p.fill=function(l){return xr("f",l),this},p.fillEvenOdd=function(l){return xr("f*",l),this},p.fillStroke=function(l){return xr("B",l),this},p.fillStrokeEvenOdd=function(l){return xr("B*",l),this};var xr=function(l,v){ye(v)==="object"?os(v,l):E(l)},Jr=function(l){l===null||J===j.ADVANCED&&l===void 0||(l=Vo(l),E(l))};function rs(l,v,M,q,Y){var ct=new or(v||this.boundingBox,M||this.xStep,q||this.yStep,this.gState,Y||this.matrix);ct.stream=this.stream;var Ct=l+"$$"+this.cloneIndex+++"$$";return ln(Ct,ct),ct}var os=function(l,v){var M=Vi[l.key],q=de[M];if(q instanceof Ui)E("q"),E(as(v)),q.gState&&p.setGState(q.gState),E(l.matrix.toString()+" cm"),E("/"+M+" sh"),E("Q");else if(q instanceof or){var Y=new Ht(1,0,0,-1,0,Pi());l.matrix&&(Y=Y.multiply(l.matrix||Xn),M=rs.call(q,l.key,l.boundingBox,l.xStep,l.yStep,Y).id),E("q"),E("/Pattern cs"),E("/"+M+" scn"),q.gState&&p.setGState(q.gState),E(v),E("Q")}},as=function(l){switch(l){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},Xr=p.moveTo=function(l,v){return E(tt(A(l))+" "+tt(B(v))+" m"),this},Ji=p.lineTo=function(l,v){return E(tt(A(l))+" "+tt(B(v))+" l"),this},_i=p.curveTo=function(l,v,M,q,Y,ct){return E([tt(A(l)),tt(B(v)),tt(A(M)),tt(B(q)),tt(A(Y)),tt(B(ct)),"c"].join(" ")),this};p.__private__.line=p.line=function(l,v,M,q,Y){if(isNaN(l)||isNaN(v)||isNaN(M)||isNaN(q)||!Qn(Y))throw new Error("Invalid arguments passed to jsPDF.line");return J===j.COMPAT?this.lines([[M-l,q-v]],l,v,[1,1],Y||"S"):this.lines([[M-l,q-v]],l,v,[1,1]).stroke()},p.__private__.lines=p.lines=function(l,v,M,q,Y,ct){var Ct,Tt,zt,te,ie,se,ke,Oe,Re,Qe,Ve,jn;if(typeof l=="number"&&(jn=M,M=v,v=l,l=jn),q=q||[1,1],ct=ct||!1,isNaN(v)||isNaN(M)||!Array.isArray(l)||!Array.isArray(q)||!Qn(Y)||typeof ct!="boolean")throw new Error("Invalid arguments passed to jsPDF.lines");for(Xr(v,M),Ct=q[0],Tt=q[1],te=l.length,Qe=v,Ve=M,zt=0;zt<te;zt++)(ie=l[zt]).length===2?(Qe=ie[0]*Ct+Qe,Ve=ie[1]*Tt+Ve,Ji(Qe,Ve)):(se=ie[0]*Ct+Qe,ke=ie[1]*Tt+Ve,Oe=ie[2]*Ct+Qe,Re=ie[3]*Tt+Ve,Qe=ie[4]*Ct+Qe,Ve=ie[5]*Tt+Ve,_i(se,ke,Oe,Re,Qe,Ve));return ct&&$o(),Jr(Y),this},p.path=function(l){for(var v=0;v<l.length;v++){var M=l[v],q=M.c;switch(M.op){case"m":Xr(q[0],q[1]);break;case"l":Ji(q[0],q[1]);break;case"c":_i.apply(this,q);break;case"h":$o()}}return this},p.__private__.rect=p.rect=function(l,v,M,q,Y){if(isNaN(l)||isNaN(v)||isNaN(M)||isNaN(q)||!Qn(Y))throw new Error("Invalid arguments passed to jsPDF.rect");return J===j.COMPAT&&(q=-q),E([tt(A(l)),tt(B(v)),tt(A(M)),tt(A(q)),"re"].join(" ")),Jr(Y),this},p.__private__.triangle=p.triangle=function(l,v,M,q,Y,ct,Ct){if(isNaN(l)||isNaN(v)||isNaN(M)||isNaN(q)||isNaN(Y)||isNaN(ct)||!Qn(Ct))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[M-l,q-v],[Y-M,ct-q],[l-Y,v-ct]],l,v,[1,1],Ct,!0),this},p.__private__.roundedRect=p.roundedRect=function(l,v,M,q,Y,ct,Ct){if(isNaN(l)||isNaN(v)||isNaN(M)||isNaN(q)||isNaN(Y)||isNaN(ct)||!Qn(Ct))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var Tt=4/3*(Math.SQRT2-1);return Y=Math.min(Y,.5*M),ct=Math.min(ct,.5*q),this.lines([[M-2*Y,0],[Y*Tt,0,Y,ct-ct*Tt,Y,ct],[0,q-2*ct],[0,ct*Tt,-Y*Tt,ct,-Y,ct],[2*Y-M,0],[-Y*Tt,0,-Y,-ct*Tt,-Y,-ct],[0,2*ct-q],[0,-ct*Tt,Y*Tt,-ct,Y,-ct]],l+Y,v,[1,1],Ct,!0),this},p.__private__.ellipse=p.ellipse=function(l,v,M,q,Y){if(isNaN(l)||isNaN(v)||isNaN(M)||isNaN(q)||!Qn(Y))throw new Error("Invalid arguments passed to jsPDF.ellipse");var ct=4/3*(Math.SQRT2-1)*M,Ct=4/3*(Math.SQRT2-1)*q;return Xr(l+M,v),_i(l+M,v-Ct,l+ct,v-q,l,v-q),_i(l-ct,v-q,l-M,v-Ct,l-M,v),_i(l-M,v+Ct,l-ct,v+q,l,v+q),_i(l+ct,v+q,l+M,v+Ct,l+M,v),Jr(Y),this},p.__private__.circle=p.circle=function(l,v,M,q){if(isNaN(l)||isNaN(v)||isNaN(M)||!Qn(q))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(l,v,M,M,q)},p.setFont=function(l,v,M){return M&&(v=_t(v,M)),oe=Wo(l,v,{disableWarning:!1}),this};var ss=p.__private__.getFont=p.getFont=function(){return we[Wo.apply(p,arguments)]};p.__private__.getFontList=p.getFontList=function(){var l,v,M={};for(l in Pe)if(Pe.hasOwnProperty(l))for(v in M[l]=[],Pe[l])Pe[l].hasOwnProperty(v)&&M[l].push(v);return M},p.addFont=function(l,v,M,q,Y){var ct=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&ct.indexOf(arguments[3])!==-1?Y=arguments[3]:arguments[3]&&ct.indexOf(arguments[3])==-1&&(M=_t(M,q)),Y=Y||"Identity-H",Vr.call(this,l,v,M,Y)};var Xi,Kr=r.lineWidth||.200025,_r=p.__private__.getLineWidth=p.getLineWidth=function(){return Kr},Yo=p.__private__.setLineWidth=p.setLineWidth=function(l){return Kr=l,E(tt(A(l))+" w"),this};p.__private__.setLineDash=Wt.API.setLineDash=Wt.API.setLineDashPattern=function(l,v){if(l=l||[],v=v||0,isNaN(v)||!Array.isArray(l))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return l=l.map(function(M){return tt(A(M))}).join(" "),v=tt(A(v)),E("["+l+"] "+v+" d"),this};var Jo=p.__private__.getLineHeight=p.getLineHeight=function(){return At*Xi};p.__private__.getLineHeight=p.getLineHeight=function(){return At*Xi};var Xo=p.__private__.setLineHeightFactor=p.setLineHeightFactor=function(l){return typeof(l=l||1.15)=="number"&&(Xi=l),this},Ko=p.__private__.getLineHeightFactor=p.getLineHeightFactor=function(){return Xi};Xo(r.lineHeight);var ui=p.__private__.getHorizontalCoordinate=function(l){return A(l)},Ci=p.__private__.getVerticalCoordinate=function(l){return J===j.ADVANCED?l:Xt[F].mediaBox.topRightY-Xt[F].mediaBox.bottomLeftY-A(l)},ls=p.__private__.getHorizontalCoordinateString=p.getHorizontalCoordinateString=function(l){return tt(ui(l))},Li=p.__private__.getVerticalCoordinateString=p.getVerticalCoordinateString=function(l){return tt(Ci(l))},In=r.strokeColor||"0 G";p.__private__.getStrokeColor=p.getDrawColor=function(){return On(In)},p.__private__.setStrokeColor=p.setDrawColor=function(l,v,M,q){return In=Mn({ch1:l,ch2:v,ch3:M,ch4:q,pdfColorType:"draw",precision:2}),E(In),this};var Zr=r.fillColor||"0 g";p.__private__.getFillColor=p.getFillColor=function(){return On(Zr)},p.__private__.setFillColor=p.setFillColor=function(l,v,M,q){return Zr=Mn({ch1:l,ch2:v,ch3:M,ch4:q,pdfColorType:"fill",precision:2}),E(Zr),this};var Ki=r.textColor||"0 g",cs=p.__private__.getTextColor=p.getTextColor=function(){return On(Ki)};p.__private__.setTextColor=p.setTextColor=function(l,v,M,q){return Ki=Mn({ch1:l,ch2:v,ch3:M,ch4:q,pdfColorType:"text",precision:3}),this};var Cr=r.charSpace,us=p.__private__.getCharSpace=p.getCharSpace=function(){return parseFloat(Cr||0)};p.__private__.setCharSpace=p.setCharSpace=function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return Cr=l,this};var Qr=0;p.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},p.__private__.setLineCap=p.setLineCap=function(l){var v=p.CapJoinStyles[l];if(v===void 0)throw new Error("Line cap style of '"+l+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Qr=v,E(v+" J"),this};var to=0;p.__private__.setLineJoin=p.setLineJoin=function(l){var v=p.CapJoinStyles[l];if(v===void 0)throw new Error("Line join style of '"+l+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return to=v,E(v+" j"),this},p.__private__.setLineMiterLimit=p.__private__.setMiterLimit=p.setLineMiterLimit=p.setMiterLimit=function(l){if(l=l||0,isNaN(l))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return E(tt(A(l))+" M"),this},p.GState=jo,p.setGState=function(l){(l=typeof l=="string"?Se[An[l]]:Zo(null,l)).equals(si)||(E("/"+l.id+" gs"),si=l)};var Zo=function(l,v){if(!l||!An[l]){var M=!1;for(var q in Se)if(Se.hasOwnProperty(q)&&Se[q].equals(v)){M=!0;break}if(M)v=Se[q];else{var Y="GS"+(Object.keys(Se).length+1).toString(10);Se[Y]=v,v.id=Y}return l&&(An[l]=v.id),pe.publish("addGState",v),v}};p.addGState=function(l,v){return Zo(l,v),this},p.saveGraphicsState=function(){return E("q"),$n.push({key:oe,size:At,color:Ki}),this},p.restoreGraphicsState=function(){E("Q");var l=$n.pop();return oe=l.key,At=l.size,Ki=l.color,si=null,this},p.setCurrentTransformationMatrix=function(l){return E(l.toString()+" cm"),this},p.comment=function(l){return E("#"+l),this};var Lr=function(l,v){var M=l||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return M},set:function(ct){isNaN(ct)||(M=parseFloat(ct))}});var q=v||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return q},set:function(ct){isNaN(ct)||(q=parseFloat(ct))}});var Y="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return Y},set:function(ct){Y=ct.toString()}}),this},eo=function(l,v,M,q){Lr.call(this,l,v),this.type="rect";var Y=M||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return Y},set:function(Ct){isNaN(Ct)||(Y=parseFloat(Ct))}});var ct=q||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return ct},set:function(Ct){isNaN(Ct)||(ct=parseFloat(Ct))}}),this},no=function(){this.page=Ne,this.currentPage=F,this.pages=Pt.slice(0),this.pagesContext=Xt.slice(0),this.x=Ke,this.y=ue,this.matrix=kn,this.width=Zi(F),this.height=Pi(F),this.outputDestination=Dt,this.id="",this.objectNumber=-1};no.prototype.restore=function(){Ne=this.page,F=this.currentPage,Xt=this.pagesContext,Pt=this.pages,Ke=this.x,ue=this.y,kn=this.matrix,io(F,this.width),ro(F,this.height),Dt=this.outputDestination};var Qo=function(l,v,M,q,Y){Jn.push(new no),Ne=F=0,Pt=[],Ke=l,ue=v,kn=Y,$r([M,q])},hs=function(l){if(Yn[l])Jn.pop().restore();else{var v=new no,M="Xo"+(Object.keys(Je).length+1).toString(10);v.id=M,Yn[l]=M,Je[M]=v,pe.publish("addFormObject",v),Jn.pop().restore()}};for(var Pr in p.beginFormObject=function(l,v,M,q,Y){return Qo(l,v,M,q,Y),this},p.endFormObject=function(l){return hs(l),this},p.doFormObject=function(l,v){var M=Je[Yn[l]];return E("q"),E(v.toString()+" cm"),E("/"+M.id+" Do"),E("Q"),this},p.getFormObject=function(l){var v=Je[Yn[l]];return{x:v.x,y:v.y,width:v.width,height:v.height,matrix:v.matrix}},p.save=function(l,v){return l=l||"generated.pdf",(v=v||{}).returnPromise=v.returnPromise||!1,v.returnPromise===!1?(rr(yr(ci()),l),typeof rr.unload=="function"&&Vt.setTimeout&&setTimeout(rr.unload,911),this):new Promise(function(M,q){try{var Y=rr(yr(ci()),l);typeof rr.unload=="function"&&Vt.setTimeout&&setTimeout(rr.unload,911),M(Y)}catch(ct){q(ct.message)}})},Wt.API)Wt.API.hasOwnProperty(Pr)&&(Pr==="events"&&Wt.API.events.length?function(l,v){var M,q,Y;for(Y=v.length-1;Y!==-1;Y--)M=v[Y][0],q=v[Y][1],l.subscribe.apply(l,[M].concat(typeof q=="function"?[q]:q))}(pe,Wt.API.events):p[Pr]=Wt.API[Pr]);var Zi=p.getPageWidth=function(l){return(Xt[l=l||F].mediaBox.topRightX-Xt[l].mediaBox.bottomLeftX)/It},io=p.setPageWidth=function(l,v){Xt[l].mediaBox.topRightX=v*It+Xt[l].mediaBox.bottomLeftX},Pi=p.getPageHeight=function(l){return(Xt[l=l||F].mediaBox.topRightY-Xt[l].mediaBox.bottomLeftY)/It},ro=p.setPageHeight=function(l,v){Xt[l].mediaBox.topRightY=v*It+Xt[l].mediaBox.bottomLeftY};return p.internal={pdfEscape:cn,getStyle:Vo,getFont:ss,getFontSize:Ot,getCharSpace:us,getTextColor:cs,getLineHeight:Jo,getLineHeightFactor:Ko,getLineWidth:_r,write:Qt,getHorizontalCoordinate:ui,getVerticalCoordinate:Ci,getCoordinateString:ls,getVerticalCoordinateString:Li,collections:{},newObject:Ue,newAdditionalObject:hr,newObjectDeferred:Be,newObjectDeferredBegin:gn,getFilters:Kn,putStream:wn,events:pe,scaleFactor:It,pageSize:{getWidth:function(){return Zi(F)},setWidth:function(l){io(F,l)},getHeight:function(){return Pi(F)},setHeight:function(l){ro(F,l)}},encryptionOptions:b,encryption:Ze,getEncryptor:es,output:wr,getNumberOfPages:Xa,pages:Pt,out:E,f2:gt,f3:N,getPageInfo:Go,getPageInfoByObjId:Yt,getCurrentPageInfo:ns,getPDFVersion:O,Point:Lr,Rectangle:eo,Matrix:Ht,hasHotfix:Ho},Object.defineProperty(p.internal.pageSize,"width",{get:function(){return Zi(F)},set:function(l){io(F,l)},enumerable:!0,configurable:!0}),Object.defineProperty(p.internal.pageSize,"height",{get:function(){return Pi(F)},set:function(l){ro(F,l)},enumerable:!0,configurable:!0}),Ya.call(p,Lt),oe="F1",zo(s,t),pe.publish("initialized"),p}function ou(r){return r.reduce(function(n,t,i){return n[t]=i,n},{})}function il(r){var n=r.family.replace(/"|'/g,"").toLowerCase(),t=function(a){return au[a=a||"normal"]?a:"normal"}(r.style),i=function(a){if(!a)return 400;if(typeof a=="number")return a>=100&&a<=900&&a%100==0?a:400;if(/^\d00$/.test(a))return parseInt(a);switch(a){case"bold":return 700;case"normal":default:return 400}}(r.weight),s=function(a){return typeof nl[a=a||"normal"]=="number"?a:"normal"}(r.stretch);return{family:n,style:t,weight:i,stretch:s,src:r.src||[],ref:r.ref||{name:n,style:[s,t,i].join(" ")}}}function zc(r,n,t,i){var s;for(s=t;s>=0&&s<n.length;s+=i)if(r[n[s]])return r[n[s]];for(s=t;s>=0&&s<n.length;s-=i)if(r[n[s]])return r[n[s]]}function Wc(r){return[r.stretch,r.style,r.weight,r.family].join(" ")}function e1(r,n,t){for(var i=(t=t||{}).defaultFontFamily||"times",s=Object.assign({},t1,t.genericFontFamilies||{}),a=null,c=null,h=0;h<n.length;++h)if(s[(a=il(n[h])).family]&&(a.family=s[a.family]),r.hasOwnProperty(a.family)){c=r[a.family];break}if(!(c=c||r[i]))throw new Error("Could not find a font-family for the rule '"+Wc(a)+"' and default family '"+i+"'.");if(c=function(f,g){if(g[f])return g[f];var b=nl[f],C=b<=nl.normal?-1:1,P=zc(g,su,b,C);if(!P)throw new Error("Could not find a matching font-stretch value for "+f);return P}(a.stretch,c),c=function(f,g){if(g[f])return g[f];for(var b=au[f],C=0;C<b.length;++C)if(g[b[C]])return g[b[C]];throw new Error("Could not find a matching font-style for "+f)}(a.style,c),!(c=function(f,g){if(g[f])return g[f];if(f===400&&g[500])return g[500];if(f===500&&g[400])return g[400];var b=Qh[f],C=zc(g,lu,b,f<400?-1:1);if(!C)throw new Error("Could not find a matching font-weight for value "+f);return C}(a.weight,c)))throw new Error("Failed to resolve a font for the rule '"+Wc(a)+"'.");return c}function Hc(r){return r.trimLeft()}function n1(r,n){for(var t=0;t<r.length;){if(r.charAt(t)===n)return[r.substring(0,t),r.substring(t+1)];t+=1}return null}function i1(r){var n=r.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return n===null?null:[n[0],r.substring(n[0].length)]}function o1(r){var n=0;if(r[n++]!==71||r[n++]!==73||r[n++]!==70||r[n++]!==56||(r[n++]+1&253)!=56||r[n++]!==97)throw new Error("Invalid GIF 87a/89a header.");var t=r[n++]|r[n++]<<8,i=r[n++]|r[n++]<<8,s=r[n++],a=s>>7,c=1<<(7&s)+1;r[n++],r[n++];var h=null,f=null;a&&(h=n,f=c,n+=3*c);var g=!0,b=[],C=0,P=null,p=0,I=null;for(this.width=t,this.height=i;g&&n<r.length;)switch(r[n++]){case 33:switch(r[n++]){case 255:if(r[n]!==11||r[n+1]==78&&r[n+2]==69&&r[n+3]==84&&r[n+4]==83&&r[n+5]==67&&r[n+6]==65&&r[n+7]==80&&r[n+8]==69&&r[n+9]==50&&r[n+10]==46&&r[n+11]==48&&r[n+12]==3&&r[n+13]==1&&r[n+16]==0)n+=14,I=r[n++]|r[n++]<<8,n++;else for(n+=12;;){if(!((N=r[n++])>=0))throw Error("Invalid block size");if(N===0)break;n+=N}break;case 249:if(r[n++]!==4||r[n+4]!==0)throw new Error("Invalid graphics extension block.");var O=r[n++];C=r[n++]|r[n++]<<8,P=r[n++],!(1&O)&&(P=null),p=O>>2&7,n++;break;case 254:for(;;){if(!((N=r[n++])>=0))throw Error("Invalid block size");if(N===0)break;n+=N}break;default:throw new Error("Unknown graphic control label: 0x"+r[n-1].toString(16))}break;case 44:var D=r[n++]|r[n++]<<8,k=r[n++]|r[n++]<<8,j=r[n++]|r[n++]<<8,J=r[n++]|r[n++]<<8,lt=r[n++],ht=lt>>6&1,_t=1<<(7&lt)+1,tt=h,R=f,vt=!1;lt>>7&&(vt=!0,tt=n,R=_t,n+=3*_t);var gt=n;for(n++;;){var N;if(!((N=r[n++])>=0))throw Error("Invalid block size");if(N===0)break;n+=N}b.push({x:D,y:k,width:j,height:J,has_local_palette:vt,palette_offset:tt,palette_size:R,data_offset:gt,data_length:n-gt,transparent_index:P,interlaced:!!ht,delay:C,disposal:p});break;case 59:g=!1;break;default:throw new Error("Unknown gif block: 0x"+r[n-1].toString(16))}this.numFrames=function(){return b.length},this.loopCount=function(){return I},this.frameInfo=function(A){if(A<0||A>=b.length)throw new Error("Frame index out of range.");return b[A]},this.decodeAndBlitFrameBGRA=function(A,z){var B=this.frameInfo(A),at=B.width*B.height,nt=new Uint8Array(at);Xc(r,B.data_offset,nt,at);var ft=B.palette_offset,Z=B.transparent_index;Z===null&&(Z=256);var pt=B.width,dt=t-pt,Mt=pt,x=4*(B.y*t+B.x),F=4*((B.y+B.height)*t+B.x),T=x,H=4*dt;B.interlaced===!0&&(H+=4*t*7);for(var $=8,Q=0,et=nt.length;Q<et;++Q){var it=nt[Q];if(Mt===0&&(Mt=pt,(T+=H)>=F&&(H=4*dt+4*t*($-1),T=x+(pt+dt)*($<<1),$>>=1)),it===Z)T+=4;else{var kt=r[ft+3*it],Pt=r[ft+3*it+1],jt=r[ft+3*it+2];z[T++]=jt,z[T++]=Pt,z[T++]=kt,z[T++]=255}--Mt}},this.decodeAndBlitFrameRGBA=function(A,z){var B=this.frameInfo(A),at=B.width*B.height,nt=new Uint8Array(at);Xc(r,B.data_offset,nt,at);var ft=B.palette_offset,Z=B.transparent_index;Z===null&&(Z=256);var pt=B.width,dt=t-pt,Mt=pt,x=4*(B.y*t+B.x),F=4*((B.y+B.height)*t+B.x),T=x,H=4*dt;B.interlaced===!0&&(H+=4*t*7);for(var $=8,Q=0,et=nt.length;Q<et;++Q){var it=nt[Q];if(Mt===0&&(Mt=pt,(T+=H)>=F&&(H=4*dt+4*t*($-1),T=x+(pt+dt)*($<<1),$>>=1)),it===Z)T+=4;else{var kt=r[ft+3*it],Pt=r[ft+3*it+1],jt=r[ft+3*it+2];z[T++]=kt,z[T++]=Pt,z[T++]=jt,z[T++]=255}--Mt}}}function Xc(r,n,t,i){for(var s=r[n++],a=1<<s,c=a+1,h=c+1,f=s+1,g=(1<<f)-1,b=0,C=0,P=0,p=r[n++],I=new Int32Array(4096),O=null;;){for(;b<16&&p!==0;)C|=r[n++]<<b,b+=8,p===1?p=r[n++]:--p;if(b<f)break;var D=C&g;if(C>>=f,b-=f,D!==a){if(D===c)break;for(var k=D<h?D:O,j=0,J=k;J>a;)J=I[J]>>8,++j;var lt=J;if(P+j+(k!==D?1:0)>i)return void _e.log("Warning, gif stream longer than expected.");t[P++]=lt;var ht=P+=j;for(k!==D&&(t[P++]=lt),J=k;j--;)J=I[J],t[--ht]=255&J,J>>=8;O!==null&&h<4096&&(I[h++]=O<<8|lt,h>=g+1&&f<12&&(++f,g=g<<1|1)),O=D}else h=c+1,g=(1<<(f=s+1))-1,O=null}return P!==i&&_e.log("Warning, gif stream shorter than expected."),t}function Ks(r){var n,t,i,s,a,c=Math.floor,h=new Array(64),f=new Array(64),g=new Array(64),b=new Array(64),C=new Array(65535),P=new Array(65535),p=new Array(64),I=new Array(64),O=[],D=0,k=7,j=new Array(64),J=new Array(64),lt=new Array(64),ht=new Array(256),_t=new Array(2048),tt=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],R=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],vt=[0,1,2,3,4,5,6,7,8,9,10,11],gt=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],N=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],A=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],z=[0,1,2,3,4,5,6,7,8,9,10,11],B=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],at=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function nt(x,F){for(var T=0,H=0,$=new Array,Q=1;Q<=16;Q++){for(var et=1;et<=x[Q];et++)$[F[H]]=[],$[F[H]][0]=T,$[F[H]][1]=Q,H++,T++;T*=2}return $}function ft(x){for(var F=x[0],T=x[1]-1;T>=0;)F&1<<T&&(D|=1<<k),T--,--k<0&&(D==255?(Z(255),Z(0)):Z(D),k=7,D=0)}function Z(x){O.push(x)}function pt(x){Z(x>>8&255),Z(255&x)}function dt(x,F,T,H,$){for(var Q,et=$[0],it=$[240],kt=function(Lt,At){var Ft,Ot,qt,Jt,ne,re,le,ve,$t,oe,It=0;for($t=0;$t<8;++$t){Ft=Lt[It],Ot=Lt[It+1],qt=Lt[It+2],Jt=Lt[It+3],ne=Lt[It+4],re=Lt[It+5],le=Lt[It+6];var Ke=Ft+(ve=Lt[It+7]),ue=Ft-ve,kn=Ot+le,we=Ot-le,Pe=qt+re,$n=qt-re,de=Jt+ne,Vi=Jt-ne,Se=Ke+de,An=Ke-de,si=kn+Pe,Ne=kn-Pe;Lt[It]=Se+si,Lt[It+4]=Se-si;var Xt=.707106781*(Ne+An);Lt[It+2]=An+Xt,Lt[It+6]=An-Xt;var pe=.382683433*((Se=Vi+$n)-(Ne=we+ue)),$i=.5411961*Se+pe,Je=1.306562965*Ne+pe,Yn=.707106781*(si=$n+we),Jn=ue+Yn,Ht=ue-Yn;Lt[It+5]=Ht+$i,Lt[It+3]=Ht-$i,Lt[It+1]=Jn+Je,Lt[It+7]=Jn-Je,It+=8}for(It=0,$t=0;$t<8;++$t){Ft=Lt[It],Ot=Lt[It+8],qt=Lt[It+16],Jt=Lt[It+24],ne=Lt[It+32],re=Lt[It+40],le=Lt[It+48];var Sn=Ft+(ve=Lt[It+56]),Xn=Ft-ve,ln=Ot+le,Ue=Ot-le,Be=qt+re,gn=qt-re,hr=Jt+ne,li=Jt-ne,Nn=Sn+hr,On=Sn-hr,Mn=ln+Be,Kn=ln-Be;Lt[It]=Nn+Mn,Lt[It+32]=Nn-Mn;var wn=.707106781*(Kn+On);Lt[It+16]=On+wn,Lt[It+48]=On-wn;var Zn=.382683433*((Nn=li+gn)-(Kn=Ue+Xn)),Yi=.5411961*Nn+Zn,fr=1.306562965*Kn+Zn,dr=.707106781*(Mn=gn+Ue),pr=Xn+dr,gr=Xn-dr;Lt[It+40]=gr+Yi,Lt[It+24]=gr-Yi,Lt[It+8]=pr+fr,Lt[It+56]=pr-fr,It++}for($t=0;$t<64;++$t)oe=Lt[$t]*At[$t],p[$t]=oe>0?oe+.5|0:oe-.5|0;return p}(x,F),Pt=0;Pt<64;++Pt)I[tt[Pt]]=kt[Pt];var jt=I[0]-T;T=I[0],jt==0?ft(H[0]):(ft(H[P[Q=32767+jt]]),ft(C[Q]));for(var Dt=63;Dt>0&&I[Dt]==0;)Dt--;if(Dt==0)return ft(et),T;for(var Gt,ut=1;ut<=Dt;){for(var E=ut;I[ut]==0&&ut<=Dt;)++ut;var Qt=ut-E;if(Qt>=16){Gt=Qt>>4;for(var Rt=1;Rt<=Gt;++Rt)ft(it);Qt&=15}Q=32767+I[ut],ft($[(Qt<<4)+P[Q]]),ft(C[Q]),ut++}return Dt!=63&&ft(et),T}function Mt(x){x=Math.min(Math.max(x,1),100),a!=x&&(function(F){for(var T=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],H=0;H<64;H++){var $=c((T[H]*F+50)/100);$=Math.min(Math.max($,1),255),h[tt[H]]=$}for(var Q=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],et=0;et<64;et++){var it=c((Q[et]*F+50)/100);it=Math.min(Math.max(it,1),255),f[tt[et]]=it}for(var kt=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Pt=0,jt=0;jt<8;jt++)for(var Dt=0;Dt<8;Dt++)g[Pt]=1/(h[tt[Pt]]*kt[jt]*kt[Dt]*8),b[Pt]=1/(f[tt[Pt]]*kt[jt]*kt[Dt]*8),Pt++}(x<50?Math.floor(5e3/x):Math.floor(200-2*x)),a=x)}this.encode=function(x,F){F&&Mt(F),O=new Array,D=0,k=7,pt(65496),pt(65504),pt(16),Z(74),Z(70),Z(73),Z(70),Z(0),Z(1),Z(1),Z(0),pt(1),pt(1),Z(0),Z(0),function(){pt(65499),pt(132),Z(0);for(var Ot=0;Ot<64;Ot++)Z(h[Ot]);Z(1);for(var qt=0;qt<64;qt++)Z(f[qt])}(),function(Ot,qt){pt(65472),pt(17),Z(8),pt(qt),pt(Ot),Z(3),Z(1),Z(17),Z(0),Z(2),Z(17),Z(1),Z(3),Z(17),Z(1)}(x.width,x.height),function(){pt(65476),pt(418),Z(0);for(var Ot=0;Ot<16;Ot++)Z(R[Ot+1]);for(var qt=0;qt<=11;qt++)Z(vt[qt]);Z(16);for(var Jt=0;Jt<16;Jt++)Z(gt[Jt+1]);for(var ne=0;ne<=161;ne++)Z(N[ne]);Z(1);for(var re=0;re<16;re++)Z(A[re+1]);for(var le=0;le<=11;le++)Z(z[le]);Z(17);for(var ve=0;ve<16;ve++)Z(B[ve+1]);for(var $t=0;$t<=161;$t++)Z(at[$t])}(),pt(65498),pt(12),Z(3),Z(1),Z(0),Z(2),Z(17),Z(3),Z(17),Z(0),Z(63),Z(0);var T=0,H=0,$=0;D=0,k=7,this.encode.displayName="_encode_";for(var Q,et,it,kt,Pt,jt,Dt,Gt,ut,E=x.data,Qt=x.width,Rt=x.height,Lt=4*Qt,At=0;At<Rt;){for(Q=0;Q<Lt;){for(Pt=Lt*At+Q,Dt=-1,Gt=0,ut=0;ut<64;ut++)jt=Pt+(Gt=ut>>3)*Lt+(Dt=4*(7&ut)),At+Gt>=Rt&&(jt-=Lt*(At+1+Gt-Rt)),Q+Dt>=Lt&&(jt-=Q+Dt-Lt+4),et=E[jt++],it=E[jt++],kt=E[jt++],j[ut]=(_t[et]+_t[it+256>>0]+_t[kt+512>>0]>>16)-128,J[ut]=(_t[et+768>>0]+_t[it+1024>>0]+_t[kt+1280>>0]>>16)-128,lt[ut]=(_t[et+1280>>0]+_t[it+1536>>0]+_t[kt+1792>>0]>>16)-128;T=dt(j,g,T,n,i),H=dt(J,b,H,t,s),$=dt(lt,b,$,t,s),Q+=32}At+=8}if(k>=0){var Ft=[];Ft[1]=k+1,Ft[0]=(1<<k+1)-1,ft(Ft)}return pt(65497),new Uint8Array(O)},r=r||50,function(){for(var x=String.fromCharCode,F=0;F<256;F++)ht[F]=x(F)}(),n=nt(R,vt),t=nt(A,z),i=nt(gt,N),s=nt(B,at),function(){for(var x=1,F=2,T=1;T<=15;T++){for(var H=x;H<F;H++)P[32767+H]=T,C[32767+H]=[],C[32767+H][1]=T,C[32767+H][0]=H;for(var $=-(F-1);$<=-x;$++)P[32767+$]=T,C[32767+$]=[],C[32767+$][1]=T,C[32767+$][0]=F-1+$;x<<=1,F<<=1}}(),function(){for(var x=0;x<256;x++)_t[x]=19595*x,_t[x+256>>0]=38470*x,_t[x+512>>0]=7471*x+32768,_t[x+768>>0]=-11059*x,_t[x+1024>>0]=-21709*x,_t[x+1280>>0]=32768*x+8421375,_t[x+1536>>0]=-27439*x,_t[x+1792>>0]=-5329*x}(),Mt(r)}function Hn(r,n){if(this.pos=0,this.buffer=r,this.datav=new DataView(r.buffer),this.is_with_alpha=!!n,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,["BM","BA","CI","CP","IC","PT"].indexOf(this.flag)===-1)throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function Kc(r){function n(R){if(!R)throw Error("assert :P")}function t(R,vt,gt){for(var N=0;4>N;N++)if(R[vt+N]!=gt.charCodeAt(N))return!0;return!1}function i(R,vt,gt,N,A){for(var z=0;z<A;z++)R[vt+z]=gt[N+z]}function s(R,vt,gt,N){for(var A=0;A<N;A++)R[vt+A]=gt}function a(R){return new Int32Array(R)}function c(R,vt){for(var gt=[],N=0;N<R;N++)gt.push(new vt);return gt}function h(R,vt){var gt=[];return function N(A,z,B){for(var at=B[z],nt=0;nt<at&&(A.push(B.length>z+1?[]:new vt),!(B.length<z+1));nt++)N(A[nt],z+1,B)}(gt,0,R),gt}var f=function(){var R=this;function vt(e,o){for(var u=1<<o-1>>>0;e&u;)u>>>=1;return u?(e&u-1)+u:e}function gt(e,o,u,d,m){n(!(d%u));do e[o+(d-=u)]=m;while(0<d)}function N(e,o,u,d,m){if(n(2328>=m),512>=m)var y=a(512);else if((y=a(m))==null)return 0;return function(w,_,L,S,U,X){var K,V,yt=_,ot=1<<L,W=a(16),G=a(16);for(n(U!=0),n(S!=null),n(w!=null),n(0<L),V=0;V<U;++V){if(15<S[V])return 0;++W[S[V]]}if(W[0]==U)return 0;for(G[1]=0,K=1;15>K;++K){if(W[K]>1<<K)return 0;G[K+1]=G[K]+W[K]}for(V=0;V<U;++V)K=S[V],0<S[V]&&(X[G[K]++]=V);if(G[15]==1)return(S=new A).g=0,S.value=X[0],gt(w,yt,1,ot,S),ot;var mt,wt=-1,bt=ot-1,Et=0,St=1,Ut=1,Nt=1<<L;for(V=0,K=1,U=2;K<=L;++K,U<<=1){if(St+=Ut<<=1,0>(Ut-=W[K]))return 0;for(;0<W[K];--W[K])(S=new A).g=K,S.value=X[V++],gt(w,yt+Et,U,Nt,S),Et=vt(Et,K)}for(K=L+1,U=2;15>=K;++K,U<<=1){if(St+=Ut<<=1,0>(Ut-=W[K]))return 0;for(;0<W[K];--W[K]){if(S=new A,(Et&bt)!=wt){for(yt+=Nt,mt=1<<(wt=K)-L;15>wt&&!(0>=(mt-=W[wt]));)++wt,mt<<=1;ot+=Nt=1<<(mt=wt-L),w[_+(wt=Et&bt)].g=mt+L,w[_+wt].value=yt-_-wt}S.g=K-L,S.value=X[V++],gt(w,yt+(Et>>L),U,Nt,S),Et=vt(Et,K)}}return St!=2*G[15]-1?0:ot}(e,o,u,d,m,y)}function A(){this.value=this.g=0}function z(){this.value=this.g=0}function B(){this.G=c(5,A),this.H=a(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=c(un,z)}function at(e,o,u,d){n(e!=null),n(o!=null),n(2147483648>d),e.Ca=254,e.I=0,e.b=-8,e.Ka=0,e.oa=o,e.pa=u,e.Jd=o,e.Yc=u+d,e.Zc=4<=d?u+d-4+1:u,Q(e)}function nt(e,o){for(var u=0;0<o--;)u|=it(e,128)<<o;return u}function ft(e,o){var u=nt(e,o);return et(e)?-u:u}function Z(e,o,u,d){var m,y=0;for(n(e!=null),n(o!=null),n(4294967288>d),e.Sb=d,e.Ra=0,e.u=0,e.h=0,4<d&&(d=4),m=0;m<d;++m)y+=o[u+m]<<8*m;e.Ra=y,e.bb=d,e.oa=o,e.pa=u}function pt(e){for(;8<=e.u&&e.bb<e.Sb;)e.Ra>>>=8,e.Ra+=e.oa[e.pa+e.bb]<<Or-8>>>0,++e.bb,e.u-=8;T(e)&&(e.h=1,e.u=0)}function dt(e,o){if(n(0<=o),!e.h&&o<=Nr){var u=F(e)&Sr[o];return e.u+=o,pt(e),u}return e.h=1,e.u=0}function Mt(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function x(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function F(e){return e.Ra>>>(e.u&Or-1)>>>0}function T(e){return n(e.bb<=e.Sb),e.h||e.bb==e.Sb&&e.u>Or}function H(e,o){e.u=o,e.h=T(e)}function $(e){e.u>=co&&(n(e.u>=co),pt(e))}function Q(e){n(e!=null&&e.oa!=null),e.pa<e.Zc?(e.I=(e.oa[e.pa++]|e.I<<8)>>>0,e.b+=8):(n(e!=null&&e.oa!=null),e.pa<e.Yc?(e.b+=8,e.I=e.oa[e.pa++]|e.I<<8):e.Ka?e.b=0:(e.I<<=8,e.b+=8,e.Ka=1))}function et(e){return nt(e,1)}function it(e,o){var u=e.Ca;0>e.b&&Q(e);var d=e.b,m=u*o>>>8,y=(e.I>>>d>m)+0;for(y?(u-=m,e.I-=m+1<<d>>>0):u=m+1,d=u,m=0;256<=d;)m+=8,d>>=8;return d=7^m+hn[d],e.b-=d,e.Ca=(u<<d)-1,y}function kt(e,o,u){e[o+0]=u>>24&255,e[o+1]=u>>16&255,e[o+2]=u>>8&255,e[o+3]=u>>0&255}function Pt(e,o){return e[o+0]<<0|e[o+1]<<8}function jt(e,o){return Pt(e,o)|e[o+2]<<16}function Dt(e,o){return Pt(e,o)|Pt(e,o+2)<<16}function Gt(e,o){var u=1<<o;return n(e!=null),n(0<o),e.X=a(u),e.X==null?0:(e.Mb=32-o,e.Xa=o,1)}function ut(e,o){n(e!=null),n(o!=null),n(e.Xa==o.Xa),i(o.X,0,e.X,0,1<<o.Xa)}function E(){this.X=[],this.Xa=this.Mb=0}function Qt(e,o,u,d){n(u!=null),n(d!=null);var m=u[0],y=d[0];return m==0&&(m=(e*y+o/2)/o),y==0&&(y=(o*m+e/2)/e),0>=m||0>=y?0:(u[0]=m,d[0]=y,1)}function Rt(e,o){return e+(1<<o)-1>>>o}function Lt(e,o){return((4278255360&e)+(4278255360&o)>>>0&4278255360)+((16711935&e)+(16711935&o)>>>0&16711935)>>>0}function At(e,o){R[o]=function(u,d,m,y,w,_,L){var S;for(S=0;S<w;++S){var U=R[e](_[L+S-1],m,y+S);_[L+S]=Lt(u[d+S],U)}}}function Ft(){this.ud=this.hd=this.jd=0}function Ot(e,o){return((4278124286&(e^o))>>>1)+(e&o)>>>0}function qt(e){return 0<=e&&256>e?e:0>e?0:255<e?255:void 0}function Jt(e,o){return qt(e+(e-o+.5>>1))}function ne(e,o,u){return Math.abs(o-u)-Math.abs(e-u)}function re(e,o,u,d,m,y,w){for(d=y[w-1],u=0;u<m;++u)y[w+u]=d=Lt(e[o+u],d)}function le(e,o,u,d,m){var y;for(y=0;y<u;++y){var w=e[o+y],_=w>>8&255,L=16711935&(L=(L=16711935&w)+((_<<16)+_));d[m+y]=(4278255360&w)+L>>>0}}function ve(e,o){o.jd=e>>0&255,o.hd=e>>8&255,o.ud=e>>16&255}function $t(e,o,u,d,m,y){var w;for(w=0;w<d;++w){var _=o[u+w],L=_>>>8,S=_,U=255&(U=(U=_>>>16)+((e.jd<<24>>24)*(L<<24>>24)>>>5));S=255&(S=(S=S+((e.hd<<24>>24)*(L<<24>>24)>>>5))+((e.ud<<24>>24)*(U<<24>>24)>>>5)),m[y+w]=(4278255360&_)+(U<<16)+S}}function oe(e,o,u,d,m){R[o]=function(y,w,_,L,S,U,X,K,V){for(L=X;L<K;++L)for(X=0;X<V;++X)S[U++]=m(_[d(y[w++])])},R[e]=function(y,w,_,L,S,U,X){var K=8>>y.b,V=y.Ea,yt=y.K[0],ot=y.w;if(8>K)for(y=(1<<y.b)-1,ot=(1<<K)-1;w<_;++w){var W,G=0;for(W=0;W<V;++W)W&y||(G=d(L[S++])),U[X++]=m(yt[G&ot]),G>>=K}else R["VP8LMapColor"+u](L,S,yt,ot,U,X,w,_,V)}}function It(e,o,u,d,m){for(u=o+u;o<u;){var y=e[o++];d[m++]=y>>16&255,d[m++]=y>>8&255,d[m++]=y>>0&255}}function Ke(e,o,u,d,m){for(u=o+u;o<u;){var y=e[o++];d[m++]=y>>16&255,d[m++]=y>>8&255,d[m++]=y>>0&255,d[m++]=y>>24&255}}function ue(e,o,u,d,m){for(u=o+u;o<u;){var y=(w=e[o++])>>16&240|w>>12&15,w=w>>0&240|w>>28&15;d[m++]=y,d[m++]=w}}function kn(e,o,u,d,m){for(u=o+u;o<u;){var y=(w=e[o++])>>16&248|w>>13&7,w=w>>5&224|w>>3&31;d[m++]=y,d[m++]=w}}function we(e,o,u,d,m){for(u=o+u;o<u;){var y=e[o++];d[m++]=y>>0&255,d[m++]=y>>8&255,d[m++]=y>>16&255}}function Pe(e,o,u,d,m,y){if(y==0)for(u=o+u;o<u;)kt(d,((y=e[o++])[0]>>24|y[1]>>8&65280|y[2]<<8&16711680|y[3]<<24)>>>0),m+=32;else i(d,m,e,o,u)}function $n(e,o){R[o][0]=R[e+"0"],R[o][1]=R[e+"1"],R[o][2]=R[e+"2"],R[o][3]=R[e+"3"],R[o][4]=R[e+"4"],R[o][5]=R[e+"5"],R[o][6]=R[e+"6"],R[o][7]=R[e+"7"],R[o][8]=R[e+"8"],R[o][9]=R[e+"9"],R[o][10]=R[e+"10"],R[o][11]=R[e+"11"],R[o][12]=R[e+"12"],R[o][13]=R[e+"13"],R[o][14]=R[e+"0"],R[o][15]=R[e+"0"]}function de(e){return e==vs||e==bs||e==la||e==ys}function Vi(){this.eb=[],this.size=this.A=this.fb=0}function Se(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function An(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new Vi,this.f.kb=new Se,this.sd=null}function si(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function Ne(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function Xt(e){return alert("todo:WebPSamplerProcessPlane"),e.T}function pe(e,o){var u=e.T,d=o.ba.f.RGBA,m=d.eb,y=d.fb+e.ka*d.A,w=Ln[o.ba.S],_=e.y,L=e.O,S=e.f,U=e.N,X=e.ea,K=e.W,V=o.cc,yt=o.dc,ot=o.Mc,W=o.Nc,G=e.ka,mt=e.ka+e.T,wt=e.U,bt=wt+1>>1;for(G==0?w(_,L,null,null,S,U,X,K,S,U,X,K,m,y,null,null,wt):(w(o.ec,o.fc,_,L,V,yt,ot,W,S,U,X,K,m,y-d.A,m,y,wt),++u);G+2<mt;G+=2)V=S,yt=U,ot=X,W=K,U+=e.Rc,K+=e.Rc,y+=2*d.A,w(_,(L+=2*e.fa)-e.fa,_,L,V,yt,ot,W,S,U,X,K,m,y-d.A,m,y,wt);return L+=e.fa,e.j+mt<e.o?(i(o.ec,o.fc,_,L,wt),i(o.cc,o.dc,S,U,bt),i(o.Mc,o.Nc,X,K,bt),u--):1&mt||w(_,L,null,null,S,U,X,K,S,U,X,K,m,y+d.A,null,null,wt),u}function $i(e,o,u){var d=e.F,m=[e.J];if(d!=null){var y=e.U,w=o.ba.S,_=w==sa||w==la;o=o.ba.f.RGBA;var L=[0],S=e.ka;L[0]=e.T,e.Kb&&(S==0?--L[0]:(--S,m[0]-=e.width),e.j+e.ka+e.T==e.o&&(L[0]=e.o-e.j-S));var U=o.eb;S=o.fb+S*o.A,e=na(d,m[0],e.width,y,L,U,S+(_?0:3),o.A),n(u==L),e&&de(w)&&Oi(U,S,_,y,L,o.A)}return 0}function Je(e){var o=e.ma,u=o.ba.S,d=11>u,m=u==oa||u==aa||u==sa||u==ms||u==12||de(u);if(o.memory=null,o.Ib=null,o.Jb=null,o.Nd=null,!so(o.Oa,e,m?11:12))return 0;if(m&&de(u)&&Ct(),e.da)alert("todo:use_scaling");else{if(d){if(o.Ib=Xt,e.Kb){if(u=e.U+1>>1,o.memory=a(e.U+2*u),o.memory==null)return 0;o.ec=o.memory,o.fc=0,o.cc=o.ec,o.dc=o.fc+e.U,o.Mc=o.cc,o.Nc=o.dc+u,o.Ib=pe,Ct()}}else alert("todo:EmitYUV");m&&(o.Jb=$i,d&&Y())}if(d&&!_l){for(e=0;256>e;++e)Mu[e]=89858*(e-128)+ua>>ca,ju[e]=-22014*(e-128)+ua,Iu[e]=-45773*(e-128),Fu[e]=113618*(e-128)+ua>>ca;for(e=mo;e<_s;++e)o=76283*(e-16)+ua>>ca,Tu[e-mo]=mn(o,255),Eu[e-mo]=mn(o+8>>4,15);_l=1}return 1}function Yn(e){var o=e.ma,u=e.U,d=e.T;return n(!(1&e.ka)),0>=u||0>=d?0:(u=o.Ib(e,o),o.Jb!=null&&o.Jb(e,o,u),o.Dc+=u,1)}function Jn(e){e.ma.memory=null}function Ht(e,o,u,d){return dt(e,8)!=47?0:(o[0]=dt(e,14)+1,u[0]=dt(e,14)+1,d[0]=dt(e,1),dt(e,3)!=0?0:!e.h)}function Sn(e,o){if(4>e)return e+1;var u=e-2>>1;return(2+(1&e)<<u)+dt(o,u)+1}function Xn(e,o){return 120<o?o-120:1<=(u=((u=vu[o-1])>>4)*e+(8-(15&u)))?u:1;var u}function ln(e,o,u){var d=F(u),m=e[o+=255&d].g-8;return 0<m&&(H(u,u.u+8),d=F(u),o+=e[o].value,o+=d&(1<<m)-1),H(u,u.u+e[o].g),e[o].value}function Ue(e,o,u){return u.g+=e.g,u.value+=e.value<<o>>>0,n(8>=u.g),e.g}function Be(e,o,u){var d=e.xc;return n((o=d==0?0:e.vc[e.md*(u>>d)+(o>>d)])<e.Wb),e.Ya[o]}function gn(e,o,u,d){var m=e.ab,y=e.c*o,w=e.C;o=w+o;var _=u,L=d;for(d=e.Ta,u=e.Ua;0<m--;){var S=e.gc[m],U=w,X=o,K=_,V=L,yt=(L=d,_=u,S.Ea);switch(n(U<X),n(X<=S.nc),S.hc){case 2:ea(K,V,(X-U)*yt,L,_);break;case 0:var ot=U,W=X,G=L,mt=_,wt=(Nt=S).Ea;ot==0&&(fs(K,V,null,null,1,G,mt),re(K,V+1,0,0,wt-1,G,mt+1),V+=wt,mt+=wt,++ot);for(var bt=1<<Nt.b,Et=bt-1,St=Rt(wt,Nt.b),Ut=Nt.K,Nt=Nt.w+(ot>>Nt.b)*St;ot<W;){var he=Ut,ge=Nt,ce=1;for(uo(K,V,G,mt-wt,1,G,mt);ce<wt;){var ae=(ce&~Et)+bt;ae>wt&&(ae=wt),(0,ki[he[ge++]>>8&15])(K,V+ +ce,G,mt+ce-wt,ae-ce,G,mt+ce),ce=ae}V+=wt,mt+=wt,++ot&Et||(Nt+=St)}X!=S.nc&&i(L,_-yt,L,_+(X-U-1)*yt,yt);break;case 1:for(yt=K,W=V,wt=(K=S.Ea)-(mt=K&~(G=(V=1<<S.b)-1)),ot=Rt(K,S.b),bt=S.K,S=S.w+(U>>S.b)*ot;U<X;){for(Et=bt,St=S,Ut=new Ft,Nt=W+mt,he=W+K;W<Nt;)ve(Et[St++],Ut),xn(Ut,yt,W,V,L,_),W+=V,_+=V;W<he&&(ve(Et[St++],Ut),xn(Ut,yt,W,wt,L,_),W+=wt,_+=wt),++U&G||(S+=ot)}break;case 3:if(K==L&&V==_&&0<S.b){for(W=L,K=yt=_+(X-U)*yt-(mt=(X-U)*Rt(S.Ea,S.b)),V=L,G=_,ot=[],mt=(wt=mt)-1;0<=mt;--mt)ot[mt]=V[G+mt];for(mt=wt-1;0<=mt;--mt)W[K+mt]=ot[mt];Tn(S,U,X,L,yt,L,_)}else Tn(S,U,X,K,V,L,_)}_=d,L=u}L!=u&&i(d,u,_,L,y)}function hr(e,o){var u=e.V,d=e.Ba+e.c*e.C,m=o-e.C;if(n(o<=e.l.o),n(16>=m),0<m){var y=e.l,w=e.Ta,_=e.Ua,L=y.width;if(gn(e,m,u,d),m=_=[_],n((u=e.C)<(d=o)),n(y.v<y.va),d>y.o&&(d=y.o),u<y.j){var S=y.j-u;u=y.j,m[0]+=S*L}if(u>=d?u=0:(m[0]+=4*y.v,y.ka=u-y.j,y.U=y.va-y.v,y.T=d-u,u=1),u){if(_=_[0],11>(u=e.ca).S){var U=u.f.RGBA,X=(d=u.S,m=y.U,y=y.T,S=U.eb,U.A),K=y;for(U=U.fb+e.Ma*U.A;0<K--;){var V=w,yt=_,ot=m,W=S,G=U;switch(d){case ra:En(V,yt,ot,W,G);break;case oa:Ai(V,yt,ot,W,G);break;case vs:Ai(V,yt,ot,W,G),Oi(W,G,0,ot,1,0);break;case fl:Mr(V,yt,ot,W,G);break;case aa:Pe(V,yt,ot,W,G,1);break;case bs:Pe(V,yt,ot,W,G,1),Oi(W,G,0,ot,1,0);break;case sa:Pe(V,yt,ot,W,G,0);break;case la:Pe(V,yt,ot,W,G,0),Oi(W,G,1,ot,1,0);break;case ms:ti(V,yt,ot,W,G);break;case ys:ti(V,yt,ot,W,G),jr(W,G,ot,1,0);break;case dl:fi(V,yt,ot,W,G);break;default:n(0)}_+=L,U+=X}e.Ma+=y}else alert("todo:EmitRescaledRowsYUVA");n(e.Ma<=u.height)}}e.C=o,n(e.C<=e.i)}function li(e){var o;if(0<e.ua)return 0;for(o=0;o<e.Wb;++o){var u=e.Ya[o].G,d=e.Ya[o].H;if(0<u[1][d[1]+0].g||0<u[2][d[2]+0].g||0<u[3][d[3]+0].g)return 0}return 1}function Nn(e,o,u,d,m,y){if(e.Z!=0){var w=e.qd,_=e.rd;for(n(Fi[e.Z]!=null);o<u;++o)Fi[e.Z](w,_,d,m,d,m,y),w=d,_=m,m+=y;e.qd=w,e.rd=_}}function On(e,o){var u=e.l.ma,d=u.Z==0||u.Z==1?e.l.j:e.C;if(d=e.C<d?d:e.C,n(o<=e.l.o),o>d){var m=e.l.width,y=u.ca,w=u.tb+m*d,_=e.V,L=e.Ba+e.c*d,S=e.gc;n(e.ab==1),n(S[0].hc==3),ds(S[0],d,o,_,L,y,w),Nn(u,d,o,y,w,m)}e.C=e.Ma=o}function Mn(e,o,u,d,m,y,w){var _=e.$/d,L=e.$%d,S=e.m,U=e.s,X=u+e.$,K=X;m=u+d*m;var V=u+d*y,yt=280+U.ua,ot=e.Pb?_:16777216,W=0<U.ua?U.Wa:null,G=U.wc,mt=X<V?Be(U,L,_):null;n(e.C<y),n(V<=m);var wt=!1;t:for(;;){for(;wt||X<V;){var bt=0;if(_>=ot){var Et=X-u;n((ot=e).Pb),ot.wd=ot.m,ot.xd=Et,0<ot.s.ua&&ut(ot.s.Wa,ot.s.vb),ot=_+yu}if(L&G||(mt=Be(U,L,_)),n(mt!=null),mt.Qb&&(o[X]=mt.qb,wt=!0),!wt)if($(S),mt.jc){bt=S,Et=o;var St=X,Ut=mt.pd[F(bt)&un-1];n(mt.jc),256>Ut.g?(H(bt,bt.u+Ut.g),Et[St]=Ut.value,bt=0):(H(bt,bt.u+Ut.g-256),n(256<=Ut.value),bt=Ut.value),bt==0&&(wt=!0)}else bt=ln(mt.G[0],mt.H[0],S);if(S.h)break;if(wt||256>bt){if(!wt)if(mt.nd)o[X]=(mt.qb|bt<<8)>>>0;else{if($(S),wt=ln(mt.G[1],mt.H[1],S),$(S),Et=ln(mt.G[2],mt.H[2],S),St=ln(mt.G[3],mt.H[3],S),S.h)break;o[X]=(St<<24|wt<<16|bt<<8|Et)>>>0}if(wt=!1,++X,++L>=d&&(L=0,++_,w!=null&&_<=y&&!(_%16)&&w(e,_),W!=null))for(;K<X;)bt=o[K++],W.X[(506832829*bt&4294967295)>>>W.Mb]=bt}else if(280>bt){if(bt=Sn(bt-256,S),Et=ln(mt.G[4],mt.H[4],S),$(S),Et=Xn(d,Et=Sn(Et,S)),S.h)break;if(X-u<Et||m-X<bt)break t;for(St=0;St<bt;++St)o[X+St]=o[X+St-Et];for(X+=bt,L+=bt;L>=d;)L-=d,++_,w!=null&&_<=y&&!(_%16)&&w(e,_);if(n(X<=m),L&G&&(mt=Be(U,L,_)),W!=null)for(;K<X;)bt=o[K++],W.X[(506832829*bt&4294967295)>>>W.Mb]=bt}else{if(!(bt<yt))break t;for(wt=bt-280,n(W!=null);K<X;)bt=o[K++],W.X[(506832829*bt&4294967295)>>>W.Mb]=bt;bt=X,n(!(wt>>>(Et=W).Xa)),o[bt]=Et.X[wt],wt=!0}wt||n(S.h==T(S))}if(e.Pb&&S.h&&X<m)n(e.m.h),e.a=5,e.m=e.wd,e.$=e.xd,0<e.s.ua&&ut(e.s.vb,e.s.Wa);else{if(S.h)break t;w?.(e,_>y?y:_),e.a=0,e.$=X-u}return 1}return e.a=3,0}function Kn(e){n(e!=null),e.vc=null,e.yc=null,e.Ya=null;var o=e.Wa;o!=null&&(o.X=null),e.vb=null,n(e!=null)}function wn(){var e=new hs;return e==null?null:(e.a=0,e.xb=ml,$n("Predictor","VP8LPredictors"),$n("Predictor","VP8LPredictors_C"),$n("PredictorAdd","VP8LPredictorsAdd"),$n("PredictorAdd","VP8LPredictorsAdd_C"),ea=le,xn=$t,En=It,Ai=Ke,ti=ue,fi=kn,Mr=we,R.VP8LMapColor32b=ho,R.VP8LMapColor8b=ps,e)}function Zn(e,o,u,d,m){var y=1,w=[e],_=[o],L=d.m,S=d.s,U=null,X=0;t:for(;;){if(u)for(;y&&dt(L,1);){var K=w,V=_,yt=d,ot=1,W=yt.m,G=yt.gc[yt.ab],mt=dt(W,2);if(yt.Oc&1<<mt)y=0;else{switch(yt.Oc|=1<<mt,G.hc=mt,G.Ea=K[0],G.nc=V[0],G.K=[null],++yt.ab,n(4>=yt.ab),mt){case 0:case 1:G.b=dt(W,3)+2,ot=Zn(Rt(G.Ea,G.b),Rt(G.nc,G.b),0,yt,G.K),G.K=G.K[0];break;case 3:var wt,bt=dt(W,8)+1,Et=16<bt?0:4<bt?1:2<bt?2:3;if(K[0]=Rt(G.Ea,Et),G.b=Et,wt=ot=Zn(bt,1,0,yt,G.K)){var St,Ut=bt,Nt=G,he=1<<(8>>Nt.b),ge=a(he);if(ge==null)wt=0;else{var ce=Nt.K[0],ae=Nt.w;for(ge[0]=Nt.K[0][0],St=1;St<1*Ut;++St)ge[St]=Lt(ce[ae+St],ge[St-1]);for(;St<4*he;++St)ge[St]=0;Nt.K[0]=null,Nt.K[0]=ge,wt=1}}ot=wt;break;case 2:break;default:n(0)}y=ot}}if(w=w[0],_=_[0],y&&dt(L,1)&&!(y=1<=(X=dt(L,4))&&11>=X)){d.a=3;break t}var xe;if(xe=y)e:{var be,ee,qe,fn=d,ze=w,dn=_,me=X,bn=u,yn=fn.m,$e=fn.s,Xe=[null],on=1,Pn=0,ei=bu[me];n:for(;;){if(bn&&dt(yn,1)){var Ye=dt(yn,3)+2,vi=Rt(ze,Ye),er=Rt(dn,Ye),Tr=vi*er;if(!Zn(vi,er,0,fn,Xe))break n;for(Xe=Xe[0],$e.xc=Ye,be=0;be<Tr;++be){var Ii=Xe[be]>>8&65535;Xe[be]=Ii,Ii>=on&&(on=Ii+1)}}if(yn.h)break n;for(ee=0;5>ee;++ee){var Me=pl[ee];!ee&&0<me&&(Me+=1<<me),Pn<Me&&(Pn=Me)}var Cs=c(on*ei,A),Pl=on,kl=c(Pl,B);if(kl==null)var fa=null;else n(65536>=Pl),fa=kl;var vo=a(Pn);if(fa==null||vo==null||Cs==null){fn.a=1;break n}var da=Cs;for(be=qe=0;be<on;++be){var qn=fa[be],Er=qn.G,Dr=qn.H,Al=0,pa=1,Sl=0;for(ee=0;5>ee;++ee){Me=pl[ee],Er[ee]=da,Dr[ee]=qe,!ee&&0<me&&(Me+=1<<me);r:{var ga,Ls=Me,ma=fn,bo=vo,Ru=da,qu=qe,Ps=0,ji=ma.m,zu=dt(ji,1);if(s(bo,0,0,Ls),zu){var Uu=dt(ji,1)+1,Wu=dt(ji,1),Nl=dt(ji,Wu==0?1:8);bo[Nl]=1,Uu==2&&(bo[Nl=dt(ji,8)]=1);var va=1}else{var Ol=a(19),Ml=dt(ji,4)+4;if(19<Ml){ma.a=3;var ba=0;break r}for(ga=0;ga<Ml;++ga)Ol[mu[ga]]=dt(ji,3);var ks=void 0,yo=void 0,Fl=ma,Hu=Ol,ya=Ls,Il=bo,As=0,Ti=Fl.m,jl=8,Tl=c(128,A);i:for(;N(Tl,0,7,Hu,19);){if(dt(Ti,1)){var Gu=2+2*dt(Ti,3);if((ks=2+dt(Ti,Gu))>ya)break i}else ks=ya;for(yo=0;yo<ya&&ks--;){$(Ti);var El=Tl[0+(127&F(Ti))];H(Ti,Ti.u+El.g);var Br=El.value;if(16>Br)Il[yo++]=Br,Br!=0&&(jl=Br);else{var Vu=Br==16,Dl=Br-16,$u=pu[Dl],Bl=dt(Ti,du[Dl])+$u;if(yo+Bl>ya)break i;for(var Yu=Vu?jl:0;0<Bl--;)Il[yo++]=Yu}}As=1;break i}As||(Fl.a=3),va=As}(va=va&&!ji.h)&&(Ps=N(Ru,qu,8,bo,Ls)),va&&Ps!=0?ba=Ps:(ma.a=3,ba=0)}if(ba==0)break n;if(pa&&gu[ee]==1&&(pa=da[qe].g==0),Al+=da[qe].g,qe+=ba,3>=ee){var wo,Ss=vo[0];for(wo=1;wo<Me;++wo)vo[wo]>Ss&&(Ss=vo[wo]);Sl+=Ss}}if(qn.nd=pa,qn.Qb=0,pa&&(qn.qb=(Er[3][Dr[3]+0].value<<24|Er[1][Dr[1]+0].value<<16|Er[2][Dr[2]+0].value)>>>0,Al==0&&256>Er[0][Dr[0]+0].value&&(qn.Qb=1,qn.qb+=Er[0][Dr[0]+0].value<<8)),qn.jc=!qn.Qb&&6>Sl,qn.jc){var wa,bi=qn;for(wa=0;wa<un;++wa){var Ei=wa,Di=bi.pd[Ei],xa=bi.G[0][bi.H[0]+Ei];256<=xa.value?(Di.g=xa.g+256,Di.value=xa.value):(Di.g=0,Di.value=0,Ei>>=Ue(xa,8,Di),Ei>>=Ue(bi.G[1][bi.H[1]+Ei],16,Di),Ei>>=Ue(bi.G[2][bi.H[2]+Ei],0,Di),Ue(bi.G[3][bi.H[3]+Ei],24,Di))}}}$e.vc=Xe,$e.Wb=on,$e.Ya=fa,$e.yc=Cs,xe=1;break e}xe=0}if(!(y=xe)){d.a=3;break t}if(0<X){if(S.ua=1<<X,!Gt(S.Wa,X)){d.a=1,y=0;break t}}else S.ua=0;var Ns=d,Rl=w,Ju=_,Os=Ns.s,Ms=Os.xc;if(Ns.c=Rl,Ns.i=Ju,Os.md=Rt(Rl,Ms),Os.wc=Ms==0?-1:(1<<Ms)-1,u){d.xb=ku;break t}if((U=a(w*_))==null){d.a=1,y=0;break t}y=(y=Mn(d,U,0,w,_,_,null))&&!L.h;break t}return y?(m!=null?m[0]=U:(n(U==null),n(u)),d.$=0,u||Kn(S)):Kn(S),y}function Yi(e,o){var u=e.c*e.i,d=u+o+16*o;return n(e.c<=o),e.V=a(d),e.V==null?(e.Ta=null,e.Ua=0,e.a=1,0):(e.Ta=e.V,e.Ua=e.Ba+u+o,1)}function fr(e,o){var u=e.C,d=o-u,m=e.V,y=e.Ba+e.c*u;for(n(o<=e.l.o);0<d;){var w=16<d?16:d,_=e.l.ma,L=e.l.width,S=L*w,U=_.ca,X=_.tb+L*u,K=e.Ta,V=e.Ua;gn(e,w,m,y),al(K,V,U,X,S),Nn(_,u,u+w,U,X,L),d-=w,m+=w*e.c,u+=w}n(u==o),e.C=e.Ma=o}function dr(){this.ub=this.yd=this.td=this.Rb=0}function pr(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function gr(){this.Fb=this.Bb=this.Cb=0,this.Zb=a(4),this.Lb=a(4)}function To(){this.Yb=function(){var e=[];return function o(u,d,m){for(var y=m[d],w=0;w<y&&(u.push(m.length>d+1?[]:0),!(m.length<d+1));w++)o(u[w],d+1,m)}(e,0,[3,11]),e}()}function Ua(){this.jb=a(3),this.Wc=h([4,8],To),this.Xc=h([4,17],To)}function Wa(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new a(4),this.od=new a(4)}function mr(){this.ld=this.La=this.dd=this.tc=0}function Eo(){this.Na=this.la=0}function Ha(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Gr(){this.ad=a(384),this.Za=0,this.Ob=a(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function Ga(){this.uc=this.M=this.Nb=0,this.wa=Array(new mr),this.Y=0,this.ya=Array(new Gr),this.aa=0,this.l=new vr}function Do(){this.y=a(16),this.f=a(8),this.ea=a(8)}function Va(){this.cb=this.a=0,this.sc="",this.m=new Mt,this.Od=new dr,this.Kc=new pr,this.ed=new Wa,this.Qa=new gr,this.Ic=this.$c=this.Aa=0,this.D=new Ga,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=c(8,Mt),this.ia=0,this.pb=c(4,Ha),this.Pa=new Ua,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new Do),this.Hd=0,this.rb=Array(new Eo),this.sb=0,this.wa=Array(new mr),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Gr),this.L=this.aa=0,this.gd=h([4,2],mr),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function vr(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function $a(){var e=new Va;return e!=null&&(e.a=0,e.sc="OK",e.cb=0,e.Xb=0,go||(go=qo)),e}function Ie(e,o,u){return e.a==0&&(e.a=o,e.sc=u,e.cb=0),0}function Bo(e,o,u){return 3<=u&&e[o+0]==157&&e[o+1]==1&&e[o+2]==42}function Ro(e,o){if(e==null)return 0;if(e.a=0,e.sc="OK",o==null)return Ie(e,2,"null VP8Io passed to VP8GetHeaders()");var u=o.data,d=o.w,m=o.ha;if(4>m)return Ie(e,7,"Truncated header.");var y=u[d+0]|u[d+1]<<8|u[d+2]<<16,w=e.Od;if(w.Rb=!(1&y),w.td=y>>1&7,w.yd=y>>4&1,w.ub=y>>5,3<w.td)return Ie(e,3,"Incorrect keyframe parameters.");if(!w.yd)return Ie(e,4,"Frame not displayable.");d+=3,m-=3;var _=e.Kc;if(w.Rb){if(7>m)return Ie(e,7,"cannot parse picture header");if(!Bo(u,d,m))return Ie(e,3,"Bad code word");_.c=16383&(u[d+4]<<8|u[d+3]),_.Td=u[d+4]>>6,_.i=16383&(u[d+6]<<8|u[d+5]),_.Ud=u[d+6]>>6,d+=7,m-=7,e.za=_.c+15>>4,e.Ub=_.i+15>>4,o.width=_.c,o.height=_.i,o.Da=0,o.j=0,o.v=0,o.va=o.width,o.o=o.height,o.da=0,o.ib=o.width,o.hb=o.height,o.U=o.width,o.T=o.height,s((y=e.Pa).jb,0,255,y.jb.length),n((y=e.Qa)!=null),y.Cb=0,y.Bb=0,y.Fb=1,s(y.Zb,0,0,y.Zb.length),s(y.Lb,0,0,y.Lb)}if(w.ub>m)return Ie(e,7,"bad partition length");at(y=e.m,u,d,w.ub),d+=w.ub,m-=w.ub,w.Rb&&(_.Ld=et(y),_.Kd=et(y)),_=e.Qa;var L,S=e.Pa;if(n(y!=null),n(_!=null),_.Cb=et(y),_.Cb){if(_.Bb=et(y),et(y)){for(_.Fb=et(y),L=0;4>L;++L)_.Zb[L]=et(y)?ft(y,7):0;for(L=0;4>L;++L)_.Lb[L]=et(y)?ft(y,6):0}if(_.Bb)for(L=0;3>L;++L)S.jb[L]=et(y)?nt(y,8):255}else _.Bb=0;if(y.Ka)return Ie(e,3,"cannot parse segment header");if((_=e.ed).zd=et(y),_.Tb=nt(y,6),_.wb=nt(y,3),_.Pc=et(y),_.Pc&&et(y)){for(S=0;4>S;++S)et(y)&&(_.vd[S]=ft(y,6));for(S=0;4>S;++S)et(y)&&(_.od[S]=ft(y,6))}if(e.L=_.Tb==0?0:_.zd?1:2,y.Ka)return Ie(e,3,"cannot parse filter header");var U=m;if(m=L=d,d=L+U,_=U,e.Xb=(1<<nt(e.m,2))-1,U<3*(S=e.Xb))u=7;else{for(L+=3*S,_-=3*S,U=0;U<S;++U){var X=u[m+0]|u[m+1]<<8|u[m+2]<<16;X>_&&(X=_),at(e.Jc[+U],u,L,X),L+=X,_-=X,m+=3}at(e.Jc[+S],u,L,_),u=L<d?0:5}if(u!=0)return Ie(e,u,"cannot parse partitions");for(u=nt(L=e.m,7),m=et(L)?ft(L,4):0,d=et(L)?ft(L,4):0,_=et(L)?ft(L,4):0,S=et(L)?ft(L,4):0,L=et(L)?ft(L,4):0,U=e.Qa,X=0;4>X;++X){if(U.Cb){var K=U.Zb[X];U.Fb||(K+=u)}else{if(0<X){e.pb[X]=e.pb[0];continue}K=u}var V=e.pb[X];V.Sc[0]=ws[mn(K+m,127)],V.Sc[1]=xs[mn(K+0,127)],V.Eb[0]=2*ws[mn(K+d,127)],V.Eb[1]=101581*xs[mn(K+_,127)]>>16,8>V.Eb[1]&&(V.Eb[1]=8),V.Qc[0]=ws[mn(K+S,117)],V.Qc[1]=xs[mn(K+L,127)],V.lc=K+L}if(!w.Rb)return Ie(e,4,"Not a key frame.");for(et(y),w=e.Pa,u=0;4>u;++u){for(m=0;8>m;++m)for(d=0;3>d;++d)for(_=0;11>_;++_)S=it(y,Lu[u][m][d][_])?nt(y,8):_u[u][m][d][_],w.Wc[u][m].Yb[d][_]=S;for(m=0;17>m;++m)w.Xc[u][m]=w.Wc[u][Pu[m]]}return e.kc=et(y),e.kc&&(e.Bd=nt(y,8)),e.cb=1}function qo(e,o,u,d,m,y,w){var _=o[m].Yb[u];for(u=0;16>m;++m){if(!it(e,_[u+0]))return m;for(;!it(e,_[u+1]);)if(_=o[++m].Yb[0],u=0,m==16)return 16;var L=o[m+1].Yb;if(it(e,_[u+2])){var S=e,U=0;if(it(S,(K=_)[(X=u)+3]))if(it(S,K[X+6])){for(_=0,X=2*(U=it(S,K[X+8]))+(K=it(S,K[X+9+U])),U=0,K=wu[X];K[_];++_)U+=U+it(S,K[_]);U+=3+(8<<X)}else it(S,K[X+7])?(U=7+2*it(S,165),U+=it(S,145)):U=5+it(S,159);else U=it(S,K[X+4])?3+it(S,K[X+5]):2;_=L[2]}else U=1,_=L[1];L=w+xu[m],0>(S=e).b&&Q(S);var X,K=S.b,V=(X=S.Ca>>1)-(S.I>>K)>>31;--S.b,S.Ca+=V,S.Ca|=1,S.I-=(X+1&V)<<K,y[L]=((U^V)-V)*d[(0<m)+0]}return 16}function Vr(e){var o=e.rb[e.sb-1];o.la=0,o.Na=0,s(e.zc,0,0,e.zc.length),e.ja=0}function Ya(e,o){if(e==null)return 0;if(o==null)return Ie(e,2,"NULL VP8Io parameter in VP8Decode().");if(!e.cb&&!Ro(e,o))return 0;if(n(e.cb),o.ac==null||o.ac(o)){o.ob&&(e.L=0);var u=ha[e.L];if(e.L==2?(e.yb=0,e.zb=0):(e.yb=o.v-u>>4,e.zb=o.j-u>>4,0>e.yb&&(e.yb=0),0>e.zb&&(e.zb=0)),e.Va=o.o+15+u>>4,e.Hb=o.va+15+u>>4,e.Hb>e.za&&(e.Hb=e.za),e.Va>e.Ub&&(e.Va=e.Ub),0<e.L){var d=e.ed;for(u=0;4>u;++u){var m;if(e.Qa.Cb){var y=e.Qa.Lb[u];e.Qa.Fb||(y+=d.Tb)}else y=d.Tb;for(m=0;1>=m;++m){var w=e.gd[u][m],_=y;if(d.Pc&&(_+=d.vd[0],m&&(_+=d.od[0])),0<(_=0>_?0:63<_?63:_)){var L=_;0<d.wb&&(L=4<d.wb?L>>2:L>>1)>9-d.wb&&(L=9-d.wb),1>L&&(L=1),w.dd=L,w.tc=2*_+L,w.ld=40<=_?2:15<=_?1:0}else w.tc=0;w.La=m}}}u=0}else Ie(e,6,"Frame setup failed"),u=e.a;if(u=u==0){if(u){e.$c=0,0<e.Aa||(e.Ic=Bu);t:{u=e.Ic,d=4*(L=e.za);var S=32*L,U=L+1,X=0<e.L?L*(0<e.Aa?2:1):0,K=(e.Aa==2?2:1)*L;if((w=d+832+(m=3*(16*u+ha[e.L])/2*S)+(y=e.Fa!=null&&0<e.Fa.length?e.Kc.c*e.Kc.i:0))!=w)u=0;else{if(w>e.Vb){if(e.Vb=0,e.Ec=a(w),e.Fc=0,e.Ec==null){u=Ie(e,1,"no memory during frame initialization.");break t}e.Vb=w}w=e.Ec,_=e.Fc,e.Ac=w,e.Bc=_,_+=d,e.Gd=c(S,Do),e.Hd=0,e.rb=c(U+1,Eo),e.sb=1,e.wa=X?c(X,mr):null,e.Y=0,e.D.Nb=0,e.D.wa=e.wa,e.D.Y=e.Y,0<e.Aa&&(e.D.Y+=L),n(!0),e.oc=w,e.pc=_,_+=832,e.ya=c(K,Gr),e.aa=0,e.D.ya=e.ya,e.D.aa=e.aa,e.Aa==2&&(e.D.aa+=L),e.R=16*L,e.B=8*L,L=(S=ha[e.L])*e.R,S=S/2*e.B,e.sa=w,e.ta=_+L,e.qa=e.sa,e.ra=e.ta+16*u*e.R+S,e.Ha=e.qa,e.Ia=e.ra+8*u*e.B+S,e.$c=0,_+=m,e.mb=y?w:null,e.nb=y?_:null,n(_+y<=e.Fc+e.Vb),Vr(e),s(e.Ac,e.Bc,0,d),u=1}}if(u){if(o.ka=0,o.y=e.sa,o.O=e.ta,o.f=e.qa,o.N=e.ra,o.ea=e.Ha,o.Vd=e.Ia,o.fa=e.R,o.Rc=e.B,o.F=null,o.J=0,!hl){for(u=-255;255>=u;++u)sl[255+u]=0>u?-u:u;for(u=-1020;1020>=u;++u)ll[1020+u]=-128>u?-128:127<u?127:u;for(u=-112;112>=u;++u)cl[112+u]=-16>u?-16:15<u?15:u;for(u=-255;510>=u;++u)ul[255+u]=0>u?0:255<u?255:u;hl=1}Dn=Ka,Fr=Ja,Si=Uo,Bn=Xa,di=Wo,fo=zo,Qi=Zr,Ir=Ki,tr=us,Ni=Qr,po=cs,_n=Cr,pi=to,Ce=Zo,Le=Ko,je=ui,We=Ci,gi=ls,Rn[0]=Qn,Rn[1]=Za,Rn[2]=ns,Rn[3]=is,Rn[4]=Vo,Rn[5]=xr,Rn[6]=$o,Rn[7]=Jr,Rn[8]=os,Rn[9]=rs,Mi[0]=Ho,Mi[1]=ts,Mi[2]=ci,Mi[3]=yr,Mi[4]=Ze,Mi[5]=es,Mi[6]=Go,mi[0]=_i,mi[1]=Qa,mi[2]=as,mi[3]=Xr,mi[4]=Xi,mi[5]=ss,mi[6]=Kr,u=1}else u=0}u&&(u=function(V,yt){for(V.M=0;V.M<V.Va;++V.M){var ot,W=V.Jc[V.M&V.Xb],G=V.m,mt=V;for(ot=0;ot<mt.za;++ot){var wt=G,bt=mt,Et=bt.Ac,St=bt.Bc+4*ot,Ut=bt.zc,Nt=bt.ya[bt.aa+ot];if(bt.Qa.Bb?Nt.$b=it(wt,bt.Pa.jb[0])?2+it(wt,bt.Pa.jb[2]):it(wt,bt.Pa.jb[1]):Nt.$b=0,bt.kc&&(Nt.Ad=it(wt,bt.Bd)),Nt.Za=!it(wt,145)+0,Nt.Za){var he=Nt.Ob,ge=0;for(bt=0;4>bt;++bt){var ce,ae=Ut[0+bt];for(ce=0;4>ce;++ce){ae=Cu[Et[St+ce]][ae];for(var xe=gl[it(wt,ae[0])];0<xe;)xe=gl[2*xe+it(wt,ae[xe])];ae=-xe,Et[St+ce]=ae}i(he,ge,Et,St,4),ge+=4,Ut[0+bt]=ae}}else ae=it(wt,156)?it(wt,128)?1:3:it(wt,163)?2:0,Nt.Ob[0]=ae,s(Et,St,ae,4),s(Ut,0,ae,4);Nt.Dd=it(wt,142)?it(wt,114)?it(wt,183)?1:3:2:0}if(mt.m.Ka)return Ie(V,7,"Premature end-of-partition0 encountered.");for(;V.ja<V.za;++V.ja){if(mt=W,wt=(G=V).rb[G.sb-1],Et=G.rb[G.sb+G.ja],ot=G.ya[G.aa+G.ja],St=G.kc?ot.Ad:0)wt.la=Et.la=0,ot.Za||(wt.Na=Et.Na=0),ot.Hc=0,ot.Gc=0,ot.ia=0;else{var be,ee;if(wt=Et,Et=mt,St=G.Pa.Xc,Ut=G.ya[G.aa+G.ja],Nt=G.pb[Ut.$b],bt=Ut.ad,he=0,ge=G.rb[G.sb-1],ae=ce=0,s(bt,he,0,384),Ut.Za)var qe=0,fn=St[3];else{xe=a(16);var ze=wt.Na+ge.Na;if(ze=go(Et,St[1],ze,Nt.Eb,0,xe,0),wt.Na=ge.Na=(0<ze)+0,1<ze)Dn(xe,0,bt,he);else{var dn=xe[0]+3>>3;for(xe=0;256>xe;xe+=16)bt[he+xe]=dn}qe=1,fn=St[0]}var me=15&wt.la,bn=15&ge.la;for(xe=0;4>xe;++xe){var yn=1&bn;for(dn=ee=0;4>dn;++dn)me=me>>1|(yn=(ze=go(Et,fn,ze=yn+(1&me),Nt.Sc,qe,bt,he))>qe)<<7,ee=ee<<2|(3<ze?3:1<ze?2:bt[he+0]!=0),he+=16;me>>=4,bn=bn>>1|yn<<7,ce=(ce<<8|ee)>>>0}for(fn=me,qe=bn>>4,be=0;4>be;be+=2){for(ee=0,me=wt.la>>4+be,bn=ge.la>>4+be,xe=0;2>xe;++xe){for(yn=1&bn,dn=0;2>dn;++dn)ze=yn+(1&me),me=me>>1|(yn=0<(ze=go(Et,St[2],ze,Nt.Qc,0,bt,he)))<<3,ee=ee<<2|(3<ze?3:1<ze?2:bt[he+0]!=0),he+=16;me>>=2,bn=bn>>1|yn<<5}ae|=ee<<4*be,fn|=me<<4<<be,qe|=(240&bn)<<be}wt.la=fn,ge.la=qe,Ut.Hc=ce,Ut.Gc=ae,Ut.ia=43690&ae?0:Nt.ia,St=!(ce|ae)}if(0<G.L&&(G.wa[G.Y+G.ja]=G.gd[ot.$b][ot.Za],G.wa[G.Y+G.ja].La|=!St),mt.Ka)return Ie(V,7,"Premature end-of-file encountered.")}if(Vr(V),G=yt,mt=1,ot=(W=V).D,wt=0<W.L&&W.M>=W.zb&&W.M<=W.Va,W.Aa==0)t:{if(ot.M=W.M,ot.uc=wt,ao(W,ot),mt=1,ot=(ee=W.D).Nb,wt=(ae=ha[W.L])*W.R,Et=ae/2*W.B,xe=16*ot*W.R,dn=8*ot*W.B,St=W.sa,Ut=W.ta-wt+xe,Nt=W.qa,bt=W.ra-Et+dn,he=W.Ha,ge=W.Ia-Et+dn,bn=(me=ee.M)==0,ce=me>=W.Va-1,W.Aa==2&&ao(W,ee),ee.uc)for(yn=(ze=W).D.M,n(ze.D.uc),ee=ze.yb;ee<ze.Hb;++ee){qe=ee,fn=yn;var $e=(Xe=(Me=ze).D).Nb;be=Me.R;var Xe=Xe.wa[Xe.Y+qe],on=Me.sa,Pn=Me.ta+16*$e*be+16*qe,ei=Xe.dd,Ye=Xe.tc;if(Ye!=0)if(n(3<=Ye),Me.L==1)0<qe&&je(on,Pn,be,Ye+4),Xe.La&&gi(on,Pn,be,Ye),0<fn&&Le(on,Pn,be,Ye+4),Xe.La&&We(on,Pn,be,Ye);else{var vi=Me.B,er=Me.qa,Tr=Me.ra+8*$e*vi+8*qe,Ii=Me.Ha,Me=Me.Ia+8*$e*vi+8*qe;$e=Xe.ld,0<qe&&(Ir(on,Pn,be,Ye+4,ei,$e),Ni(er,Tr,Ii,Me,vi,Ye+4,ei,$e)),Xe.La&&(_n(on,Pn,be,Ye,ei,$e),Ce(er,Tr,Ii,Me,vi,Ye,ei,$e)),0<fn&&(Qi(on,Pn,be,Ye+4,ei,$e),tr(er,Tr,Ii,Me,vi,Ye+4,ei,$e)),Xe.La&&(po(on,Pn,be,Ye,ei,$e),pi(er,Tr,Ii,Me,vi,Ye,ei,$e))}}if(W.ia&&alert("todo:DitherRow"),G.put!=null){if(ee=16*me,me=16*(me+1),bn?(G.y=W.sa,G.O=W.ta+xe,G.f=W.qa,G.N=W.ra+dn,G.ea=W.Ha,G.W=W.Ia+dn):(ee-=ae,G.y=St,G.O=Ut,G.f=Nt,G.N=bt,G.ea=he,G.W=ge),ce||(me-=ae),me>G.o&&(me=G.o),G.F=null,G.J=null,W.Fa!=null&&0<W.Fa.length&&ee<me&&(G.J=ro(W,G,ee,me-ee),G.F=W.mb,G.F==null&&G.F.length==0)){mt=Ie(W,3,"Could not decode alpha data.");break t}ee<G.j&&(ae=G.j-ee,ee=G.j,n(!(1&ae)),G.O+=W.R*ae,G.N+=W.B*(ae>>1),G.W+=W.B*(ae>>1),G.F!=null&&(G.J+=G.width*ae)),ee<me&&(G.O+=G.v,G.N+=G.v>>1,G.W+=G.v>>1,G.F!=null&&(G.J+=G.v),G.ka=ee-G.j,G.U=G.va-G.v,G.T=me-ee,mt=G.put(G))}ot+1!=W.Ic||ce||(i(W.sa,W.ta-wt,St,Ut+16*W.R,wt),i(W.qa,W.ra-Et,Nt,bt+8*W.B,Et),i(W.Ha,W.Ia-Et,he,ge+8*W.B,Et))}if(!mt)return Ie(V,6,"Output aborted.")}return 1}(e,o)),o.bc!=null&&o.bc(o),u&=1}return u?(e.cb=0,u):0}function Fn(e,o,u,d,m){m=e[o+u+32*d]+(m>>3),e[o+u+32*d]=-256&m?0>m?0:255:m}function br(e,o,u,d,m,y){Fn(e,o,0,u,d+m),Fn(e,o,1,u,d+y),Fn(e,o,2,u,d-y),Fn(e,o,3,u,d-m)}function cn(e){return(20091*e>>16)+e}function $r(e,o,u,d){var m,y=0,w=a(16);for(m=0;4>m;++m){var _=e[o+0]+e[o+8],L=e[o+0]-e[o+8],S=(35468*e[o+4]>>16)-cn(e[o+12]),U=cn(e[o+4])+(35468*e[o+12]>>16);w[y+0]=_+U,w[y+1]=L+S,w[y+2]=L-S,w[y+3]=_-U,y+=4,o++}for(m=y=0;4>m;++m)_=(e=w[y+0]+4)+w[y+8],L=e-w[y+8],S=(35468*w[y+4]>>16)-cn(w[y+12]),Fn(u,d,0,0,_+(U=cn(w[y+4])+(35468*w[y+12]>>16))),Fn(u,d,1,0,L+S),Fn(u,d,2,0,L-S),Fn(u,d,3,0,_-U),y++,d+=32}function zo(e,o,u,d){var m=e[o+0]+4,y=35468*e[o+4]>>16,w=cn(e[o+4]),_=35468*e[o+1]>>16;br(u,d,0,m+w,e=cn(e[o+1]),_),br(u,d,1,m+y,e,_),br(u,d,2,m-y,e,_),br(u,d,3,m-w,e,_)}function Ja(e,o,u,d,m){$r(e,o,u,d),m&&$r(e,o+16,u,d+4)}function Uo(e,o,u,d){Fr(e,o+0,u,d,1),Fr(e,o+32,u,d+128,1)}function Xa(e,o,u,d){var m;for(e=e[o+0]+4,m=0;4>m;++m)for(o=0;4>o;++o)Fn(u,d,o,m,e)}function Wo(e,o,u,d){e[o+0]&&Bn(e,o+0,u,d),e[o+16]&&Bn(e,o+16,u,d+4),e[o+32]&&Bn(e,o+32,u,d+128),e[o+48]&&Bn(e,o+48,u,d+128+4)}function Ka(e,o,u,d){var m,y=a(16);for(m=0;4>m;++m){var w=e[o+0+m]+e[o+12+m],_=e[o+4+m]+e[o+8+m],L=e[o+4+m]-e[o+8+m],S=e[o+0+m]-e[o+12+m];y[0+m]=w+_,y[8+m]=w-_,y[4+m]=S+L,y[12+m]=S-L}for(m=0;4>m;++m)w=(e=y[0+4*m]+3)+y[3+4*m],_=y[1+4*m]+y[2+4*m],L=y[1+4*m]-y[2+4*m],S=e-y[3+4*m],u[d+0]=w+_>>3,u[d+16]=S+L>>3,u[d+32]=w-_>>3,u[d+48]=S-L>>3,d+=64}function Yr(e,o,u){var d,m=o-32,y=vn,w=255-e[m-1];for(d=0;d<u;++d){var _,L=y,S=w+e[o-1];for(_=0;_<u;++_)e[o+_]=L[S+e[m+_]];o+=32}}function Za(e,o){Yr(e,o,4)}function Qa(e,o){Yr(e,o,8)}function ts(e,o){Yr(e,o,16)}function ci(e,o){var u;for(u=0;16>u;++u)i(e,o+32*u,e,o-32,16)}function yr(e,o){var u;for(u=16;0<u;--u)s(e,o,e[o-1],16),o+=32}function wr(e,o,u){var d;for(d=0;16>d;++d)s(o,u+32*d,e,16)}function Ho(e,o){var u,d=16;for(u=0;16>u;++u)d+=e[o-1+32*u]+e[o+u-32];wr(d>>5,e,o)}function Ze(e,o){var u,d=8;for(u=0;16>u;++u)d+=e[o-1+32*u];wr(d>>4,e,o)}function es(e,o){var u,d=8;for(u=0;16>u;++u)d+=e[o+u-32];wr(d>>4,e,o)}function Go(e,o){wr(128,e,o)}function Yt(e,o,u){return e+2*o+u+2>>2}function ns(e,o){var u,d=o-32;for(d=new Uint8Array([Yt(e[d-1],e[d+0],e[d+1]),Yt(e[d+0],e[d+1],e[d+2]),Yt(e[d+1],e[d+2],e[d+3]),Yt(e[d+2],e[d+3],e[d+4])]),u=0;4>u;++u)i(e,o+32*u,d,0,d.length)}function is(e,o){var u=e[o-1],d=e[o-1+32],m=e[o-1+64],y=e[o-1+96];kt(e,o+0,16843009*Yt(e[o-1-32],u,d)),kt(e,o+32,16843009*Yt(u,d,m)),kt(e,o+64,16843009*Yt(d,m,y)),kt(e,o+96,16843009*Yt(m,y,y))}function Qn(e,o){var u,d=4;for(u=0;4>u;++u)d+=e[o+u-32]+e[o-1+32*u];for(d>>=3,u=0;4>u;++u)s(e,o+32*u,d,4)}function Vo(e,o){var u=e[o-1+0],d=e[o-1+32],m=e[o-1+64],y=e[o-1-32],w=e[o+0-32],_=e[o+1-32],L=e[o+2-32],S=e[o+3-32];e[o+0+96]=Yt(d,m,e[o-1+96]),e[o+1+96]=e[o+0+64]=Yt(u,d,m),e[o+2+96]=e[o+1+64]=e[o+0+32]=Yt(y,u,d),e[o+3+96]=e[o+2+64]=e[o+1+32]=e[o+0+0]=Yt(w,y,u),e[o+3+64]=e[o+2+32]=e[o+1+0]=Yt(_,w,y),e[o+3+32]=e[o+2+0]=Yt(L,_,w),e[o+3+0]=Yt(S,L,_)}function $o(e,o){var u=e[o+1-32],d=e[o+2-32],m=e[o+3-32],y=e[o+4-32],w=e[o+5-32],_=e[o+6-32],L=e[o+7-32];e[o+0+0]=Yt(e[o+0-32],u,d),e[o+1+0]=e[o+0+32]=Yt(u,d,m),e[o+2+0]=e[o+1+32]=e[o+0+64]=Yt(d,m,y),e[o+3+0]=e[o+2+32]=e[o+1+64]=e[o+0+96]=Yt(m,y,w),e[o+3+32]=e[o+2+64]=e[o+1+96]=Yt(y,w,_),e[o+3+64]=e[o+2+96]=Yt(w,_,L),e[o+3+96]=Yt(_,L,L)}function xr(e,o){var u=e[o-1+0],d=e[o-1+32],m=e[o-1+64],y=e[o-1-32],w=e[o+0-32],_=e[o+1-32],L=e[o+2-32],S=e[o+3-32];e[o+0+0]=e[o+1+64]=y+w+1>>1,e[o+1+0]=e[o+2+64]=w+_+1>>1,e[o+2+0]=e[o+3+64]=_+L+1>>1,e[o+3+0]=L+S+1>>1,e[o+0+96]=Yt(m,d,u),e[o+0+64]=Yt(d,u,y),e[o+0+32]=e[o+1+96]=Yt(u,y,w),e[o+1+32]=e[o+2+96]=Yt(y,w,_),e[o+2+32]=e[o+3+96]=Yt(w,_,L),e[o+3+32]=Yt(_,L,S)}function Jr(e,o){var u=e[o+0-32],d=e[o+1-32],m=e[o+2-32],y=e[o+3-32],w=e[o+4-32],_=e[o+5-32],L=e[o+6-32],S=e[o+7-32];e[o+0+0]=u+d+1>>1,e[o+1+0]=e[o+0+64]=d+m+1>>1,e[o+2+0]=e[o+1+64]=m+y+1>>1,e[o+3+0]=e[o+2+64]=y+w+1>>1,e[o+0+32]=Yt(u,d,m),e[o+1+32]=e[o+0+96]=Yt(d,m,y),e[o+2+32]=e[o+1+96]=Yt(m,y,w),e[o+3+32]=e[o+2+96]=Yt(y,w,_),e[o+3+64]=Yt(w,_,L),e[o+3+96]=Yt(_,L,S)}function rs(e,o){var u=e[o-1+0],d=e[o-1+32],m=e[o-1+64],y=e[o-1+96];e[o+0+0]=u+d+1>>1,e[o+2+0]=e[o+0+32]=d+m+1>>1,e[o+2+32]=e[o+0+64]=m+y+1>>1,e[o+1+0]=Yt(u,d,m),e[o+3+0]=e[o+1+32]=Yt(d,m,y),e[o+3+32]=e[o+1+64]=Yt(m,y,y),e[o+3+64]=e[o+2+64]=e[o+0+96]=e[o+1+96]=e[o+2+96]=e[o+3+96]=y}function os(e,o){var u=e[o-1+0],d=e[o-1+32],m=e[o-1+64],y=e[o-1+96],w=e[o-1-32],_=e[o+0-32],L=e[o+1-32],S=e[o+2-32];e[o+0+0]=e[o+2+32]=u+w+1>>1,e[o+0+32]=e[o+2+64]=d+u+1>>1,e[o+0+64]=e[o+2+96]=m+d+1>>1,e[o+0+96]=y+m+1>>1,e[o+3+0]=Yt(_,L,S),e[o+2+0]=Yt(w,_,L),e[o+1+0]=e[o+3+32]=Yt(u,w,_),e[o+1+32]=e[o+3+64]=Yt(d,u,w),e[o+1+64]=e[o+3+96]=Yt(m,d,u),e[o+1+96]=Yt(y,m,d)}function as(e,o){var u;for(u=0;8>u;++u)i(e,o+32*u,e,o-32,8)}function Xr(e,o){var u;for(u=0;8>u;++u)s(e,o,e[o-1],8),o+=32}function Ji(e,o,u){var d;for(d=0;8>d;++d)s(o,u+32*d,e,8)}function _i(e,o){var u,d=8;for(u=0;8>u;++u)d+=e[o+u-32]+e[o-1+32*u];Ji(d>>4,e,o)}function ss(e,o){var u,d=4;for(u=0;8>u;++u)d+=e[o+u-32];Ji(d>>3,e,o)}function Xi(e,o){var u,d=4;for(u=0;8>u;++u)d+=e[o-1+32*u];Ji(d>>3,e,o)}function Kr(e,o){Ji(128,e,o)}function _r(e,o,u){var d=e[o-u],m=e[o+0],y=3*(m-d)+gs[1020+e[o-2*u]-e[o+u]],w=ia[112+(y+4>>3)];e[o-u]=vn[255+d+ia[112+(y+3>>3)]],e[o+0]=vn[255+m-w]}function Yo(e,o,u,d){var m=e[o+0],y=e[o+u];return Cn[255+e[o-2*u]-e[o-u]]>d||Cn[255+y-m]>d}function Jo(e,o,u,d){return 4*Cn[255+e[o-u]-e[o+0]]+Cn[255+e[o-2*u]-e[o+u]]<=d}function Xo(e,o,u,d,m){var y=e[o-3*u],w=e[o-2*u],_=e[o-u],L=e[o+0],S=e[o+u],U=e[o+2*u],X=e[o+3*u];return 4*Cn[255+_-L]+Cn[255+w-S]>d?0:Cn[255+e[o-4*u]-y]<=m&&Cn[255+y-w]<=m&&Cn[255+w-_]<=m&&Cn[255+X-U]<=m&&Cn[255+U-S]<=m&&Cn[255+S-L]<=m}function Ko(e,o,u,d){var m=2*d+1;for(d=0;16>d;++d)Jo(e,o+d,u,m)&&_r(e,o+d,u)}function ui(e,o,u,d){var m=2*d+1;for(d=0;16>d;++d)Jo(e,o+d*u,1,m)&&_r(e,o+d*u,1)}function Ci(e,o,u,d){var m;for(m=3;0<m;--m)Ko(e,o+=4*u,u,d)}function ls(e,o,u,d){var m;for(m=3;0<m;--m)ui(e,o+=4,u,d)}function Li(e,o,u,d,m,y,w,_){for(y=2*y+1;0<m--;){if(Xo(e,o,u,y,w))if(Yo(e,o,u,_))_r(e,o,u);else{var L=e,S=o,U=u,X=L[S-2*U],K=L[S-U],V=L[S+0],yt=L[S+U],ot=L[S+2*U],W=27*(mt=gs[1020+3*(V-K)+gs[1020+X-yt]])+63>>7,G=18*mt+63>>7,mt=9*mt+63>>7;L[S-3*U]=vn[255+L[S-3*U]+mt],L[S-2*U]=vn[255+X+G],L[S-U]=vn[255+K+W],L[S+0]=vn[255+V-W],L[S+U]=vn[255+yt-G],L[S+2*U]=vn[255+ot-mt]}o+=d}}function In(e,o,u,d,m,y,w,_){for(y=2*y+1;0<m--;){if(Xo(e,o,u,y,w))if(Yo(e,o,u,_))_r(e,o,u);else{var L=e,S=o,U=u,X=L[S-U],K=L[S+0],V=L[S+U],yt=ia[112+((ot=3*(K-X))+4>>3)],ot=ia[112+(ot+3>>3)],W=yt+1>>1;L[S-2*U]=vn[255+L[S-2*U]+W],L[S-U]=vn[255+X+ot],L[S+0]=vn[255+K-yt],L[S+U]=vn[255+V-W]}o+=d}}function Zr(e,o,u,d,m,y){Li(e,o,u,1,16,d,m,y)}function Ki(e,o,u,d,m,y){Li(e,o,1,u,16,d,m,y)}function cs(e,o,u,d,m,y){var w;for(w=3;0<w;--w)In(e,o+=4*u,u,1,16,d,m,y)}function Cr(e,o,u,d,m,y){var w;for(w=3;0<w;--w)In(e,o+=4,1,u,16,d,m,y)}function us(e,o,u,d,m,y,w,_){Li(e,o,m,1,8,y,w,_),Li(u,d,m,1,8,y,w,_)}function Qr(e,o,u,d,m,y,w,_){Li(e,o,1,m,8,y,w,_),Li(u,d,1,m,8,y,w,_)}function to(e,o,u,d,m,y,w,_){In(e,o+4*m,m,1,8,y,w,_),In(u,d+4*m,m,1,8,y,w,_)}function Zo(e,o,u,d,m,y,w,_){In(e,o+4,1,m,8,y,w,_),In(u,d+4,1,m,8,y,w,_)}function Lr(){this.ba=new An,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new Ne,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function eo(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function no(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function Qo(){this.ua=0,this.Wa=new E,this.vb=new E,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new B,this.yc=new A}function hs(){this.xb=this.a=0,this.l=new vr,this.ca=new An,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new x,this.Pb=0,this.wd=new x,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new Qo,this.ab=0,this.gc=c(4,no),this.Oc=0}function Pr(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new vr,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function Zi(e,o,u,d,m,y,w){for(e=e==null?0:e[o+0],o=0;o<w;++o)m[y+o]=e+u[d+o]&255,e=m[y+o]}function io(e,o,u,d,m,y,w){var _;if(e==null)Zi(null,null,u,d,m,y,w);else for(_=0;_<w;++_)m[y+_]=e[o+_]+u[d+_]&255}function Pi(e,o,u,d,m,y,w){if(e==null)Zi(null,null,u,d,m,y,w);else{var _,L=e[o+0],S=L,U=L;for(_=0;_<w;++_)S=U+(L=e[o+_])-S,U=u[d+_]+(-256&S?0>S?0:255:S)&255,S=L,m[y+_]=U}}function ro(e,o,u,d){var m=o.width,y=o.o;if(n(e!=null&&o!=null),0>u||0>=d||u+d>y)return null;if(!e.Cc){if(e.ga==null){var w;if(e.ga=new Pr,(w=e.ga==null)||(w=o.width*o.o,n(e.Gb.length==0),e.Gb=a(w),e.Uc=0,e.Gb==null?w=0:(e.mb=e.Gb,e.nb=e.Uc,e.rc=null,w=1),w=!w),!w){w=e.ga;var _=e.Fa,L=e.P,S=e.qc,U=e.mb,X=e.nb,K=L+1,V=S-1,yt=w.l;if(n(_!=null&&U!=null&&o!=null),Fi[0]=null,Fi[1]=Zi,Fi[2]=io,Fi[3]=Pi,w.ca=U,w.tb=X,w.c=o.width,w.i=o.height,n(0<w.c&&0<w.i),1>=S)o=0;else if(w.$a=_[L+0]>>0&3,w.Z=_[L+0]>>2&3,w.Lc=_[L+0]>>4&3,L=_[L+0]>>6&3,0>w.$a||1<w.$a||4<=w.Z||1<w.Lc||L)o=0;else if(yt.put=Yn,yt.ac=Je,yt.bc=Jn,yt.ma=w,yt.width=o.width,yt.height=o.height,yt.Da=o.Da,yt.v=o.v,yt.va=o.va,yt.j=o.j,yt.o=o.o,w.$a)t:{n(w.$a==1),o=wn();e:for(;;){if(o==null){o=0;break t}if(n(w!=null),w.mc=o,o.c=w.c,o.i=w.i,o.l=w.l,o.l.ma=w,o.l.width=w.c,o.l.height=w.i,o.a=0,Z(o.m,_,K,V),!Zn(w.c,w.i,1,o,null)||(o.ab==1&&o.gc[0].hc==3&&li(o.s)?(w.ic=1,_=o.c*o.i,o.Ta=null,o.Ua=0,o.V=a(_),o.Ba=0,o.V==null?(o.a=1,o=0):o=1):(w.ic=0,o=Yi(o,w.c)),!o))break e;o=1;break t}w.mc=null,o=0}else o=V>=w.c*w.i;w=!o}if(w)return null;e.ga.Lc!=1?e.Ga=0:d=y-u}n(e.ga!=null),n(u+d<=y);t:{if(o=(_=e.ga).c,y=_.l.o,_.$a==0){if(K=e.rc,V=e.Vc,yt=e.Fa,L=e.P+1+u*o,S=e.mb,U=e.nb+u*o,n(L<=e.P+e.qc),_.Z!=0)for(n(Fi[_.Z]!=null),w=0;w<d;++w)Fi[_.Z](K,V,yt,L,S,U,o),K=S,V=U,U+=o,L+=o;else for(w=0;w<d;++w)i(S,U,yt,L,o),K=S,V=U,U+=o,L+=o;e.rc=K,e.Vc=V}else{if(n(_.mc!=null),o=u+d,n((w=_.mc)!=null),n(o<=w.i),w.C>=o)o=1;else if(_.ic||Y(),_.ic){_=w.V,K=w.Ba,V=w.c;var ot=w.i,W=(yt=1,L=w.$/V,S=w.$%V,U=w.m,X=w.s,w.$),G=V*ot,mt=V*o,wt=X.wc,bt=W<mt?Be(X,S,L):null;n(W<=G),n(o<=ot),n(li(X));e:for(;;){for(;!U.h&&W<mt;){if(S&wt||(bt=Be(X,S,L)),n(bt!=null),$(U),256>(ot=ln(bt.G[0],bt.H[0],U)))_[K+W]=ot,++W,++S>=V&&(S=0,++L<=o&&!(L%16)&&On(w,L));else{if(!(280>ot)){yt=0;break e}ot=Sn(ot-256,U);var Et,St=ln(bt.G[4],bt.H[4],U);if($(U),!(W>=(St=Xn(V,St=Sn(St,U)))&&G-W>=ot)){yt=0;break e}for(Et=0;Et<ot;++Et)_[K+W+Et]=_[K+W+Et-St];for(W+=ot,S+=ot;S>=V;)S-=V,++L<=o&&!(L%16)&&On(w,L);W<mt&&S&wt&&(bt=Be(X,S,L))}n(U.h==T(U))}On(w,L>o?o:L);break e}!yt||U.h&&W<G?(yt=0,w.a=U.h?5:3):w.$=W,o=yt}else o=Mn(w,w.V,w.Ba,w.c,w.i,o,fr);if(!o){d=0;break t}}u+d>=y&&(e.Cc=1),d=1}if(!d)return null;if(e.Cc&&((d=e.ga)!=null&&(d.mc=null),e.ga=null,0<e.Ga))return alert("todo:WebPDequantizeLevels"),null}return e.nb+u*m}function l(e,o,u,d,m,y){for(;0<m--;){var w,_=e,L=o+(u?1:0),S=e,U=o+(u?0:3);for(w=0;w<d;++w){var X=S[U+4*w];X!=255&&(X*=32897,_[L+4*w+0]=_[L+4*w+0]*X>>23,_[L+4*w+1]=_[L+4*w+1]*X>>23,_[L+4*w+2]=_[L+4*w+2]*X>>23)}o+=y}}function v(e,o,u,d,m){for(;0<d--;){var y;for(y=0;y<u;++y){var w=e[o+2*y+0],_=15&(S=e[o+2*y+1]),L=4369*_,S=(240&S|S>>4)*L>>16;e[o+2*y+0]=(240&w|w>>4)*L>>16&240|(15&w|w<<4)*L>>16>>4&15,e[o+2*y+1]=240&S|_}o+=m}}function M(e,o,u,d,m,y,w,_){var L,S,U=255;for(S=0;S<m;++S){for(L=0;L<d;++L){var X=e[o+L];y[w+4*L]=X,U&=X}o+=u,w+=_}return U!=255}function q(e,o,u,d,m){var y;for(y=0;y<m;++y)u[d+y]=e[o+y]>>8}function Y(){Oi=l,jr=v,na=M,al=q}function ct(e,o,u){R[e]=function(d,m,y,w,_,L,S,U,X,K,V,yt,ot,W,G,mt,wt){var bt,Et=wt-1>>1,St=_[L+0]|S[U+0]<<16,Ut=X[K+0]|V[yt+0]<<16;n(d!=null);var Nt=3*St+Ut+131074>>2;for(o(d[m+0],255&Nt,Nt>>16,ot,W),y!=null&&(Nt=3*Ut+St+131074>>2,o(y[w+0],255&Nt,Nt>>16,G,mt)),bt=1;bt<=Et;++bt){var he=_[L+bt]|S[U+bt]<<16,ge=X[K+bt]|V[yt+bt]<<16,ce=St+he+Ut+ge+524296,ae=ce+2*(he+Ut)>>3;Nt=ae+St>>1,St=(ce=ce+2*(St+ge)>>3)+he>>1,o(d[m+2*bt-1],255&Nt,Nt>>16,ot,W+(2*bt-1)*u),o(d[m+2*bt-0],255&St,St>>16,ot,W+(2*bt-0)*u),y!=null&&(Nt=ce+Ut>>1,St=ae+ge>>1,o(y[w+2*bt-1],255&Nt,Nt>>16,G,mt+(2*bt-1)*u),o(y[w+2*bt+0],255&St,St>>16,G,mt+(2*bt+0)*u)),St=he,Ut=ge}1&wt||(Nt=3*St+Ut+131074>>2,o(d[m+wt-1],255&Nt,Nt>>16,ot,W+(wt-1)*u),y!=null&&(Nt=3*Ut+St+131074>>2,o(y[w+wt-1],255&Nt,Nt>>16,G,mt+(wt-1)*u)))}}function Ct(){Ln[ra]=Au,Ln[oa]=vl,Ln[fl]=Su,Ln[aa]=bl,Ln[sa]=yl,Ln[ms]=wl,Ln[dl]=Nu,Ln[vs]=vl,Ln[bs]=bl,Ln[la]=yl,Ln[ys]=wl}function Tt(e){return e&~Ou?0>e?0:255:e>>xl}function zt(e,o){return Tt((19077*e>>8)+(26149*o>>8)-14234)}function te(e,o,u){return Tt((19077*e>>8)-(6419*o>>8)-(13320*u>>8)+8708)}function ie(e,o){return Tt((19077*e>>8)+(33050*o>>8)-17685)}function se(e,o,u,d,m){d[m+0]=zt(e,u),d[m+1]=te(e,o,u),d[m+2]=ie(e,o)}function ke(e,o,u,d,m){d[m+0]=ie(e,o),d[m+1]=te(e,o,u),d[m+2]=zt(e,u)}function Oe(e,o,u,d,m){var y=te(e,o,u);o=y<<3&224|ie(e,o)>>3,d[m+0]=248&zt(e,u)|y>>5,d[m+1]=o}function Re(e,o,u,d,m){var y=240&ie(e,o)|15;d[m+0]=240&zt(e,u)|te(e,o,u)>>4,d[m+1]=y}function Qe(e,o,u,d,m){d[m+0]=255,se(e,o,u,d,m+1)}function Ve(e,o,u,d,m){ke(e,o,u,d,m),d[m+3]=255}function jn(e,o,u,d,m){se(e,o,u,d,m),d[m+3]=255}function mn(e,o){return 0>e?0:e>o?o:e}function hi(e,o,u){R[e]=function(d,m,y,w,_,L,S,U,X){for(var K=U+(-2&X)*u;U!=K;)o(d[m+0],y[w+0],_[L+0],S,U),o(d[m+1],y[w+0],_[L+0],S,U+u),m+=2,++w,++L,U+=2*u;1&X&&o(d[m+0],y[w+0],_[L+0],S,U)}}function ta(e,o,u){return u==0?e==0?o==0?6:5:o==0?4:0:u}function oo(e,o,u,d,m){switch(e>>>30){case 3:Fr(o,u,d,m,0);break;case 2:fo(o,u,d,m);break;case 1:Bn(o,u,d,m)}}function ao(e,o){var u,d,m=o.M,y=o.Nb,w=e.oc,_=e.pc+40,L=e.oc,S=e.pc+584,U=e.oc,X=e.pc+600;for(u=0;16>u;++u)w[_+32*u-1]=129;for(u=0;8>u;++u)L[S+32*u-1]=129,U[X+32*u-1]=129;for(0<m?w[_-1-32]=L[S-1-32]=U[X-1-32]=129:(s(w,_-32-1,127,21),s(L,S-32-1,127,9),s(U,X-32-1,127,9)),d=0;d<e.za;++d){var K=o.ya[o.aa+d];if(0<d){for(u=-1;16>u;++u)i(w,_+32*u-4,w,_+32*u+12,4);for(u=-1;8>u;++u)i(L,S+32*u-4,L,S+32*u+4,4),i(U,X+32*u-4,U,X+32*u+4,4)}var V=e.Gd,yt=e.Hd+d,ot=K.ad,W=K.Hc;if(0<m&&(i(w,_-32,V[yt].y,0,16),i(L,S-32,V[yt].f,0,8),i(U,X-32,V[yt].ea,0,8)),K.Za){var G=w,mt=_-32+16;for(0<m&&(d>=e.za-1?s(G,mt,V[yt].y[15],4):i(G,mt,V[yt+1].y,0,4)),u=0;4>u;u++)G[mt+128+u]=G[mt+256+u]=G[mt+384+u]=G[mt+0+u];for(u=0;16>u;++u,W<<=2)G=w,mt=_+Cl[u],Rn[K.Ob[u]](G,mt),oo(W,ot,16*+u,G,mt)}else if(G=ta(d,m,K.Ob[0]),Mi[G](w,_),W!=0)for(u=0;16>u;++u,W<<=2)oo(W,ot,16*+u,w,_+Cl[u]);for(u=K.Gc,G=ta(d,m,K.Dd),mi[G](L,S),mi[G](U,X),W=ot,G=L,mt=S,255&(K=u>>0)&&(170&K?Si(W,256,G,mt):di(W,256,G,mt)),K=U,W=X,255&(u>>=8)&&(170&u?Si(ot,320,K,W):di(ot,320,K,W)),m<e.Ub-1&&(i(V[yt].y,0,w,_+480,16),i(V[yt].f,0,L,S+224,8),i(V[yt].ea,0,U,X+224,8)),u=8*y*e.B,V=e.sa,yt=e.ta+16*d+16*y*e.R,ot=e.qa,K=e.ra+8*d+u,W=e.Ha,G=e.Ia+8*d+u,u=0;16>u;++u)i(V,yt+u*e.R,w,_+32*u,16);for(u=0;8>u;++u)i(ot,K+u*e.B,L,S+32*u,8),i(W,G+u*e.B,U,X+32*u,8)}}function kr(e,o,u,d,m,y,w,_,L){var S=[0],U=[0],X=0,K=L!=null?L.kd:0,V=L??new eo;if(e==null||12>u)return 7;V.data=e,V.w=o,V.ha=u,o=[o],u=[u],V.gb=[V.gb];t:{var yt=o,ot=u,W=V.gb;if(n(e!=null),n(ot!=null),n(W!=null),W[0]=0,12<=ot[0]&&!t(e,yt[0],"RIFF")){if(t(e,yt[0]+8,"WEBP")){W=3;break t}var G=Dt(e,yt[0]+4);if(12>G||4294967286<G){W=3;break t}if(K&&G>ot[0]-8){W=7;break t}W[0]=G,yt[0]+=12,ot[0]-=12}W=0}if(W!=0)return W;for(G=0<V.gb[0],u=u[0];;){t:{var mt=e;ot=o,W=u;var wt=S,bt=U,Et=yt=[0];if((Nt=X=[X])[0]=0,8>W[0])W=7;else{if(!t(mt,ot[0],"VP8X")){if(Dt(mt,ot[0]+4)!=10){W=3;break t}if(18>W[0]){W=7;break t}var St=Dt(mt,ot[0]+8),Ut=1+jt(mt,ot[0]+12);if(2147483648<=Ut*(mt=1+jt(mt,ot[0]+15))){W=3;break t}Et!=null&&(Et[0]=St),wt!=null&&(wt[0]=Ut),bt!=null&&(bt[0]=mt),ot[0]+=18,W[0]-=18,Nt[0]=1}W=0}}if(X=X[0],yt=yt[0],W!=0)return W;if(ot=!!(2&yt),!G&&X)return 3;if(y!=null&&(y[0]=!!(16&yt)),w!=null&&(w[0]=ot),_!=null&&(_[0]=0),w=S[0],yt=U[0],X&&ot&&L==null){W=0;break}if(4>u){W=7;break}if(G&&X||!G&&!X&&!t(e,o[0],"ALPH")){u=[u],V.na=[V.na],V.P=[V.P],V.Sa=[V.Sa];t:{St=e,W=o,G=u;var Nt=V.gb;wt=V.na,bt=V.P,Et=V.Sa,Ut=22,n(St!=null),n(G!=null),mt=W[0];var he=G[0];for(n(wt!=null),n(Et!=null),wt[0]=null,bt[0]=null,Et[0]=0;;){if(W[0]=mt,G[0]=he,8>he){W=7;break t}var ge=Dt(St,mt+4);if(4294967286<ge){W=3;break t}var ce=8+ge+1&-2;if(Ut+=ce,0<Nt&&Ut>Nt){W=3;break t}if(!t(St,mt,"VP8 ")||!t(St,mt,"VP8L")){W=0;break t}if(he[0]<ce){W=7;break t}t(St,mt,"ALPH")||(wt[0]=St,bt[0]=mt+8,Et[0]=ge),mt+=ce,he-=ce}}if(u=u[0],V.na=V.na[0],V.P=V.P[0],V.Sa=V.Sa[0],W!=0)break}u=[u],V.Ja=[V.Ja],V.xa=[V.xa];t:if(Nt=e,W=o,G=u,wt=V.gb[0],bt=V.Ja,Et=V.xa,St=W[0],mt=!t(Nt,St,"VP8 "),Ut=!t(Nt,St,"VP8L"),n(Nt!=null),n(G!=null),n(bt!=null),n(Et!=null),8>G[0])W=7;else{if(mt||Ut){if(Nt=Dt(Nt,St+4),12<=wt&&Nt>wt-12){W=3;break t}if(K&&Nt>G[0]-8){W=7;break t}bt[0]=Nt,W[0]+=8,G[0]-=8,Et[0]=Ut}else Et[0]=5<=G[0]&&Nt[St+0]==47&&!(Nt[St+4]>>5),bt[0]=G[0];W=0}if(u=u[0],V.Ja=V.Ja[0],V.xa=V.xa[0],o=o[0],W!=0)break;if(4294967286<V.Ja)return 3;if(_==null||ot||(_[0]=V.xa?2:1),w=[w],yt=[yt],V.xa){if(5>u){W=7;break}_=w,K=yt,ot=y,e==null||5>u?e=0:5<=u&&e[o+0]==47&&!(e[o+4]>>5)?(G=[0],Nt=[0],wt=[0],Z(bt=new x,e,o,u),Ht(bt,G,Nt,wt)?(_!=null&&(_[0]=G[0]),K!=null&&(K[0]=Nt[0]),ot!=null&&(ot[0]=wt[0]),e=1):e=0):e=0}else{if(10>u){W=7;break}_=yt,e==null||10>u||!Bo(e,o+3,u-3)?e=0:(K=e[o+0]|e[o+1]<<8|e[o+2]<<16,ot=16383&(e[o+7]<<8|e[o+6]),e=16383&(e[o+9]<<8|e[o+8]),1&K||3<(K>>1&7)||!(K>>4&1)||K>>5>=V.Ja||!ot||!e?e=0:(w&&(w[0]=ot),_&&(_[0]=e),e=1))}if(!e||(w=w[0],yt=yt[0],X&&(S[0]!=w||U[0]!=yt)))return 3;L!=null&&(L[0]=V,L.offset=o-L.w,n(4294967286>o-L.w),n(L.offset==L.ha-u));break}return W==0||W==7&&X&&L==null?(y!=null&&(y[0]|=V.na!=null&&0<V.na.length),d!=null&&(d[0]=w),m!=null&&(m[0]=yt),0):W}function so(e,o,u){var d=o.width,m=o.height,y=0,w=0,_=d,L=m;if(o.Da=e!=null&&0<e.Da,o.Da&&(_=e.cd,L=e.bd,y=e.v,w=e.j,11>u||(y&=-2,w&=-2),0>y||0>w||0>=_||0>=L||y+_>d||w+L>m))return 0;if(o.v=y,o.j=w,o.va=y+_,o.o=w+L,o.U=_,o.T=L,o.da=e!=null&&0<e.da,o.da){if(!Qt(_,L,u=[e.ib],y=[e.hb]))return 0;o.ib=u[0],o.hb=y[0]}return o.ob=e!=null&&e.ob,o.Kb=e==null||!e.Sd,o.da&&(o.ob=o.ib<3*d/4&&o.hb<3*m/4,o.Kb=0),1}function lo(e){if(e==null)return 2;if(11>e.S){var o=e.f.RGBA;o.fb+=(e.height-1)*o.A,o.A=-o.A}else o=e.f.kb,e=e.height,o.O+=(e-1)*o.fa,o.fa=-o.fa,o.N+=(e-1>>1)*o.Ab,o.Ab=-o.Ab,o.W+=(e-1>>1)*o.Db,o.Db=-o.Db,o.F!=null&&(o.J+=(e-1)*o.lb,o.lb=-o.lb);return 0}function Ar(e,o,u,d){if(d==null||0>=e||0>=o)return 2;if(u!=null){if(u.Da){var m=u.cd,y=u.bd,w=-2&u.v,_=-2&u.j;if(0>w||0>_||0>=m||0>=y||w+m>e||_+y>o)return 2;e=m,o=y}if(u.da){if(!Qt(e,o,m=[u.ib],y=[u.hb]))return 2;e=m[0],o=y[0]}}d.width=e,d.height=o;t:{var L=d.width,S=d.height;if(e=d.S,0>=L||0>=S||!(e>=ra&&13>e))e=2;else{if(0>=d.Rd&&d.sd==null){w=y=m=o=0;var U=(_=L*Ll[e])*S;if(11>e||(y=(S+1)/2*(o=(L+1)/2),e==12&&(w=(m=L)*S)),(S=a(U+2*y+w))==null){e=1;break t}d.sd=S,11>e?((L=d.f.RGBA).eb=S,L.fb=0,L.A=_,L.size=U):((L=d.f.kb).y=S,L.O=0,L.fa=_,L.Fd=U,L.f=S,L.N=0+U,L.Ab=o,L.Cd=y,L.ea=S,L.W=0+U+y,L.Db=o,L.Ed=y,e==12&&(L.F=S,L.J=0+U+2*y),L.Tc=w,L.lb=m)}if(o=1,m=d.S,y=d.width,w=d.height,m>=ra&&13>m)if(11>m)e=d.f.RGBA,o&=(_=Math.abs(e.A))*(w-1)+y<=e.size,o&=_>=y*Ll[m],o&=e.eb!=null;else{e=d.f.kb,_=(y+1)/2,U=(w+1)/2,L=Math.abs(e.fa),S=Math.abs(e.Ab);var X=Math.abs(e.Db),K=Math.abs(e.lb),V=K*(w-1)+y;o&=L*(w-1)+y<=e.Fd,o&=S*(U-1)+_<=e.Cd,o=(o&=X*(U-1)+_<=e.Ed)&L>=y&S>=_&X>=_,o&=e.y!=null,o&=e.f!=null,o&=e.ea!=null,m==12&&(o&=K>=y,o&=V<=e.Tc,o&=e.F!=null)}else o=0;e=o?0:2}}return e!=0||u!=null&&u.fd&&(e=lo(d)),e}var un=64,Sr=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],Nr=24,Or=32,co=8,hn=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];At("Predictor0","PredictorAdd0"),R.Predictor0=function(){return 4278190080},R.Predictor1=function(e){return e},R.Predictor2=function(e,o,u){return o[u+0]},R.Predictor3=function(e,o,u){return o[u+1]},R.Predictor4=function(e,o,u){return o[u-1]},R.Predictor5=function(e,o,u){return Ot(Ot(e,o[u+1]),o[u+0])},R.Predictor6=function(e,o,u){return Ot(e,o[u-1])},R.Predictor7=function(e,o,u){return Ot(e,o[u+0])},R.Predictor8=function(e,o,u){return Ot(o[u-1],o[u+0])},R.Predictor9=function(e,o,u){return Ot(o[u+0],o[u+1])},R.Predictor10=function(e,o,u){return Ot(Ot(e,o[u-1]),Ot(o[u+0],o[u+1]))},R.Predictor11=function(e,o,u){var d=o[u+0];return 0>=ne(d>>24&255,e>>24&255,(o=o[u-1])>>24&255)+ne(d>>16&255,e>>16&255,o>>16&255)+ne(d>>8&255,e>>8&255,o>>8&255)+ne(255&d,255&e,255&o)?d:e},R.Predictor12=function(e,o,u){var d=o[u+0];return(qt((e>>24&255)+(d>>24&255)-((o=o[u-1])>>24&255))<<24|qt((e>>16&255)+(d>>16&255)-(o>>16&255))<<16|qt((e>>8&255)+(d>>8&255)-(o>>8&255))<<8|qt((255&e)+(255&d)-(255&o)))>>>0},R.Predictor13=function(e,o,u){var d=o[u-1];return(Jt((e=Ot(e,o[u+0]))>>24&255,d>>24&255)<<24|Jt(e>>16&255,d>>16&255)<<16|Jt(e>>8&255,d>>8&255)<<8|Jt(e>>0&255,d>>0&255))>>>0};var fs=R.PredictorAdd0;R.PredictorAdd1=re,At("Predictor2","PredictorAdd2"),At("Predictor3","PredictorAdd3"),At("Predictor4","PredictorAdd4"),At("Predictor5","PredictorAdd5"),At("Predictor6","PredictorAdd6"),At("Predictor7","PredictorAdd7"),At("Predictor8","PredictorAdd8"),At("Predictor9","PredictorAdd9"),At("Predictor10","PredictorAdd10"),At("Predictor11","PredictorAdd11"),At("Predictor12","PredictorAdd12"),At("Predictor13","PredictorAdd13");var uo=R.PredictorAdd2;oe("ColorIndexInverseTransform","MapARGB","32b",function(e){return e>>8&255},function(e){return e}),oe("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(e){return e},function(e){return e>>8&255});var ea,Tn=R.ColorIndexInverseTransform,ho=R.MapARGB,ds=R.VP8LColorIndexInverseTransformAlpha,ps=R.MapAlpha,ki=R.VP8LPredictorsAdd=[];ki.length=16,(R.VP8LPredictors=[]).length=16,(R.VP8LPredictorsAdd_C=[]).length=16,(R.VP8LPredictors_C=[]).length=16;var xn,En,Ai,ti,fi,Mr,Dn,Fr,fo,Si,Bn,di,Qi,Ir,tr,Ni,po,_n,pi,Ce,Le,je,We,gi,Oi,jr,na,al,sl=a(511),ll=a(2041),cl=a(225),ul=a(767),hl=0,gs=ll,ia=cl,vn=ul,Cn=sl,ra=0,oa=1,fl=2,aa=3,sa=4,ms=5,dl=6,vs=7,bs=8,la=9,ys=10,du=[2,3,7],pu=[3,3,11],pl=[280,256,256,256,40],gu=[0,1,1,1,0],mu=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],vu=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],bu=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],yu=8,ws=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],xs=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],go=null,wu=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],xu=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],gl=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],_u=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],Cu=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],Lu=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],Pu=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],Mi=[],Rn=[],mi=[],ku=1,ml=2,Fi=[],Ln=[];ct("UpsampleRgbLinePair",se,3),ct("UpsampleBgrLinePair",ke,3),ct("UpsampleRgbaLinePair",jn,4),ct("UpsampleBgraLinePair",Ve,4),ct("UpsampleArgbLinePair",Qe,4),ct("UpsampleRgba4444LinePair",Re,2),ct("UpsampleRgb565LinePair",Oe,2);var Au=R.UpsampleRgbLinePair,Su=R.UpsampleBgrLinePair,vl=R.UpsampleRgbaLinePair,bl=R.UpsampleBgraLinePair,yl=R.UpsampleArgbLinePair,wl=R.UpsampleRgba4444LinePair,Nu=R.UpsampleRgb565LinePair,ca=16,ua=1<<ca-1,mo=-227,_s=482,xl=6,Ou=(256<<xl)-1,_l=0,Mu=a(256),Fu=a(256),Iu=a(256),ju=a(256),Tu=a(_s-mo),Eu=a(_s-mo);hi("YuvToRgbRow",se,3),hi("YuvToBgrRow",ke,3),hi("YuvToRgbaRow",jn,4),hi("YuvToBgraRow",Ve,4),hi("YuvToArgbRow",Qe,4),hi("YuvToRgba4444Row",Re,2),hi("YuvToRgb565Row",Oe,2);var Cl=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],ha=[0,2,8],Du=[8,7,6,4,4,2,2,2,1,1,1,1],Bu=1;this.WebPDecodeRGBA=function(e,o,u,d,m){var y=oa,w=new Lr,_=new An;w.ba=_,_.S=y,_.width=[_.width],_.height=[_.height];var L=_.width,S=_.height,U=new si;if(U==null||e==null)var X=2;else n(U!=null),X=kr(e,o,u,U.width,U.height,U.Pd,U.Qd,U.format,null);if(X!=0?L=0:(L!=null&&(L[0]=U.width[0]),S!=null&&(S[0]=U.height[0]),L=1),L){_.width=_.width[0],_.height=_.height[0],d!=null&&(d[0]=_.width),m!=null&&(m[0]=_.height);t:{if(d=new vr,(m=new eo).data=e,m.w=o,m.ha=u,m.kd=1,o=[0],n(m!=null),((e=kr(m.data,m.w,m.ha,null,null,null,o,null,m))==0||e==7)&&o[0]&&(e=4),(o=e)==0){if(n(w!=null),d.data=m.data,d.w=m.w+m.offset,d.ha=m.ha-m.offset,d.put=Yn,d.ac=Je,d.bc=Jn,d.ma=w,m.xa){if((e=wn())==null){w=1;break t}if(function(K,V){var yt=[0],ot=[0],W=[0];e:for(;;){if(K==null)return 0;if(V==null)return K.a=2,0;if(K.l=V,K.a=0,Z(K.m,V.data,V.w,V.ha),!Ht(K.m,yt,ot,W)){K.a=3;break e}if(K.xb=ml,V.width=yt[0],V.height=ot[0],!Zn(yt[0],ot[0],1,K,null))break e;return 1}return n(K.a!=0),0}(e,d)){if(d=(o=Ar(d.width,d.height,w.Oa,w.ba))==0){e:{d=e;n:for(;;){if(d==null){d=0;break e}if(n(d.s.yc!=null),n(d.s.Ya!=null),n(0<d.s.Wb),n((u=d.l)!=null),n((m=u.ma)!=null),d.xb!=0){if(d.ca=m.ba,d.tb=m.tb,n(d.ca!=null),!so(m.Oa,u,aa)){d.a=2;break n}if(!Yi(d,u.width)||u.da)break n;if((u.da||de(d.ca.S))&&Y(),11>d.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),d.ca.f.kb.F!=null&&Y()),d.Pb&&0<d.s.ua&&d.s.vb.X==null&&!Gt(d.s.vb,d.s.Wa.Xa)){d.a=1;break n}d.xb=0}if(!Mn(d,d.V,d.Ba,d.c,d.i,u.o,hr))break n;m.Dc=d.Ma,d=1;break e}n(d.a!=0),d=0}d=!d}d&&(o=e.a)}else o=e.a}else{if((e=new $a)==null){w=1;break t}if(e.Fa=m.na,e.P=m.P,e.qc=m.Sa,Ro(e,d)){if((o=Ar(d.width,d.height,w.Oa,w.ba))==0){if(e.Aa=0,u=w.Oa,n((m=e)!=null),u!=null){if(0<(L=0>(L=u.Md)?0:100<L?255:255*L/100)){for(S=U=0;4>S;++S)12>(X=m.pb[S]).lc&&(X.ia=L*Du[0>X.lc?0:X.lc]>>3),U|=X.ia;U&&(alert("todo:VP8InitRandom"),m.ia=1)}m.Ga=u.Id,100<m.Ga?m.Ga=100:0>m.Ga&&(m.Ga=0)}Ya(e,d)||(o=e.a)}}else o=e.a}o==0&&w.Oa!=null&&w.Oa.fd&&(o=lo(w.ba))}w=o}y=w!=0?null:11>y?_.f.RGBA.eb:_.f.kb.y}else y=null;return y};var Ll=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function g(R,vt){for(var gt="",N=0;N<4;N++)gt+=String.fromCharCode(R[vt++]);return gt}function b(R,vt){return(R[vt+0]<<0|R[vt+1]<<8|R[vt+2]<<16)>>>0}function C(R,vt){return(R[vt+0]<<0|R[vt+1]<<8|R[vt+2]<<16|R[vt+3]<<24)>>>0}new f;var P=[0],p=[0],I=[],O=new f,D=r,k=function(R,vt){var gt={},N=0,A=!1,z=0,B=0;if(gt.frames=[],!function(F,T,H,$){for(var Q=0;Q<$;Q++)if(F[T+Q]!=H.charCodeAt(Q))return!0;return!1}(R,vt,"RIFF",4)){var at,nt;for(C(R,vt+=4),vt+=8;vt<R.length;){var ft=g(R,vt),Z=C(R,vt+=4);vt+=4;var pt=Z+(1&Z);switch(ft){case"VP8 ":case"VP8L":gt.frames[N]===void 0&&(gt.frames[N]={}),(x=gt.frames[N]).src_off=A?B:vt-8,x.src_size=z+Z+8,N++,A&&(A=!1,z=0,B=0);break;case"VP8X":(x=gt.header={}).feature_flags=R[vt];var dt=vt+4;x.canvas_width=1+b(R,dt),dt+=3,x.canvas_height=1+b(R,dt),dt+=3;break;case"ALPH":A=!0,z=pt+8,B=vt-8;break;case"ANIM":(x=gt.header).bgcolor=C(R,vt),dt=vt+4,x.loop_count=(at=R)[(nt=dt)+0]<<0|at[nt+1]<<8,dt+=2;break;case"ANMF":var Mt,x;(x=gt.frames[N]={}).offset_x=2*b(R,vt),vt+=3,x.offset_y=2*b(R,vt),vt+=3,x.width=1+b(R,vt),vt+=3,x.height=1+b(R,vt),vt+=3,x.duration=b(R,vt),vt+=3,Mt=R[vt++],x.dispose=1&Mt,x.blend=Mt>>1&1}ft!="ANMF"&&(vt+=pt)}return gt}}(D,0);k.response=D,k.rgbaoutput=!0,k.dataurl=!1;var j=k.header?k.header:null,J=k.frames?k.frames:null;if(j){j.loop_counter=j.loop_count,P=[j.canvas_height],p=[j.canvas_width];for(var lt=0;lt<J.length&&J[lt].blend!=0;lt++);}var ht=J[0],_t=O.WebPDecodeRGBA(D,ht.src_off,ht.src_size,p,P);ht.rgba=_t,ht.imgwidth=p[0],ht.imgheight=P[0];for(var tt=0;tt<p[0]*P[0]*4;tt++)I[tt]=_t[tt];return this.width=p,this.height=P,this.data=I,this}var Vt,_e,No,Zs,rr,Dc,zh,Bc,Fe,rl,ur,zr,Zt,zi,pn,qc,ri,Uh,Wh,Hh,Te,Ee,De,Gh,Vh,el,Ea,$h,Yh,Jh,Xh,nu,Js,oi,iu,ru,Gn,ar,sr,lr,Oo,Ge,Mo,cr,Ra,Fo,Hi,Io,Bt,Kh,Zh,au,su,nl,lu,Qh,t1,Uc,Da,Gc,Vc,Xs,Ur,Ba,$c,Yc,Jc,r1,Vn,Gi,a1,s1,ai,l1,Zc,cu,c1,u1,h1,f1,d1,p1,g1,uu,m1,v1,b1,y1,w1,ol,hu=zl(()=>{"use strict";Zu();Tc();Vt=function(){return typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this}();_e={log:Vs,warn:function(r){Vt.console&&(typeof Vt.console.warn=="function"?Vt.console.warn.apply(Vt.console,arguments):Vs.call(null,arguments))},error:function(r){Vt.console&&(typeof Vt.console.error=="function"?Vt.console.error.apply(Vt.console,arguments):Vs(r))}};rr=Vt.saveAs||((typeof window>"u"?"undefined":ye(window))!=="object"||window!==Vt?function(){}:typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype?function(r,n,t){var i=Vt.URL||Vt.webkitURL,s=document.createElement("a");n=n||r.name||"download",s.download=n,s.rel="noopener",typeof r=="string"?(s.href=r,s.origin!==location.origin?Ec(s.href)?$s(r,n,t):Ta(s,s.target="_blank"):Ta(s)):(s.href=i.createObjectURL(r),setTimeout(function(){i.revokeObjectURL(s.href)},4e4),setTimeout(function(){Ta(s)},0))}:"msSaveOrOpenBlob"in navigator?function(r,n,t){if(n=n||r.name||"download",typeof r=="string")if(Ec(r))$s(r,n,t);else{var i=document.createElement("a");i.href=r,i.target="_blank",setTimeout(function(){Ta(i)})}else navigator.msSaveOrOpenBlob(function(s,a){return a===void 0?a={autoBom:!1}:ye(a)!=="object"&&(_e.warn("Deprecated: Expected third argument to be a object"),a={autoBom:!a}),a.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(s.type)?new Blob(["\uFEFF",s],{type:s.type}):s}(r,t),n)}:function(r,n,t,i){if((i=i||open("","_blank"))&&(i.document.title=i.document.body.innerText="downloading..."),typeof r=="string")return $s(r,n,t);var s=r.type==="application/octet-stream",a=/constructor/i.test(Vt.HTMLElement)||Vt.safari,c=/CriOS\/[\d]+/.test(navigator.userAgent);if((c||s&&a)&&(typeof FileReader>"u"?"undefined":ye(FileReader))==="object"){var h=new FileReader;h.onloadend=function(){var b=h.result;b=c?b:b.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=b:location=b,i=null},h.readAsDataURL(r)}else{var f=Vt.URL||Vt.webkitURL,g=f.createObjectURL(r);i?i.location=g:location.href=g,i=null,setTimeout(function(){f.revokeObjectURL(g)},4e4)}});No=Vt.atob.bind(Vt),Zs=Vt.btoa.bind(Vt);Dc="0123456789abcdef".split("");zh=function(r){for(var n=0;n<r.length;n++)r[n]=Rh(r[n]);return r.join("")}(tu("hello"))!="5d41402abc4b2a76b9719d911017c592";Bc={print:4,modify:8,copy:16,"annot-forms":32};Wr.prototype.lsbFirstWord=function(r){return String.fromCharCode(r>>0&255,r>>8&255,r>>16&255,r>>24&255)},Wr.prototype.toHexString=function(r){return r.split("").map(function(n){return("0"+(255&n.charCodeAt(0)).toString(16)).slice(-2)}).join("")},Wr.prototype.hexToBytes=function(r){for(var n=[],t=0;t<r.length;t+=2)n.push(String.fromCharCode(parseInt(r.substr(t,2),16)));return n.join("")},Wr.prototype.processOwnerPassword=function(r,n){return tl(Qs(n).substr(0,5),r)},Wr.prototype.encryptor=function(r,n){var t=Qs(this.encryptionKey+String.fromCharCode(255&r,r>>8&255,r>>16&255,255&n,n>>8&255)).substr(0,10);return function(i){return tl(t,i)}},jo.prototype.equals=function(r){var n,t="id,objectNumber,equals";if(!r||ye(r)!==ye(this))return!1;var i=0;for(n in this)if(!(t.indexOf(n)>=0)){if(this.hasOwnProperty(n)&&!r.hasOwnProperty(n)||this[n]!==r[n])return!1;i++}for(n in r)r.hasOwnProperty(n)&&t.indexOf(n)<0&&i--;return i===0},Wt.API={events:[]},Wt.version="2.5.1";Fe=Wt.API,rl=1,ur=function(r){return r.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},zr=function(r){return r.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},Zt=function(r){return r.toFixed(2)},zi=function(r){return r.toFixed(5)};Fe.__acroform__={};pn=function(r,n){r.prototype=Object.create(n.prototype),r.prototype.constructor=r},qc=function(r){return r*rl},ri=function(r){var n=new iu,t=Bt.internal.getHeight(r)||0,i=Bt.internal.getWidth(r)||0;return n.BBox=[0,0,Number(Zt(i)),Number(Zt(t))],n},Uh=Fe.__acroform__.setBit=function(r,n){if(r=r||0,n=n||0,isNaN(r)||isNaN(n))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return r|=1<<n},Wh=Fe.__acroform__.clearBit=function(r,n){if(r=r||0,n=n||0,isNaN(r)||isNaN(n))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return r&=~(1<<n)},Hh=Fe.__acroform__.getBit=function(r,n){if(isNaN(r)||isNaN(n))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return r&1<<n?1:0},Te=Fe.__acroform__.getBitForPdf=function(r,n){if(isNaN(r)||isNaN(n))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return Hh(r,n-1)},Ee=Fe.__acroform__.setBitForPdf=function(r,n){if(isNaN(r)||isNaN(n))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return Uh(r,n-1)},De=Fe.__acroform__.clearBitForPdf=function(r,n){if(isNaN(r)||isNaN(n))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return Wh(r,n-1)},Gh=Fe.__acroform__.calculateCoordinates=function(r,n){var t=n.internal.getHorizontalCoordinate,i=n.internal.getVerticalCoordinate,s=r[0],a=r[1],c=r[2],h=r[3],f={};return f.lowerLeft_X=t(s)||0,f.lowerLeft_Y=i(a+h)||0,f.upperRight_X=t(s+c)||0,f.upperRight_Y=i(a)||0,[Number(Zt(f.lowerLeft_X)),Number(Zt(f.lowerLeft_Y)),Number(Zt(f.upperRight_X)),Number(Zt(f.upperRight_Y))]},Vh=function(r){if(r.appearanceStreamContent)return r.appearanceStreamContent;if(r.V||r.DV){var n=[],t=r._V||r.DV,i=el(r,t),s=r.scope.internal.getFont(r.fontName,r.fontStyle).id;n.push("/Tx BMC"),n.push("q"),n.push("BT"),n.push(r.scope.__private__.encodeColorString(r.color)),n.push("/"+s+" "+Zt(i.fontSize)+" Tf"),n.push("1 0 0 1 0 0 Tm"),n.push(i.text),n.push("ET"),n.push("Q"),n.push("EMC");var a=ri(r);return a.scope=r.scope,a.stream=n.join(`
`),a}},el=function(r,n){var t=r.fontSize===0?r.maxFontSize:r.fontSize,i={text:"",fontSize:""},s=(n=(n=n.substr(0,1)=="("?n.substr(1):n).substr(n.length-1)==")"?n.substr(0,n.length-1):n).split(" ");s=r.multiline?s.map(function(N){return N.split(`
`)}):s.map(function(N){return[N]});var a=t,c=Bt.internal.getHeight(r)||0;c=c<0?-c:c;var h=Bt.internal.getWidth(r)||0;h=h<0?-h:h;var f=function(N,A,z){if(N+1<s.length){var B=A+" "+s[N+1][0];return Ea(B,r,z).width<=h-4}return!1};a++;t:for(;a>0;){n="",a--;var g,b,C=Ea("3",r,a).height,P=r.multiline?c-a:(c-C)/2,p=P+=2,I=0,O=0,D=0;if(a<=0){n=`(...) Tj
`,n+="% Width of Text: "+Ea(n,r,a=12).width+", FieldWidth:"+h+`
`;break}for(var k="",j=0,J=0;J<s.length;J++)if(s.hasOwnProperty(J)){var lt=!1;if(s[J].length!==1&&D!==s[J].length-1){if((C+2)*(j+2)+2>c)continue t;k+=s[J][D],lt=!0,O=J,J--}else{k=(k+=s[J][D]+" ").substr(k.length-1)==" "?k.substr(0,k.length-1):k;var ht=parseInt(J),_t=f(ht,k,a),tt=J>=s.length-1;if(_t&&!tt){k+=" ",D=0;continue}if(_t||tt){if(tt)O=ht;else if(r.multiline&&(C+2)*(j+2)+2>c)continue t}else{if(!r.multiline||(C+2)*(j+2)+2>c)continue t;O=ht}}for(var R="",vt=I;vt<=O;vt++){var gt=s[vt];if(r.multiline){if(vt===O){R+=gt[D]+" ",D=(D+1)%gt.length;continue}if(vt===I){R+=gt[gt.length-1]+" ";continue}}R+=gt[0]+" "}switch(R=R.substr(R.length-1)==" "?R.substr(0,R.length-1):R,b=Ea(R,r,a).width,r.textAlign){case"right":g=h-b-2;break;case"center":g=(h-b)/2;break;case"left":default:g=2}n+=Zt(g)+" "+Zt(p)+` Td
`,n+="("+ur(R)+`) Tj
`,n+=-Zt(g)+` 0 Td
`,p=-(a+2),b=0,I=lt?O:O+1,j++,k=""}break}return i.text=n,i.fontSize=a,i},Ea=function(r,n,t){var i=n.scope.internal.getFont(n.fontName,n.fontStyle),s=n.scope.getStringUnitWidth(r,{font:i,fontSize:parseFloat(t),charSpace:0})*parseFloat(t);return{height:n.scope.getStringUnitWidth("3",{font:i,fontSize:parseFloat(t),charSpace:0})*parseFloat(t)*1.5,width:s}},$h={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},Yh=function(r,n){var t={type:"reference",object:r};n.internal.getPageInfo(r.page).pageContext.annotations.find(function(i){return i.type===t.type&&i.object===t.object})===void 0&&n.internal.getPageInfo(r.page).pageContext.annotations.push(t)},Jh=function(r,n){for(var t in r)if(r.hasOwnProperty(t)){var i=t,s=r[t];n.internal.newObjectDeferredBegin(s.objId,!0),ye(s)==="object"&&typeof s.putStream=="function"&&s.putStream(),delete r[i]}},Xh=function(r,n){if(n.scope=r,r.internal!==void 0&&(r.internal.acroformPlugin===void 0||r.internal.acroformPlugin.isInitialized===!1)){if(Gn.FieldNum=0,r.internal.acroformPlugin=JSON.parse(JSON.stringify($h)),r.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");rl=r.internal.scaleFactor,r.internal.acroformPlugin.acroFormDictionaryRoot=new ru,r.internal.acroformPlugin.acroFormDictionaryRoot.scope=r,r.internal.acroformPlugin.acroFormDictionaryRoot._eventID=r.internal.events.subscribe("postPutResources",function(){(function(t){t.internal.events.unsubscribe(t.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete t.internal.acroformPlugin.acroFormDictionaryRoot._eventID,t.internal.acroformPlugin.printedOut=!0})(r)}),r.internal.events.subscribe("buildDocument",function(){(function(t){t.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var i=t.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];a.objId=void 0,a.hasAnnotation&&Yh(a,t)}})(r)}),r.internal.events.subscribe("putCatalog",function(){(function(t){if(t.internal.acroformPlugin.acroFormDictionaryRoot===void 0)throw new Error("putCatalogCallback: Root missing.");t.internal.write("/AcroForm "+t.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")})(r)}),r.internal.events.subscribe("postPutPages",function(t){(function(i,s){var a=!i;for(var c in i||(s.internal.newObjectDeferredBegin(s.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),s.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),i=i||s.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(i.hasOwnProperty(c)){var h=i[c],f=[],g=h.Rect;if(h.Rect&&(h.Rect=Gh(h.Rect,s)),s.internal.newObjectDeferredBegin(h.objId,!0),h.DA=Bt.createDefaultAppearanceStream(h),ye(h)==="object"&&typeof h.getKeyValueListForStream=="function"&&(f=h.getKeyValueListForStream()),h.Rect=g,h.hasAppearanceStream&&!h.appearanceStreamContent){var b=Vh(h);f.push({key:"AP",value:"<</N "+b+">>"}),s.internal.acroformPlugin.xForms.push(b)}if(h.appearanceStreamContent){var C="";for(var P in h.appearanceStreamContent)if(h.appearanceStreamContent.hasOwnProperty(P)){var p=h.appearanceStreamContent[P];if(C+="/"+P+" ",C+="<<",Object.keys(p).length>=1||Array.isArray(p)){for(var c in p)if(p.hasOwnProperty(c)){var I=p[c];typeof I=="function"&&(I=I.call(s,h)),C+="/"+c+" "+I+" ",s.internal.acroformPlugin.xForms.indexOf(I)>=0||s.internal.acroformPlugin.xForms.push(I)}}else typeof(I=p)=="function"&&(I=I.call(s,h)),C+="/"+c+" "+I,s.internal.acroformPlugin.xForms.indexOf(I)>=0||s.internal.acroformPlugin.xForms.push(I);C+=">>"}f.push({key:"AP",value:`<<
`+C+">>"})}s.internal.putStream({additionalKeyValues:f,objectId:h.objId}),s.internal.out("endobj")}a&&Jh(s.internal.acroformPlugin.xForms,s)})(t,r)}),r.internal.acroformPlugin.isInitialized=!0}},nu=Fe.__acroform__.arrayToPdfArray=function(r,n,t){var i=function(c){return c};if(Array.isArray(r)){for(var s="[",a=0;a<r.length;a++)switch(a!==0&&(s+=" "),ye(r[a])){case"boolean":case"number":case"object":s+=r[a].toString();break;case"string":r[a].substr(0,1)!=="/"?(n!==void 0&&t&&(i=t.internal.getEncryptor(n)),s+="("+ur(i(r[a].toString()))+")"):s+=r[a].toString()}return s+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},Js=function(r,n,t){var i=function(s){return s};return n!==void 0&&t&&(i=t.internal.getEncryptor(n)),(r=r||"").toString(),r="("+ur(i(r))+")"},oi=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(this._objId===void 0){if(this.scope===void 0)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(r){this._objId=r}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};oi.prototype.toString=function(){return this.objId+" 0 R"},oi.prototype.putStream=function(){var r=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:r,objectId:this.objId}),this.scope.internal.out("endobj")},oi.prototype.getKeyValueListForStream=function(){var r=[],n=Object.getOwnPropertyNames(this).filter(function(a){return a!="content"&&a!="appearanceStreamContent"&&a!="scope"&&a!="objId"&&a.substring(0,1)!="_"});for(var t in n)if(Object.getOwnPropertyDescriptor(this,n[t]).configurable===!1){var i=n[t],s=this[i];s&&(Array.isArray(s)?r.push({key:i,value:nu(s,this.objId,this.scope)}):s instanceof oi?(s.scope=this.scope,r.push({key:i,value:s.objId+" 0 R"})):typeof s!="function"&&r.push({key:i,value:s}))}return r};iu=function(){oi.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var r,n=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return n},set:function(t){n=t}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(t){r=t.trim()},get:function(){return r||null}})};pn(iu,oi);ru=function(){oi.call(this);var r,n=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return n.length>0?n:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return n}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(r){var t=function(i){return i};return this.scope&&(t=this.scope.internal.getEncryptor(this.objId)),"("+ur(t(r))+")"}},set:function(t){r=t}})};pn(ru,oi);Gn=function r(){oi.call(this);var n=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return n},set:function(k){if(isNaN(k))throw new Error('Invalid value "'+k+'" for attribute F supplied.');n=k}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!Te(n,3)},set:function(k){k?this.F=Ee(n,3):this.F=De(n,3)}});var t=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return t},set:function(k){if(isNaN(k))throw new Error('Invalid value "'+k+'" for attribute Ff supplied.');t=k}});var i=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(i.length!==0)return i},set:function(k){i=k!==void 0?k:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!i||isNaN(i[0])?0:i[0]},set:function(k){i[0]=k}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!i||isNaN(i[1])?0:i[1]},set:function(k){i[1]=k}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!i||isNaN(i[2])?0:i[2]},set:function(k){i[2]=k}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!i||isNaN(i[3])?0:i[3]},set:function(k){i[3]=k}});var s="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return s},set:function(k){switch(k){case"/Btn":case"/Tx":case"/Ch":case"/Sig":s=k;break;default:throw new Error('Invalid value "'+k+'" for attribute FT supplied.')}}});var a=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!a||a.length<1){if(this instanceof Ra)return;a="FieldObject"+r.FieldNum++}var k=function(j){return j};return this.scope&&(k=this.scope.internal.getEncryptor(this.objId)),"("+ur(k(a))+")"},set:function(k){a=k.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return a},set:function(k){a=k}});var c="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return c},set:function(k){c=k}});var h="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return h},set:function(k){h=k}});var f=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return f},set:function(k){f=k}});var g=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return g===void 0?50/rl:g},set:function(k){g=k}});var b="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return b},set:function(k){b=k}});var C="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!C||this instanceof Ra||this instanceof Hi))return Js(C,this.objId,this.scope)},set:function(k){k=k.toString(),C=k}});var P=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(P)return this instanceof Ge?P:Js(P,this.objId,this.scope)},set:function(k){k=k.toString(),P=this instanceof Ge?k:k.substr(0,1)==="("?zr(k.substr(1,k.length-2)):zr(k)}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof Ge?zr(P.substr(1,P.length-1)):P},set:function(k){k=k.toString(),P=this instanceof Ge?"/"+k:k}});var p=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(p)return p},set:function(k){this.V=k}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(p)return this instanceof Ge?p:Js(p,this.objId,this.scope)},set:function(k){k=k.toString(),p=this instanceof Ge?k:k.substr(0,1)==="("?zr(k.substr(1,k.length-2)):zr(k)}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof Ge?zr(p.substr(1,p.length-1)):p},set:function(k){k=k.toString(),p=this instanceof Ge?"/"+k:k}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var I,O=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return O},set:function(k){k=!!k,O=k}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(I)return I},set:function(k){I=k}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,1)},set:function(k){k?this.Ff=Ee(this.Ff,1):this.Ff=De(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,2)},set:function(k){k?this.Ff=Ee(this.Ff,2):this.Ff=De(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,3)},set:function(k){k?this.Ff=Ee(this.Ff,3):this.Ff=De(this.Ff,3)}});var D=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(D!==null)return D},set:function(k){if([0,1,2].indexOf(k)===-1)throw new Error('Invalid value "'+k+'" for attribute Q supplied.');D=k}}),Object.defineProperty(this,"textAlign",{get:function(){var k;switch(D){case 0:default:k="left";break;case 1:k="center";break;case 2:k="right"}return k},configurable:!0,enumerable:!0,set:function(k){switch(k){case"right":case 2:D=2;break;case"center":case 1:D=1;break;case"left":case 0:default:D=0}}})};pn(Gn,oi);ar=function(){Gn.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var r=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return r},set:function(t){r=t}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return r},set:function(t){r=t}});var n=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return nu(n,this.objId,this.scope)},set:function(t){var i,s;s=[],typeof(i=t)=="string"&&(s=function(a,c,h){h||(h=1);for(var f,g=[];f=c.exec(a);)g.push(f[h]);return g}(i,/\((.*?)\)/g)),n=s}}),this.getOptions=function(){return n},this.setOptions=function(t){n=t,this.sort&&n.sort()},this.addOption=function(t){t=(t=t||"").toString(),n.push(t),this.sort&&n.sort()},this.removeOption=function(t,i){for(i=i||!1,t=(t=t||"").toString();n.indexOf(t)!==-1&&(n.splice(n.indexOf(t),1),i!==!1););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,18)},set:function(t){t?this.Ff=Ee(this.Ff,18):this.Ff=De(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,19)},set:function(t){this.combo===!0&&(t?this.Ff=Ee(this.Ff,19):this.Ff=De(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,20)},set:function(t){t?(this.Ff=Ee(this.Ff,20),n.sort()):this.Ff=De(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,22)},set:function(t){t?this.Ff=Ee(this.Ff,22):this.Ff=De(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,23)},set:function(t){t?this.Ff=Ee(this.Ff,23):this.Ff=De(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,27)},set:function(t){t?this.Ff=Ee(this.Ff,27):this.Ff=De(this.Ff,27)}}),this.hasAppearanceStream=!1};pn(ar,Gn);sr=function(){ar.call(this),this.fontName="helvetica",this.combo=!1};pn(sr,ar);lr=function(){sr.call(this),this.combo=!0};pn(lr,sr);Oo=function(){lr.call(this),this.edit=!0};pn(Oo,lr);Ge=function(){Gn.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,15)},set:function(t){t?this.Ff=Ee(this.Ff,15):this.Ff=De(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,16)},set:function(t){t?this.Ff=Ee(this.Ff,16):this.Ff=De(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,17)},set:function(t){t?this.Ff=Ee(this.Ff,17):this.Ff=De(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,26)},set:function(t){t?this.Ff=Ee(this.Ff,26):this.Ff=De(this.Ff,26)}});var r,n={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(a){return a};if(this.scope&&(t=this.scope.internal.getEncryptor(this.objId)),Object.keys(n).length!==0){var i,s=[];for(i in s.push("<<"),n)s.push("/"+i+" ("+ur(t(n[i]))+")");return s.push(">>"),s.join(`
`)}},set:function(t){ye(t)==="object"&&(n=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return n.CA||""},set:function(t){typeof t=="string"&&(n.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(t){r=t}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(t){r="/"+t}})};pn(Ge,Gn);Mo=function(){Ge.call(this),this.pushButton=!0};pn(Mo,Ge);cr=function(){Ge.call(this),this.radio=!0,this.pushButton=!1;var r=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return r},set:function(n){r=n!==void 0?n:[]}})};pn(cr,Ge);Ra=function(){var r,n;Gn.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return r},set:function(s){r=s}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return n},set:function(s){n=s}});var t,i={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var s=function(h){return h};this.scope&&(s=this.scope.internal.getEncryptor(this.objId));var a,c=[];for(a in c.push("<<"),i)c.push("/"+a+" ("+ur(s(i[a]))+")");return c.push(">>"),c.join(`
`)},set:function(s){ye(s)==="object"&&(i=s)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return i.CA||""},set:function(s){typeof s=="string"&&(i.CA=s)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return t},set:function(s){t=s}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return t.substr(1,t.length-1)},set:function(s){t="/"+s}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Bt.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};pn(Ra,Gn),cr.prototype.setAppearance=function(r){if(!("createAppearanceStream"in r)||!("getCA"in r))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var n in this.Kids)if(this.Kids.hasOwnProperty(n)){var t=this.Kids[n];t.appearanceStreamContent=r.createAppearanceStream(t.optionName),t.caption=r.getCA()}},cr.prototype.createOption=function(r){var n=new Ra;return n.Parent=this,n.optionName=r,this.Kids.push(n),Kh.call(this.scope,n),n};Fo=function(){Ge.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Bt.CheckBox.createAppearanceStream()};pn(Fo,Ge);Hi=function(){Gn.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,13)},set:function(n){n?this.Ff=Ee(this.Ff,13):this.Ff=De(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,21)},set:function(n){n?this.Ff=Ee(this.Ff,21):this.Ff=De(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,23)},set:function(n){n?this.Ff=Ee(this.Ff,23):this.Ff=De(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,24)},set:function(n){n?this.Ff=Ee(this.Ff,24):this.Ff=De(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,25)},set:function(n){n?this.Ff=Ee(this.Ff,25):this.Ff=De(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,26)},set:function(n){n?this.Ff=Ee(this.Ff,26):this.Ff=De(this.Ff,26)}});var r=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return r},set:function(n){r=n}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return r},set:function(n){Number.isInteger(n)&&(r=n)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};pn(Hi,Gn);Io=function(){Hi.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!Te(this.Ff,14)},set:function(r){r?this.Ff=Ee(this.Ff,14):this.Ff=De(this.Ff,14)}}),this.password=!0};pn(Io,Hi);Bt={CheckBox:{createAppearanceStream:function(){return{N:{On:Bt.CheckBox.YesNormal},D:{On:Bt.CheckBox.YesPushDown,Off:Bt.CheckBox.OffPushDown}}},YesPushDown:function(r){var n=ri(r);n.scope=r.scope;var t=[],i=r.scope.internal.getFont(r.fontName,r.fontStyle).id,s=r.scope.__private__.encodeColorString(r.color),a=el(r,r.caption);return t.push("0.749023 g"),t.push("0 0 "+Zt(Bt.internal.getWidth(r))+" "+Zt(Bt.internal.getHeight(r))+" re"),t.push("f"),t.push("BMC"),t.push("q"),t.push("0 0 1 rg"),t.push("/"+i+" "+Zt(a.fontSize)+" Tf "+s),t.push("BT"),t.push(a.text),t.push("ET"),t.push("Q"),t.push("EMC"),n.stream=t.join(`
`),n},YesNormal:function(r){var n=ri(r);n.scope=r.scope;var t=r.scope.internal.getFont(r.fontName,r.fontStyle).id,i=r.scope.__private__.encodeColorString(r.color),s=[],a=Bt.internal.getHeight(r),c=Bt.internal.getWidth(r),h=el(r,r.caption);return s.push("1 g"),s.push("0 0 "+Zt(c)+" "+Zt(a)+" re"),s.push("f"),s.push("q"),s.push("0 0 1 rg"),s.push("0 0 "+Zt(c-1)+" "+Zt(a-1)+" re"),s.push("W"),s.push("n"),s.push("0 g"),s.push("BT"),s.push("/"+t+" "+Zt(h.fontSize)+" Tf "+i),s.push(h.text),s.push("ET"),s.push("Q"),n.stream=s.join(`
`),n},OffPushDown:function(r){var n=ri(r);n.scope=r.scope;var t=[];return t.push("0.749023 g"),t.push("0 0 "+Zt(Bt.internal.getWidth(r))+" "+Zt(Bt.internal.getHeight(r))+" re"),t.push("f"),n.stream=t.join(`
`),n}},RadioButton:{Circle:{createAppearanceStream:function(r){var n={D:{Off:Bt.RadioButton.Circle.OffPushDown},N:{}};return n.N[r]=Bt.RadioButton.Circle.YesNormal,n.D[r]=Bt.RadioButton.Circle.YesPushDown,n},getCA:function(){return"l"},YesNormal:function(r){var n=ri(r);n.scope=r.scope;var t=[],i=Bt.internal.getWidth(r)<=Bt.internal.getHeight(r)?Bt.internal.getWidth(r)/4:Bt.internal.getHeight(r)/4;i=Number((.9*i).toFixed(5));var s=Bt.internal.Bezier_C,a=Number((i*s).toFixed(5));return t.push("q"),t.push("1 0 0 1 "+zi(Bt.internal.getWidth(r)/2)+" "+zi(Bt.internal.getHeight(r)/2)+" cm"),t.push(i+" 0 m"),t.push(i+" "+a+" "+a+" "+i+" 0 "+i+" c"),t.push("-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c"),t.push("-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c"),t.push(a+" -"+i+" "+i+" -"+a+" "+i+" 0 c"),t.push("f"),t.push("Q"),n.stream=t.join(`
`),n},YesPushDown:function(r){var n=ri(r);n.scope=r.scope;var t=[],i=Bt.internal.getWidth(r)<=Bt.internal.getHeight(r)?Bt.internal.getWidth(r)/4:Bt.internal.getHeight(r)/4;i=Number((.9*i).toFixed(5));var s=Number((2*i).toFixed(5)),a=Number((s*Bt.internal.Bezier_C).toFixed(5)),c=Number((i*Bt.internal.Bezier_C).toFixed(5));return t.push("0.749023 g"),t.push("q"),t.push("1 0 0 1 "+zi(Bt.internal.getWidth(r)/2)+" "+zi(Bt.internal.getHeight(r)/2)+" cm"),t.push(s+" 0 m"),t.push(s+" "+a+" "+a+" "+s+" 0 "+s+" c"),t.push("-"+a+" "+s+" -"+s+" "+a+" -"+s+" 0 c"),t.push("-"+s+" -"+a+" -"+a+" -"+s+" 0 -"+s+" c"),t.push(a+" -"+s+" "+s+" -"+a+" "+s+" 0 c"),t.push("f"),t.push("Q"),t.push("0 g"),t.push("q"),t.push("1 0 0 1 "+zi(Bt.internal.getWidth(r)/2)+" "+zi(Bt.internal.getHeight(r)/2)+" cm"),t.push(i+" 0 m"),t.push(i+" "+c+" "+c+" "+i+" 0 "+i+" c"),t.push("-"+c+" "+i+" -"+i+" "+c+" -"+i+" 0 c"),t.push("-"+i+" -"+c+" -"+c+" -"+i+" 0 -"+i+" c"),t.push(c+" -"+i+" "+i+" -"+c+" "+i+" 0 c"),t.push("f"),t.push("Q"),n.stream=t.join(`
`),n},OffPushDown:function(r){var n=ri(r);n.scope=r.scope;var t=[],i=Bt.internal.getWidth(r)<=Bt.internal.getHeight(r)?Bt.internal.getWidth(r)/4:Bt.internal.getHeight(r)/4;i=Number((.9*i).toFixed(5));var s=Number((2*i).toFixed(5)),a=Number((s*Bt.internal.Bezier_C).toFixed(5));return t.push("0.749023 g"),t.push("q"),t.push("1 0 0 1 "+zi(Bt.internal.getWidth(r)/2)+" "+zi(Bt.internal.getHeight(r)/2)+" cm"),t.push(s+" 0 m"),t.push(s+" "+a+" "+a+" "+s+" 0 "+s+" c"),t.push("-"+a+" "+s+" -"+s+" "+a+" -"+s+" 0 c"),t.push("-"+s+" -"+a+" -"+a+" -"+s+" 0 -"+s+" c"),t.push(a+" -"+s+" "+s+" -"+a+" "+s+" 0 c"),t.push("f"),t.push("Q"),n.stream=t.join(`
`),n}},Cross:{createAppearanceStream:function(r){var n={D:{Off:Bt.RadioButton.Cross.OffPushDown},N:{}};return n.N[r]=Bt.RadioButton.Cross.YesNormal,n.D[r]=Bt.RadioButton.Cross.YesPushDown,n},getCA:function(){return"8"},YesNormal:function(r){var n=ri(r);n.scope=r.scope;var t=[],i=Bt.internal.calculateCross(r);return t.push("q"),t.push("1 1 "+Zt(Bt.internal.getWidth(r)-2)+" "+Zt(Bt.internal.getHeight(r)-2)+" re"),t.push("W"),t.push("n"),t.push(Zt(i.x1.x)+" "+Zt(i.x1.y)+" m"),t.push(Zt(i.x2.x)+" "+Zt(i.x2.y)+" l"),t.push(Zt(i.x4.x)+" "+Zt(i.x4.y)+" m"),t.push(Zt(i.x3.x)+" "+Zt(i.x3.y)+" l"),t.push("s"),t.push("Q"),n.stream=t.join(`
`),n},YesPushDown:function(r){var n=ri(r);n.scope=r.scope;var t=Bt.internal.calculateCross(r),i=[];return i.push("0.749023 g"),i.push("0 0 "+Zt(Bt.internal.getWidth(r))+" "+Zt(Bt.internal.getHeight(r))+" re"),i.push("f"),i.push("q"),i.push("1 1 "+Zt(Bt.internal.getWidth(r)-2)+" "+Zt(Bt.internal.getHeight(r)-2)+" re"),i.push("W"),i.push("n"),i.push(Zt(t.x1.x)+" "+Zt(t.x1.y)+" m"),i.push(Zt(t.x2.x)+" "+Zt(t.x2.y)+" l"),i.push(Zt(t.x4.x)+" "+Zt(t.x4.y)+" m"),i.push(Zt(t.x3.x)+" "+Zt(t.x3.y)+" l"),i.push("s"),i.push("Q"),n.stream=i.join(`
`),n},OffPushDown:function(r){var n=ri(r);n.scope=r.scope;var t=[];return t.push("0.749023 g"),t.push("0 0 "+Zt(Bt.internal.getWidth(r))+" "+Zt(Bt.internal.getHeight(r))+" re"),t.push("f"),n.stream=t.join(`
`),n}}},createDefaultAppearanceStream:function(r){var n=r.scope.internal.getFont(r.fontName,r.fontStyle).id,t=r.scope.__private__.encodeColorString(r.color);return"/"+n+" "+r.fontSize+" Tf "+t}};Bt.internal={Bezier_C:.551915024494,calculateCross:function(r){var n=Bt.internal.getWidth(r),t=Bt.internal.getHeight(r),i=Math.min(n,t);return{x1:{x:(n-i)/2,y:(t-i)/2+i},x2:{x:(n-i)/2+i,y:(t-i)/2},x3:{x:(n-i)/2,y:(t-i)/2},x4:{x:(n-i)/2+i,y:(t-i)/2+i}}}},Bt.internal.getWidth=function(r){var n=0;return ye(r)==="object"&&(n=qc(r.Rect[2])),n},Bt.internal.getHeight=function(r){var n=0;return ye(r)==="object"&&(n=qc(r.Rect[3])),n};Kh=Fe.addField=function(r){if(Xh(this,r),!(r instanceof Gn))throw new Error("Invalid argument passed to jsPDF.addField.");var n;return(n=r).scope.internal.acroformPlugin.printedOut&&(n.scope.internal.acroformPlugin.printedOut=!1,n.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),n.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(n),r.page=r.scope.internal.getCurrentPageInfo().pageNumber,this};Fe.AcroFormChoiceField=ar,Fe.AcroFormListBox=sr,Fe.AcroFormComboBox=lr,Fe.AcroFormEditBox=Oo,Fe.AcroFormButton=Ge,Fe.AcroFormPushButton=Mo,Fe.AcroFormRadioButton=cr,Fe.AcroFormCheckBox=Fo,Fe.AcroFormTextField=Hi,Fe.AcroFormPasswordField=Io,Fe.AcroFormAppearance=Bt,Fe.AcroForm={ChoiceField:ar,ListBox:sr,ComboBox:lr,EditBox:Oo,Button:Ge,PushButton:Mo,RadioButton:cr,CheckBox:Fo,TextField:Hi,PasswordField:Io,Appearance:Bt},Wt.AcroForm={ChoiceField:ar,ListBox:sr,ComboBox:lr,EditBox:Oo,Button:Ge,PushButton:Mo,RadioButton:cr,CheckBox:Fo,TextField:Hi,PasswordField:Io,Appearance:Bt};Zh=Wt.AcroForm;(function(r){r.__addimage__={};var n="UNKNOWN",t={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},i=r.__addimage__.getImageFileTypeByImageData=function(N,A){var z,B,at,nt,ft,Z=n;if((A=A||n)==="RGBA"||N.data!==void 0&&N.data instanceof Uint8ClampedArray&&"height"in N&&"width"in N)return"RGBA";if(_t(N))for(ft in t)for(at=t[ft],z=0;z<at.length;z+=1){for(nt=!0,B=0;B<at[z].length;B+=1)if(at[z][B]!==void 0&&at[z][B]!==N[B]){nt=!1;break}if(nt===!0){Z=ft;break}}else for(ft in t)for(at=t[ft],z=0;z<at.length;z+=1){for(nt=!0,B=0;B<at[z].length;B+=1)if(at[z][B]!==void 0&&at[z][B]!==N.charCodeAt(B)){nt=!1;break}if(nt===!0){Z=ft;break}}return Z===n&&A!==n&&(Z=A),Z},s=function N(A){for(var z=this.internal.write,B=this.internal.putStream,at=(0,this.internal.getFilters)();at.indexOf("FlateEncode")!==-1;)at.splice(at.indexOf("FlateEncode"),1);A.objectId=this.internal.newObject();var nt=[];if(nt.push({key:"Type",value:"/XObject"}),nt.push({key:"Subtype",value:"/Image"}),nt.push({key:"Width",value:A.width}),nt.push({key:"Height",value:A.height}),A.colorSpace===D.INDEXED?nt.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(A.palette.length/3-1)+" "+("sMask"in A&&A.sMask!==void 0?A.objectId+2:A.objectId+1)+" 0 R]"}):(nt.push({key:"ColorSpace",value:"/"+A.colorSpace}),A.colorSpace===D.DEVICE_CMYK&&nt.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),nt.push({key:"BitsPerComponent",value:A.bitsPerComponent}),"decodeParameters"in A&&A.decodeParameters!==void 0&&nt.push({key:"DecodeParms",value:"<<"+A.decodeParameters+">>"}),"transparency"in A&&Array.isArray(A.transparency)){for(var ft="",Z=0,pt=A.transparency.length;Z<pt;Z++)ft+=A.transparency[Z]+" "+A.transparency[Z]+" ";nt.push({key:"Mask",value:"["+ft+"]"})}A.sMask!==void 0&&nt.push({key:"SMask",value:A.objectId+1+" 0 R"});var dt=A.filter!==void 0?["/"+A.filter]:void 0;if(B({data:A.data,additionalKeyValues:nt,alreadyAppliedFilters:dt,objectId:A.objectId}),z("endobj"),"sMask"in A&&A.sMask!==void 0){var Mt="/Predictor "+A.predictor+" /Colors 1 /BitsPerComponent "+A.bitsPerComponent+" /Columns "+A.width,x={width:A.width,height:A.height,colorSpace:"DeviceGray",bitsPerComponent:A.bitsPerComponent,decodeParameters:Mt,data:A.sMask};"filter"in A&&(x.filter=A.filter),N.call(this,x)}if(A.colorSpace===D.INDEXED){var F=this.internal.newObject();B({data:R(new Uint8Array(A.palette)),objectId:F}),z("endobj")}},a=function(){var N=this.internal.collections.addImage_images;for(var A in N)s.call(this,N[A])},c=function(){var N,A=this.internal.collections.addImage_images,z=this.internal.write;for(var B in A)z("/I"+(N=A[B]).index,N.objectId,"0","R")},h=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",a),this.internal.events.subscribe("putXobjectDict",c))},f=function(){var N=this.internal.collections.addImage_images;return h.call(this),N},g=function(){return Object.keys(this.internal.collections.addImage_images).length},b=function(N){return typeof r["process"+N.toUpperCase()]=="function"},C=function(N){return ye(N)==="object"&&N.nodeType===1},P=function(N,A){if(N.nodeName==="IMG"&&N.hasAttribute("src")){var z=""+N.getAttribute("src");if(z.indexOf("data:image/")===0)return No(unescape(z).split("base64,").pop());var B=r.loadFile(z,!0);if(B!==void 0)return B}if(N.nodeName==="CANVAS"){if(N.width===0||N.height===0)throw new Error("Given canvas must have data. Canvas width: "+N.width+", height: "+N.height);var at;switch(A){case"PNG":at="image/png";break;case"WEBP":at="image/webp";break;case"JPEG":case"JPG":default:at="image/jpeg"}return No(N.toDataURL(at,1).split("base64,").pop())}},p=function(N){var A=this.internal.collections.addImage_images;if(A){for(var z in A)if(N===A[z].alias)return A[z]}},I=function(N,A,z){return N||A||(N=-96,A=-96),N<0&&(N=-1*z.width*72/N/this.internal.scaleFactor),A<0&&(A=-1*z.height*72/A/this.internal.scaleFactor),N===0&&(N=A*z.width/z.height),A===0&&(A=N*z.height/z.width),[N,A]},O=function(N,A,z,B,at,nt){var ft=I.call(this,z,B,at),Z=this.internal.getCoordinateString,pt=this.internal.getVerticalCoordinateString,dt=f.call(this);if(z=ft[0],B=ft[1],dt[at.index]=at,nt){nt*=Math.PI/180;var Mt=Math.cos(nt),x=Math.sin(nt),F=function(H){return H.toFixed(4)},T=[F(Mt),F(x),F(-1*x),F(Mt),0,0,"cm"]}this.internal.write("q"),nt?(this.internal.write([1,"0","0",1,Z(N),pt(A+B),"cm"].join(" ")),this.internal.write(T.join(" ")),this.internal.write([Z(z),"0","0",Z(B),"0","0","cm"].join(" "))):this.internal.write([Z(z),"0","0",Z(B),Z(N),pt(A+B),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+at.index+" Do"),this.internal.write("Q")},D=r.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};r.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var k=r.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},j=r.__addimage__.sHashCode=function(N){var A,z,B=0;if(typeof N=="string")for(z=N.length,A=0;A<z;A++)B=(B<<5)-B+N.charCodeAt(A),B|=0;else if(_t(N))for(z=N.byteLength/2,A=0;A<z;A++)B=(B<<5)-B+N[A],B|=0;return B},J=r.__addimage__.validateStringAsBase64=function(N){(N=N||"").toString().trim();var A=!0;return N.length===0&&(A=!1),N.length%4!=0&&(A=!1),/^[A-Za-z0-9+/]+$/.test(N.substr(0,N.length-2))===!1&&(A=!1),/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(N.substr(-2))===!1&&(A=!1),A},lt=r.__addimage__.extractImageFromDataUrl=function(N){var A=(N=N||"").split("base64,"),z=null;if(A.length===2){var B=/^data:(\w*\/\w*);*(charset=(?!charset=)[\w=-]*)*;*$/.exec(A[0]);Array.isArray(B)&&(z={mimeType:B[1],charset:B[2],data:A[1]})}return z},ht=r.__addimage__.supportsArrayBuffer=function(){return typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"};r.__addimage__.isArrayBuffer=function(N){return ht()&&N instanceof ArrayBuffer};var _t=r.__addimage__.isArrayBufferView=function(N){return ht()&&typeof Uint32Array<"u"&&(N instanceof Int8Array||N instanceof Uint8Array||typeof Uint8ClampedArray<"u"&&N instanceof Uint8ClampedArray||N instanceof Int16Array||N instanceof Uint16Array||N instanceof Int32Array||N instanceof Uint32Array||N instanceof Float32Array||N instanceof Float64Array)},tt=r.__addimage__.binaryStringToUint8Array=function(N){for(var A=N.length,z=new Uint8Array(A),B=0;B<A;B++)z[B]=N.charCodeAt(B);return z},R=r.__addimage__.arrayBufferToBinaryString=function(N){for(var A="",z=_t(N)?N:new Uint8Array(N),B=0;B<z.length;B+=8192)A+=String.fromCharCode.apply(null,z.subarray(B,B+8192));return A};r.addImage=function(){var N,A,z,B,at,nt,ft,Z,pt;if(typeof arguments[1]=="number"?(A=n,z=arguments[1],B=arguments[2],at=arguments[3],nt=arguments[4],ft=arguments[5],Z=arguments[6],pt=arguments[7]):(A=arguments[1],z=arguments[2],B=arguments[3],at=arguments[4],nt=arguments[5],ft=arguments[6],Z=arguments[7],pt=arguments[8]),ye(N=arguments[0])==="object"&&!C(N)&&"imageData"in N){var dt=N;N=dt.imageData,A=dt.format||A||n,z=dt.x||z||0,B=dt.y||B||0,at=dt.w||dt.width||at,nt=dt.h||dt.height||nt,ft=dt.alias||ft,Z=dt.compression||Z,pt=dt.rotation||dt.angle||pt}var Mt=this.internal.getFilters();if(Z===void 0&&Mt.indexOf("FlateEncode")!==-1&&(Z="SLOW"),isNaN(z)||isNaN(B))throw new Error("Invalid coordinates passed to jsPDF.addImage");h.call(this);var x=vt.call(this,N,A,ft,Z);return O.call(this,z,B,at,nt,x,pt),this};var vt=function(N,A,z,B){var at,nt,ft;if(typeof N=="string"&&i(N)===n){N=unescape(N);var Z=gt(N,!1);(Z!==""||(Z=r.loadFile(N,!0))!==void 0)&&(N=Z)}if(C(N)&&(N=P(N,A)),A=i(N,A),!b(A))throw new Error("addImage does not support files of type '"+A+"', please ensure that a plugin for '"+A+"' support is added.");if(((ft=z)==null||ft.length===0)&&(z=function(pt){return typeof pt=="string"||_t(pt)?j(pt):_t(pt.data)?j(pt.data):null}(N)),(at=p.call(this,z))||(ht()&&(N instanceof Uint8Array||A==="RGBA"||(nt=N,N=tt(N))),at=this["process"+A.toUpperCase()](N,g.call(this),z,function(pt){return pt&&typeof pt=="string"&&(pt=pt.toUpperCase()),pt in r.image_compression?pt:k.NONE}(B),nt)),!at)throw new Error("An unknown error occurred whilst processing the image.");return at},gt=r.__addimage__.convertBase64ToBinaryString=function(N,A){var z;A=typeof A!="boolean"||A;var B,at="";if(typeof N=="string"){B=(z=lt(N))!==null?z.data:N;try{at=No(B)}catch(nt){if(A)throw J(B)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+nt.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return at};r.getImageProperties=function(N){var A,z,B="";if(C(N)&&(N=P(N)),typeof N=="string"&&i(N)===n&&((B=gt(N,!1))===""&&(B=r.loadFile(N)||""),N=B),z=i(N),!b(z))throw new Error("addImage does not support files of type '"+z+"', please ensure that a plugin for '"+z+"' support is added.");if(!ht()||N instanceof Uint8Array||(N=tt(N)),!(A=this["process"+z.toUpperCase()](N)))throw new Error("An unknown error occurred whilst processing the image");return A.fileType=z,A}})(Wt.API),function(r){var n=function(t){if(t!==void 0&&t!="")return!0};Wt.API.events.push(["addPage",function(t){this.internal.getPageInfo(t.pageNumber).pageContext.annotations=[]}]),r.events.push(["putPage",function(t){for(var i,s,a,c=this.internal.getCoordinateString,h=this.internal.getVerticalCoordinateString,f=this.internal.getPageInfoByObjId(t.objId),g=t.pageContext.annotations,b=!1,C=0;C<g.length&&!b;C++)switch((i=g[C]).type){case"link":(n(i.options.url)||n(i.options.pageNumber))&&(b=!0);break;case"reference":case"text":case"freetext":b=!0}if(b!=0){this.internal.write("/Annots [");for(var P=0;P<g.length;P++){i=g[P];var p=this.internal.pdfEscape,I=this.internal.getEncryptor(t.objId);switch(i.type){case"reference":this.internal.write(" "+i.object.objId+" 0 R ");break;case"text":var O=this.internal.newAdditionalObject(),D=this.internal.newAdditionalObject(),k=this.internal.getEncryptor(O.objId),j=i.title||"Note";a="<</Type /Annot /Subtype /Text "+(s="/Rect ["+c(i.bounds.x)+" "+h(i.bounds.y+i.bounds.h)+" "+c(i.bounds.x+i.bounds.w)+" "+h(i.bounds.y)+"] ")+"/Contents ("+p(k(i.contents))+")",a+=" /Popup "+D.objId+" 0 R",a+=" /P "+f.objId+" 0 R",a+=" /T ("+p(k(j))+") >>",O.content=a;var J=O.objId+" 0 R";a="<</Type /Annot /Subtype /Popup "+(s="/Rect ["+c(i.bounds.x+30)+" "+h(i.bounds.y+i.bounds.h)+" "+c(i.bounds.x+i.bounds.w+30)+" "+h(i.bounds.y)+"] ")+" /Parent "+J,i.open&&(a+=" /Open true"),a+=" >>",D.content=a,this.internal.write(O.objId,"0 R",D.objId,"0 R");break;case"freetext":s="/Rect ["+c(i.bounds.x)+" "+h(i.bounds.y)+" "+c(i.bounds.x+i.bounds.w)+" "+h(i.bounds.y+i.bounds.h)+"] ";var lt=i.color||"#000000";a="<</Type /Annot /Subtype /FreeText "+s+"/Contents ("+p(I(i.contents))+")",a+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+lt+")",a+=" /Border [0 0 0]",a+=" >>",this.internal.write(a);break;case"link":if(i.options.name){var ht=this.annotations._nameMap[i.options.name];i.options.pageNumber=ht.page,i.options.top=ht.y}else i.options.top||(i.options.top=0);if(s="/Rect ["+i.finalBounds.x+" "+i.finalBounds.y+" "+i.finalBounds.w+" "+i.finalBounds.h+"] ",a="",i.options.url)a="<</Type /Annot /Subtype /Link "+s+"/Border [0 0 0] /A <</S /URI /URI ("+p(I(i.options.url))+") >>";else if(i.options.pageNumber)switch(a="<</Type /Annot /Subtype /Link "+s+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(i.options.pageNumber).objId+" 0 R",i.options.magFactor=i.options.magFactor||"XYZ",i.options.magFactor){case"Fit":a+=" /Fit]";break;case"FitH":a+=" /FitH "+i.options.top+"]";break;case"FitV":i.options.left=i.options.left||0,a+=" /FitV "+i.options.left+"]";break;case"XYZ":default:var _t=h(i.options.top);i.options.left=i.options.left||0,i.options.zoom===void 0&&(i.options.zoom=0),a+=" /XYZ "+i.options.left+" "+_t+" "+i.options.zoom+"]"}a!=""&&(a+=" >>",this.internal.write(a))}}this.internal.write("]")}}]),r.createAnnotation=function(t){var i=this.internal.getCurrentPageInfo();switch(t.type){case"link":this.link(t.bounds.x,t.bounds.y,t.bounds.w,t.bounds.h,t);break;case"text":case"freetext":i.pageContext.annotations.push(t)}},r.link=function(t,i,s,a,c){var h=this.internal.getCurrentPageInfo(),f=this.internal.getCoordinateString,g=this.internal.getVerticalCoordinateString;h.pageContext.annotations.push({finalBounds:{x:f(t),y:g(i),w:f(t+s),h:g(i+a)},options:c,type:"link"})},r.textWithLink=function(t,i,s,a){var c,h,f=this.getTextWidth(t),g=this.internal.getLineHeight()/this.internal.scaleFactor;if(a.maxWidth!==void 0){h=a.maxWidth;var b=this.splitTextToSize(t,h).length;c=Math.ceil(g*b)}else h=f,c=g;return this.text(t,i,s,a),s+=.2*g,a.align==="center"&&(i-=f/2),a.align==="right"&&(i-=f),this.link(i,s-g,h,c,a),f},r.getTextWidth=function(t){var i=this.internal.getFontSize();return this.getStringUnitWidth(t)*i/this.internal.scaleFactor}}(Wt.API),function(r){var n={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},t={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},i={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},s=[1570,1571,1573,1575];r.__arabicParser__={};var a=r.__arabicParser__.isInArabicSubstitutionA=function(O){return n[O.charCodeAt(0)]!==void 0},c=r.__arabicParser__.isArabicLetter=function(O){return typeof O=="string"&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(O)},h=r.__arabicParser__.isArabicEndLetter=function(O){return c(O)&&a(O)&&n[O.charCodeAt(0)].length<=2},f=r.__arabicParser__.isArabicAlfLetter=function(O){return c(O)&&s.indexOf(O.charCodeAt(0))>=0};r.__arabicParser__.arabicLetterHasIsolatedForm=function(O){return c(O)&&a(O)&&n[O.charCodeAt(0)].length>=1};var g=r.__arabicParser__.arabicLetterHasFinalForm=function(O){return c(O)&&a(O)&&n[O.charCodeAt(0)].length>=2};r.__arabicParser__.arabicLetterHasInitialForm=function(O){return c(O)&&a(O)&&n[O.charCodeAt(0)].length>=3};var b=r.__arabicParser__.arabicLetterHasMedialForm=function(O){return c(O)&&a(O)&&n[O.charCodeAt(0)].length==4},C=r.__arabicParser__.resolveLigatures=function(O){var D=0,k=t,j="",J=0;for(D=0;D<O.length;D+=1)k[O.charCodeAt(D)]!==void 0?(J++,typeof(k=k[O.charCodeAt(D)])=="number"&&(j+=String.fromCharCode(k),k=t,J=0),D===O.length-1&&(k=t,j+=O.charAt(D-(J-1)),D-=J-1,J=0)):(k=t,j+=O.charAt(D-J),D-=J,J=0);return j};r.__arabicParser__.isArabicDiacritic=function(O){return O!==void 0&&i[O.charCodeAt(0)]!==void 0};var P=r.__arabicParser__.getCorrectForm=function(O,D,k){return c(O)?a(O)===!1?-1:!g(O)||!c(D)&&!c(k)||!c(k)&&h(D)||h(O)&&!c(D)||h(O)&&f(D)||h(O)&&h(D)?0:b(O)&&c(D)&&!h(D)&&c(k)&&g(k)?3:h(O)||!c(k)?1:2:-1},p=function(O){var D=0,k=0,j=0,J="",lt="",ht="",_t=(O=O||"").split("\\s+"),tt=[];for(D=0;D<_t.length;D+=1){for(tt.push(""),k=0;k<_t[D].length;k+=1)J=_t[D][k],lt=_t[D][k-1],ht=_t[D][k+1],c(J)?(j=P(J,lt,ht),tt[D]+=j!==-1?String.fromCharCode(n[J.charCodeAt(0)][j]):J):tt[D]+=J;tt[D]=C(tt[D])}return tt.join(" ")},I=r.__arabicParser__.processArabic=r.processArabic=function(){var O,D=typeof arguments[0]=="string"?arguments[0]:arguments[0].text,k=[];if(Array.isArray(D)){var j=0;for(k=[],j=0;j<D.length;j+=1)Array.isArray(D[j])?k.push([p(D[j][0]),D[j][1],D[j][2]]):k.push([p(D[j])]);O=k}else O=p(D);return typeof arguments[0]=="string"?O:(arguments[0].text=O,arguments[0])};r.events.push(["preProcessText",I])}(Wt.API),Wt.API.autoPrint=function(r){var n;switch((r=r||{}).variant=r.variant||"non-conform",r.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){n=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+n+" 0 R")})}return this},function(r){var n=function(){var t=void 0;Object.defineProperty(this,"pdf",{get:function(){return t},set:function(h){t=h}});var i=150;Object.defineProperty(this,"width",{get:function(){return i},set:function(h){i=isNaN(h)||Number.isInteger(h)===!1||h<0?150:h,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=i+1)}});var s=300;Object.defineProperty(this,"height",{get:function(){return s},set:function(h){s=isNaN(h)||Number.isInteger(h)===!1||h<0?300:h,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=s+1)}});var a=[];Object.defineProperty(this,"childNodes",{get:function(){return a},set:function(h){a=h}});var c={};Object.defineProperty(this,"style",{get:function(){return c},set:function(h){c=h}}),Object.defineProperty(this,"parentNode",{})};n.prototype.getContext=function(t,i){var s;if((t=t||"2d")!=="2d")return null;for(s in i)this.pdf.context2d.hasOwnProperty(s)&&(this.pdf.context2d[s]=i[s]);return this.pdf.context2d._canvas=this,this.pdf.context2d},n.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},r.events.push(["initialized",function(){this.canvas=new n,this.canvas.pdf=this}])}(Wt.API),function(r){var n={left:0,top:0,bottom:0,right:0},t=!1,i=function(){this.internal.__cell__===void 0&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},n),this.internal.__cell__.margins.width=this.getPageWidth(),s.call(this))},s=function(){this.internal.__cell__.lastCell=new a,this.internal.__cell__.pages=1},a=function(){var f=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return f},set:function(O){f=O}});var g=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return g},set:function(O){g=O}});var b=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return b},set:function(O){b=O}});var C=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return C},set:function(O){C=O}});var P=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return P},set:function(O){P=O}});var p=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return p},set:function(O){p=O}});var I=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return I},set:function(O){I=O}}),this};a.prototype.clone=function(){return new a(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},a.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},r.setHeaderFunction=function(f){return i.call(this),this.internal.__cell__.headerFunction=typeof f=="function"?f:void 0,this},r.getTextDimensions=function(f,g){i.call(this);var b=(g=g||{}).fontSize||this.getFontSize(),C=g.font||this.getFont(),P=g.scaleFactor||this.internal.scaleFactor,p=0,I=0,O=0,D=this;if(!Array.isArray(f)&&typeof f!="string"){if(typeof f!="number")throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");f=String(f)}var k=g.maxWidth;k>0?typeof f=="string"?f=this.splitTextToSize(f,k):Object.prototype.toString.call(f)==="[object Array]"&&(f=f.reduce(function(J,lt){return J.concat(D.splitTextToSize(lt,k))},[])):f=Array.isArray(f)?f:[f];for(var j=0;j<f.length;j++)p<(O=this.getStringUnitWidth(f[j],{font:C})*b)&&(p=O);return p!==0&&(I=f.length),{w:p/=P,h:Math.max((I*b*this.getLineHeightFactor()-b*(this.getLineHeightFactor()-1))/P,0)}},r.cellAddPage=function(){i.call(this),this.addPage();var f=this.internal.__cell__.margins||n;return this.internal.__cell__.lastCell=new a(f.left,f.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var c=r.cell=function(){var f;f=arguments[0]instanceof a?arguments[0]:new a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),i.call(this);var g=this.internal.__cell__.lastCell,b=this.internal.__cell__.padding,C=this.internal.__cell__.margins||n,P=this.internal.__cell__.tableHeaderRow,p=this.internal.__cell__.printHeaders;return g.lineNumber!==void 0&&(g.lineNumber===f.lineNumber?(f.x=(g.x||0)+(g.width||0),f.y=g.y||0):g.y+g.height+f.height+C.bottom>this.getPageHeight()?(this.cellAddPage(),f.y=C.top,p&&P&&(this.printHeaderRow(f.lineNumber,!0),f.y+=P[0].height)):f.y=g.y+g.height||f.y),f.text[0]!==void 0&&(this.rect(f.x,f.y,f.width,f.height,t===!0?"FD":void 0),f.align==="right"?this.text(f.text,f.x+f.width-b,f.y+b,{align:"right",baseline:"top"}):f.align==="center"?this.text(f.text,f.x+f.width/2,f.y+b,{align:"center",baseline:"top",maxWidth:f.width-b-b}):this.text(f.text,f.x+b,f.y+b,{align:"left",baseline:"top",maxWidth:f.width-b-b})),this.internal.__cell__.lastCell=f,this};r.table=function(f,g,b,C,P){if(i.call(this),!b)throw new Error("No data for PDF table.");var p,I,O,D,k=[],j=[],J=[],lt={},ht={},_t=[],tt=[],R=(P=P||{}).autoSize||!1,vt=P.printHeaders!==!1,gt=P.css&&P.css["font-size"]!==void 0?16*P.css["font-size"]:P.fontSize||12,N=P.margins||Object.assign({width:this.getPageWidth()},n),A=typeof P.padding=="number"?P.padding:3,z=P.headerBackgroundColor||"#c8c8c8",B=P.headerTextColor||"#000";if(s.call(this),this.internal.__cell__.printHeaders=vt,this.internal.__cell__.margins=N,this.internal.__cell__.table_font_size=gt,this.internal.__cell__.padding=A,this.internal.__cell__.headerBackgroundColor=z,this.internal.__cell__.headerTextColor=B,this.setFontSize(gt),C==null)j=k=Object.keys(b[0]),J=k.map(function(){return"left"});else if(Array.isArray(C)&&ye(C[0])==="object")for(k=C.map(function(dt){return dt.name}),j=C.map(function(dt){return dt.prompt||dt.name||""}),J=C.map(function(dt){return dt.align||"left"}),p=0;p<C.length;p+=1)ht[C[p].name]=C[p].width*(19.049976/25.4);else Array.isArray(C)&&typeof C[0]=="string"&&(j=k=C,J=k.map(function(){return"left"}));if(R||Array.isArray(C)&&typeof C[0]=="string")for(p=0;p<k.length;p+=1){for(lt[D=k[p]]=b.map(function(dt){return dt[D]}),this.setFont(void 0,"bold"),_t.push(this.getTextDimensions(j[p],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),I=lt[D],this.setFont(void 0,"normal"),O=0;O<I.length;O+=1)_t.push(this.getTextDimensions(I[O],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);ht[D]=Math.max.apply(null,_t)+A+A,_t=[]}if(vt){var at={};for(p=0;p<k.length;p+=1)at[k[p]]={},at[k[p]].text=j[p],at[k[p]].align=J[p];var nt=h.call(this,at,ht);tt=k.map(function(dt){return new a(f,g,ht[dt],nt,at[dt].text,void 0,at[dt].align)}),this.setTableHeaderRow(tt),this.printHeaderRow(1,!1)}var ft=C.reduce(function(dt,Mt){return dt[Mt.name]=Mt.align,dt},{});for(p=0;p<b.length;p+=1){"rowStart"in P&&P.rowStart instanceof Function&&P.rowStart({row:p,data:b[p]},this);var Z=h.call(this,b[p],ht);for(O=0;O<k.length;O+=1){var pt=b[p][k[O]];"cellStart"in P&&P.cellStart instanceof Function&&P.cellStart({row:p,col:O,data:pt},this),c.call(this,new a(f,g,ht[k[O]],Z,pt,p+2,ft[k[O]]))}}return this.internal.__cell__.table_x=f,this.internal.__cell__.table_y=g,this};var h=function(f,g){var b=this.internal.__cell__.padding,C=this.internal.__cell__.table_font_size,P=this.internal.scaleFactor;return Object.keys(f).map(function(p){var I=f[p];return this.splitTextToSize(I.hasOwnProperty("text")?I.text:I,g[p]-b-b)},this).map(function(p){return this.getLineHeightFactor()*p.length*C/P+b+b},this).reduce(function(p,I){return Math.max(p,I)},0)};r.setTableHeaderRow=function(f){i.call(this),this.internal.__cell__.tableHeaderRow=f},r.printHeaderRow=function(f,g){if(i.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var b;if(t=!0,typeof this.internal.__cell__.headerFunction=="function"){var C=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new a(C[0],C[1],C[2],C[3],void 0,-1)}this.setFont(void 0,"bold");for(var P=[],p=0;p<this.internal.__cell__.tableHeaderRow.length;p+=1){b=this.internal.__cell__.tableHeaderRow[p].clone(),g&&(b.y=this.internal.__cell__.margins.top||0,P.push(b)),b.lineNumber=f;var I=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),c.call(this,b),this.setTextColor(I)}P.length>0&&this.setTableHeaderRow(P),this.setFont(void 0,"normal"),t=!1}}(Wt.API);au={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},su=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],nl=ou(su),lu=[100,200,300,400,500,600,700,800,900],Qh=ou(lu);t1={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},Uc={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};Xs=["times"];(function(r){var n,t,i,s,a,c,h,f,g,b=function(x){return x=x||{},this.isStrokeTransparent=x.isStrokeTransparent||!1,this.strokeOpacity=x.strokeOpacity||1,this.strokeStyle=x.strokeStyle||"#000000",this.fillStyle=x.fillStyle||"#000000",this.isFillTransparent=x.isFillTransparent||!1,this.fillOpacity=x.fillOpacity||1,this.font=x.font||"10px sans-serif",this.textBaseline=x.textBaseline||"alphabetic",this.textAlign=x.textAlign||"left",this.lineWidth=x.lineWidth||1,this.lineJoin=x.lineJoin||"miter",this.lineCap=x.lineCap||"butt",this.path=x.path||[],this.transform=x.transform!==void 0?x.transform.clone():new f,this.globalCompositeOperation=x.globalCompositeOperation||"normal",this.globalAlpha=x.globalAlpha||1,this.clip_path=x.clip_path||[],this.currentPoint=x.currentPoint||new c,this.miterLimit=x.miterLimit||10,this.lastPoint=x.lastPoint||new c,this.lineDashOffset=x.lineDashOffset||0,this.lineDash=x.lineDash||[],this.margin=x.margin||[0,0,0,0],this.prevPageLastElemOffset=x.prevPageLastElemOffset||0,this.ignoreClearRect=typeof x.ignoreClearRect!="boolean"||x.ignoreClearRect,this};r.events.push(["initialized",function(){this.context2d=new C(this),n=this.internal.f2,t=this.internal.getCoordinateString,i=this.internal.getVerticalCoordinateString,s=this.internal.getHorizontalCoordinate,a=this.internal.getVerticalCoordinate,c=this.internal.Point,h=this.internal.Rectangle,f=this.internal.Matrix,g=new b}]);var C=function(x){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var F=x;Object.defineProperty(this,"pdf",{get:function(){return F}});var T=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return T},set:function(ut){T=!!ut}});var H=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return H},set:function(ut){H=!!ut}});var $=0;Object.defineProperty(this,"posX",{get:function(){return $},set:function(ut){isNaN(ut)||($=ut)}});var Q=0;Object.defineProperty(this,"posY",{get:function(){return Q},set:function(ut){isNaN(ut)||(Q=ut)}}),Object.defineProperty(this,"margin",{get:function(){return g.margin},set:function(ut){var E;typeof ut=="number"?E=[ut,ut,ut,ut]:((E=new Array(4))[0]=ut[0],E[1]=ut.length>=2?ut[1]:E[0],E[2]=ut.length>=3?ut[2]:E[0],E[3]=ut.length>=4?ut[3]:E[1]),g.margin=E}});var et=!1;Object.defineProperty(this,"autoPaging",{get:function(){return et},set:function(ut){et=ut}});var it=0;Object.defineProperty(this,"lastBreak",{get:function(){return it},set:function(ut){it=ut}});var kt=[];Object.defineProperty(this,"pageBreaks",{get:function(){return kt},set:function(ut){kt=ut}}),Object.defineProperty(this,"ctx",{get:function(){return g},set:function(ut){ut instanceof b&&(g=ut)}}),Object.defineProperty(this,"path",{get:function(){return g.path},set:function(ut){g.path=ut}});var Pt=[];Object.defineProperty(this,"ctxStack",{get:function(){return Pt},set:function(ut){Pt=ut}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(ut){var E;E=P(ut),this.ctx.fillStyle=E.style,this.ctx.isFillTransparent=E.a===0,this.ctx.fillOpacity=E.a,this.pdf.setFillColor(E.r,E.g,E.b,{a:E.a}),this.pdf.setTextColor(E.r,E.g,E.b,{a:E.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(ut){var E=P(ut);this.ctx.strokeStyle=E.style,this.ctx.isStrokeTransparent=E.a===0,this.ctx.strokeOpacity=E.a,E.a===0?this.pdf.setDrawColor(255,255,255):(E.a,this.pdf.setDrawColor(E.r,E.g,E.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(ut){["butt","round","square"].indexOf(ut)!==-1&&(this.ctx.lineCap=ut,this.pdf.setLineCap(ut))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(ut){isNaN(ut)||(this.ctx.lineWidth=ut,this.pdf.setLineWidth(ut))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(ut){["bevel","round","miter"].indexOf(ut)!==-1&&(this.ctx.lineJoin=ut,this.pdf.setLineJoin(ut))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(ut){isNaN(ut)||(this.ctx.miterLimit=ut,this.pdf.setMiterLimit(ut))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(ut){this.ctx.textBaseline=ut}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(ut){["right","end","center","left","start"].indexOf(ut)!==-1&&(this.ctx.textAlign=ut)}});var jt=null;function Dt(ut,E){if(jt===null){var Qt=function(Rt){var Lt=[];return Object.keys(Rt).forEach(function(At){Rt[At].forEach(function(Ft){var Ot=null;switch(Ft){case"bold":Ot={family:At,weight:"bold"};break;case"italic":Ot={family:At,style:"italic"};break;case"bolditalic":Ot={family:At,weight:"bold",style:"italic"};break;case"":case"normal":Ot={family:At}}Ot!==null&&(Ot.ref={name:At,style:Ft},Lt.push(Ot))})}),Lt}(ut.getFontList());jt=function(Rt){for(var Lt={},At=0;At<Rt.length;++At){var Ft=il(Rt[At]),Ot=Ft.family,qt=Ft.stretch,Jt=Ft.style,ne=Ft.weight;Lt[Ot]=Lt[Ot]||{},Lt[Ot][qt]=Lt[Ot][qt]||{},Lt[Ot][qt][Jt]=Lt[Ot][qt][Jt]||{},Lt[Ot][qt][Jt][ne]=Ft}return Lt}(Qt.concat(E))}return jt}var Gt=null;Object.defineProperty(this,"fontFaces",{get:function(){return Gt},set:function(ut){jt=null,Gt=ut}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(ut){var E;if(this.ctx.font=ut,(E=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(ut))!==null){var Qt=E[1],Rt=(E[2],E[3]),Lt=E[4],At=(E[5],E[6]),Ft=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(Lt)[2];Lt=Math.floor(Ft==="px"?parseFloat(Lt)*this.pdf.internal.scaleFactor:Ft==="em"?parseFloat(Lt)*this.pdf.getFontSize():parseFloat(Lt)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(Lt);var Ot=function($t){var oe,It,Ke=[],ue=$t.trim();if(ue==="")return Xs;if(ue in Uc)return[Uc[ue]];for(;ue!=="";){switch(It=null,oe=(ue=Hc(ue)).charAt(0)){case'"':case"'":It=n1(ue.substring(1),oe);break;default:It=i1(ue)}if(It===null||(Ke.push(It[0]),(ue=Hc(It[1]))!==""&&ue.charAt(0)!==","))return Xs;ue=ue.replace(/^,/,"")}return Ke}(At);if(this.fontFaces){var qt=e1(Dt(this.pdf,this.fontFaces),Ot.map(function($t){return{family:$t,stretch:"normal",weight:Rt,style:Qt}}));this.pdf.setFont(qt.ref.name,qt.ref.style)}else{var Jt="";(Rt==="bold"||parseInt(Rt,10)>=700||Qt==="bold")&&(Jt="bold"),Qt==="italic"&&(Jt+="italic"),Jt.length===0&&(Jt="normal");for(var ne="",re={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},le=0;le<Ot.length;le++){if(this.pdf.internal.getFont(Ot[le],Jt,{noFallback:!0,disableWarning:!0})!==void 0){ne=Ot[le];break}if(Jt==="bolditalic"&&this.pdf.internal.getFont(Ot[le],"bold",{noFallback:!0,disableWarning:!0})!==void 0)ne=Ot[le],Jt="bold";else if(this.pdf.internal.getFont(Ot[le],"normal",{noFallback:!0,disableWarning:!0})!==void 0){ne=Ot[le],Jt="normal";break}}if(ne===""){for(var ve=0;ve<Ot.length;ve++)if(re[Ot[ve]]){ne=re[Ot[ve]];break}}ne=ne===""?"Times":ne,this.pdf.setFont(ne,Jt)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(ut){this.ctx.globalCompositeOperation=ut}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(ut){this.ctx.globalAlpha=ut}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(ut){this.ctx.lineDashOffset=ut,Mt.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(ut){this.ctx.lineDash=ut,Mt.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(ut){this.ctx.ignoreClearRect=!!ut}})};C.prototype.setLineDash=function(x){this.lineDash=x},C.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},C.prototype.fill=function(){lt.call(this,"fill",!1)},C.prototype.stroke=function(){lt.call(this,"stroke",!1)},C.prototype.beginPath=function(){this.path=[{type:"begin"}]},C.prototype.moveTo=function(x,F){if(isNaN(x)||isNaN(F))throw _e.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var T=this.ctx.transform.applyToPoint(new c(x,F));this.path.push({type:"mt",x:T.x,y:T.y}),this.ctx.lastPoint=new c(x,F)},C.prototype.closePath=function(){var x=new c(0,0),F=0;for(F=this.path.length-1;F!==-1;F--)if(this.path[F].type==="begin"&&ye(this.path[F+1])==="object"&&typeof this.path[F+1].x=="number"){x=new c(this.path[F+1].x,this.path[F+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new c(x.x,x.y)},C.prototype.lineTo=function(x,F){if(isNaN(x)||isNaN(F))throw _e.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var T=this.ctx.transform.applyToPoint(new c(x,F));this.path.push({type:"lt",x:T.x,y:T.y}),this.ctx.lastPoint=new c(T.x,T.y)},C.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),lt.call(this,null,!0)},C.prototype.quadraticCurveTo=function(x,F,T,H){if(isNaN(T)||isNaN(H)||isNaN(x)||isNaN(F))throw _e.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var $=this.ctx.transform.applyToPoint(new c(T,H)),Q=this.ctx.transform.applyToPoint(new c(x,F));this.path.push({type:"qct",x1:Q.x,y1:Q.y,x:$.x,y:$.y}),this.ctx.lastPoint=new c($.x,$.y)},C.prototype.bezierCurveTo=function(x,F,T,H,$,Q){if(isNaN($)||isNaN(Q)||isNaN(x)||isNaN(F)||isNaN(T)||isNaN(H))throw _e.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var et=this.ctx.transform.applyToPoint(new c($,Q)),it=this.ctx.transform.applyToPoint(new c(x,F)),kt=this.ctx.transform.applyToPoint(new c(T,H));this.path.push({type:"bct",x1:it.x,y1:it.y,x2:kt.x,y2:kt.y,x:et.x,y:et.y}),this.ctx.lastPoint=new c(et.x,et.y)},C.prototype.arc=function(x,F,T,H,$,Q){if(isNaN(x)||isNaN(F)||isNaN(T)||isNaN(H)||isNaN($))throw _e.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(Q=!!Q,!this.ctx.transform.isIdentity){var et=this.ctx.transform.applyToPoint(new c(x,F));x=et.x,F=et.y;var it=this.ctx.transform.applyToPoint(new c(0,T)),kt=this.ctx.transform.applyToPoint(new c(0,0));T=Math.sqrt(Math.pow(it.x-kt.x,2)+Math.pow(it.y-kt.y,2))}Math.abs($-H)>=2*Math.PI&&(H=0,$=2*Math.PI),this.path.push({type:"arc",x,y:F,radius:T,startAngle:H,endAngle:$,counterclockwise:Q})},C.prototype.arcTo=function(x,F,T,H,$){throw new Error("arcTo not implemented.")},C.prototype.rect=function(x,F,T,H){if(isNaN(x)||isNaN(F)||isNaN(T)||isNaN(H))throw _e.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(x,F),this.lineTo(x+T,F),this.lineTo(x+T,F+H),this.lineTo(x,F+H),this.lineTo(x,F),this.lineTo(x+T,F),this.lineTo(x,F)},C.prototype.fillRect=function(x,F,T,H){if(isNaN(x)||isNaN(F)||isNaN(T)||isNaN(H))throw _e.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!p.call(this)){var $={};this.lineCap!=="butt"&&($.lineCap=this.lineCap,this.lineCap="butt"),this.lineJoin!=="miter"&&($.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(x,F,T,H),this.fill(),$.hasOwnProperty("lineCap")&&(this.lineCap=$.lineCap),$.hasOwnProperty("lineJoin")&&(this.lineJoin=$.lineJoin)}},C.prototype.strokeRect=function(x,F,T,H){if(isNaN(x)||isNaN(F)||isNaN(T)||isNaN(H))throw _e.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");I.call(this)||(this.beginPath(),this.rect(x,F,T,H),this.stroke())},C.prototype.clearRect=function(x,F,T,H){if(isNaN(x)||isNaN(F)||isNaN(T)||isNaN(H))throw _e.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(x,F,T,H))},C.prototype.save=function(x){x=typeof x!="boolean"||x;for(var F=this.pdf.internal.getCurrentPageInfo().pageNumber,T=0;T<this.pdf.internal.getNumberOfPages();T++)this.pdf.setPage(T+1),this.pdf.internal.out("q");if(this.pdf.setPage(F),x){this.ctx.fontSize=this.pdf.internal.getFontSize();var H=new b(this.ctx);this.ctxStack.push(this.ctx),this.ctx=H}},C.prototype.restore=function(x){x=typeof x!="boolean"||x;for(var F=this.pdf.internal.getCurrentPageInfo().pageNumber,T=0;T<this.pdf.internal.getNumberOfPages();T++)this.pdf.setPage(T+1),this.pdf.internal.out("Q");this.pdf.setPage(F),x&&this.ctxStack.length!==0&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},C.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var P=function(x){var F,T,H,$;if(x.isCanvasGradient===!0&&(x=x.getColor()),!x)return{r:0,g:0,b:0,a:0,style:x};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(x))F=0,T=0,H=0,$=0;else{var Q=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(x);if(Q!==null)F=parseInt(Q[1]),T=parseInt(Q[2]),H=parseInt(Q[3]),$=1;else if((Q=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(x))!==null)F=parseInt(Q[1]),T=parseInt(Q[2]),H=parseInt(Q[3]),$=parseFloat(Q[4]);else{if($=1,typeof x=="string"&&x.charAt(0)!=="#"){var et=new Qc(x);x=et.ok?et.toHex():"#000000"}x.length===4?(F=x.substring(1,2),F+=F,T=x.substring(2,3),T+=T,H=x.substring(3,4),H+=H):(F=x.substring(1,3),T=x.substring(3,5),H=x.substring(5,7)),F=parseInt(F,16),T=parseInt(T,16),H=parseInt(H,16)}}return{r:F,g:T,b:H,a:$,style:x}},p=function(){return this.ctx.isFillTransparent||this.globalAlpha==0},I=function(){return!!(this.ctx.isStrokeTransparent||this.globalAlpha==0)};C.prototype.fillText=function(x,F,T,H){if(isNaN(F)||isNaN(T)||typeof x!="string")throw _e.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(H=isNaN(H)?void 0:H,!p.call(this)){var $=Z(this.ctx.transform.rotation),Q=this.ctx.transform.scaleX;A.call(this,{text:x,x:F,y:T,scale:Q,angle:$,align:this.textAlign,maxWidth:H})}},C.prototype.strokeText=function(x,F,T,H){if(isNaN(F)||isNaN(T)||typeof x!="string")throw _e.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!I.call(this)){H=isNaN(H)?void 0:H;var $=Z(this.ctx.transform.rotation),Q=this.ctx.transform.scaleX;A.call(this,{text:x,x:F,y:T,scale:Q,renderingMode:"stroke",angle:$,align:this.textAlign,maxWidth:H})}},C.prototype.measureText=function(x){if(typeof x!="string")throw _e.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var F=this.pdf,T=this.pdf.internal.scaleFactor,H=F.internal.getFontSize(),$=F.getStringUnitWidth(x)*H/F.internal.scaleFactor,Q=function(et){var it=(et=et||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return it}}),this};return new Q({width:$*=Math.round(96*T/72*1e4)/1e4})},C.prototype.scale=function(x,F){if(isNaN(x)||isNaN(F))throw _e.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var T=new f(x,0,0,F,0,0);this.ctx.transform=this.ctx.transform.multiply(T)},C.prototype.rotate=function(x){if(isNaN(x))throw _e.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var F=new f(Math.cos(x),Math.sin(x),-Math.sin(x),Math.cos(x),0,0);this.ctx.transform=this.ctx.transform.multiply(F)},C.prototype.translate=function(x,F){if(isNaN(x)||isNaN(F))throw _e.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var T=new f(1,0,0,1,x,F);this.ctx.transform=this.ctx.transform.multiply(T)},C.prototype.transform=function(x,F,T,H,$,Q){if(isNaN(x)||isNaN(F)||isNaN(T)||isNaN(H)||isNaN($)||isNaN(Q))throw _e.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var et=new f(x,F,T,H,$,Q);this.ctx.transform=this.ctx.transform.multiply(et)},C.prototype.setTransform=function(x,F,T,H,$,Q){x=isNaN(x)?1:x,F=isNaN(F)?0:F,T=isNaN(T)?0:T,H=isNaN(H)?1:H,$=isNaN($)?0:$,Q=isNaN(Q)?0:Q,this.ctx.transform=new f(x,F,T,H,$,Q)};var O=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};C.prototype.drawImage=function(x,F,T,H,$,Q,et,it,kt){var Pt=this.pdf.getImageProperties(x),jt=1,Dt=1,Gt=1,ut=1;H!==void 0&&it!==void 0&&(Gt=it/H,ut=kt/$,jt=Pt.width/H*it/H,Dt=Pt.height/$*kt/$),Q===void 0&&(Q=F,et=T,F=0,T=0),H!==void 0&&it===void 0&&(it=H,kt=$),H===void 0&&it===void 0&&(it=Pt.width,kt=Pt.height);for(var E,Qt=this.ctx.transform.decompose(),Rt=Z(Qt.rotate.shx),Lt=new f,At=(Lt=(Lt=(Lt=Lt.multiply(Qt.translate)).multiply(Qt.skew)).multiply(Qt.scale)).applyToRectangle(new h(Q-F*Gt,et-T*ut,H*jt,$*Dt)),Ft=D.call(this,At),Ot=[],qt=0;qt<Ft.length;qt+=1)Ot.indexOf(Ft[qt])===-1&&Ot.push(Ft[qt]);if(J(Ot),this.autoPaging)for(var Jt=Ot[0],ne=Ot[Ot.length-1],re=Jt;re<ne+1;re++){this.pdf.setPage(re);var le=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],ve=re===1?this.posY+this.margin[0]:this.margin[0],$t=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],oe=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],It=re===1?0:$t+(re-2)*oe;if(this.ctx.clip_path.length!==0){var Ke=this.path;E=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(E,this.posX+this.margin[3],-It+ve+this.ctx.prevPageLastElemOffset),ht.call(this,"fill",!0),this.path=Ke}var ue=JSON.parse(JSON.stringify(At));ue=j([ue],this.posX+this.margin[3],-It+ve+this.ctx.prevPageLastElemOffset)[0];var kn=(re>Jt||re<ne)&&O.call(this);kn&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],le,oe,null).clip().discardPath()),this.pdf.addImage(x,"JPEG",ue.x,ue.y,ue.w,ue.h,null,null,Rt),kn&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(x,"JPEG",At.x,At.y,At.w,At.h,null,null,Rt)};var D=function(x,F,T){var H=[];F=F||this.pdf.internal.pageSize.width,T=T||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var $=this.posY+this.ctx.prevPageLastElemOffset;switch(x.type){default:case"mt":case"lt":H.push(Math.floor((x.y+$)/T)+1);break;case"arc":H.push(Math.floor((x.y+$-x.radius)/T)+1),H.push(Math.floor((x.y+$+x.radius)/T)+1);break;case"qct":var Q=pt(this.ctx.lastPoint.x,this.ctx.lastPoint.y,x.x1,x.y1,x.x,x.y);H.push(Math.floor((Q.y+$)/T)+1),H.push(Math.floor((Q.y+Q.h+$)/T)+1);break;case"bct":var et=dt(this.ctx.lastPoint.x,this.ctx.lastPoint.y,x.x1,x.y1,x.x2,x.y2,x.x,x.y);H.push(Math.floor((et.y+$)/T)+1),H.push(Math.floor((et.y+et.h+$)/T)+1);break;case"rect":H.push(Math.floor((x.y+$)/T)+1),H.push(Math.floor((x.y+x.h+$)/T)+1)}for(var it=0;it<H.length;it+=1)for(;this.pdf.internal.getNumberOfPages()<H[it];)k.call(this);return H},k=function(){var x=this.fillStyle,F=this.strokeStyle,T=this.font,H=this.lineCap,$=this.lineWidth,Q=this.lineJoin;this.pdf.addPage(),this.fillStyle=x,this.strokeStyle=F,this.font=T,this.lineCap=H,this.lineWidth=$,this.lineJoin=Q},j=function(x,F,T){for(var H=0;H<x.length;H++)switch(x[H].type){case"bct":x[H].x2+=F,x[H].y2+=T;case"qct":x[H].x1+=F,x[H].y1+=T;case"mt":case"lt":case"arc":default:x[H].x+=F,x[H].y+=T}return x},J=function(x){return x.sort(function(F,T){return F-T})},lt=function(x,F){for(var T,H,$=this.fillStyle,Q=this.strokeStyle,et=this.lineCap,it=this.lineWidth,kt=Math.abs(it*this.ctx.transform.scaleX),Pt=this.lineJoin,jt=JSON.parse(JSON.stringify(this.path)),Dt=JSON.parse(JSON.stringify(this.path)),Gt=[],ut=0;ut<Dt.length;ut++)if(Dt[ut].x!==void 0)for(var E=D.call(this,Dt[ut]),Qt=0;Qt<E.length;Qt+=1)Gt.indexOf(E[Qt])===-1&&Gt.push(E[Qt]);for(var Rt=0;Rt<Gt.length;Rt++)for(;this.pdf.internal.getNumberOfPages()<Gt[Rt];)k.call(this);if(J(Gt),this.autoPaging)for(var Lt=Gt[0],At=Gt[Gt.length-1],Ft=Lt;Ft<At+1;Ft++){this.pdf.setPage(Ft),this.fillStyle=$,this.strokeStyle=Q,this.lineCap=et,this.lineWidth=kt,this.lineJoin=Pt;var Ot=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],qt=Ft===1?this.posY+this.margin[0]:this.margin[0],Jt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],ne=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],re=Ft===1?0:Jt+(Ft-2)*ne;if(this.ctx.clip_path.length!==0){var le=this.path;T=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(T,this.posX+this.margin[3],-re+qt+this.ctx.prevPageLastElemOffset),ht.call(this,x,!0),this.path=le}if(H=JSON.parse(JSON.stringify(jt)),this.path=j(H,this.posX+this.margin[3],-re+qt+this.ctx.prevPageLastElemOffset),F===!1||Ft===0){var ve=(Ft>Lt||Ft<At)&&O.call(this);ve&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],Ot,ne,null).clip().discardPath()),ht.call(this,x,F),ve&&this.pdf.restoreGraphicsState()}this.lineWidth=it}else this.lineWidth=kt,ht.call(this,x,F),this.lineWidth=it;this.path=jt},ht=function(x,F){if((x!=="stroke"||F||!I.call(this))&&(x==="stroke"||F||!p.call(this))){for(var T,H,$=[],Q=this.path,et=0;et<Q.length;et++){var it=Q[et];switch(it.type){case"begin":$.push({begin:!0});break;case"close":$.push({close:!0});break;case"mt":$.push({start:it,deltas:[],abs:[]});break;case"lt":var kt=$.length;if(Q[et-1]&&!isNaN(Q[et-1].x)&&(T=[it.x-Q[et-1].x,it.y-Q[et-1].y],kt>0)){for(;kt>=0;kt--)if($[kt-1].close!==!0&&$[kt-1].begin!==!0){$[kt-1].deltas.push(T),$[kt-1].abs.push(it);break}}break;case"bct":T=[it.x1-Q[et-1].x,it.y1-Q[et-1].y,it.x2-Q[et-1].x,it.y2-Q[et-1].y,it.x-Q[et-1].x,it.y-Q[et-1].y],$[$.length-1].deltas.push(T);break;case"qct":var Pt=Q[et-1].x+2/3*(it.x1-Q[et-1].x),jt=Q[et-1].y+2/3*(it.y1-Q[et-1].y),Dt=it.x+2/3*(it.x1-it.x),Gt=it.y+2/3*(it.y1-it.y),ut=it.x,E=it.y;T=[Pt-Q[et-1].x,jt-Q[et-1].y,Dt-Q[et-1].x,Gt-Q[et-1].y,ut-Q[et-1].x,E-Q[et-1].y],$[$.length-1].deltas.push(T);break;case"arc":$.push({deltas:[],abs:[],arc:!0}),Array.isArray($[$.length-1].abs)&&$[$.length-1].abs.push(it)}}H=F?null:x==="stroke"?"stroke":"fill";for(var Qt=!1,Rt=0;Rt<$.length;Rt++)if($[Rt].arc)for(var Lt=$[Rt].abs,At=0;At<Lt.length;At++){var Ft=Lt[At];Ft.type==="arc"?R.call(this,Ft.x,Ft.y,Ft.radius,Ft.startAngle,Ft.endAngle,Ft.counterclockwise,void 0,F,!Qt):z.call(this,Ft.x,Ft.y),Qt=!0}else if($[Rt].close===!0)this.pdf.internal.out("h"),Qt=!1;else if($[Rt].begin!==!0){var Ot=$[Rt].start.x,qt=$[Rt].start.y;B.call(this,$[Rt].deltas,Ot,qt),Qt=!0}H&&vt.call(this,H),F&&gt.call(this)}},_t=function(x){var F=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,T=F*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return x-T;case"top":return x+F-T;case"hanging":return x+F-2*T;case"middle":return x+F/2-T;case"ideographic":return x;case"alphabetic":default:return x}},tt=function(x){return x+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};C.prototype.createLinearGradient=function(){var x=function(){};return x.colorStops=[],x.addColorStop=function(F,T){this.colorStops.push([F,T])},x.getColor=function(){return this.colorStops.length===0?"#000000":this.colorStops[0][1]},x.isCanvasGradient=!0,x},C.prototype.createPattern=function(){return this.createLinearGradient()},C.prototype.createRadialGradient=function(){return this.createLinearGradient()};var R=function(x,F,T,H,$,Q,et,it,kt){for(var Pt=nt.call(this,T,H,$,Q),jt=0;jt<Pt.length;jt++){var Dt=Pt[jt];jt===0&&(kt?N.call(this,Dt.x1+x,Dt.y1+F):z.call(this,Dt.x1+x,Dt.y1+F)),at.call(this,x,F,Dt.x2,Dt.y2,Dt.x3,Dt.y3,Dt.x4,Dt.y4)}it?gt.call(this):vt.call(this,et)},vt=function(x){switch(x){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},gt=function(){this.pdf.clip(),this.pdf.discardPath()},N=function(x,F){this.pdf.internal.out(t(x)+" "+i(F)+" m")},A=function(x){var F;switch(x.align){case"right":case"end":F="right";break;case"center":F="center";break;case"left":case"start":default:F="left"}var T=this.pdf.getTextDimensions(x.text),H=_t.call(this,x.y),$=tt.call(this,H)-T.h,Q=this.ctx.transform.applyToPoint(new c(x.x,H)),et=this.ctx.transform.decompose(),it=new f;it=(it=(it=it.multiply(et.translate)).multiply(et.skew)).multiply(et.scale);for(var kt,Pt,jt,Dt=this.ctx.transform.applyToRectangle(new h(x.x,H,T.w,T.h)),Gt=it.applyToRectangle(new h(x.x,$,T.w,T.h)),ut=D.call(this,Gt),E=[],Qt=0;Qt<ut.length;Qt+=1)E.indexOf(ut[Qt])===-1&&E.push(ut[Qt]);if(J(E),this.autoPaging)for(var Rt=E[0],Lt=E[E.length-1],At=Rt;At<Lt+1;At++){this.pdf.setPage(At);var Ft=At===1?this.posY+this.margin[0]:this.margin[0],Ot=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],qt=this.pdf.internal.pageSize.height-this.margin[2],Jt=qt-this.margin[0],ne=this.pdf.internal.pageSize.width-this.margin[1],re=ne-this.margin[3],le=At===1?0:Ot+(At-2)*Jt;if(this.ctx.clip_path.length!==0){var ve=this.path;kt=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(kt,this.posX+this.margin[3],-1*le+Ft),ht.call(this,"fill",!0),this.path=ve}var $t=j([JSON.parse(JSON.stringify(Gt))],this.posX+this.margin[3],-le+Ft+this.ctx.prevPageLastElemOffset)[0];x.scale>=.01&&(Pt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Pt*x.scale),jt=this.lineWidth,this.lineWidth=jt*x.scale);var oe=this.autoPaging!=="text";if(oe||$t.y+$t.h<=qt){if(oe||$t.y>=Ft&&$t.x<=ne){var It=oe?x.text:this.pdf.splitTextToSize(x.text,x.maxWidth||ne-$t.x)[0],Ke=j([JSON.parse(JSON.stringify(Dt))],this.posX+this.margin[3],-le+Ft+this.ctx.prevPageLastElemOffset)[0],ue=oe&&(At>Rt||At<Lt)&&O.call(this);ue&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],re,Jt,null).clip().discardPath()),this.pdf.text(It,Ke.x,Ke.y,{angle:x.angle,align:F,renderingMode:x.renderingMode}),ue&&this.pdf.restoreGraphicsState()}}else $t.y<qt&&(this.ctx.prevPageLastElemOffset+=qt-$t.y);x.scale>=.01&&(this.pdf.setFontSize(Pt),this.lineWidth=jt)}else x.scale>=.01&&(Pt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Pt*x.scale),jt=this.lineWidth,this.lineWidth=jt*x.scale),this.pdf.text(x.text,Q.x+this.posX,Q.y+this.posY,{angle:x.angle,align:F,renderingMode:x.renderingMode,maxWidth:x.maxWidth}),x.scale>=.01&&(this.pdf.setFontSize(Pt),this.lineWidth=jt)},z=function(x,F,T,H){T=T||0,H=H||0,this.pdf.internal.out(t(x+T)+" "+i(F+H)+" l")},B=function(x,F,T){return this.pdf.lines(x,F,T,null,null)},at=function(x,F,T,H,$,Q,et,it){this.pdf.internal.out([n(s(T+x)),n(a(H+F)),n(s($+x)),n(a(Q+F)),n(s(et+x)),n(a(it+F)),"c"].join(" "))},nt=function(x,F,T,H){for(var $=2*Math.PI,Q=Math.PI/2;F>T;)F-=$;var et=Math.abs(T-F);et<$&&H&&(et=$-et);for(var it=[],kt=H?-1:1,Pt=F;et>1e-5;){var jt=Pt+kt*Math.min(et,Q);it.push(ft.call(this,x,Pt,jt)),et-=Math.abs(jt-Pt),Pt=jt}return it},ft=function(x,F,T){var H=(T-F)/2,$=x*Math.cos(H),Q=x*Math.sin(H),et=$,it=-Q,kt=et*et+it*it,Pt=kt+et*$+it*Q,jt=4/3*(Math.sqrt(2*kt*Pt)-Pt)/(et*Q-it*$),Dt=et-jt*it,Gt=it+jt*et,ut=Dt,E=-Gt,Qt=H+F,Rt=Math.cos(Qt),Lt=Math.sin(Qt);return{x1:x*Math.cos(F),y1:x*Math.sin(F),x2:Dt*Rt-Gt*Lt,y2:Dt*Lt+Gt*Rt,x3:ut*Rt-E*Lt,y3:ut*Lt+E*Rt,x4:x*Math.cos(T),y4:x*Math.sin(T)}},Z=function(x){return 180*x/Math.PI},pt=function(x,F,T,H,$,Q){var et=x+.5*(T-x),it=F+.5*(H-F),kt=$+.5*(T-$),Pt=Q+.5*(H-Q),jt=Math.min(x,$,et,kt),Dt=Math.max(x,$,et,kt),Gt=Math.min(F,Q,it,Pt),ut=Math.max(F,Q,it,Pt);return new h(jt,Gt,Dt-jt,ut-Gt)},dt=function(x,F,T,H,$,Q,et,it){var kt,Pt,jt,Dt,Gt,ut,E,Qt,Rt,Lt,At,Ft,Ot,qt,Jt=T-x,ne=H-F,re=$-T,le=Q-H,ve=et-$,$t=it-Q;for(Pt=0;Pt<41;Pt++)Rt=(E=(jt=x+(kt=Pt/40)*Jt)+kt*((Gt=T+kt*re)-jt))+kt*(Gt+kt*($+kt*ve-Gt)-E),Lt=(Qt=(Dt=F+kt*ne)+kt*((ut=H+kt*le)-Dt))+kt*(ut+kt*(Q+kt*$t-ut)-Qt),Pt==0?(At=Rt,Ft=Lt,Ot=Rt,qt=Lt):(At=Math.min(At,Rt),Ft=Math.min(Ft,Lt),Ot=Math.max(Ot,Rt),qt=Math.max(qt,Lt));return new h(Math.round(At),Math.round(Ft),Math.round(Ot-At),Math.round(qt-Ft))},Mt=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var x,F,T=(x=this.ctx.lineDash,F=this.ctx.lineDashOffset,JSON.stringify({lineDash:x,lineDashOffset:F}));this.prevLineDash!==T&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=T)}}})(Wt.API),function(r){var n=function(a){var c,h,f,g,b,C,P,p,I,O;for(/[^\x00-\xFF]/.test(a),h=[],f=0,g=(a+=c="\0\0\0\0".slice(a.length%4||4)).length;g>f;f+=4)(b=(a.charCodeAt(f)<<24)+(a.charCodeAt(f+1)<<16)+(a.charCodeAt(f+2)<<8)+a.charCodeAt(f+3))!==0?(C=(b=((b=((b=((b=(b-(O=b%85))/85)-(I=b%85))/85)-(p=b%85))/85)-(P=b%85))/85)%85,h.push(C+33,P+33,p+33,I+33,O+33)):h.push(122);return function(D,k){for(var j=k;j>0;j--)D.pop()}(h,c.length),String.fromCharCode.apply(String,h)+"~>"},t=function(a){var c,h,f,g,b,C=String,P="length",p=255,I="charCodeAt",O="slice",D="replace";for(a[O](-2),a=a[O](0,-2)[D](/\s/g,"")[D]("z","!!!!!"),f=[],g=0,b=(a+=c="uuuuu"[O](a[P]%5||5))[P];b>g;g+=5)h=52200625*(a[I](g)-33)+614125*(a[I](g+1)-33)+7225*(a[I](g+2)-33)+85*(a[I](g+3)-33)+(a[I](g+4)-33),f.push(p&h>>24,p&h>>16,p&h>>8,p&h);return function(k,j){for(var J=j;J>0;J--)k.pop()}(f,c[P]),C.fromCharCode.apply(C,f)},i=function(a){var c=new RegExp(/^([0-9A-Fa-f]{2})+$/);if((a=a.replace(/\s/g,"")).indexOf(">")!==-1&&(a=a.substr(0,a.indexOf(">"))),a.length%2&&(a+="0"),c.test(a)===!1)return"";for(var h="",f=0;f<a.length;f+=2)h+=String.fromCharCode("0x"+(a[f]+a[f+1]));return h},s=function(a){for(var c=new Uint8Array(a.length),h=a.length;h--;)c[h]=a.charCodeAt(h);return a=(c=ja(c)).reduce(function(f,g){return f+String.fromCharCode(g)},"")};r.processDataByFilters=function(a,c){var h=0,f=a||"",g=[];for(typeof(c=c||[])=="string"&&(c=[c]),h=0;h<c.length;h+=1)switch(c[h]){case"ASCII85Decode":case"/ASCII85Decode":f=t(f),g.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":f=n(f),g.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":f=i(f),g.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":f=f.split("").map(function(b){return("0"+b.charCodeAt().toString(16)).slice(-2)}).join("")+">",g.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":f=s(f),g.push("/FlateDecode");break;default:throw new Error('The filter: "'+c[h]+'" is not implemented')}return{data:f,reverseChain:g.reverse().join(" ")}}}(Wt.API),function(r){r.loadFile=function(n,t,i){return function(s,a,c){a=a!==!1,c=typeof c=="function"?c:function(){};var h=void 0;try{h=function(f,g,b){var C=new XMLHttpRequest,P=0,p=function(I){var O=I.length,D=[],k=String.fromCharCode;for(P=0;P<O;P+=1)D.push(k(255&I.charCodeAt(P)));return D.join("")};if(C.open("GET",f,!g),C.overrideMimeType("text/plain; charset=x-user-defined"),g===!1&&(C.onload=function(){C.status===200?b(p(this.responseText)):b(void 0)}),C.send(null),g&&C.status===200)return p(C.responseText)}(s,a,c)}catch{}return h}(n,t,i)},r.loadImageFile=r.loadFile}(Wt.API),function(r){function n(){return(Vt.html2canvas?Promise.resolve(Vt.html2canvas):import("./chunk-UM74E6YY.js")).catch(function(c){return Promise.reject(new Error("Could not load html2canvas: "+c))}).then(function(c){return c.default?c.default:c})}function t(){return(Vt.DOMPurify?Promise.resolve(Vt.DOMPurify):import("./chunk-CYYEWKTJ.js")).catch(function(c){return Promise.reject(new Error("Could not load dompurify: "+c))}).then(function(c){return c.default?c.default:c})}var i=function(c){var h=ye(c);return h==="undefined"?"undefined":h==="string"||c instanceof String?"string":h==="number"||c instanceof Number?"number":h==="function"||c instanceof Function?"function":c&&c.constructor===Array?"array":c&&c.nodeType===1?"element":h==="object"?"object":"unknown"},s=function(c,h){var f=document.createElement(c);for(var g in h.className&&(f.className=h.className),h.innerHTML&&h.dompurify&&(f.innerHTML=h.dompurify.sanitize(h.innerHTML)),h.style)f.style[g]=h.style[g];return f},a=function c(h){var f=Object.assign(c.convert(Promise.resolve()),JSON.parse(JSON.stringify(c.template))),g=c.convert(Promise.resolve(),f);return g=(g=g.setProgress(1,c,1,[c])).set(h)};(a.prototype=Object.create(Promise.prototype)).constructor=a,a.convert=function(c,h){return c.__proto__=h||a.prototype,c},a.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},a.prototype.from=function(c,h){return this.then(function(){switch(h=h||function(f){switch(i(f)){case"string":return"string";case"element":return f.nodeName.toLowerCase()==="canvas"?"canvas":"element";default:return"unknown"}}(c)){case"string":return this.then(t).then(function(f){return this.set({src:s("div",{innerHTML:c,dompurify:f})})});case"element":return this.set({src:c});case"canvas":return this.set({canvas:c});case"img":return this.set({img:c});default:return this.error("Unknown source type.")}})},a.prototype.to=function(c){switch(c){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},a.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var c={position:"relative",display:"inline-block",width:(typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},h=function f(g,b){for(var C=g.nodeType===3?document.createTextNode(g.nodeValue):g.cloneNode(!1),P=g.firstChild;P;P=P.nextSibling)b!==!0&&P.nodeType===1&&P.nodeName==="SCRIPT"||C.appendChild(f(P,b));return g.nodeType===1&&(g.nodeName==="CANVAS"?(C.width=g.width,C.height=g.height,C.getContext("2d").drawImage(g,0,0)):g.nodeName!=="TEXTAREA"&&g.nodeName!=="SELECT"||(C.value=g.value),C.addEventListener("load",function(){C.scrollTop=g.scrollTop,C.scrollLeft=g.scrollLeft},!0)),C}(this.prop.src,this.opt.html2canvas.javascriptEnabled);h.tagName==="BODY"&&(c.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=s("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=s("div",{className:"html2pdf__container",style:c}),this.prop.container.appendChild(h),this.prop.container.firstChild.appendChild(s("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},a.prototype.toCanvas=function(){var c=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(c).then(n).then(function(h){var f=Object.assign({},this.opt.html2canvas);return delete f.onrendered,h(this.prop.container,f)}).then(function(h){(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},a.prototype.toContext2d=function(){var c=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(c).then(n).then(function(h){var f=this.opt.jsPDF,g=this.opt.fontFaces,b=typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,C=Object.assign({async:!0,allowTaint:!0,scale:b,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete C.onrendered,f.context2d.autoPaging=this.opt.autoPaging===void 0||this.opt.autoPaging,f.context2d.posX=this.opt.x,f.context2d.posY=this.opt.y,f.context2d.margin=this.opt.margin,f.context2d.fontFaces=g,g)for(var P=0;P<g.length;++P){var p=g[P],I=p.src.find(function(O){return O.format==="truetype"});I&&f.addFont(I.url,p.ref.name,p.ref.style)}return C.windowHeight=C.windowHeight||0,C.windowHeight=C.windowHeight==0?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):C.windowHeight,f.context2d.save(!0),h(this.prop.container,C)}).then(function(h){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},a.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var c=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=c})},a.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},a.prototype.output=function(c,h,f){return(f=f||"pdf").toLowerCase()==="img"||f.toLowerCase()==="image"?this.outputImg(c,h):this.outputPdf(c,h)},a.prototype.outputPdf=function(c,h){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(c,h)})},a.prototype.outputImg=function(c){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(c){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+c+'" is not supported.'}})},a.prototype.save=function(c){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(c?{filename:c}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},a.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},a.prototype.set=function(c){if(i(c)!=="object")return this;var h=Object.keys(c||{}).map(function(f){if(f in a.template.prop)return function(){this.prop[f]=c[f]};switch(f){case"margin":return this.setMargin.bind(this,c.margin);case"jsPDF":return function(){return this.opt.jsPDF=c.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,c.pageSize);default:return function(){this.opt[f]=c[f]}}},this);return this.then(function(){return this.thenList(h)})},a.prototype.get=function(c,h){return this.then(function(){var f=c in a.template.prop?this.prop[c]:this.opt[c];return h?h(f):f})},a.prototype.setMargin=function(c){return this.then(function(){switch(i(c)){case"number":c=[c,c,c,c];case"array":if(c.length===2&&(c=[c[0],c[1],c[0],c[1]]),c.length===4)break;default:return this.error("Invalid margin array.")}this.opt.margin=c}).then(this.setPageSize)},a.prototype.setPageSize=function(c){function h(f,g){return Math.floor(f*g/72*96)}return this.then(function(){(c=c||Wt.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(c.inner={width:c.width-this.opt.margin[1]-this.opt.margin[3],height:c.height-this.opt.margin[0]-this.opt.margin[2]},c.inner.px={width:h(c.inner.width,c.k),height:h(c.inner.height,c.k)},c.inner.ratio=c.inner.height/c.inner.width),this.prop.pageSize=c})},a.prototype.setProgress=function(c,h,f,g){return c!=null&&(this.progress.val=c),h!=null&&(this.progress.state=h),f!=null&&(this.progress.n=f),g!=null&&(this.progress.stack=g),this.progress.ratio=this.progress.val/this.progress.state,this},a.prototype.updateProgress=function(c,h,f,g){return this.setProgress(c?this.progress.val+c:null,h||null,f?this.progress.n+f:null,g?this.progress.stack.concat(g):null)},a.prototype.then=function(c,h){var f=this;return this.thenCore(c,h,function(g,b){return f.updateProgress(null,null,1,[g]),Promise.prototype.then.call(this,function(C){return f.updateProgress(null,g),C}).then(g,b).then(function(C){return f.updateProgress(1),C})})},a.prototype.thenCore=function(c,h,f){f=f||Promise.prototype.then,c&&(c=c.bind(this)),h&&(h=h.bind(this));var g=Promise.toString().indexOf("[native code]")!==-1&&Promise.name==="Promise"?this:a.convert(Object.assign({},this),Promise.prototype),b=f.call(g,c,h);return a.convert(b,this.__proto__)},a.prototype.thenExternal=function(c,h){return Promise.prototype.then.call(this,c,h)},a.prototype.thenList=function(c){var h=this;return c.forEach(function(f){h=h.thenCore(f)}),h},a.prototype.catch=function(c){c&&(c=c.bind(this));var h=Promise.prototype.catch.call(this,c);return a.convert(h,this)},a.prototype.catchExternal=function(c){return Promise.prototype.catch.call(this,c)},a.prototype.error=function(c){return this.then(function(){throw new Error(c)})},a.prototype.using=a.prototype.set,a.prototype.saveAs=a.prototype.save,a.prototype.export=a.prototype.output,a.prototype.run=a.prototype.then,Wt.getPageSize=function(c,h,f){if(ye(c)==="object"){var g=c;c=g.orientation,h=g.unit||h,f=g.format||f}h=h||"mm",f=f||"a4",c=(""+(c||"P")).toLowerCase();var b,C=(""+f).toLowerCase(),P={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(h){case"pt":b=1;break;case"mm":b=72/25.4;break;case"cm":b=72/2.54;break;case"in":b=72;break;case"px":b=.75;break;case"pc":case"em":b=12;break;case"ex":b=6;break;default:throw"Invalid unit: "+h}var p,I=0,O=0;if(P.hasOwnProperty(C))I=P[C][1]/b,O=P[C][0]/b;else try{I=f[1],O=f[0]}catch{throw new Error("Invalid format: "+f)}if(c==="p"||c==="portrait")c="p",O>I&&(p=O,O=I,I=p);else{if(c!=="l"&&c!=="landscape")throw"Invalid orientation: "+c;c="l",I>O&&(p=O,O=I,I=p)}return{width:O,height:I,unit:h,k:b,orientation:c}},r.html=function(c,h){(h=h||{}).callback=h.callback||function(){},h.html2canvas=h.html2canvas||{},h.html2canvas.canvas=h.html2canvas.canvas||this.canvas,h.jsPDF=h.jsPDF||this,h.fontFaces=h.fontFaces?h.fontFaces.map(il):null;var f=new a(h);return h.worker?f:f.from(c).doCallback()}}(Wt.API),Wt.API.addJS=function(r){return Vc=r,this.internal.events.subscribe("postPutResources",function(){Da=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(Da+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),Gc=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+Vc+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){Da!==void 0&&Gc!==void 0&&this.internal.out("/Names <</JavaScript "+Da+" 0 R>>")}),this},function(r){var n;r.events.push(["postPutResources",function(){var t=this,i=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var s=t.outline.render().split(/\r\n/),a=0;a<s.length;a++){var c=s[a],h=i.exec(c);if(h!=null){var f=h[1];t.internal.newObjectDeferredBegin(f,!1)}t.internal.write(c)}if(this.outline.createNamedDestinations){var g=this.internal.pages.length,b=[];for(a=0;a<g;a++){var C=t.internal.newObject();b.push(C);var P=t.internal.getPageInfo(a+1);t.internal.write("<< /D["+P.objId+" 0 R /XYZ null null null]>> endobj")}var p=t.internal.newObject();for(t.internal.write("<< /Names [ "),a=0;a<b.length;a++)t.internal.write("(page_"+(a+1)+")"+b[a]+" 0 R");t.internal.write(" ] >>","endobj"),n=t.internal.newObject(),t.internal.write("<< /Dests "+p+" 0 R"),t.internal.write(">>","endobj")}}]),r.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+n+" 0 R"))}]),r.events.push(["initialized",function(){var t=this;t.outline={createNamedDestinations:!1,root:{children:[]}},t.outline.add=function(i,s,a){var c={title:s,options:a,children:[]};return i==null&&(i=this.root),i.children.push(c),c},t.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=t,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},t.outline.genIds_r=function(i){i.id=t.internal.newObjectDeferred();for(var s=0;s<i.children.length;s++)this.genIds_r(i.children[s])},t.outline.renderRoot=function(i){this.objStart(i),this.line("/Type /Outlines"),i.children.length>0&&(this.line("/First "+this.makeRef(i.children[0])),this.line("/Last "+this.makeRef(i.children[i.children.length-1]))),this.line("/Count "+this.count_r({count:0},i)),this.objEnd()},t.outline.renderItems=function(i){for(var s=this.ctx.pdf.internal.getVerticalCoordinateString,a=0;a<i.children.length;a++){var c=i.children[a];this.objStart(c),this.line("/Title "+this.makeString(c.title)),this.line("/Parent "+this.makeRef(i)),a>0&&this.line("/Prev "+this.makeRef(i.children[a-1])),a<i.children.length-1&&this.line("/Next "+this.makeRef(i.children[a+1])),c.children.length>0&&(this.line("/First "+this.makeRef(c.children[0])),this.line("/Last "+this.makeRef(c.children[c.children.length-1])));var h=this.count=this.count_r({count:0},c);if(h>0&&this.line("/Count "+h),c.options&&c.options.pageNumber){var f=t.internal.getPageInfo(c.options.pageNumber);this.line("/Dest ["+f.objId+" 0 R /XYZ 0 "+s(0)+" 0]")}this.objEnd()}for(var g=0;g<i.children.length;g++)this.renderItems(i.children[g])},t.outline.line=function(i){this.ctx.val+=i+`\r
`},t.outline.makeRef=function(i){return i.id+" 0 R"},t.outline.makeString=function(i){return"("+t.internal.pdfEscape(i)+")"},t.outline.objStart=function(i){this.ctx.val+=`\r
`+i.id+` 0 obj\r
<<\r
`},t.outline.objEnd=function(){this.ctx.val+=`>> \r
endobj\r
`},t.outline.count_r=function(i,s){for(var a=0;a<s.children.length;a++)i.count++,this.count_r(i,s.children[a]);return i.count}}])}(Wt.API),function(r){var n=[192,193,194,195,196,197,198,199];r.processJPEG=function(t,i,s,a,c,h){var f,g=this.decode.DCT_DECODE,b=null;if(typeof t=="string"||this.__addimage__.isArrayBuffer(t)||this.__addimage__.isArrayBufferView(t)){switch(t=c||t,t=this.__addimage__.isArrayBuffer(t)?new Uint8Array(t):t,(f=function(C){for(var P,p=256*C.charCodeAt(4)+C.charCodeAt(5),I=C.length,O={width:0,height:0,numcomponents:1},D=4;D<I;D+=2){if(D+=p,n.indexOf(C.charCodeAt(D+1))!==-1){P=256*C.charCodeAt(D+5)+C.charCodeAt(D+6),O={width:256*C.charCodeAt(D+7)+C.charCodeAt(D+8),height:P,numcomponents:C.charCodeAt(D+9)};break}p=256*C.charCodeAt(D+2)+C.charCodeAt(D+3)}return O}(t=this.__addimage__.isArrayBufferView(t)?this.__addimage__.arrayBufferToBinaryString(t):t)).numcomponents){case 1:h=this.color_spaces.DEVICE_GRAY;break;case 4:h=this.color_spaces.DEVICE_CMYK;break;case 3:h=this.color_spaces.DEVICE_RGB}b={data:t,width:f.width,height:f.height,colorSpace:h,bitsPerComponent:8,filter:g,index:i,alias:s}}return b}}(Wt.API);r1=function(){var r,n,t;function i(a){var c,h,f,g,b,C,P,p,I,O,D,k,j,J;for(this.data=a,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},C=null;;){switch(c=this.readUInt32(),I=(function(){var lt,ht;for(ht=[],lt=0;lt<4;++lt)ht.push(String.fromCharCode(this.data[this.pos++]));return ht}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(c);break;case"fcTL":C&&this.animation.frames.push(C),this.pos+=4,C={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},b=this.readUInt16(),g=this.readUInt16()||100,C.delay=1e3*b/g,C.disposeOp=this.data[this.pos++],C.blendOp=this.data[this.pos++],C.data=[];break;case"IDAT":case"fdAT":for(I==="fdAT"&&(this.pos+=4,c-=4),a=C?.data||this.imgData,k=0;0<=c?k<c:k>c;0<=c?++k:--k)a.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(f=this.palette.length/3,this.transparency.indexed=this.read(c),this.transparency.indexed.length>f)throw new Error("More transparent colors than palette size");if((O=f-this.transparency.indexed.length)>0)for(j=0;0<=O?j<O:j>O;0<=O?++j:--j)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(c)[0];break;case 2:this.transparency.rgb=this.read(c)}break;case"tEXt":P=(D=this.read(c)).indexOf(0),p=String.fromCharCode.apply(String,D.slice(0,P)),this.text[p]=String.fromCharCode.apply(String,D.slice(P+1));break;case"IEND":return C&&this.animation.frames.push(C),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=(J=this.colorType)===4||J===6,h=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*h,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=c}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}i.prototype.read=function(a){var c,h;for(h=[],c=0;0<=a?c<a:c>a;0<=a?++c:--c)h.push(this.data[this.pos++]);return h},i.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},i.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},i.prototype.decodePixels=function(a){var c=this.pixelBitlength/8,h=new Uint8Array(this.width*this.height*c),f=0,g=this;if(a==null&&(a=this.imgData),a.length===0)return new Uint8Array(0);function b(C,P,p,I){var O,D,k,j,J,lt,ht,_t,tt,R,vt,gt,N,A,z,B,at,nt,ft,Z,pt,dt=Math.ceil((g.width-C)/p),Mt=Math.ceil((g.height-P)/I),x=g.width==dt&&g.height==Mt;for(A=c*dt,gt=x?h:new Uint8Array(A*Mt),lt=a.length,N=0,D=0;N<Mt&&f<lt;){switch(a[f++]){case 0:for(j=at=0;at<A;j=at+=1)gt[D++]=a[f++];break;case 1:for(j=nt=0;nt<A;j=nt+=1)O=a[f++],J=j<c?0:gt[D-c],gt[D++]=(O+J)%256;break;case 2:for(j=ft=0;ft<A;j=ft+=1)O=a[f++],k=(j-j%c)/c,z=N&&gt[(N-1)*A+k*c+j%c],gt[D++]=(z+O)%256;break;case 3:for(j=Z=0;Z<A;j=Z+=1)O=a[f++],k=(j-j%c)/c,J=j<c?0:gt[D-c],z=N&&gt[(N-1)*A+k*c+j%c],gt[D++]=(O+Math.floor((J+z)/2))%256;break;case 4:for(j=pt=0;pt<A;j=pt+=1)O=a[f++],k=(j-j%c)/c,J=j<c?0:gt[D-c],N===0?z=B=0:(z=gt[(N-1)*A+k*c+j%c],B=k&&gt[(N-1)*A+(k-1)*c+j%c]),ht=J+z-B,_t=Math.abs(ht-J),R=Math.abs(ht-z),vt=Math.abs(ht-B),tt=_t<=R&&_t<=vt?J:R<=vt?z:B,gt[D++]=(O+tt)%256;break;default:throw new Error("Invalid filter algorithm: "+a[f-1])}if(!x){var F=((P+N*I)*g.width+C)*c,T=N*A;for(j=0;j<dt;j+=1){for(var H=0;H<c;H+=1)h[F++]=gt[T++];F+=(p-1)*c}}N++}}return a=jc(a),g.interlaceMethod==1?(b(0,0,8,8),b(4,0,8,8),b(0,4,4,8),b(2,0,4,4),b(0,2,2,4),b(1,0,2,2),b(0,1,1,2)):b(0,0,1,1),h},i.prototype.decodePalette=function(){var a,c,h,f,g,b,C,P,p;for(h=this.palette,b=this.transparency.indexed||[],g=new Uint8Array((b.length||0)+h.length),f=0,a=0,c=C=0,P=h.length;C<P;c=C+=3)g[f++]=h[c],g[f++]=h[c+1],g[f++]=h[c+2],g[f++]=(p=b[a++])!=null?p:255;return g},i.prototype.copyToImageData=function(a,c){var h,f,g,b,C,P,p,I,O,D,k;if(f=this.colors,O=null,h=this.hasAlphaChannel,this.palette.length&&(O=(k=this._decodedPalette)!=null?k:this._decodedPalette=this.decodePalette(),f=4,h=!0),I=(g=a.data||a).length,C=O||c,b=P=0,f===1)for(;b<I;)p=O?4*c[b/4]:P,D=C[p++],g[b++]=D,g[b++]=D,g[b++]=D,g[b++]=h?C[p++]:255,P=p;else for(;b<I;)p=O?4*c[b/4]:P,g[b++]=C[p++],g[b++]=C[p++],g[b++]=C[p++],g[b++]=h?C[p++]:255,P=p},i.prototype.decode=function(){var a;return a=new Uint8Array(this.width*this.height*4),this.copyToImageData(a,this.decodePixels()),a};var s=function(){if(Object.prototype.toString.call(Vt)==="[object Window]"){try{n=Vt.document.createElement("canvas"),t=n.getContext("2d")}catch{return!1}return!0}return!1};return s(),r=function(a){var c;if(s()===!0)return t.width=a.width,t.height=a.height,t.clearRect(0,0,a.width,a.height),t.putImageData(a,0,0),(c=new Image).src=n.toDataURL(),c;throw new Error("This method requires a Browser with Canvas-capability.")},i.prototype.decodeFrames=function(a){var c,h,f,g,b,C,P,p;if(this.animation){for(p=[],h=b=0,C=(P=this.animation.frames).length;b<C;h=++b)c=P[h],f=a.createImageData(c.width,c.height),g=this.decodePixels(new Uint8Array(c.data)),this.copyToImageData(f,g),c.imageData=f,p.push(c.image=r(f));return p}},i.prototype.renderFrame=function(a,c){var h,f,g;return h=(f=this.animation.frames)[c],g=f[c-1],c===0&&a.clearRect(0,0,this.width,this.height),g?.disposeOp===1?a.clearRect(g.xOffset,g.yOffset,g.width,g.height):g?.disposeOp===2&&a.putImageData(g.imageData,g.xOffset,g.yOffset),h.blendOp===0&&a.clearRect(h.xOffset,h.yOffset,h.width,h.height),a.drawImage(h.image,h.xOffset,h.yOffset)},i.prototype.animate=function(a){var c,h,f,g,b,C,P=this;return h=0,C=this.animation,g=C.numFrames,f=C.frames,b=C.numPlays,(c=function(){var p,I;if(p=h++%g,I=f[p],P.renderFrame(a,p),g>1&&h/g<b)return P.animation._timeout=setTimeout(c,I.delay)})()},i.prototype.stopAnimation=function(){var a;return clearTimeout((a=this.animation)!=null?a._timeout:void 0)},i.prototype.render=function(a){var c,h;return a._png&&a._png.stopAnimation(),a._png=this,a.width=this.width,a.height=this.height,c=a.getContext("2d"),this.animation?(this.decodeFrames(c),this.animate(c)):(h=c.createImageData(this.width,this.height),this.copyToImageData(h,this.decodePixels()),c.putImageData(h,0,0))},i}();(function(r){var n=function(){return typeof ja=="function"},t=function(P,p,I,O){var D=4,k=c;switch(O){case r.image_compression.FAST:D=1,k=a;break;case r.image_compression.MEDIUM:D=6,k=h;break;case r.image_compression.SLOW:D=9,k=f}P=i(P,p,I,k);var j=ja(P,{level:D});return r.__addimage__.arrayBufferToBinaryString(j)},i=function(P,p,I,O){for(var D,k,j,J=P.length/p,lt=new Uint8Array(P.length+J),ht=b(),_t=0;_t<J;_t+=1){if(j=_t*p,D=P.subarray(j,j+p),O)lt.set(O(D,I,k),j+_t);else{for(var tt,R=ht.length,vt=[];tt<R;tt+=1)vt[tt]=ht[tt](D,I,k);var gt=C(vt.concat());lt.set(vt[gt],j+_t)}k=D}return lt},s=function(P){var p=Array.apply([],P);return p.unshift(0),p},a=function(P,p){var I,O=[],D=P.length;O[0]=1;for(var k=0;k<D;k+=1)I=P[k-p]||0,O[k+1]=P[k]-I+256&255;return O},c=function(P,p,I){var O,D=[],k=P.length;D[0]=2;for(var j=0;j<k;j+=1)O=I&&I[j]||0,D[j+1]=P[j]-O+256&255;return D},h=function(P,p,I){var O,D,k=[],j=P.length;k[0]=3;for(var J=0;J<j;J+=1)O=P[J-p]||0,D=I&&I[J]||0,k[J+1]=P[J]+256-(O+D>>>1)&255;return k},f=function(P,p,I){var O,D,k,j,J=[],lt=P.length;J[0]=4;for(var ht=0;ht<lt;ht+=1)O=P[ht-p]||0,D=I&&I[ht]||0,k=I&&I[ht-p]||0,j=g(O,D,k),J[ht+1]=P[ht]-j+256&255;return J},g=function(P,p,I){if(P===p&&p===I)return P;var O=Math.abs(p-I),D=Math.abs(P-I),k=Math.abs(P+p-I-I);return O<=D&&O<=k?P:D<=k?p:I},b=function(){return[s,a,c,h,f]},C=function(P){var p=P.map(function(I){return I.reduce(function(O,D){return O+Math.abs(D)},0)});return p.indexOf(Math.min.apply(null,p))};r.processPNG=function(P,p,I,O){var D,k,j,J,lt,ht,_t,tt,R,vt,gt,N,A,z,B,at=this.decode.FLATE_DECODE,nt="";if(this.__addimage__.isArrayBuffer(P)&&(P=new Uint8Array(P)),this.__addimage__.isArrayBufferView(P)){if(P=(j=new r1(P)).imgData,k=j.bits,D=j.colorSpace,lt=j.colors,[4,6].indexOf(j.colorType)!==-1){if(j.bits===8){R=(tt=j.pixelBitlength==32?new Uint32Array(j.decodePixels().buffer):j.pixelBitlength==16?new Uint16Array(j.decodePixels().buffer):new Uint8Array(j.decodePixels().buffer)).length,gt=new Uint8Array(R*j.colors),vt=new Uint8Array(R);var ft,Z=j.pixelBitlength-j.bits;for(z=0,B=0;z<R;z++){for(A=tt[z],ft=0;ft<Z;)gt[B++]=A>>>ft&255,ft+=j.bits;vt[z]=A>>>ft&255}}if(j.bits===16){R=(tt=new Uint32Array(j.decodePixels().buffer)).length,gt=new Uint8Array(R*(32/j.pixelBitlength)*j.colors),vt=new Uint8Array(R*(32/j.pixelBitlength)),N=j.colors>1,z=0,B=0;for(var pt=0;z<R;)A=tt[z++],gt[B++]=A>>>0&255,N&&(gt[B++]=A>>>16&255,A=tt[z++],gt[B++]=A>>>0&255),vt[pt++]=A>>>16&255;k=8}O!==r.image_compression.NONE&&n()?(P=t(gt,j.width*j.colors,j.colors,O),_t=t(vt,j.width,1,O)):(P=gt,_t=vt,at=void 0)}if(j.colorType===3&&(D=this.color_spaces.INDEXED,ht=j.palette,j.transparency.indexed)){var dt=j.transparency.indexed,Mt=0;for(z=0,R=dt.length;z<R;++z)Mt+=dt[z];if((Mt/=255)===R-1&&dt.indexOf(0)!==-1)J=[dt.indexOf(0)];else if(Mt!==R){for(tt=j.decodePixels(),vt=new Uint8Array(tt.length),z=0,R=tt.length;z<R;z++)vt[z]=dt[tt[z]];_t=t(vt,j.width,1)}}var x=function(F){var T;switch(F){case r.image_compression.FAST:T=11;break;case r.image_compression.MEDIUM:T=13;break;case r.image_compression.SLOW:T=14;break;default:T=12}return T}(O);return at===this.decode.FLATE_DECODE&&(nt="/Predictor "+x+" "),nt+="/Colors "+lt+" /BitsPerComponent "+k+" /Columns "+j.width,(this.__addimage__.isArrayBuffer(P)||this.__addimage__.isArrayBufferView(P))&&(P=this.__addimage__.arrayBufferToBinaryString(P)),(_t&&this.__addimage__.isArrayBuffer(_t)||this.__addimage__.isArrayBufferView(_t))&&(_t=this.__addimage__.arrayBufferToBinaryString(_t)),{alias:I,data:P,index:p,filter:at,decodeParameters:nt,transparency:J,palette:ht,sMask:_t,predictor:x,width:j.width,height:j.height,bitsPerComponent:k,colorSpace:D}}}})(Wt.API),function(r){r.processGIF89A=function(n,t,i,s){var a=new o1(n),c=a.width,h=a.height,f=[];a.decodeAndBlitFrameRGBA(0,f);var g={data:f,width:c,height:h},b=new Ks(100).encode(g,100);return r.processJPEG.call(this,b,t,i,s)},r.processGIF87A=r.processGIF89A}(Wt.API),Hn.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.bitPP===16&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var r=this.colors===0?1<<this.bitPP:this.colors;this.palette=new Array(r);for(var n=0;n<r;n++){var t=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0);this.palette[n]={red:s,green:i,blue:t,quad:a}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},Hn.prototype.parseBGR=function(){this.pos=this.offset;try{var r="bit"+this.bitPP,n=this.width*this.height*4;this.data=new Uint8Array(n),this[r]()}catch(t){_e.log("bit decode error:"+t)}},Hn.prototype.bit1=function(){var r,n=Math.ceil(this.width/8),t=n%4;for(r=this.height-1;r>=0;r--){for(var i=this.bottom_up?r:this.height-1-r,s=0;s<n;s++)for(var a=this.datav.getUint8(this.pos++,!0),c=i*this.width*4+8*s*4,h=0;h<8&&8*s+h<this.width;h++){var f=this.palette[a>>7-h&1];this.data[c+4*h]=f.blue,this.data[c+4*h+1]=f.green,this.data[c+4*h+2]=f.red,this.data[c+4*h+3]=255}t!==0&&(this.pos+=4-t)}},Hn.prototype.bit4=function(){for(var r=Math.ceil(this.width/2),n=r%4,t=this.height-1;t>=0;t--){for(var i=this.bottom_up?t:this.height-1-t,s=0;s<r;s++){var a=this.datav.getUint8(this.pos++,!0),c=i*this.width*4+2*s*4,h=a>>4,f=15&a,g=this.palette[h];if(this.data[c]=g.blue,this.data[c+1]=g.green,this.data[c+2]=g.red,this.data[c+3]=255,2*s+1>=this.width)break;g=this.palette[f],this.data[c+4]=g.blue,this.data[c+4+1]=g.green,this.data[c+4+2]=g.red,this.data[c+4+3]=255}n!==0&&(this.pos+=4-n)}},Hn.prototype.bit8=function(){for(var r=this.width%4,n=this.height-1;n>=0;n--){for(var t=this.bottom_up?n:this.height-1-n,i=0;i<this.width;i++){var s=this.datav.getUint8(this.pos++,!0),a=t*this.width*4+4*i;if(s<this.palette.length){var c=this.palette[s];this.data[a]=c.red,this.data[a+1]=c.green,this.data[a+2]=c.blue,this.data[a+3]=255}else this.data[a]=255,this.data[a+1]=255,this.data[a+2]=255,this.data[a+3]=255}r!==0&&(this.pos+=4-r)}},Hn.prototype.bit15=function(){for(var r=this.width%3,n=parseInt("11111",2),t=this.height-1;t>=0;t--){for(var i=this.bottom_up?t:this.height-1-t,s=0;s<this.width;s++){var a=this.datav.getUint16(this.pos,!0);this.pos+=2;var c=(a&n)/n*255|0,h=(a>>5&n)/n*255|0,f=(a>>10&n)/n*255|0,g=a>>15?255:0,b=i*this.width*4+4*s;this.data[b]=f,this.data[b+1]=h,this.data[b+2]=c,this.data[b+3]=g}this.pos+=r}},Hn.prototype.bit16=function(){for(var r=this.width%3,n=parseInt("11111",2),t=parseInt("111111",2),i=this.height-1;i>=0;i--){for(var s=this.bottom_up?i:this.height-1-i,a=0;a<this.width;a++){var c=this.datav.getUint16(this.pos,!0);this.pos+=2;var h=(c&n)/n*255|0,f=(c>>5&t)/t*255|0,g=(c>>11)/n*255|0,b=s*this.width*4+4*a;this.data[b]=g,this.data[b+1]=f,this.data[b+2]=h,this.data[b+3]=255}this.pos+=r}},Hn.prototype.bit24=function(){for(var r=this.height-1;r>=0;r--){for(var n=this.bottom_up?r:this.height-1-r,t=0;t<this.width;t++){var i=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),c=n*this.width*4+4*t;this.data[c]=a,this.data[c+1]=s,this.data[c+2]=i,this.data[c+3]=255}this.pos+=this.width%4}},Hn.prototype.bit32=function(){for(var r=this.height-1;r>=0;r--)for(var n=this.bottom_up?r:this.height-1-r,t=0;t<this.width;t++){var i=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),c=this.datav.getUint8(this.pos++,!0),h=n*this.width*4+4*t;this.data[h]=a,this.data[h+1]=s,this.data[h+2]=i,this.data[h+3]=c}},Hn.prototype.getData=function(){return this.data},function(r){r.processBMP=function(n,t,i,s){var a=new Hn(n,!1),c=a.width,h=a.height,f={data:a.getData(),width:c,height:h},g=new Ks(100).encode(f,100);return r.processJPEG.call(this,g,t,i,s)}}(Wt.API),Kc.prototype.getData=function(){return this.data},function(r){r.processWEBP=function(n,t,i,s){var a=new Kc(n,!1),c=a.width,h=a.height,f={data:a.getData(),width:c,height:h},g=new Ks(100).encode(f,100);return r.processJPEG.call(this,g,t,i,s)}}(Wt.API),Wt.API.processRGBA=function(r,n,t){for(var i=r.data,s=i.length,a=new Uint8Array(s/4*3),c=new Uint8Array(s/4),h=0,f=0,g=0;g<s;g+=4){var b=i[g],C=i[g+1],P=i[g+2],p=i[g+3];a[h++]=b,a[h++]=C,a[h++]=P,c[f++]=p}var I=this.__addimage__.arrayBufferToBinaryString(a);return{alpha:this.__addimage__.arrayBufferToBinaryString(c),data:I,index:n,alias:t,colorSpace:"DeviceRGB",bitsPerComponent:8,width:r.width,height:r.height}},Wt.API.setLanguage=function(r){return this.internal.languageSettings===void 0&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),{af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[r]!==void 0&&(this.internal.languageSettings.languageCode=r,this.internal.languageSettings.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},Ur=Wt.API,Ba=Ur.getCharWidthsArray=function(r,n){var t,i,s=(n=n||{}).font||this.internal.getFont(),a=n.fontSize||this.internal.getFontSize(),c=n.charSpace||this.internal.getCharSpace(),h=n.widths?n.widths:s.metadata.Unicode.widths,f=h.fof?h.fof:1,g=n.kerning?n.kerning:s.metadata.Unicode.kerning,b=g.fof?g.fof:1,C=n.doKerning!==!1,P=0,p=r.length,I=0,O=h[0]||f,D=[];for(t=0;t<p;t++)i=r.charCodeAt(t),typeof s.metadata.widthOfString=="function"?D.push((s.metadata.widthOfGlyph(s.metadata.characterToGlyph(i))+c*(1e3/a)||0)/1e3):(P=C&&ye(g[i])==="object"&&!isNaN(parseInt(g[i][I],10))?g[i][I]/b:0,D.push((h[i]||O)/f+P)),I=i;return D},$c=Ur.getStringUnitWidth=function(r,n){var t=(n=n||{}).fontSize||this.internal.getFontSize(),i=n.font||this.internal.getFont(),s=n.charSpace||this.internal.getCharSpace();return Ur.processArabic&&(r=Ur.processArabic(r)),typeof i.metadata.widthOfString=="function"?i.metadata.widthOfString(r,t,s)/t:Ba.apply(this,arguments).reduce(function(a,c){return a+c},0)},Yc=function(r,n,t,i){for(var s=[],a=0,c=r.length,h=0;a!==c&&h+n[a]<t;)h+=n[a],a++;s.push(r.slice(0,a));var f=a;for(h=0;a!==c;)h+n[a]>i&&(s.push(r.slice(f,a)),h=0,f=a),h+=n[a],a++;return f!==a&&s.push(r.slice(f,a)),s},Jc=function(r,n,t){t||(t={});var i,s,a,c,h,f,g,b=[],C=[b],P=t.textIndent||0,p=0,I=0,O=r.split(" "),D=Ba.apply(this,[" ",t])[0];if(f=t.lineIndent===-1?O[0].length+2:t.lineIndent||0){var k=Array(f).join(" "),j=[];O.map(function(lt){(lt=lt.split(/\s*\n/)).length>1?j=j.concat(lt.map(function(ht,_t){return(_t&&ht.length?`
`:"")+ht})):j.push(lt[0])}),O=j,f=$c.apply(this,[k,t])}for(a=0,c=O.length;a<c;a++){var J=0;if(i=O[a],f&&i[0]==`
`&&(i=i.substr(1),J=1),P+p+(I=(s=Ba.apply(this,[i,t])).reduce(function(lt,ht){return lt+ht},0))>n||J){if(I>n){for(h=Yc.apply(this,[i,s,n-(P+p),n]),b.push(h.shift()),b=[h.pop()];h.length;)C.push([h.shift()]);I=s.slice(i.length-(b[0]?b[0].length:0)).reduce(function(lt,ht){return lt+ht},0)}else b=[i];C.push(b),P=I+f,p=D}else b.push(i),P+=p+I,p=D}return g=f?function(lt,ht){return(ht?k:"")+lt.join(" ")}:function(lt){return lt.join(" ")},C.map(g)},Ur.splitTextToSize=function(r,n,t){var i,s=(t=t||{}).fontSize||this.internal.getFontSize(),a=(function(b){if(b.widths&&b.kerning)return{widths:b.widths,kerning:b.kerning};var C=this.internal.getFont(b.fontName,b.fontStyle);return C.metadata.Unicode?{widths:C.metadata.Unicode.widths||{0:1},kerning:C.metadata.Unicode.kerning||{}}:{font:C.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,t);i=Array.isArray(r)?r:String(r).split(/\r?\n/);var c=1*this.internal.scaleFactor*n/s;a.textIndent=t.textIndent?1*t.textIndent*this.internal.scaleFactor/s:0,a.lineIndent=t.lineIndent;var h,f,g=[];for(h=0,f=i.length;h<f;h++)g=g.concat(Jc.apply(this,[i[h],c,a]));return g},function(r){r.__fontmetrics__=r.__fontmetrics__||{};for(var n="klmnopqrstuvwxyz",t={},i={},s=0;s<n.length;s++)t[n[s]]="0123456789abcdef"[s],i["0123456789abcdef"[s]]=n[s];var a=function(C){return"0x"+parseInt(C,10).toString(16)},c=r.__fontmetrics__.compress=function(C){var P,p,I,O,D=["{"];for(var k in C){if(P=C[k],isNaN(parseInt(k,10))?p="'"+k+"'":(k=parseInt(k,10),p=(p=a(k).slice(2)).slice(0,-1)+i[p.slice(-1)]),typeof P=="number")P<0?(I=a(P).slice(3),O="-"):(I=a(P).slice(2),O=""),I=O+I.slice(0,-1)+i[I.slice(-1)];else{if(ye(P)!=="object")throw new Error("Don't know what to do with value type "+ye(P)+".");I=c(P)}D.push(p+I)}return D.push("}"),D.join("")},h=r.__fontmetrics__.uncompress=function(C){if(typeof C!="string")throw new Error("Invalid argument passed to uncompress.");for(var P,p,I,O,D={},k=1,j=D,J=[],lt="",ht="",_t=C.length-1,tt=1;tt<_t;tt+=1)(O=C[tt])=="'"?P?(I=P.join(""),P=void 0):P=[]:P?P.push(O):O=="{"?(J.push([j,I]),j={},I=void 0):O=="}"?((p=J.pop())[0][p[1]]=j,I=void 0,j=p[0]):O=="-"?k=-1:I===void 0?t.hasOwnProperty(O)?(lt+=t[O],I=parseInt(lt,16)*k,k=1,lt=""):lt+=O:t.hasOwnProperty(O)?(ht+=t[O],j[I]=parseInt(ht,16)*k,k=1,I=void 0,ht=""):ht+=O;return D},f={codePages:["WinAnsiEncoding"],WinAnsiEncoding:h("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},g={Unicode:{Courier:f,"Courier-Bold":f,"Courier-BoldOblique":f,"Courier-Oblique":f,Helvetica:f,"Helvetica-Bold":f,"Helvetica-BoldOblique":f,"Helvetica-Oblique":f,"Times-Roman":f,"Times-Bold":f,"Times-BoldItalic":f,"Times-Italic":f}},b={Unicode:{"Courier-Oblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":h("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":h("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:h("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:h("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":h("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":h("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};r.events.push(["addFont",function(C){var P=C.font,p=b.Unicode[P.postScriptName];p&&(P.metadata.Unicode={},P.metadata.Unicode.widths=p.widths,P.metadata.Unicode.kerning=p.kerning);var I=g.Unicode[P.postScriptName];I&&(P.metadata.Unicode.encoding=I,P.encoding=I.codePages[0])}])}(Wt.API),function(r){var n=function(t){for(var i=t.length,s=new Uint8Array(i),a=0;a<i;a++)s[a]=t.charCodeAt(a);return s};r.API.events.push(["addFont",function(t){var i=void 0,s=t.font,a=t.instance;if(!s.isStandardFont){if(a===void 0)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+s.postScriptName+"').");if(typeof(i=a.existsFileInVFS(s.postScriptName)===!1?a.loadFile(s.postScriptName):a.getFileFromVFS(s.postScriptName))!="string")throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+s.postScriptName+"').");(function(c,h){h=/^\x00\x01\x00\x00/.test(h)?n(h):n(No(h)),c.metadata=r.API.TTFFont.open(h),c.metadata.Unicode=c.metadata.Unicode||{encoding:{},kerning:{},widths:[]},c.metadata.glyIdsUsed=[0]})(s,i)}}])}(Wt),function(r){function n(){return(Vt.canvg?Promise.resolve(Vt.canvg):import("./chunk-NAQ7NYMF.js")).catch(function(t){return Promise.reject(new Error("Could not load canvg: "+t))}).then(function(t){return t.default?t.default:t})}Wt.API.addSvgAsImage=function(t,i,s,a,c,h,f,g){if(isNaN(i)||isNaN(s))throw _e.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(a)||isNaN(c))throw _e.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var b=document.createElement("canvas");b.width=a,b.height=c;var C=b.getContext("2d");C.fillStyle="#fff",C.fillRect(0,0,b.width,b.height);var P={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},p=this;return n().then(function(I){return I.fromString(C,t,P)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(I){return I.render(P)}).then(function(){p.addImage(b.toDataURL("image/jpeg",1),i,s,a,c,f,g)})}}(),Wt.API.putTotalPages=function(r){var n,t=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(n=new RegExp(r,"g"),t=this.internal.getNumberOfPages()):(n=new RegExp(this.pdfEscape16(r,this.internal.getFont()),"g"),t=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var i=1;i<=this.internal.getNumberOfPages();i++)for(var s=0;s<this.internal.pages[i].length;s++)this.internal.pages[i][s]=this.internal.pages[i][s].replace(n,t);return this},Wt.API.viewerPreferences=function(r,n){var t;r=r||{},n=n||!1;var i,s,a,c={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},h=Object.keys(c),f=[],g=0,b=0,C=0;function P(I,O){var D,k=!1;for(D=0;D<I.length;D+=1)I[D]===O&&(k=!0);return k}if(this.internal.viewerpreferences===void 0&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(c)),this.internal.viewerpreferences.isSubscribed=!1),t=this.internal.viewerpreferences.configuration,r==="reset"||n===!0){var p=h.length;for(C=0;C<p;C+=1)t[h[C]].value=t[h[C]].defaultValue,t[h[C]].explicitSet=!1}if(ye(r)==="object"){for(s in r)if(a=r[s],P(h,s)&&a!==void 0){if(t[s].type==="boolean"&&typeof a=="boolean")t[s].value=a;else if(t[s].type==="name"&&P(t[s].valueSet,a))t[s].value=a;else if(t[s].type==="integer"&&Number.isInteger(a))t[s].value=a;else if(t[s].type==="array"){for(g=0;g<a.length;g+=1)if(i=!0,a[g].length===1&&typeof a[g][0]=="number")f.push(String(a[g]-1));else if(a[g].length>1){for(b=0;b<a[g].length;b+=1)typeof a[g][b]!="number"&&(i=!1);i===!0&&f.push([a[g][0]-1,a[g][1]-1].join(" "))}t[s].value="["+f.join(" ")+"]"}else t[s].value=t[s].defaultValue;t[s].explicitSet=!0}}return this.internal.viewerpreferences.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){var I,O=[];for(I in t)t[I].explicitSet===!0&&(t[I].type==="name"?O.push("/"+I+" /"+t[I].value):O.push("/"+I+" "+t[I].value));O.length!==0&&this.internal.write(`/ViewerPreferences
<<
`+O.join(`
`)+`
>>`)}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=t,this},function(r){var n=function(){var i='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',s=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),a=unescape(encodeURIComponent(i)),c=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),h=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),f=unescape(encodeURIComponent("</x:xmpmeta>")),g=a.length+c.length+h.length+s.length+f.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+g+" >>"),this.internal.write("stream"),this.internal.write(s+a+c+h+f),this.internal.write("endstream"),this.internal.write("endobj")},t=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};r.addMetadata=function(i,s){return this.internal.__metadata__===void 0&&(this.internal.__metadata__={metadata:i,namespaceuri:s||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",t),this.internal.events.subscribe("postPutResources",n)),this}}(Wt.API),function(r){var n=r.API,t=n.pdfEscape16=function(a,c){for(var h,f=c.metadata.Unicode.widths,g=["","0","00","000","0000"],b=[""],C=0,P=a.length;C<P;++C){if(h=c.metadata.characterToGlyph(a.charCodeAt(C)),c.metadata.glyIdsUsed.push(h),c.metadata.toUnicode[h]=a.charCodeAt(C),f.indexOf(h)==-1&&(f.push(h),f.push([parseInt(c.metadata.widthOfGlyph(h),10)])),h=="0")return b.join("");h=h.toString(16),b.push(g[4-h.length],h)}return b.join("")},i=function(a){var c,h,f,g,b,C,P;for(b=`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange`,f=[],C=0,P=(h=Object.keys(a).sort(function(p,I){return p-I})).length;C<P;C++)c=h[C],f.length>=100&&(b+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar`,f=[]),a[c]!==void 0&&a[c]!==null&&typeof a[c].toString=="function"&&(g=("0000"+a[c].toString(16)).slice(-4),c=("0000"+(+c).toString(16)).slice(-4),f.push("<"+c+"><"+g+">"));return f.length&&(b+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar
`),b+=`endcmap
CMapName currentdict /CMap defineresource pop
end
end`};n.events.push(["putFont",function(a){(function(c){var h=c.font,f=c.out,g=c.newObject,b=c.putStream;if(h.metadata instanceof r.API.TTFFont&&h.encoding==="Identity-H"){for(var C=h.metadata.Unicode.widths,P=h.metadata.subset.encode(h.metadata.glyIdsUsed,1),p="",I=0;I<P.length;I++)p+=String.fromCharCode(P[I]);var O=g();b({data:p,addLength1:!0,objectId:O}),f("endobj");var D=g();b({data:i(h.metadata.toUnicode),addLength1:!0,objectId:D}),f("endobj");var k=g();f("<<"),f("/Type /FontDescriptor"),f("/FontName /"+Hr(h.fontName)),f("/FontFile2 "+O+" 0 R"),f("/FontBBox "+r.API.PDFObject.convert(h.metadata.bbox)),f("/Flags "+h.metadata.flags),f("/StemV "+h.metadata.stemV),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f(">>"),f("endobj");var j=g();f("<<"),f("/Type /Font"),f("/BaseFont /"+Hr(h.fontName)),f("/FontDescriptor "+k+" 0 R"),f("/W "+r.API.PDFObject.convert(C)),f("/CIDToGIDMap /Identity"),f("/DW 1000"),f("/Subtype /CIDFontType2"),f("/CIDSystemInfo"),f("<<"),f("/Supplement 0"),f("/Registry (Adobe)"),f("/Ordering ("+h.encoding+")"),f(">>"),f(">>"),f("endobj"),h.objectNumber=g(),f("<<"),f("/Type /Font"),f("/Subtype /Type0"),f("/ToUnicode "+D+" 0 R"),f("/BaseFont /"+Hr(h.fontName)),f("/Encoding /"+h.encoding),f("/DescendantFonts ["+j+" 0 R]"),f(">>"),f("endobj"),h.isAlreadyPutted=!0}})(a)}]),n.events.push(["putFont",function(a){(function(c){var h=c.font,f=c.out,g=c.newObject,b=c.putStream;if(h.metadata instanceof r.API.TTFFont&&h.encoding==="WinAnsiEncoding"){for(var C=h.metadata.rawData,P="",p=0;p<C.length;p++)P+=String.fromCharCode(C[p]);var I=g();b({data:P,addLength1:!0,objectId:I}),f("endobj");var O=g();b({data:i(h.metadata.toUnicode),addLength1:!0,objectId:O}),f("endobj");var D=g();f("<<"),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f("/StemV "+h.metadata.stemV),f("/Type /FontDescriptor"),f("/FontFile2 "+I+" 0 R"),f("/Flags 96"),f("/FontBBox "+r.API.PDFObject.convert(h.metadata.bbox)),f("/FontName /"+Hr(h.fontName)),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f(">>"),f("endobj"),h.objectNumber=g();for(var k=0;k<h.metadata.hmtx.widths.length;k++)h.metadata.hmtx.widths[k]=parseInt(h.metadata.hmtx.widths[k]*(1e3/h.metadata.head.unitsPerEm));f("<</Subtype/TrueType/Type/Font/ToUnicode "+O+" 0 R/BaseFont/"+Hr(h.fontName)+"/FontDescriptor "+D+" 0 R/Encoding/"+h.encoding+" /FirstChar 29 /LastChar 255 /Widths "+r.API.PDFObject.convert(h.metadata.hmtx.widths)+">>"),f("endobj"),h.isAlreadyPutted=!0}})(a)}]);var s=function(a){var c,h=a.text||"",f=a.x,g=a.y,b=a.options||{},C=a.mutex||{},P=C.pdfEscape,p=C.activeFontKey,I=C.fonts,O=p,D="",k=0,j="",J=I[O].encoding;if(I[O].encoding!=="Identity-H")return{text:h,x:f,y:g,options:b,mutex:C};for(j=h,O=p,Array.isArray(h)&&(j=h[0]),k=0;k<j.length;k+=1)I[O].metadata.hasOwnProperty("cmap")&&(c=I[O].metadata.cmap.unicode.codeMap[j[k].charCodeAt(0)]),c||j[k].charCodeAt(0)<256&&I[O].metadata.hasOwnProperty("Unicode")?D+=j[k]:D+="";var lt="";return parseInt(O.slice(1))<14||J==="WinAnsiEncoding"?lt=P(D,O).split("").map(function(ht){return ht.charCodeAt(0).toString(16)}).join(""):J==="Identity-H"&&(lt=t(D,I[O])),C.isHex=!0,{text:lt,x:f,y:g,options:b,mutex:C}};n.events.push(["postProcessText",function(a){var c=a.text||"",h=[],f={text:c,x:a.x,y:a.y,options:a.options,mutex:a.mutex};if(Array.isArray(c)){var g=0;for(g=0;g<c.length;g+=1)Array.isArray(c[g])&&c[g].length===3?h.push([s(Object.assign({},f,{text:c[g][0]})).text,c[g][1],c[g][2]]):h.push(s(Object.assign({},f,{text:c[g]})).text);a.text=h}else a.text=s(Object.assign({},f,{text:c})).text}])}(Wt),function(r){var n=function(){return this.internal.vFS===void 0&&(this.internal.vFS={}),!0};r.existsFileInVFS=function(t){return n.call(this),this.internal.vFS[t]!==void 0},r.addFileToVFS=function(t,i){return n.call(this),this.internal.vFS[t]=i,this},r.getFileFromVFS=function(t){return n.call(this),this.internal.vFS[t]!==void 0?this.internal.vFS[t]:null}}(Wt.API),function(r){r.__bidiEngine__=r.prototype.__bidiEngine__=function(i){var s,a,c,h,f,g,b,C=n,P=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],p=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],I={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},O={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},D=["(",")","(","<",">","<","[","]","[","{","}","{","\xAB","\xBB","\xAB","\u2039","\u203A","\u2039","\u2045","\u2046","\u2045","\u207D","\u207E","\u207D","\u208D","\u208E","\u208D","\u2264","\u2265","\u2264","\u2329","\u232A","\u2329","\uFE59","\uFE5A","\uFE59","\uFE5B","\uFE5C","\uFE5B","\uFE5D","\uFE5E","\uFE5D","\uFE64","\uFE65","\uFE64"],k=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),j=!1,J=0;this.__bidiEngine__={};var lt=function(N){var A=N.charCodeAt(),z=A>>8,B=O[z];return B!==void 0?C[256*B+(255&A)]:z===252||z===253?"AL":k.test(z)?"L":z===8?"R":"N"},ht=function(N){for(var A,z=0;z<N.length;z++){if((A=lt(N.charAt(z)))==="L")return!1;if(A==="R")return!0}return!1},_t=function(N,A,z,B){var at,nt,ft,Z,pt=A[B];switch(pt){case"L":case"R":j=!1;break;case"N":case"AN":break;case"EN":j&&(pt="AN");break;case"AL":j=!0,pt="R";break;case"WS":pt="N";break;case"CS":B<1||B+1>=A.length||(at=z[B-1])!=="EN"&&at!=="AN"||(nt=A[B+1])!=="EN"&&nt!=="AN"?pt="N":j&&(nt="AN"),pt=nt===at?nt:"N";break;case"ES":pt=(at=B>0?z[B-1]:"B")==="EN"&&B+1<A.length&&A[B+1]==="EN"?"EN":"N";break;case"ET":if(B>0&&z[B-1]==="EN"){pt="EN";break}if(j){pt="N";break}for(ft=B+1,Z=A.length;ft<Z&&A[ft]==="ET";)ft++;pt=ft<Z&&A[ft]==="EN"?"EN":"N";break;case"NSM":if(c&&!h){for(Z=A.length,ft=B+1;ft<Z&&A[ft]==="NSM";)ft++;if(ft<Z){var dt=N[B],Mt=dt>=1425&&dt<=2303||dt===64286;if(at=A[ft],Mt&&(at==="R"||at==="AL")){pt="R";break}}}pt=B<1||(at=A[B-1])==="B"?"N":z[B-1];break;case"B":j=!1,s=!0,pt=J;break;case"S":a=!0,pt="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":j=!1;break;case"BN":pt="N"}return pt},tt=function(N,A,z){var B=N.split("");return z&&R(B,z,{hiLevel:J}),B.reverse(),A&&A.reverse(),B.join("")},R=function(N,A,z){var B,at,nt,ft,Z,pt=-1,dt=N.length,Mt=0,x=[],F=J?p:P,T=[];for(j=!1,s=!1,a=!1,at=0;at<dt;at++)T[at]=lt(N[at]);for(nt=0;nt<dt;nt++){if(Z=Mt,x[nt]=_t(N,T,x,nt),B=240&(Mt=F[Z][I[x[nt]]]),Mt&=15,A[nt]=ft=F[Mt][5],B>0)if(B===16){for(at=pt;at<nt;at++)A[at]=1;pt=-1}else pt=-1;if(F[Mt][6])pt===-1&&(pt=nt);else if(pt>-1){for(at=pt;at<nt;at++)A[at]=ft;pt=-1}T[nt]==="B"&&(A[nt]=0),z.hiLevel|=ft}a&&function(H,$,Q){for(var et=0;et<Q;et++)if(H[et]==="S"){$[et]=J;for(var it=et-1;it>=0&&H[it]==="WS";it--)$[it]=J}}(T,A,dt)},vt=function(N,A,z,B,at){if(!(at.hiLevel<N)){if(N===1&&J===1&&!s)return A.reverse(),void(z&&z.reverse());for(var nt,ft,Z,pt,dt=A.length,Mt=0;Mt<dt;){if(B[Mt]>=N){for(Z=Mt+1;Z<dt&&B[Z]>=N;)Z++;for(pt=Mt,ft=Z-1;pt<ft;pt++,ft--)nt=A[pt],A[pt]=A[ft],A[ft]=nt,z&&(nt=z[pt],z[pt]=z[ft],z[ft]=nt);Mt=Z}Mt++}}},gt=function(N,A,z){var B=N.split(""),at={hiLevel:J};return z||(z=[]),R(B,z,at),function(nt,ft,Z){if(Z.hiLevel!==0&&b)for(var pt,dt=0;dt<nt.length;dt++)ft[dt]===1&&(pt=D.indexOf(nt[dt]))>=0&&(nt[dt]=D[pt+1])}(B,z,at),vt(2,B,A,z,at),vt(1,B,A,z,at),B.join("")};return this.__bidiEngine__.doBidiReorder=function(N,A,z){if(function(at,nt){if(nt)for(var ft=0;ft<at.length;ft++)nt[ft]=ft;h===void 0&&(h=ht(at)),g===void 0&&(g=ht(at))}(N,A),c||!f||g)if(c&&f&&h^g)J=h?1:0,N=tt(N,A,z);else if(!c&&f&&g)J=h?1:0,N=gt(N,A,z),N=tt(N,A);else if(!c||h||f||g){if(c&&!f&&h^g)N=tt(N,A),h?(J=0,N=gt(N,A,z)):(J=1,N=gt(N,A,z),N=tt(N,A));else if(c&&h&&!f&&g)J=1,N=gt(N,A,z),N=tt(N,A);else if(!c&&!f&&h^g){var B=b;h?(J=1,N=gt(N,A,z),J=0,b=!1,N=gt(N,A,z),b=B):(J=0,N=gt(N,A,z),N=tt(N,A),J=1,b=!1,N=gt(N,A,z),b=B,N=tt(N,A))}}else J=0,N=gt(N,A,z);else J=h?1:0,N=gt(N,A,z);return N},this.__bidiEngine__.setOptions=function(N){N&&(c=N.isInputVisual,f=N.isOutputVisual,h=N.isInputRtl,g=N.isOutputRtl,b=N.isSymmetricSwapping)},this.__bidiEngine__.setOptions(i),this.__bidiEngine__};var n=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],t=new r.__bidiEngine__({isInputVisual:!0});r.API.events.push(["postProcessText",function(i){var s=i.text,a=(i.x,i.y,i.options||{}),c=(i.mutex,a.lang,[]);if(a.isInputVisual=typeof a.isInputVisual!="boolean"||a.isInputVisual,t.setOptions(a),Object.prototype.toString.call(s)==="[object Array]"){var h=0;for(c=[],h=0;h<s.length;h+=1)Object.prototype.toString.call(s[h])==="[object Array]"?c.push([t.doBidiReorder(s[h][0]),s[h][1],s[h][2]]):c.push([t.doBidiReorder(s[h])]);i.text=c}else i.text=t.doBidiReorder(s);t.setOptions({isInputVisual:!0})}])}(Wt),Wt.API.TTFFont=function(){function r(n){var t;if(this.rawData=n,t=this.contents=new Gi(n),this.contents.pos=4,t.readString(4)==="ttcf")throw new Error("TTCF not supported.");t.pos=0,this.parse(),this.subset=new w1(this),this.registerTTF()}return r.open=function(n){return new r(n)},r.prototype.parse=function(){return this.directory=new a1(this.contents),this.head=new l1(this),this.name=new d1(this),this.cmap=new cu(this),this.toUnicode={},this.hhea=new c1(this),this.maxp=new p1(this),this.hmtx=new g1(this),this.post=new h1(this),this.os2=new u1(this),this.loca=new y1(this),this.glyf=new m1(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},r.prototype.registerTTF=function(){var n,t,i,s,a;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var c,h,f,g;for(g=[],c=0,h=(f=this.bbox).length;c<h;c++)n=f[c],g.push(Math.round(n*this.scaleFactor));return g}).call(this),this.stemV=0,this.post.exists?(i=255&(s=this.post.italic_angle),32768&(t=s>>16)&&(t=-(1+(65535^t))),this.italicAngle=+(t+"."+i)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=(a=this.familyClass)===1||a===2||a===3||a===4||a===5||a===7,this.isScript=this.familyClass===10,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),this.italicAngle!==0&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},r.prototype.characterToGlyph=function(n){var t;return((t=this.cmap.unicode)!=null?t.codeMap[n]:void 0)||0},r.prototype.widthOfGlyph=function(n){var t;return t=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(n).advance*t},r.prototype.widthOfString=function(n,t,i){var s,a,c,h;for(c=0,a=0,h=(n=""+n).length;0<=h?a<h:a>h;a=0<=h?++a:--a)s=n.charCodeAt(a),c+=this.widthOfGlyph(this.characterToGlyph(s))+i*(1e3/t)||0;return c*(t/1e3)},r.prototype.lineHeight=function(n,t){var i;return t==null&&(t=!1),i=t?this.lineGap:0,(this.ascender+i-this.decender)/1e3*n},r}();Gi=function(){function r(n){this.data=n??[],this.pos=0,this.length=this.data.length}return r.prototype.readByte=function(){return this.data[this.pos++]},r.prototype.writeByte=function(n){return this.data[this.pos++]=n},r.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},r.prototype.writeUInt32=function(n){return this.writeByte(n>>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n)},r.prototype.readInt32=function(){var n;return(n=this.readUInt32())>=2147483648?n-4294967296:n},r.prototype.writeInt32=function(n){return n<0&&(n+=4294967296),this.writeUInt32(n)},r.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},r.prototype.writeUInt16=function(n){return this.writeByte(n>>8&255),this.writeByte(255&n)},r.prototype.readInt16=function(){var n;return(n=this.readUInt16())>=32768?n-65536:n},r.prototype.writeInt16=function(n){return n<0&&(n+=65536),this.writeUInt16(n)},r.prototype.readString=function(n){var t,i;for(i=[],t=0;0<=n?t<n:t>n;t=0<=n?++t:--t)i[t]=String.fromCharCode(this.readByte());return i.join("")},r.prototype.writeString=function(n){var t,i,s;for(s=[],t=0,i=n.length;0<=i?t<i:t>i;t=0<=i?++t:--t)s.push(this.writeByte(n.charCodeAt(t)));return s},r.prototype.readShort=function(){return this.readInt16()},r.prototype.writeShort=function(n){return this.writeInt16(n)},r.prototype.readLongLong=function(){var n,t,i,s,a,c,h,f;return n=this.readByte(),t=this.readByte(),i=this.readByte(),s=this.readByte(),a=this.readByte(),c=this.readByte(),h=this.readByte(),f=this.readByte(),128&n?-1*(72057594037927940*(255^n)+281474976710656*(255^t)+1099511627776*(255^i)+4294967296*(255^s)+16777216*(255^a)+65536*(255^c)+256*(255^h)+(255^f)+1):72057594037927940*n+281474976710656*t+1099511627776*i+4294967296*s+16777216*a+65536*c+256*h+f},r.prototype.writeLongLong=function(n){var t,i;return t=Math.floor(n/4294967296),i=4294967295&n,this.writeByte(t>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t),this.writeByte(i>>24&255),this.writeByte(i>>16&255),this.writeByte(i>>8&255),this.writeByte(255&i)},r.prototype.readInt=function(){return this.readInt32()},r.prototype.writeInt=function(n){return this.writeInt32(n)},r.prototype.read=function(n){var t,i;for(t=[],i=0;0<=n?i<n:i>n;i=0<=n?++i:--i)t.push(this.readByte());return t},r.prototype.write=function(n){var t,i,s,a;for(a=[],i=0,s=n.length;i<s;i++)t=n[i],a.push(this.writeByte(t));return a},r}(),a1=function(){var r;function n(t){var i,s,a;for(this.scalarType=t.readInt(),this.tableCount=t.readShort(),this.searchRange=t.readShort(),this.entrySelector=t.readShort(),this.rangeShift=t.readShort(),this.tables={},s=0,a=this.tableCount;0<=a?s<a:s>a;s=0<=a?++s:--s)i={tag:t.readString(4),checksum:t.readInt(),offset:t.readInt(),length:t.readInt()},this.tables[i.tag]=i}return n.prototype.encode=function(t){var i,s,a,c,h,f,g,b,C,P,p,I,O;for(O in p=Object.keys(t).length,f=Math.log(2),C=16*Math.floor(Math.log(p)/f),c=Math.floor(C/f),b=16*p-C,(s=new Gi).writeInt(this.scalarType),s.writeShort(p),s.writeShort(C),s.writeShort(c),s.writeShort(b),a=16*p,g=s.pos+a,h=null,I=[],t)for(P=t[O],s.writeString(O),s.writeInt(r(P)),s.writeInt(g),s.writeInt(P.length),I=I.concat(P),O==="head"&&(h=g),g+=P.length;g%4;)I.push(0),g++;return s.write(I),i=2981146554-r(s.data),s.pos=h+8,s.writeUInt32(i),s.data},r=function(t){var i,s,a,c;for(t=uu.call(t);t.length%4;)t.push(0);for(a=new Gi(t),s=0,i=0,c=t.length;i<c;i=i+=4)s+=a.readUInt32();return 4294967295&s},n}(),s1={}.hasOwnProperty,ai=function(r,n){for(var t in n)s1.call(n,t)&&(r[t]=n[t]);function i(){this.constructor=r}return i.prototype=n.prototype,r.prototype=new i,r.__super__=n.prototype,r};Vn=function(){function r(n){var t;this.file=n,t=this.file.directory.tables[this.tag],this.exists=!!t,t&&(this.offset=t.offset,this.length=t.length,this.parse(this.file.contents))}return r.prototype.parse=function(){},r.prototype.encode=function(){},r.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},r}();l1=function(r){function n(){return n.__super__.constructor.apply(this,arguments)}return ai(n,Vn),n.prototype.tag="head",n.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.revision=t.readInt(),this.checkSumAdjustment=t.readInt(),this.magicNumber=t.readInt(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.readLongLong(),this.modified=t.readLongLong(),this.xMin=t.readShort(),this.yMin=t.readShort(),this.xMax=t.readShort(),this.yMax=t.readShort(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort(),this.indexToLocFormat=t.readShort(),this.glyphDataFormat=t.readShort()},n.prototype.encode=function(t){var i;return(i=new Gi).writeInt(this.version),i.writeInt(this.revision),i.writeInt(this.checkSumAdjustment),i.writeInt(this.magicNumber),i.writeShort(this.flags),i.writeShort(this.unitsPerEm),i.writeLongLong(this.created),i.writeLongLong(this.modified),i.writeShort(this.xMin),i.writeShort(this.yMin),i.writeShort(this.xMax),i.writeShort(this.yMax),i.writeShort(this.macStyle),i.writeShort(this.lowestRecPPEM),i.writeShort(this.fontDirectionHint),i.writeShort(t),i.writeShort(this.glyphDataFormat),i.data},n}(),Zc=function(){function r(n,t){var i,s,a,c,h,f,g,b,C,P,p,I,O,D,k,j,J;switch(this.platformID=n.readUInt16(),this.encodingID=n.readShort(),this.offset=t+n.readInt(),C=n.pos,n.pos=this.offset,this.format=n.readUInt16(),this.length=n.readUInt16(),this.language=n.readUInt16(),this.isUnicode=this.platformID===3&&this.encodingID===1&&this.format===4||this.platformID===0&&this.format===4,this.codeMap={},this.format){case 0:for(f=0;f<256;++f)this.codeMap[f]=n.readByte();break;case 4:for(p=n.readUInt16(),P=p/2,n.pos+=6,a=function(){var lt,ht;for(ht=[],f=lt=0;0<=P?lt<P:lt>P;f=0<=P?++lt:--lt)ht.push(n.readUInt16());return ht}(),n.pos+=2,O=function(){var lt,ht;for(ht=[],f=lt=0;0<=P?lt<P:lt>P;f=0<=P?++lt:--lt)ht.push(n.readUInt16());return ht}(),g=function(){var lt,ht;for(ht=[],f=lt=0;0<=P?lt<P:lt>P;f=0<=P?++lt:--lt)ht.push(n.readUInt16());return ht}(),b=function(){var lt,ht;for(ht=[],f=lt=0;0<=P?lt<P:lt>P;f=0<=P?++lt:--lt)ht.push(n.readUInt16());return ht}(),s=(this.length-n.pos+this.offset)/2,h=function(){var lt,ht;for(ht=[],f=lt=0;0<=s?lt<s:lt>s;f=0<=s?++lt:--lt)ht.push(n.readUInt16());return ht}(),f=k=0,J=a.length;k<J;f=++k)for(D=a[f],i=j=I=O[f];I<=D?j<=D:j>=D;i=I<=D?++j:--j)b[f]===0?c=i+g[f]:(c=h[b[f]/2+(i-I)-(P-f)]||0)!==0&&(c+=g[f]),this.codeMap[i]=65535&c}n.pos=C}return r.encode=function(n,t){var i,s,a,c,h,f,g,b,C,P,p,I,O,D,k,j,J,lt,ht,_t,tt,R,vt,gt,N,A,z,B,at,nt,ft,Z,pt,dt,Mt,x,F,T,H,$,Q,et,it,kt,Pt,jt;switch(B=new Gi,c=Object.keys(n).sort(function(Dt,Gt){return Dt-Gt}),t){case"macroman":for(O=0,D=function(){var Dt=[];for(I=0;I<256;++I)Dt.push(0);return Dt}(),j={0:0},a={},at=0,pt=c.length;at<pt;at++)j[it=n[s=c[at]]]==null&&(j[it]=++O),a[s]={old:n[s],new:j[n[s]]},D[s]=j[n[s]];return B.writeUInt16(1),B.writeUInt16(0),B.writeUInt32(12),B.writeUInt16(0),B.writeUInt16(262),B.writeUInt16(0),B.write(D),{charMap:a,subtable:B.data,maxGlyphID:O+1};case"unicode":for(A=[],C=[],J=0,j={},i={},k=g=null,nt=0,dt=c.length;nt<dt;nt++)j[ht=n[s=c[nt]]]==null&&(j[ht]=++J),i[s]={old:ht,new:j[ht]},h=j[ht]-s,k!=null&&h===g||(k&&C.push(k),A.push(s),g=h),k=s;for(k&&C.push(k),C.push(65535),A.push(65535),gt=2*(vt=A.length),R=2*Math.pow(Math.log(vt)/Math.LN2,2),P=Math.log(R/2)/Math.LN2,tt=2*vt-R,f=[],_t=[],p=[],I=ft=0,Mt=A.length;ft<Mt;I=++ft){if(N=A[I],b=C[I],N===65535){f.push(0),_t.push(0);break}if(N-(z=i[N].new)>=32768)for(f.push(0),_t.push(2*(p.length+vt-I)),s=Z=N;N<=b?Z<=b:Z>=b;s=N<=b?++Z:--Z)p.push(i[s].new);else f.push(z-N),_t.push(0)}for(B.writeUInt16(3),B.writeUInt16(1),B.writeUInt32(12),B.writeUInt16(4),B.writeUInt16(16+8*vt+2*p.length),B.writeUInt16(0),B.writeUInt16(gt),B.writeUInt16(R),B.writeUInt16(P),B.writeUInt16(tt),Q=0,x=C.length;Q<x;Q++)s=C[Q],B.writeUInt16(s);for(B.writeUInt16(0),et=0,F=A.length;et<F;et++)s=A[et],B.writeUInt16(s);for(kt=0,T=f.length;kt<T;kt++)h=f[kt],B.writeUInt16(h);for(Pt=0,H=_t.length;Pt<H;Pt++)lt=_t[Pt],B.writeUInt16(lt);for(jt=0,$=p.length;jt<$;jt++)O=p[jt],B.writeUInt16(O);return{charMap:i,subtable:B.data,maxGlyphID:J+1}}},r}(),cu=function(r){function n(){return n.__super__.constructor.apply(this,arguments)}return ai(n,Vn),n.prototype.tag="cmap",n.prototype.parse=function(t){var i,s,a;for(t.pos=this.offset,this.version=t.readUInt16(),a=t.readUInt16(),this.tables=[],this.unicode=null,s=0;0<=a?s<a:s>a;s=0<=a?++s:--s)i=new Zc(t,this.offset),this.tables.push(i),i.isUnicode&&this.unicode==null&&(this.unicode=i);return!0},n.encode=function(t,i){var s,a;return i==null&&(i="macroman"),s=Zc.encode(t,i),(a=new Gi).writeUInt16(0),a.writeUInt16(1),s.table=a.data.concat(s.subtable),s},n}(),c1=function(r){function n(){return n.__super__.constructor.apply(this,arguments)}return ai(n,Vn),n.prototype.tag="hhea",n.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.ascender=t.readShort(),this.decender=t.readShort(),this.lineGap=t.readShort(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort(),this.minRightSideBearing=t.readShort(),this.xMaxExtent=t.readShort(),this.caretSlopeRise=t.readShort(),this.caretSlopeRun=t.readShort(),this.caretOffset=t.readShort(),t.pos+=8,this.metricDataFormat=t.readShort(),this.numberOfMetrics=t.readUInt16()},n}(),u1=function(r){function n(){return n.__super__.constructor.apply(this,arguments)}return ai(n,Vn),n.prototype.tag="OS/2",n.prototype.parse=function(t){if(t.pos=this.offset,this.version=t.readUInt16(),this.averageCharWidth=t.readShort(),this.weightClass=t.readUInt16(),this.widthClass=t.readUInt16(),this.type=t.readShort(),this.ySubscriptXSize=t.readShort(),this.ySubscriptYSize=t.readShort(),this.ySubscriptXOffset=t.readShort(),this.ySubscriptYOffset=t.readShort(),this.ySuperscriptXSize=t.readShort(),this.ySuperscriptYSize=t.readShort(),this.ySuperscriptXOffset=t.readShort(),this.ySuperscriptYOffset=t.readShort(),this.yStrikeoutSize=t.readShort(),this.yStrikeoutPosition=t.readShort(),this.familyClass=t.readShort(),this.panose=function(){var i,s;for(s=[],i=0;i<10;++i)s.push(t.readByte());return s}(),this.charRange=function(){var i,s;for(s=[],i=0;i<4;++i)s.push(t.readInt());return s}(),this.vendorID=t.readString(4),this.selection=t.readShort(),this.firstCharIndex=t.readShort(),this.lastCharIndex=t.readShort(),this.version>0&&(this.ascent=t.readShort(),this.descent=t.readShort(),this.lineGap=t.readShort(),this.winAscent=t.readShort(),this.winDescent=t.readShort(),this.codePageRange=function(){var i,s;for(s=[],i=0;i<2;i=++i)s.push(t.readInt());return s}(),this.version>1))return this.xHeight=t.readShort(),this.capHeight=t.readShort(),this.defaultChar=t.readShort(),this.breakChar=t.readShort(),this.maxContext=t.readShort()},n}(),h1=function(r){function n(){return n.__super__.constructor.apply(this,arguments)}return ai(n,Vn),n.prototype.tag="post",n.prototype.parse=function(t){var i,s,a;switch(t.pos=this.offset,this.format=t.readInt(),this.italicAngle=t.readInt(),this.underlinePosition=t.readShort(),this.underlineThickness=t.readShort(),this.isFixedPitch=t.readInt(),this.minMemType42=t.readInt(),this.maxMemType42=t.readInt(),this.minMemType1=t.readInt(),this.maxMemType1=t.readInt(),this.format){case 65536:break;case 131072:var c;for(s=t.readUInt16(),this.glyphNameIndex=[],c=0;0<=s?c<s:c>s;c=0<=s?++c:--c)this.glyphNameIndex.push(t.readUInt16());for(this.names=[],a=[];t.pos<this.offset+this.length;)i=t.readByte(),a.push(this.names.push(t.readString(i)));return a;case 151552:return s=t.readUInt16(),this.offsets=t.read(s);case 196608:break;case 262144:return this.map=(function(){var h,f,g;for(g=[],c=h=0,f=this.file.maxp.numGlyphs;0<=f?h<f:h>f;c=0<=f?++h:--h)g.push(t.readUInt32());return g}).call(this)}},n}(),f1=function(r,n){this.raw=r,this.length=r.length,this.platformID=n.platformID,this.encodingID=n.encodingID,this.languageID=n.languageID},d1=function(r){function n(){return n.__super__.constructor.apply(this,arguments)}return ai(n,Vn),n.prototype.tag="name",n.prototype.parse=function(t){var i,s,a,c,h,f,g,b,C,P,p;for(t.pos=this.offset,t.readShort(),i=t.readShort(),f=t.readShort(),s=[],c=0;0<=i?c<i:c>i;c=0<=i?++c:--c)s.push({platformID:t.readShort(),encodingID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:this.offset+f+t.readShort()});for(g={},c=C=0,P=s.length;C<P;c=++C)a=s[c],t.pos=a.offset,b=t.readString(a.length),h=new f1(b,a),g[p=a.nameID]==null&&(g[p]=[]),g[a.nameID].push(h);this.strings=g,this.copyright=g[0],this.fontFamily=g[1],this.fontSubfamily=g[2],this.uniqueSubfamily=g[3],this.fontName=g[4],this.version=g[5];try{this.postscriptName=g[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch{this.postscriptName=g[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=g[7],this.manufacturer=g[8],this.designer=g[9],this.description=g[10],this.vendorUrl=g[11],this.designerUrl=g[12],this.license=g[13],this.licenseUrl=g[14],this.preferredFamily=g[15],this.preferredSubfamily=g[17],this.compatibleFull=g[18],this.sampleText=g[19]},n}(),p1=function(r){function n(){return n.__super__.constructor.apply(this,arguments)}return ai(n,Vn),n.prototype.tag="maxp",n.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.numGlyphs=t.readUInt16(),this.maxPoints=t.readUInt16(),this.maxContours=t.readUInt16(),this.maxCompositePoints=t.readUInt16(),this.maxComponentContours=t.readUInt16(),this.maxZones=t.readUInt16(),this.maxTwilightPoints=t.readUInt16(),this.maxStorage=t.readUInt16(),this.maxFunctionDefs=t.readUInt16(),this.maxInstructionDefs=t.readUInt16(),this.maxStackElements=t.readUInt16(),this.maxSizeOfInstructions=t.readUInt16(),this.maxComponentElements=t.readUInt16(),this.maxComponentDepth=t.readUInt16()},n}(),g1=function(r){function n(){return n.__super__.constructor.apply(this,arguments)}return ai(n,Vn),n.prototype.tag="hmtx",n.prototype.parse=function(t){var i,s,a,c,h,f,g;for(t.pos=this.offset,this.metrics=[],i=0,f=this.file.hhea.numberOfMetrics;0<=f?i<f:i>f;i=0<=f?++i:--i)this.metrics.push({advance:t.readUInt16(),lsb:t.readInt16()});for(a=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var b,C;for(C=[],i=b=0;0<=a?b<a:b>a;i=0<=a?++b:--b)C.push(t.readInt16());return C}(),this.widths=(function(){var b,C,P,p;for(p=[],b=0,C=(P=this.metrics).length;b<C;b++)c=P[b],p.push(c.advance);return p}).call(this),s=this.widths[this.widths.length-1],g=[],i=h=0;0<=a?h<a:h>a;i=0<=a?++h:--h)g.push(this.widths.push(s));return g},n.prototype.forGlyph=function(t){return t in this.metrics?this.metrics[t]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[t-this.metrics.length]}},n}(),uu=[].slice,m1=function(r){function n(){return n.__super__.constructor.apply(this,arguments)}return ai(n,Vn),n.prototype.tag="glyf",n.prototype.parse=function(){return this.cache={}},n.prototype.glyphFor=function(t){var i,s,a,c,h,f,g,b,C,P;return t in this.cache?this.cache[t]:(c=this.file.loca,i=this.file.contents,s=c.indexOf(t),(a=c.lengthOf(t))===0?this.cache[t]=null:(i.pos=this.offset+s,h=(f=new Gi(i.read(a))).readShort(),b=f.readShort(),P=f.readShort(),g=f.readShort(),C=f.readShort(),this.cache[t]=h===-1?new b1(f,b,P,g,C):new v1(f,h,b,P,g,C),this.cache[t]))},n.prototype.encode=function(t,i,s){var a,c,h,f,g;for(h=[],c=[],f=0,g=i.length;f<g;f++)a=t[i[f]],c.push(h.length),a&&(h=h.concat(a.encode(s)));return c.push(h.length),{table:h,offsets:c}},n}(),v1=function(){function r(n,t,i,s,a,c){this.raw=n,this.numberOfContours=t,this.xMin=i,this.yMin=s,this.xMax=a,this.yMax=c,this.compound=!1}return r.prototype.encode=function(){return this.raw.data},r}(),b1=function(){function r(n,t,i,s,a){var c,h;for(this.raw=n,this.xMin=t,this.yMin=i,this.xMax=s,this.yMax=a,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],c=this.raw;h=c.readShort(),this.glyphOffsets.push(c.pos),this.glyphIDs.push(c.readUInt16()),32&h;)c.pos+=1&h?4:2,128&h?c.pos+=8:64&h?c.pos+=4:8&h&&(c.pos+=2)}return r.prototype.encode=function(){var n,t,i;for(t=new Gi(uu.call(this.raw.data)),n=0,i=this.glyphIDs.length;n<i;++n)t.pos=this.glyphOffsets[n];return t.data},r}(),y1=function(r){function n(){return n.__super__.constructor.apply(this,arguments)}return ai(n,Vn),n.prototype.tag="loca",n.prototype.parse=function(t){var i,s;return t.pos=this.offset,i=this.file.head.indexToLocFormat,this.offsets=i===0?(function(){var a,c;for(c=[],s=0,a=this.length;s<a;s+=2)c.push(2*t.readUInt16());return c}).call(this):(function(){var a,c;for(c=[],s=0,a=this.length;s<a;s+=4)c.push(t.readUInt32());return c}).call(this)},n.prototype.indexOf=function(t){return this.offsets[t]},n.prototype.lengthOf=function(t){return this.offsets[t+1]-this.offsets[t]},n.prototype.encode=function(t,i){for(var s=new Uint32Array(this.offsets.length),a=0,c=0,h=0;h<s.length;++h)if(s[h]=a,c<i.length&&i[c]==h){++c,s[h]=a;var f=this.offsets[h],g=this.offsets[h+1]-f;g>0&&(a+=g)}for(var b=new Array(4*s.length),C=0;C<s.length;++C)b[4*C+3]=255&s[C],b[4*C+2]=(65280&s[C])>>8,b[4*C+1]=(16711680&s[C])>>16,b[4*C]=(4278190080&s[C])>>24;return b},n}(),w1=function(){function r(n){this.font=n,this.subset={},this.unicodes={},this.next=33}return r.prototype.generateCmap=function(){var n,t,i,s,a;for(t in s=this.font.cmap.tables[0].codeMap,n={},a=this.subset)i=a[t],n[t]=s[i];return n},r.prototype.glyphsFor=function(n){var t,i,s,a,c,h,f;for(s={},c=0,h=n.length;c<h;c++)s[a=n[c]]=this.font.glyf.glyphFor(a);for(a in t=[],s)(i=s[a])!=null&&i.compound&&t.push.apply(t,i.glyphIDs);if(t.length>0)for(a in f=this.glyphsFor(t))i=f[a],s[a]=i;return s},r.prototype.encode=function(n,t){var i,s,a,c,h,f,g,b,C,P,p,I,O,D,k;for(s in i=cu.encode(this.generateCmap(),"unicode"),c=this.glyphsFor(n),p={0:0},k=i.charMap)p[(f=k[s]).old]=f.new;for(I in P=i.maxGlyphID,c)I in p||(p[I]=P++);return b=function(j){var J,lt;for(J in lt={},j)lt[j[J]]=J;return lt}(p),C=Object.keys(b).sort(function(j,J){return j-J}),O=function(){var j,J,lt;for(lt=[],j=0,J=C.length;j<J;j++)h=C[j],lt.push(b[h]);return lt}(),a=this.font.glyf.encode(c,O,p),g=this.font.loca.encode(a.offsets,O),D={cmap:this.font.cmap.raw(),glyf:a.table,loca:g,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(t)},this.font.os2.exists&&(D["OS/2"]=this.font.os2.raw()),this.font.directory.encode(D)},r}();Wt.API.PDFObject=function(){var r;function n(){}return r=function(t,i){return(Array(i+1).join("0")+t).slice(-i)},n.convert=function(t){var i,s,a,c;if(Array.isArray(t))return"["+function(){var h,f,g;for(g=[],h=0,f=t.length;h<f;h++)i=t[h],g.push(n.convert(i));return g}().join(" ")+"]";if(typeof t=="string")return"/"+t;if(t?.isString)return"("+t+")";if(t instanceof Date)return"(D:"+r(t.getUTCFullYear(),4)+r(t.getUTCMonth(),2)+r(t.getUTCDate(),2)+r(t.getUTCHours(),2)+r(t.getUTCMinutes(),2)+r(t.getUTCSeconds(),2)+"Z)";if({}.toString.call(t)==="[object Object]"){for(s in a=["<<"],t)c=t[s],a.push("/"+s+" "+n.convert(c));return a.push(">>"),a.join(`
`)}return""+t},n}();ol=Wt});function th(r,n){if(r&1&&Kt(0,"div",9),r&2){let t=yi();nr("height",t.pbThickness,"px")("color",t.pbColor),La("ngx-position-absolute",t.loaderId!==t.defaultConfig.masterLoaderId)("loading-foreground",t.showForeground)("foreground-closing",t.foregroundClosing)("fast-closing",t.foregroundClosing&&t.fastFadeOut),Ae("ngClass","ngx-progress-bar-"+t.pbDirection)}}function eh(r,n){if(r&1&&Kt(0,"img",10),r&2){let t=yi();nr("width",t.logoSize,"px")("height",t.logoSize,"px")("top",t.logoTop),Ae("ngClass",t.logoPosition)("src",t.trustedLogoUrl,$l)}}function nh(r,n){r&1&&Kt(0,"div")}function ih(r,n){if(r&1&&(st(0,"div"),zn(1,nh,1,0,"div",11),rt()),r&2){let t=yi();Fs(t.fgSpinnerClass),He(),Ae("ngForOf",t.fgDivs)}}function rh(r,n){r&1&&Is(0)}function oh(r,n){if(r&1&&zn(0,rh,1,0,"ng-container",12),r&2){let t=yi();Ae("ngTemplateOutlet",t.fgsTemplate)}}function ah(r,n){r&1&&Kt(0,"div")}function sh(r,n){if(r&1&&(st(0,"div"),zn(1,ah,1,0,"div",11),rt()),r&2){let t=yi();Fs(t.bgSpinnerClass),He(),Ae("ngForOf",t.bgDivs)}}function lh(r,n){r&1&&Is(0)}function ch(r,n){if(r&1&&zn(0,lh,1,0,"ng-container",12),r&2){let t=yi();Ae("ngTemplateOutlet",t.bgsTemplate)}}var Ds=function(r){return r.ballScaleMultiple="ball-scale-multiple",r.ballSpin="ball-spin",r.ballSpinClockwise="ball-spin-clockwise",r.ballSpinClockwiseFadeRotating="ball-spin-clockwise-fade-rotating",r.ballSpinFadeRotating="ball-spin-fade-rotating",r.chasingDots="chasing-dots",r.circle="circle",r.cubeGrid="cube-grid",r.doubleBounce="double-bounce",r.fadingCircle="fading-circle",r.foldingCube="folding-cube",r.pulse="pulse",r.rectangleBounce="rectangle-bounce",r.rectangleBounceParty="rectangle-bounce-party",r.rectangleBouncePulseOut="rectangle-bounce-pulse-out",r.rectangleBouncePulseOutRapid="rectangle-bounce-pulse-out-rapid",r.rotatingPlane="rotating-plane",r.squareJellyBox="square-jelly-box",r.squareLoader="square-loader",r.threeBounce="three-bounce",r.threeStrings="three-strings",r.wanderingCubes="wandering-cubes",r}(Ds||{}),Un=function(r){return r.bottomCenter="bottom-center",r.bottomLeft="bottom-left",r.bottomRight="bottom-right",r.centerCenter="center-center",r.centerLeft="center-left",r.centerRight="center-right",r.topCenter="top-center",r.topLeft="top-left",r.topRight="top-right",r}(Un||{}),yc=function(r){return r.leftToRight="ltr",r.rightToLeft="rtl",r}(yc||{}),Sa="fg-default",Na="bg-default",uh="master",hh={},gc=0,Es=0,mc=1001,vc=601,qr=!1,Lo=!0,fh=500,dh=300;var Oa={"ball-scale-multiple":{divs:3,class:"sk-ball-scale-multiple"},"ball-spin":{divs:8,class:"sk-ball-spin"},"ball-spin-clockwise":{divs:8,class:"sk-ball-spin-clockwise"},"ball-spin-clockwise-fade-rotating":{divs:8,class:"sk-ball-spin-clockwise-fade-rotating"},"ball-spin-fade-rotating":{divs:8,class:"sk-ball-spin-fade-rotating"},"chasing-dots":{divs:2,class:"sk-chasing-dots"},circle:{divs:12,class:"sk-circle"},"cube-grid":{divs:9,class:"sk-cube-grid"},"double-bounce":{divs:2,class:"sk-double-bounce"},"fading-circle":{divs:12,class:"sk-fading-circle"},"folding-cube":{divs:4,class:"sk-folding-cube"},pulse:{divs:1,class:"sk-pulse"},"rectangle-bounce":{divs:5,class:"sk-rectangle-bounce"},"rectangle-bounce-party":{divs:5,class:"sk-rectangle-bounce-party"},"rectangle-bounce-pulse-out":{divs:5,class:"sk-rectangle-bounce-pulse-out"},"rectangle-bounce-pulse-out-rapid":{divs:5,class:"sk-rectangle-bounce-pulse-out-rapid"},"rotating-plane":{divs:1,class:"sk-rotating-plane"},"square-jelly-box":{divs:2,class:"sk-square-jelly-box"},"square-loader":{divs:1,class:"sk-square-loader"},"three-bounce":{divs:3,class:"sk-three-bounce"},"three-strings":{divs:3,class:"sk-three-strings"},"wandering-cubes":{divs:2,class:"sk-wandering-cubes"}},ph={bgsColor:"#00ACC1",bgsOpacity:.5,bgsPosition:Un.bottomRight,bgsSize:60,bgsType:Ds.ballSpinClockwise,blur:5,delay:0,fastFadeOut:!1,fgsColor:"#00ACC1",fgsPosition:Un.centerCenter,fgsSize:60,fgsType:Ds.ballSpinClockwise,gap:24,logoPosition:Un.centerCenter,logoSize:120,logoUrl:"",masterLoaderId:uh,overlayBorderRadius:"0",overlayColor:"rgba(40, 40, 40, 0.8)",pbColor:"#00ACC1",pbDirection:yc.leftToRight,pbThickness:3,hasProgressBar:!0,text:"",textColor:"#FFFFFF",textPosition:Un.centerCenter,maxTime:-1,minTime:300},wc=new _a("ngxUiLoaderCustom.config"),gh=(()=>{class r{constructor(t){this.config=t,this.defaultConfig=Rr({},ph),this.config&&(this.config.minTime&&this.config.minTime<Es&&(this.config.minTime=Es),this.defaultConfig=Rr(Rr({},this.defaultConfig),this.config)),this.loaders={},this.showForeground=new xo({loaderId:"",isShow:!1}),this.showForeground$=this.showForeground.asObservable(),this.showBackground=new xo({loaderId:"",isShow:!1}),this.showBackground$=this.showBackground.asObservable(),this.fgClosing=new xo({loaderId:"",isShow:!1}),this.foregroundClosing$=this.fgClosing.asObservable(),this.bgClosing=new xo({loaderId:"",isShow:!1}),this.backgroundClosing$=this.bgClosing.asObservable()}bindLoaderData(t){let i=t===this.defaultConfig.masterLoaderId;if(this.loaders[t]){if(this.loaders[t].isBound)throw new Error(`[ngx-ui-loader] - loaderId "${t}" is duplicated.`);this.loaders[t].isBound=!0,this.loaders[t].isMaster=i,this.hasRunningTask(Lo,t)?this.showForeground.next({loaderId:t,isShow:!0}):this.hasRunningTask(qr,t)&&this.showBackground.next({loaderId:t,isShow:!0})}else this.createLoaderData(t,i,!0)}destroyLoaderData(t){this.stopAllLoader(t),delete this.loaders[t]}getDefaultConfig(){return Rr({},this.defaultConfig)}getLoaders(){return JSON.parse(JSON.stringify(this.loaders))}getLoader(t){return t=t||this.defaultConfig.masterLoaderId,this.loaders[t]?JSON.parse(JSON.stringify(this.loaders[t])):null}startLoader(t,i=Sa,s){this.readyToStart(t,i,!0,s)&&(this.loaders[t].tasks[i].isOtherRunning||(this.hasRunningTask(qr,t)&&(this.backgroundCloseout(t),this.showBackground.next({loaderId:t,isShow:!1})),this.showForeground.next({loaderId:t,isShow:!0})))}start(t=Sa,i){this.startLoader(this.defaultConfig.masterLoaderId,t,i)}startBackgroundLoader(t,i=Na,s){this.readyToStart(t,i,!1,s)&&!this.hasRunningTask(Lo,t)&&!this.loaders[t].tasks[i].isOtherRunning&&this.showBackground.next({loaderId:t,isShow:!0})}startBackground(t=Na,i){this.startBackgroundLoader(this.defaultConfig.masterLoaderId,t,i)}stopLoader(t,i=Sa){this.readyToStop(t,i)&&(this.hasRunningTask(Lo,t)||(this.foregroundCloseout(t),this.showForeground.next({loaderId:t,isShow:!1}),this.hasRunningTask(qr,t)&&setTimeout(()=>{this.hasRunningTask(qr,t)&&this.showBackground.next({loaderId:t,isShow:!0})},this.defaultConfig.fastFadeOut?dh:fh)))}stop(t=Sa){this.stopLoader(this.defaultConfig.masterLoaderId,t)}stopBackgroundLoader(t,i=Na){this.readyToStop(t,i)&&!this.hasRunningTask(Lo,t)&&!this.hasRunningTask(qr,t)&&(this.backgroundCloseout(t),this.showBackground.next({loaderId:t,isShow:!1}))}stopBackground(t=Na){this.stopBackgroundLoader(this.defaultConfig.masterLoaderId,t)}stopAllLoader(t){if(!this.loaders[t]){console.warn(`[ngx-ui-loader] - loaderId "${t}" does not exist.`);return}this.hasRunningTask(Lo,t)?(this.foregroundCloseout(t),this.showForeground.next({loaderId:t,isShow:!1})):this.hasRunningTask(qr,t)&&(this.backgroundCloseout(t),this.showBackground.next({loaderId:t,isShow:!1})),this.clearAllTimers(this.loaders[t].tasks),this.loaders[t].tasks={}}stopAll(){this.stopAllLoader(this.defaultConfig.masterLoaderId)}hasRunningTask(t,i,s){if(this.loaders[i]){let a=this.loaders[i].tasks;return s?a[s]?!!a[s].startAt:!1:Object.keys(a).some(c=>!!a[c].startAt&&a[c].isForeground===t)}return!1}createLoaderData(t,i,s){this.loaders[t]||(this.loaders[t]={loaderId:t,tasks:{},isMaster:i,isBound:s})}foregroundCloseout(t){this.fgClosing.next({loaderId:t,isShow:!0}),setTimeout(()=>{this.fgClosing.next({loaderId:t,isShow:!1})},this.defaultConfig.fastFadeOut?vc:mc)}backgroundCloseout(t){this.bgClosing.next({loaderId:t,isShow:!0}),setTimeout(()=>{this.bgClosing.next({loaderId:t,isShow:!1})},this.defaultConfig.fastFadeOut?vc:mc)}clearTimers(t){clearTimeout(t.delayTimer),clearTimeout(t.maxTimer),clearTimeout(t.minTimer)}clearAllTimers(t){Object.keys(t).map(i=>{this.clearTimers(t[i])})}readyToStart(t,i,s,a=hh){this.createLoaderData(t,void 0,!1);let c=this.hasRunningTask(s,t);if(!this.loaders[t].tasks[i])this.loaders[t].tasks[i]={taskId:i,isForeground:s,minTime:a.minTime>=Es?a.minTime:this.defaultConfig.minTime,maxTime:a.maxTime?a.maxTime:this.defaultConfig.maxTime,delay:a.delay>=gc?a.delay:this.defaultConfig.delay};else if(this.loaders[t].tasks[i].isForeground!==s)throw new Error(`[ngx-ui-loader] - taskId "${i}" is duplicated.`);return!(this.setDelayTimer(this.loaders[t].tasks[i],t)||(this.loaders[t].tasks[i]=ql(Rr({},this.loaders[t].tasks[i]),{isOtherRunning:c,startAt:Date.now()}),this.setMaxTimer(this.loaders[t].tasks[i],t),!this.loaders[t].isBound))}readyToStop(t,i){if(!this.loaders[t])return console.warn(`[ngx-ui-loader] - loaderId "${t}" does not exist.`),!1;let s=this.loaders[t].tasks[i];return s?s.isDelayed?(this.clearTimers(s),delete this.loaders[t].tasks[i],!1):this.setMinTimer(s,t)?!1:(this.clearTimers(s),delete this.loaders[t].tasks[i],!0):!1}setDelayTimer(t,i){if(t.delay>gc){if(t.isDelayed)return!0;if(!t.delayTimer)return t.isDelayed=!0,t.delayTimer=setTimeout(()=>{t.isDelayed=!1,t.isForeground?this.startLoader(i,t.taskId):this.startBackgroundLoader(i,t.taskId)},t.delay),!0}return!1}setMaxTimer(t,i){t.maxTime>t.minTime&&(clearTimeout(t.maxTimer),t.maxTimer=setTimeout(()=>{t.isForeground?this.stopLoader(i,t.taskId):this.stopBackgroundLoader(i,t.taskId)},t.maxTime))}setMinTimer(t,i){let s=Date.now();return t.startAt&&t.startAt+t.minTime>s?(t.minTimer=setTimeout(()=>{t.isForeground?this.stopLoader(i,t.taskId):this.stopBackgroundLoader(i,t.taskId)},t.startAt+t.minTime-s),!0):!1}}return r.\u0275fac=function(t){return new(t||r)(Ca(wc,8))},r.\u0275prov=Bi({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),xc=(()=>{class r{constructor(t,i,s){this.domSanitizer=t,this.changeDetectorRef=i,this.ngxService=s,this.initialized=!1,this.defaultConfig=this.ngxService.getDefaultConfig(),this.bgsColor=this.defaultConfig.bgsColor,this.bgsOpacity=this.defaultConfig.bgsOpacity,this.bgsPosition=this.defaultConfig.bgsPosition,this.bgsSize=this.defaultConfig.bgsSize,this.bgsType=this.defaultConfig.bgsType,this.fastFadeOut=this.defaultConfig.fastFadeOut,this.fgsColor=this.defaultConfig.fgsColor,this.fgsPosition=this.defaultConfig.fgsPosition,this.fgsSize=this.defaultConfig.fgsSize,this.fgsType=this.defaultConfig.fgsType,this.gap=this.defaultConfig.gap,this.loaderId=this.defaultConfig.masterLoaderId,this.logoPosition=this.defaultConfig.logoPosition,this.logoSize=this.defaultConfig.logoSize,this.logoUrl=this.defaultConfig.logoUrl,this.overlayBorderRadius=this.defaultConfig.overlayBorderRadius,this.overlayColor=this.defaultConfig.overlayColor,this.pbColor=this.defaultConfig.pbColor,this.pbDirection=this.defaultConfig.pbDirection,this.pbThickness=this.defaultConfig.pbThickness,this.hasProgressBar=this.defaultConfig.hasProgressBar,this.text=this.defaultConfig.text,this.textColor=this.defaultConfig.textColor,this.textPosition=this.defaultConfig.textPosition}ngOnInit(){this.initializeSpinners(),this.ngxService.bindLoaderData(this.loaderId),this.determinePositions(),this.trustedLogoUrl=this.domSanitizer.bypassSecurityTrustResourceUrl(this.logoUrl),this.showForegroundWatcher=this.ngxService.showForeground$.pipe(_o(t=>this.loaderId===t.loaderId)).subscribe(t=>{this.showForeground=t.isShow,this.changeDetectorRef.markForCheck()}),this.showBackgroundWatcher=this.ngxService.showBackground$.pipe(_o(t=>this.loaderId===t.loaderId)).subscribe(t=>{this.showBackground=t.isShow,this.changeDetectorRef.markForCheck()}),this.foregroundClosingWatcher=this.ngxService.foregroundClosing$.pipe(_o(t=>this.loaderId===t.loaderId)).subscribe(t=>{this.foregroundClosing=t.isShow,this.changeDetectorRef.markForCheck()}),this.backgroundClosingWatcher=this.ngxService.backgroundClosing$.pipe(_o(t=>this.loaderId===t.loaderId)).subscribe(t=>{this.backgroundClosing=t.isShow,this.changeDetectorRef.markForCheck()}),this.initialized=!0}ngOnChanges(t){if(!this.initialized)return;let i=t.bgsType,s=t.fgsType,a=t.logoUrl;(s||i)&&this.initializeSpinners(),this.determinePositions(),a&&(this.trustedLogoUrl=this.domSanitizer.bypassSecurityTrustResourceUrl(this.logoUrl))}ngOnDestroy(){this.ngxService.destroyLoaderData(this.loaderId),this.showForegroundWatcher&&this.showForegroundWatcher.unsubscribe(),this.showBackgroundWatcher&&this.showBackgroundWatcher.unsubscribe(),this.foregroundClosingWatcher&&this.foregroundClosingWatcher.unsubscribe(),this.backgroundClosingWatcher&&this.backgroundClosingWatcher.unsubscribe()}initializeSpinners(){this.fgDivs=Array(Oa[this.fgsType].divs).fill(1),this.fgSpinnerClass=Oa[this.fgsType].class,this.bgDivs=Array(Oa[this.bgsType].divs).fill(1),this.bgSpinnerClass=Oa[this.bgsType].class}determinePositions(){this.logoTop="initial",this.spinnerTop="initial",this.textTop="initial";let t=24;this.logoPosition.startsWith("center")?this.logoTop="50%":this.logoPosition.startsWith("top")&&(this.logoTop="30px"),this.fgsPosition.startsWith("center")?this.spinnerTop="50%":this.fgsPosition.startsWith("top")&&(this.spinnerTop="30px"),this.textPosition.startsWith("center")?this.textTop="50%":this.textPosition.startsWith("top")&&(this.textTop="30px"),this.fgsPosition===Un.centerCenter?this.logoUrl&&this.logoPosition===Un.centerCenter?this.text&&this.textPosition===Un.centerCenter?(this.logoTop=this.domSanitizer.bypassSecurityTrustStyle(`calc(50% - ${this.fgsSize/2}px - ${t/2}px - ${this.gap}px)`),this.spinnerTop=this.domSanitizer.bypassSecurityTrustStyle(`calc(50% + ${this.logoSize/2}px - ${t/2}px)`),this.textTop=this.domSanitizer.bypassSecurityTrustStyle(`calc(50% + ${this.logoSize/2}px + ${this.gap}px + ${this.fgsSize/2}px)`)):(this.logoTop=this.domSanitizer.bypassSecurityTrustStyle(`calc(50% - ${this.fgsSize/2}px - ${this.gap/2}px)`),this.spinnerTop=this.domSanitizer.bypassSecurityTrustStyle(`calc(50% + ${this.logoSize/2}px + ${this.gap/2}px)`)):this.text&&this.textPosition===Un.centerCenter&&(this.spinnerTop=this.domSanitizer.bypassSecurityTrustStyle(`calc(50% - ${t/2}px - ${this.gap/2}px)`),this.textTop=this.domSanitizer.bypassSecurityTrustStyle(`calc(50% + ${this.fgsSize/2}px + ${this.gap/2}px)`)):this.logoUrl&&this.logoPosition===Un.centerCenter&&this.text&&this.textPosition===Un.centerCenter&&(this.logoTop=this.domSanitizer.bypassSecurityTrustStyle(`calc(50% - ${t/2}px - ${this.gap/2}px)`),this.textTop=this.domSanitizer.bypassSecurityTrustStyle(`calc(50% + ${this.logoSize/2}px + ${this.gap/2}px)`))}}return r.\u0275fac=function(t){return new(t||r)(Ri(ac),Ri(Kl),Ri(gh))},r.\u0275cmp=ni({type:r,selectors:[["ngx-ui-loader"]],inputs:{bgsColor:"bgsColor",bgsOpacity:"bgsOpacity",bgsPosition:"bgsPosition",bgsSize:"bgsSize",bgsTemplate:"bgsTemplate",bgsType:"bgsType",fgsColor:"fgsColor",fgsPosition:"fgsPosition",fgsSize:"fgsSize",fgsTemplate:"fgsTemplate",fgsType:"fgsType",gap:"gap",loaderId:"loaderId",logoPosition:"logoPosition",logoSize:"logoSize",logoUrl:"logoUrl",overlayBorderRadius:"overlayBorderRadius",overlayColor:"overlayColor",pbColor:"pbColor",pbDirection:"pbDirection",pbThickness:"pbThickness",hasProgressBar:"hasProgressBar",text:"text",textColor:"textColor",textPosition:"textPosition"},features:[Gl],decls:14,vars:50,consts:[["foregroundTemplate",""],["backgroundTemplate",""],["class","ngx-progress-bar",3,"ngx-position-absolute","ngClass","height","color","loading-foreground","foreground-closing","fast-closing",4,"ngIf"],[1,"ngx-overlay"],["class","ngx-loading-logo",3,"ngClass","src","width","height","top",4,"ngIf"],[1,"ngx-foreground-spinner",3,"ngClass"],[3,"class",4,"ngIf","ngIfElse"],[1,"ngx-loading-text",3,"ngClass"],[1,"ngx-background-spinner",3,"ngClass"],[1,"ngx-progress-bar",3,"ngClass"],[1,"ngx-loading-logo",3,"ngClass","src"],[4,"ngFor","ngForOf"],[4,"ngTemplateOutlet"]],template:function(t,i){if(t&1&&(zn(0,th,1,13,"div",2),st(1,"div",3),zn(2,eh,1,8,"img",4),st(3,"div",5),zn(4,ih,2,3,"div",6)(5,oh,1,1,"ng-template",null,0,Ts),rt(),st(7,"div",7)(8,"span"),xt(9),rt()()(),st(10,"div",8),zn(11,sh,2,3,"div",6)(12,ch,1,1,"ng-template",null,1,Ts),rt()),t&2){let s=js(6),a=js(13);Ae("ngIf",i.hasProgressBar),He(),nr("background-color",i.overlayColor)("border-radius",i.overlayBorderRadius),La("ngx-position-absolute",i.loaderId!==i.defaultConfig.masterLoaderId)("loading-foreground",i.showForeground)("foreground-closing",i.foregroundClosing)("fast-closing",i.foregroundClosing&&i.fastFadeOut),He(),Ae("ngIf",i.logoUrl),He(),nr("color",i.fgsColor)("width",i.fgsSize,"px")("height",i.fgsSize,"px")("top",i.spinnerTop),Ae("ngClass",i.fgsPosition),He(),Ae("ngIf",!i.fgsTemplate)("ngIfElse",s),He(3),nr("top",i.textTop)("color",i.textColor),Ae("ngClass",i.textPosition),He(2),Yl(i.text),He(),nr("width",i.bgsSize,"px")("height",i.bgsSize,"px")("color",i.bgsColor)("opacity",i.bgsOpacity),La("ngx-position-absolute",i.loaderId!==i.defaultConfig.masterLoaderId)("loading-background",i.showBackground)("background-closing",i.backgroundClosing)("fast-closing",i.backgroundClosing&&i.fastFadeOut),Ae("ngClass",i.bgsPosition),He(),Ae("ngIf",!i.bgsTemplate)("ngIfElse",a)}},dependencies:[ka,Ql,tc,nc],styles:['.ngx-progress-bar[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:3px;z-index:99999!important;display:none;color:#00acc1;overflow:hidden}.ngx-progress-bar.loading-foreground[_ngcontent-%COMP%], .ngx-progress-bar.foreground-closing[_ngcontent-%COMP%]{display:block}.ngx-progress-bar.foreground-closing[_ngcontent-%COMP%]{opacity:0!important;transition:opacity .5s ease-out .5s}.ngx-progress-bar.fast-closing[_ngcontent-%COMP%]{transition:opacity .3s ease-out .3s!important}.ngx-progress-bar[_ngcontent-%COMP%]:before, .ngx-progress-bar[_ngcontent-%COMP%]:after{background-color:currentColor;content:"";display:block;width:100%;height:100%;position:absolute;top:0}.ngx-progress-bar-ltr[_ngcontent-%COMP%]:before{transform:translate3d(-100%,0,0)}.ngx-progress-bar-ltr[_ngcontent-%COMP%]:after{animation:_ngcontent-%COMP%_progressBar-slide-ltr 12s ease-out 0s 1 normal;transform:translate3d(-5%,0,0)}.ngx-progress-bar-rtl[_ngcontent-%COMP%]:before{transform:translate3d(100%,0,0)}.ngx-progress-bar-rtl[_ngcontent-%COMP%]:after{animation:_ngcontent-%COMP%_progressBar-slide-rtl 12s ease-out 0s 1 normal;transform:translate3d(5%,0,0)}.foreground-closing.ngx-progress-bar-ltr[_ngcontent-%COMP%]:before{animation:_ngcontent-%COMP%_progressBar-slide-complete-ltr 1s ease-out 0s 1;transform:translateZ(0)}.fast-closing.ngx-progress-bar-ltr[_ngcontent-%COMP%]:before{animation:_ngcontent-%COMP%_progressBar-slide-complete-ltr .6s ease-out 0s 1!important}.foreground-closing.ngx-progress-bar-rtl[_ngcontent-%COMP%]:before{animation:_ngcontent-%COMP%_progressBar-slide-complete-rtl 1s ease-out 0s 1;transform:translateZ(0)}.fast-closing.ngx-progress-bar-rtl[_ngcontent-%COMP%]:before{animation:_ngcontent-%COMP%_progressBar-slide-complete-rtl .6s ease-out 0s 1!important}@keyframes _ngcontent-%COMP%_progressBar-slide-ltr{0%{transform:translate3d(-100%,0,0)}to{transform:translate3d(-5%,0,0)}}@keyframes _ngcontent-%COMP%_progressBar-slide-rtl{0%{transform:translate3d(100%,0,0)}to{transform:translate3d(5%,0,0)}}@keyframes _ngcontent-%COMP%_progressBar-slide-complete-ltr{0%{transform:translate3d(-75%,0,0)}50%{transform:translateZ(0)}}@keyframes _ngcontent-%COMP%_progressBar-slide-complete-rtl{0%{transform:translate3d(75%,0,0)}50%{transform:translateZ(0)}}.ngx-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;z-index:99998!important;background-color:#282828cc;cursor:progress;display:none}.ngx-overlay.loading-foreground[_ngcontent-%COMP%], .ngx-overlay.foreground-closing[_ngcontent-%COMP%]{display:block}.ngx-overlay.foreground-closing[_ngcontent-%COMP%]{opacity:0!important;transition:opacity .5s ease-out .5s}.ngx-overlay.fast-closing[_ngcontent-%COMP%]{transition:opacity .3s ease-out .3s!important}.ngx-overlay[_ngcontent-%COMP%] > .ngx-foreground-spinner[_ngcontent-%COMP%]{position:fixed;width:60px;height:60px;margin:0;color:#00acc1}.ngx-overlay[_ngcontent-%COMP%] > .ngx-loading-logo[_ngcontent-%COMP%]{position:fixed;margin:0;width:120px;height:120px}.ngx-overlay[_ngcontent-%COMP%] > .ngx-loading-text[_ngcontent-%COMP%]{position:fixed;margin:0;font-family:sans-serif;font-weight:400;font-size:1.2em;color:#fff}.ngx-background-spinner[_ngcontent-%COMP%]{position:fixed;z-index:99997!important;width:60px;height:60px;margin:0;color:#00acc1;opacity:.6;display:none}.ngx-background-spinner.loading-background[_ngcontent-%COMP%], .ngx-background-spinner.background-closing[_ngcontent-%COMP%]{display:block}.ngx-background-spinner.background-closing[_ngcontent-%COMP%]{opacity:0!important;transition:opacity .7s ease-out}.ngx-background-spinner.fast-closing[_ngcontent-%COMP%]{transition:opacity .4s ease-out!important}.ngx-position-absolute[_ngcontent-%COMP%]{position:absolute!important}.ngx-position-absolute[_ngcontent-%COMP%] > .ngx-foreground-spinner[_ngcontent-%COMP%], .ngx-position-absolute[_ngcontent-%COMP%] > .ngx-loading-logo[_ngcontent-%COMP%], .ngx-position-absolute[_ngcontent-%COMP%] > .ngx-loading-text[_ngcontent-%COMP%]{position:absolute!important}.ngx-position-absolute.ngx-progress-bar[_ngcontent-%COMP%]{z-index:99996!important}.ngx-position-absolute.ngx-overlay[_ngcontent-%COMP%]{z-index:99995!important}.ngx-position-absolute.ngx-background-spinner[_ngcontent-%COMP%]{z-index:99994!important}.ngx-position-absolute[_ngcontent-%COMP%]   .sk-square-jelly-box[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){z-index:99994!important}.top-left[_ngcontent-%COMP%]{top:30px;left:30px}.top-center[_ngcontent-%COMP%]{top:30px;left:50%;transform:translate(-50%)}.top-right[_ngcontent-%COMP%]{top:30px;right:30px}.center-left[_ngcontent-%COMP%]{top:50%;left:30px;transform:translateY(-50%)}.center-center[_ngcontent-%COMP%]{top:50%;left:50%;transform:translate(-50%,-50%)}.center-right[_ngcontent-%COMP%]{top:50%;right:30px;transform:translateY(-50%)}.bottom-left[_ngcontent-%COMP%]{bottom:30px;left:30px}.bottom-center[_ngcontent-%COMP%]{bottom:30px;left:50%;transform:translate(-50%)}.bottom-right[_ngcontent-%COMP%]{bottom:30px;right:30px}.sk-ball-scale-multiple[_ngcontent-%COMP%], .sk-ball-scale-multiple[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;box-sizing:border-box}.sk-ball-scale-multiple[_ngcontent-%COMP%]{width:100%;height:100%;font-size:0}.sk-ball-scale-multiple[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:inline-block;float:none;background-color:currentColor;border:0 solid currentColor}.sk-ball-scale-multiple[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;border-radius:100%;opacity:0;animation:_ngcontent-%COMP%_ball-scale-multiple 1s 0s linear infinite}.sk-ball-scale-multiple[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.sk-ball-scale-multiple[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){animation-delay:.4s}@keyframes _ngcontent-%COMP%_ball-scale-multiple{0%{opacity:0;transform:scale(0)}5%{opacity:.75}to{opacity:0;transform:scale(1)}}.sk-ball-spin[_ngcontent-%COMP%], .sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;box-sizing:border-box}.sk-ball-spin[_ngcontent-%COMP%]{width:100%;height:100%;font-size:0}.sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:inline-block;float:none;background-color:currentColor;border:0 solid currentColor}.sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;width:25%;height:25%;margin-top:-12.5%;margin-left:-12.5%;border-radius:100%;animation:_ngcontent-%COMP%_ball-spin-clockwise 1s infinite ease-in-out}.sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){top:5%;left:50%;animation-delay:-1.125s}.sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){top:18.1801948466%;left:81.8198051534%;animation-delay:-1.25s}.sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){top:50%;left:95%;animation-delay:-1.375s}.sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){top:81.8198051534%;left:81.8198051534%;animation-delay:-1.5s}.sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){top:94.9999999966%;left:50.0000000005%;animation-delay:-1.625s}.sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(6){top:81.8198046966%;left:18.1801949248%;animation-delay:-1.75s}.sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(7){top:49.9999750815%;left:5.0000051215%;animation-delay:-1.875s}.sk-ball-spin[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(8){top:18.179464974%;left:18.1803700518%;animation-delay:-2s}@keyframes _ngcontent-%COMP%_ball-spin{0%,to{opacity:1;transform:scale(1)}20%{opacity:1}80%{opacity:0;transform:scale(0)}}.sk-ball-spin-clockwise[_ngcontent-%COMP%], .sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;box-sizing:border-box}.sk-ball-spin-clockwise[_ngcontent-%COMP%]{width:100%;height:100%;font-size:0}.sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:inline-block;float:none;background-color:currentColor;border:0 solid currentColor}.sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;width:25%;height:25%;margin-top:-12.5%;margin-left:-12.5%;border-radius:100%;animation:_ngcontent-%COMP%_ball-spin-clockwise 1s infinite ease-in-out}.sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){top:5%;left:50%;animation-delay:-.875s}.sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){top:18.1801948466%;left:81.8198051534%;animation-delay:-.75s}.sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){top:50%;left:95%;animation-delay:-.625s}.sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){top:81.8198051534%;left:81.8198051534%;animation-delay:-.5s}.sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){top:94.9999999966%;left:50.0000000005%;animation-delay:-.375s}.sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(6){top:81.8198046966%;left:18.1801949248%;animation-delay:-.25s}.sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(7){top:49.9999750815%;left:5.0000051215%;animation-delay:-.125s}.sk-ball-spin-clockwise[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(8){top:18.179464974%;left:18.1803700518%;animation-delay:0s}@keyframes _ngcontent-%COMP%_ball-spin-clockwise{0%,to{opacity:1;transform:scale(1)}20%{opacity:1}80%{opacity:0;transform:scale(0)}}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%], .sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;box-sizing:border-box}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%]{font-size:0;width:100%;height:100%;animation:_ngcontent-%COMP%_ball-spin-clockwise-fade-rotating-rotate 6s infinite linear}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:inline-block;float:none;background-color:currentColor;border:0 solid currentColor}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;width:25%;height:25%;margin-top:-12.5%;margin-left:-12.5%;border-radius:100%;animation:_ngcontent-%COMP%_ball-spin-clockwise-fade-rotating 1s infinite linear}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){top:5%;left:50%;animation-delay:-.875s}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){top:18.1801948466%;left:81.8198051534%;animation-delay:-.75s}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){top:50%;left:95%;animation-delay:-.625s}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){top:81.8198051534%;left:81.8198051534%;animation-delay:-.5s}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){top:94.9999999966%;left:50.0000000005%;animation-delay:-.375s}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(6){top:81.8198046966%;left:18.1801949248%;animation-delay:-.25s}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(7){top:49.9999750815%;left:5.0000051215%;animation-delay:-.125s}.sk-ball-spin-clockwise-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(8){top:18.179464974%;left:18.1803700518%;animation-delay:0s}@keyframes _ngcontent-%COMP%_ball-spin-clockwise-fade-rotating-rotate{to{transform:rotate(-360deg)}}@keyframes _ngcontent-%COMP%_ball-spin-clockwise-fade-rotating{50%{opacity:.25;transform:scale(.5)}to{opacity:1;transform:scale(1)}}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%], .sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;box-sizing:border-box}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%]{width:100%;height:100%;font-size:0;animation:_ngcontent-%COMP%_ball-spin-fade-rotate 6s infinite linear}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:inline-block;float:none;background-color:currentColor;border:0 solid currentColor}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;width:25%;height:25%;margin-top:-12.5%;margin-left:-12.5%;border-radius:100%;animation:_ngcontent-%COMP%_ball-spin-fade 1s infinite linear}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){top:5%;left:50%;animation-delay:-1.125s}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){top:18.1801948466%;left:81.8198051534%;animation-delay:-1.25s}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){top:50%;left:95%;animation-delay:-1.375s}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){top:81.8198051534%;left:81.8198051534%;animation-delay:-1.5s}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){top:94.9999999966%;left:50.0000000005%;animation-delay:-1.625s}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(6){top:81.8198046966%;left:18.1801949248%;animation-delay:-1.75s}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(7){top:49.9999750815%;left:5.0000051215%;animation-delay:-1.875s}.sk-ball-spin-fade-rotating[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(8){top:18.179464974%;left:18.1803700518%;animation-delay:-2s}@keyframes _ngcontent-%COMP%_ball-spin-fade-rotate{to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_ball-spin-fade{0%,to{opacity:1;transform:scale(1)}50%{opacity:.25;transform:scale(.5)}}.sk-chasing-dots[_ngcontent-%COMP%]{margin:auto;width:100%;height:100%;position:absolute;text-align:center;animation:_ngcontent-%COMP%_sk-chasingDots-rotate 2s infinite linear}.sk-chasing-dots[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:60%;height:60%;display:inline-block;position:absolute;top:0;background-color:currentColor;border-radius:100%;animation:_ngcontent-%COMP%_sk-chasingDots-bounce 2s infinite ease-in-out}.sk-chasing-dots[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){top:auto;bottom:0;animation-delay:-1s}@keyframes _ngcontent-%COMP%_sk-chasingDots-rotate{to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_sk-chasingDots-bounce{0%,to{transform:scale(0)}50%{transform:scale(1)}}.sk-circle[_ngcontent-%COMP%]{margin:auto;width:100%;height:100%;position:relative}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:100%;height:100%;position:absolute;left:0;top:0}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:before{content:"";display:block;margin:0 auto;width:15%;height:15%;background-color:currentColor;border-radius:100%;animation:_ngcontent-%COMP%_sk-circle-bounceDelay 1.2s infinite ease-in-out both}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){transform:rotate(30deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){transform:rotate(60deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){transform:rotate(90deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){transform:rotate(120deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(6){transform:rotate(150deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(7){transform:rotate(180deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(8){transform:rotate(210deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(9){transform:rotate(240deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(10){transform:rotate(270deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(11){transform:rotate(300deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(12){transform:rotate(330deg)}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2):before{animation-delay:-1.1s}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3):before{animation-delay:-1s}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4):before{animation-delay:-.9s}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5):before{animation-delay:-.8s}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(6):before{animation-delay:-.7s}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(7):before{animation-delay:-.6s}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(8):before{animation-delay:-.5s}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(9):before{animation-delay:-.4s}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(10):before{animation-delay:-.3s}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(11):before{animation-delay:-.2s}.sk-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(12):before{animation-delay:-.1s}@keyframes _ngcontent-%COMP%_sk-circle-bounceDelay{0%,80%,to{transform:scale(0)}40%{transform:scale(1)}}.sk-cube-grid[_ngcontent-%COMP%]{width:100%;height:100%;margin:auto}.sk-cube-grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:33%;height:33%;background-color:currentColor;float:left;animation:_ngcontent-%COMP%_sk-cubeGrid-scaleDelay 1.3s infinite ease-in-out}.sk-cube-grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){animation-delay:.2s}.sk-cube-grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){animation-delay:.3s}.sk-cube-grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){animation-delay:.4s}.sk-cube-grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){animation-delay:.1s}.sk-cube-grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){animation-delay:.2s}.sk-cube-grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(6){animation-delay:.3s}.sk-cube-grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(7){animation-delay:0s}.sk-cube-grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(8){animation-delay:.1s}.sk-cube-grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(9){animation-delay:.2s}@keyframes _ngcontent-%COMP%_sk-cubeGrid-scaleDelay{0%,70%,to{transform:scaleZ(1)}35%{transform:scale3D(0,0,1)}}.sk-double-bounce[_ngcontent-%COMP%]{width:100%;height:100%;position:relative;margin:auto}.sk-double-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:100%;height:100%;border-radius:50%;background-color:currentColor;opacity:.6;position:absolute;top:0;left:0;animation:_ngcontent-%COMP%_sk-doubleBounce-bounce 2s infinite ease-in-out}.sk-double-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){animation-delay:-1s}@keyframes _ngcontent-%COMP%_sk-doubleBounce-bounce{0%,to{transform:scale(0)}50%{transform:scale(1)}}.sk-fading-circle[_ngcontent-%COMP%]{margin:auto;width:100%;height:100%;position:relative}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:100%;height:100%;position:absolute;left:0;top:0}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:before{content:"";display:block;margin:0 auto;width:15%;height:15%;background-color:currentColor;border-radius:100%;animation:_ngcontent-%COMP%_sk-fadingCircle-FadeDelay 1.2s infinite ease-in-out both}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){transform:rotate(30deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){transform:rotate(60deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){transform:rotate(90deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){transform:rotate(120deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(6){transform:rotate(150deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(7){transform:rotate(180deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(8){transform:rotate(210deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(9){transform:rotate(240deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(10){transform:rotate(270deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(11){transform:rotate(300deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(12){transform:rotate(330deg)}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2):before{animation-delay:-1.1s}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3):before{animation-delay:-1s}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4):before{animation-delay:-.9s}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5):before{animation-delay:-.8s}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(6):before{animation-delay:-.7s}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(7):before{animation-delay:-.6s}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(8):before{animation-delay:-.5s}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(9):before{animation-delay:-.4s}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(10):before{animation-delay:-.3s}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(11):before{animation-delay:-.2s}.sk-fading-circle[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(12):before{animation-delay:-.1s}@keyframes _ngcontent-%COMP%_sk-fadingCircle-FadeDelay{0%,39%,to{opacity:0}40%{opacity:1}}.sk-folding-cube[_ngcontent-%COMP%]{margin:auto;width:100%;height:100%;position:relative;transform:rotate(45deg)}.sk-folding-cube[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{float:left;width:50%;height:50%;position:relative;transform:scale(1.1)}.sk-folding-cube[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:currentColor;animation:_ngcontent-%COMP%_sk-foldingCube-angle 2.4s infinite linear both;transform-origin:100% 100%}.sk-folding-cube[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){transform:scale(1.1) rotate(90deg)}.sk-folding-cube[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){transform:scale(1.1) rotate(270deg)}.sk-folding-cube[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){transform:scale(1.1) rotate(180deg)}.sk-folding-cube[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2):before{animation-delay:.3s}.sk-folding-cube[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3):before{animation-delay:.9s}.sk-folding-cube[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4):before{animation-delay:.6s}@keyframes _ngcontent-%COMP%_sk-foldingCube-angle{0%,10%{transform:perspective(140px) rotateX(-180deg);opacity:0}25%,75%{transform:perspective(140px) rotateX(0);opacity:1}90%,to{transform:perspective(140px) rotateY(180deg);opacity:0}}.sk-pulse[_ngcontent-%COMP%]{width:100%;height:100%;margin:auto}.sk-pulse[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:100%;height:100%;background-color:currentColor;border-radius:100%;animation:_ngcontent-%COMP%_sk-pulse-scaleOut 1s infinite ease-in-out}@keyframes _ngcontent-%COMP%_sk-pulse-scaleOut{0%{transform:scale(0)}to{transform:scale(1);opacity:0}}.sk-rectangle-bounce[_ngcontent-%COMP%]{margin:auto;width:100%;height:100%;text-align:center;font-size:0}.sk-rectangle-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{background-color:currentColor;height:100%;width:10%;margin:0 5%;display:inline-block;animation:_ngcontent-%COMP%_sk-rectangleBounce-stretchDelay 1.2s infinite ease-in-out}.sk-rectangle-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){animation-delay:-1.1s}.sk-rectangle-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){animation-delay:-1s}.sk-rectangle-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){animation-delay:-.9s}.sk-rectangle-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){animation-delay:-.8s}@keyframes _ngcontent-%COMP%_sk-rectangleBounce-stretchDelay{0%,40%,to{transform:scaleY(.4)}20%{transform:scaleY(1)}}.sk-rectangle-bounce-party[_ngcontent-%COMP%], .sk-rectangle-bounce-party[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;box-sizing:border-box}.sk-rectangle-bounce-party[_ngcontent-%COMP%]{margin:auto;width:100%;height:100%;text-align:center;font-size:0}.sk-rectangle-bounce-party[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:inline-block;float:none;background-color:currentColor;border:0 solid currentColor}.sk-rectangle-bounce-party[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:10%;height:100%;margin:0 5%;border-radius:0;animation-name:_ngcontent-%COMP%_rectangle-bounce-party;animation-iteration-count:infinite}.sk-rectangle-bounce-party[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){animation-duration:.43s;animation-delay:-.23s}.sk-rectangle-bounce-party[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){animation-duration:.62s;animation-delay:-.32s}.sk-rectangle-bounce-party[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){animation-duration:.43s;animation-delay:-.44s}.sk-rectangle-bounce-party[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){animation-duration:.8s;animation-delay:-.31s}.sk-rectangle-bounce-party[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){animation-duration:.74s;animation-delay:-.24s}@keyframes _ngcontent-%COMP%_rectangle-bounce-party{0%{transform:scaleY(1)}50%{transform:scaleY(.4)}to{transform:scaleY(1)}}.sk-rectangle-bounce-pulse-out[_ngcontent-%COMP%], .sk-rectangle-bounce-pulse-out[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;box-sizing:border-box}.sk-rectangle-bounce-pulse-out[_ngcontent-%COMP%]{margin:auto;width:100%;height:100%;text-align:center;font-size:0}.sk-rectangle-bounce-pulse-out[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:inline-block;float:none;background-color:currentColor;border:0 solid currentColor}.sk-rectangle-bounce-pulse-out[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:10%;height:100%;margin:0 5%;border-radius:0;animation:_ngcontent-%COMP%_rectangle-bounce-pulse-out .9s infinite cubic-bezier(.85,.25,.37,.85)}.sk-rectangle-bounce-pulse-out[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){animation-delay:-.9s}.sk-rectangle-bounce-pulse-out[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2), .sk-rectangle-bounce-pulse-out[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){animation-delay:-.7s}.sk-rectangle-bounce-pulse-out[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1), .sk-rectangle-bounce-pulse-out[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){animation-delay:-.5s}@keyframes _ngcontent-%COMP%_rectangle-bounce-pulse-out{0%{transform:scaley(1)}50%{transform:scaley(.4)}to{transform:scaley(1)}}.sk-rectangle-bounce-pulse-out-rapid[_ngcontent-%COMP%], .sk-rectangle-bounce-pulse-out-rapid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;box-sizing:border-box}.sk-rectangle-bounce-pulse-out-rapid[_ngcontent-%COMP%]{margin:auto;width:100%;height:100%;text-align:center;font-size:0}.sk-rectangle-bounce-pulse-out-rapid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:inline-block;float:none;background-color:currentColor;border:0 solid currentColor}.sk-rectangle-bounce-pulse-out-rapid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:10%;height:100%;margin:0 5%;border-radius:0;animation:_ngcontent-%COMP%_rectangle-bounce-pulse-out-rapid .9s infinite cubic-bezier(.11,.49,.38,.78)}.sk-rectangle-bounce-pulse-out-rapid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){animation-delay:-.9s}.sk-rectangle-bounce-pulse-out-rapid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2), .sk-rectangle-bounce-pulse-out-rapid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(4){animation-delay:-.65s}.sk-rectangle-bounce-pulse-out-rapid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1), .sk-rectangle-bounce-pulse-out-rapid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(5){animation-delay:-.4s}@keyframes _ngcontent-%COMP%_rectangle-bounce-pulse-out-rapid{0%{transform:scaley(1)}80%{transform:scaley(.4)}90%{transform:scaley(1)}}.sk-rotating-plane[_ngcontent-%COMP%]{width:100%;height:100%;text-align:center;margin:auto}.sk-rotating-plane[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:100%;height:100%;background-color:currentColor;animation:_ngcontent-%COMP%_sk-rotatePlane 1.2s infinite ease-in-out}@keyframes _ngcontent-%COMP%_sk-rotatePlane{0%{transform:perspective(120px) rotateX(0) rotateY(0)}50%{transform:perspective(120px) rotateX(-180.1deg) rotateY(0)}to{transform:perspective(120px) rotateX(-180deg) rotateY(-179.9deg)}}.sk-square-jelly-box[_ngcontent-%COMP%], .sk-square-jelly-box[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;box-sizing:border-box}.sk-square-jelly-box[_ngcontent-%COMP%]{width:100%;height:100%;font-size:0}.sk-square-jelly-box[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:inline-block;float:none;background-color:currentColor;border:0 solid currentColor}.sk-square-jelly-box[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1), .sk-square-jelly-box[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){position:absolute;left:0;width:100%}.sk-square-jelly-box[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){top:-25%;z-index:99997;height:100%;border-radius:10%;animation:_ngcontent-%COMP%_square-jelly-box-animate .6s -.1s linear infinite}.sk-square-jelly-box[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){bottom:-9%;height:10%;background:#000;border-radius:50%;opacity:.2;animation:_ngcontent-%COMP%_square-jelly-box-shadow .6s -.1s linear infinite}@keyframes _ngcontent-%COMP%_square-jelly-box-animate{17%{border-bottom-right-radius:10%}25%{transform:translateY(25%) rotate(22.5deg)}50%{border-bottom-right-radius:100%;transform:translateY(50%) scaleY(.9) rotate(45deg)}75%{transform:translateY(25%) rotate(67.5deg)}to{transform:translateY(0) rotate(90deg)}}@keyframes _ngcontent-%COMP%_square-jelly-box-shadow{50%{transform:scaleX(1.25)}}.sk-square-loader[_ngcontent-%COMP%], .sk-square-loader[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;box-sizing:border-box}.sk-square-loader[_ngcontent-%COMP%]{font-size:0;width:100%;height:100%}.sk-square-loader[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{display:inline-block;float:none;background-color:currentColor;border:0 solid currentColor}.sk-square-loader[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:100%;height:100%;background:transparent;border-width:3px;border-radius:0;animation:_ngcontent-%COMP%_square-loader 2s infinite ease}.sk-square-loader[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:after{display:inline-block;width:100%;vertical-align:top;content:"";background-color:currentColor;animation:_ngcontent-%COMP%_square-loader-inner 2s infinite ease-in}@keyframes _ngcontent-%COMP%_square-loader{0%{transform:rotate(0)}25%{transform:rotate(180deg)}50%{transform:rotate(180deg)}75%{transform:rotate(360deg)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_square-loader-inner{0%{height:0}25%{height:0}50%{height:100%}75%{height:100%}to{height:0}}.sk-three-bounce[_ngcontent-%COMP%]{margin:auto;width:100%;height:100%;text-align:center}.sk-three-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{margin-top:35%;width:30%;height:30%;background-color:currentColor;border-radius:100%;display:inline-block;animation:_ngcontent-%COMP%_sk-threeBounce-bounceDelay 1.4s infinite ease-in-out both}.bottom-center[_ngcontent-%COMP%] > .sk-three-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%], .bottom-left[_ngcontent-%COMP%] > .sk-three-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%], .bottom-right[_ngcontent-%COMP%] > .sk-three-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{margin-top:70%!important}.top-center[_ngcontent-%COMP%] > .sk-three-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%], .top-left[_ngcontent-%COMP%] > .sk-three-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%], .top-right[_ngcontent-%COMP%] > .sk-three-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{margin-top:0!important}.sk-three-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.sk-three-bounce[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}@keyframes _ngcontent-%COMP%_sk-threeBounce-bounceDelay{0%,80%,to{transform:scale(0)}40%{transform:scale(1)}}.sk-three-strings[_ngcontent-%COMP%]{width:100%;height:100%}.sk-three-strings[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:absolute;box-sizing:border-box;width:100%;height:100%;border-radius:50%}.sk-three-strings[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){left:0%;top:0%;animation:_ngcontent-%COMP%_sk-threeStrings-rotateOne 1s linear infinite;border-bottom:3px solid currentColor}.sk-three-strings[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){right:0%;top:0%;animation:_ngcontent-%COMP%_sk-threeStrings-rotateTwo 1s linear infinite;border-right:3px solid currentColor}.sk-three-strings[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(3){right:0%;bottom:0%;animation:_ngcontent-%COMP%_sk-threeStrings-rotateThree 1s linear infinite;border-top:3px solid currentColor}@keyframes _ngcontent-%COMP%_sk-threeStrings-rotateOne{0%{transform:rotateX(35deg) rotateY(-45deg) rotate(0)}to{transform:rotateX(35deg) rotateY(-45deg) rotate(360deg)}}@keyframes _ngcontent-%COMP%_sk-threeStrings-rotateTwo{0%{transform:rotateX(50deg) rotateY(10deg) rotate(0)}to{transform:rotateX(50deg) rotateY(10deg) rotate(360deg)}}@keyframes _ngcontent-%COMP%_sk-threeStrings-rotateThree{0%{transform:rotateX(35deg) rotateY(55deg) rotate(0)}to{transform:rotateX(35deg) rotateY(55deg) rotate(360deg)}}.sk-wandering-cubes[_ngcontent-%COMP%]{margin:auto;width:100%;height:100%;position:relative;text-align:center}.sk-wandering-cubes[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{background-color:currentColor;width:25%;height:25%;position:absolute;top:0;left:0;animation:_ngcontent-%COMP%_sk-wanderingCubes-cubeMove 1.8s infinite ease-in-out}.sk-wandering-cubes[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.9s}@keyframes _ngcontent-%COMP%_sk-wanderingCubes-cubeMove{25%{transform:translate(290%) rotate(-90deg) scale(.5)}50%{transform:translate(290%) translateY(290%) rotate(-179deg)}50.1%{transform:translate(290%) translateY(290%) rotate(-180deg)}75%{transform:translate(0) translateY(290%) rotate(-270deg) scale(.5)}to{transform:rotate(-360deg)}}'],changeDetection:0}),r})();var q1=(()=>{class r{static forRoot(t){return{ngModule:r,providers:[{provide:wc,useValue:t}]}}}return r.\u0275fac=function(t){return new(t||r)},r.\u0275mod=Hl({type:r}),r.\u0275inj=Wl({imports:[[ic]]}),r})(),z1=new _a("ngxUiLoaderRouterCustom.config");var U1=new _a("ngxUiLoaderHttpCustom.config");var vh=()=>["/admin"],bh=r=>["/admin",r];function yh(r,n){if(r&1&&(st(0,"span"),xt(1," / "),st(2,"a",11),xt(3),rt()()),r&2){let t=yi();He(2),Ae("routerLink",Pa(2,bh,t.currentModule.path)),He(),Jl(" ",t.currentModule.data.title," ")}}var Cc=(()=>{let n=class n{toggleSideBar(){this.toggle.emit()}constructor(i,s,a){this.router=i,this.activatedRoute=s,this._document=a,this.isOpen=!0,this.toggle=new Vl}ngOnInit(){}refresh(){window.location.reload()}};n.\u0275fac=function(s){return new(s||n)(Ri(cc),Ri(sc),Ri(Zl))},n.\u0275cmp=ni({type:n,selectors:[["app-admin-header"]],inputs:{isOpen:"isOpen"},outputs:{toggle:"toggle"},decls:59,vars:3,consts:[[1,"navbar-custom"],[1,"topbar"],[1,"topbar-menu","d-flex","align-items-center","gap-lg-2","gap-1"],[1,"logo-box"],["routerLink","",1,"logo-light"],["src","../../assets/images/logo-light.png","alt","logo","height","22",1,"logo-lg"],["src","../../assets/images/logo-sm.png","alt","small logo","height","22",1,"logo-sm"],["routerLink","",1,"logo-dark"],["src","../../assets/images/logo-dark.png","alt","dark logo","height","22",1,"logo-lg"],[1,"button-toggle-menu"],[1,"mdi","mdi-menu",3,"click"],["routerLinkActive","active-link",3,"routerLink"],[4,"ngIf"],[1,"topbar-menu","d-flex","align-items-center","gap-4"],[1,"d-none","d-md-inline-block"],["href","","data-bs-toggle","fullscreen","title","Full screen",1,"nav-link"],[1,"mdi","mdi-fullscreen","font-size-24"],[1,"dropdown"],["data-bs-toggle","dropdown","href","#","role","button","aria-haspopup","false","aria-expanded","false",1,"nav-link","dropdown-toggle","waves-effect","waves-light","arrow-none"],["title","Search",1,"mdi","mdi-magnify","font-size-24"],[1,"dropdown-menu","dropdown-menu-animated","dropdown-menu-end","dropdown-lg","p-0"],[1,"p-3"],["type","search","placeholder","Search ...","aria-label","Recipient's username",1,"form-control"],[1,"dropdown","d-none","d-md-inline-block"],["src","../../assets/images/flags/us.jpg","alt","user-image","height","18",1,"me-0","me-sm-1"],[1,"dropdown-menu","dropdown-menu-end","dropdown-menu-animated"],["href","javascript:void(0);",1,"dropdown-item"],["src","../../assets/images/flags/germany.jpg","alt","user-image","height","12",1,"me-1"],[1,"align-middle"],["id","theme-mode",1,"nav-link"],[1,"bx","bx-moon","font-size-24"],["data-bs-toggle","dropdown","href","#","role","button","aria-haspopup","false","aria-expanded","false",1,"nav-link","dropdown-toggle","nav-user","me-0","waves-effect","waves-light"],["src","../../assets/images/users/avatar-4.jpg","alt","user-image",1,"rounded-circle"],[1,"ms-1","d-none","d-md-inline-block"],[1,"mdi","mdi-chevron-down"],[1,"dropdown-menu","dropdown-menu-end","profile-dropdown"],[1,"dropdown-header","noti-title"],[1,"text-overflow","m-0"],["href","javascript:void(0);",1,"dropdown-item","notify-item"],[1,"fe-user"],[1,"fe-settings"],[1,"dropdown-divider"],["routerLink","",1,"dropdown-item","notify-item"],[1,"fe-log-out"]],template:function(s,a){s&1&&(st(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"a",4),Kt(5,"img",5)(6,"img",6),rt(),st(7,"a",7),Kt(8,"img",8)(9,"img",6),rt()(),st(10,"button",9)(11,"i",10),Co("click",function(){return a.toggleSideBar()}),rt()(),st(12,"span")(13,"a",11),xt(14,"Dashboard"),rt(),zn(15,yh,4,4,"span",12),rt()(),st(16,"ul",13)(17,"li",14)(18,"a",15),Kt(19,"i",16),rt()(),st(20,"li",17)(21,"a",18),Kt(22,"i",19),rt(),st(23,"div",20)(24,"form",21),Kt(25,"input",22),rt()()(),st(26,"li",23)(27,"a",18),Kt(28,"img",24),rt(),st(29,"div",25)(30,"a",26),Kt(31,"img",27),st(32,"span",28),xt(33,"German"),rt()()()(),st(34,"li",29),Kt(35,"i",30),rt(),st(36,"li",17)(37,"a",31),Kt(38,"img",32),st(39,"span",33),xt(40," Anurag K. "),Kt(41,"i",34),rt()(),st(42,"div",35)(43,"div",36)(44,"h6",37),xt(45,"Welcome Admin!"),rt()(),st(46,"a",38),Kt(47,"i",39),st(48,"span"),xt(49,"My Account"),rt()(),st(50,"a",38),Kt(51,"i",40),st(52,"span"),xt(53,"Settings"),rt()(),Kt(54,"div",41),st(55,"a",42),Kt(56,"i",43),st(57,"span"),xt(58,"Logout"),rt()()()()()()()),s&2&&(He(13),Ae("routerLink",Xl(2,vh)),He(2),Ae("ngIf",a.currentModule&&a.currentModule.data&&a.currentModule.data.title))},dependencies:[ka,Aa,uc,dc,hc,fc]});let r=n;return r})();var Lc=(()=>{let n=class n{};n.\u0275fac=function(s){return new(s||n)},n.\u0275cmp=ni({type:n,selectors:[["app-admin-footer"]],decls:12,vars:0,consts:[[1,"footer"],[1,"container-fluid"],[1,"row"],[1,"col-md-6"],[1,"d-none","d-md-flex","gap-4","align-item-center","justify-content-md-end"],[1,"mb-0"],["href","https://sarthaktech.in/","target","_blank"]],template:function(s,a){s&1&&(st(0,"footer",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div"),xt(5," \xA9 Rachin Export"),rt()(),st(6,"div",3)(7,"div",4)(8,"p",5),xt(9,"Design & Developed by "),st(10,"a",6),xt(11,"Sarthak Tech"),rt()()()()()()())}});let r=n;return r})();var _h=r=>({display:r}),Pc=(()=>{let n=class n{constructor(){this.isOpen=!0}ngOnInit(){this.loadScript()}loadScript(){let i=document.createElement("script");i.src="../../../../../assets/lib/jquery/jquery.min.js",i.onload=()=>{},document.body.appendChild(i)}};n.\u0275fac=function(s){return new(s||n)},n.\u0275cmp=ni({type:n,selectors:[["app-admin-sidebar"]],inputs:{isOpen:"isOpen"},decls:394,vars:3,consts:[[1,"main-menu",3,"ngStyle"],[1,"logo-box"],["href","#",1,"logo-light"],["href","#",1,"logo-dark"],["data-simplebar",""],[1,"app-menu"],[1,"menu-title"],[1,"menu-item"],["routerLink","",1,"menu-link","waves-effect","waves-light"],[1,"menu-icon"],[1,"bx","bx-home-smile"],[1,"menu-text"],["href","#menuExpages","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],[1,"bx","bx-file"],[1,"menu-arrow"],["id","menuExpages",1,"collapse"],[1,"sub-menu"],["routerLink","buyer-master",1,"menu-link"],["routerLink","purchase-details",1,"menu-link"],["routerLink","weaver-employee",1,"menu-link"],["routerLink","manage-branch",1,"menu-link"],["routerLink","raw-material-group",1,"menu-link"],["routerLink","code-dyeing-details",1,"menu-link"],["routerLink","dyeing-rate",1,"menu-link"],["routerLink","quality",1,"menu-link"],["routerLink","color-code-details",1,"menu-link"],["routerLink","design",1,"menu-link"],["routerLink","size",1,"menu-link"],["routerLink","map-master",1,"menu-link"],["routerLink","map-rate",1,"menu-link"],["routerLink","material-lagat",1,"menu-link"],["routerLink","finishing-head",1,"menu-link"],["routerLink","finishing-process",1,"menu-link"],["routerLink","",1,"menu-link"],["routerLink","view-material-lagat",1,"menu-link"],["routerLink","view-detail-material-lagat",1,"menu-link"],["href","#menuExpages2","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],["id","menuExpages2",1,"collapse"],["href","#menuForms12","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],["id","menuForms12",1,"collapse"],["routerLink","purches-manufacturing",1,"menu-link"],["href","#menuForms123","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],["id","menuForms123",1,"collapse"],["routerLink","material-deying",1,"menu-link"],["routerLink","buyer-order",1,"menu-link"],["routerLink","view-buyer-order",1,"menu-link"],["routerLink","buyerOrderList",1,"menu-link"],["href","#menuForms1234","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],["id","menuForms1234",1,"collapse"],["routerLink","designing-map-order",1,"menu-link"],["routerLink","regular-map-order",1,"menu-link"],["routerLink","carpet-order-issue",1,"menu-link"],["routerLink","view-carpet-order-issue",1,"menu-link"],["routerLink","carpet-order-issue-print",1,"menu-link"],["routerLink","reciving-carpet",1,"menu-link"],["routerLink","carpet-finishing",1,"menu-link"],["href","#menuMultilevel","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],[1,"bx","bx-share-alt"],["id","menuMultilevel",1,"collapse"],["href","#menuMultilevel2","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],["id","menuMultilevel2",1,"collapse"],["routerLink","importer-detail",1,"menu-link"],["routerLink","wholeseller-customer",1,"menu-link"],["routerLink","retailer-customer",1,"menu-link"],["routerLink","quality-design-code",1,"menu-link"],["routerLink","size-code",1,"menu-link"],["routerLink","gst",1,"menu-link"],["routerLink","report-koti",1,"menu-link"],["href","#menuMultilevel3","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],["id","menuMultilevel3",1,"collapse"],["routerLink","order-form-report",1,"menu-link"],["routerLink","print",1,"menu-link"],["routerLink","bar-code-details",1,"menu-link"],["href","#menuMultilevel8","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],["id","menuMultilevel8",1,"collapse"],["routerLink","import-invoices",1,"menu-link"],["routerLink","import-invoices-report",1,"menu-link"],["routerLink","container-despatch",1,"menu-link"],["routerLink","container-despatch-report",1,"menu-link"],["routerLink","container-received",1,"menu-link"],["routerLink","container-received-report",1,"menu-link"],["href","#menuMultilevel9","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],["id","menuMultilevel9",1,"collapse"],["routerLink","new-challan-sale",1,"menu-link"],["routerLink","challan-report",1,"menu-link"],["routerLink","bills-for-wholeseller",1,"menu-link"],["routerLink","report-yearly",1,"menu-link"],["routerLink","bills-for-retailer",1,"menu-link"],["routerLink","carpet-stock","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],["id","menuMultilevel11",1,"collapse"],["routerLink","stock-report",1,"menu-link"],["href","#menuIcons","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],[1,"bx","bx-aperture"],["id","menuIcons",1,"collapse"],["routerLink","view-importer-invoice",1,"menu-link"],["href","#menuMultilevel300","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],["id","menuMultilevel300",1,"collapse"],["routerLink","importer-invoice-report",1,"menu-link"],["routerLink","cont-details-report",1,"menu-link"],["routerLink","report-order",1,"menu-link"],["routerLink","report-monthly",1,"menu-link"],["routerLink","report-stock",1,"menu-link"],["routerLink","report-sale",1,"menu-link"],["routerLink","report-gst",1,"menu-link"],["href","#menuForms","data-bs-toggle","collapse",1,"menu-link","waves-effect","waves-light"],[1,"bx","bxs-eraser"],["id","menuForms",1,"collapse"]],template:function(s,a){s&1&&(st(0,"div",0)(1,"div",1)(2,"a",2),xt(3," Rachin Export "),rt(),st(4,"a",3),xt(5," Rachin Export "),rt()(),st(6,"div",4)(7,"ul",5)(8,"li",6),xt(9,"Menu"),rt(),st(10,"li",7)(11,"a",8)(12,"span",9),Kt(13,"i",10),rt(),st(14,"span",11),xt(15," Dashboards "),rt()()(),st(16,"li",6),xt(17,"Rachin Exports"),rt(),st(18,"li",7)(19,"a",12)(20,"span",9),Kt(21,"i",13),rt(),st(22,"span",11),xt(23," Master "),rt(),Kt(24,"span",14),rt(),st(25,"div",15)(26,"ul",16)(27,"li",7)(28,"a",17)(29,"span",11),xt(30,"Buyer Master"),rt()()(),st(31,"li",7)(32,"a",18)(33,"span",11),xt(34,"Purchaser Ledger"),rt()()(),st(35,"li",7)(36,"a",19)(37,"span",11),xt(38,"Weaver/Employee"),rt()()(),st(39,"li",7)(40,"a",20)(41,"span",11),xt(42,"Branch Address"),rt()()(),st(43,"li",7)(44,"a",21)(45,"span",11),xt(46,"Raw Material Group"),rt()()(),st(47,"li",7)(48,"a",22)(49,"span",11),xt(50," Dyeing Details Code"),rt()()(),st(51,"li",7)(52,"a",23)(53,"span",11),xt(54," Dyeing Rate"),rt()()(),st(55,"li",7)(56,"a",24)(57,"span",11),xt(58,"Quality"),rt()()(),st(59,"li",7)(60,"a",25)(61,"span",11),xt(62,"Color "),rt()()(),st(63,"li",7)(64,"a",26)(65,"span",11),xt(66,"Design"),rt()()(),st(67,"li",7)(68,"a",27)(69,"span",11),xt(70,"Size"),rt()()(),st(71,"li",7)(72,"a",28)(73,"span",11),xt(74,"Map Master"),rt()()(),st(75,"li",7)(76,"a",29)(77,"span",11),xt(78,"Map Rate"),rt()()(),st(79,"li",7)(80,"a",30)(81,"span",11),xt(82,"Material Lagat"),rt()()(),st(83,"li",7)(84,"a",31)(85,"span",11),xt(86,"Finishing Head"),rt()()(),st(87,"li",7)(88,"a",32)(89,"span",11),xt(90,"Finishing Process"),rt()()(),st(91,"li",7)(92,"a",33)(93,"span",11),xt(94,"Carpet Stock"),rt()()(),st(95,"li",7)(96,"a",34)(97,"span",11),xt(98,"View Material lagat"),rt()()(),st(99,"li",7)(100,"a",35)(101,"span",11),xt(102,"View Detail Material Lagat"),rt()()()()()(),st(103,"li",7)(104,"a",36)(105,"span",9),Kt(106,"i",13),rt(),st(107,"span",11),xt(108," Manufacturing "),rt(),Kt(109,"span",14),rt(),st(110,"div",37)(111,"ul",16)(112,"li",7)(113,"a",38)(114,"span",11),xt(115,"Purchase Order"),rt(),Kt(116,"span",14),rt(),st(117,"div",39)(118,"ul",16)(119,"li",7)(120,"a",40)(121,"span",11),xt(122,"Material Purchase Order"),rt()()()()()(),st(123,"li",7)(124,"a",41)(125,"span",11),xt(126,"Material Deying"),rt(),Kt(127,"span",14),rt(),st(128,"div",42)(129,"ul",16)(130,"li",7)(131,"a",43)(132,"span",11),xt(133,"Dyeing Order Issue"),rt()()()()()(),st(134,"li",7)(135,"a",44)(136,"span",11),xt(137,"Buyer Order"),rt()()(),st(138,"li",7)(139,"a",45)(140,"span",11),xt(141,"View Buyer Order"),rt()()(),st(142,"li",7)(143,"a",46)(144,"span",11),xt(145,"Buyer Order List"),rt()()(),st(146,"li",7)(147,"a",47)(148,"span",11),xt(149,"Map"),rt(),Kt(150,"span",14),rt(),st(151,"div",48)(152,"ul",16)(153,"li",7)(154,"a",49)(155,"span",11),xt(156,"New Map Order"),rt()()(),st(157,"li",7)(158,"a",50)(159,"span",11),xt(160,"Regular Map Order"),rt()()()()()(),st(161,"li",7)(162,"a",51)(163,"span",11),xt(164,"Carpet Order-issue"),rt()()(),st(165,"li",7)(166,"a",52)(167,"span",11),xt(168,"view Issued Carpet"),rt()()(),st(169,"li",7)(170,"a",53)(171,"span",11),xt(172,"Print Carpet Order Issue "),rt()()(),st(173,"li",7)(174,"a",54)(175,"span",11),xt(176,"Reciving Carpet"),rt()()(),st(177,"li",7)(178,"a",55)(179,"span",11),xt(180," Carpet Finishing "),rt()()()()()(),st(181,"li",6),xt(182,"K.O.T.I"),rt(),st(183,"li",7)(184,"a",56)(185,"span",9),Kt(186,"i",57),rt(),st(187,"span",11),xt(188," K.O.T.I "),rt(),Kt(189,"span",14),rt(),st(190,"div",58)(191,"ul",16)(192,"li",7)(193,"a",59)(194,"span",11),xt(195," Master "),rt(),Kt(196,"span",14),rt(),st(197,"div",60)(198,"ul",16)(199,"li",7)(200,"a",61)(201,"span",11),xt(202,"Importer Detail"),rt()()(),st(203,"li",7)(204,"a",62)(205,"span",11),xt(206,"Wholeseller Customer"),rt()()(),st(207,"li",7)(208,"a",63)(209,"span",11),xt(210,"Retailer Customer"),rt()()(),st(211,"li",7)(212,"a",64)(213,"span",11),xt(214,"Quality & Design Code"),rt()()(),st(215,"li",7)(216,"a",65)(217,"span",11),xt(218,"Size Code"),rt()()(),st(219,"li",7)(220,"a",66)(221,"span",11),xt(222,"Gst"),rt()()(),st(223,"li",7)(224,"a",67)(225,"span",11),xt(226,"Report"),rt()()()()()(),st(227,"li",7)(228,"a",68)(229,"span",11),xt(230,"Order Form"),rt(),Kt(231,"span",14),rt(),st(232,"div",69)(233,"ul",16)(234,"li",7)(235,"a",70)(236,"span",11),xt(237,"Report"),rt()()()()(),st(238,"div",69)(239,"ul",16)(240,"li",7)(241,"a",71)(242,"span",11),xt(243,"print"),rt()()()()()(),st(244,"li",7)(245,"a",72)(246,"span",11),xt(247,"BarCode Details"),rt()()(),st(248,"li",7)(249,"a",73)(250,"span",11),xt(251,"Import Details"),rt(),Kt(252,"span",14),rt(),st(253,"div",74)(254,"ul",16)(255,"li",7)(256,"a",75)(257,"span",11),xt(258,"Import Invoices"),rt()()(),st(259,"li",7)(260,"a",76)(261,"span",11),xt(262,"Import Invoices Report"),rt()()(),st(263,"li",7)(264,"a",77)(265,"span",11),xt(266,"Container Despatch"),rt()()(),st(267,"li",7)(268,"a",78)(269,"span",11),xt(270,"Container Despatch Report"),rt()()(),st(271,"li",7)(272,"a",79)(273,"span",11),xt(274,"Container Received "),rt()()(),st(275,"li",7)(276,"a",80)(277,"span",11),xt(278,"Container Received Report "),rt()()(),st(279,"li",7)(280,"a",78)(281,"span",11),xt(282,"Report "),rt()()()()()(),st(283,"li",7)(284,"a",81)(285,"span",11),xt(286,"Sale"),rt(),Kt(287,"span",14),rt(),st(288,"div",82)(289,"ul",16)(290,"li",6),xt(291,"Challan"),rt(),st(292,"li",7)(293,"a",83)(294,"span",11),xt(295,"New Challan"),rt()()(),st(296,"li",7)(297,"a",84)(298,"span",11),xt(299,"Report"),rt()()(),st(300,"li",6),xt(301,"Bill"),rt(),st(302,"li",7)(303,"a",85)(304,"span",11),xt(305,"Bills for Wholeseller"),rt()()(),st(306,"li",7)(307,"a",86)(308,"span",11),xt(309,"Bills Report"),rt()()(),st(310,"li",7)(311,"a",87)(312,"span",11),xt(313,"Bills for Retailer"),rt()()(),st(314,"li",7)(315,"a",78)(316,"span",11),xt(317,"Report"),rt()()()()()(),st(318,"li",7)(319,"a",88)(320,"span",11),xt(321,"Stock"),rt(),Kt(322,"span",14),rt(),st(323,"div",89)(324,"ul",16)(325,"li",7)(326,"a",90)(327,"span",11),xt(328,"Report"),rt()()()()()()()()(),st(329,"li",7)(330,"a",91)(331,"span",9),Kt(332,"i",92),rt(),st(333,"span",11),xt(334," Report "),rt(),Kt(335,"span",14),rt(),st(336,"div",93)(337,"ul",16)(338,"li",7)(339,"a",94)(340,"span",11),xt(341,"View Import Invoices"),rt()()(),st(342,"li",7)(343,"a",95)(344,"span",11),xt(345,"Importer Invoice"),rt(),Kt(346,"span",14),rt(),st(347,"div",96)(348,"ul",16)(349,"li",7)(350,"a",97)(351,"span",11),xt(352,"Invoices"),rt()()(),st(353,"li",7)(354,"a",98)(355,"span",11),xt(356,"Cont-Details"),rt()()()()()(),st(357,"li",7)(358,"a",99)(359,"span",11),xt(360,"Order"),rt()()(),st(361,"li",7)(362,"a",100)(363,"span",11),xt(364,"Monthly Report"),rt()()(),st(365,"li",7)(366,"a",86)(367,"span",11),xt(368,"Yearly Report"),rt()()(),st(369,"li",7)(370,"a",101)(371,"span",11),xt(372,"Stock"),rt()()(),st(373,"li",7)(374,"a",102)(375,"span",11),xt(376,"Sale"),rt()()(),st(377,"li",7)(378,"a",103)(379,"span",11),xt(380,"Gst"),rt()()()()()(),st(381,"li",7)(382,"a",104)(383,"span",9),Kt(384,"i",105),rt(),st(385,"span",11),xt(386,"Stock Details"),rt(),Kt(387,"span",14),rt(),st(388,"div",106)(389,"ul",16)(390,"li",7)(391,"a",90)(392,"span",11),xt(393,"Report"),rt()()()()()()()()()),s&2&&Ae("ngStyle",Pa(1,_h,a.isOpen?"block":"none"))},dependencies:[ec,Aa]});let r=n;return r})();var K1=(()=>{let n=class n{constructor(){this.sidebarOpen=!0,this.sideOpen=!0}toggleSideBar(){this.sidebarOpen=!this.sidebarOpen}toggleSideBar1(){this.sideOpen=!this.sideOpen}};n.\u0275fac=function(s){return new(s||n)},n.\u0275cmp=ni({type:n,selectors:[["app-admin"]],decls:8,vars:2,consts:[[1,"layout-wrapper"],[3,"toggle","isOpen"],[1,"page-content"],[1,"px-3"]],template:function(s,a){s&1&&(st(0,"div",0)(1,"app-admin-sidebar",1),Co("toggle",function(){return a.toggleSideBar()}),rt(),st(2,"div",2)(3,"app-admin-header",1),Co("toggle",function(){return a.toggleSideBar1()}),rt(),st(4,"div",3),Kt(5,"router-outlet")(6,"ngx-ui-loader"),rt(),Kt(7,"app-admin-footer"),rt()()),s&2&&(He(),Ae("isOpen",a.sideOpen),He(2),Ae("isOpen",a.sideOpen))},dependencies:[lc,xc,Cc,Lc,Pc],styles:[".pagecontent[_ngcontent-%COMP%]{width:100%}"]});let r=n;return r})();var nf=(()=>{let n=class n{constructor(i){this.httpClient=i,this.apiUrl=pc.apiUrl,this.phaseOne=`${this.apiUrl}/phase-one`,this.phaseTwo=`${this.apiUrl}/phase-two`}createImporter(i){return this.httpClient.post(`${this.phaseTwo}/impoter/create-impoter`,i)}addImporterPrice(i,s){return this.httpClient.patch(`${this.phaseTwo}/impoter/update-price-data/${i}`,s)}getImporter(i){return this.httpClient.get(`${this.phaseTwo}/impoter/get-impoter/${i}`)}addPrice(i,s){return this.httpClient.put(`${this.phaseTwo}/impoter/update-impoter/${s}`,i)}deleteImporter(i){return this.httpClient.delete(`${this.phaseTwo}/impoter/delete-impoter/${i}`)}setData(i){this.data=i}getData(){return this.data}getAllImporter(){return this.httpClient.get(`${this.phaseTwo}/impoter/getAll-impoter`)}UploadExcel(i,s){debugger;let a=new FormData;return a.append("file",i),a.append("impoterNo",s.importNo),a.append("impoterName",s.importerName),this.httpClient.post(`${this.phaseOne}/exlupload/exlupload`,a)}ExcelCalculationData(i){return this.httpClient.post(`${this.phaseOne}/exldata/exldata`,i)}createInvoicePdf(i){debugger;let s=new FormData;return s.append("invoicePdf",i),this.httpClient.post(`${this.phaseOne}/invoice/upload-invoice-pdf`,s)}getInvoicePdf(i){return this.httpClient.get(`${this.phaseOne}/invoice/get-invoice-pdf/${i}`)}getAllInvoicePdf(){return this.httpClient.get(`${this.phaseOne}/invoice/get-all-invoice-pdf`)}createInvoiceDetails(i){return this.httpClient.post(`${this.phaseOne}/impoter/create-impoter-invoice`,i)}getInvoiceDetails(i){return this.httpClient.get(`${this.phaseOne}/impoter/get-impoter-invoice/${i}`)}getAllImporterInvoice(){return this.httpClient.get(`${this.phaseOne}/impoter/getAll-impoter-invoice`)}getExcelSummary(i){debugger;return this.httpClient.post(`${this.phaseOne}/exldata/exldata`,i)}getAllExcelList(i){return this.httpClient.post(`${this.phaseOne}/getAllData/getAll-data`,i)}deleteImporterInvoice(i){return this.httpClient.delete(`${this.phaseOne}/impoter//delete-impoter-invoice/${i}`)}addContainerDespatched(i){return this.httpClient.post(`${this.phaseOne}/container/create-container`,i)}getAllContainerDespatche(){return this.httpClient.get(`${this.phaseOne}/container/getAll-container`)}deleteContainerDespatched(i){return this.httpClient.delete(`${this.phaseOne}/container/delete-container/${i}`)}getContainerDespatch(i){return this.httpClient.get(`${this.phaseOne}/container/get-container/${i}`)}updateContainerDespatched(i,s){return this.httpClient.put(`${this.phaseOne}/container/update-container/${i}`,s)}addQualityDesign(i){return this.httpClient.post(`${this.phaseTwo}/qualityandDesign/craete-qualityanddesign`,i)}getAllQualityDesign(){return this.httpClient.get(`${this.phaseTwo}/qualityandDesign/getAll-qualityanddesign`)}addSizeCode(i){return this.httpClient.post(`${this.phaseOne}/size/create-size`,i)}getAllSizeList(){return this.httpClient.get(`${this.phaseOne}/size/get-all-size`)}uploadBillPdf(i){debugger;let s=new FormData;return s.append("billOfLading",i),this.httpClient.post(`${this.phaseOne}/billOfLadings/upload-billoflading-pdf`,s)}addContainerRecieved(i){return this.httpClient.post(`${this.phaseOne}/containerRcv/upload-containerReceived`,i)}getAllContainerRecieved(){return this.httpClient.get(`${this.phaseOne}/containerRcv/getAll-containerReceived`)}getAllChallan(){return this.httpClient.get(`${this.phaseTwo}/CreateChallan/CreateChallan`)}getChallans(i){debugger;let s={filterValue:i};return this.httpClient.post(`${this.phaseTwo}/CreateChallan/getChallans`,s)}createChallan(i){return this.httpClient.post(`${this.phaseTwo}/CreateChallan/CreateChallan`,i)}getChallan(i){return this.httpClient.get(`${this.phaseTwo}/CreateChallan/CreateChallan/${i}`)}updateChallan(i,s){return this.httpClient.put(`${this.phaseTwo}/CreateChallan/CreateChallan/${i}`,s)}deleteChallan(i,s){return this.httpClient.delete(`${this.phaseTwo}/CreateChallan/CreateChallan/${i}/${s}`)}addWholesaler(i){return this.httpClient.post(`${this.phaseTwo}/wholeSeller/create-wholeseller`,i)}getsWholesalerList(){return this.httpClient.get(`${this.phaseTwo}/wholeSeller/getAll-wholeseller`)}getWholesaller(i){return this.httpClient.get(`${this.phaseTwo}/wholeSeller/get-wholeseller/${i}`)}updateWholesaler(i,s){debugger;return this.httpClient.put(`${this.phaseTwo}/wholeSeller/update-wholeseller/${s}`,i)}deletWholesaller(i){return this.httpClient.delete(`${this.phaseTwo}/wholeSeller/delete-wholeseller/${i}`)}addRetailer(i){return this.httpClient.post(`${this.phaseTwo}/retailer/create-retailer`,i)}getRetailerList(){return this.httpClient.get(`${this.phaseTwo}/retailer/getAll-retailer`)}updateRetailer(i,s){debugger;return this.httpClient.patch(`${this.phaseTwo}/retailer/add-retailer-price/${s}`,i)}getsBill(){return this.httpClient.get(`${this.phaseTwo}/wholeSeller/BillForWholeseller`)}createBill(i){return this.httpClient.post(`${this.phaseTwo}/wholeSeller/BillForWholeseller`,i)}updateBill(i,s){debugger;return this.httpClient.put(`${this.phaseTwo}/wholeSeller/BillForWholeseller/${s}`,i)}deleteWholesellerBill(i){debugger;return this.httpClient.delete(`${this.phaseTwo}/wholeSeller/BillForWholeseller/${i}`)}getBills(){return this.httpClient.get(`${this.phaseTwo}/wholeSeller/getAll-billwholeseller`)}createGst(i){return this.httpClient.post(`${this.phaseTwo}/gst/create-gst`,i)}getAllGst(){return this.httpClient.get(`${this.phaseTwo}/gst/get-all-gst`)}getGst(i){return this.httpClient.get(`${this.phaseTwo}/gst/get-gst/${i}`)}updateGst(i,s){return this.httpClient.put(`${this.phaseTwo}/gst/update-gst/${i}`,s)}deleteGst(i){return this.httpClient.delete(`${this.phaseTwo}/gst/delete-gst/${i}`)}getsretailser(){return this.httpClient.get(`${this.phaseTwo}/`)}getBarCodeDetails(i){return this.httpClient.post(`${this.phaseTwo}/wholeSeller/carpet-details`,i)}getCarpetStock(i){return this.httpClient.post(`${this.phaseOne}/containerRcv/carpet-stock-details`,i)}getSolededStock(i,s){debugger;let a=new rc().set("page",i.toString()).set("limit",s.toString());return this.httpClient.get(`${this.phaseOne}/containerRcv/get-all-soleded`,{params:a})}};n.\u0275fac=function(s){return new(s||n)(Ca(oc))},n.\u0275prov=Bi({token:n,factory:n.\u0275fac,providedIn:"root"});let r=n;return r})();var of=(()=>{let n=class n{constructor(){this.currentNumber=1}generateKOTINumber(i){this.currentNumber=i;let a=`KOTI-${this.currentNumber.toString().padStart(5,"0")}`;return this.currentNumber++,a}convertDate(i){let s=new Date(i),a=s.getDate(),c=s.getMonth()+1,h=s.getFullYear();return`${a<10?"0":""}${a}.${c<10?"0":""}${c}.${h}`}convertDate2(i){let s=new Date(i),a=s.getDate(),c=s.getMonth()+1,h=s.getFullYear();return`${a<10?"0":""}${a}/${c<10?"0":""}${c}/${h}`}formatDate(i){let s=i.split("/"),a=s[0],c=s[1],h=s[2],f=new Date(`${h}-${c}-${a}`),g=f.toLocaleString("en-US",{weekday:"short"}),b=f.toLocaleString("en-US",{month:"long"});return`${g} ${b} ${a} ${h} 00:00:00 GMT+0530 (India Standard Time)`}formatDateToIso(i){let s=i.split("/"),a=s[0],c=s[1],h=s[2];return new Date(`${h}-${c}-${a}T00:00:00.000Z`).toISOString()}setData(i){debugger;this.carpetData=i}getData(){debugger;return this.carpetData}};n.\u0275fac=function(s){return new(s||n)},n.\u0275prov=Bi({token:n,factory:n.\u0275fac,providedIn:"root"});let r=n;return r})();var fu=Ul(Ku());hu();var za=Ul(Qu());var hf=(()=>{let n=class n{constructor(){}success(i,s){za.default.fire({title:i,text:s,icon:"success"})}error(i,s){za.default.fire({title:i,text:s,icon:"error"})}confirm(i){return za.default.fire({title:i.title||"Are you sure?",text:i.text||"You won't be able to revert this!",icon:i.icon||"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:i.confirmButtonText||"Yes, delete it!",cancelButtonText:i.cancelButtonText||"Cancel"})}generatePDF(i){let s=document.getElementById(i);s&&(0,fu.default)(s).then(a=>{let c=a.toDataURL("image/png"),h=new ol("p","mm","a4"),f=h.getImageProperties(c),g=h.internal.pageSize.getWidth(),b=f.height*g/f.width;h.addImage(c,"PNG",0,0,g,b),h.save("document.pdf")})}};n.\u0275fac=function(s){return new(s||n)},n.\u0275prov=Bi({token:n,factory:n.\u0275fac,providedIn:"root"});let r=n;return r})();export{yc as a,gh as b,xc as c,q1 as d,K1 as e,nf as f,of as g,Wt as h,x1 as i,hu as j,hf as k};
