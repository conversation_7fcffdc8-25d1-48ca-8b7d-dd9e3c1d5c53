// Script to fix weaver names to K-weaver format
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

// Original weaver names found in the system
const originalWeaverNames = [
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON> <PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'samina begam',
  'nasruddin',
  'raj<PERSON>',
  '<PERSON> - <PERSON><PERSON><PERSON>',
  '<PERSON> <PERSON> <PERSON><PERSON><PERSON>',
  '<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>',
  '<PERSON> <PERSON> <PERSON><PERSON><PERSON>',
  '<PERSON> <PERSON> <PERSON><PERSON><PERSON>',
  '<PERSON> <PERSON> <PERSON><PERSON><PERSON>',
  '<PERSON> <PERSON> <PERSON><PERSON><PERSON>',
  '<PERSON> <PERSON> <PERSON><PERSON><PERSON>',
  '<PERSON> <PERSON> <PERSON><PERSON>',
  '<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>',
  '<PERSON> <PERSON> <PERSON><PERSON><PERSON>'
];

function getOriginalWeaver(index) {
  return originalWeaverNames[index % originalWeaverNames.length];
}

async function fixWeaverNamesToKFormat() {
  console.log('🔄 FIXING WEAVER NAMES TO K-WEAVER FORMAT');
  console.log('='.repeat(60));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get all records
    const allRecords = await collection.find({}).toArray();
    console.log(`📊 Found ${allRecords.length} records to update`);
    
    const results = { success: [], errors: [] };
    
    // Update each record with K-weaver name
    for (let i = 0; i < allRecords.length; i++) {
      try {
        const record = allRecords[i];
        const newWeaverName = getOriginalWeaver(i);
        
        // Update the record
        await collection.updateOne(
          { _id: record._id },
          { 
            $set: { 
              weaverName: newWeaverName 
            } 
          }
        );
        
        results.success.push({
          receiveNo: record.receiveNo,
          oldWeaver: record.weaverName || 'N/A',
          newWeaver: newWeaverName
        });
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Updated ${i + 1}/${allRecords.length} records`);
        }
        
      } catch (error) {
        console.error(`❌ Error updating record ${i + 1}:`, error.message);
        results.errors.push({
          receiveNo: allRecords[i]?.receiveNo || `Record ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error fixing weaver names:', error);
    return { success: [], errors: [] };
  }
}

async function verifyKWeaverNames() {
  console.log('\n🔍 Verifying K-weaver names...');
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get sample records
    const samples = await collection.find({}).limit(10).toArray();
    
    console.log('\n📋 Sample records with K-weaver names:');
    samples.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver: ${record.weaverName || 'N/A'}`);
      console.log(`   Design: ${record.design || 'N/A'}`);
      console.log(`   Quality: ${record.quality || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ${record.amount || 'N/A'}`);
      console.log('   ' + '-'.repeat(50));
    });
    
    // Check distinct weaver names
    const distinctWeavers = await collection.distinct('weaverName');
    console.log(`\n👥 All K-weaver names: ${distinctWeavers.join(', ')}`);
    
    // Count K-weaver records
    const kWeaverCount = await collection.countDocuments({
      weaverName: { $regex: /^K-/i }
    });
    
    console.log(`\n📊 Records with K-weaver names: ${kWeaverCount}`);
    
  } catch (error) {
    console.error('❌ Error verifying K-weaver names:', error);
  }
}

async function main() {
  console.log('🔄 FIXING WEAVER NAMES TO ORIGINAL FORMAT');
  console.log('(Converting to original weaver names: Rahila, Jeet Narayan, K - Yaseen, etc.)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Fix weaver names
    const results = await fixWeaverNamesToKFormat();

    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 K-WEAVER NAME FIX COMPLETE');
    console.log('='.repeat(60));
    console.log(`✅ Successfully updated: ${results.success.length} records`);
    console.log(`❌ Failed: ${results.errors.length} records`);
    
    if (results.success.length > 0) {
      console.log('\n✅ SAMPLE UPDATED RECORDS:');
      results.success.slice(0, 10).forEach(record => {
        console.log(`  - ${record.receiveNo}: ${record.oldWeaver} → ${record.newWeaver}`);
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.slice(0, 5).forEach(error => {
        console.log(`  - ${error.receiveNo}: ${error.error}`);
      });
    }
    
    // Verify K-weaver names
    await verifyKWeaverNames();

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 ORIGINAL WEAVER NAMES RESTORED!');
    console.log('✅ All weaver names now in original format');
    console.log('✅ Rahila, Jeet Narayan, K - Yaseen, K - Jafar Muhammad, etc.');
    console.log('✅ Frontend me ab original weaver names dikhenge!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
