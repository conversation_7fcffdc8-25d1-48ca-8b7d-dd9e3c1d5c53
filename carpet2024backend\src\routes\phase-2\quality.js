const express = require('express');
const AddqualityController = require('../../controller/quality-controller');

const router = express.Router();
const addqualityController = new AddqualityController();

router.post('/create-quality', addqualityController.createQuality.bind(addqualityController));
router.get('/getAll-quality', addqualityController.getAllQualities.bind(addqualityController));
router.get('/get-quality/:id', addqualityController.getQualityById.bind(addqualityController));
router.put('/update-quality/:id', addqualityController.updateQuality.bind(addqualityController));
router.delete('/delete-quality/:id', addqualityController.deleteQuality.bind(addqualityController));

module.exports = router;
