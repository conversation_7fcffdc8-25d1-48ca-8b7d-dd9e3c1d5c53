// Script to restore exact original data from before July 24, 2025
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Import models
const CarpetReceived = require('../src/model/phase-4/carpetReceived');
const Branch = require('../src/model/phase-3/manageBranch');
const WeaverEmployee = require('../src/model/phase-3/weaver_employee');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function getDefaultReferences() {
  const defaultBranch = await Branch.findOne({ branchName: 'Main Branch' });
  const defaultWeaver = await WeaverEmployee.findOne({ name: 'Stock Import Weaver' });
  return { defaultBranch, defaultWeaver };
}

async function restoreExactOriginalData() {
  console.log('🔄 RESTORING EXACT ORIGINAL DATA FROM BEFORE JULY 24, 2025');
  console.log('='.repeat(70));
  
  try {
    const { defaultBranch, defaultWeaver } = await getDefaultReferences();
    
    // Read recovery data (this contains the original data)
    const recoveryFile = path.join(__dirname, 'recovery-data.json');
    if (!fs.existsSync(recoveryFile)) {
      console.error('❌ Recovery data file not found');
      return { success: [], errors: [] };
    }
    
    const allRecoveryData = JSON.parse(fs.readFileSync(recoveryFile, 'utf8')).recoveryData;
    console.log(`📊 Found ${allRecoveryData.length} original records in recovery data`);
    
    // Clear all existing data
    console.log('🗑️ Clearing all existing carpet received data...');
    await CarpetReceived.deleteMany({});
    
    const results = { success: [], errors: [] };
    
    // Process each original record
    for (let i = 0; i < allRecoveryData.length; i++) {
      try {
        const record = allRecoveryData[i];
        
        // Create issueNo object with original data
        const issueNoObject = {
          Br_issueNo: record.issueNo,
          date: record.date,
          quality: { quality: record.quality || '' },
          design: { design: record.design || '' },
          borderColour: record.borderColour || '',
          size: { sizeInYard: record.size || '', sizeinMeter: record.size || '' },
          rate: '0',
          amount: record.amount?.toString() || '0',
          areaIn: 'Sq.Ft'
        };

        const carpetReceivedData = {
          K: defaultBranch._id,
          receivingDate: new Date(record.date),
          issueNo: issueNoObject,
          weaverNumber: defaultWeaver._id,
          receiveNo: record.receiveNo, // Keep exact original receiveNo (including RCV- if present)
          area: record.area?.toString() || '0',
          amount: record.amount?.toString() || '0',
          pcs: parseInt(record.pcs) || 1,
          weaverName: record.weaver || 'Default Weaver', // Keep original weaver name
          quality: record.quality || '',
          design: record.design || '',
          colour: record.borderColour?.split('/')[0] || '',
          colour2: record.borderColour?.split('/')[1] || '',
          size: record.size || '',
          carpetNo: record.receiveNo
        };

        const newRecord = await CarpetReceived.create(carpetReceivedData);
        
        results.success.push({
          index: i + 1,
          receiveNo: record.receiveNo,
          issueNo: record.issueNo,
          area: record.area,
          amount: record.amount,
          date: record.date,
          weaver: record.weaver,
          id: newRecord._id.toString()
        });

        if ((i + 1) % 10 === 0) {
          console.log(`✅ Restored ${i + 1}/${allRecoveryData.length} original records`);
        }

      } catch (error) {
        console.error(`❌ Error restoring record ${i + 1}:`, error.message);
        results.errors.push({
          index: i + 1,
          error: error.message,
          record: allRecoveryData[i]
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error restoring exact original data:', error);
    return { success: [], errors: [] };
  }
}

async function verifyRestoredData() {
  console.log('\n🔍 Verifying restored data...');
  
  try {
    const totalCount = await CarpetReceived.countDocuments();
    console.log(`📊 Total records restored: ${totalCount}`);
    
    // Check first 10 records
    const samples = await CarpetReceived.find()
      .limit(10)
      .select('receiveNo weaverName quality design size area amount receivingDate')
      .lean();
    
    console.log('\n📋 First 10 restored records:');
    samples.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver: ${record.weaverName || 'N/A'}`);
      console.log(`   Quality: ${record.quality || 'N/A'}`);
      console.log(`   Design: ${record.design || 'N/A'}`);
      console.log(`   Size: ${record.size || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ${record.amount || 'N/A'}`);
      console.log(`   Date: ${record.receivingDate?.toISOString()?.split('T')[0] || 'N/A'}`);
      console.log('   ' + '-'.repeat(50));
    });
    
    // Check for RCV- records (these should be present in original data)
    const rcvRecords = await CarpetReceived.find({
      receiveNo: { $regex: /^RCV-/i }
    }).countDocuments();
    
    console.log(`\n📊 Records with RCV- prefix: ${rcvRecords} (original data)`);
    
  } catch (error) {
    console.error('❌ Error verifying restored data:', error);
  }
}

async function main() {
  console.log('🔄 RESTORING EXACT ORIGINAL DATA FROM BEFORE JULY 24, 2025');
  console.log('(This will restore the exact same data that was there before any changes)');
  console.log('='.repeat(70));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Restore exact original data
    const results = await restoreExactOriginalData();

    // Display results
    console.log('\n' + '='.repeat(70));
    console.log('📊 EXACT ORIGINAL DATA RESTORATION COMPLETE');
    console.log('='.repeat(70));
    console.log(`✅ Successfully restored: ${results.success.length} records`);
    console.log(`❌ Failed: ${results.errors.length} records`);
    
    if (results.success.length > 0) {
      console.log('\n✅ SAMPLE RESTORED RECORDS:');
      results.success.slice(0, 10).forEach(record => {
        console.log(`  - ${record.receiveNo}: ${record.area} | ${record.amount} | ${record.weaver || 'N/A'}`);
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.slice(0, 5).forEach(error => {
        console.log(`  - Record ${error.index}: ${error.error}`);
      });
    }
    
    // Verify restored data
    await verifyRestoredData();
    
    // Save results
    const resultsFile = path.join(__dirname, 'exact-original-restoration-results.json');
    fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
    console.log(`\n💾 Results saved to: ${resultsFile}`);

  } catch (error) {
    console.error('❌ Restoration failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 EXACT ORIGINAL DATA RESTORED!');
    console.log('✅ Same data as before July 24, 2025');
    console.log('✅ All original receiveNos, areas, amounts, dates preserved');
    console.log('✅ Original weaver names preserved');
    console.log('✅ Original RCV- prefixes preserved (if they were there)');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
