const CarpetReceived = require('../../model/phase-4/carpetReceived');


const createCarpetReceived = async (data) => {
  const newCarpetReceived = new CarpetReceived(data);
  return await newCarpetReceived.save();
};

const getAllCarpetReceiveds = async () => {
  return await CarpetReceived.find()
    .populate('weaverNumber')
    .populate('K');
};

const getCarpetReceivedById = async (id) => {
  return await CarpetReceived.findById(id);
};

const updateCarpetReceived = async (id, data) => {
  return await CarpetReceived.findByIdAndUpdate(id, data, { new: true });
};

const deleteCarpetReceived = async (id) => {
  return await CarpetReceived.findByIdAndDelete(id);
};

const getAllrecivedCarpet = async () => {
  return await CarpetReceived.find()
    .populate('buyerOrder')
    .populate('weaverNumber')
    .populate('quality')
    .populate('design')
    .populate('size')
    .populate('branch');

};


// Count received pieces for a specific issue ID
const countReceivedPiecesByIssueId = async (issueId) => {
  try {
    console.log('Counting pieces for issue ID:', issueId);

    // Convert issueId to ObjectId if it's a string
    const mongoose = require('mongoose');
    let objectId;
    try {
      objectId = mongoose.Types.ObjectId.isValid(issueId) ? new mongoose.Types.ObjectId(issueId) : issueId;
    } catch (e) {
      objectId = issueId;
    }

    // First, let's see what data we have
    const allCarpets = await CarpetReceived.find({});
    console.log('All carpets in database:', allCarpets.length);

    // Check different possible field structures
    const matchQueries = [
      { 'issueNo._id': objectId },
      { 'issueNo._id': issueId },
      { 'issueNo': objectId },
      { 'issueNo': issueId }
    ];

    let totalPcs = 0;

    for (const query of matchQueries) {
      const result = await CarpetReceived.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            totalPcs: { $sum: '$pcs' }
          }
        }
      ]);

      if (result.length > 0 && result[0].totalPcs > 0) {
        totalPcs = result[0].totalPcs;
        console.log('Found match with query:', query, 'Total pieces:', totalPcs);
        break;
      }
    }

    console.log('Final count for issue', issueId, ':', totalPcs);
    return totalPcs;
  } catch (error) {
    console.error('Error counting received pieces:', error);
    return 0;
  }
};

// Get received carpets by issue IDs
const getReceivedCarpetsByIssueIds = async (issueIds) => {
  try {
    console.log('Searching for issue IDs:', issueIds);

    // Convert string IDs to ObjectId if needed
    const ObjectId = require('mongoose').Types.ObjectId;
    const objectIds = issueIds.map(id => {
      try {
        return new ObjectId(id);
      } catch (e) {
        return id; // Keep as string if conversion fails
      }
    });

    // Query based on the actual MongoDB structure shown in screenshots
    const receivedCarpets = await CarpetReceived.find({
      $or: [
        { 'issueNo._id': { $in: objectIds } },
        { 'issueNo._id': { $in: issueIds } }
      ]
    })
    .populate('K', 'branchCode')
    .populate('weaverNumber', 'name weaverName')
    .populate({
      path: 'issueNo',
      populate: [
        { path: 'quality', select: 'quality' },
        { path: 'design', select: 'design' },
        { path: 'size', select: 'sizeInYard' }
      ]
    })
    .sort({ receivingDate: -1 });

    console.log('Found received carpets:', receivedCarpets.length);
    console.log('Received carpets data:', JSON.stringify(receivedCarpets, null, 2));
    return receivedCarpets;
  } catch (error) {
    console.error('Error fetching received carpets by issue IDs:', error);
    throw error;
  }
};

module.exports = {
  createCarpetReceived,
  getAllCarpetReceiveds,
  getCarpetReceivedById,
  updateCarpetReceived,
  deleteCarpetReceived,
  countReceivedPiecesByIssueId,
  getAllrecivedCarpet,
  getReceivedCarpetsByIssueIds,
};
