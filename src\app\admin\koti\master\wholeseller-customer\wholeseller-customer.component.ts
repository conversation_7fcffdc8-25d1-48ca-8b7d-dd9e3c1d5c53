import {
  AfterViewInit,
  Component,
  Inject,
  OnInit,
  Optional,
  ViewChild,
} from '@angular/core';
import {
  Form<PERSON><PERSON>er,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MyErrorStateMatcher } from '../importer-detail/importer-detail.component';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { ImporterService } from '../../../../services/importer.service';
import { error } from 'console';
import Swal from 'sweetalert2';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

export interface UserData {
  customerCode: string;
  id: string;
  index: number;
  customerName: string;
  address: string;
  zipCode: string;
  country: string;
  contactNumber: string;
  email: string;
  bankName: string;
  email2: string;
  enterPassword: string;
  bankAddress: string;
  accountNumber: string;
  swiftCode: string;
  contactNumber2: string;
  bankContactNumber: string;
}

const ELEMENT_DATA: UserData[] = [];
@Component({
  selector: 'app-wholeseller-customer',
  templateUrl: './wholeseller-customer.component.html',
  styleUrl: './wholeseller-customer.component.css',
})
export class WholesellerCustomerComponent implements OnInit {
  Country = 'option2';

  emailFormControl = new FormControl('', [
    Validators.required,
    Validators.email,
  ]);

  matcher = new MyErrorStateMatcher();

  hide = true;
  country: string[] = ['Germany', 'India'];
  displayedColumns: string[] = [
    'id',
    'customerCode',
    'customerName',
    'address',
    'zipCode',
    'country',
    'contactNumber',
    'email',
    'bankName',
    'email2',
    'enterPassword',
    'bankAddress',
    'accountNumber',
    'swiftCode',
    'contactNumber2',
    'bankContactNumber',
    'action',
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);
  isOpenDialog: boolean = false;

  isUpdate:boolean=false;
  frmWholeshaler!: FormGroup;
  constructor(
    private fb: FormBuilder,
    private _services: ImporterService,
    @Optional() public dialogRef: MatDialogRef<WholesellerCustomerComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}
  ngOnInit(): void {
    this.frmWholeshaler = this.fb.group({
      customerCode: [],
      customerName: [],
      address: [],
      zipCode: [],
      country: [],
      contactNumber: [],
      email: [],
      bankName: [],
      bankEmail: [],
      enterPassword: [],
      bankAddress: [],
      accountNumber: [],
      swiftCode: [],
      contactNumber2: [],
      bankContactNumber: [],
    });

    this.getsWholsaler();
    this.dialog();
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addWholshaler() {
    console.log(this.frmWholeshaler.value);
    let formData = this.frmWholeshaler.value;
    let frmData = {
      customerCode: formData.customerCode,
      customerName: formData.customerName,
      address: formData.address,
      zipCode: formData.zipCode,
      country: formData.country,
      contactNumber: formData.contactNumber,
      email: formData.email,
      bankDetails: {
        bankName: formData.bankName,
        email: formData.email,
        enterPassword: formData.enterPassword,
        bankAddress: formData.bankAddress,
        accountNumber: formData.accountNumber,
        swiftCode: formData.swiftCode,
        contactNumber: formData.contactNumber,
        bankContactNumber: formData.bankContactNumber,
      },
    };
    this._services.addWholesaler(frmData).subscribe(
      (resp: any) => {
        Swal.fire('Success!', 'Data has been saved successfully', 'success');
        this.getsWholsaler();
        this.clear()
      },
      (error) => {
        Swal.fire('warning!', error.message, 'warning');
      }
    );
  }

  getsWholsaler() {
    this._services.getsWholesalerList().subscribe(
      (resp: any) => {
        if (resp) {
          ELEMENT_DATA.length = 0;
          resp.map((x: any, ind: number) => {
            ELEMENT_DATA.push({
              id: x._id,
              index: ind + 1,
              customerCode: x.customerCode,
              customerName: x.customerName,
              address: x.address,
              zipCode: x.zipCode,
              country: x.country,
              contactNumber: x.contactNumber,
              email: x.email,
              bankName: x.bankDetails?.bankName,
              email2: x.bankDetails?.email,
              enterPassword: x.bankDetails?.enterPassword,
              bankAddress: x.bankDetails?.bankAddress,
              accountNumber: x.bankDetails?.accountNumber,
              swiftCode: x.bankDetails?.swiftCode,
              contactNumber2: x.bankDetails?.contactNumber,
              bankContactNumber: x.bankDetails?.bankContactNumber,
            });
          });
          this.dataSource = new MatTableDataSource(ELEMENT_DATA);
          this.ngAfterViewInit();

          return;
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }

  dialog() {
    if (this.dialogRef) {
      this.isOpenDialog = true;
      console.log(this.dialogRef);
    }
  }

  logRow(row: UserData) {
    console.log('Clicked row:', row);

    if (this.dialogRef) {
      this.dialogRef.close(row);
    }
  }
editId:any;
  edit(id: any) {
    this._services.getWholesaller(id).subscribe((resp: any) => {
      debugger;

      console.log(resp);
      let data = resp;
      this.frmWholeshaler.patchValue({
        customerCode: data.customerCode,
        customerName: data.customerName,
        address: data.address,
        zipCode: data.zipCode,
        country: data.country,
        contactNumber: data.contactNumber,
        email: data.email,
        bankName: data.bankDetails.bankName,
        bankEmail: data.bankDetails.email,
        enterPassword: data.bankDetails.enterPassword,
        bankAddress: data.bankDetails.bankAddress,
        accountNumber: data.bankDetails.accountNumber,
        swiftCode: data.bankDetails.swiftCode,
        contactNumber2: data.contactNumber2,
        bankContactNumber: data.bankDetails.bankContactNumber,
      });
      this.editId=id;
      this.isUpdate=true
    });
  }
  Update(){
    let formData = this.frmWholeshaler.value;
    let frmData = {
      customerCode: formData.customerCode,
      customerName: formData.customerName,
      address: formData.address,
      zipCode: formData.zipCode,
      country: formData.country,
      contactNumber: formData.contactNumber,
      email: formData.email,
      bankDetails: {
        bankName: formData.bankName,
        email: formData.email,
        enterPassword: formData.enterPassword,
        bankAddress: formData.bankAddress,
        accountNumber: formData.accountNumber,
        swiftCode: formData.swiftCode,
        contactNumber: formData.contactNumber,
        bankContactNumber: formData.bankContactNumber,
      },
    };
    this._services.updateWholesaler(frmData,this.editId).subscribe((resp:any)=>{
      Swal.fire({
        title: "Updated!",
        text: 'Wholsaler record has been updated',
        icon: "success"
      });
      this.clear()
    })
  }
  delete(id: any) {


      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!"
      }).then((result) => {
        if (result.isConfirmed) {

          this._services.deletWholesaller(id).subscribe((resp:any)=>{
            debugger
            Swal.fire({
              title: "Deleted!",
              text: 'Wholsaler has been deleted',
              icon: "success"
            });
            this.getsWholsaler();
          })

        }
      });


  }

  clear(){
    this.frmWholeshaler.reset();
  }
}
