<div class="container mt-4">
  <section style="margin: 10px">
    <fieldset style="min-height: 200px">
      <legend><b> {{isUpdated?'Update':'Add'}} Raw Material Group</b></legend>

      <form [formGroup]="frmRawMaterial" (ngSubmit)="addRawMaterial()">
        <div class="row">
          <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline">
              <mat-label>Group</mat-label>
              <mat-select formControlName="Group">
                @for (group of groupList; track group) {
                <mat-option [value]="group">{{ group }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmRawMaterial.get('Group')?.invalid && (frmRawMaterial.get('Group')?.dirty || frmRawMaterial.get('Group')?.touched)">
              <div *ngIf="frmRawMaterial.get('Group')?.errors?.['required']">
               <span class="errText"> Please select a group type.</span>
              </div>

            </div>
          </div>

          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Item</mat-label>
              <input matInput placeholder="Item" formControlName="Item" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmRawMaterial.get('Item')?.invalid && (frmRawMaterial.get('Item')?.dirty || frmRawMaterial.get('Item')?.touched)">
              <div *ngIf="frmRawMaterial.get('Item')?.errors?.['required']">
               <span class="errText">  Item is requierd.</span>
              </div>

            </div>
          </div>
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Quality</mat-label>
              <input matInput placeholder="Quality" formControlName="Quality" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmRawMaterial.get('Quality')?.invalid && (frmRawMaterial.get('Quality')?.dirty || frmRawMaterial.get('Quality')?.touched)">
              <div *ngIf="frmRawMaterial.get('Quality')?.errors?.['required']">
               <span class="errText"> Quality is requierd.</span>
              </div>

            </div>
            <!-- <mat-form-field appearance="outline">
              <mat-label>Quality</mat-label>
              <mat-select formControlName="Quality" (valueChange)="onQuality($event)">
                <mat-option *ngFor="let quality of qualityList" value="{{quality.id}}">
                  {{ quality.quality }}
                </mat-option>
              </mat-select>
            </mat-form-field> -->
          </div>
          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Description</mat-label>
              <input matInput placeholder="Description" formControlName="Description" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmRawMaterial.get('Description')?.invalid && (frmRawMaterial.get('Description')?.dirty || frmRawMaterial.get('Description')?.touched)">
              <div *ngIf="frmRawMaterial.get('Description')?.errors?.['required']">
               <span class="errText"> Description is requierd.</span>
              </div>

            </div>
          </div>
          <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline">
              <mat-label>Count</mat-label>
              <input matInput placeholder="Count" formControlName="Count" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmRawMaterial.get('Count')?.invalid && (frmRawMaterial.get('Count')?.dirty || frmRawMaterial.get('Count')?.touched)">
              <div *ngIf="frmRawMaterial.get('Count')?.errors?.['required']">
               <span class="errText"> Count is requierd.</span>
              </div>

            </div>
          </div>


          <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline">
              <mat-label>Yarn Colour</mat-label>
              <input matInput placeholder="Yarn Colour" formControlName="Color" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmRawMaterial.get('Color')?.invalid && (frmRawMaterial.get('Color')?.dirty || frmRawMaterial.get('Color')?.touched)">
              <div *ngIf="frmRawMaterial.get('Color')?.errors?.['required']">
               <span class="errText"> Yarn color is requierd.</span>
              </div>

            </div>
          </div>
          <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Details</mat-label>
              <input matInput placeholder="Details" formControlName="Details" />
            </mat-form-field>
          </div>

          <div class="col-lg-3 col-md-3">
            <label class="form-label"><b>Upload Material Image</b></label
            ><input type="file" class="form-control"   (change)="file($event)" />
          </div>

          <div class="mb-2 col-md-3">
            <button *ngIf="!isUpdated; else updateTemplate" mat-flat-button color="primary" [disabled]="!frmRawMaterial.valid">Add</button>
            <ng-template #updateTemplate>
              <button mat-flat-button color="primary" (click)="update()" type="button" [disabled]="!frmRawMaterial.valid">Update</button>
            </ng-template>
          </div>
        </div>
      </form>
    </fieldset>
  </section>
</div>

<div class="container mt-4">
  <section>
    <fieldset style="min-height: 200px">
      <legend><b>List</b></legend>
      <div class="row">
        <div class="col-md-4">
          <mat-form-field appearance="outline">
            <mat-label>Search</mat-label>
            <input
              matInput
              (keyup)="applyFilter($event)"
              placeholder="Ex. Jack"
              #input
            />
          </mat-form-field>
        </div>
      </div>

      <div class="row">
        <div class="col-12">
          <div >
            <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8" style="width: max-content;">
              <!-- ID Column -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>No.</th>
                <td mat-cell *matCellDef="let row">{{ row.index }}</td>
              </ng-container>

              <!-- Group Column -->
              <ng-container matColumnDef="Group">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Group</th>
                <td mat-cell *matCellDef="let row">{{ row.Group }}</td>
              </ng-container>

              <!-- Item Column -->
              <ng-container matColumnDef="Item">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Item</th>
                <td mat-cell *matCellDef="let row">{{ row.Item }}</td>
              </ng-container>

              <!-- Count Column -->
              <ng-container matColumnDef="Count">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Count</th>
                <td mat-cell *matCellDef="let row">{{ row.Count }}</td>
              </ng-container>

              <ng-container matColumnDef="Quality">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                 M Quality
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Quality }}</td>
              </ng-container>
              <!-- Colour Column -->
              <ng-container matColumnDef="Colour">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Colour
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Colour }}</td>
              </ng-container>

              <!-- Description Column -->
              <ng-container matColumnDef="Description">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Description
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Description }}</td>
              </ng-container>

              <!-- Details Column -->
              <ng-container matColumnDef="Details">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Details
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Details }}</td>
              </ng-container>
              <!-- Image Column -->
              <ng-container matColumnDef="Image">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Image</th>
                <td mat-cell *matCellDef="let row"><img (click)="openModal(row.Image)" crossorigin="anonymous" width="50px" [src]="row.Image" alt="Image" ></td>
              </ng-container>
              <!-- Action Column -->
              <ng-container matColumnDef="Action">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Action
                </th>
                <td mat-cell *matCellDef="let row">

                  <a (click)="edit(row.id)"
                  ><i
                    class="fa fa-pencil-square-o fa-edit"
                    title="Edit Challan "
                    aria-hidden="true"
                  ></i
                ></a>
                &nbsp;
                <a (click)="delete(row.id)"><i class="fa fa-trash-o" aria-hidden="true"></i></a>

                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>

              <!-- Row shown when there is no matching data. -->
              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="4">
                  No data matching the filter "{{ input.value }}"
                </td>
              </tr>
            </table>

            <mat-paginator [length]="100" [pageSize]="10" [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page">
            </mat-paginator>
          </div>
        </div>
      </div>
    </fieldset>
  </section>
</div>




<div class="modal fade" id="myModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Image Preview</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <img [src]="selectedImageUrl" crossorigin="anonymous" class="img-fluid" id="img01" alt="Image">
        <div id="caption">{{ selectedImageUrl }}</div>
      </div>
    </div>
  </div>
</div>
