<div class="container mt-4">
  <section>
    <div class="row text-center"><div class="col-md-12"><h3>{{ isUpdated ? 'Update retailser Bill' : 'Add retailser Bill' }}</h3></div></div>
    <fieldset>
      <legend><b>Whole Seller Details</b></legend>

     <form (ngSubmit)="onSubmit()" [formGroup]="frmBill">
      <div class="row">
        <div class="mb-2 col-md-2">
          <mat-form-field appearance="outline" class="ex-width">
            <mat-label>Bill No.</mat-label>
            <input matInput placeholder="Bill No." formControlName="billNo" />
          </mat-form-field>
        </div>
        <div class="mb-2 col-md-3">
          <mat-form-field appearance="outline" class="ex-width">
            <mat-label>Whole Seller Name</mat-label>
            <mat-select formControlName="retailserName">
              @for (retailer of retailerList; track retailer) {
              <mat-option [value]="retailer.retailerName">{{ retailer.retailerName }}</mat-option>
              }
            </mat-select>
          </mat-form-field>
        </div>
        <div class="mb-2 col-md-2">
          <mat-form-field class="ex-width" appearance="outline">
            <mat-label> Date</mat-label>
            <input
              matInput
              [matDatepicker]="picker"
              (dateInput)="addEvent($event)"
              formControlName="chooseAdate"
              (dateChange)="addEvent($event)"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="picker"
            ></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>

        <div class="mb-2 col-md-3">
          <mat-form-field appearance="outline">
            <mat-label>Challan No</mat-label>
            <mat-select  multiple (selectionChange)="selectBcr($event)" [formControl]="myControl">
              @for (bcr of options; track bcr) {
                <mat-option [value]="bcr">{{bcr}}</mat-option>
              }
            </mat-select>
          </mat-form-field>
          <!-- <mat-form-field class="example-full-width" appearance="outline">
            <mat-label>Challan No</mat-label>
            <input
              type="text" style="width:150px"
              placeholder="Challan No."
              aria-label="Number"
              matInput
              [formControl]="myControl"
              [matAutocomplete]="auto"
            />
            <mat-autocomplete
              autoActiveFirstOption
              #auto="matAutocomplete"
              (optionSelected)="onOptionSelected($event)"
            >
              <mat-option
                *ngFor="let option of filteredOptions | async"
                [value]="option"
              >
                {{ option }}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field> -->
        </div>

        <div class="mt-2 col-md-1" *ngIf="!isUpdated">
          <button mat-flat-button color="primary">Save</button>
        </div>
        <div class="mt-2 col-md-1"*ngIf="isUpdated">
          <button mat-flat-button color="primary" type="button" (click)="updateBill()">Update</button>
        </div>
        <div class="mt-2 col-md-2">
          <button mat-flat-button type="button" (click)="reset()" color="primary">Add New</button>
        </div>
      </div>
     </form>
    </fieldset>
  </section>
</div>

<div class="container mt-5">
  <section>
    <fieldset>
      <legend><b>List</b></legend>

      <div class="row mt-2">
        <div style="height: 372px; overflow: auto" #scrollContainer>
          <table class="table">
            <thead>
              <tr class="tt">
                <th>Bar Code</th>
                <th>Carpet Details</th>
                <th>Area</th>
                <th>Evk Price</th>
                <th>Amount</th>
                <th>Action</th>
              </tr>
            </thead>

            <tr *ngFor="let item of billItemList; let i = index">
              <td>{{ item.carpetNo }}</td>
              <td>
                <span
                  style="
                    color: black;
                    font-weight: bold;
                    text-decoration: underline;
                  "
                  *ngIf="item.challanNo"
                  >RG: {{ item.challanNo }} vom {{ item.challanDate }} an
                  {{ item.customer }} <br />
                </span>

                <!-- RG:&nbsp;{{item.challanNo}} &nbsp;vom&nbsp; {{item.challanDate}} an {{item.customer}} <br> -->
                {{ item.qualityDesign }} <br />
                {{ item.qualityCode }}, {{ item.colour }} {{ item.colourCode }}
                <br />
                {{ item.size }} cm
              </td>
              <td>{{ item.area  | number : "1.2-2" }} QM</td>
              <td class="text-center">
                {{ item.evkPrice  | number : "1.2-2" }}
              </td>
              <td class="text-center">
                {{ item.amount  | number : "1.2-2" }}
              </td>
              <td>
                 <a *ngIf="item.challanNo" (click)="removeChallan(item.c)"><i class="fa fa-times" aria-hidden="true"></i></a>

              <!-- <a *ngIf="isUpdate" (click)="deleteBill(item.challanNo)"><i class="fa fa-trash-o fa-lg fa-trash"
                  aria-hidden="true"></i></a> -->
              </td>
            </tr>
            <tfoot>
              <tr>
                <td class="text-end" colspan="2">Total</td>
                <td class="text-end">Profit</td>
                <td class="text-end">Gst 19%</td>
                <td class="text-end">Discount</td>
                <td class="text-end">Gross</td>
              </tr>
              <tr>
                <td class="text-end" colspan="2">
                  {{ financialData.amount | number : "1.2-2" }}
                </td>


                <td class="text-end">
                  {{ financialData.profit | number : "1.2-2" }}
                </td>


                <td class="text-end">
                  {{ financialData.gst | number : "1.2-2" }}
                </td>


                <td class="text-end">
                  <input
                    style="width: 63px; text-align: end;border-style: none;"
                    class="inputDic"
                    placeholder="0.00"
                    #setDiscountAmt
                    type="text"
                    (blur)="setDiscount()"
                  />
                </td>


                <td class="text-end">
                  {{ financialData.grossAmount | number : "1.2-2" }}
                </td>
              </tr>
              </tfoot>
          </table>
        </div>
      </div>
    </fieldset>

    <!-- <fieldset
    style="min-height: 90px; width: 300px; float: right"
    class="mt-2"
  >
    <table style="width: 100%">
      <tr>
        <td>Total</td>
        <td class="text-end">
          {{ financialData.amount | number : "1.2-2" }}
        </td>
      </tr>
      <tr>
        <td>Profit</td>
        <td class="text-end">
          {{ financialData.profit | number : "1.2-2" }}
        </td>
      </tr>

      <tr>
        <td>Gst 19%</td>
        <td class="text-end">
          {{ financialData.gst | number : "1.2-2" }}
        </td>
      </tr>
      <tr>
        <td>Discount</td>
        <td class="text-end">
          <input
            style="width: 63px; text-align: end;border-style: none;"
            class="inputDic"
            placeholder="0.00"
            #setDiscountAmt
            type="text"
            (blur)="setDiscount()"
          />
        </td>
      </tr>


      <tr>
        <td>Gross</td>
        <td class="text-end">
          {{ financialData.grossAmount | number : "1.2-2" }}
        </td>
      </tr>
    </table>
  </fieldset> -->
  </section>
</div>
