// repositories/impoterRepository.js
const { I<PERSON><PERSON>, UpdateHistoryModel } = require("../model/phase-2/impoter");

class ImpoterRepository {
  async create(impoterData) {
    try {
      return await Impoter.create(impoterData);
    } catch (error) {
      throw error;
    }
  }

  async UpdateOmporterAddOnPrice(query, updatedData) {
    try {
      return await Impoter.updateOne(query, updatedData);
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      return await Impoter.find();
    } catch (error) {
      throw error;
    }
  }

  async findById(id) {
    try {
      return await Impoter.findById(id);
    } catch (error) {
      throw error;
    }
  }
  async getAddOnePriceById(query) {
    try {
      return await Impoter.findOne(query);
    } catch {
      throw error;
    }
  }
  async update(id, impoterData) {
    try {
      const updatedImpoter = await Impoter.findByIdAndUpdate(id, impoterData);
      return updatedImpoter;
    } catch (error) {
      throw error;
    }
  }

  async delete(id) {
    try {
      return await Impoter.findByIdAndDelete(id);
    } catch (error) {
      throw error;
    }
  }
  ////////////////////////
  ////////// here code for add on price
  /////
  async updatePrice(id, reqData) {
    try {
      let updatedData = await Impoter.updateOne(
        { _id: id },
        {
          $push: {
            addOnPrice: {
              toDate: reqData.toDate,
              fromDate: reqData.fromDate,
              quality: reqData.quality,
              design: reqData.design,
              orderPrice: reqData.orderPrice,
            },
          },
        }
      );
      if (updatedData.acknowledged == true && updatedData.modifiedCount == 1) {
        return updatedData;
      }
      return 
    } catch (error) {
      throw error;
    }
  }
}

class AddOnPriceUpdateHistoryRepository {
  async getAllAddOnPriceHistoryData(query) {
    try {
      return await UpdateHistoryModel.find(query);
    } catch (error) {
      return error;
    }
  }

  async create(body) {
    try {
      return await UpdateHistoryModel.create(body);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = { ImpoterRepository, AddOnPriceUpdateHistoryRepository };
