
.text-right{
    text-align: end;
  }
  
       .text-center {
              text-align: center!important;
          }
  
          .py-5 {
              padding-top: 3rem!important;
              padding-bottom: 3rem!important;
          }
  
          .mt-5 {
              margin-top: 3rem!important;
          }
  
          .my-5 {
              margin-top: 3rem!important;
              margin-bottom: 3rem!important;
          }
  
          table {
              width: 100%;
              height: 10px;
              text-transform: capitalize;
          }
  
          .tablecontent2 {
              border-bottom: 2px solid black;
              border-top: 2px solid black;
              padding: 15px 0px;
              text-align: center;
          }
  
          .tablecontent3 {
              border: 2px solid black;
              text-align: start;
          }
  
          .tablecontent4 {
              border-top: 2px solid black;
              margin-top: 200px;
          }
  
          .tablecontent5 th {
              padding: 7px;
              border-right: 2px solid black;
          }
  
          .paragraph5 {
              font-weight: bold;
          }
  
          .container-fluid {
              width: 100%;
              margin-right: auto;
              margin-left: auto;
          }
  
          /* @charset "UTF-8"; */
           :root,
          [data-bs-theme=light] {
              --bs-blue: #0d6efd;
              --bs-indigo: #6610f2;
              --bs-purple: #6f42c1;
              --bs-pink: #d63384;
              --bs-red: #dc3545;
              --bs-orange: #fd7e14;
              --bs-yellow: #ffc107;
              --bs-green: #198754;
              --bs-teal: #20c997;
              --bs-cyan: #0dcaf0;
              --bs-black: #000;
              --bs-white: #fff;
              --bs-gray: #6c757d;
              --bs-gray-dark: #343a40;
              --bs-gray-100: #f8f9fa;
              --bs-gray-200: #e9ecef;
              --bs-gray-300: #dee2e6;
              --bs-gray-400: #ced4da;
              --bs-gray-500: #adb5bd;
              --bs-gray-600: #6c757d;
              --bs-gray-700: #495057;
              --bs-gray-800: #343a40;
              --bs-gray-900: #212529;
              --bs-primary: #0d6efd;
              --bs-secondary: #6c757d;
              --bs-success: #198754;
              --bs-info: #0dcaf0;
              --bs-warning: #ffc107;
              --bs-danger: #dc3545;
              --bs-light: #f8f9fa;
              --bs-dark: #212529;
              --bs-primary-rgb: 13, 110, 253;
              --bs-secondary-rgb: 108, 117, 125;
              --bs-success-rgb: 25, 135, 84;
              --bs-info-rgb: 13, 202, 240;
              --bs-warning-rgb: 255, 193, 7;
              --bs-danger-rgb: 220, 53, 69;
              --bs-light-rgb: 248, 249, 250;
              --bs-dark-rgb: 33, 37, 41;
              --bs-primary-text-emphasis: #052c65;
              --bs-secondary-text-emphasis: #2b2f32;
              --bs-success-text-emphasis: #0a3622;
              --bs-info-text-emphasis: #055160;
              --bs-warning-text-emphasis: #664d03;
              --bs-danger-text-emphasis: #58151c;
              --bs-light-text-emphasis: #495057;
              --bs-dark-text-emphasis: #495057;
              --bs-primary-bg-subtle: #cfe2ff;
              --bs-secondary-bg-subtle: #e2e3e5;
              --bs-success-bg-subtle: #d1e7dd;
              --bs-info-bg-subtle: #cff4fc;
              --bs-warning-bg-subtle: #fff3cd;
              --bs-danger-bg-subtle: #f8d7da;
              --bs-light-bg-subtle: #fcfcfd;
              --bs-dark-bg-subtle: #ced4da;
              --bs-primary-border-subtle: #9ec5fe;
              --bs-secondary-border-subtle: #c4c8cb;
              --bs-success-border-subtle: #a3cfbb;
              --bs-info-border-subtle: #9eeaf9;
              --bs-warning-border-subtle: #ffe69c;
              --bs-danger-border-subtle: #f1aeb5;
              --bs-light-border-subtle: #e9ecef;
              --bs-dark-border-subtle: #adb5bd;
              --bs-white-rgb: 255, 255, 255;
              --bs-black-rgb: 0, 0, 0;
              --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
              --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
              --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
              --bs-body-font-family: var(--bs-font-sans-serif);
              --bs-body-font-size: 1rem;
              --bs-body-font-weight: 400;
              --bs-body-line-height: 1.5;
              --bs-body-color: #212529;
              --bs-body-color-rgb: 33, 37, 41;
              --bs-body-bg: #fff;
              --bs-body-bg-rgb: 255, 255, 255;
              --bs-emphasis-color: #000;
              --bs-emphasis-color-rgb: 0, 0, 0;
              --bs-secondary-color: rgba(33, 37, 41, 0.75);
              --bs-secondary-color-rgb: 33, 37, 41;
              --bs-secondary-bg: #e9ecef;
              --bs-secondary-bg-rgb: 233, 236, 239;
              --bs-tertiary-color: rgba(33, 37, 41, 0.5);
              --bs-tertiary-color-rgb: 33, 37, 41;
              --bs-tertiary-bg: #f8f9fa;
              --bs-tertiary-bg-rgb: 248, 249, 250;
              --bs-heading-color: inherit;
              --bs-link-color: #0d6efd;
              --bs-link-color-rgb: 13, 110, 253;
              --bs-link-decoration: underline;
              --bs-link-hover-color: #0a58ca;
              --bs-link-hover-color-rgb: 10, 88, 202;
              --bs-code-color: #d63384;
              --bs-highlight-color: #212529;
              --bs-highlight-bg: #fff3cd;
              --bs-border-width: 1px;
              --bs-border-style: solid;
              --bs-border-color: #dee2e6;
              --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
              --bs-border-radius: 0.375rem;
              --bs-border-radius-sm: 0.25rem;
              --bs-border-radius-lg: 0.5rem;
              --bs-border-radius-xl: 1rem;
              --bs-border-radius-xxl: 2rem;
              --bs-border-radius-2xl: var(--bs-border-radius-xxl);
              --bs-border-radius-pill: 50rem;
              --bs-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
              --bs-box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
              --bs-box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
              --bs-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075);
              --bs-focus-ring-width: 0.25rem;
              --bs-focus-ring-opacity: 0.25;
              --bs-focus-ring-color: rgba(13, 110, 253, 0.25);
              --bs-form-valid-color: #198754;
              --bs-form-valid-border-color: #198754;
              --bs-form-invalid-color: #dc3545;
              --bs-form-invalid-border-color: #dc3545;
          }
  
          [data-bs-theme=dark] {
              color-scheme: dark;
              --bs-body-color: #dee2e6;
              --bs-body-color-rgb: 222, 226, 230;
              --bs-body-bg: #212529;
              --bs-body-bg-rgb: 33, 37, 41;
              --bs-emphasis-color: #fff;
              --bs-emphasis-color-rgb: 255, 255, 255;
              --bs-secondary-color: rgba(222, 226, 230, 0.75);
              --bs-secondary-color-rgb: 222, 226, 230;
              --bs-secondary-bg: #343a40;
              --bs-secondary-bg-rgb: 52, 58, 64;
              --bs-tertiary-color: rgba(222, 226, 230, 0.5);
              --bs-tertiary-color-rgb: 222, 226, 230;
              --bs-tertiary-bg: #2b3035;
              --bs-tertiary-bg-rgb: 43, 48, 53;
              --bs-primary-text-emphasis: #6ea8fe;
              --bs-secondary-text-emphasis: #a7acb1;
              --bs-success-text-emphasis: #75b798;
              --bs-info-text-emphasis: #6edff6;
              --bs-warning-text-emphasis: #ffda6a;
              --bs-danger-text-emphasis: #ea868f;
              --bs-light-text-emphasis: #f8f9fa;
              --bs-dark-text-emphasis: #dee2e6;
              --bs-primary-bg-subtle: #031633;
              --bs-secondary-bg-subtle: #161719;
              --bs-success-bg-subtle: #051b11;
              --bs-info-bg-subtle: #032830;
              --bs-warning-bg-subtle: #332701;
              --bs-danger-bg-subtle: #2c0b0e;
              --bs-light-bg-subtle: #343a40;
              --bs-dark-bg-subtle: #1a1d20;
              --bs-primary-border-subtle: #084298;
              --bs-secondary-border-subtle: #41464b;
              --bs-success-border-subtle: #0f5132;
              --bs-info-border-subtle: #087990;
              --bs-warning-border-subtle: #997404;
              --bs-danger-border-subtle: #842029;
              --bs-light-border-subtle: #495057;
              --bs-dark-border-subtle: #343a40;
              --bs-heading-color: inherit;
              --bs-link-color: #6ea8fe;
              --bs-link-hover-color: #8bb9fe;
              --bs-link-color-rgb: 110, 168, 254;
              --bs-link-hover-color-rgb: 139, 185, 254;
              --bs-code-color: #e685b5;
              --bs-highlight-color: #dee2e6;
              --bs-highlight-bg: #664d03;
              --bs-border-color: #495057;
              --bs-border-color-translucent: rgba(255, 255, 255, 0.15);
              --bs-form-valid-color: #75b798;
              --bs-form-valid-border-color: #75b798;
              --bs-form-invalid-color: #ea868f;
              --bs-form-invalid-border-color: #ea868f;
          }
  
          *,
          *::before,
          *::after {
              box-sizing: border-box;
          }
  
          @media (prefers-reduced-motion: no-preference) {
               :root {
                  scroll-behavior: smooth;
              }
          }
  
          body {
              margin: 0;
              font-family: var(--bs-body-font-family);
              font-size: var(--bs-body-font-size);
              font-weight: var(--bs-body-font-weight);
              line-height: var(--bs-body-line-height);
              color: var(--bs-body-color);
              text-align: var(--bs-body-text-align);
              background-color: var(--bs-body-bg);
              -webkit-text-size-adjust: 100%;
              -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
          }
  
          hr {
              margin: 1rem 0;
              color: inherit;
              border: 0;
              border-top: var(--bs-border-width) solid;
              opacity: 0.25;
          }
  
          h6,
          .h6,
          h5,
          .h5,
          h4,
          .h4,
          h3,
          .h3,
          h2,
          .h2,
          h1,
          .h1 {
              margin-top: 0;
              margin-bottom: 0.5rem;
              font-weight: 500;
              line-height: 1.2;
              color: var(--bs-heading-color);
          }
  
          h1,
          .h1 {
              font-size: calc(1.375rem + 1.5vw);
          }
  
          @media (min-width: 1200px) {
              h1,
              .h1 {
                  font-size: 2.5rem;
              }
          }
  
          h2,
          .h2 {
              font-size: calc(1.325rem + 0.9vw);
          }
  
          @media (min-width: 1200px) {
              h2,
              .h2 {
                  font-size: 2rem;
              }
          }
  
          h3,
          .h3 {
              font-size: calc(1.3rem + 0.6vw);
          }
  
          @media (min-width: 1200px) {
              h3,
              .h3 {
                  font-size: 1.75rem;
              }
          }
  
          h4,
          .h4 {
              font-size: calc(1.275rem + 0.3vw);
          }
  
          @media (min-width: 1200px) {
              h4,
              .h4 {
                  font-size: 1.5rem;
              }
          }
  
          h5,
          .h5 {
              font-size: 1.25rem;
          }
  
          h6,
          .h6 {
              font-size: 1rem;
          }
  
          p {
              margin-top: 0;
              margin-bottom: 1rem;
          }
  
          abbr[title] {
              -webkit-text-decoration: underline dotted;
              text-decoration: underline dotted;
              cursor: help;
              -webkit-text-decoration-skip-ink: none;
              text-decoration-skip-ink: none;
          }
  
          address {
              margin-bottom: 1rem;
              font-style: normal;
              line-height: inherit;
          }
  
          ol,
          ul {
              padding-left: 2rem;
          }
  
          ol,
          ul,
          dl {
              margin-top: 0;
              margin-bottom: 1rem;
          }
  
          ol ol,
          ul ul,
          ol ul,
          ul ol {
              margin-bottom: 0;
          }
  
          dt {
              font-weight: 700;
          }
  
          dd {
              margin-bottom: 0.5rem;
              margin-left: 0;
          }
  
          blockquote {
              margin: 0 0 1rem;
          }
  
          b,
          strong {
              font-weight: bolder;
          }
  
          small,
          .small {
              font-size: 0.875em;
          }
  
          mark,
          .mark {
              padding: 0.1875em;
              color: var(--bs-highlight-color);
              background-color: var(--bs-highlight-bg);
          }
  
          sub,
          sup {
              position: relative;
              font-size: 0.75em;
              line-height: 0;
              vertical-align: baseline;
          }
  
          sub {
              bottom: -0.25em;
          }
  
          sup {
              top: -0.5em;
          }
  
          a {
              color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));
              text-decoration: underline;
          }
  
          a:hover {
              --bs-link-color-rgb: var(--bs-link-hover-color-rgb);
          }
  
          a:not([href]):not([class]),
          a:not([href]):not([class]):hover {
              color: inherit;
              text-decoration: none;
          }
  
          pre,
          code,
          kbd,
          samp {
              font-family: var(--bs-font-monospace);
              font-size: 1em;
          }
  
          pre {
              display: block;
              margin-top: 0;
              margin-bottom: 1rem;
              overflow: auto;
              font-size: 0.875em;
          }
  
          pre code {
              font-size: inherit;
              color: inherit;
              word-break: normal;
          }
  
          code {
              font-size: 0.875em;
              color: var(--bs-code-color);
              word-wrap: break-word;
          }
  
          a>code {
              color: inherit;
          }
  
          kbd {
              padding: 0.1875rem 0.375rem;
              font-size: 0.875em;
              color: var(--bs-body-bg);
              background-color: var(--bs-body-color);
              border-radius: 0.25rem;
          }
  
          kbd kbd {
              padding: 0;
              font-size: 1em;
          }
  
          figure {
              margin: 0 0 1rem;
          }
  
          img,
          svg {
              vertical-align: middle;
          }
  
          table {
              caption-side: bottom;
              border-collapse: collapse;
          }
  
          caption {
              padding-top: 0.5rem;
              padding-bottom: 0.5rem;
              color: var(--bs-secondary-color);
              text-align: left;
          }
  
          th {
              text-align: inherit;
              text-align: -webkit-match-parent;
          }
  
          thead,
          tbody,
          tfoot,
          tr,
          td,
          th {
              border-color: inherit;
              border-style: solid;
              border-width: 0;
          }
  
          label {
              display: inline-block;
          }
  
          button {
              border-radius: 0;
          }
  
          button:focus:not(:focus-visible) {
              outline: 0;
          }
  
          input,
          button,
          select,
          optgroup,
          textarea {
              margin: 0;
              font-family: inherit;
              font-size: inherit;
              line-height: inherit;
          }
  
          button,
          select {
              text-transform: none;
          }
  
          [role=button] {
              cursor: pointer;
          }
  
          select {
              word-wrap: normal;
          }
  
          select:disabled {
              opacity: 1;
          }
  
          [list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {
              display: none !important;
          }
  
          button,
          [type=button],
          [type=reset],
          [type=submit] {
              -webkit-appearance: button;
          }
  
          button:not(:disabled),
          [type=button]:not(:disabled),
          [type=reset]:not(:disabled),
          [type=submit]:not(:disabled) {
              cursor: pointer;
          }
  
           ::-moz-focus-inner {
              padding: 0;
              border-style: none;
          }
  
          textarea {
              resize: vertical;
          }
  
          fieldset {
              min-width: 0;
              padding: 0;
              margin: 0;
              border: 0;
          }
  
          legend {
              float: left;
              width: 100%;
              padding: 0;
              margin-bottom: 0.5rem;
              font-size: calc(1.275rem + 0.3vw);
              line-height: inherit;
          }
  
          @media (min-width: 1200px) {
              legend {
                  font-size: 1.5rem;
              }
          }
  
          legend+* {
              clear: left;
          }
  
           ::-webkit-datetime-edit-fields-wrapper,
           ::-webkit-datetime-edit-text,
           ::-webkit-datetime-edit-minute,
           ::-webkit-datetime-edit-hour-field,
           ::-webkit-datetime-edit-day-field,
           ::-webkit-datetime-edit-month-field,
           ::-webkit-datetime-edit-year-field {
              padding: 0;
          }
  
           ::-webkit-inner-spin-button {
              height: auto;
          }
  
          [type=search] {
              -webkit-appearance: textfield;
              outline-offset: -2px;
          }
          /* rtl:raw:
      [type="tel"],
      [type="url"],
      [type="email"],
      [type="number"] {
        direction: ltr;
      }
      */
  
           ::-webkit-search-decoration {
              -webkit-appearance: none;
          }
  
           ::-webkit-color-swatch-wrapper {
              padding: 0;
          }
  
           ::-webkit-file-upload-button {
              font: inherit;
              -webkit-appearance: button;
          }
  
           ::file-selector-button {
              font: inherit;
              -webkit-appearance: button;
          }
  
          output {
              display: inline-block;
          }
  
          iframe {
              border: 0;
          }
  
          summary {
              display: list-item;
              cursor: pointer;
          }
  
          progress {
              vertical-align: baseline;
          }
  
          [hidden] {
              display: none !important;
          }
  
          .lead {
              font-size: 1.25rem;
              font-weight: 300;
          }
  
          .display-1 {
              font-size: calc(1.625rem + 4.5vw);
              font-weight: 300;
              line-height: 1.2;
          }
  
          @media (min-width: 1200px) {
              .display-1 {
                  font-size: 5rem;
              }
          }
  
          .display-2 {
              font-size: calc(1.575rem + 3.9vw);
              font-weight: 300;
              line-height: 1.2;
          }
  
          @media (min-width: 1200px) {
              .display-2 {
                  font-size: 4.5rem;
              }
          }
  
          .display-3 {
              font-size: calc(1.525rem + 3.3vw);
              font-weight: 300;
              line-height: 1.2;
          }
  
          @media (min-width: 1200px) {
              .display-3 {
                  font-size: 4rem;
              }
          }
  
          .display-4 {
              font-size: calc(1.475rem + 2.7vw);
              font-weight: 300;
              line-height: 1.2;
          }
  
          @media (min-width: 1200px) {
              .display-4 {
                  font-size: 3.5rem;
              }
          }
  
          .display-5 {
              font-size: calc(1.425rem + 2.1vw);
              font-weight: 300;
              line-height: 1.2;
          }
  
          @media (min-width: 1200px) {
              .display-5 {
                  font-size: 3rem;
              }
          }
  
          .display-6 {
              font-size: calc(1.375rem + 1.5vw);
              font-weight: 300;
              line-height: 1.2;
          }
  
          @media (min-width: 1200px) {
              .display-6 {
                  font-size: 2.5rem;
              }
          }
  
          .list-unstyled {
              padding-left: 0;
              list-style: none;
          }
  
          .list-inline {
              padding-left: 0;
              list-style: none;
          }
  
          .list-inline-item {
              display: inline-block;
          }
  
          .list-inline-item:not(:last-child) {
              margin-right: 0.5rem;
          }
  
          .initialism {
              font-size: 0.875em;
              text-transform: uppercase;
          }
  
          .blockquote {
              margin-bottom: 1rem;
              font-size: 1.25rem;
          }
  
          .blockquote> :last-child {
              margin-bottom: 0;
          }
  
          .blockquote-footer {
              margin-top: -1rem;
              margin-bottom: 1rem;
              font-size: 0.875em;
              color: #6c757d;
          }
  
          .blockquote-footer::before {
              content: "— ";
          }
  
          .img-fluid {
              max-width: 100%;
              height: auto;
          }
  
          .img-thumbnail {
              padding: 0.25rem;
              background-color: var(--bs-body-bg);
              border: var(--bs-border-width) solid var(--bs-border-color);
              border-radius: var(--bs-border-radius);
              max-width: 100%;
              height: auto;
          }
  
          .figure {
              display: inline-block;
          }
  
          .figure-img {
              margin-bottom: 0.5rem;
              line-height: 1;
          }
  
          .figure-caption {
              font-size: 0.875em;
              color: var(--bs-secondary-color);
          }
  
          .container,
          .container-fluid,
          .container-xxl,
          .container-xl,
          .container-lg,
          .container-md,
          .container-sm {
              --bs-gutter-x: 1.5rem;
              --bs-gutter-y: 0;
              width: 100%;
              padding-right: calc(var(--bs-gutter-x) * 0.5);
              padding-left: calc(var(--bs-gutter-x) * 0.5);
              margin-right: auto;
              margin-left: auto;
          }
  
          @media (min-width: 576px) {
              .container-sm,
              .container {
                  max-width: 540px;
              }
          }
  
          @media (min-width: 768px) {
              .container-md,
              .container-sm,
              .container {
                  max-width: 720px;
              }
          }
  
          @media (min-width: 992px) {
              .container-lg,
              .container-md,
              .container-sm,
              .container {
                  max-width: 960px;
              }
          }
  
          @media (min-width: 1200px) {
              .container-xl,
              .container-lg,
              .container-md,
              .container-sm,
              .container {
                  max-width: 1140px;
              }
          }
  
          @media (min-width: 1400px) {
              .container-xxl,
              .container-xl,
              .container-lg,
              .container-md,
              .container-sm,
              .container {
                  max-width: 1320px;
              }
          }
  
           :root {
              --bs-breakpoint-xs: 0;
              --bs-breakpoint-sm: 576px;
              --bs-breakpoint-md: 768px;
              --bs-breakpoint-lg: 992px;
              --bs-breakpoint-xl: 1200px;
              --bs-breakpoint-xxl: 1400px;
          }
  
          .row {
              --bs-gutter-x: 1.5rem;
              --bs-gutter-y: 0;
              display: flex;
              flex-wrap: wrap;
              margin-top: calc(-1 * var(--bs-gutter-y));
              margin-right: calc(-0.5 * var(--bs-gutter-x));
              margin-left: calc(-0.5 * var(--bs-gutter-x));
          }
  
          .row>* {
              flex-shrink: 0;
              width: 100%;
              max-width: 100%;
              padding-right: calc(var(--bs-gutter-x) * 0.5);
              padding-left: calc(var(--bs-gutter-x) * 0.5);
              margin-top: var(--bs-gutter-y);
          }
  
          .col {
              flex: 1 0 0%;
          }
  
          .row-cols-auto>* {
              flex: 0 0 auto;
              width: auto;
          }
  
          .row-cols-1>* {
              flex: 0 0 auto;
              width: 100%;
          }
  
          .row-cols-2>* {
              flex: 0 0 auto;
              width: 50%;
          }
  
          .row-cols-3>* {
              flex: 0 0 auto;
              width: 33.33333333%;
          }
  
          .row-cols-4>* {
              flex: 0 0 auto;
              width: 25%;
          }
  
          .row-cols-5>* {
              flex: 0 0 auto;
              width: 20%;
          }
  
          .row-cols-6>* {
              flex: 0 0 auto;
              width: 16.66666667%;
          }
  
          .col-auto {
              flex: 0 0 auto;
              width: auto;
          }
  
          .col-1 {
              flex: 0 0 auto;
              width: 8.33333333%;
          }
  
          .col-2 {
              flex: 0 0 auto;
              width: 16.66666667%;
          }
  
          .col-3 {
              flex: 0 0 auto;
              width: 25%;
          }
  
          .col-4 {
              flex: 0 0 auto;
              width: 33.33333333%;
          }
  
          .col-5 {
              flex: 0 0 auto;
              width: 41.66666667%;
          }
  
          .col-6 {
              flex: 0 0 auto;
              width: 50%;
          }
  
          .col-7 {
              flex: 0 0 auto;
              width: 58.33333333%;
          }
  
          .col-8 {
              flex: 0 0 auto;
              width: 66.66666667%;
          }
  
          .col-9 {
              flex: 0 0 auto;
              width: 75%;
          }
  
          .col-10 {
              flex: 0 0 auto;
              width: 83.33333333%;
          }
  
          .col-11 {
              flex: 0 0 auto;
              width: 91.66666667%;
          }
  
          .col-12 {
              flex: 0 0 auto;
              width: 100%;
          }
  
          .offset-1 {
              margin-left: 8.33333333%;
          }
  
          .offset-2 {
              margin-left: 16.66666667%;
          }
  
          .offset-3 {
              margin-left: 25%;
          }
  
          .offset-4 {
              margin-left: 33.33333333%;
          }
  
          .offset-5 {
              margin-left: 41.66666667%;
          }
  
          .offset-6 {
              margin-left: 50%;
          }
  
          .offset-7 {
              margin-left: 58.33333333%;
          }
  
          .offset-8 {
              margin-left: 66.66666667%;
          }
  
          .offset-9 {
              margin-left: 75%;
          }
  
          .offset-10 {
              margin-left: 83.33333333%;
          }
  
          .offset-11 {
              margin-left: 91.66666667%;
          }
  
          .g-0,
          .gx-0 {
              --bs-gutter-x: 0;
          }
  
          .g-0,
          .gy-0 {
              --bs-gutter-y: 0;
          }
  
          .g-1,
          .gx-1 {
              --bs-gutter-x: 0.25rem;
          }
  
          .g-1,
          .gy-1 {
              --bs-gutter-y: 0.25rem;
          }
  
          .g-2,
          .gx-2 {
              --bs-gutter-x: 0.5rem;
          }
  
          .g-2,
          .gy-2 {
              --bs-gutter-y: 0.5rem;
          }
  
          .g-3,
          .gx-3 {
              --bs-gutter-x: 1rem;
          }
  
          .g-3,
          .gy-3 {
              --bs-gutter-y: 1rem;
          }
  
          .g-4,
          .gx-4 {
              --bs-gutter-x: 1.5rem;
          }
  
          .g-4,
          .gy-4 {
              --bs-gutter-y: 1.5rem;
          }
  
          .g-5,
          .gx-5 {
              --bs-gutter-x: 3rem;
          }
  
          .g-5,
          .gy-5 {
              --bs-gutter-y: 3rem;
          }
  
          .table {
              --bs-table-color-type: initial;
              --bs-table-bg-type: initial;
              --bs-table-color-state: initial;
              --bs-table-bg-state: initial;
              --bs-table-color: var(--bs-emphasis-color);
              --bs-table-bg: var(--bs-body-bg);
              --bs-table-border-color: var(--bs-border-color);
              --bs-table-accent-bg: transparent;
              --bs-table-striped-color: var(--bs-emphasis-color);
              --bs-table-striped-bg: rgba(var(--bs-emphasis-color-rgb), 0.05);
              --bs-table-active-color: var(--bs-emphasis-color);
              --bs-table-active-bg: rgba(var(--bs-emphasis-color-rgb), 0.1);
              --bs-table-hover-color: var(--bs-emphasis-color);
              --bs-table-hover-bg: rgba(var(--bs-emphasis-color-rgb), 0.075);
              width: 100%;
              margin-bottom: 1rem;
              vertical-align: top;
              border-color: var(--bs-table-border-color);
          }
  
          .table> :not(caption)>*>* {
              padding: 0.5rem 0.5rem;
              color: var(--bs-table-color-state, var(--bs-table-color-type, var(--bs-table-color)));
              background-color: var(--bs-table-bg);
              border-bottom-width: var(--bs-border-width);
              box-shadow: inset 0 0 0 9999px var(--bs-table-bg-state, var(--bs-table-bg-type, var(--bs-table-accent-bg)));
          }
  
          .table>tbody {
              vertical-align: inherit;
          }
  
          .table>thead {
              vertical-align: bottom;
          }
  
          .table-group-divider {
              border-top: calc(var(--bs-border-width) * 2) solid currentcolor;
          }
  
          .caption-top {
              caption-side: top;
          }
  
          .table-sm> :not(caption)>*>* {
              padding: 0.25rem 0.25rem;
          }
  
          .table-bordered> :not(caption)>* {
              border-width: var(--bs-border-width) 0;
          }
  
          .table-bordered> :not(caption)>*>* {
              border-width: 0 var(--bs-border-width);
          }
  
          .table-borderless> :not(caption)>*>* {
              border-bottom-width: 0;
          }
  
          .table-borderless> :not(:first-child) {
              border-top-width: 0;
          }
  
          .table-striped>tbody>tr:nth-of-type(odd)>* {
              --bs-table-color-type: var(--bs-table-striped-color);
              --bs-table-bg-type: var(--bs-table-striped-bg);
          }
  
          .table-striped-columns> :not(caption)>tr> :nth-child(even) {
              --bs-table-color-type: var(--bs-table-striped-color);
              --bs-table-bg-type: var(--bs-table-striped-bg);
          }
  
          .table-active {
              --bs-table-color-state: var(--bs-table-active-color);
              --bs-table-bg-state: var(--bs-table-active-bg);
          }
  
          .table-hover>tbody>tr:hover>* {
              --bs-table-color-state: var(--bs-table-hover-color);
              --bs-table-bg-state: var(--bs-table-hover-bg);
          }
  
          .table-primary {
              --bs-table-color: #000;
              --bs-table-bg: #cfe2ff;
              --bs-table-border-color: #a6b5cc;
              --bs-table-striped-bg: #c5d7f2;
              --bs-table-striped-color: #000;
              --bs-table-active-bg: #bacbe6;
              --bs-table-active-color: #000;
              --bs-table-hover-bg: #bfd1ec;
              --bs-table-hover-color: #000;
              color: var(--bs-table-color);
              border-color: var(--bs-table-border-color);
          }
  
          .table-secondary {
              --bs-table-color: #000;
              --bs-table-bg: #e2e3e5;
              --bs-table-border-color: #b5b6b7;
              --bs-table-striped-bg: #d7d8da;
              --bs-table-striped-color: #000;
              --bs-table-active-bg: #cbccce;
              --bs-table-active-color: #000;
              --bs-table-hover-bg: #d1d2d4;
              --bs-table-hover-color: #000;
              color: var(--bs-table-color);
              border-color: var(--bs-table-border-color);
          }
  
          .table-success {
              --bs-table-color: #000;
              --bs-table-bg: #d1e7dd;
              --bs-table-border-color: #a7b9b1;
              --bs-table-striped-bg: #c7dbd2;
              --bs-table-striped-color: #000;
              --bs-table-active-bg: #bcd0c7;
              --bs-table-active-color: #000;
              --bs-table-hover-bg: #c1d6cc;
              --bs-table-hover-color: #000;
              color: var(--bs-table-color);
              border-color: var(--bs-table-border-color);
          }
  
          .table-info {
              --bs-table-color: #000;
              --bs-table-bg: #cff4fc;
              --bs-table-border-color: #a6c3ca;
              --bs-table-striped-bg: #c5e8ef;
              --bs-table-striped-color: #000;
              --bs-table-active-bg: #badce3;
              --bs-table-active-color: #000;
              --bs-table-hover-bg: #bfe2e9;
              --bs-table-hover-color: #000;
              color: var(--bs-table-color);
              border-color: var(--bs-table-border-color);
          }
  
          .table-warning {
              --bs-table-color: #000;
              --bs-table-bg: #fff3cd;
              --bs-table-border-color: #ccc2a4;
              --bs-table-striped-bg: #f2e7c3;
              --bs-table-striped-color: #000;
              --bs-table-active-bg: #e6dbb9;
              --bs-table-active-color: #000;
              --bs-table-hover-bg: #ece1be;
              --bs-table-hover-color: #000;
              color: var(--bs-table-color);
              border-color: var(--bs-table-border-color);
          }
  
          .table-danger {
              --bs-table-color: #000;
              --bs-table-bg: #f8d7da;
              --bs-table-border-color: #c6acae;
              --bs-table-striped-bg: #eccccf;
              --bs-table-striped-color: #000;
              --bs-table-active-bg: #dfc2c4;
              --bs-table-active-color: #000;
              --bs-table-hover-bg: #e5c7ca;
              --bs-table-hover-color: #000;
              color: var(--bs-table-color);
              border-color: var(--bs-table-border-color);
          }
  
          .table-light {
              --bs-table-color: #000;
              --bs-table-bg: #f8f9fa;
              --bs-table-border-color: #c6c7c8;
              --bs-table-striped-bg: #ecedee;
              --bs-table-striped-color: #000;
              --bs-table-active-bg: #dfe0e1;
              --bs-table-active-color: #000;
              --bs-table-hover-bg: #e5e6e7;
              --bs-table-hover-color: #000;
              color: var(--bs-table-color);
              border-color: var(--bs-table-border-color);
          }
  
          .table-dark {
              --bs-table-color: #fff;
              --bs-table-bg: #212529;
              --bs-table-border-color: #4d5154;
              --bs-table-striped-bg: #2c3034;
              --bs-table-striped-color: #fff;
              --bs-table-active-bg: #373b3e;
              --bs-table-active-color: #fff;
              --bs-table-hover-bg: #323539;
              --bs-table-hover-color: #fff;
              color: var(--bs-table-color);
              border-color: var(--bs-table-border-color);
          }
  
          .table-responsive {
              overflow-x: auto;
              -webkit-overflow-scrolling: touch;
          }
  
          @media (max-width: 575.98px) {
              .table-responsive-sm {
                  overflow-x: auto;
                  -webkit-overflow-scrolling: touch;
              }
          }
  
          @media (max-width: 767.98px) {
              .table-responsive-md {
                  overflow-x: auto;
                  -webkit-overflow-scrolling: touch;
              }
          }
  
          @media (max-width: 991.98px) {
              .table-responsive-lg {
                  overflow-x: auto;
                  -webkit-overflow-scrolling: touch;
              }
          }
  
          @media (max-width: 1199.98px) {
              .table-responsive-xl {
                  overflow-x: auto;
                  -webkit-overflow-scrolling: touch;
              }
          }
  
          @media (max-width: 1399.98px) {
              .table-responsive-xxl {
                  overflow-x: auto;
                  -webkit-overflow-scrolling: touch;
              }
          }
  
          .focus-ring:focus {
              outline: 0;
              box-shadow: var(--bs-focus-ring-x, 0) var(--bs-focus-ring-y, 0) var(--bs-focus-ring-blur, 0) var(--bs-focus-ring-width) var(--bs-focus-ring-color);
          }
  
          .icon-link {
              display: inline-flex;
              gap: 0.375rem;
              align-items: center;
              -webkit-text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 0.5));
              text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 0.5));
              text-underline-offset: 0.25em;
              -webkit-backface-visibility: hidden;
              backface-visibility: hidden;
          }
  
          .icon-link>.bi {
              flex-shrink: 0;
              width: 1em;
              height: 1em;
              fill: currentcolor;
              transition: 0.2s ease-in-out transform;
          }
  
          @media (prefers-reduced-motion: reduce) {
              .icon-link>.bi {
                  transition: none;
              }
          }
  
          .icon-link-hover:hover>.bi,
          .icon-link-hover:focus-visible>.bi {
              transform: var(--bs-icon-link-transform, translate3d(0.25em, 0, 0));
          }
  
          .ratio {
              position: relative;
              width: 100%;
          }
  
          .ratio::before {
              display: block;
              padding-top: var(--bs-aspect-ratio);
              content: "";
          }
  
          .ratio>* {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
          }
  
          .ratio-1x1 {
              --bs-aspect-ratio: 100%;
          }
  
          .ratio-4x3 {
              --bs-aspect-ratio: 75%;
          }
  
          .ratio-16x9 {
              --bs-aspect-ratio: 56.25%;
          }
  
          .ratio-21x9 {
              --bs-aspect-ratio: 42.8571428571%;
          }
  
          .fixed-top {
              position: fixed;
              top: 0;
              right: 0;
              left: 0;
              z-index: 1030;
          }
  
          .fixed-bottom {
              position: fixed;
              right: 0;
              bottom: 0;
              left: 0;
              z-index: 1030;
          }
  
          .sticky-top {
              position: -webkit-sticky;
              position: sticky;
              top: 0;
              z-index: 1020;
          }
  
          .sticky-bottom {
              position: -webkit-sticky;
              position: sticky;
              bottom: 0;
              z-index: 1020;
          }
  
          @media (min-width: 576px) {
              .sticky-sm-top {
                  position: -webkit-sticky;
                  position: sticky;
                  top: 0;
                  z-index: 1020;
              }
              .sticky-sm-bottom {
                  position: -webkit-sticky;
                  position: sticky;
                  bottom: 0;
                  z-index: 1020;
              }
          }
  
          @media (min-width: 768px) {
              .sticky-md-top {
                  position: -webkit-sticky;
                  position: sticky;
                  top: 0;
                  z-index: 1020;
              }
              .sticky-md-bottom {
                  position: -webkit-sticky;
                  position: sticky;
                  bottom: 0;
                  z-index: 1020;
              }
          }
  
          @media (min-width: 992px) {
              .sticky-lg-top {
                  position: -webkit-sticky;
                  position: sticky;
                  top: 0;
                  z-index: 1020;
              }
              .sticky-lg-bottom {
                  position: -webkit-sticky;
                  position: sticky;
                  bottom: 0;
                  z-index: 1020;
              }
          }
  
          @media (min-width: 1200px) {
              .sticky-xl-top {
                  position: -webkit-sticky;
                  position: sticky;
                  top: 0;
                  z-index: 1020;
              }
              .sticky-xl-bottom {
                  position: -webkit-sticky;
                  position: sticky;
                  bottom: 0;
                  z-index: 1020;
              }
          }
  
          @media (min-width: 1400px) {
              .sticky-xxl-top {
                  position: -webkit-sticky;
                  position: sticky;
                  top: 0;
                  z-index: 1020;
              }
              .sticky-xxl-bottom {
                  position: -webkit-sticky;
                  position: sticky;
                  bottom: 0;
                  z-index: 1020;
              }
          }
  
          .hstack {
              display: flex;
              flex-direction: row;
              align-items: center;
              align-self: stretch;
          }
  
          .vstack {
              display: flex;
              flex: 1 1 auto;
              flex-direction: column;
              align-self: stretch;
          }
  
          .visually-hidden,
          .visually-hidden-focusable:not(:focus):not(:focus-within) {
              width: 1px !important;
              height: 1px !important;
              padding: 0 !important;
              margin: -1px !important;
              overflow: hidden !important;
              clip: rect(0, 0, 0, 0) !important;
              white-space: nowrap !important;
              border: 0 !important;
          }
  
          .visually-hidden:not(caption),
          .visually-hidden-focusable:not(:focus):not(:focus-within):not(caption) {
              position: absolute !important;
          }
  
          .stretched-link::after {
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              left: 0;
              z-index: 1;
              content: "";
          }
  
          .text-truncate {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
          }
  
          .vr {
              display: inline-block;
              align-self: stretch;
              width: var(--bs-border-width);
              min-height: 1em;
              background-color: currentcolor;
              opacity: 0.25;
          }
  
          .align-baseline {
              vertical-align: baseline !important;
          }
  
          .align-top {
              vertical-align: top !important;
          }
  
          .align-middle {
              vertical-align: middle !important;
          }
  
          .align-bottom {
              vertical-align: bottom !important;
          }
  
          .align-text-bottom {
              vertical-align: text-bottom !important;
          }
  
          .align-text-top {
              vertical-align: text-top !important;
          }
  
          .float-start {
              float: left !important;
          }
  
          .float-end {
              float: right !important;
          }
  
          .float-none {
              float: none !important;
          }
  
          .object-fit-contain {
              -o-object-fit: contain !important;
              object-fit: contain !important;
          }
  
          .object-fit-cover {
              -o-object-fit: cover !important;
              object-fit: cover !important;
          }
  
          .object-fit-fill {
              -o-object-fit: fill !important;
              object-fit: fill !important;
          }
  
          .object-fit-scale {
              -o-object-fit: scale-down !important;
              object-fit: scale-down !important;
          }
  
          .object-fit-none {
              -o-object-fit: none !important;
              object-fit: none !important;
          }
  
          .opacity-0 {
              opacity: 0 !important;
          }
  
          .opacity-25 {
              opacity: 0.25 !important;
          }
  
          .opacity-50 {
              opacity: 0.5 !important;
          }
  
          .opacity-75 {
              opacity: 0.75 !important;
          }
  
          .opacity-100 {
              opacity: 1 !important;
          }
  
          .overflow-auto {
              overflow: auto !important;
          }
  
          .overflow-hidden {
              overflow: hidden !important;
          }
  
          .overflow-visible {
              overflow: visible !important;
          }
  
          .overflow-scroll {
              overflow: scroll !important;
          }
  
          .overflow-x-auto {
              overflow-x: auto !important;
          }
  
          .overflow-x-hidden {
              overflow-x: hidden !important;
          }
  
          .overflow-x-visible {
              overflow-x: visible !important;
          }
  
          .overflow-x-scroll {
              overflow-x: scroll !important;
          }
  
          .overflow-y-auto {
              overflow-y: auto !important;
          }
  
          .overflow-y-hidden {
              overflow-y: hidden !important;
          }
  
          .overflow-y-visible {
              overflow-y: visible !important;
          }
  
          .overflow-y-scroll {
              overflow-y: scroll !important;
          }
  
          .d-inline {
              display: inline !important;
          }
  
          .d-inline-block {
              display: inline-block !important;
          }
  
          .d-block {
              display: block !important;
          }
  
          .d-grid {
              display: grid !important;
          }
  
          .d-inline-grid {
              display: inline-grid !important;
          }
  
          .d-table {
              display: table !important;
          }
  
          .d-table-row {
              display: table-row !important;
          }
  
          .d-table-cell {
              display: table-cell !important;
          }
  
          .d-flex {
              display: flex !important;
          }
  
          .d-inline-flex {
              display: inline-flex !important;
          }
  
          .d-none {
              display: none !important;
          }
  
          .shadow {
              box-shadow: var(--bs-box-shadow) !important;
          }
  
          .shadow-sm {
              box-shadow: var(--bs-box-shadow-sm) !important;
          }
  
          .shadow-lg {
              box-shadow: var(--bs-box-shadow-lg) !important;
          }
  
          .shadow-none {
              box-shadow: none !important;
          }
  
          .focus-ring-primary {
              --bs-focus-ring-color: rgba(var(--bs-primary-rgb), var(--bs-focus-ring-opacity));
          }
  
          .focus-ring-secondary {
              --bs-focus-ring-color: rgba(var(--bs-secondary-rgb), var(--bs-focus-ring-opacity));
          }
  
          .focus-ring-success {
              --bs-focus-ring-color: rgba(var(--bs-success-rgb), var(--bs-focus-ring-opacity));
          }
  
          .focus-ring-info {
              --bs-focus-ring-color: rgba(var(--bs-info-rgb), var(--bs-focus-ring-opacity));
          }
  
          .focus-ring-warning {
              --bs-focus-ring-color: rgba(var(--bs-warning-rgb), var(--bs-focus-ring-opacity));
          }
  
          .focus-ring-danger {
              --bs-focus-ring-color: rgba(var(--bs-danger-rgb), var(--bs-focus-ring-opacity));
          }
  
          .focus-ring-light {
              --bs-focus-ring-color: rgba(var(--bs-light-rgb), var(--bs-focus-ring-opacity));
          }
  
          .focus-ring-dark {
              --bs-focus-ring-color: rgba(var(--bs-dark-rgb), var(--bs-focus-ring-opacity));
          }
  
          .position-static {
              position: static !important;
          }
  
          .position-relative {
              position: relative !important;
          }
  
          .position-absolute {
              position: absolute !important;
          }
  
          .position-fixed {
              position: fixed !important;
          }
  
          .position-sticky {
              position: -webkit-sticky !important;
              position: sticky !important;
          }
  
          .top-0 {
              top: 0 !important;
          }
  
          .top-50 {
              top: 50% !important;
          }
  
          .top-100 {
              top: 100% !important;
          }
  
          .bottom-0 {
              bottom: 0 !important;
          }
  
          .bottom-50 {
              bottom: 50% !important;
          }
  
          .bottom-100 {
              bottom: 100% !important;
          }
  
          .start-0 {
              left: 0 !important;
          }
  
          .start-50 {
              left: 50% !important;
          }
  
          .start-100 {
              left: 100% !important;
          }
  
          .end-0 {
              right: 0 !important;
          }
  
          .end-50 {
              right: 50% !important;
          }
  
          .end-100 {
              right: 100% !important;
          }
  
          .translate-middle {
              transform: translate(-50%, -50%) !important;
          }
  
          .translate-middle-x {
              transform: translateX(-50%) !important;
          }
  
          .translate-middle-y {
              transform: translateY(-50%) !important;
          }
  
          .border {
              border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
          }
  
          .border-0 {
              border: 0 !important;
          }
  
          .border-top {
              border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
          }
  
          .border-top-0 {
              border-top: 0 !important;
          }
  
          .border-end {
              border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
          }
  
          .border-end-0 {
              border-right: 0 !important;
          }
  
          .border-bottom {
              border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
          }
  
          .border-bottom-0 {
              border-bottom: 0 !important;
          }
  
          .border-start {
              border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
          }
  
          .border-start-0 {
              border-left: 0 !important;
          }
  
          .border-primary {
              --bs-border-opacity: 1;
              border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;
          }
  
          .border-secondary {
              --bs-border-opacity: 1;
              border-color: rgba(var(--bs-secondary-rgb), var(--bs-border-opacity)) !important;
          }
  
          .border-success {
              --bs-border-opacity: 1;
              border-color: rgba(var(--bs-success-rgb), var(--bs-border-opacity)) !important;
          }
  
          .border-info {
              --bs-border-opacity: 1;
              border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;
          }
  
          .border-warning {
              --bs-border-opacity: 1;
              border-color: rgba(var(--bs-warning-rgb), var(--bs-border-opacity)) !important;
          }
  
          .border-danger {
              --bs-border-opacity: 1;
              border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important;
          }
  
          .border-light {
              --bs-border-opacity: 1;
              border-color: rgba(var(--bs-light-rgb), var(--bs-border-opacity)) !important;
          }
  
          .border-dark {
              --bs-border-opacity: 1;
              border-color: rgba(var(--bs-dark-rgb), var(--bs-border-opacity)) !important;
          }
  
          .border-black {
              --bs-border-opacity: 1;
              border-color: rgba(var(--bs-black-rgb), var(--bs-border-opacity)) !important;
          }
  
          .border-white {
              --bs-border-opacity: 1;
              border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;
          }
  
          .border-primary-subtle {
              border-color: var(--bs-primary-border-subtle) !important;
          }
  
          .border-secondary-subtle {
              border-color: var(--bs-secondary-border-subtle) !important;
          }
  
          .border-success-subtle {
              border-color: var(--bs-success-border-subtle) !important;
          }
  
          .border-info-subtle {
              border-color: var(--bs-info-border-subtle) !important;
          }
  
          .border-warning-subtle {
              border-color: var(--bs-warning-border-subtle) !important;
          }
  
          .border-danger-subtle {
              border-color: var(--bs-danger-border-subtle) !important;
          }
  
          .border-light-subtle {
              border-color: var(--bs-light-border-subtle) !important;
          }
  
          .border-dark-subtle {
              border-color: var(--bs-dark-border-subtle) !important;
          }
  
          .border-1 {
              border-width: 1px !important;
          }
  
          .border-2 {
              border-width: 2px !important;
          }
  
          .border-3 {
              border-width: 3px !important;
          }
  
          .border-4 {
              border-width: 4px !important;
          }
  
          .border-5 {
              border-width: 5px !important;
          }
  
          .border-opacity-10 {
              --bs-border-opacity: 0.1;
          }
  
          .border-opacity-25 {
              --bs-border-opacity: 0.25;
          }
  
          .border-opacity-50 {
              --bs-border-opacity: 0.5;
          }
  
          .border-opacity-75 {
              --bs-border-opacity: 0.75;
          }
  
          .border-opacity-100 {
              --bs-border-opacity: 1;
          }
  
          .w-25 {
              width: 25% !important;
          }
  
          .w-50 {
              width: 50% !important;
          }
  
          .w-75 {
              width: 75% !important;
          }
  
          .w-100 {
              width: 100% !important;
          }
  
          .w-auto {
              width: auto !important;
          }
  
          .mw-100 {
              max-width: 100% !important;
          }
  
          .vw-100 {
              width: 100vw !important;
          }
  
          .min-vw-100 {
              min-width: 100vw !important;
          }
  
          .h-25 {
              height: 25% !important;
          }
  
          .h-50 {
              height: 50% !important;
          }
  
          .h-75 {
              height: 75% !important;
          }
  
          .h-100 {
              height: 100% !important;
          }
  
          .h-auto {
              height: auto !important;
          }
  
          .mh-100 {
              max-height: 100% !important;
          }
  
          .vh-100 {
              height: 100vh !important;
          }
  
          .min-vh-100 {
              min-height: 100vh !important;
          }
  
          .flex-fill {
              flex: 1 1 auto !important;
          }
  
          .flex-row {
              flex-direction: row !important;
          }
  
          .flex-column {
              flex-direction: column !important;
          }
  
          .flex-row-reverse {
              flex-direction: row-reverse !important;
          }
  
          .flex-column-reverse {
              flex-direction: column-reverse !important;
          }
  
          .flex-grow-0 {
              flex-grow: 0 !important;
          }
  
          .flex-grow-1 {
              flex-grow: 1 !important;
          }
  
          .flex-shrink-0 {
              flex-shrink: 0 !important;
          }
  
          .flex-shrink-1 {
              flex-shrink: 1 !important;
          }
  
          .flex-wrap {
              flex-wrap: wrap !important;
          }
  
          .flex-nowrap {
              flex-wrap: nowrap !important;
          }
  
          .flex-wrap-reverse {
              flex-wrap: wrap-reverse !important;
          }
  
          .justify-content-start {
              justify-content: flex-start !important;
          }
  
          .justify-content-end {
              justify-content: flex-end !important;
          }
  
          .justify-content-center {
              justify-content: center !important;
          }
  
          .justify-content-between {
              justify-content: space-between !important;
          }
  
          .justify-content-around {
              justify-content: space-around !important;
          }
  
          .justify-content-evenly {
              justify-content: space-evenly !important;
          }
  
          .align-items-start {
              align-items: flex-start !important;
          }
  
          .align-items-end {
              align-items: flex-end !important;
          }
  
          .align-items-center {
              align-items: center !important;
          }
  
          .align-items-baseline {
              align-items: baseline !important;
          }
  
          .align-items-stretch {
              align-items: stretch !important;
          }
  
          .align-content-start {
              align-content: flex-start !important;
          }
  
          .align-content-end {
              align-content: flex-end !important;
          }
  
          .align-content-center {
              align-content: center !important;
          }
  
          .align-content-between {
              align-content: space-between !important;
          }
  
          .align-content-around {
              align-content: space-around !important;
          }
  
          .align-content-stretch {
              align-content: stretch !important;
          }
  
          .align-self-auto {
              align-self: auto !important;
          }
  
          .align-self-start {
              align-self: flex-start !important;
          }
  
          .align-self-end {
              align-self: flex-end !important;
          }
  
          .align-self-center {
              align-self: center !important;
          }
  
          .align-self-baseline {
              align-self: baseline !important;
          }
  
          .align-self-stretch {
              align-self: stretch !important;
          }
  
          .order-first {
              order: -1 !important;
          }
  
          .order-0 {
              order: 0 !important;
          }
  
          .order-1 {
              order: 1 !important;
          }
  
          .order-2 {
              order: 2 !important;
          }
  
          .order-3 {
              order: 3 !important;
          }
  
          .order-4 {
              order: 4 !important;
          }
  
          .order-5 {
              order: 5 !important;
          }
  
          .order-last {
              order: 6 !important;
          }
  
          .m-0 {
              margin: 0 !important;
          }
  
          .m-1 {
              margin: 0.25rem !important;
          }
  
          .m-2 {
              margin: 0.5rem !important;
          }
  
          .m-3 {
              margin: 1rem !important;
          }
  
          .m-4 {
              margin: 1.5rem !important;
          }
  
          .m-5 {
              margin: 3rem !important;
          }
  
          .m-auto {
              margin: auto !important;
          }
  
          .mx-0 {
              margin-right: 0 !important;
              margin-left: 0 !important;
          }
  
          .mx-1 {
              margin-right: 0.25rem !important;
              margin-left: 0.25rem !important;
          }
  
          .mx-2 {
              margin-right: 0.5rem !important;
              margin-left: 0.5rem !important;
          }
  
          .mx-3 {
              margin-right: 1rem !important;
              margin-left: 1rem !important;
          }
  
          .mx-4 {
              margin-right: 1.5rem !important;
              margin-left: 1.5rem !important;
          }
  
          .mx-5 {
              margin-right: 3rem !important;
              margin-left: 3rem !important;
          }
  
          .mx-auto {
              margin-right: auto !important;
              margin-left: auto !important;
          }
  
          .my-0 {
              margin-top: 0 !important;
              margin-bottom: 0 !important;
          }
  
          .my-1 {
              margin-top: 0.25rem !important;
              margin-bottom: 0.25rem !important;
          }
  
          .my-2 {
              margin-top: 0.5rem !important;
              margin-bottom: 0.5rem !important;
          }
  
          .my-3 {
              margin-top: 1rem !important;
              margin-bottom: 1rem !important;
          }
  
          .my-4 {
              margin-top: 1.5rem !important;
              margin-bottom: 1.5rem !important;
          }
  
          .my-5 {
              margin-top: 3rem !important;
              margin-bottom: 3rem !important;
          }
  
          .my-auto {
              margin-top: auto !important;
              margin-bottom: auto !important;
          }
  
          .mt-0 {
              margin-top: 0 !important;
          }
  
          .mt-1 {
              margin-top: 0.25rem !important;
          }
  
          .mt-2 {
              margin-top: 0.5rem !important;
          }
  
          .mt-3 {
              margin-top: 1rem !important;
          }
  
          .mt-4 {
              margin-top: 1.5rem !important;
          }
  
          .mt-5 {
              margin-top: 3rem !important;
          }
  
          .mt-auto {
              margin-top: auto !important;
          }
  
          .me-0 {
              margin-right: 0 !important;
          }
  
          .me-1 {
              margin-right: 0.25rem !important;
          }
  
          .me-2 {
              margin-right: 0.5rem !important;
          }
  
          .me-3 {
              margin-right: 1rem !important;
          }
  
          .me-4 {
              margin-right: 1.5rem !important;
          }
  
          .me-5 {
              margin-right: 3rem !important;
          }
  
          .me-auto {
              margin-right: auto !important;
          }
  
          .mb-0 {
              margin-bottom: 0 !important;
          }
  
          .mb-1 {
              margin-bottom: 0.25rem !important;
          }
  
          .mb-2 {
              margin-bottom: 0.5rem !important;
          }
  
          .mb-3 {
              margin-bottom: 1rem !important;
          }
  
          .mb-4 {
              margin-bottom: 1.5rem !important;
          }
  
          .mb-5 {
              margin-bottom: 3rem !important;
          }
  
          .mb-auto {
              margin-bottom: auto !important;
          }
  
          .ms-0 {
              margin-left: 0 !important;
          }
  
          .ms-1 {
              margin-left: 0.25rem !important;
          }
  
          .ms-2 {
              margin-left: 0.5rem !important;
          }
  
          .ms-3 {
              margin-left: 1rem !important;
          }
  
          .ms-4 {
              margin-left: 1.5rem !important;
          }
  
          .ms-5 {
              margin-left: 3rem !important;
          }
  
          .ms-auto {
              margin-left: auto !important;
          }
  
          .p-0 {
              padding: 0 !important;
          }
  
          .p-1 {
              padding: 0.25rem !important;
          }
  
          .p-2 {
              padding: 0.5rem !important;
          }
  
          .p-3 {
              padding: 1rem !important;
          }
  
          .p-4 {
              padding: 1.5rem !important;
          }
  
          .p-5 {
              padding: 3rem !important;
          }
  
          .px-0 {
              padding-right: 0 !important;
              padding-left: 0 !important;
          }
  
          .px-1 {
              padding-right: 0.25rem !important;
              padding-left: 0.25rem !important;
          }
  
          .px-2 {
              padding-right: 0.5rem !important;
              padding-left: 0.5rem !important;
          }
  
          .px-3 {
              padding-right: 1rem !important;
              padding-left: 1rem !important;
          }
  
          .px-4 {
              padding-right: 1.5rem !important;
              padding-left: 1.5rem !important;
          }
  
          .px-5 {
              padding-right: 3rem !important;
              padding-left: 3rem !important;
          }
  
          .py-0 {
              padding-top: 0 !important;
              padding-bottom: 0 !important;
          }
  
          .py-1 {
              padding-top: 0.25rem !important;
              padding-bottom: 0.25rem !important;
          }
  
          .py-2 {
              padding-top: 0.5rem !important;
              padding-bottom: 0.5rem !important;
          }
  
          .py-3 {
              padding-top: 1rem !important;
              padding-bottom: 1rem !important;
          }
  
          .py-4 {
              padding-top: 1.5rem !important;
              padding-bottom: 1.5rem !important;
          }
  
          .py-5 {
              padding-top: 3rem !important;
              padding-bottom: 3rem !important;
          }
  
          .pt-0 {
              padding-top: 0 !important;
          }
  
          .pt-1 {
              padding-top: 0.25rem !important;
          }
  
          .pt-2 {
              padding-top: 0.5rem !important;
          }
  
          .pt-3 {
              padding-top: 1rem !important;
          }
  
          .pt-4 {
              padding-top: 1.5rem !important;
          }
  
          .pt-5 {
              padding-top: 3rem !important;
          }
  
          .pe-0 {
              padding-right: 0 !important;
          }
  
          .pe-1 {
              padding-right: 0.25rem !important;
          }
  
          .pe-2 {
              padding-right: 0.5rem !important;
          }
  
          .pe-3 {
              padding-right: 1rem !important;
          }
  
          .pe-4 {
              padding-right: 1.5rem !important;
          }
  
          .pe-5 {
              padding-right: 3rem !important;
          }
  
          .pb-0 {
              padding-bottom: 0 !important;
          }
  
          .pb-1 {
              padding-bottom: 0.25rem !important;
          }
  
          .pb-2 {
              padding-bottom: 0.5rem !important;
          }
  
          .pb-3 {
              padding-bottom: 1rem !important;
          }
  
          .pb-4 {
              padding-bottom: 1.5rem !important;
          }
  
          .pb-5 {
              padding-bottom: 3rem !important;
          }
  
          .ps-0 {
              padding-left: 0 !important;
          }
  
          .ps-1 {
              padding-left: 0.25rem !important;
          }
  
          .ps-2 {
              padding-left: 0.5rem !important;
          }
  
          .ps-3 {
              padding-left: 1rem !important;
          }
  
          .ps-4 {
              padding-left: 1.5rem !important;
          }
  
          .ps-5 {
              padding-left: 3rem !important;
          }
  
          .gap-0 {
              gap: 0 !important;
          }
  
          .gap-1 {
              gap: 0.25rem !important;
          }
  
          .gap-2 {
              gap: 0.5rem !important;
          }
  
          .gap-3 {
              gap: 1rem !important;
          }
  
          .gap-4 {
              gap: 1.5rem !important;
          }
  
          .gap-5 {
              gap: 3rem !important;
          }
  
          .row-gap-0 {
              row-gap: 0 !important;
          }
  
          .row-gap-1 {
              row-gap: 0.25rem !important;
          }
  
          .row-gap-2 {
              row-gap: 0.5rem !important;
          }
  
          .row-gap-3 {
              row-gap: 1rem !important;
          }
  
          .row-gap-4 {
              row-gap: 1.5rem !important;
          }
  
          .row-gap-5 {
              row-gap: 3rem !important;
          }
  
          .column-gap-0 {
              -moz-column-gap: 0 !important;
              column-gap: 0 !important;
          }
  
          .column-gap-1 {
              -moz-column-gap: 0.25rem !important;
              column-gap: 0.25rem !important;
          }
  
          .column-gap-2 {
              -moz-column-gap: 0.5rem !important;
              column-gap: 0.5rem !important;
          }
  
          .column-gap-3 {
              -moz-column-gap: 1rem !important;
              column-gap: 1rem !important;
          }
  
          .column-gap-4 {
              -moz-column-gap: 1.5rem !important;
              column-gap: 1.5rem !important;
          }
  
          .column-gap-5 {
              -moz-column-gap: 3rem !important;
              column-gap: 3rem !important;
          }
  
          .font-monospace {
              font-family: var(--bs-font-monospace) !important;
          }
  
          .fs-1 {
              font-size: calc(1.375rem + 1.5vw) !important;
          }
  
          .fs-2 {
              font-size: calc(1.325rem + 0.9vw) !important;
          }
  
          .fs-3 {
              font-size: calc(1.3rem + 0.6vw) !important;
          }
  
          .fs-4 {
              font-size: calc(1.275rem + 0.3vw) !important;
          }
  
          .fs-5 {
              font-size: 1.25rem !important;
          }
  
          .fs-6 {
              font-size: 1rem !important;
          }
  
          .fst-italic {
              font-style: italic !important;
          }
  
          .fst-normal {
              font-style: normal !important;
          }
  
          .fw-lighter {
              font-weight: lighter !important;
          }
  
          .fw-light {
              font-weight: 300 !important;
          }
  
          .fw-normal {
              font-weight: 400 !important;
          }
  
          .fw-medium {
              font-weight: 500 !important;
          }
  
          .fw-semibold {
              font-weight: 600 !important;
          }
  
          .fw-bold {
              font-weight: 700 !important;
          }
  
          .fw-bolder {
              font-weight: bolder !important;
          }
  
          .lh-1 {
              line-height: 1 !important;
          }
  
          .lh-sm {
              line-height: 1.25 !important;
          }
  
          .lh-base {
              line-height: 1.5 !important;
          }
  
          .lh-lg {
              line-height: 2 !important;
          }
  
          .text-start {
              text-align: left !important;
          }
  
          .text-end {
              text-align: right !important;
          }
  
          .text-center {
              text-align: center !important;
          }
  
          .text-lowercase {
              text-transform: lowercase !important;
          }
  
          .text-uppercase {
              text-transform: uppercase !important;
          }
  
          .text-capitalize {
              text-transform: capitalize !important;
          }
  
          .text-wrap {
              white-space: normal !important;
          }
  
          .text-nowrap {
              white-space: nowrap !important;
          }
          /* rtl:begin:remove */
  
          .text-break {
              word-wrap: break-word !important;
              word-break: break-word !important;
          }
          /* rtl:end:remove */
  
          .text-primary {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-secondary {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-success {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-info {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-warning {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-danger {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-light {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-dark {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-black {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-white {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-body {
              --bs-text-opacity: 1;
              color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;
          }
  
          .text-muted {
              --bs-text-opacity: 1;
              color: var(--bs-secondary-color) !important;
          }
  
          .text-black-50 {
              --bs-text-opacity: 1;
              color: rgba(0, 0, 0, 0.5) !important;
          }
  
          .text-white-50 {
              --bs-text-opacity: 1;
              color: rgba(255, 255, 255, 0.5) !important;
          }
  
          .text-body-secondary {
              --bs-text-opacity: 1;
              color: var(--bs-secondary-color) !important;
          }
  
          .text-body-tertiary {
              --bs-text-opacity: 1;
              color: var(--bs-tertiary-color) !important;
          }
  
          .text-body-emphasis {
              --bs-text-opacity: 1;
              color: var(--bs-emphasis-color) !important;
          }
  
          .text-reset {
              --bs-text-opacity: 1;
              color: inherit !important;
          }
  
  