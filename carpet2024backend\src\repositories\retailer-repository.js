// repositories/retailerRepository.js

const Retailer = require('../model/phase-2/retailer');

async function createRetailer(data) {
    return await Retailer.create(data);
}

async function getRetailers() {
    return await Retailer.find();
}

async function getRetailerById(id) {
    return await Retailer.findById(id);
}

async function updateRetailer(id, data) {
    return await Retailer.findByIdAndUpdate(id, data, { new: true });
}

async function deleteRetailer(id) {
    return await Retailer.findByIdAndDelete(id);
}

async function addRetailerPrice(id,reqData){
    try {
        let updatedData = await Retailer.updateOne(
          { _id: id },
          {
            $push: {
              addOnPrice: {
                toDate: reqData.toDate,
                fromDate: reqData.fromDate,
                quality: reqData.quality,
                design: reqData.design,
                orderPrice: reqData.orderPrice,
              },
            },
          }
        );
        if (updatedData.acknowledged == true && updatedData.modifiedCount == 1) {
          return updatedData;
        }
        return 
      } catch (error) {
        throw error;
      }
}
module.exports = {
    createRetailer,
    getRetailers,
    getRetailerById,
    updateRetailer,
    deleteRetailer,
    addRetailerPrice
};
