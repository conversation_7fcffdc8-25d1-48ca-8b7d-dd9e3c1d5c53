// Script to check actual issues collection and find real issue numbers
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function checkActualIssuesCollection() {
  console.log('🔍 CHECKING ACTUAL ISSUES COLLECTION');
  console.log('='.repeat(80));
  
  try {
    const db = mongoose.connection.db;
    
    // List all collections to find issue-related collections
    const collections = await db.listCollections().toArray();
    console.log('📋 Available collections:');
    collections.forEach(col => {
      if (col.name.toLowerCase().includes('issue') || col.name.toLowerCase().includes('order') || col.name.toLowerCase().includes('carpet')) {
        console.log(`   📁 ${col.name}`);
      }
    });
    
    // Check carpetorderissues collection (most likely)
    const issueCollectionNames = [
      'carpetorderissues',
      'carpetorders', 
      'issues',
      'carpetissues',
      'orderissues'
    ];
    
    let actualIssues = [];
    let issueCollectionName = null;
    
    for (const collectionName of issueCollectionNames) {
      try {
        const collection = db.collection(collectionName);
        const count = await collection.countDocuments();
        if (count > 0) {
          console.log(`\n📊 Found ${count} documents in ${collectionName}`);
          const samples = await collection.find({}).limit(10).toArray();
          
          if (samples.length > 0) {
            console.log(`\n📋 Sample documents from ${collectionName}:`);
            samples.forEach((doc, index) => {
              console.log(`${index + 1}. Document keys: ${Object.keys(doc).join(', ')}`);
              if (doc.Br_issueNo || doc.issueNo || doc.issue_no) {
                console.log(`   Issue No: ${doc.Br_issueNo || doc.issueNo || doc.issue_no}`);
              }
            });
            
            actualIssues = await collection.find({}).toArray();
            issueCollectionName = collectionName;
            break;
          }
        }
      } catch (error) {
        // Collection doesn't exist, continue
      }
    }
    
    if (actualIssues.length > 0) {
      console.log(`\n🎯 FOUND ACTUAL ISSUES IN: ${issueCollectionName}`);
      console.log('='.repeat(60));
      console.log(`📊 Total actual issues: ${actualIssues.length}`);
      
      // Extract issue numbers
      const issueNumbers = actualIssues.map(issue => {
        return issue.Br_issueNo || issue.issueNo || issue.issue_no || issue._id.toString();
      }).filter(num => num);
      
      console.log('\n📋 Actual Issue Numbers:');
      issueNumbers.forEach((issueNo, index) => {
        console.log(`   ${index + 1}. ${issueNo}`);
      });
      
      // Check which carpet numbers don't have corresponding issues
      const carpetCollection = db.collection('carpetreceiveds');
      const carpetRecords = await carpetCollection.find({ receiveNo: { $regex: /^K-/ } }).toArray();
      
      console.log('\n🔍 MAPPING CARPETS TO ACTUAL ISSUES:');
      console.log('='.repeat(60));
      
      const mappedCarpets = [];
      const unmappedCarpets = [];
      
      carpetRecords.forEach(carpet => {
        const carpetIssueNo = carpet.issueNo?.Br_issueNo || carpet.carpetNo;
        const hasActualIssue = issueNumbers.includes(carpetIssueNo);
        
        if (hasActualIssue) {
          mappedCarpets.push({
            carpetNo: carpet.receiveNo,
            issueNo: carpetIssueNo,
            weaver: carpet.weaverName,
            design: carpet.design,
            hasIssue: true
          });
        } else {
          unmappedCarpets.push({
            carpetNo: carpet.receiveNo,
            issueNo: carpetIssueNo,
            weaver: carpet.weaverName,
            design: carpet.design,
            hasIssue: false
          });
        }
      });
      
      console.log(`\n✅ CARPETS WITH ACTUAL ISSUES (${mappedCarpets.length}):`);
      mappedCarpets.forEach((carpet, index) => {
        console.log(`   ${index + 1}. ${carpet.carpetNo} → ${carpet.issueNo} | ${carpet.weaver} | ${carpet.design}`);
      });
      
      console.log(`\n❌ CARPETS WITHOUT ACTUAL ISSUES (${unmappedCarpets.length}):`);
      unmappedCarpets.forEach((carpet, index) => {
        console.log(`   ${index + 1}. ${carpet.carpetNo} → ${carpet.issueNo} | ${carpet.weaver} | ${carpet.design}`);
      });
      
      return { actualIssues, mappedCarpets, unmappedCarpets, issueNumbers };
      
    } else {
      console.log('\n❌ No actual issues collection found');
      return null;
    }
    
  } catch (error) {
    console.error('❌ Error checking issues collection:', error);
    return null;
  }
}

async function main() {
  console.log('🔍 FINDING ACTUAL ISSUE NUMBERS AND CARPET MAPPING');
  console.log('(Checking real issues collection vs carpet records)');
  console.log('='.repeat(80));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Check actual issues collection
    const result = await checkActualIssuesCollection();

    if (result) {
      console.log('\n🎯 FINAL ANALYSIS:');
      console.log('='.repeat(50));
      console.log(`📊 Actual issues found: ${result.actualIssues.length}`);
      console.log(`📊 Carpets with real issues: ${result.mappedCarpets.length}`);
      console.log(`📊 Carpets without real issues: ${result.unmappedCarpets.length}`);
      
      if (result.unmappedCarpets.length > 0) {
        console.log('\n⚠️ PROBLEM IDENTIFIED:');
        console.log(`   ${result.unmappedCarpets.length} carpet records have issue numbers that don't exist in actual issues collection`);
        console.log('   These carpets need to be mapped to existing issues or new issues need to be created');
      }
      
      console.log('\n📋 ISSUE NUMBER RANGE:');
      const issueNums = result.issueNumbers
        .filter(num => num.startsWith('K-'))
        .map(num => parseInt(num.replace('K-24000', '').replace('K-2400', '')))
        .filter(num => !isNaN(num))
        .sort((a, b) => a - b);
      
      if (issueNums.length > 0) {
        console.log(`   Actual issue range: ${Math.min(...issueNums)} to ${Math.max(...issueNums)}`);
        console.log(`   Total actual issues: ${issueNums.length}`);
      }
    }

  } catch (error) {
    console.error('❌ Analysis failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 ACTUAL ISSUE ANALYSIS COMPLETE!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
