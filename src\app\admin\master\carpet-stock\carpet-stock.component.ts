import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ImporterService } from '../../../services/importer.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { Router } from '@angular/router';
import { CustomeServiceService } from '../../../services/custome-service.service';
export interface PeriodicElement {

  index: number;
  // bcrNo: string;
  qualityDesign: string;
  color: string;
  size: string;
  // qdCode: string;
  // cCode: string;
  pcs: string;
  area: string;
  evkPrice: string;
  amount: string;
}

const ELEMENT_DATA: PeriodicElement[] = [];
@Component({
  selector: 'app-carpet-stock',
  templateUrl: './carpet-stock.component.html',
  styleUrl: './carpet-stock.component.css',
})
export class CarpetStockComponent implements OnInit {
  constructor(private fb: FormBuilder, private service: ImporterService, private ngxLoader: NgxUiLoaderService,private router:Router,private customeService:CustomeServiceService) {}
  frmCarpetStock!: FormGroup;
  displayedColumns: any[] = [
    'index',
    // 'barCode',
    'qd',
    // 'qdCode',
    'colour',
    // 'cCode',
    'size',
    // 'sCode',
    'pcs',
    'area',
    'evkPrice',
    'amount',
    'action'
  ];
  dataSource = new MatTableDataSource(ELEMENT_DATA);

  qualityDesignList: any = [];
  colorList: any = [];
  sizeList: any = [];
  colourStockArray: any = [];
  stockArray: any = [];
  ngOnInit(): void {
  this.frmCarpetStock = this.fb.group({
      qualityDesign: [],
      colour: [],
      size: [],
    });
    ELEMENT_DATA.length=0;
    this.getStock();
  }
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }
  getStock() {
    this.ngxLoader.start();
    this.service.getAllContainerRecieved().subscribe((resp: any) => {
      console.log(resp);
      this.stockArray = resp;
      this.qualityDesignList = resp.flatMap((val: any, i: number) => {
        return val.containerItem.map((x: any, i: any) => {
          if (x.status !== 'sale') {
            return x.QualityDesign;
          }
        });
      });
      this.qualityDesignList = this.qualityDesignList.filter(
        (value: any, index: number, self: any[]) => {
          return self.indexOf(value) === index;
        }
      );
      // this.qualityDesignList.unshift('All');

      this.qualityDesignList = this.qualityDesignList.filter((i:any)=>i!==undefined);
      this.qualityDesignList = [...new Set(this.qualityDesignList)].sort();
      this.qualityDesignList.unshift('All');
      console.log(this.qualityDesignList)
      this.ngxLoader.stop();
    });
  }
selectedQuality:any;
  getColor(event: any) {
    ;
    this.selectedQuality=event;
    this.colorList = [];
    this.colourStockArray = [];
    this.stockArray.flatMap((val: any, i: number) => {
      return val.containerItem.map((x: any, i: any) => {

        if (x.QualityDesign === event && x.status!=='sale') {

          this.colourStockArray.push(x.Color);
          return this.colourStockArray;
        }
      });
    });
    this.colorList = this.colourStockArray.filter(
      (value: any, index: number, self: any[]) => {
        return self.indexOf(value) === index;
      }
    );

    this.colorList.unshift('All');
  }

  selectColour(event: any) {

    this.sizeList = [];
    let sizeArray: any = [];
    this.stockArray.flatMap((val: any, i: number) => {
      return val.containerItem.map((x: any, i: any) => {
        if ( x.QualityDesign===this.selectedQuality && x.Color === event && x.status!=='sale') {
          sizeArray.push(x.Size);
          return sizeArray;
        }
      });
    });
    this.sizeList = sizeArray.filter(
      (value: any, index: number, self: any[]) => {
        return self.indexOf(value) === index;
      }
    );
    debugger



// Step 1: Unique sizes nikalna
const uniqueSizes = [...new Set(this.sizeList)];

// Step 2: Function to check if two sizes are within the 10-unit range
function isWithinRange(size1:any, size2:any) {
  const [width1, length1] = size1.split(' X ').map(Number);
  const [width2, length2] = size2.split(' X ').map(Number);

  return (
    Math.abs(width1 - width2) <= 10 &&
    Math.abs(length1 - length2) <= 10
  );
}

// Step 3: Filter unique ranges within the 10-unit range
const filteredRanges:any[] = [];

uniqueSizes.forEach(size1 => {
  let isUnique = true;

  filteredRanges.forEach(range => {
    if (isWithinRange(range, size1)) {
      isUnique = false;
    }
  });

  if (isUnique) {
    filteredRanges.push(size1);
  }
});

// Outputting the final filtered unique sizes
console.log("Filtered Unique Sizes:");
console.log(filteredRanges);

const sortedSizes = filteredRanges.sort((a:any, b:any) => {
  const [widthA, lengthA] = a.split(' X ').map(Number);
  const [widthB, lengthB] = b.split(' X ').map(Number);

  if (widthA === widthB) {
    return lengthA - lengthB; // If widths are equal, sort by length
  }

  return widthA - widthB; // Otherwise, sort by width
});
this.sizeList=sortedSizes;
    this.sizeList.unshift('All');
  }

  getStockDetails() {
    ELEMENT_DATA.length = 0;
    let frmData = this.frmCarpetStock.value;
    let count = 1;
    if(frmData.qualityandDesign==='All'){
      frmData.colour='All';
      frmData.size='All';
    }
    if(frmData.qualityandDesign!=='All' && frmData.colour==='All'){
      frmData.size='All';
    }
    if(frmData.qualityandDesign!=='All' && frmData.colour!=='All'){
      frmData.size ?frmData.size:frmData.size='All'
    }




this.service.getCarpetStock(frmData).subscribe((resp:any)=>{
  ;

  let containerItem=this.groupData(resp.data);
  console.log(containerItem);

    containerItem.map((x: any, ind: any) => {

          ELEMENT_DATA.push({

            index: ind+1,
            qualityDesign: x.QualityDesign,
            color: x.Color,
            size: x.Size,
            area: x.totalArea.toFixed(2),
            evkPrice: x.EvKPrice,
            pcs:x.pcs,
            amount: x.totalAmount>0?x.totalAmount.toFixed(2):x.EvKPrice,
          });
          return;


      });

      this.dataSource = new MatTableDataSource(ELEMENT_DATA);

      this.ngAfterViewInit();
      return;
    });
    ;
  }

  reset(){
    this.frmCarpetStock.reset();
  }



  navigate(data:any){
    let carpetData = this.stockArray.flatMap((items: any) =>
      items.containerItem.filter((item: any) => {
        const [width1, height1] = data.size.split(" X ").map(Number);
        const [width2, height2] = item.Size.split(" X ").map(Number);

        const isWithinRange = (value1: number, value2: number, tolerance: number) => {
          return Math.abs(value1 - value2) <= tolerance;
        };

        return (
          item.QualityDesign === data.qualityDesign &&
          item.Color === data.color &&
          isWithinRange(width1, width2, 10) &&
          isWithinRange(height1, height2, 10) &&
          item.status !== 'sale'
        );
      })
    );






    // This will give you an array of objects similar to stockArray, but with containerItem arrays filtered according to your criteria.
    console.log(carpetData);


    this.customeService.setData(carpetData);
    this.router.navigateByUrl('/admin/carpet-stock-details');
  }

  // ---------------------------------------------------------------

  groupedCarpets:any;
  // groupData(data: any) {
  //   debugger
  //   this.groupedCarpets = data.map((items:any)=>{
  //     items.containerItem.reduce((acc: any, carpet: any) => {
  //       const { QualityDesign, Color, Size, Amount, Area, EvKPrice, status } = carpet;

  //       // Skip processing if the status is 'sale'
  //       if (status === 'sale') {
  //         return acc; // Skip this item and continue with the next iteration
  //       }

  //       let found = false;

  //       for (let key in acc) {
  //         let group = acc[key];
  //         if (
  //           group.QualityDesign === QualityDesign &&
  //           group.Color === Color &&
  //           this.areSizesSimilar(group.Size, Size)
  //         ) {
  //           group.totalAmount += parseFloat(Amount);
  //           group.totalArea += parseFloat(Area);
  //           group.pcs += 1;
  //           found = true;
  //           break;
  //         }
  //       }

  //       if (!found) {
  //         acc[`${QualityDesign}-${Color}-${Size}`] = {
  //           QualityDesign,
  //           Color,
  //           Size,
  //           EvKPrice: parseFloat(EvKPrice),
  //           totalAmount: parseFloat(Amount),
  //           totalArea: parseFloat(Area),
  //           pcs: 1
  //         };
  //       }

  //       return acc;
  //     }, {});
  //   })

  //   const aggregatedCarpets = Object.values(this.groupedCarpets);
  //   console.log(aggregatedCarpets);
  //   return aggregatedCarpets;
  // }

  groupData(data: any) {
    debugger;

    // Initialize an empty object to store all grouped carpets
    this.groupedCarpets = {};

    // Iterate through each container
    data.forEach((items: any) => {
      items.containerItem.reduce((acc: any, carpet: any) => {
        const { QualityDesign, Color, Size, Amount, Area, EvKPrice, status } = carpet;

        // Skip processing if the status is 'sale'
        if (status === 'sale') {
          return acc; // Skip this item and continue with the next iteration
        }

        let found = false;

        // Check if similar group already exists
        for (let key in acc) {
          let group = acc[key];
          if (
            group.QualityDesign === QualityDesign &&
            group.Color === Color &&
            this.areSizesSimilar(group.Size, Size)
          ) {
            group.totalAmount += parseFloat(Amount);
            group.totalArea += parseFloat(Area);
            group.pcs += 1;
            found = true;
            break;
          }
        }

        // If no matching group is found, create a new group
        if (!found) {
          acc[`${QualityDesign}-${Color}-${Size}`] = {
            QualityDesign,
            Color,
            Size,
            EvKPrice: parseFloat(EvKPrice),
            totalAmount: parseFloat(Amount),
            totalArea: parseFloat(Area),
            pcs: 1
          };
        }

        return acc;
      }, this.groupedCarpets); // Accumulate into the global groupedCarpets object
    });

    // Convert the grouped carpets object to an array
    const aggregatedCarpets = Object.values(this.groupedCarpets);
    console.log(aggregatedCarpets);
    return aggregatedCarpets;
  }


  // Helper function to check if sizes are similar
  areSizesSimilar(size1: string, size2: string): boolean {
    const sizeTolerance = 10;
    const parseSize = (sizeStr: string) => {
      const [width, height] = sizeStr.split(" X ").map(Number);
      return { width, height };
    };

    const { width: width1, height: height1 } = parseSize(size1);
    const { width: width2, height: height2 } = parseSize(size2);
    return (
      Math.abs(width1 - width2) <= sizeTolerance &&
      Math.abs(height1 - height2) <= sizeTolerance
    );
  }

}
