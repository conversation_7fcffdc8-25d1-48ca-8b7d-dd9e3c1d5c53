import { Component, ElementRef, OnInit, ViewChild, viewChild } from '@angular/core';
import { ImporterService } from '../../../../services/importer.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import * as XLSX from 'xlsx'
import { error } from 'node:console';
import { CustomeFunction } from '../../../../Shared/customeFunctions';
import { CustomeServiceService } from '../../../../services/custome-service.service';
import Swal from 'sweetalert2';
import { ActivatedRoute } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';

export interface ExcelRecord {
  Date: string;
  BaleNo: string;
  PcsNo: string;
  Quality: string;
  Design: string;
  Colour: string;
  Width: string;
  Length: string;
  Area: string;
  InvoiceNo: string;
  ImpoterCode: string;
  Remark: string;
}

const ELEMENT_DATA2: ExcelRecord[] = [];


@Component({
  selector: 'app-import-invoices',
  templateUrl: './import-invoices.component.html',
  styleUrl: './import-invoices.component.css'
})
export class ImportInvoicesComponent implements OnInit {
  [x: string]: any;


  displayedColumns: string[] = ['Date', 'BaleNo', 'PcsNo', 'Quality', 'Design', 'Colour', 'Width', 'Length', 'Area',
    'InvoiceNo',
    'ImpoterCode',
    'Remark'];
  dataSource = new MatTableDataSource<ExcelRecord>(ELEMENT_DATA2);
  frmImportInvoice!: FormGroup;
  excelData: any = [];
  blPdf: any;
  invoicePdf: any;
  // file!:File;
  importerName: any = [];

  invoiceNo: any;
  excelSummaryData: any = [];

  impotererNo: any = [];
  totalArea: any;
  totalPcs: any;
  totalAmount: any;
  @ViewChild('chooseExcel') chooseExcel !: ElementRef;

  constructor(private importerServices: ImporterService, private formBuilder: FormBuilder, private customeService: CustomeServiceService, private activatedRoute: ActivatedRoute) { }
  ngOnInit(): void {
    this.frmImportInvoice = this.formBuilder.group({
      importNo: [],
      importerName: [],
      invoiceNo: [],
      areaOfUnit: [],
      curency: [],
      quantity: [],
      totalArea: [],
      amount: [],
      date: []
      // file:[],
      // invoicepdf:[],
      // uploadBlPdf:[],
      // uploadCsvExcel :[],     
    })
    this.getCustomerName()
    this.getAllImporterInvoices();
    this.getAllImporterInvoiceCode();


  }

  importername = 'option2';
  AreaofUnit = 'option2';

  areaOfUnit: any = ['Sqrt Meter', 'Sqrt Feet']

  selectedImportNo: any;
  impotererNo1: any = [];
  getAllImporterInvoiceCode() {

    this.importerServices.getAllImporterInvoice().subscribe((resp: any) => {

      if (resp.length === 0) {

        this.impotererNo.push('KOTI-00001');
      } else {

        resp.forEach((val: any) => {


          let importcode = val.impotererNo.split('/')[0];

          const numericPart = parseInt(importcode.split('-')[1]);
          this.impotererNo1.push({ original: importcode, numeric: numericPart });
        });


        this.impotererNo1.sort((a: any, b: any) => b.numeric - a.numeric);


        const nextNumericPart = this.impotererNo1[0].numeric + 1;


        const nextImporterNumber = `KOTI-${nextNumericPart.toString().padStart(5, '0')}`;
        this.impotererNo1.unshift({ original: nextImporterNumber, numeric: nextNumericPart });

        this.impotererNo = Array.from(new Set(this.impotererNo1.map((obj: any) => obj.original)));
        setTimeout(() => {
          if (this.impotererNo.length > 0) {
            this.selectedImportNo = this.impotererNo[0];
          }
        }, 2000);
      }
    });

  }


  importInvoice() {
    let frmData = this.frmImportInvoice.value;

    let data = {

      invoiceNo: frmData.invoiceNo,
      invoicePdf: this.invoicePdf,
      blPdf: this.blPdf,
      impotererNo: frmData.importNo + '/' + this.importerCode.importerCode,
      impotererName: frmData.importerName,
      areaOfUnit: frmData.areaOfUnit,
      currency: frmData.curency,
      quantity: frmData.quantity,
      totalArea: frmData.totalArea,
      amount: frmData.amount,
    }


    this.importerServices.createInvoiceDetails(data).subscribe((resp: any) => {

      Swal.fire(
        'Success!',
        'Data has been saved successfully',
        'success'
      )
    }, (error) => {
      console.log(error)
      Swal.fire(
        'warning!',
        'Failed',
        'warning'
      )
    })
  }
  currencyArray: any = ['New', 'Dollar', 'Ruppes', 'Euro']

  uploadpdf(data: any) {
    let file = data.target.files[0];
    this.importerServices.createInvoicePdf(file).subscribe((res: any) => {

      this.invoicePdf = res._id
      // console.log(res);
      console.log('hiiiiiii', this.invoicePdf);
    }, (err: any) => {
      console.log(err)
    }
    )


  }
  uploadpdf2(data: any) {

    let file = data.target.files[0];
    this.importerServices.createInvoicePdf(file).subscribe((res: any) => {

      this.blPdf = res._id;
      console.log('heloooooo', this.blPdf)
    }, (err: any) => {
      console.log(err)
    })
  }
  invoiceData: any = {};
  readExcel(event: any) {
    let file = event.target.files[0];

    let impoterNo = this.frmImportInvoice.value

    let data = this.frmImportInvoice.value
    if (!impoterNo || !data.importerName || !data.date) {
      Swal.fire(
        'warning!',
        'Please select importer no, importer name and date ',
        'warning'
      )

      this.chooseExcel.nativeElement.value = '';
    } else {


      this.importerServices.UploadExcel(file, impoterNo).subscribe((resp: any) => {
        debugger

        if (resp) {
          this.invoiceData = {
            InvoiceNo: resp.data[0].InvoiceNo,
            impoterName: resp.data[0].impoterName
          }
          this.getExcelSummary(this.invoiceData);
          this.getExcelList(this.invoiceData);
        }

        console.log(resp);

      }, (error) => {

        Swal.fire(
          'warning!',
          error.error.text,
          'warning'
        )

      })


    }


  }

  getCustomerName() {

    this.importerServices.getAllImporter().subscribe((res: any) => {
      res.map((item: any) => {

        this.importerName.push({ importerName: item.impoter.address.customerName, importerCode: item.impoter.address.customerCode });

      })
      // this.importerName = res
      console.log(this.importerName, 'here all mporter list ');
    })
  }




  ///////////////////////////////////////////////
  /////////////////////////////here code ofr get all importer invoice list 
  ///////////
  getAllImporterInvoices() {
    this.importerServices.getAllImporterInvoice().subscribe((resp: any) => {

      // console.log(resp);
      let len = resp.length;
      let data = this.customeService.generateKOTINumber(len);
      // console.log(data);


      this.frmImportInvoice.get('importNo')?.setValue(data);
    });
  }
  ////////////////////////////selected
  importerCode: any;
  getSelectedImporter: any;
  getImporterName(data: any) {
    this.importerServices.getAllImporter().subscribe((resp: any) => {
      this.getSelectedImporter = resp.filter((x: any) => {

        if (x.impoter.address.customerName == data) {

          return x
        }
      })
    })

    this.importerCode = this.importerName.find((x: any) => x.importerName == data)


    setTimeout(() => {
      console.log(this.getSelectedImporter, 'hhhhhhhhhhhhhhhhh');
      // this.getExcelSummary(this.getSelectedImporter);
    }, 1000)
  }
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  getExcelSummary(a: any) {

    let dd = this.frmImportInvoice.get('date')?.value;

    let date = this.customeService.convertDate2(dd);


    let design = this.getSelectedImporter[0].impoter.addOnPrice.design;
    let quality = this.getSelectedImporter[0].impoter.addOnPrice.quality;
    this.importerServices.getExcelSummary(a).subscribe((resp: any) => {

      console.log(resp);

      let priceList = this.getSelectedImporter[0].impoter.addOnPrice;
      resp.result.forEach((val: any) => {
        const matchingPrice = priceList.find((x: any) => {
          return x.toDate <= date && x.fromDate >= date && x.quality === val.name && x.design === val.design;
        });

        if (matchingPrice) {
          this.excelSummaryData.push({
            quality: val.name,
            design: val.design,
            quantity: val.total,
            area: parseFloat(val.area).toFixed(2),
            amount: parseFloat(val.multiValue).toFixed(2),
            rate: parseFloat(matchingPrice.orderPrice).toFixed(2)
          });
        } else {
          this.excelSummaryData.push({
            quality: val.name,
            design: val.design,
            quantity: val.total,
            area: parseFloat(val.area).toFixed(2),
            amount: parseFloat(val.multiValue).toFixed(2),
            rate: ''
          });
        }
      });




      this.frmImportInvoice.get('quantity')?.setValue(resp.totalData.total)
      this.frmImportInvoice.get('totalArea')?.setValue(resp.totalData.areaTotal)
      this.frmImportInvoice.get('amount')?.setValue(resp.totalData.multiValueTotal)

      this.totalAmount = resp.totalData.multiValueTotal;
      this.totalPcs = resp.totalData.total;
      this.totalArea = resp.totalData.areaTotal;


      // }


    })
  }

  getExcelList(data:any) {
    this.importerServices.getAllExcelList(data).subscribe((resp: any) => {
      console.log(resp)
ELEMENT_DATA2.length=0;
      resp.map((val: any, ind: number) => {
        let date = this.customeService.convertDate(val.Date);
        this.invoiceNo = val.InvoiceNo;
        ELEMENT_DATA2.push({
          Date: date,
          BaleNo: val.BaleNo,
          PcsNo: val.PcsNo,
          Quality: val.Quality,
          Design: val.Design,
          Colour: val.Colour,
          Width: val.Width,
          Length: val.Length,
          Area: val.Area,
          InvoiceNo: val.InvoiceNo,
          ImpoterCode: val.ImpoterCode,
          Remark: val.Remark,
        })
      })
      console.log(this.excelData)
      this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
      this.ngAfterViewInit();
      this.frmImportInvoice.get('invoiceNo')?.setValue(this.invoiceNo);
      return;
    
    })
  }

  currencySign: any;

  selectCurrency(currency: any) {

    this.currencySign = currency;
  }


}
