const userRepository = require("../repositories/user-repository");
const roleRepository = require("../repositories/role-repository");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
class UserService {
  async createUser(userData) {
    try {
      return await userRepository.createUser(userData);
    } catch (error) {
      throw error;
    }
  }
  async loginUser(email, password) {
    try {
      // Retrieve user from database by email
      const user = await userRepository.getUserByEmail(email);
      // Check if user exists
      if (!user) {
        throw new Error("User not found");
      }
      // // Compare provided password with hashed password in database
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new Error("Invalid password");
      }

      // Generate JWT token
      const token = jwt.sign({ userId: user }, "your_secret_key", {
        expiresIn: "1h",
      });
      return { token };
    } catch (error) {
      throw error;
    }
  }

  async getUserById(userId) {
    try {
      const user = await userRepository.getUserById(userId);
      return user;
    } catch (error) {
      throw error;
    }
  }

  async getAllUsers() {
    try {
      const users = await userRepository.getAllUsers();
      return users;
    } catch (error) {
      throw error;
    }
  }

  async updateUser(userId, userData) {
    try {
      const user = await userRepository.updateUser(userId, userData);
      return user;
    } catch (error) {
      throw error;
    }
  }

  async deleteUser(userId) {
    try {
      const user = await userRepository.deleteUser(userId);
      return user;
    } catch (error) {
      throw error;
    }
  }

  async addUserRole(userId, roleName) {
    try {
      const user = await userRepository.getUserById(userId);
      const role = await roleRepository.getRoleByName(roleName);
      if (!user.role.find((userRole) => userRole._id.equals(role.id))) {
        user.role.push(role.id);
      }
      await user.save();
      return user;
    } catch (error) {
      throw error;
    }
  }
  async removeUserRole(userId, roleName) {
    try {
      const user = await userRepository.getUserById(userId);
      const role = await roleRepository.getRoleByName(roleName);
      user.role = user.role.filter((userRole) => !userRole._id.equals(role.id));

      await user.save();
      return user;
    } catch (error) {
      throw error;
    }
  }

  async updatePassword(userId, oldPassword, newPassword) {
    try {
      const user = await userRepository.getUserById(userId);
      const isPasswordMatch = await bcrypt.compare(oldPassword, user.password);
      if (!isPasswordMatch) {
        throw new Error("Old password is incorrect");
      }

      const hashedNewPassword = await bcrypt.hash(newPassword, 10);
      user.password = hashedNewPassword;
      await user.save();

      return user;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new UserService();
