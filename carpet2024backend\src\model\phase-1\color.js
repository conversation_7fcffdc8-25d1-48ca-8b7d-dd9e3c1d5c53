const mongoose = require('mongoose');

const colorSchema = new mongoose.Schema({
  initialColor: {
    type: String,
   // required: true,
   // unique:true
  },
  newColor: {
    type: String,
    required: true,
  },
  colorCode: {
    type: String,
    required: true,
  },
  companyColorCode: {
    type: String,
    required: true,
  },
  remark:{
     type: String
  },
  quality: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AddQuality'
},
});

const Color = mongoose.model('Color', colorSchema);

module.exports = Color;
