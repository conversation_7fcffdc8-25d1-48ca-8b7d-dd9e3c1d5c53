// Simple script to import the 12 missing H- records
const mongoose = require('mongoose');

const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

// The 12 missing records that failed due to duplicates
const missingRecords = [
  'H-210977', 'H-220003', 'H-220009', 'H-220010', 'H-220005', 'H-170003', 
  'H-180067', 'H-180033', 'H-180034', 'H-180291', 'H-170060'
];

async function importMissing() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 15000,
      connectTimeoutMS: 10000
    });
    console.log('✅ Connected successfully');
    
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    console.log('🔍 Checking missing records...');
    
    for (const recordId of missingRecords) {
      const exists = await collection.findOne({ receiveNo: recordId });
      if (exists) {
        console.log(`✅ ${recordId} already exists`);
      } else {
        console.log(`❌ ${recordId} is missing`);
      }
    }
    
    // Count total H- records
    const hCount = await collection.countDocuments({ receiveNo: { $regex: /^H-/ } });
    console.log(`📊 Total H- records in database: ${hCount}`);
    
    await mongoose.connection.close();
    console.log('🔌 Connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

importMissing();
