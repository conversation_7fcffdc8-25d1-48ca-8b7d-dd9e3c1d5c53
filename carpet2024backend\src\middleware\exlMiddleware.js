const multer = require("multer");
const path = require("path")

const excelFilter = (req,file,cb) =>{
	if(	file.mimetype.includes("excel")||
		file.mimetype.includes("spreadsheetml")
		){
		cb(null,true);
	}else{
		cb("Please upload only excel file.",false)
	}
};

var storage = multer.diskStorage({
    destination: (req, file, cb) => {
        // var aa = __dirname+"../../public/all-excel"
        // console.log(aa)
        cb(null, path.resolve(__dirname, '../../public/all-pdf'));// Adjust the path based on your directory structure
    },
    filename: (req, file, cb) => {
       // console.log(file.originalname);
        cb(null, `${Date.now()}-chihili-${file.originalname}`);
    }
});
var uploadFile = multer({storage:storage, fileFilter:excelFilter});
module.exports = uploadFile;
