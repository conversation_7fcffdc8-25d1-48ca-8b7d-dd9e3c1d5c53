// services/container-rev-service.js

const containerReceivedRepository = require("../repositories/container-rcv-repository");

const mapContainerReceivedData = (data) => {
  const mappedData = {
    impoterName: data.impoterName,
    expensesAmount: data.expensesAmount,
    totalArea: data.totalArea,
    espPrice: data.espPrice,
    blPdf: data.blPdf,
    containerItem: data.containerItem,
  };

  // const additionalFields = ['SNo', 'GerCarpetNo', 'QualityDesign', 'QCode', 'Size', 'SCore', 'Area', 'Rate', 'EvKPrice', 'InvoiceNo', 'ImporterCode', 'Remarks'];

  // additionalFields.forEach(field => {
  //     if (data.hasOwnProperty(field)) {
  //         mappedData[field] = data[field];
  //     }
  // });

  return mappedData;
};

async function createContainerReceived(containerReceivedData) {
  const mappedData = mapContainerReceivedData(containerReceivedData);
  return containerReceivedRepository.createContainerReceived(mappedData);
}

async function getContainerReceivedById(containerReceivedId) {
  return containerReceivedRepository.getContainerReceivedById(
    containerReceivedId
  );
}

async function getAllContainerReceived() {
  return containerReceivedRepository.getAllContainerReceived();
}

async function updateContainerReceived(
  containerReceivedId,
  containerReceivedData
) {
  const mappedData = mapContainerReceivedData(containerReceivedData);
  return containerReceivedRepository.updateContainerReceived(
    containerReceivedId,
    mappedData
  );
}

async function deleteContainerReceived(containerReceivedId) {
  return containerReceivedRepository.deleteContainerReceived(
    containerReceivedId
  );
}
async function stockDetails(carpet) {
  return containerReceivedRepository.stockDetails(carpet);
}

async function getCompleteStockDetails() {
  return containerReceivedRepository.getCompleteStockDetails();
}
module.exports = {
  createContainerReceived,
  getContainerReceivedById,
  getAllContainerReceived,
  updateContainerReceived,
  deleteContainerReceived,
  stockDetails,
  getCompleteStockDetails // New function exported
};
