const mongoose = require('mongoose');

const addContainerDespatchSchema = new mongoose.Schema({
  impoterNo: {
    type: String,
    required: true,
  },
  chooseAdate: {
    type: String,
    required: true,
  },
  country: {
    type: String,
    required: true,
  },
  currency: {
    type: String,
    required: true,
  },
  containerNumber: {
    type: String,
    required: true,
    unique: true,
  },
  linerDetails: {
    type: String,
    required: true,
  },
  noOfInvoice: {
    type: Number,
    required: true,
  },
  totalNumberOfPcs: {
    type: Number,
    required: true,
  },
  totalQuantity: {
    type: Number,
    required: true,
  },
  totalAmount: {
    type: Number,
    required: true,
  },
});

const AddContainerDespatch = mongoose.model('AddContainerDespatch', addContainerDespatchSchema);

module.exports = AddContainerDespatch;
