const mongoose = require('mongoose');

const deyingRateSchema = new mongoose.Schema({
  name: {
  type: mongoose.Schema.Types.ObjectId,
    ref: 'WeaverEmployee'
  },
  toDate: {
    type: Date,
    required: true
  },
  fromDate: {
    type: Date,
    required: true
  },
  material: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RawMaterialGroup'
  },
  quality: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AddQuality'
  },
  count: {
    type: String,
    required: true
  },
  color: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Color'
  },
  yarnColor:{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RawMaterialGroup'
  },
  rate: {
    type: Number,
    required: true
  },
 
});

const deyingRate = mongoose.model('deyingRate', deyingRateSchema);

module.exports = deyingRate;
