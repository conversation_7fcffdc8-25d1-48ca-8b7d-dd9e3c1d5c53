// repositories/sizeRepository.js
// const Size = require('../model/phase-3/masterSize');

// class SizeRepository {
//   async create(sizeData) {
//     const size = new Size(sizeData);
//     return await size.save();
//   }

//   async findById(id) {
//     return await Size.findById(id);
//   }

//   async findAll() {
//     return await Size.find();
//   }

//   async update(id, sizeData) {
//     return await Size.findByIdAndUpdate(id, sizeData, { new: true });
//   }

//   async delete(id) {
//     return await Size.findByIdAndDelete(id);
//   }
// }

// module.exports = new SizeRepository();
// repositories/sizeRepository.js
const Size = require('../model/phase-3/masterSize');

class SizeRepository {
  async create(sizeData) {
    const size = new Size(sizeData);
    return await size.save();
  }

  async findById(id) {
    return await Size.findById(id);
  }

  async getAllSizes() {
    return await Size.find();
  }

  async updateSize(id, sizeData) {
    return await Size.findByIdAndUpdate(id, sizeData, { new: true });
  }

  async deleteSize(id) {
    return await Size.findByIdAndDelete(id);
  }
}

module.exports = new SizeRepository();
