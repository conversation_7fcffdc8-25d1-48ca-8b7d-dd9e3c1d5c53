const jwt = require('jsonwebtoken');

const secretKey = 'your_secret_key'; // Replace with your secret key

function createToken(payload) {
    return jwt.sign(payload, secretKey, { expiresIn: '1h' });
}

function verifyToken(token) {
    try {
        const decoded = jwt.verify(token, secretKey);
        return decoded;
    } catch (error) {
        throw new Error('Invalid token');
    }
}

module.exports = { createToken, verifyToken };
