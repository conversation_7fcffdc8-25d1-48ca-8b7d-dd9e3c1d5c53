const buyerOrderRepository = require('../../repositories/manifacturing/buyerOrder-repository');

class BuyerOrderService {
  async createBuyerOrder(buyerOrder) {
    return await buyerOrderRepository.create(buyerOrder);
  }

  async getBuyerOrderById(id) {
    return await buyerOrderRepository.findById(id);
  }

  async getAllBuyerOrders() {
    return await buyerOrderRepository.findAll();
  }

  async updateBuyerOrder(id, buyerOrder) {
    return await buyerOrderRepository.update(id, buyerOrder);
  }

  async deleteBuyerOrder(id) {
    return await buyerOrderRepository.delete(id);
  }
  async deleteItem(id) {
    return await buyerOrderRepository.deleteItem(id);
  }

  async updateBuyerOrderField(id, field, value) {
    return await buyerOrderRepository.updateField(id, field, value);
  }

  async deleteBuyerOrderField(id, field) {
    return await buyerOrderRepository.deleteField(id, field);
  }
  calculateTotalIssued(buyerOrder) {
    return buyerOrder.buyerorder.reduce((total, order) => {
      return total + order.items.reduce((subTotal, item) => {
        return subTotal + (item.issued || 0);
      }, 0);
    }, 0);
  }

  calculateTotalCanceled(buyerOrder) {
    return buyerOrder.buyerorder.reduce((total, order) => {
      return total + order.items.reduce((subTotal, item) => {
        return subTotal + (item.canceled || 0);
      }, 0);
    }, 0);
  }

  calculateTotalStock(buyerOrder) {
    return buyerOrder.buyerorder.reduce((total, order) => {
      return total + order.items.reduce((subTotal, item) => {
        return subTotal + (item.pcs || 0) - (item.issued || 0) - (item.canceled || 0);
      }, 0);
    }, 0);
  }

  calculateTotalOrdered(buyerOrder) {
    return buyerOrder.buyerorder.reduce((total, order) => {
      return total + order.items.reduce((subTotal, item) => {
        return subTotal + (item.pcs || 0);
      }, 0);
    }, 0);
  }

  addDynamicFields(buyerOrder) {
    if (!buyerOrder) return buyerOrder;

    buyerOrder.totalIssue = this.calculateTotalIssued(buyerOrder);
    buyerOrder.totalCancel = this.calculateTotalCanceled(buyerOrder);
    buyerOrder.totalStock = this.calculateTotalStock(buyerOrder);
    buyerOrder.totalOrdered = this.calculateTotalOrdered(buyerOrder);

    return buyerOrder;
  }
}

module.exports = new BuyerOrderService();
