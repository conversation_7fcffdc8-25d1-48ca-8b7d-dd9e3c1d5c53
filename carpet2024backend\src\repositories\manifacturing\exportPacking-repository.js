const mongoose = require('mongoose');
const ExportPackingList = require('../../model/phase-4/exportPacking');

/**
 * Repository for Export Packing List operations
 */
class ExportPackingRepository {
  /**
   * Create a new export packing list
   * @param {Object} data - Export packing list data
   * @returns {Promise<Object>} - Created export packing list
   */
  async createExportPacking(data) {
    try {
      console.log('Creating export packing list with data:', JSON.stringify(data, null, 2));

      // Ensure data is valid
      if (!data) {
        throw new Error('No data provided');
      }

      // Validate required fields
      const requiredFields = ['invoiceNo', 'baleNo', 'date', 'items', 'totalArea', 'totalTArea', 'totalPcses'];
      for (const field of requiredFields) {
        if (!data[field]) {
          throw new Error(`${field} is required`);
        }
      }

      // Validate invoiceNo is a valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(data.invoiceNo)) {
        throw new Error('Invalid invoiceNo: Must be a valid ObjectId');
      }

      // Ensure items array exists and is valid
      if (!data.items || !Array.isArray(data.items)) {
        throw new Error('Items must be a valid array');
      }

      // Make sure each item has a baleNo and all required fields
      if (data.items && Array.isArray(data.items)) {
        data.items = data.items.map(item => {
          if (!item) {
            throw new Error('Item cannot be null or undefined');
          }

          // Ensure baleNo is consistent across all items
          const baleNo = item.baleNo || data.baleNo;

          return {
            ...item,
            pcsNo: String(item.pcsNo || ''),
            baleNo: baleNo,
            quality: item.quality || '',
            design: item.design || '',
            colour: item.colour || '',
            size: item.size || '',
            area: parseFloat(item.area || 0),
            tArea: parseFloat(item.tArea || 0),
            remarks: item.remarks || '',
            areaIn: item.areaIn === 'Sq.Meter' ? 'Sq.Meter' : 'Sq.Feet', // Only allow Sq.Feet or Sq.Meter
            addedAt: item.addedAt || new Date()
          };
        });
      }

      // Calculate totalArea and totalTArea if not provided
      let calculatedTotalArea = data.totalArea;
      let calculatedTotalTArea = data.totalTArea;

      if ((!calculatedTotalArea || !calculatedTotalTArea) && data.items && data.items.length > 0) {
        const totalArea = data.items.reduce((sum, item) => sum + (parseFloat(item.area) || 0), 0);
        const totalTArea = data.items.reduce((sum, item) => sum + (parseFloat(item.tArea) || 0), 0);

        calculatedTotalArea = calculatedTotalArea || totalArea;
        calculatedTotalTArea = calculatedTotalTArea || totalTArea;
      }

      const exportPackingList = new ExportPackingList({
        invoiceNo: data.invoiceNo,
        baleNo: data.baleNo,
        date: data.date || new Date(),
        items: data.items,
        totalArea: calculatedTotalArea || 0,
        totalTArea: calculatedTotalTArea || 0,
        totalPcses: data.totalPcses || (data.items ? data.items.length : 0),
        createdAt: data.createdAt || new Date()
      });

      return await exportPackingList.save();
    } catch (error) {
      console.error('Error creating export packing list:', error);
      throw error;
    }
  }

  /**
   * Get all export packing lists
   * @returns {Promise<Array>} - Array of export packing lists
   */
  async getAllExportPackings() {
    return await ExportPackingList.find().sort({ createdAt: -1 });
  }

  /**
   * Get export packing list by ID
   * @param {string} id - Export packing list ID
   * @returns {Promise<Object>} - Export packing list
   */
  async getExportPackingById(id) {
    return await ExportPackingList.findById(id);
  }

  /**
   * Get export packing lists by invoice number
   * @param {string} invoiceNo - Invoice number
   * @returns {Promise<Array>} - Array of export packing lists
   */
  async getExportPackingsByInvoiceNo(invoiceNo) {
    return await ExportPackingList.find({ invoiceNo });
  }

  /**
   * Update export packing list by ID
   * @param {string} id - Export packing list ID
   * @param {Object} data - Updated export packing list data
   * @returns {Promise<Object>} - Updated export packing list
   */
  async updateExportPacking(id, data) {
    try {
      // Validate invoiceNo if provided
      if (data.invoiceNo && !mongoose.Types.ObjectId.isValid(data.invoiceNo)) {
        throw new Error('Invalid invoiceNo: Must be a valid ObjectId');
      }

      // Make sure each item has a baleNo and all required fields
      if (data.items && Array.isArray(data.items)) {
        data.items = data.items.map(item => {
          // Ensure baleNo is consistent across all items
          const baleNo = item.baleNo || data.baleNo;

          return {
            ...item,
            pcsNo: String(item.pcsNo || ''),
            baleNo: baleNo,
            quality: item.quality || '',
            design: item.design || '',
            colour: item.colour || '',
            size: item.size || '',
            area: parseFloat(item.area || 0),
            tArea: parseFloat(item.tArea || 0),
            remarks: item.remarks || '',
            areaIn: item.areaIn === 'Sq.Meter' ? 'Sq.Meter' : 'Sq.Feet', // Only allow Sq.Feet or Sq.Meter
            addedAt: item.addedAt || new Date()
          };
        });
      }

      return await ExportPackingList.findByIdAndUpdate(id, data, { new: true });
    } catch (error) {
      console.error('Error updating export packing list:', error);
      throw error;
    }
  }

  /**
   * Delete export packing list by ID
   * @param {string} id - Export packing list ID
   * @returns {Promise<Object>} - Deleted export packing list
   */
  async deleteExportPacking(id) {
    return await ExportPackingList.findByIdAndDelete(id);
  }
}

module.exports = new ExportPackingRepository();