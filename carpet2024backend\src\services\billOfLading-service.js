const billOfLadingRepository = require('../repositories/billOfLading-repository');

class BillOfLadingService {
  async getAllBillOfLadings() {
    try {
      return await billOfLadingRepository.getAllBillOfLadings();
    } catch (error) {
      throw error;
    }
  }

  async getBillOfLadingById(id) {
    try {
      return await billOfLadingRepository.getBillOfLadingById(id);
    } catch (error) {
      throw error;
    }
  }

  async createBillOfLading(billOfLadingData) {
    try {
      return await billOfLadingRepository.createBillOfLading(billOfLadingData);
    } catch (error) {
      throw error;
    }
  }

  async updateBillOfLading(id, updatedData) {
    try {
      return await billOfLadingRepository.updateBillOfLading(id, updatedData);
    } catch (error) {
      throw error;
    }
  }

  async deleteBillOfLading(id) {
    try {
      return await billOfLadingRepository.deleteBillOfLading(id);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new BillOfLadingService();
