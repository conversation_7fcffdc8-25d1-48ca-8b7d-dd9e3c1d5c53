
.invoice-table {
  width: 100%;

}




.invoice-table {
  display: flex;
  justify-content: flex-end;
}

.table-colour{
  border-color:white;
}




.right-align {
  text-align: right;
}

 .line-above {
  border-top: 1px solid black;
  width: 100px;
}

.line-bottom{
  border-bottom: 2px solid black;

}



.bb{
  margin-top: 600px;

}

.text-hd-n{
  display: flex;
  gap: 20px;
  width: 100%;
  font-size: 17px;
}
.hd-text h4{
  width: 100%;
}
.card1{
  border: none;
}
 .line1 ul li {
  display: inline;
  font-size: 15px;
}
.line1 ul {
  display: flex;
  gap: 40px;
}
span{

}

.bill ul {
  display: flex;
  gap: 60px;
}
.mdle-hd {

  display: flex;
  justify-content: space-between;
}

.md-hd1 h6 {
  margin-left: 24px;
}



.space1{
  min-height: 40vh;
}

.footer ul li {
  display: inline;
}
.footer ul{
 display: flex;
 gap: 215px;
}

.footer11{
  display: flex;
}
.footer12 ul{
 list-style: none;
}
.footer12 ul li{
  font-size: 10px;
}

@media (max-width: 767px) {
  .footer11{
      display: flex;
  }

}



.second-page11 {
  display: flex;
  justify-content: space-between;
}

.second-page{
  margin-top: 180px;
}

.card-text{
  display: flex;
  justify-content: space-between;
}
.second-text22{
  display: flex;
  gap: 20px;
}


.footer{
  display: flex;
  gap: 18%;
  height: auto !important;
    margin-top: 5px !important;
}

.line {border: 1px solid #000;   margin-right: 30px;}


.counter-heading-text{
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.counter-heading-text1{

  width: 24%;

}
.counter-heading-text2{

  width: 100%;
  line-height: 10px;

}
.counter-heading-text3{

  width: 25%;

}
.counter-heading-text3 p{
 font-size: 15px;
}
.counter-heading-text4{

  width: 25%;

}
.counter-heading-text4 p{
 font-size: 15px;

}
.counter-heading-text5{

  width: 25%;
}

.counter-heading-text5 p{
  font-size: 15px;

}

.last-counter1{
  display: flex;
  justify-content: space-between;
}
.mdle-hd{
  display: flex;
  gap: 20px;
}
.count{
  border: none;

}
.mat-h5, .mat-typography .mat-h5, .mat-typography h5 {
  font: 400 calc(20px* .83) / 20px Roboto, sans-serif;
  margin: 0 0 12px;
}
@media print {
  .no-print {
    display: none;
  }
}




.textsize{
  font-size: 20px !important;
}

.spacing-row {
  margin-bottom: 15px;
}






.page {
  page-break-after: always;
}

@page {
  margin-bottom: 10px;

}

/* .page:nth-child(n+2)  {
  margin-top: 100px;
  } */



.font1{
  font-size: 25px !important;
}
p{
  font-size: 20px;
}
.h5txt{ font-weight: bold;}


.mt{
  margin-top: 150px;
}
