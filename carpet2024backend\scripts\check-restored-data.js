// Simple script to check restored data
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function main() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    
    // Get total count
    const totalCount = await db.collection('carpetreceiveds').countDocuments();
    console.log(`📊 Total carpet received records: ${totalCount}`);
    
    // Get sample records
    const samples = await db.collection('carpetreceiveds').find().limit(10).toArray();
    
    console.log('\n📋 Sample records:');
    samples.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo} - Area: ${record.area} - Amount: ${record.amount} - Date: ${record.receivingDate?.toISOString()?.split('T')[0]}`);
    });
    
    // Check RCV- records
    const rcvCount = await db.collection('carpetreceiveds').find({
      receiveNo: { $regex: /^RCV-/i }
    }).countDocuments();
    
    console.log(`\n🔍 Records with RCV- prefix: ${rcvCount}`);
    
    // Show some RCV- records
    if (rcvCount > 0) {
      const rcvSamples = await db.collection('carpetreceiveds').find({
        receiveNo: { $regex: /^RCV-/i }
      }).limit(5).toArray();
      
      console.log('\n📋 Sample RCV- records:');
      rcvSamples.forEach((record, index) => {
        console.log(`${index + 1}. ${record.receiveNo} - Area: ${record.area} - Amount: ${record.amount}`);
      });
    }
    
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

main();
