// models/retailer.js

const mongoose = require('mongoose');

const retailerSchema = new mongoose.Schema({
    retailerCode: { type: String, required: true },
    retailerStoreName: { type: String, required: true },
    contactPerson: { type: String, required: true },
    contactPersonNumber: { type: String, required: true },
    address: { type: String, required: true },
    zipcode: { type: String, required: true },
    country: { type: String, required: true },
    contactNumber: { type: String, required: true },
    email: { type: String, required: true },
    password: { type: String, required: true },
    bankDetails: {
        bankName: { type: String, required: true },
        accountNumber: { type: String, required: true },
        bankSwiftCode: { type: String, required: true },
        bankAddress: { type: String, required: true },
        bankEmail: { type: String, required: true },
        bankContactNumber: { type: String, required: true }
    },
    addOnPrice: {
        toDate:{type: String},
        fromDate:{type: String},
        quality:{ type: String},
        design:{type: String},
        orderPrice:{type: Number}
    }
});

const Retailer = mongoose.model('Retailer', retailerSchema);

module.exports = Retailer;
