// pdfRepository.js
const PdfDetails = require('../model/phase-1/pdf');

class PdfRepository {
  async createPdf(pdfData) {
    try {
      return await PdfDetails.create(pdfData);
    } catch (error) {
      throw error;
    }
  }

  async getAllPdf() {
    try {
      return await PdfDetails.find({});
    } catch (error) {
      throw error;
    }
  }

  async getPdfById(pdfId) {
    try {
      return await PdfDetails.findById(pdfId);
    } catch (error) {
      throw error;
    }
  }

  async updatePdf(pdfId, newData) {
    try {
      return await PdfDetails.findByIdAndUpdate(pdfId, newData, { new: true });
    } catch (error) {
      throw error;
    }
  }

  async deletePdf(pdfId) {
    try {
      return await PdfDetails.findByIdAndDelete(pdfId);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new PdfRepository();
