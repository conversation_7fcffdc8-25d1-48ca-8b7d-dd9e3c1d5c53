import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';

import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MasterService } from '../../../services/master.service';
import Swal from 'sweetalert2';
import { environment } from '../../../../environments/environment.development';
import { Global } from '../../../Shared/validators';

export interface UserData {
  aadhaarDetails: string;
  panDetails: string;
  epfoDetails: string;
  esicDetails: string;
  id: string;
  index: number;
  branch:string;
  groupName: string;
  name: string;
  address: string;
  emailId: string;
  zipCode: string;
  contactNo: string;
  country: string;
  aadhaarNo: string;
  panNo: string;
  epfoNo: string;
  esicNo: string;
  bankAccountNo: string;
  ifscCode: string;
  bankName: string;
  tds: string;
  commission: string;

}

const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-weaver-employee',
  templateUrl: './weaver-employee.component.html',
  styleUrl: './weaver-employee.component.css',
})
export class WeaverEmployeeComponent implements OnInit, AfterViewInit {
  Group = 'Weaver/Contractor';
  groupList: any = [
    'Weaver/Contractor',
    'Dyers',
    'Finisher',
    'Staff',
    'Map Designer',
  ];
  hide = true;
  isUpdated: boolean = false;
  ids!: string;
  displayedColumns: string[] = [
    'id',
    'branch',
    'groupName',
    'name',
    'address',
    'emailId',
    'zipCode',
    'contactNo',
    'country',
    'bankAccountNo',
    'ifscCode',
    'tds',
    'commission',
    'bankName',
    'aadharNo',
    'aadhaarDetails',
    'panNo',
    'panDetails',
    'epfoNo',
    'epfoDetails',
    'esicNo',
    'esicDetails',
    'action',
  ];
  dataSource = new MatTableDataSource(ELEMENT_DATA);

  frmWeaverEmployee!: FormGroup;
  constructor(private service: MasterService, private fb: FormBuilder) {}
  ngOnInit(): void {
    this.frmWeaverEmployee = this.fb.group({
      branch:[],
      groupName: ['',[Validators.required]],
      name: ['',[Validators.required]],
      address: ['',[Validators.required]],
      zipCode: ['',[Validators.required]],
      aadhaarNo: ['',[ Validators.required,]],
      country: ['',[Validators.required]],
      panNo: ['',],
      bankName:[,[Validators.required]],
      ifscCode:[,[Validators.required]],
      bankAccountNo:[,[Validators.required]],
      epfoNo: [''],
      esicNo: [''],
      emailId: ['',[ Validators.required,Validators.pattern(Global.emailRegex)]],
      password: ['',[ Validators.required]],
      contactNo: ['',[ Validators.required,Validators.pattern(Global.contactRegex)]],
      tds: ['', [Validators.required]],
      commission: ['', [Validators.required]]
    });

    // Add value change listeners for TDS and Commission fields
    this.frmWeaverEmployee.get('tds')?.valueChanges.subscribe(value => {
      this.formatPercentageField('tds', value);
    });

    this.frmWeaverEmployee.get('commission')?.valueChanges.subscribe(value => {
      this.formatPercentageField('commission', value);
    });

    this.getsWeaverEmployee();
    this.getAllBranch();
  }

  // Format percentage fields to always show % symbol
  formatPercentageField(fieldName: string, value: any): void {
    if (value === null || value === undefined || value === '') return;

    // If the user is typing, don't interfere
    if (this.isUserTyping) return;

    this.isUserTyping = true;

    // Remove any existing % symbol and trim whitespace
    let numericValue = value.toString().replace(/[^\d.]/g, '').trim();

    // If empty after cleaning, return empty
    if (numericValue === '') {
      this.frmWeaverEmployee.get(fieldName)?.setValue('', { emitEvent: false });
      this.isUserTyping = false;
      return;
    }

    // Format with % symbol only if there's a value
    const formattedValue = numericValue + ' %';

    // Update the form control without triggering another valueChanges event
    this.frmWeaverEmployee.get(fieldName)?.setValue(formattedValue, { emitEvent: false });

    setTimeout(() => {
      this.isUserTyping = false;
    }, 10);
  }

  // Handle focus event on percentage fields
  onPercentageFieldFocus(fieldName: string): void {
    const control = this.frmWeaverEmployee.get(fieldName);
    if (!control) return;

    // Get current value
    let value = control.value;
    if (!value) {
      // Leave it blank if it's empty
      return;
    }

    // Remove % symbol and spaces for editing
    const numericValue = value.toString().replace(/[^\d.]/g, '').trim();

    // Set the numeric value for editing
    this.isUserTyping = true;
    control.setValue(numericValue, { emitEvent: false });
    setTimeout(() => {
      this.isUserTyping = false;
    }, 10);
  }

  // Handle blur event on percentage fields
  onPercentageFieldBlur(fieldName: string): void {
    const control = this.frmWeaverEmployee.get(fieldName);
    if (!control) return;

    // Get current value
    let value = control.value;

    // Format with % symbol
    this.formatPercentageField(fieldName, value);
  }

  // Flag to prevent infinite loops in valueChanges
  isUserTyping: boolean = false;

  aadharFile: any;
  panFile: any;
  epfoFile: any;
  esicFile: any;

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  handleFileInput(event: any) {
    const inputId = event.target.id;
    const files = event.target.files;

    switch (inputId) {
      case 'aadharFile':
        this.aadharFile = files;
        break;
      case 'panFile':
        this.panFile = files;
        break;
      case 'epfoFile':
        this.epfoFile = files;
        break;
      case 'esicFile':
        this.esicFile = files;
        break;
      default:
        console.error('Unknown file input');
    }


  }

  prepareFormData(frmData: any): FormData {
    let formData = new FormData();

    formData.append('branch', frmData.branch);
    formData.append('groupName', frmData.groupName);
    formData.append('name', frmData.name);
    formData.append('address', frmData.address);
    formData.append('zipcode', frmData.zipCode);
    formData.append('aadhaarNo', frmData.aadhaarNo);
    formData.append('panNo', frmData.panNo);
    formData.append('epfoNo', frmData.epfoNo);
    formData.append('esicNo', frmData.esicNo);
    formData.append('emailId', frmData.emailId);
    formData.append('password', frmData.password);
    formData.append('country', frmData.country);
    formData.append('contactNo', frmData.contactNo);
    formData.append('bankAccountNo', frmData.bankAccountNo);
    formData.append('ifscCode', frmData.ifscCode);
    // Extract numeric values from percentage fields
    const tdsValue = frmData.tds ? frmData.tds.toString().replace(/[^\d.]/g, '') : '';
    const commissionValue = frmData.commission ? frmData.commission.toString().replace(/[^\d.]/g, '') : '';

    // Use 0 as default if empty
    formData.append('tds', tdsValue || '0');
    formData.append('commission', commissionValue || '0');
    formData.append('bankName', frmData.bankName);

    if (this.aadharFile && this.aadharFile.length > 0) {
      formData.append('aadhaarFile', this.aadharFile[0]);
    }
    if (this.panFile && this.panFile.length > 0) {
      formData.append('panFile', this.panFile[0]);
    }
    if (this.epfoFile && this.epfoFile.length > 0) {
      formData.append('epfoFile', this.epfoFile[0]);
    }
    if (this.esicFile && this.esicFile.length > 0) {
      formData.append('esicFile', this.esicFile[0]);
    }

    return formData;
  }

  addWeaver() {
    console.log(this.frmWeaverEmployee.value);
    let frmData = this.frmWeaverEmployee.value;

    let formData = this.prepareFormData(frmData);


    this.service.addWeaverEmployee(formData).subscribe({
      next: (value) => {
        if (value) {
          Swal.fire({
            title: 'Success',
            text: 'Successfully Added!',
            icon: 'success',
          });


          this.getsWeaverEmployee();
        }
      },
      error: (err) => {
        Swal.fire({
          title: 'Error',
          text: err.message,
          icon: 'warning',
        });
      },
    });
  }

  updateWeaver() {
    console.log(this.frmWeaverEmployee.value);
    let frmData = this.frmWeaverEmployee.value;

    let formData = this.prepareFormData(frmData);

    this.service.updateWeaverEmployee(this.ids, formData).subscribe({
      next: (value) => {
        if (value) {
          Swal.fire({
            title: 'Success',
            text: 'Successfully Updated!',
            icon: 'success',
          });
          this.getsWeaverEmployee();
        }
      },
      error: (err) => {
        Swal.fire({
          title: 'Error',
          text: err.message,
          icon: 'warning',
        });
      },
    });
  }

  // addWeaver() {
  //   console.log(this.frmWeaverEmployee.value);
  //   let frmData = this.frmWeaverEmployee.value;

  //   let formData = new FormData();

  //   formData.append('groupName', frmData.groupName);
  //   formData.append('name', frmData.name);
  //   formData.append('address', frmData.address);
  //   formData.append('zipcode', frmData.zipCode);
  //   formData.append('aadhaarNo', frmData.aadhaarNo);
  //   formData.append('panNo', frmData.panNo);
  //   formData.append('epfoNo', frmData.epfoNo);
  //   formData.append('esicNo', frmData.esicNo);
  //   formData.append('emailId', frmData.emailId);
  //   formData.append('password', frmData.password);
  //   formData.append('country', frmData.country);
  //   formData.append('contactNo', frmData.contactNo);

  //   if (this.aadharFile && this.aadharFile.length > 0) {
  //     formData.append('aadhaarFile', this.aadharFile[0]);
  //   }
  //   if (this.panFile && this.panFile.length > 0) {
  //     formData.append('panFile', this.panFile[0]);
  //   }
  //   if (this.epfoFile && this.epfoFile.length > 0) {
  //     formData.append('epfoFile', this.epfoFile[0]);
  //   }
  //   if (this.esicFile && this.esicFile.length > 0) {
  //     formData.append('esicFile', this.esicFile[0]);
  //   }
  //   this.service.addWeaverEmployee(formData).subscribe({
  //     next: (value) => {
  //       if (value) {
  //         Swal.fire({
  //           title: 'Success',
  //           text: 'Successfully Added!',
  //           icon: 'success',
  //         });
  //         this.getsWeaverEmployee();
  //       }
  //     },
  //     error(err) {
  //       Swal.fire({
  //         title: 'Success',
  //         text: err.message,
  //         icon: 'warning',
  //       });
  //     },
  //   });
  // }
  // updateWeaver(){
  //   console.log(this.frmWeaverEmployee.value);
  //   let frmData = this.frmWeaverEmployee.value;

  //   let formData = new FormData();

  //   formData.append('groupName', frmData.groupName);
  //   formData.append('name', frmData.name);
  //   formData.append('address', frmData.address);
  //   formData.append('zipcode', frmData.zipCode);
  //   formData.append('aadhaarNo', frmData.aadhaarNo);
  //   formData.append('panNo', frmData.panNo);
  //   formData.append('epfoNo', frmData.epfoNo);
  //   formData.append('esicNo', frmData.esicNo);
  //   formData.append('emailId', frmData.emailId);
  //   formData.append('password', frmData.password);
  //   formData.append('country', frmData.country);
  //   formData.append('contactNo', frmData.contactNo);

  //   if (this.aadharFile && this.aadharFile.length > 0) {
  //     formData.append('aadhaarFile', this.aadharFile[0]);
  //   }
  //   if (this.panFile && this.panFile.length > 0) {
  //     formData.append('panFile', this.panFile[0]);
  //   }
  //   if (this.epfoFile && this.epfoFile.length > 0) {
  //     formData.append('epfoFile', this.epfoFile[0]);
  //   }
  //   if (this.esicFile && this.esicFile.length > 0) {
  //     formData.append('esicFile', this.esicFile[0]);
  //   }

  //   this.service.updateWeaverEmployee(this.ids,formData).subscribe({
  //     next: (value) => {
  //       if (value) {
  //         Swal.fire({
  //           title: 'Success',
  //           text: 'Successfully Updated!',
  //           icon: 'success',
  //         });
  //         this.getsWeaverEmployee();
  //       }
  //     },
  //     error(err) {
  //       Swal.fire({
  //         title: 'Success',
  //         text: err.message,
  //         icon: 'warning',
  //       });
  //     },
  //   });
  // }
  getsWeaverEmployee() {
    this.service.getAllWeaverEmployee().subscribe({
      next: (value: any) => {
        // Use arrow function to preserve 'this'
        if (value) {
          ELEMENT_DATA.length = 0;

          value.map((v: any, i: number) => {

            ELEMENT_DATA.push({
              id: v._id,
              index: i + 1,
               branch:v.branch,
              // branch:v.brancCode + '  ' + v.branchName,
              groupName: v.groupName,
              name: v.name,
              address: v.address,
              emailId: v.emailId,
              zipCode: v.zipcode,
              contactNo: v.contactNo,
              country: v.country,
              bankAccountNo: v.bankAccountNo,
              ifscCode: v.ifscCode,
              tds: v.tds,
              commission: v.commission,
              bankName: v.bankName,
              aadhaarNo: v.aadhaarDetails.aadhaarNo,
              aadhaarDetails: v.aadhaarDetails?.aadhaarFile
                ? environment.imageUrl + v.aadhaarDetails?.aadhaarFile
                : '',
              panNo: v.panDetails.panNo,
              panDetails: v.panDetails?.panFile
                ? environment.imageUrl + v.panDetails?.panFile
                : '',
              epfoNo: v.epfoDetails.epfoNo,
              epfoDetails: v.epfoDetails?.epfoFile
                ? environment.imageUrl + v.epfoDetails?.epfoFile
                : '',
              esicNo: v.esicDetails.esicNo,
              esicDetails: v.esicDetails?.esicFile
                ? environment.imageUrl + v.esicDetails?.esicFile
                : '',
            });
          });
          this.dataSource = new MatTableDataSource(ELEMENT_DATA);

          // this.ngAfterViewInit();
          // return;
        }
      },
      error: (err) => {
        console.error('Error fetching data', err);
      },
    });
  }

  viewPdf(url: any) {
    window.open(url, '_blank');
  }

  edit(data: any) {
    this.ids = data.id;
    this.isUpdated = true;

   this.service.getWeaverEmployee(this.ids).subscribe((res:any)=>{
    debugger
     this.frmWeaverEmployee.patchValue(
      {

        branch:res.branch,
        groupName:res.groupName,
        name:res.name,
        address:res.address,
        zipCode:res.zipcode,
        aadhaarNo:res.aadhaarDetails.aadhaarNo,
        country:res.country,
        panNo:res.panDetails.panNo,
        tds: res.tds && res.tds !== '0' ? res.tds + ' %' : '',
        commission: res.commission && res.commission !== '0' ? res.commission + ' %' : '',
        bankName:res.bankName,
        ifscCode:res.ifscCode,
        bankAccountNo:res.bankAccountNo,
        epfoNo:res.epfoDetails.epfoNo,
        esicNo:res.esicDetails.esicNo,
        emailId:res.emailId,
        emailRegex:res.emailRegex,
        password:res.password,
        contactNo:res.contactNo,
      }
     );
   })


  }
  delete(id: string) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        this.service.deleteWeaverEmployee(id).subscribe({
          next: (res) => {
            if (res) {
              Swal.fire({
                title: 'Deleted!',
                text: 'Your file has been deleted.',
                icon: 'success',
              });

              this.getsWeaverEmployee();
            }
          },
          error(err) {
            Swal.fire({
              title: 'Failed!',
              text: err,
              icon: 'warning',
            });
          },
        });
      }
    });
  }

  branchList:any[]=[]

  getAllBranch(){
   this.service.getsBranch().subscribe((res:any)=>{

    res.map((x:any)=>{
      this.branchList.push({
        id:x._id,
        branchCode:x.branchCode,
        branchName:x.branchName,
      })
    })
   })
  }

}
