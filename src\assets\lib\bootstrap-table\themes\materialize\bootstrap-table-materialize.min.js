/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var e=n(t);function r(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function o(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,n){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},u(t,n)}function c(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var e,r=i(t);if(n){var o=i(this).constructor;e=Reflect.construct(r,arguments,o)}else e=r.apply(this,arguments);return c(this,e)}}function f(t,n){for(;!Object.prototype.hasOwnProperty.call(t,n)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,n,e){var r=f(t,n);if(r){var o=Object.getOwnPropertyDescriptor(r,n);return o.get?o.get.call(arguments.length<3?t:e):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},p=function(t){return t&&t.Math==Math&&t},d=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof s&&s)||function(){return this}()||Function("return this")(),y={},h=function(t){try{return!!t()}catch(t){return!0}},b=!h((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),g=!h((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),v=g,m=Function.prototype.call,w=v?m.bind(m):function(){return m.apply(m,arguments)},O={},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,P=S&&!j.call({1:2},1);O.f=P?function(t){var n=S(this,t);return!!n&&n.enumerable}:j;var T,E,x=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},A=g,D=Function.prototype,_=D.call,C=A&&D.bind.bind(_,_),I=function(t){return A?C(t):function(){return _.apply(t,arguments)}},R=I,k=R({}.toString),F=R("".slice),M=function(t){return F(k(t),8,-1)},L=M,z=I,G=function(t){if("Function"===L(t))return z(t)},N=h,B=M,W=Object,$=G("".split),q=N((function(){return!W("z").propertyIsEnumerable(0)}))?function(t){return"String"==B(t)?$(t,""):W(t)}:W,H=function(t){return null==t},U=H,X=TypeError,K=function(t){if(U(t))throw X("Can't call method on "+t);return t},Q=q,V=K,Y=function(t){return Q(V(t))},J="object"==typeof document&&document.all,Z={all:J,IS_HTMLDDA:void 0===J&&void 0!==J},tt=Z.all,nt=Z.IS_HTMLDDA?function(t){return"function"==typeof t||t===tt}:function(t){return"function"==typeof t},et=nt,rt=Z.all,ot=Z.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:et(t)||t===rt}:function(t){return"object"==typeof t?null!==t:et(t)},it=d,ut=nt,ct=function(t){return ut(t)?t:void 0},at=function(t,n){return arguments.length<2?ct(it[t]):it[t]&&it[t][n]},ft=G({}.isPrototypeOf),lt=d,st=at("navigator","userAgent")||"",pt=lt.process,dt=lt.Deno,yt=pt&&pt.versions||dt&&dt.version,ht=yt&&yt.v8;ht&&(E=(T=ht.split("."))[0]>0&&T[0]<4?1:+(T[0]+T[1])),!E&&st&&(!(T=st.match(/Edge\/(\d+)/))||T[1]>=74)&&(T=st.match(/Chrome\/(\d+)/))&&(E=+T[1]);var bt=E,gt=h,vt=!!Object.getOwnPropertySymbols&&!gt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&bt&&bt<41})),mt=vt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,wt=at,Ot=nt,jt=ft,St=Object,Pt=mt?function(t){return"symbol"==typeof t}:function(t){var n=wt("Symbol");return Ot(n)&&jt(n.prototype,St(t))},Tt=String,Et=nt,xt=function(t){try{return Tt(t)}catch(t){return"Object"}},At=TypeError,Dt=function(t){if(Et(t))return t;throw At(xt(t)+" is not a function")},_t=Dt,Ct=H,It=w,Rt=nt,kt=ot,Ft=TypeError,Mt={exports:{}},Lt=d,zt=Object.defineProperty,Gt=function(t,n){try{zt(Lt,t,{value:n,configurable:!0,writable:!0})}catch(e){Lt[t]=n}return n},Nt=Gt,Bt="__core-js_shared__",Wt=d[Bt]||Nt(Bt,{}),$t=Wt;(Mt.exports=function(t,n){return $t[t]||($t[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var qt=K,Ht=Object,Ut=function(t){return Ht(qt(t))},Xt=Ut,Kt=G({}.hasOwnProperty),Qt=Object.hasOwn||function(t,n){return Kt(Xt(t),n)},Vt=G,Yt=0,Jt=Math.random(),Zt=Vt(1..toString),tn=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Zt(++Yt+Jt,36)},nn=d,en=Mt.exports,rn=Qt,on=tn,un=vt,cn=mt,an=en("wks"),fn=nn.Symbol,ln=fn&&fn.for,sn=cn?fn:fn&&fn.withoutSetter||on,pn=function(t){if(!rn(an,t)||!un&&"string"!=typeof an[t]){var n="Symbol."+t;un&&rn(fn,t)?an[t]=fn[t]:an[t]=cn&&ln?ln(n):sn(n)}return an[t]},dn=w,yn=ot,hn=Pt,bn=function(t,n){var e=t[n];return Ct(e)?void 0:_t(e)},gn=function(t,n){var e,r;if("string"===n&&Rt(e=t.toString)&&!kt(r=It(e,t)))return r;if(Rt(e=t.valueOf)&&!kt(r=It(e,t)))return r;if("string"!==n&&Rt(e=t.toString)&&!kt(r=It(e,t)))return r;throw Ft("Can't convert object to primitive value")},vn=TypeError,mn=pn("toPrimitive"),wn=function(t,n){if(!yn(t)||hn(t))return t;var e,r=bn(t,mn);if(r){if(void 0===n&&(n="default"),e=dn(r,t,n),!yn(e)||hn(e))return e;throw vn("Can't convert object to primitive value")}return void 0===n&&(n="number"),gn(t,n)},On=Pt,jn=function(t){var n=wn(t,"string");return On(n)?n:n+""},Sn=ot,Pn=d.document,Tn=Sn(Pn)&&Sn(Pn.createElement),En=function(t){return Tn?Pn.createElement(t):{}},xn=En,An=!b&&!h((function(){return 7!=Object.defineProperty(xn("div"),"a",{get:function(){return 7}}).a})),Dn=b,_n=w,Cn=O,In=x,Rn=Y,kn=jn,Fn=Qt,Mn=An,Ln=Object.getOwnPropertyDescriptor;y.f=Dn?Ln:function(t,n){if(t=Rn(t),n=kn(n),Mn)try{return Ln(t,n)}catch(t){}if(Fn(t,n))return In(!_n(Cn.f,t,n),t[n])};var zn={},Gn=b&&h((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Nn=ot,Bn=String,Wn=TypeError,$n=function(t){if(Nn(t))return t;throw Wn(Bn(t)+" is not an object")},qn=b,Hn=An,Un=Gn,Xn=$n,Kn=jn,Qn=TypeError,Vn=Object.defineProperty,Yn=Object.getOwnPropertyDescriptor,Jn="enumerable",Zn="configurable",te="writable";zn.f=qn?Un?function(t,n,e){if(Xn(t),n=Kn(n),Xn(e),"function"==typeof t&&"prototype"===n&&"value"in e&&te in e&&!e.writable){var r=Yn(t,n);r&&r.writable&&(t[n]=e.value,e={configurable:Zn in e?e.configurable:r.configurable,enumerable:Jn in e?e.enumerable:r.enumerable,writable:!1})}return Vn(t,n,e)}:Vn:function(t,n,e){if(Xn(t),n=Kn(n),Xn(e),Hn)try{return Vn(t,n,e)}catch(t){}if("get"in e||"set"in e)throw Qn("Accessors not supported");return"value"in e&&(t[n]=e.value),t};var ne=zn,ee=x,re=b?function(t,n,e){return ne.f(t,n,ee(1,e))}:function(t,n,e){return t[n]=e,t},oe={exports:{}},ie=b,ue=Qt,ce=Function.prototype,ae=ie&&Object.getOwnPropertyDescriptor,fe=ue(ce,"name"),le={EXISTS:fe,PROPER:fe&&"something"===function(){}.name,CONFIGURABLE:fe&&(!ie||ie&&ae(ce,"name").configurable)},se=nt,pe=Wt,de=G(Function.toString);se(pe.inspectSource)||(pe.inspectSource=function(t){return de(t)});var ye,he,be,ge=pe.inspectSource,ve=nt,me=d.WeakMap,we=ve(me)&&/native code/.test(String(me)),Oe=Mt.exports,je=tn,Se=Oe("keys"),Pe=function(t){return Se[t]||(Se[t]=je(t))},Te={},Ee=we,xe=d,Ae=ot,De=re,_e=Qt,Ce=Wt,Ie=Pe,Re=Te,ke="Object already initialized",Fe=xe.TypeError,Me=xe.WeakMap;if(Ee||Ce.state){var Le=Ce.state||(Ce.state=new Me);Le.get=Le.get,Le.has=Le.has,Le.set=Le.set,ye=function(t,n){if(Le.has(t))throw Fe(ke);return n.facade=t,Le.set(t,n),n},he=function(t){return Le.get(t)||{}},be=function(t){return Le.has(t)}}else{var ze=Ie("state");Re[ze]=!0,ye=function(t,n){if(_e(t,ze))throw Fe(ke);return n.facade=t,De(t,ze,n),n},he=function(t){return _e(t,ze)?t[ze]:{}},be=function(t){return _e(t,ze)}}var Ge={set:ye,get:he,has:be,enforce:function(t){return be(t)?he(t):ye(t,{})},getterFor:function(t){return function(n){var e;if(!Ae(n)||(e=he(n)).type!==t)throw Fe("Incompatible receiver, "+t+" required");return e}}},Ne=h,Be=nt,We=Qt,$e=b,qe=le.CONFIGURABLE,He=ge,Ue=Ge.enforce,Xe=Ge.get,Ke=Object.defineProperty,Qe=$e&&!Ne((function(){return 8!==Ke((function(){}),"length",{value:8}).length})),Ve=String(String).split("String"),Ye=oe.exports=function(t,n,e){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),e&&e.getter&&(n="get "+n),e&&e.setter&&(n="set "+n),(!We(t,"name")||qe&&t.name!==n)&&($e?Ke(t,"name",{value:n,configurable:!0}):t.name=n),Qe&&e&&We(e,"arity")&&t.length!==e.arity&&Ke(t,"length",{value:e.arity});try{e&&We(e,"constructor")&&e.constructor?$e&&Ke(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=Ue(t);return We(r,"source")||(r.source=Ve.join("string"==typeof n?n:"")),t};Function.prototype.toString=Ye((function(){return Be(this)&&Xe(this).source||He(this)}),"toString");var Je=nt,Ze=zn,tr=oe.exports,nr=Gt,er=function(t,n,e,r){r||(r={});var o=r.enumerable,i=void 0!==r.name?r.name:n;if(Je(e)&&tr(e,i,r),r.global)o?t[n]=e:nr(n,e);else{try{r.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=e:Ze.f(t,n,{value:e,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return t},rr={},or=Math.ceil,ir=Math.floor,ur=Math.trunc||function(t){var n=+t;return(n>0?ir:or)(n)},cr=function(t){var n=+t;return n!=n||0===n?0:ur(n)},ar=cr,fr=Math.max,lr=Math.min,sr=cr,pr=Math.min,dr=function(t){return t>0?pr(sr(t),9007199254740991):0},yr=function(t){return dr(t.length)},hr=Y,br=function(t,n){var e=ar(t);return e<0?fr(e+n,0):lr(e,n)},gr=yr,vr=function(t){return function(n,e,r){var o,i=hr(n),u=gr(i),c=br(r,u);if(t&&e!=e){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===e)return t||c||0;return!t&&-1}},mr={includes:vr(!0),indexOf:vr(!1)},wr=Qt,Or=Y,jr=mr.indexOf,Sr=Te,Pr=G([].push),Tr=function(t,n){var e,r=Or(t),o=0,i=[];for(e in r)!wr(Sr,e)&&wr(r,e)&&Pr(i,e);for(;n.length>o;)wr(r,e=n[o++])&&(~jr(i,e)||Pr(i,e));return i},Er=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],xr=Tr,Ar=Er.concat("length","prototype");rr.f=Object.getOwnPropertyNames||function(t){return xr(t,Ar)};var Dr={};Dr.f=Object.getOwnPropertySymbols;var _r=at,Cr=rr,Ir=Dr,Rr=$n,kr=G([].concat),Fr=_r("Reflect","ownKeys")||function(t){var n=Cr.f(Rr(t)),e=Ir.f;return e?kr(n,e(t)):n},Mr=Qt,Lr=Fr,zr=y,Gr=zn,Nr=h,Br=nt,Wr=/#|\.prototype\./,$r=function(t,n){var e=Hr[qr(t)];return e==Xr||e!=Ur&&(Br(n)?Nr(n):!!n)},qr=$r.normalize=function(t){return String(t).replace(Wr,".").toLowerCase()},Hr=$r.data={},Ur=$r.NATIVE="N",Xr=$r.POLYFILL="P",Kr=$r,Qr=d,Vr=y.f,Yr=re,Jr=er,Zr=Gt,to=function(t,n,e){for(var r=Lr(n),o=Gr.f,i=zr.f,u=0;u<r.length;u++){var c=r[u];Mr(t,c)||e&&Mr(e,c)||o(t,c,i(n,c))}},no=Kr,eo=function(t,n){var e,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(e=a?Qr:f?Qr[c]||Zr(c,{}):(Qr[c]||{}).prototype)for(r in n){if(i=n[r],o=t.dontCallGetSet?(u=Vr(e,r))&&u.value:e[r],!no(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;to(i,o)}(t.sham||o&&o.sham)&&Yr(i,"sham",!0),Jr(e,r,i,t)}},ro=Dt,oo=g,io=G(G.bind),uo=M,co=Array.isArray||function(t){return"Array"==uo(t)},ao={};ao[pn("toStringTag")]="z";var fo="[object z]"===String(ao),lo=fo,so=nt,po=M,yo=pn("toStringTag"),ho=Object,bo="Arguments"==po(function(){return arguments}()),go=lo?po:function(t){var n,e,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,n){try{return t[n]}catch(t){}}(n=ho(t),yo))?e:bo?po(n):"Object"==(r=po(n))&&so(n.callee)?"Arguments":r},vo=G,mo=h,wo=nt,Oo=go,jo=ge,So=function(){},Po=[],To=at("Reflect","construct"),Eo=/^\s*(?:class|function)\b/,xo=vo(Eo.exec),Ao=!Eo.exec(So),Do=function(t){if(!wo(t))return!1;try{return To(So,Po,t),!0}catch(t){return!1}},_o=function(t){if(!wo(t))return!1;switch(Oo(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Ao||!!xo(Eo,jo(t))}catch(t){return!0}};_o.sham=!0;var Co=!To||mo((function(){var t;return Do(Do.call)||!Do(Object)||!Do((function(){t=!0}))||t}))?_o:Do,Io=co,Ro=Co,ko=ot,Fo=pn("species"),Mo=Array,Lo=function(t){var n;return Io(t)&&(n=t.constructor,(Ro(n)&&(n===Mo||Io(n.prototype))||ko(n)&&null===(n=n[Fo]))&&(n=void 0)),void 0===n?Mo:n},zo=function(t,n){return ro(t),void 0===n?t:oo?io(t,n):function(){return t.apply(n,arguments)}},Go=q,No=Ut,Bo=yr,Wo=function(t,n){return new(Lo(t))(0===n?0:n)},$o=G([].push),qo=function(t){var n=1==t,e=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,l,s){for(var p,d,y=No(a),h=Go(y),b=zo(f,l),g=Bo(h),v=0,m=s||Wo,w=n?m(a,g):e||u?m(a,0):void 0;g>v;v++)if((c||v in h)&&(d=b(p=h[v],v,y),t))if(n)w[v]=d;else if(d)switch(t){case 3:return!0;case 5:return p;case 6:return v;case 2:$o(w,p)}else switch(t){case 4:return!1;case 7:$o(w,p)}return i?-1:r||o?o:w}},Ho={forEach:qo(0),map:qo(1),filter:qo(2),some:qo(3),every:qo(4),find:qo(5),findIndex:qo(6),filterReject:qo(7)},Uo={},Xo=Tr,Ko=Er,Qo=Object.keys||function(t){return Xo(t,Ko)},Vo=b,Yo=Gn,Jo=zn,Zo=$n,ti=Y,ni=Qo;Uo.f=Vo&&!Yo?Object.defineProperties:function(t,n){Zo(t);for(var e,r=ti(n),o=ni(n),i=o.length,u=0;i>u;)Jo.f(t,e=o[u++],r[e]);return t};var ei,ri=at("document","documentElement"),oi=$n,ii=Uo,ui=Er,ci=Te,ai=ri,fi=En,li=Pe("IE_PROTO"),si=function(){},pi=function(t){return"<script>"+t+"</"+"script>"},di=function(t){t.write(pi("")),t.close();var n=t.parentWindow.Object;return t=null,n},yi=function(){try{ei=new ActiveXObject("htmlfile")}catch(t){}var t,n;yi="undefined"!=typeof document?document.domain&&ei?di(ei):((n=fi("iframe")).style.display="none",ai.appendChild(n),n.src=String("javascript:"),(t=n.contentWindow.document).open(),t.write(pi("document.F=Object")),t.close(),t.F):di(ei);for(var e=ui.length;e--;)delete yi.prototype[ui[e]];return yi()};ci[li]=!0;var hi=pn,bi=Object.create||function(t,n){var e;return null!==t?(si.prototype=oi(t),e=new si,si.prototype=null,e[li]=t):e=yi(),void 0===n?e:ii.f(e,n)},gi=zn.f,vi=hi("unscopables"),mi=Array.prototype;null==mi[vi]&&gi(mi,vi,{configurable:!0,value:bi(null)});var wi=function(t){mi[vi][t]=!0},Oi=eo,ji=Ho.find,Si=wi,Pi="find",Ti=!0;Pi in[]&&Array(1).find((function(){Ti=!1})),Oi({target:"Array",proto:!0,forced:Ti},{find:function(t){return ji(this,t,arguments.length>1?arguments[1]:void 0)}}),Si(Pi);var Ei=go,xi=fo?{}.toString:function(){return"[object "+Ei(this)+"]"};fo||er(Object.prototype,"toString",xi,{unsafe:!0});var Ai=mr.includes,Di=wi;eo({target:"Array",proto:!0,forced:h((function(){return!Array(1).includes()}))},{includes:function(t){return Ai(this,t,arguments.length>1?arguments[1]:void 0)}}),Di("includes");var _i=ot,Ci=M,Ii=pn("match"),Ri=function(t){var n;return _i(t)&&(void 0!==(n=t[Ii])?!!n:"RegExp"==Ci(t))},ki=TypeError,Fi=go,Mi=String,Li=pn("match"),zi=eo,Gi=function(t){if(Ri(t))throw ki("The method doesn't accept regular expressions");return t},Ni=K,Bi=function(t){if("Symbol"===Fi(t))throw TypeError("Cannot convert a Symbol value to a string");return Mi(t)},Wi=function(t){var n=/./;try{"/./"[t](n)}catch(e){try{return n[Li]=!1,"/./"[t](n)}catch(t){}}return!1},$i=G("".indexOf);zi({target:"String",proto:!0,forced:!Wi("includes")},{includes:function(t){return!!~$i(Bi(Ni(this)),Bi(Gi(t)),arguments.length>1?arguments[1]:void 0)}}),e.default.extend(e.default.fn.bootstrapTable.defaults,{classes:"table highlight",buttonsPrefix:"",buttonsClass:"waves-effect waves-light btn"}),e.default.fn.bootstrapTable.theme="materialize",e.default.BootstrapTable=function(t){!function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&u(t,n)}(p,t);var n,c,f,s=a(p);function p(){return r(this,p),s.apply(this,arguments)}return n=p,c=[{key:"initConstants",value:function(){l(i(p.prototype),"initConstants",this).call(this),this.constants.classes.buttonsGroup="button-group",this.constants.classes.buttonsDropdown="",this.constants.classes.input="input-field",this.constants.classes.input="",this.constants.classes.paginationDropdown="",this.constants.classes.buttonActive="green",this.constants.html.toolbarDropdown=['<ul class="dropdown-content">',"</ul>"],this.constants.html.toolbarDropdownItem='<li class="dropdown-item-marker"><label>%s</label></li>',this.constants.html.toolbarDropdownSeparator='<li class="divider" tabindex="-1"></li>',this.constants.html.pageDropdown=['<ul id="pagination-list-id" class="dropdown-content">',"</ul>"],this.constants.html.pageDropdownItem='<li><a class="%s" href="#">%s</a></li>',this.constants.html.dropdownCaret='<i class="material-icons">arrow_drop_down</i>',this.constants.html.pagination=['<ul class="pagination%s">',"</ul>"],this.constants.html.paginationItem='<li class="waves-effect page-item%s" aria-label="%s"><a href="#">%s</a></li>',this.constants.html.icon='<i class="%s">%s</i>',this.constants.html.inputGroup="%s%s"}},{key:"initToolbar",value:function(){l(i(p.prototype),"initToolbar",this).call(this),this.handleToolbar()}},{key:"handleToolbar",value:function(){this.$toolbar.find(".dropdown-toggle").length&&this.$toolbar.find(".dropdown-toggle").each((function(t,n){if(e.default(n).next().length){var r="toolbar-columns-id".concat(t);e.default(n).next().attr("id",r),e.default(n).attr("data-target",r).dropdown({alignment:"right",constrainWidth:!1,closeOnClick:!1})}}))}},{key:"initPagination",value:function(){l(i(p.prototype),"initPagination",this).call(this),this.options.pagination&&this.paginationParts.includes("pageSize")&&this.$pagination.find(".dropdown-toggle").attr("data-target",this.$pagination.find(".dropdown-content").attr("id")).dropdown()}}],c&&o(n.prototype,c),f&&o(n,f),Object.defineProperty(n,"prototype",{writable:!1}),p}(e.default.BootstrapTable)}));
