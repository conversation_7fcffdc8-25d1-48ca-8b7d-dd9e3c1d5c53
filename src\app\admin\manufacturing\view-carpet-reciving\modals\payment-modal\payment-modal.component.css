mat-form-field {
    width: 100%;
    margin: 5px 0;
  }
  
  mat-label {
    color: black;
  }

  .row {
    margin-bottom: 10px;
  }
  
  .col-md-4, .col-md-6, .col-md-3 {
    padding: 0 5px;
  }
  
  mat-dialog-content {
    min-width: 500px;
  }

  .title {
    color: #1f497d;
    display: flex;
    justify-content: center;
    text-decoration: underline;
}
.bank-option {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
}

.bank-name {
  flex: 1;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}

.bank-remove-btn {
  margin-left: 12px !important;
  opacity: 0.6 !important;
  transition: opacity 0.2s !important;
}

.bank-remove-btn:hover {
  opacity: 1 !important;
}

