const mongoose = require('mongoose');

const purchaserDetailsSchema = new mongoose.Schema({
  group: { type: String, required: true },
  subGroup: { type: String, required: true },
  Material: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RawMaterialGroup'
},
  count: { type: Number, required: true },
  partyname: { type: String, required: true },
  address: { type: String, required: true },
  zipcode: { type: String, required: true },
  contractno: { type: String, required: true },
  email: { type: String, required: true },
  gstno: { type: String, required: true },
  panno: { type: String, required: true }
});

const purchaserDetails = mongoose.model('purchaserDetails', purchaserDetailsSchema);

module.exports = purchaserDetails;
