import { Component, Input, OnInit } from '@angular/core';
import { ManufactureService } from '../../../services/manufacture.service';
import { ActivatedRoute } from '@angular/router';
import { CustomeServiceService } from '../../../services/custome-service.service';

@Component({
  selector: 'app-print-carpet-order-issue',
  templateUrl: './print-carpet-order-issue.component.html',
  styleUrl: './print-carpet-order-issue.component.css'
})
export class PrintCarpetOrderIssueComponent implements OnInit{
 
  printId:any

  constructor(private manufactureService:ManufactureService,private activatedRoute:ActivatedRoute,private customerService:CustomeServiceService){}

  // @Input() message?:string


  ngOnInit(): void {

   debugger
     this.printId = this.activatedRoute.snapshot.paramMap.get('id') || ''
    if(this.printId){
      this.getAllCarpetOrderData(this.printId)
    };
    this.getData()
    
 }

  obj:any

  getData(){
    debugger
    let data = localStorage.getItem('printData');
    this.obj = data ? JSON.parse(data) : null;
    console.log(this.obj);
  }

  // objs:any
  // getInsetedId(){
  //   let datas = localStorage.getItem('insertedId')
  //   this.objs = datas ? JSON.parse(datas) : null
  // }

  getById(id:any){
   this.manufactureService.getOrderIssue(id).subscribe((res:any)=>{

   })
  }


  orderIssueData:any
  totalArea:any
  getAllCarpetOrderData(id:string){

    this.manufactureService.getsOrderIssueList().subscribe((res:any)=>{
      
      this.orderIssueData =  res.find((x:any) => x._id === id)
      this.orderIssueData.date
      debugger
    // let area = this.orderIssueData.buyerOrder.items.find((x:any)=> x._id === this.orderIssueData.itemId)
    //  this.totalArea =area.totalArea
    //  console.log(this.orderIssueData)
    let areaItem = this.orderIssueData.buyerOrder.items.find((x:any)=> x._id === this.orderIssueData.itemId);
        
    // ✅ Area value ko assign karna
    this.totalArea = areaItem?.totalArea || 0; 
    console.log(this.orderIssueData)
   
    console.log("✅ Total Area:", this.totalArea);
    
    })
  }
 
  printPage() {
    const printContents = document.getElementById('invoice')?.innerHTML;
    if (printContents) {
      const originalContents = document.body.innerHTML;
      document.body.innerHTML = printContents;
      window.print();
      document.body.innerHTML = originalContents;
      window.location.reload(); // To restore Angular bindings
    } else {
      window.print();
    }
  }
}
