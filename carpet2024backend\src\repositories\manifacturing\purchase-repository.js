// repositories/purchaseRepository.js
const Purchase = require('../../model/phase-4/purchase');

class PurchaseRepository {
  async create(purchaseData) {
    const purchase = new Purchase(purchaseData);
    return await purchase.save();
  }

  async findAll() {
    return await Purchase.find().populate('materialGroup').populate('partyName').populate('purchase').populate('woolQuality');
  }

  async findById(id) {
    return await Purchase.findById(id).populate('materialGroup').populate('partyName').populate('purchase').populate('woolQuality');
  }

  async update(id, purchaseData) {
    return await Purchase.findByIdAndUpdate(id, purchaseData, { new: true });
  }

  async delete(id) {
    return await Purchase.findByIdAndDelete(id);
  }
}

module.exports = new PurchaseRepository();
