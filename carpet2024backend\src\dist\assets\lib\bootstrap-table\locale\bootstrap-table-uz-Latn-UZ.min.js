/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},y={},g={}.propertyIsEnumerable,h=Object.getOwnPropertyDescriptor,b=h&&!g.call({1:2},1);y.f=b?function(t){var n=h(this,t);return!!n&&n.enumerable}:g;var d,m,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},w=f,S=Function.prototype,O=S.call,j=w&&S.bind.bind(O,O),P=function(t){return w?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),x=T("".slice),E=function(t){return x(A(t),8,-1)},C=E,F=P,M=function(t){if("Function"===C(t))return F(t)},R=a,D=E,L=Object,I=M("".split),k=R((function(){return!L("z").propertyIsEnumerable(0)}))?function(t){return"String"==D(t)?I(t,""):L(t)}:L,z=function(t){return null==t},H=z,N=TypeError,_=function(t){if(H(t))throw N("Can't call method on "+t);return t},q=k,U=_,G=function(t){return q(U(t))},B="object"==typeof document&&document.all,K={all:B,IS_HTMLDDA:void 0===B&&void 0!==B},W=K.all,Y=K.IS_HTMLDDA?function(t){return"function"==typeof t||t===W}:function(t){return"function"==typeof t},Q=Y,Z=K.all,J=K.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===Z}:function(t){return"object"==typeof t?null!==t:Q(t)},V=i,X=Y,$=function(t){return X(t)?t:void 0},tt=function(t,n){return arguments.length<2?$(V[t]):V[t]&&V[t][n]},nt=M({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(m=(d=at.split("."))[0]>0&&d[0]<4?1:+(d[0]+d[1])),!m&&et&&(!(d=et.match(/Edge\/(\d+)/))||d[1]>=74)&&(d=et.match(/Chrome\/(\d+)/))&&(m=+d[1]);var ct=m,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,yt=tt,gt=Y,ht=nt,bt=Object,dt=pt?function(t){return"symbol"==typeof t}:function(t){var n=yt("Symbol");return gt(n)&&ht(n.prototype,bt(t))},mt=String,vt=Y,wt=function(t){try{return mt(t)}catch(t){return"Object"}},St=TypeError,Ot=function(t){if(vt(t))return t;throw St(wt(t)+" is not a function")},jt=z,Pt=p,Tt=Y,At=J,xt=TypeError,Et={exports:{}},Ct=i,Ft=Object.defineProperty,Mt=function(t,n){try{Ft(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Rt=Mt,Dt="__core-js_shared__",Lt=i[Dt]||Rt(Dt,{}),It=Lt;(Et.exports=function(t,n){return It[t]||(It[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var kt=_,zt=Object,Ht=function(t){return zt(kt(t))},Nt=Ht,_t=M({}.hasOwnProperty),qt=Object.hasOwn||function(t,n){return _t(Nt(t),n)},Ut=M,Gt=0,Bt=Math.random(),Kt=Ut(1..toString),Wt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Kt(++Gt+Bt,36)},Yt=i,Qt=Et.exports,Zt=qt,Jt=Wt,Vt=st,Xt=pt,$t=Qt("wks"),tn=Yt.Symbol,nn=tn&&tn.for,rn=Xt?tn:tn&&tn.withoutSetter||Jt,en=function(t){if(!Zt($t,t)||!Vt&&"string"!=typeof $t[t]){var n="Symbol."+t;Vt&&Zt(tn,t)?$t[t]=tn[t]:$t[t]=Xt&&nn?nn(n):rn(n)}return $t[t]},on=p,un=J,an=dt,cn=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},yn=dt,gn=function(t){var n=pn(t,"string");return yn(n)?n:n+""},hn=J,bn=i.document,dn=hn(bn)&&hn(bn.createElement),mn=function(t){return dn?bn.createElement(t):{}},vn=!c&&!a((function(){return 7!=Object.defineProperty(mn("div"),"a",{get:function(){return 7}}).a})),wn=c,Sn=p,On=y,jn=v,Pn=G,Tn=gn,An=qt,xn=vn,En=Object.getOwnPropertyDescriptor;u.f=wn?En:function(t,n){if(t=Pn(t),n=Tn(n),xn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!Sn(On.f,t,n),t[n])};var Cn={},Fn=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mn=J,Rn=String,Dn=TypeError,Ln=function(t){if(Mn(t))return t;throw Dn(Rn(t)+" is not an object")},In=c,kn=vn,zn=Fn,Hn=Ln,Nn=gn,_n=TypeError,qn=Object.defineProperty,Un=Object.getOwnPropertyDescriptor,Gn="enumerable",Bn="configurable",Kn="writable";Cn.f=In?zn?function(t,n,r){if(Hn(t),n=Nn(n),Hn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Kn in r&&!r.writable){var e=Un(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Bn in r?r.configurable:e.configurable,enumerable:Gn in r?r.enumerable:e.enumerable,writable:!1})}return qn(t,n,r)}:qn:function(t,n,r){if(Hn(t),n=Nn(n),Hn(r),kn)try{return qn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw _n("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Wn=Cn,Yn=v,Qn=c?function(t,n,r){return Wn.f(t,n,Yn(1,r))}:function(t,n,r){return t[n]=r,t},Zn={exports:{}},Jn=c,Vn=qt,Xn=Function.prototype,$n=Jn&&Object.getOwnPropertyDescriptor,tr=Vn(Xn,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Jn||Jn&&$n(Xn,"name").configurable)},rr=Y,er=Lt,or=M(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=Y,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,yr=Wt,gr=pr("keys"),hr={},br=sr,dr=i,mr=J,vr=Qn,wr=qt,Sr=Lt,Or=function(t){return gr[t]||(gr[t]=yr(t))},jr=hr,Pr="Object already initialized",Tr=dr.TypeError,Ar=dr.WeakMap;if(br||Sr.state){var xr=Sr.state||(Sr.state=new Ar);xr.get=xr.get,xr.has=xr.has,xr.set=xr.set,ir=function(t,n){if(xr.has(t))throw Tr(Pr);return n.facade=t,xr.set(t,n),n},ur=function(t){return xr.get(t)||{}},ar=function(t){return xr.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(wr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return wr(t,Er)?t[Er]:{}},ar=function(t){return wr(t,Er)}}var Cr={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!mr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=a,Mr=Y,Rr=qt,Dr=c,Lr=nr.CONFIGURABLE,Ir=cr,kr=Cr.enforce,zr=Cr.get,Hr=Object.defineProperty,Nr=Dr&&!Fr((function(){return 8!==Hr((function(){}),"length",{value:8}).length})),_r=String(String).split("String"),qr=Zn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Rr(t,"name")||Lr&&t.name!==n)&&(Dr?Hr(t,"name",{value:n,configurable:!0}):t.name=n),Nr&&r&&Rr(r,"arity")&&t.length!==r.arity&&Hr(t,"length",{value:r.arity});try{r&&Rr(r,"constructor")&&r.constructor?Dr&&Hr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=kr(t);return Rr(e,"source")||(e.source=_r.join("string"==typeof n?n:"")),t};Function.prototype.toString=qr((function(){return Mr(this)&&zr(this).source||Ir(this)}),"toString");var Ur=Y,Gr=Cn,Br=Zn.exports,Kr=Mt,Wr={},Yr=Math.ceil,Qr=Math.floor,Zr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Yr)(n)},Jr=function(t){var n=+t;return n!=n||0===n?0:Zr(n)},Vr=Jr,Xr=Math.max,$r=Math.min,te=Jr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=G,ie=function(t,n){var r=Vr(t);return r<0?Xr(r+n,0):$r(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=qt,le=G,se=ce.indexOf,pe=hr,ye=M([].push),ge=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ye(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ye(i,r));return i},he=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Wr.f=Object.getOwnPropertyNames||function(t){return ge(t,he)};var be={};be.f=Object.getOwnPropertySymbols;var de=tt,me=Wr,ve=be,we=Ln,Se=M([].concat),Oe=de("Reflect","ownKeys")||function(t){var n=me.f(we(t)),r=ve.f;return r?Se(n,r(t)):n},je=qt,Pe=Oe,Te=u,Ae=Cn,xe=a,Ee=Y,Ce=/#|\.prototype\./,Fe=function(t,n){var r=Re[Me(t)];return r==Le||r!=De&&(Ee(n)?xe(n):!!n)},Me=Fe.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Re=Fe.data={},De=Fe.NATIVE="N",Le=Fe.POLYFILL="P",Ie=Fe,ke=i,ze=u.f,He=Qn,Ne=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Ur(r)&&Br(r,i,e),e.global)o?t[n]=r:Kr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Gr.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},_e=Mt,qe=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var a=e[u];je(t,a)||r&&je(r,a)||o(t,a,i(n,a))}},Ue=Ie,Ge=E,Be=Array.isArray||function(t){return"Array"==Ge(t)},Ke=TypeError,We=gn,Ye=Cn,Qe=v,Ze={};Ze[en("toStringTag")]="z";var Je="[object z]"===String(Ze),Ve=Y,Xe=E,$e=en("toStringTag"),to=Object,no="Arguments"==Xe(function(){return arguments}()),ro=M,eo=a,oo=Y,io=Je?Xe:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),$e))?r:no?Xe(n):"Object"==(e=Xe(n))&&Ve(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),yo=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},go=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};go.sham=!0;var ho=!fo||eo((function(){var t;return yo(yo.call)||!yo(Object)||!yo((function(){t=!0}))||t}))?go:yo,bo=Be,mo=ho,vo=J,wo=en("species"),So=Array,Oo=function(t){var n;return bo(t)&&(n=t.constructor,(mo(n)&&(n===So||bo(n.prototype))||vo(n)&&null===(n=n[wo]))&&(n=void 0)),void 0===n?So:n},jo=a,Po=ct,To=en("species"),Ao=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?ke:f?ke[a]||_e(a,{}):(ke[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=ze(r,e))&&u.value:r[e],!Ue(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;qe(i,o)}(t.sham||o&&o.sham)&&He(i,"sham",!0),Ne(r,e,i,t)}},xo=a,Eo=Be,Co=J,Fo=Ht,Mo=ee,Ro=function(t){if(t>9007199254740991)throw Ke("Maximum allowed index exceeded");return t},Do=function(t,n,r){var e=We(n);e in t?Ye.f(t,e,Qe(0,r)):t[e]=r},Lo=function(t,n){return new(Oo(t))(0===n?0:n)},Io=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},ko=ct,zo=en("isConcatSpreadable"),Ho=ko>=51||!xo((function(){var t=[];return t[zo]=!1,t.concat()[0]!==t})),No=Io("concat"),_o=function(t){if(!Co(t))return!1;var n=t[zo];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!Ho||!No},{concat:function(t){var n,r,e,o,i,u=Fo(this),a=Lo(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(_o(i=-1===n?u:arguments[n]))for(o=Mo(i),Ro(c+o),r=0;r<o;r++,c++)r in i&&Do(a,c,i[r]);else Ro(c+1),Do(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["uz-Latn-UZ"]=r.default.fn.bootstrapTable.locales.uz={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Yuklanyapti, iltimos kuting"},formatRecordsPerPage:function(t){return"".concat(t," qator har sahifada")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Ko'rsatypati ".concat(t," dan ").concat(n," gacha ").concat(r," qatorlarni (filtered from ").concat(e," total rows)"):"Ko'rsatypati ".concat(t," dan ").concat(n," gacha ").concat(r," qatorlarni")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Filtrlarni tozalash"},formatSearch:function(){return"Qidirish"},formatNoMatches:function(){return"Hech narsa topilmadi"},formatPaginationSwitch:function(){return"Sahifalashni yashirish/ko'rsatish"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Yangilash"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Ustunlar"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Hammasi"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Eksport"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["uz-Latn-UZ"])}));
