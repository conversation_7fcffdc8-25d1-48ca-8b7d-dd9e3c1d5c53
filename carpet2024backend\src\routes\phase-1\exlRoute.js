var express = require('express')
var router = express()
const bodyParser = require('body-parser');
const { uploadExl, exldata, getAllData } = require('../../controller/exlController')
const upload = require("../../middleware/exlMiddleware");
const { checkDuplicateEntry } = require('../../middleware/impoter-invoice-middleware');
router.use(bodyParser.json());
router.use(bodyParser.urlencoded({ extended: true }));
router.post('/exlupload',  upload.single("file"),checkDuplicateEntry, uploadExl);
router.post('/getAll-data', getAllData)
router.post("/exldata", exldata);
const ImportExistingStock = require('../../middleware/importStock');
router.post('/import-stock',upload.single("file"),ImportExistingStock.importExistingStock);
router.post('/challanStock',upload.single('file'),ImportExistingStock.inportExistingChallan)

module.exports = router;



