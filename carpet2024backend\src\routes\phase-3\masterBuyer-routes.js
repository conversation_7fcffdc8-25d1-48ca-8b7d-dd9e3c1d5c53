// routes/buyerRoutes.js
const express = require('express');
const router = express.Router();
const buyerController = require('../../controller/masterBuyer-controller');

router.get('/getAll-buyer', buyerController.getAllBuyers);
router.get('/get-buyer/:id', buyerController.getBuyerById);
router.post('/craete-buyer', buyerController.createBuyer);
router.put('/update-buyer/:id', buyerController.updateBuyer);
router.delete('/delete-buyer/:id', buyerController.deleteBuyer);

module.exports = router;
