/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},y={},g={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!g.call({1:2},1);y.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:g;var m,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},w=f,S=Function.prototype,O=S.call,j=w&&S.bind.bind(O,O),P=function(t){return w?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),x=T("".slice),E=function(t){return x(A(t),8,-1)},C=E,F=P,R=function(t){if("Function"===C(t))return F(t)},M=c,D=E,I=Object,L=R("".split),k=M((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"==D(t)?L(t,""):I(t)}:I,H=function(t){return null==t},N=H,_=TypeError,z=function(t){if(N(t))throw _("Can't call method on "+t);return t},G=k,B=z,q=function(t){return G(B(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},K=W.all,Z=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===K}:function(t){return"function"==typeof t},J=Z,Q=W.all,V=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:J(t)||t===Q}:function(t){return"object"==typeof t?null!==t:J(t)},X=i,Y=Z,$=function(t){return Y(t)?t:void 0},tt=function(t,n){return arguments.length<2?$(X[t]):X[t]&&X[t][n]},nt=R({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(h=(m=ct.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!h&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(h=+m[1]);var at=h,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,yt=tt,gt=Z,bt=nt,dt=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=yt("Symbol");return gt(n)&&bt(n.prototype,dt(t))},ht=String,vt=Z,wt=function(t){try{return ht(t)}catch(t){return"Object"}},St=TypeError,Ot=function(t){if(vt(t))return t;throw St(wt(t)+" is not a function")},jt=H,Pt=p,Tt=Z,At=V,xt=TypeError,Et={exports:{}},Ct=i,Ft=Object.defineProperty,Rt=function(t,n){try{Ft(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Mt=Rt,Dt="__core-js_shared__",It=i[Dt]||Mt(Dt,{}),Lt=It;(Et.exports=function(t,n){return Lt[t]||(Lt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var kt=z,Ht=Object,Nt=function(t){return Ht(kt(t))},_t=Nt,zt=R({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return zt(_t(t),n)},Bt=R,qt=0,Ut=Math.random(),Wt=Bt(1..toString),Kt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++qt+Ut,36)},Zt=i,Jt=Et.exports,Qt=Gt,Vt=Kt,Xt=st,Yt=pt,$t=Jt("wks"),tn=Zt.Symbol,nn=tn&&tn.for,rn=Yt?tn:tn&&tn.withoutSetter||Vt,en=function(t){if(!Qt($t,t)||!Xt&&"string"!=typeof $t[t]){var n="Symbol."+t;Xt&&Qt(tn,t)?$t[t]=tn[t]:$t[t]=Yt&&nn?nn(n):rn(n)}return $t[t]},on=p,un=V,cn=mt,an=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},yn=mt,gn=function(t){var n=pn(t,"string");return yn(n)?n:n+""},bn=V,dn=i.document,mn=bn(dn)&&bn(dn.createElement),hn=function(t){return mn?dn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),wn=a,Sn=p,On=y,jn=v,Pn=q,Tn=gn,An=Gt,xn=vn,En=Object.getOwnPropertyDescriptor;u.f=wn?En:function(t,n){if(t=Pn(t),n=Tn(n),xn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!Sn(On.f,t,n),t[n])};var Cn={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Rn=V,Mn=String,Dn=TypeError,In=function(t){if(Rn(t))return t;throw Dn(Mn(t)+" is not an object")},Ln=a,kn=vn,Hn=Fn,Nn=In,_n=gn,zn=TypeError,Gn=Object.defineProperty,Bn=Object.getOwnPropertyDescriptor,qn="enumerable",Un="configurable",Wn="writable";Cn.f=Ln?Hn?function(t,n,r){if(Nn(t),n=_n(n),Nn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=Bn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:qn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(Nn(t),n=_n(n),Nn(r),kn)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw zn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Kn=Cn,Zn=v,Jn=a?function(t,n,r){return Kn.f(t,n,Zn(1,r))}:function(t,n,r){return t[n]=r,t},Qn={exports:{}},Vn=a,Xn=Gt,Yn=Function.prototype,$n=Vn&&Object.getOwnPropertyDescriptor,tr=Xn(Yn,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Vn||Vn&&$n(Yn,"name").configurable)},rr=Z,er=It,or=R(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=Z,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,yr=Kt,gr=pr("keys"),br={},dr=sr,mr=i,hr=V,vr=Jn,wr=Gt,Sr=It,Or=function(t){return gr[t]||(gr[t]=yr(t))},jr=br,Pr="Object already initialized",Tr=mr.TypeError,Ar=mr.WeakMap;if(dr||Sr.state){var xr=Sr.state||(Sr.state=new Ar);xr.get=xr.get,xr.has=xr.has,xr.set=xr.set,ir=function(t,n){if(xr.has(t))throw Tr(Pr);return n.facade=t,xr.set(t,n),n},ur=function(t){return xr.get(t)||{}},cr=function(t){return xr.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(wr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return wr(t,Er)?t[Er]:{}},cr=function(t){return wr(t,Er)}}var Cr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=c,Rr=Z,Mr=Gt,Dr=a,Ir=nr.CONFIGURABLE,Lr=ar,kr=Cr.enforce,Hr=Cr.get,Nr=Object.defineProperty,_r=Dr&&!Fr((function(){return 8!==Nr((function(){}),"length",{value:8}).length})),zr=String(String).split("String"),Gr=Qn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Mr(t,"name")||Ir&&t.name!==n)&&(Dr?Nr(t,"name",{value:n,configurable:!0}):t.name=n),_r&&r&&Mr(r,"arity")&&t.length!==r.arity&&Nr(t,"length",{value:r.arity});try{r&&Mr(r,"constructor")&&r.constructor?Dr&&Nr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=kr(t);return Mr(e,"source")||(e.source=zr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Rr(this)&&Hr(this).source||Lr(this)}),"toString");var Br=Z,qr=Cn,Ur=Qn.exports,Wr=Rt,Kr={},Zr=Math.ceil,Jr=Math.floor,Qr=Math.trunc||function(t){var n=+t;return(n>0?Jr:Zr)(n)},Vr=function(t){var n=+t;return n!=n||0===n?0:Qr(n)},Xr=Vr,Yr=Math.max,$r=Math.min,te=Vr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=q,ie=function(t,n){var r=Xr(t);return r<0?Yr(r+n,0):$r(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=Gt,le=q,se=ae.indexOf,pe=br,ye=R([].push),ge=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ye(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ye(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Kr.f=Object.getOwnPropertyNames||function(t){return ge(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var me=tt,he=Kr,ve=de,we=In,Se=R([].concat),Oe=me("Reflect","ownKeys")||function(t){var n=he.f(we(t)),r=ve.f;return r?Se(n,r(t)):n},je=Gt,Pe=Oe,Te=u,Ae=Cn,xe=c,Ee=Z,Ce=/#|\.prototype\./,Fe=function(t,n){var r=Me[Re(t)];return r==Ie||r!=De&&(Ee(n)?xe(n):!!n)},Re=Fe.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Me=Fe.data={},De=Fe.NATIVE="N",Ie=Fe.POLYFILL="P",Le=Fe,ke=i,He=u.f,Ne=Jn,_e=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Br(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:qr.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},ze=Rt,Ge=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var c=e[u];je(t,c)||r&&je(r,c)||o(t,c,i(n,c))}},Be=Le,qe=E,Ue=Array.isArray||function(t){return"Array"==qe(t)},We=TypeError,Ke=gn,Ze=Cn,Je=v,Qe={};Qe[en("toStringTag")]="z";var Ve="[object z]"===String(Qe),Xe=Z,Ye=E,$e=en("toStringTag"),to=Object,no="Arguments"==Ye(function(){return arguments}()),ro=R,eo=c,oo=Z,io=Ve?Ye:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),$e))?r:no?Ye(n):"Object"==(e=Ye(n))&&Xe(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),yo=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},go=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};go.sham=!0;var bo=!fo||eo((function(){var t;return yo(yo.call)||!yo(Object)||!yo((function(){t=!0}))||t}))?go:yo,mo=Ue,ho=bo,vo=V,wo=en("species"),So=Array,Oo=function(t){var n;return mo(t)&&(n=t.constructor,(ho(n)&&(n===So||mo(n.prototype))||vo(n)&&null===(n=n[wo]))&&(n=void 0)),void 0===n?So:n},jo=c,Po=at,To=en("species"),Ao=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?ke:f?ke[c]||ze(c,{}):(ke[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=He(r,e))&&u.value:r[e],!Be(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&Ne(i,"sham",!0),_e(r,e,i,t)}},xo=c,Eo=Ue,Co=V,Fo=Nt,Ro=ee,Mo=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Do=function(t,n,r){var e=Ke(n);e in t?Ze.f(t,e,Je(0,r)):t[e]=r},Io=function(t,n){return new(Oo(t))(0===n?0:n)},Lo=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},ko=at,Ho=en("isConcatSpreadable"),No=ko>=51||!xo((function(){var t=[];return t[Ho]=!1,t.concat()[0]!==t})),_o=Lo("concat"),zo=function(t){if(!Co(t))return!1;var n=t[Ho];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!No||!_o},{concat:function(t){var n,r,e,o,i,u=Fo(this),c=Io(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(zo(i=-1===n?u:arguments[n]))for(o=Ro(i),Mo(a+o),r=0;r<o;r++,a++)r in i&&Do(c,a,i[r]);else Mo(a+1),Do(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["af-ZA"]=r.default.fn.bootstrapTable.locales.af={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Besig om te laai, wag asseblief"},formatRecordsPerPage:function(t){return"".concat(t," rekords per bladsy")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Resultate ".concat(t," tot ").concat(n," van ").concat(r," rye (filtered from ").concat(e," total rows)"):"Resultate ".concat(t," tot ").concat(n," van ").concat(r," rye")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Soek"},formatNoMatches:function(){return"Geen rekords gevind nie"},formatPaginationSwitch:function(){return"Wys/verberg bladsy nummering"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Herlaai"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolomme"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["af-ZA"])}));
