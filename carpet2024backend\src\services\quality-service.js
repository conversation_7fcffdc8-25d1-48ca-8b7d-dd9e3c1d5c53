const AddqualityRepository = require('../repositories/quality-repository');

class AddqualityService {
    constructor() {
        this.addqualityRepository = new AddqualityRepository();
    }

    async createQuality(qualityData) {
        try {
            return await this.addqualityRepository.createQuality(qualityData);
        } catch (error) {
            throw error;
        }
    }

    async getAllQualities() {
        try {
            return await this.addqualityRepository.getAllQualities();
        } catch (error) {
            throw error;
        }
    }

    async getQualityById(qualityId) {
        try {
            return await this.addqualityRepository.getQualityById(qualityId);
        } catch (error) {
            throw error;
        }
    }

    async updateQuality(qualityId, qualityData) {
        try {
            return await this.addqualityRepository.updateQuality(qualityId, qualityData);
        } catch (error) {
            throw error;
        }
    }

    async deleteQuality(qualityId) {
        try {
            return await this.addqualityRepository.deleteQuality(qualityId);
        } catch (error) {
            throw error;
        }
    }
}

module.exports = AddqualityService;
