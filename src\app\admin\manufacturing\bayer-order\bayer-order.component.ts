import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import { MatDialog } from '@angular/material/dialog';
import { DesignComponent } from '../../master/design/design.component';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SweetalertService } from '../../../services/sweetalert.service';
import Swal from 'sweetalert2';
import { Global } from '../../../Shared/validators';
import { ManufactureService } from '../../../services/manufacture.service';
import { ActivatedRoute } from '@angular/router';

export interface UserData {
  SrNo: string;
  Quality: string;
  Design: string;
  GroundBorder: string;
  size: string;
  KhapSize: string;
  Pcs: string;
  Area: string;
  Action: string;
}

const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-bayer-order',
  templateUrl: './bayer-order.component.html',
  styleUrl: './bayer-order.component.css',
})
export class BayerOrderComponent implements OnInit {
  CustomerName = 'option1';
  OrderType = 'option2';
  WeavingUnit = 'option2';
  CostumerOrder = 'option2';
  Priority = 'option1';
  Quality = 'option2';
  Size = 'option2';
  Type = 'option1';
  orderNo!: string;
  _isQualityChanged: boolean = false;
  orderTypeList: any = ['Order', 'TypeB', 'Stock', 'Sample'];
  customerList: any = [];
  customerOrderUnitList: any = ['SQ MTR', 'SQ YARD'];
  weavingUnitList: any = ['SQ Yaard', 'SQ Feet', 'OTHER1'];
  priorityList: any = ['Normal', 'First', 'Second'];
  isUpdatedOrderList: boolean = false;
  idUpdatedOrderList: any;
  selectedArrayList: any = [];
  constructor(
    private _services: MasterService,
    private manufactureService: ManufactureService,
    private matDig: MatDialog,
    private fb: FormBuilder,
    private alert: SweetalertService,
    private activedRoute: ActivatedRoute
  ) {}
  qualityList: any = [];
  sizeList: any = [];
  editedId: any;
  displayedColumns: string[] = [
    'SrNo',
    'Quality',
    'Design',
    'GroundBorder',
    'size',
    'KhapSize',
    'Pcs',
    'Area',
    'Action',
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);
  totalSizeInYaard: any;
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  isUpdated: boolean = false;
  frmBuyerOrder!: FormGroup;
  frmOrder!: FormGroup;
  frmOrderItem!: FormGroup;
  ngOnInit() {
    this.frmOrder = this.fb.group({
      buyerName: ['', [Validators.required]],
      orderNo: ['', [Validators.required]],
      companyOrderNo: ['', [Validators.required]],
      orderDate: [],
      shippingDate: [],
      orderType: ['TypeB', [Validators.required]],
      customerOrder: ['SQ YARD', [Validators.required]],
      // weavingUnit: ['', [Validators.required]],
      priority: ['Normal', [Validators.required]],
      area: ['', [Validators.required]],
      // quality: [''],
      // design: [''],
      // ground: [''],
      // border: [''],
      // size: [''],
      // type: ['Normal ( NR )'],
      // pcs: ['1'],
      // totalArea: [''],
      // khapSize: [''],
    });

    this.editedId = this.activedRoute.snapshot.paramMap.get('id') || '';

    this.frmOrderItem = this.fb.group({
      quality: ['', [Validators.required]],
      design: [''],
      ground: [''],
      border: [''],
      size: ['', [Validators.required]],
      type: ['Normal ( NR )', [Validators.required]], // Default value set
      pcs: ['1', [Validators.required, Validators.min(1)]], // pcs must be at least 1
      totalArea: [''],
      khapSize: [''],
    });
    // Set default value for type after reset as well
    this.frmOrderItem.valueChanges.subscribe(() => {
      if (!this.frmOrderItem.get('type')?.value) {
        this.frmOrderItem.get('type')?.setValue('Normal ( NR )', { emitEvent: false });
      }
    });
    this.getIssueItems();
    this.quality();
    this.size();
    this.getsCustomer();
    this.getsBuyerOrder();
    this.getDesign();
    setTimeout(() => {
      if (this.editedId) {
        this.edit(this.editedId);
      }
    }, 1000);
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  quality() {
    this._services.getqualityList().subscribe((resp: any) => {
      // console.log(resp);

      resp.map((v: any) => {
        this.qualityList.push({
          id: v._id,
          quality: v.quality,
        });
      });
    });
  }

  size() {
    this._services.getAllSizesList().subscribe((resp: any) => {
      if (resp) {
        resp.map((v: any) => {
          this.sizeList.push({
            id: v._id,
            size: v.sizeInYard,
            khapSize: v.khapSize,
          });
        });
      }
    });
  }
  selectSize(data: any) {
    let khapsize = this.sizeList.find((x: any) => x.id === data);
    this.frmOrderItem.get('khapSize')?.setValue(khapsize.khapSize);
    this.setSizeYard(khapsize.size);
    return;
  }

  // selectQuality(qty: any) {

  //   let dialogRef = this.matDig.open(DesignComponent, {
  //     disableClose: true,
  //   });

  //   dialogRef.afterClosed().subscribe((result) => {
  //     if (result) {
  //       this.frmOrderItem.get('design')?.setValue(result.design);
  //       this.frmOrderItem
  //         .get('ground')
  //         ?.setValue(result.ground + '/' + result.border);
  //     }
  //     console.log('result result result', result);
  //   });
  // }
  qualitys: any;
  designId!: String;
  selectQuality(qty: any) {
    this.qualitys = this.qualityList.find((x: any) => x.id === qty);

    let dialogRef = this.matDig.open(DesignComponent, {
      disableClose: true,
      width: '500px',
      data: { quality: this.qualitys },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // this.groundId = '';
        // this.borderId = '';
        // this.designId = '';
        debugger
        this.frmOrderItem.get('design')?.setValue(result.design);
        this.designId = result._id;
        this.frmOrderItem
          .get('ground')
          ?.setValue(result.ground + '/' + result.border);
        // this.groundId = result.groundId;
        // this.borderId = result.borderId;
        // this.designId = result._id;
      }
      console.log('result result result', result);
    });
  }

  calculateArea() {
    let tv = parseFloat(this.totalSizeInYaard.toFixed(2));
    let frmData = parseInt(this.frmOrder.value.pcs);
    this.frmOrder.get('totalArea')?.setValue((tv * frmData).toFixed(2));
  }

  setSizeYard(size: any) {
    const formData = size;

    const calcData = formData.split(/[Xx]/);

    const width1 = calcData[0].split('.')[0];
    const width2 = calcData[0].split('.')[1];

    console.log('before ', width1, 'after ', width2);

    console.log(calcData);

    const length1 = calcData[1].split('.')[0];
    const length2 = calcData[1].split('.')[1];

    const sizeInYaardCalc1 = parseInt(width1) * 12 + parseInt(width2);

    const sizeInYaardCalc2 = parseInt(length1) * 12 + parseInt(length2);
    console.log(sizeInYaardCalc1);
    console.log(sizeInYaardCalc2);

    this.totalSizeInYaard = (sizeInYaardCalc1 * sizeInYaardCalc2) / 1296;


let pcs = this.frmOrderItem.get('pcs')?.value;

  let totalVal = parseInt(pcs) * this.totalSizeInYaard;

    this.frmOrderItem
      .get('totalArea')
      ?.setValue(totalVal.toFixed(2));
  }

  getsCustomer() {
    this._services.getAllBuyerList().subscribe({
      next: (value) => {
        value.map((x: any) => {
          this.customerList.push({
            id: x._id,
            name: x.customerName,
            code: x.customerCode,
          });
        });
      },
    });
  }
  selectCustomer(id: any) {
    let data = this.customerList.find((x: any) => x.id === id);
    this.frmOrder
      .get('companyOrderNo')
      ?.setValue(data.code + '-' + this.orderNo);
    data.code;
  }

  getsBuyerOrder() {
    this._services.getsBuyerOrder().subscribe({
      next: (value: any) => {
        this.orderNo = this.incrementOrderNo(
          value.length.toString().padStart(6, '0')
        );

        console.log(this.orderNo);
      },
    });
  }

  incrementOrderNo(orderNo: string): string {
    let numericOrderNo = parseInt(orderNo, 10);
    numericOrderNo++;
    return numericOrderNo.toString().padStart(6, '0');
  }

  orderList: any = [];
  addItems() {
    if (!this.showOrderItemValidationErrors()) {
      return;
    }
    console.log(this.frmOrder.value);

    let data = this.frmOrderItem.value;
    let size = this.sizeList.find((x: any) => x.id === data.size);
    data.size = size.size;
    data['sid'] = size.id;
    data.quality = this.qualitys.quality;
    data['id'] = this.qualitys.id;

    data['designId'] = this.designId;

    this.orderList.push(data);
    this.claculateArea(this.orderList);

    this.frmOrderItem.reset({ type: 'Normal ( NR )', pcs: '1' });
  }

  createOrder() {
    let frmData = this.frmOrder.value;

    frmData['items'] = this.orderList;
    frmData.items.forEach(function (data: any) {
      data['quality'] = data['id'];
      delete data['qty'], (data['sizeId'] = data['sid']);
      delete data['sid'], (data['design'] = data['designId']);
      delete data['designId'];
      data['groundColour'] = data['ground'];
      data['PcsesPending'] = data['PcsesPending'] || data['pcs'];
      data['PcsesAssigned'] = data['PcsesAssigned'] || 0;
    });

    this.manufactureService.addBuyerOrder(frmData).subscribe({
      next: (value) => {
        this.alert.success('success', 'Order has been created');
      },
      error: (err) => {
        this.alert.error('warning', 'Failed!, Something went wrong');
      },
    });

    this.orderList.length = 0;
  }
  deleteBuyerOrder(id: any) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to delete order!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result: any) => {
      if (result.isConfirmed) {
        let ids = this.orderList[id].itemId;
        this.manufactureService.deleteBuyerOrderItem(ids).subscribe({
          next: (value:any) => {
            this.alert.success('success',value.message);
          },
          error: (err:any) => {
            this.alert.error('warning', err.error.message);
          },
        });
      }
    });

    // this.adminService.deleteBuyerOrderItem(orderId).subscribe((resp:any)=> {
    //
    //   this.responsMessage = resp.message;
    //   console.log(this.orderList);
    //   Swal.fire({
    //     title: "warning!",
    //     text: this.responsMessage,
    //     icon: "warning"
    //   });
    //   // this._snackBar.openSnackBar(this.responsMessage, '');

    // },
    // (error) => {
    //   if (error.error.msg) {
    //     this.responsMessage = error.error.message;
    //   } else {
    //     this.responsMessage = global.genricError;
    //   }
    //   this._snackBar.openSnackBar(this.responsMessage, global.error);

    // })
  }
  _isSizeChanged: boolean = false;
  calcTotalArea: any = 0;
  delete(ind: number) {
    this.calcTotalArea = 0;

    let objectToDelete = this.orderList[ind];

    let index = this.orderList.findIndex(
      (item: any) => item === objectToDelete
    );

    if (index !== -1) {
      this.orderList.splice(index, 1);
    }
    let orderLen = this.orderList.length;
    for (let i = 0; i < orderLen; i++) {
      this.calcTotalArea =
        this.calcTotalArea + parseFloat(this.orderList[i].totalArea);
    }

    /// here below line code for set value from total area

    this.frmBuyerOrder.get('area')?.setValue(this.calcTotalArea.toFixed(2));

    console.log(this.orderList);
  }
  editMode = false;
  editedRowIndex: number | undefined;
  toggleEditMode(index: number): void {
    this.editMode = !this.editMode;
    this.editedRowIndex = index;
  }
  saveChanges(index: number): void {
    if (index >= 0) {
      this.isUpdatedOrderList = true;
      this.idUpdatedOrderList = index;
      this.toggleEditMode(index);

      console.log(this.orderList[index]);

      this.frmOrderItem.patchValue(
        // this.orderList[index]
        {
          design: this.orderList[index].design,
          ground: this.orderList[index].ground,
          khapSize: this.orderList[index].khapSize,
          pcs: this.orderList[index].pcs,
          quality: this.isUpdated
            ? this.orderList[index].id
            : this.orderList[index].id,
          // quality: this.isUpdated?this.orderList[this.idUpdatedOrderList]['qty']= this.orderList[index].id:this.qualitys.id,

          size: this.isUpdated
            ? this.orderList[index].sid
            : this.orderList[index].sid,
          totalArea: this.orderList[index].totalArea,
          type: this.isUpdated
            ? this.orderList[index].type
            : this.orderList[index].type,
        }
      );

      this.calcTotalArea(this.orderList);
    }
  }
  updateOrderList() {
    this.calcTotalArea = 0;
    let frmdata = this.frmOrderItem.value;
    if (parseInt(frmdata.pcs) >= 1) {

      // Check if this is an existing item with itemId (means it's from database)
      let currentItem = this.orderList[this.idUpdatedOrderList];
      if (currentItem.itemId) {
        // Get current buyer order data to check PcsesAssigned
        this.manufactureService.getBuyerOrder(this.editedId).subscribe((orderData: any) => {
          let item = orderData.items.find((x: any) => x._id === currentItem.itemId);
          let currentPcsesAssigned = item?.PcsesAssigned || 0;

          // Check if new total pieces is less than already assigned pieces
          if (parseInt(frmdata.pcs) < currentPcsesAssigned) {
            Swal.fire({
              title: 'Warning!',
              text: `Cannot reduce total pieces to ${frmdata.pcs}. Already ${currentPcsesAssigned} pieces have been assigned/issued. Please increase the total pieces or cancel some issued pieces first.`,
              icon: 'warning',
              confirmButtonText: 'OK',
              confirmButtonColor: '#f39c12'
            });
            return; // Stop execution
          } else {
            // Proceed with update
            this.proceedWithUpdate(frmdata);
          }
        }, (error: any) => {
          // If API call fails, proceed with update (fallback)
          this.proceedWithUpdate(frmdata);
        });
      } else {
        // New item, no validation needed
        this.proceedWithUpdate(frmdata);
      }
    } else {
      Swal.fire({
        title: 'Incorrect !',
        text: 'Put 1 or more pcs !',
        icon: 'warning',
      });
    }
  }

  proceedWithUpdate(frmdata: any) {
    const selectedSize = this.sizeList.find(
      (Size: any) => Size.id === frmdata.size
    );
    let qid = frmdata.quality;
    this.qualitys = this.qualityList.find((x: any) => x.id === qid);
    console.log('Selected Size:', selectedSize);

    frmdata['sid'] = selectedSize.id;
    frmdata['size'] = selectedSize.size;

    // Get current item to preserve PcsesAssigned
    let currentItem = this.orderList[this.idUpdatedOrderList];

    // If this is an existing item from database, get current PcsesAssigned
    if (currentItem.itemId) {
      this.manufactureService.getBuyerOrder(this.editedId).subscribe((orderData: any) => {
        let item = orderData.items.find((x: any) => x._id === currentItem.itemId);
        let currentPcsesAssigned = item?.PcsesAssigned || 0;

        // Calculate correct PcsesPending: new total pcs - already assigned pcs
        let newPcsesPending = Math.max(0, parseInt(frmdata.pcs) - currentPcsesAssigned);

        // Update the order list with correct values
        this.updateOrderListItem(frmdata, selectedSize, currentPcsesAssigned, newPcsesPending);
        this.completeUpdate();
      });
    } else {
      // New item - no assigned pieces yet
      this.updateOrderListItem(frmdata, selectedSize, 0, parseInt(frmdata.pcs));
      this.completeUpdate();
    }
  }

  updateOrderListItem(frmdata: any, selectedSize: any, pcsesAssigned: number, pcsesPending: number) {
    this.orderList[this.idUpdatedOrderList].quality = this.qualitys.quality;
    this.orderList[this.idUpdatedOrderList]['id'] = this.qualitys.id;
    this.orderList[this.idUpdatedOrderList].border = frmdata.border;
    this.orderList[this.idUpdatedOrderList].design = frmdata.design;
    this.orderList[this.idUpdatedOrderList].ground = frmdata.ground;
    this.orderList[this.idUpdatedOrderList].khapSize = frmdata.khapSize;
    this.orderList[this.idUpdatedOrderList].pcs = frmdata.pcs;
    this.orderList[this.idUpdatedOrderList].xpcs = frmdata.pcs;
    this.orderList[this.idUpdatedOrderList]['isUpdatedItem'] = 'updated';

    // Set correct PcsesAssigned and PcsesPending values
    this.orderList[this.idUpdatedOrderList].PcsesAssigned = pcsesAssigned;
    this.orderList[this.idUpdatedOrderList].PcsesPending = pcsesPending;

    this.orderList[this.idUpdatedOrderList].size = frmdata.size;
    this.orderList[this.idUpdatedOrderList].sid = frmdata.sid;
    this.orderList[this.idUpdatedOrderList].totalArea = frmdata.totalArea;
    this.orderList[this.idUpdatedOrderList].type = frmdata.type;
  }

  completeUpdate() {
    console.log('this is orderlist', this.orderList);
    this.frmOrderItem.reset();
    this.frmOrder.get('type')?.setValue('Normal ( NR )');
    this.selectedArrayList.length = 0;
    this.isUpdatedOrderList = false;

    this.claculateArea(this.orderList);

    // Show success alert after updating order list
    Swal.fire({
      title: 'Success!',
      text: 'Order item has been updated successfully',
      icon: 'success',
      confirmButtonText: 'OK'
    });
  }

  claculateArea(data: any) {
    let totalArea = this.orderList.reduce(
      (acc: any, cuu: any) => acc + parseFloat(cuu.totalArea),
      0
    );

    this.frmOrder.get('area')?.setValue(totalArea.toFixed(2));
  }

  designList: any = [];
  getDesign() {
    this._services.getAllDesignList().subscribe((res: any) => {
      this.designList = res;
    });
    // this.designList =
  }
  issueList: any;
  getIssueItems() {
    this.manufactureService.getsOrderIssueList().subscribe({
      next: (value) => {
        this.issueList = value;
        console.log(value);
      },
      error: (err) => {
        console.log(err);
      },
    });
  }
  edit(id: any) {
    this.manufactureService.getBuyerOrder(id).subscribe((res: any) => {
      this.isUpdated = true;
      this.frmOrder.patchValue(res);

      res.items.map((x: any, i: number) => {
        let size = this.sizeList.find((s: any) => s.id === x.sizeId);
        let qual = this.qualityList.find((q: any) => q.id === x.quality);
        let des = this.designList.find((d: any) => d._id === x.design);

        // let pcs = this.issueList
        //   .filter((y: any) => y.itemId === x._id)
        //   .reduce((cuu: any, acc: any) => cuu + parseInt(acc.pcs), 0);




        let dd = des?.design;
        let did = des?._id;
        this.orderList.push({
          design: dd,
          did: did,
          ground: x.groundColour,
          khapSize: size.khapSize,
          // pcs: pcs + parseInt(x.pcs),
          pcs: x.pcs,
          xpcs: x.pcs,
          itemId: x._id,
          id: qual.id,
          sid: size.id,
          quality: qual.quality,
          size: size.size,
          totalArea: x.totalArea,
          type: x.type,
          PcsesAssigned: x.PcsesAssigned || 0,
          PcsesPending: x.PcsesPending !== undefined ? x.PcsesPending : x.pcs,
        });
      });
    });
  }

  updateBuyerOrder() {
    let formData = this.frmOrder.value;
    debugger;
    // let itemList = [...this.orderList];
    // formData['items'] = itemList;
    // formData.items.forEach(function (data: any) {
    //   data['quality'] = data['id'];
    //   delete data['qty'], (data['sizeId'] = data['sid']);
    //   delete data['sid'],
    //     (data['design'] = data['did']),
    //     delete data['designId'];
    //   data['groundColour'] = data['ground'];
    //   data['pcs'] = data['xpcs']!==0?data['xpcs']:data['pcs'];
    //   data['isUpdatedItem']= data['isUpdatedItem']?data['isUpdatedItem']:'not_update';
    // });

    let itemList = [...this.orderList]; // Cloning the order list
    formData['items'] = itemList; // Assign cloned list to formData

    formData.items.forEach(function (data: any) {
      // Check if 'xpcs' exists; if yes, it's an existing record, otherwise, it's a new record
      if (data['xpcs'] !== undefined) {
        // Existing Record: update fields accordingly
        data['quality'] = data['id']; // Set quality from id
        data['pcs'] = data['xpcs']; // Set pcs from xpcs since it's an existing record
        data['isUpdatedItem'] = data['isUpdatedItem']
          ? data['isUpdatedItem']
          : 'not_update';
      } else {
        // New Record: set fields for a new entry
        data['quality'] = data['id']; // Set quality from id
        data['pcs'] = data['pcs']; // Set pcs directly since it's a new record
        data['isUpdatedItem'] = 'new'; // Mark as new record
      }

      // Common transformations for both existing and new records
      delete data['qty'];
      data['sizeId'] = data['sid'];
      delete data['sid'];
      data['design'] = data['did']?data['did']:data['designId'];
      delete data['designId'];
      data['groundColour'] = data['ground'];
      data['PcsesPending'] = data['PcsesPending'] || data['pcs'];
      data['PcsesAssigned'] = data['PcsesAssigned'] || 0;
    });

    console.log(formData);

    this.orderList.length = 0;

    this.manufactureService
      .updateBuyerOrder(formData, this.editedId)
      .subscribe((res: any) => {
        if (res.success === false && res.warning === true) {
          // Show warning alert for pieces validation
          Swal.fire({
            title: 'Warning!',
            text: res.message,
            icon: 'warning',
            confirmButtonText: 'OK',
            confirmButtonColor: '#f39c12'
          });
        } else {
          Swal.fire('Success!', 'Data has been update successfully', 'success');
        }
      }, (error: any) => {
        // Handle error response
        if (error.error && error.error.warning === true) {
          Swal.fire({
            title: 'Warning!',
            text: error.error.message,
            icon: 'warning',
            confirmButtonText: 'OK',
            confirmButtonColor: '#f39c12'
          });
        } else {
          Swal.fire('Error!', 'Something went wrong. Please try again.', 'error');
        }
      });
  }


  calcArea(){
    debugger
    let size = this.frmOrderItem.get('size')?.value;
    let khapsize = this.sizeList.find((x: any) => x.id === size);
    this.setSizeYard(khapsize.size);

  }

  showOrderItemValidationErrors() {
    let errorFields = Object.keys(this.frmOrderItem.controls)
      .filter(key => this.frmOrderItem.get(key)?.invalid)
      .map(key => key.replace(/([A-Z])/g, ' $1').trim());

    if (errorFields.length > 0) {
      Swal.fire({
        title: 'Validation Error',
        text: `Please fill the following fields: ${errorFields.join(', ')}`,
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return false;
    }
    return true;
  }

  // Check if item fields should be readonly based on assigned pieces
  isItemFieldsReadonly(item: any): boolean {
    if (!item) {
      return false;
    }

    if (!item.itemId) {
      return false; // New item, all fields editable
    }

    // Convert to number to ensure proper comparison
    let assignedPieces = Number(item.PcsesAssigned) || 0;
    let hasAssignedPieces = assignedPieces > 0;

    // For existing items, check if any pieces are assigned
    return hasAssignedPieces;
  }

  // Get current assigned pieces for an item
  getCurrentAssignedPieces(item: any): number {
    if (!item) return 0;
    return item.PcsesAssigned || 0;
  }
}
