
fieldset {
    font-family: sans-serif;
    border: 2px solid #1F497D;
    background: #ffffff;
    border-radius: 5px;
    padding: 15px;
  }

  fieldset legend {
    background: #ffffff;
    color: #000000;
    padding: 5px 10px ;
    font-size: 20px;
    border-radius: 5px;
    /* box-shadow: 0 0 0 5px #ddd; */
    margin-left: 20px;
  }

  legend {
    float: left;
    width: auto;
    padding: 0;
    margin-top: -32px;

    margin-bottom: 0.5rem;
    font-size: calc(1.275rem + .3vw);
    line-height: inherit;
  }
  .ex-width{
    width: 100%;
  }
  .fa-times{
    color: rgb(233, 7, 7);
  }
  thead th, tfoot td {
    position: sticky;
    z-index: 1;
    color: rgb(8, 8, 8);
    background-color: rgb(71, 196, 92);
  }
  th, td {
    /* background-color: rgb(71, 196, 92); */
    padding: 10px;
    text-align: left;
    border: 1px solid #ddd;
  }

  .sticky-header th {
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 1;
    background-color: rgb(71, 196, 92);
  }


.sticky-footer td {
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 1;
  background-color: rgb(71, 196, 92);
}

tfoot tr:first-child .sticky-footer td {
  position: sticky;
  bottom: 40px; /* Adjust this value based on the height of your footer rows */
  background: #fff;
  z-index: 1;
  background-color: rgb(71, 196, 92);
}

tfoot tr:last-child .sticky-footer td {
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 1;
  background-color: rgb(71, 196, 92);
}

.row{
  width: 100%;
}
