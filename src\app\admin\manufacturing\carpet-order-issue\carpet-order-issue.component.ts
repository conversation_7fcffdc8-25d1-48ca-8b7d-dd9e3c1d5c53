import { AfterViewInit, Component, OnInit, ViewChild, ElementRef, ChangeDetectorRef, NgZone } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ManufactureService } from '../../../services/manufacture.service';
import { MatSelectChange } from '@angular/material/select';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MasterService } from '../../../services/master.service';
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';
import { SweetalertService } from '../../../services/sweetalert.service';
import { debug } from 'node:console';

// Define UserData interface
export interface UserData {
  SrNo: string;
  Quality: string;
  Design: string;
  GroundBorder: string;
  Size: string;
  KhapSize: string;
  Pcs: string;
  TotalAreaInYd: string;
  Action: string;
}
export interface BuyerOrder {
  // pcs: number;
    PcsesPending: number;
  _id: string;
  companyOrderNo: string;
  items: BuyerOrder[];
}
// Define SetData interface
interface SetData {
  quality: string;
  design: string;
  borderColour: string;
  size: string;
  khapSize: string;
  AreaIn: number | string,
  itemId: string;
  Br_issueNo: string;
  date: Date | string;
  buyerOrder: string;
  weaver: string;
  pcs: number | string;
  area: number | string;
  rate: number;
  amount: number | string;
  MapOrderNo: string;
  branch: string;
  existingData: string;

}

const ELEMENT_DATA: UserData[] = [];


@Component({
  selector: 'app-carpet-order-issue',
  templateUrl:'./carpet-order-issue.component.html',
  styleUrl: './carpet-order-issue.component.css',
})
export class CarpetOrderIssueComponent implements OnInit, AfterViewInit {

  setData: SetData = {
    quality: '',
    design: '',
    borderColour: '',
    size: '',
    khapSize: '',
    AreaIn:'',
    itemId: '',
    Br_issueNo: '',
    date: new Date(),
    buyerOrder: '',
    weaver: '',
    pcs: '',
    area: '',
    rate: 0,
    MapOrderNo: '',
    branch: '',
    existingData: '', // Initialize as empty string
    amount: '',
  };
filterData: any;
  insertedId: any;

selectedIssue($event: any) {
throw new Error('Method not implemented.');
}

weavernamesfi:any;
  BuyerOrderNo = 'option1';
  Weaver = 'option1';
  AreaIn = 'Sq.Feet';
  ordernoList: any[] = [];
  filteredNumbers:any[] = [];
  weaverList: any[] = [];
  issuedId: string | undefined;
  frmOrderIssue!: FormGroup;
  isUpdated: boolean = false;
  editId: string = '';
  Br_issueNo: unknown;
  showOrderList: boolean = true; // Controls visibility of Order List section

  // Display names for form fields
  qualityDisplayName: string = '';
  designDisplayName: string = '';
  sizeDisplayName: string = '';
  constructor(
    private manufactureService: ManufactureService,
    private _fb: FormBuilder,
    private alert: SweetalertService,
    private adminServices: MasterService,
    private activeRoute: ActivatedRoute,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone
  ) {}

  displayedColumns: string[] = [
    'SrNo',
    'Quality',
    'Design',
    'GroundBorder',
    'Size',
    'KhapSize',
    'Pcs',
    'TotalAreaInYd',
    'Action',
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);




  ngOnInit(): void {
    // Best practice: Do NOT set {disabled: true} here. All enabling/disabling is handled in TypeScript using .disable()/.enable().
    this.frmOrderIssue = this._fb.group({
      Br_issueNo: ['', Validators.required],
      date: [new Date(), Validators.required],
      buyerOrder: ['', Validators.required],
      weaver: ['', Validators.required],
      quality: ['', Validators.required],
      design: ['', Validators.required],
      borderColour: ['', Validators.required],
      size: ['', Validators.required],
      khapSize: ['', Validators.required],
      AreaIn: ['Sq.Feet', Validators.required],
      pcs: ['', [Validators.required, Validators.min(1)]],
      area: ['', [Validators.required, Validators.min(0.01)]],
      rate: ['', [Validators.required, Validators.min(0.01)]],
      amount: ['', Validators.required],
      MapOrderNo: [''],
      // uploadFile: [null],
    });
    console.log('Current AreaIn:', this.AreaIn);




    this.customerList();
    this.getWeaverList();

    let ids = this.activeRoute.snapshot.paramMap.get('id') || '';
    if (ids) {
      this.isUpdated = true;
      this.editIssueOrder(ids); // Only call this for edit
    } else {
      this.getsIssueList(); // Only call this for new
    }

    setTimeout(() => {
      console.log('Updated AreaIn after delay:', this.AreaIn);
    }, 500);

    // this.frmOrderIssue.get('weaver')?.valueChanges.subscribe(() => {
    //   if (!this.isUpdated) { // Only for new, not edit
    //     const currentIssueNo = this.frmOrderIssue.value.Br_issueNo?.split('-').pop() || '2400001';
    //     this.frmOrderIssue.patchValue({
    //       Br_issueNo: `K-${currentIssueNo}`
    //     });
    //   }
    // });




    this.frmOrderIssue.get('pcs')?.valueChanges.subscribe(() => this.calculateArea());
    this.frmOrderIssue.get('rate')?.valueChanges.subscribe(() => this.calculateAmount());
    this.frmOrderIssue.get('area')?.valueChanges.subscribe(() => this.calculateAmount());
  }



  printinconsole(){
    console.log( this.frmOrderIssue)
  }


  calculateArea() {
    const pcsValue = this.frmOrderIssue.get('pcs')?.value;
    const pcs = Number(pcsValue) || 0;
    const areaUnit = this.frmOrderIssue.get('AreaIn')?.value;
    const selected = this.newData.find(x => x.mainId === this.issuedId);

    if (!selected || !areaUnit || isNaN(pcs)) return;

    let areaPerPiece = 0;

    if (areaUnit === 'Sq.Feet') {
      areaPerPiece = Number(selected.areaInFeet) || 0;
    } else if (areaUnit === 'Sq.Yard') {
      areaPerPiece = Number(selected.areaInYard) || 0;
    }

    const totalArea = (areaPerPiece * pcs).toFixed(2);
    const suffix = areaUnit === 'Sq.Feet' ? 'Ft' : areaUnit === 'Sq.Yard' ? 'Yd' : '';
    this.frmOrderIssue.get('area')?.setValue(`${totalArea} ${suffix}`);

    this.calculateAmount(); // recalculate amount after area is patched
  }


  onAreaChange(event: MatSelectChange) {
    console.log("🟢 Selected AreaIn:", event.value);
    this.AreaIn = event.value;
    this.calculateArea();
  }


  calculateAmount() {
    const areaRaw = this.frmOrderIssue.get('area')?.value || '0';
    const rateRaw = this.frmOrderIssue.get('rate')?.value || 0;

    // Extract numeric value from area string (e.g., "10.50 Ft" -> 10.50)
    const area = parseFloat(areaRaw.toString().split(' ')[0]) || 0;
    const rate = Number(rateRaw) || 0;

    // Check for valid numbers
    if (isNaN(area) || isNaN(rate)) {
      this.frmOrderIssue.get('amount')?.setValue('0.00');
      return;
    }

    // Round to nearest integer, then always show .00 as string
    let amount = Math.round(area * rate);
    let amountStr = amount.toFixed(2); // Always show two decimals as string
    this.frmOrderIssue.get('amount')?.setValue(amountStr);
  }



  // message = 'lorem lorem'

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  @ViewChild('rateInput') rateInput!: ElementRef;
  @ViewChild('pcsInput') pcsInput!: ElementRef;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;

    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  customerList() {
    this.manufactureService.getAllBuyerOrder().subscribe((res) => {
      this.ordernoList = (res as BuyerOrder[]).filter(order =>
        (order.items as BuyerOrder[]).some(item => item.PcsesPending > 0)
      );
    });
  }
//  customerList() {
//     this.manufactureService.getAllBuyerOrder().subscribe((res) => {
//       this.ordernoList = (res as BuyerOrder[]).filter(order =>
//         (order.items as BuyerOrder[]).some(item => (item.PcsesPending || 0) > 0)
//       );
//     });
//   }

  // customerList() {
  //   debugger
  //   this.manufactureService.getAllBuyerOrder().subscribe((res: any) => {
  //     debugger
  //     this.ordernoList = res;
  //     console.log(this.ordernoList)
  //     const filteredNumbers = this.ordernoList
  // .filter(num => num !== 0)

  //   });
  // }
  getWeaverList() {

    this.adminServices.getAllWeaverEmployee().subscribe({
      next: (value: any) => {
        const filteredData = value.filter((item: { groupName: string; }) => item.groupName === "Weaver/Contractor");
        // Sort by weaver.name alphabetically (case-insensitive, ignore null)
        filteredData.sort((a: any, b: any) => {
          const nameA = (a.name || '').trim().toLowerCase();
          const nameB = (b.name || '').trim().toLowerCase();
          return nameA.localeCompare(nameB, 'en', { sensitivity: 'base' });
        });
        this.weaverList = filteredData;
      },
      error: (err) => {
        console.log(err);
      },
    });
  }
onWeaverChange(event: any) {
  const selectedWeaverId = event.value;
  const selectedWeaver = this.weaverList.find(w => w._id === selectedWeaverId);

  console.log('🔍 Weaver Changed:', selectedWeaverId);
  console.log('🔍 Found Weaver:', selectedWeaver);

  let branchCode = 'K'; // Default fallback

  // First priority: Check if weaver has proper branch code
  if (selectedWeaver && selectedWeaver.branch?.branchCode) {
    branchCode = selectedWeaver.branch.branchCode;
    console.log('✅ Using branch code from weaver data:', branchCode);
  }
  // Second priority: Extract from weaver name pattern
  else if (selectedWeaver && selectedWeaver.name) {
    const weaverName = selectedWeaver.name.trim();
    console.log('🔍 Extracting branch code from weaver name:', weaverName);

    // Check for patterns like "K - Sherbano", "H - Test", "A - Someone"
    const prefixMatch = weaverName.match(/^([A-Z])\s*[-]\s*/);
    if (prefixMatch && prefixMatch[1]) {
      branchCode = prefixMatch[1].toUpperCase();
      console.log('✅ Extracted branch code from name pattern:', branchCode);
    } else {
      console.log('⚠️ Could not extract branch code from name, using default:', branchCode);
    }
  } else {
    console.log('⚠️ No weaver data or name found, using default branch code:', branchCode);
  }

  console.log('🔍 Final branch code to use:', branchCode);
  this.getsIssueList(branchCode);
}




  selectedOrder: any; // Yeh buyer order ka selected item store karega
  items: any;
  newData: any[] = [];
  existIssuedData: any;
  selectedOrderNo(id: string) {
    this.items = this.ordernoList.find((ids: any) => ids._id === id);
    this.items = this.ordernoList.find((order: any) => order._id === id);
    console.log("Selected Order:", this.items);

    if (this.items) {
      this.selectedOrder = this.items.items[0]; // Pehla item select karna, agar multiple ho to logic update karo
    }
    console.log(this.ordernoList)
    this.manufactureService.getsOrderIssueList().subscribe({
      next: (value: any) => {
        this.existIssuedData = value.filter((q: any) => q._id === this.editId);

        this.newData = this.items.items.map((x: any) => ({
          mainId: x._id,
          qid: x.quality?._id,
          quality: x.quality?.quality,
          did: x.design?._id,
          design: x.design?.design,
          borderColour: x.groundColour,
          sid: x.sizeId?._id,
          size: x.sizeId?.sizeInYard,
          khapSize: x.sizeId?.khapSize,
          areaInFeet: x.sizeId?.areaInfeet,
          areaInYard: x.sizeId?.areaInYard,
          pcs: parseInt(x.PcsesPending),
          totalAreaInYd: (parseInt(x.PcsesPending) * (x.sizeId?.areaInYard || 0)).toFixed(2),
          amount: x.amount
        })).filter((item: any) => item.pcs > 0);
  console.log(this.newData)
        this.dataSource = new MatTableDataSource(this.newData);

        this.ngAfterViewInit();

        let data = this.newData.find((x: any) => x.mainId === this.existIssuedData[0]?.itemId);
        this.selectItem(data);


      },
    });
  }








  setDigit(val: any) {
    let _val = parseInt(val.target.value);
    this.frmOrderIssue.get('rate')?.patchValue(_val.toFixed(2));
  }


  // Also call after getsIssueList for new
getsIssueList(branchCodeFromWeaver: string = 'K') {
  console.log('🔍 getsIssueList called with branch code:', branchCodeFromWeaver);

  this.manufactureService.getsOrderIssueList().subscribe({
    next: (value: any) => {
      console.log('🔍 All existing issue numbers:', value.map((item: any) => item.Br_issueNo));

      let nextIssueNo = `${branchCodeFromWeaver}-2400001`;

      if (value && value.length > 0) {
        // Find the highest sequential number across ALL branches (not just current branch)
        let maxNum = 2400000;

        value.forEach((item: any) => {
          if (item.Br_issueNo && item.Br_issueNo.includes('-')) {
            const parts = item.Br_issueNo.split('-');
            const num = parseInt(parts[1]);
            if (!isNaN(num) && num > maxNum) {
              maxNum = num;
              console.log('🔍 New max sequential number found:', maxNum);
            }
          }
        });

        // Generate next sequential number with current weaver's branch prefix
        const nextSequentialNum = maxNum + 1;
        nextIssueNo = `${branchCodeFromWeaver}-${nextSequentialNum}`;

        console.log('🔍 Calculated next issue number:', nextIssueNo);
        console.log('🔍 Using branch prefix:', branchCodeFromWeaver);
        console.log('🔍 Next sequential number:', nextSequentialNum);
      } else {
        console.log('🔍 No existing issues found, using default starting number');
      }

      if (!this.isUpdated) {
        this.frmOrderIssue.get('Br_issueNo')?.setValue(nextIssueNo);
        console.log('✅ Set Br_issueNo in form:', nextIssueNo);
      } else {
        console.log('🔍 Form is in update mode, not setting Br_issueNo');
      }

      this.updateFormControlStates();
      console.log('✅ Final Generated Br_issueNo:', nextIssueNo);
    },
    error: (err) => {
      console.error('❌ Error in getsIssueList:', err);
    }
  });
}


  // incrementId(currentId: any) {
  //   const incrementedId = parseInt(currentId.Br_issueNo, 10) + 1;
  //   const incrementedIdString = incrementedId
  //     .toString()
  //     .padStart(currentId.length, '0');
  //   return incrementedIdString;
  // }
  incrementId(currentBrIssueNo: string): string {
    if (!currentBrIssueNo) return 'K-2400001';

    // Extract branchCode and number part
    const parts = currentBrIssueNo.split('-');
    const prefix = parts.length > 1 ? parts[0] : 'K';
    const numberPart = parts.length > 1 ? parts[1] : parts[0].replace(/\D/g, '');

    // If no Br_issueNo has been saved yet, start from 2400001
    if (!numberPart || numberPart === '00001') {
      return 'K-2400001';
    }

    const incrementedNum = (parseInt(numberPart) || 2400000) + 1;
    const incrementedStr = incrementedNum.toString();

    return `${prefix}-${incrementedStr}`;
  }


  issueCarpet() {
    // Manual validation for rate field (strict check for blank, null, 0, or non-numeric)
    const rateValue = this.frmOrderIssue.get('rate')?.value;
    if (
      rateValue === '' ||
      rateValue === null ||
      rateValue === undefined ||
      isNaN(Number(rateValue)) ||
      Number(rateValue) <= 0
    ) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Rate cannot be blank or zero. Please enter a valid rate.',
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      this.frmOrderIssue.get('rate')?.setValue('');
      return;
    }
    if (this.frmOrderIssue.invalid) {
      this.showValidationErrors();
      return;
    }
    console.log('Form valid?', this.frmOrderIssue.valid);
console.log('Form value:', this.frmOrderIssue.value);

    let getData = this.mapItem();
    console.log("🟢 Final Request Payload:", getData);  // <-- Debugging
    this.manufactureService.postOrderIssueList(getData).subscribe({
      next: (value: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Order has been created successfully!',
          icon: 'success',
          confirmButtonText: 'OK'
        }).then(() => {
          this.insertedId = value._id;
          localStorage.setItem('insertedId', value._id);

          window.location.reload();

        });
      },
      error: (err) => {
        Swal.fire('Error', 'Failed to create order. Please try again.', 'error');
        console.error(err);
      },
      // error: (err) => {
      //   console.error('HttpErrorResponse', err); // ← THIS
      // }

    });
  }




  // Remove duplicate editIssueOrder implementation
  file: any;
  chooseFile(event: any) {
    this.file = event.target.files[0];
  }

  showValidationErrors() {
    let errorFields = Object.keys(this.frmOrderIssue.controls)
      .filter(key => this.frmOrderIssue.get(key)?.invalid)
      .map(key => key.replace(/([A-Z])/g, ' $1').trim());

    if (errorFields.length > 0) {
      Swal.fire({
        title: 'Validation Error',
        text: `Please fill the following fields: ${errorFields.join(', ')}`,
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return false;  // Stop further execution
    }
    return true;  // Proceed if valid
  }



 pcsReceived: number = 0;

  // Store original form values to use during update
  originalFormValues: any = {};

  get isPCSReceivedZero(): boolean {
    return this.pcsReceived === 0;
  }
  get isPCSReceivedGreaterThanZero(): boolean {
    return this.pcsReceived > 0;
  }

  // Call this after loading/editing an order or when pcsReceived changes
  updateFormControlStates() {
    // If creating a new order, enable all controls and return
    if (!this.isUpdated) {
      Object.keys(this.frmOrderIssue.controls).forEach(key => {
        this.frmOrderIssue.get(key)?.enable({ emitEvent: false });
      });
      return;
    }
    // Always start by enabling all controls
    Object.keys(this.frmOrderIssue.controls).forEach(key => {
      this.frmOrderIssue.get(key)?.enable({ emitEvent: false });
    });
    // PCSReceived = 0: Br. Issue No, Buyer Order No, Area In, pcs are disabled; others enabled; HIDE Order List
    if (this.isPCSReceivedZero) {
      this.frmOrderIssue.get('Br_issueNo')?.disable();
      this.frmOrderIssue.get('buyerOrder')?.disable();
      this.frmOrderIssue.get('AreaIn')?.disable();
      this.frmOrderIssue.get('pcs')?.disable();
      this.showOrderList = false; // Hide Order List when PCSReceived = 0
      // All others remain enabled
    }
    // PCSReceived > 0: Weaver, Br. Issue No, Date, Buyer Order No, Area In, Rate are disabled; pcs enabled; HIDE Order List
    else if (this.isPCSReceivedGreaterThanZero) {
      this.frmOrderIssue.get('weaver')?.disable();
      this.frmOrderIssue.get('Br_issueNo')?.disable();
      this.frmOrderIssue.get('date')?.disable();
      this.frmOrderIssue.get('buyerOrder')?.disable();
      this.frmOrderIssue.get('AreaIn')?.disable();
      this.frmOrderIssue.get('rate')?.disable();
      this.frmOrderIssue.get('pcs')?.enable();
      this.showOrderList = false; // Hide Order List when PCSReceived > 0
    }
  }

  setAutoFocus() {
    setTimeout(() => {
      if (this.pcsReceived === 0) {
        // Focus on rate field when PCSReceived = 0
        if (this.rateInput && this.rateInput.nativeElement) {
          this.rateInput.nativeElement.focus();
        }
      } else if (this.pcsReceived > 0) {
        // Focus on pcs field when PCSReceived > 0
        if (this.pcsInput && this.pcsInput.nativeElement) {
          this.pcsInput.nativeElement.focus();
        }
      }
    }, 100);
  }

  // Call updateFormControlStates after edit/load
  editIssueOrder(id: string) {
    this.manufactureService.getOrderIssue(id).subscribe({
      next: (value: any) => {
         console.log ('🔍 Frontend: Received carpet order issue data:', value)
        this.isUpdated = true;
        this.editId = id;
        this.pcsReceived = value.PCSReceived ?? value.pcsReceived ?? 0;
        console.log('🔍 Frontend: pcsReceived', this.pcsReceived)
        console.log('🔍 Frontend: value.itemId:', value.itemId, 'value._id:', value._id);
        this.issuedId = value.itemId || value._id || '';
        console.log('🔍 Frontend: Set issuedId to:', this.issuedId);
        // Enable all controls before patching to avoid Angular warnings
        Object.keys(this.frmOrderIssue.controls).forEach(key => {
          this.frmOrderIssue.get(key)?.enable({ emitEvent: false });
        });
        // Store display names
        this.qualityDisplayName = value.quality?.quality || '';
        this.designDisplayName = value.design?.design || '';
        this.sizeDisplayName = value.size?.sizeInYard || '';

        this.frmOrderIssue.patchValue({
          pcsReceived: value.PCSReceived,
          Br_issueNo: value.Br_issueNo,
          date: new Date(value.date),
          buyerOrder: value.buyerOrder?._id || value.buyerOrder,
          weaver: value.weaver?._id || value.weaver,
          AreaIn: value.areaIn,
          area: value.area,
          rate: value.rate,
          amount: value.amount,
          MapOrderNo: value.MapOrderNo,
          quality: value.quality?._id || value.quality,
          design: value.design?._id || value.design,
          borderColour: value.borderColour,
          size: value.size?._id || value.size,
          khapSize: value.khapSize,
          pcs: value.pcs,
        });

        // Store original values before disabling any controls
        this.originalFormValues = {
          Br_issueNo: value.Br_issueNo,
          date: new Date(value.date),
          buyerOrder: value.buyerOrder?._id || value.buyerOrder,
          weaver: value.weaver?._id || value.weaver,
          AreaIn: value.areaIn,
          area: value.area,
          rate: value.rate,
          amount: value.amount,
          MapOrderNo: value.MapOrderNo,
          quality: value.quality?._id || value.quality,
          design: value.design?._id || value.design,
          borderColour: value.borderColour,
          size: value.size?._id || value.size,
          khapSize: value.khapSize,
          pcs: value.pcs,
        };
        console.log('🔍 Frontend: Stored original form values:', this.originalFormValues);

        this.updateFormControlStates();

        // Auto-focus based on PCSReceived value
        this.setAutoFocus();
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  // Also call after selectItem (if PCSReceived can change)
  selectItem(id: any) {
    if (!id || !id.mainId) {
      return;
    }
    this.issuedId = id.mainId;
    this.frmOrderIssue.patchValue(id);

    // Set display names for Quality, Design, Size
    this.qualityDisplayName = id.quality || '';
    this.designDisplayName = id.design || '';
    this.sizeDisplayName = id.size || '';

    this.pcsReceived = id.PCSReceived || id.pcsReceived || id.pcs || 0;
    if (this.existIssuedData && Array.isArray(this.existIssuedData) && this.existIssuedData.length > 0) {
      let pcs = id.mainId === this.existIssuedData[0]?.itemId ? this.existIssuedData[0]?.pcs : 1;
      this.frmOrderIssue.get('pcs')?.setValue(pcs);
    } else {
      this.frmOrderIssue.get('pcs')?.setValue(1);
    }
    this.updateFormControlStates();
    this.cdr.detectChanges();
    this.ngZone.runOutsideAngular(() => {
      setTimeout(() => {
        if (this.rateInput && this.rateInput.nativeElement) {
          this.rateInput.nativeElement.focus();
        }
      }, 200);
    });
  }




  update() {
    // Manual validation for rate field (strict check for blank, null, 0, or non-numeric)
    const rateValue = this.frmOrderIssue.get('rate')?.value;
    if (
      rateValue === '' ||
      rateValue === null ||
      rateValue === undefined ||
      isNaN(Number(rateValue)) ||
      Number(rateValue) <= 0
    ) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Rate cannot be blank or zero. Please enter a valid rate.',
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      this.frmOrderIssue.get('rate')?.setValue('');
      return;
    }
    if (this.frmOrderIssue.invalid) {
      this.showValidationErrors();
      return;
    }

    let getData = this.mapItem();
    console.log('Frontend: Sending update with editId:', this.editId);
    console.log('Frontend: FormData being sent:', getData);
    this.manufactureService.updateOrderIssue(this.editId, getData).subscribe({
      next: () => {
        Swal.fire({
          title: 'Success!',
          text: 'Order has been updated successfully!',
          icon: 'success',
          confirmButtonText: 'OK'
        }).then(() => {
          this.router.navigate(['/admin/view-carpet-order-issue']);
        });
      },
      error: (err) => {
        console.error('Update error:', err);
        let errorMessage = 'Failed to update order. Please try again.';

        // Handle specific validation errors
        if (err.error && err.error.message) {
          errorMessage = err.error.message;
        } else if (err.message) {
          errorMessage = err.message;
        }

        Swal.fire('Error', errorMessage, 'error');
      },
    });
  }

  mapItem() {
    let data = this.newData.find((x: any) => x.mainId === this.issuedId);
    console.log(`🔍 my data:`, data);
    console.log(`🔍 issuedId:`, this.issuedId);
    console.log(`🔍 newData:`, this.newData);
    console.log(`🔍 isUpdated:`, this.isUpdated);

    // For edit mode, if data is not found in newData, create a fallback data structure
    if (!data && this.isUpdated) {
      console.log('🔄 Edit mode: Creating fallback data structure');
      // Get current form values including disabled fields and create a basic data structure
      const formValues = this.frmOrderIssue.getRawValue();
      console.log('🔍 Frontend: Fallback using raw form values:', formValues);
      data = {
        mainId: this.issuedId,
        qid: formValues.quality, // This should already be the ID from the form
        did: formValues.design,  // This should already be the ID from the form
        borderColour: formValues.borderColour,
        sid: formValues.size,    // This should already be the ID from the form
        khapSize: formValues.khapSize,
        amount: formValues.amount
      };
      console.log('🔄 Created fallback data:', data);
    }

    if (data) {
        // Use getRawValue to get all form values including disabled fields
        const rawFormValues = this.frmOrderIssue.getRawValue();
        console.log("🔍 Frontend: Raw form values (including disabled):", rawFormValues);
        console.log("🔍 Frontend: Specific disabled field values:");
        console.log("  Br_issueNo:", rawFormValues.Br_issueNo);
        console.log("  buyerOrder:", rawFormValues.buyerOrder);
        console.log("  pcs:", rawFormValues.pcs);

        let brch = this.weaverList.find(
            (bc: any) => bc._id === rawFormValues.weaver
        );

        this.calculateAmount(); // Ensure amount is calculated

        let formAmount = this.frmOrderIssue.get('amount')?.value;
        // Always send amount as string with .00
        let calculatedAmount = !isNaN(parseFloat(formAmount))
            ? parseFloat(formAmount).toFixed(2)
            : (data.amount || '0.00');

        console.log("✅ Corrected Amount:", calculatedAmount);

        // Ensure all critical fields are included, even if disabled
        // Use original values for disabled fields, current values for enabled fields
        this.setData = {
            quality: data.qid,
            design: data.did,
            borderColour: data.borderColour,
            size: data.sid,
            khapSize: data.khapSize,
            itemId: this.issuedId || '',
            Br_issueNo: this.frmOrderIssue.get('Br_issueNo')?.disabled ? this.originalFormValues.Br_issueNo : rawFormValues.Br_issueNo,
            date: this.frmOrderIssue.get('date')?.disabled ? this.originalFormValues.date : rawFormValues.date,
            buyerOrder: this.frmOrderIssue.get('buyerOrder')?.disabled ? this.originalFormValues.buyerOrder : rawFormValues.buyerOrder,
            weaver: this.frmOrderIssue.get('weaver')?.disabled ? this.originalFormValues.weaver : rawFormValues.weaver,
            pcs: this.frmOrderIssue.get('pcs')?.disabled ? this.originalFormValues.pcs : rawFormValues.pcs,
            area: this.frmOrderIssue.get('area')?.disabled ? this.originalFormValues.area : rawFormValues.area,
            AreaIn: this.AreaIn,
            rate: this.frmOrderIssue.get('rate')?.disabled ? this.originalFormValues.rate : rawFormValues.rate,
            amount: calculatedAmount,
            MapOrderNo: this.frmOrderIssue.get('MapOrderNo')?.disabled ? this.originalFormValues.MapOrderNo : rawFormValues.MapOrderNo,
            branch: brch ? brch.branch._id : '',
            existingData: '',
        };

        console.log("✅ Frontend: Mapped Data:", this.setData);
        console.log("🔍 Frontend: Sending itemId:", this.setData.itemId, "Type:", typeof this.setData.itemId);
        console.log("🔍 Frontend: Critical fields in setData:");
        console.log("  Br_issueNo:", this.setData.Br_issueNo);
        console.log("  buyerOrder:", this.setData.buyerOrder);
        console.log("  pcs:", this.setData.pcs);

        if (this.existIssuedData && this.existIssuedData.length > 0) {
            this.setData.existingData = JSON.stringify(this.existIssuedData);
        }

        localStorage.setItem('printData', JSON.stringify(this.setData));

        const formData = new FormData();

        Object.keys(this.setData).forEach((key: string) => {
            const value = this.setData[key as keyof typeof this.setData];
            if (value !== null && value !== undefined) {
                formData.append(key, value.toString());
            }
        });

        // ✅ FIXED: Using forEach instead of entries()
        console.log("✅ Final FormData Before Sending:");
        formData.forEach((value, key) => {
            console.log(`  ${key}: ${value}`);
        });

        if (this.file) {
            formData.append('uploadFile', this.file, this.file.name);
        } else {
            console.warn('⚠️ No file selected');
        }

        return formData;
    } else {
        console.error('❌ Data still not found after fallback attempt');
        console.error('❌ issuedId:', this.issuedId);
        console.error('❌ Available newData:', this.newData);

        // This should not happen now, but just in case
        throw new Error('Unable to create form data for update');
    }
}



matchPcs() {
    let getPcs = Number(this.frmOrderIssue.get('pcs')?.value);
    console.log('PCS Validation - getPcs:', getPcs, 'pcsReceived:', this.pcsReceived, 'isPCSReceivedGreaterThanZero:', this.isPCSReceivedGreaterThanZero, 'isUpdated:', this.isUpdated);

    // Only apply strict validation for UPDATE mode
    if (this.isUpdated) {
      // Prevent pcs below pcsReceived if pcsReceived > 0
      if (this.isPCSReceivedGreaterThanZero && getPcs < this.pcsReceived) {
        Swal.fire({
          title: 'PCS Validation',
          text: `PCS cannot be less than already received (${this.pcsReceived}). Minimum allowed: ${this.pcsReceived}`,
          icon: 'warning',
          confirmButtonText: 'OK'
        });
        this.frmOrderIssue.get('pcs')?.setValue(this.pcsReceived);
        return;
      }

      // NEW VALIDATION: Prevent PCS from exceeding current issued PCS + PcsesPending
      let currentIssuedPcs = 0;
      if (this.existIssuedData && Array.isArray(this.existIssuedData) && this.existIssuedData.length > 0) {
        currentIssuedPcs = parseInt(this.existIssuedData[0]?.pcs) || 0;
      }

      // Get current PcsesPending from newData (which has the correct pending pieces)
      let currentPcsesPending = 0;
      let newDataItem = this.newData.find((x:any)=>x.mainId===this.issuedId);
      currentPcsesPending = parseInt(newDataItem?.pcs) || 0;

      // If not found in newData, try to get from original buyer order item
      if (currentPcsesPending === 0 && this.items && this.items.items) {
        let currentItem = this.items.items.find((x: any) => x._id === this.issuedId);
        currentPcsesPending = parseInt(currentItem?.PcsesPending) || 0;
      }

      let maxAllowedPcs = currentIssuedPcs + currentPcsesPending;

      console.log('PCS Validation Details:', {
        currentIssuedPcs,
        currentPcsesPending,
        maxAllowedPcs,
        getPcs,
        issuedId: this.issuedId,
        pcsReceived: this.pcsReceived
      });

      // Only validate max limit if we have valid data and it's greater than 0
      if (maxAllowedPcs > 0 && getPcs > maxAllowedPcs) {
        Swal.fire({
          title: 'PCS Validation',
          text: `PCS cannot exceed ${maxAllowedPcs} (Current Issued: ${currentIssuedPcs} + Available Pending: ${currentPcsesPending})`,
          icon: 'warning',
          confirmButtonText: 'OK'
        });
        // Set to minimum allowed value
        const minValue = this.pcsReceived > 0 ? this.pcsReceived : 1;
        const maxValue = Math.min(maxAllowedPcs, minValue);
        this.frmOrderIssue.get('pcs')?.setValue(Math.max(maxValue, minValue));
        return;
      }

      // For update mode when PCSReceived = 0, validate against available pieces
      if (this.isPCSReceivedZero) {
        let existPcs = this.newData.find((x:any)=>x.mainId===this.issuedId)?.pcs||0;
        if(getPcs > existPcs || getPcs < 1){
          Swal.fire({
            title: 'PCS Validation',
            text: 'Please issue item pcs less or equal to order pcs',
            icon: 'warning',
            confirmButtonText: 'OK'
          });
          this.frmOrderIssue.get('pcs')?.setValue(1);
          return;
        }
      }
    } else {
      // For NEW orders, add validation with alert as requested
      let existPcs = this.newData.find((x:any)=>x.mainId===this.issuedId)?.pcs||'';
      if(getPcs > existPcs && getPcs != existPcs || getPcs < 1){
        Swal.fire({
          title: 'PCS Validation',
          text: 'Please issue item pcs less or equal to order pcs',
          icon: 'warning',
          confirmButtonText: 'OK'
        });
        this.frmOrderIssue.get('pcs')?.setValue(1);
        return;
      }
    }

    // Recalculate area and amount when pcs changes
    this.calculateArea();
  }
}
