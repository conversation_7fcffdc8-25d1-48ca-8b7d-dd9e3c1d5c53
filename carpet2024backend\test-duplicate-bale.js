const axios = require('axios');

// Test data with the same bale number
const testData1 = {
  invoiceNo: "66f68847bda4fff511ef97f6", // Use an existing invoice ID
  baleNo: "6", // Use the same bale number that was causing issues
  date: new Date(),
  items: [
    {
      pcsNo: "8",
      carpetNo: "K-2400005",
      baleNo: "6",
      quality: "12 x 60",
      design: "Isfahan 3",
      colour: "Cream/Beige",
      size: "1.11 x 3.11",
      area: 0.83,
      tArea: 0.83,
      remarks: "Test 1",
      areaDisplay: "0.83 Yd",
      areaIn: "SqFt",
      serialNo: 1,
      addedAt: new Date()
    }
  ],
  totalArea: 0.83,
  totalTArea: 0.83,
  totalPcses: 1,
  createdAt: new Date()
};

const testData2 = {
  invoiceNo: "66f68847bda4fff511ef97f6", // Same invoice ID
  baleNo: "6", // Same bale number
  date: new Date(),
  items: [
    {
      pcsNo: "9",
      carpetNo: "K-2400006",
      baleNo: "6",
      quality: "12 x 60",
      design: "Isfahan 4",
      colour: "Red/Blue",
      size: "2.11 x 3.11",
      area: 0.93,
      tArea: 0.93,
      remarks: "Test 2",
      areaDisplay: "0.93 Yd",
      areaIn: "SqFt",
      serialNo: 1,
      addedAt: new Date()
    }
  ],
  totalArea: 0.93,
  totalTArea: 0.93,
  totalPcses: 1,
  createdAt: new Date()
};

async function testDuplicateBale() {
  try {
    console.log('Sending first request...');
    const response1 = await axios.post('http://localhost:2000/api/phase-four/exportPackingList/exportPacking', testData1);
    console.log('First request successful:', response1.data);
    
    console.log('Sending second request with same bale number...');
    const response2 = await axios.post('http://localhost:2000/api/phase-four/exportPackingList/exportPacking', testData2);
    console.log('Second request successful:', response2.data);
    
    console.log('Test passed! Both requests were successful with the same bale number.');
  } catch (error) {
    console.error('Test failed:', error.response ? error.response.data : error.message);
  }
}

testDuplicateBale();
