// routes/retailerRoutes.js

const express = require('express');
const router = express.Router();
const retailerController = require('../../controller/retailer-controller');

router.post('/create-retailer', retailerController.createRetailer);
router.get('/getAll-retailer', retailerController.getRetailers);
router.get('/get-retailer/:id', retailerController.getRetailerById);
router.put('/update-retailer/:id', retailerController.updateRetailer);
router.delete('/delete-retailer/:id', retailerController.deleteRetailer);
router.patch('/add-retailer-price/:id',retailerController.addRetailerPrice)
module.exports = router;
