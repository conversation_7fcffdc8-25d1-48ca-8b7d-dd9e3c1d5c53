import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import Swal from 'sweetalert2';
import { SweetalertService } from '../../../services/sweetalert.service';
import { Global } from '../../../Shared/validators';
export interface UserData {
  group: string;
  id: string;
  subGroup:string;
  index: number;
  Material: string;
  count: string;
  partyname: string;
  address: string;
  zipcode: string;
  contractno: string;
  email: string;
  gstno: string;
  panno: string;
}

/** Constants used to fill up our data base. */
const ELEMENT_DATA: UserData[] = [];
@Component({
  selector: 'app-purchase-details',
  templateUrl: './purchase-details.component.html',
  styleUrl: './purchase-details.component.css',
})
export class PurchaseDetailsComponent implements OnInit {
  Group = 'option2';
  Count = 'option1';
  Material = 'option3';

  displayedColumns: string[] = [
    'id',
    'group',
    'subGroup',
    'Material',
    'count',
    'partyname',
    'address',
    'zipcode',
    'contractno',
    'email',
    'gstno',
    'panno',
    'action',
  ];

  groupList: any = [
    'Raw Material',
    'Dyes & Chemicals',
    'Plastic Material',
    'Wood',
  ];

  subGroupList:any =['Wool', 'Tana', 'Tharri', 'Silk', 'Others'];
  materialList: any = [];
  countList: any = [12251, 1542411, 525414];

  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);

  editId!: string;
  isUpdated: boolean = false;

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  constructor(
    private services: MasterService,
    private fb: FormBuilder,
    private alertService: SweetalertService
  ) {}
  frmPurchaseDetails!: FormGroup;
  ngOnInit(): void {
    this.frmPurchaseDetails = this.fb.group({
      group: ['',[Validators.required]],
      subGroup: ['',[Validators.required]],
      Material: ['',[Validators.required]],
      count: ['',[Validators.required]],
      partyname: ['',[Validators.required,Validators.pattern(Global.nameRegex)]],
      address: ['',[Validators.required]],
      zipcode: ['',[Validators.required]],
      contractno: ['',[ Validators.required,Validators.pattern(Global.contactRegex)]],
      email: ['',[ Validators.required,Validators.pattern(Global.emailRegex)]],
      gstno: [''],

      panno: [''],
    });
    this.gets();
    this.rawMaterialList();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }
  materialName:any;

  material(data:any){
    debugger
this.materialName = data;
  }
  addPurchaseDetails() {
    let formData = this.frmPurchaseDetails.value;
    formData.Material = this.materialName;
    debugger;
    this.services.addPurchaseDetails(formData).subscribe({
      next: (value) => {
        if (value) {
          this.alertService.success(
            'success',
            'Purchase detail has been saved'
          );
          this.gets();
        }
      },
      error: (err) => {
        this.alertService.error('warning', 'Something went wrong');
      },
    });
  }

  rawMaterialList() {
    this.services.getsRawMaterial().subscribe({
      next: (value: any) => {
        value.map((v: any) => {
          this.materialList.push({
            material: v.Item,
            id: v._id,
          });
        });
        // this.alertService.success('success', 'Raw material list');
      },
      error: (err) => {
        this.alertService.error('Failed', 'Something went wrong');
      },
    });
  }
  gets() {
    this.services.getsPurchaseDetails().subscribe({
      next: (value: any) => {
        ELEMENT_DATA.length = 0;
        value.map((v: any, i: number) => {
          debugger;
          ELEMENT_DATA.push({
            id: v._id,
            index: i + 1,
            group: v.group,
            subGroup:v.subGroup,
            Material: v.Material ? v.Material.Item : 'N/A',
            count: v.count,
            partyname: v.partyname,
            address: v.address,
            zipcode: v.zipcode,
            contractno: v.contractno,
            email: v.email,
            gstno: v.gstno,
            panno: v.panno,
          });
        });

        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
      },
    });
  }

  edit(id: any) {
    this.services.getPurchaseDetails(id).subscribe({
      next: (value) => {
        this.isUpdated = true;
        this.editId = id;
        this.frmPurchaseDetails.patchValue(value);
      },
      error: (err) => {
        this.alertService.error('Failed', 'Something went wrong');
      },
    });

    debugger;
  }
  delete(id: string) {
    this.alertService
      .confirm({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        confirmButtonText: 'Yes, delete it!',
      })
      .then((result) => {
        if (result.isConfirmed) {
          this.services.deletePurchaseDetails(id).subscribe({
            next: (value) => {
              this.alertService.success(
                'Deleted!',
                'purchase details has been deleted.'
              );
              this.gets();
            },
            error: (err) => {
              this.alertService.success('Failed!', 'Something went wrong');
            },
          });
        }
      });
  }

  update(){
    let formData = this.frmPurchaseDetails.value;
    debugger;
    formData.Material = this.materialName;
    this.services.updatePurchaseDetails(this.editId,formData).subscribe({
      next: (value) => {
        if (value) {
          this.alertService.success(
            'success',
            'Purchase detail has been updated'
          );
          this.gets();
        }
      },
      error: (err) => {
        this.alertService.error('warning', 'Something went wrong');
      },
    });
  }
}
