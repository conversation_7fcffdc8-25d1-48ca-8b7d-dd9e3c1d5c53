var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Czech = {
    weekdays: {
        shorthand: ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Čtvrtek",
            "Pátek",
            "Sobota",
        ],
    },
    months: {
        shorthand: [
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>v<PERSON>",
            "Srp",
            "<PERSON><PERSON><PERSON>",
            "Říj",
            "<PERSON><PERSON>",
            "<PERSON>",
        ],
        longhand: [
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Kvě<PERSON>",
            "Červen",
            "Červenec",
            "Srpen",
            "<PERSON><PERSON><PERSON><PERSON>",
            "Říjen",
            "Listopad",
            "Prosinec",
        ],
    },
    firstDayOfWeek: 1,
    ordinal: function () {
        return ".";
    },
    rangeSeparator: " do ",
    weekAbbreviation: "Týd.",
    scrollTitle: "Rolujte pro změnu",
    toggleTitle: "Přepnout dopoledne/odpoledne",
    amPM: ["dop.", "odp."],
    yearAriaLabel: "Rok",
    time_24hr: true,
};
fp.l10ns.cs = Czech;
export default fp.l10ns;
