import { Injectable } from "@angular/core";
import { environment } from "../../environments/environment.development";
import { HttpClient } from "@angular/common/http";

@Injectable(
  { providedIn:'root'}
)
export class SelectListServices {
  apiUrl: any = environment.apiUrl;
  phaseOne: any = `${this.apiUrl}/phase-one`;
  phaseTwo: any = `${this.apiUrl}/phase-two`;
  phaseThree: any = `${this.apiUrl}/phase-three`;
  phaseFour:any=`${this.apiUrl}/phase-four`;
  constructor(private http: HttpClient) {}

  qualityListDDL(){
// return this
  }
 designListDDL(){

  }
  sizeListDDL(){

  }
}
