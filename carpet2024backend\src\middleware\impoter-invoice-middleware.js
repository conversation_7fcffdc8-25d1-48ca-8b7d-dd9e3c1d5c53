
const ImpoterDetails = require('../model/phase-1/packageListExcel');

const checkDuplicateEntry = async (req, res, next) => {
    const { impoterName, invoiceNo } = req.body;
    try {
        console.log("Checking for duplicate entry with invoice number:", invoiceNo);

        // Check for existing entries with the same invoice number but different importer names
        const existingEntry = await ImpoterDetails.findOne({ invoiceNo, impoterName: { $ne: impoterName } });

        if (existingEntry) {
            console.log("Duplicate entry found with a different importer name:", existingEntry.impoterName);
            // return res.status(400).json({ message: 'Invoice number already exists with a different importer name' });
        }
        
        console.log("No duplicate entry found with the same invoice number.");
        next();
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};

module.exports = { checkDuplicateEntry };

// // const ImpoterDetails = require('../model/phase-1/packageListExcel');

// // const checkDuplicateEntry = async (req, res, next) => {
// //     const { impoterName, invoiceNo } = req.body;
// //     try {

// //         // Check for existing entries with the same invoice number
// //         const existingEntries = await ImpoterDetails.find({ invoiceNo });

// //         // If no entries exist with the same invoice number, proceed
// //         if (!existingEntries || existingEntries.length === 0) {
        
// //             return next();
// //         }

// //         // If entries exist with the same invoice number, check for duplicate entries with different importer names
// //         const duplicateEntry = existingEntries.find(entry => entry.impoterName !== impoterName);
// //         if (duplicateEntry) {
// //             return res.status(400).json({ message: 'No duplicate entry found with the same invoice number for the same importer name' });
// //         }
    
// //         next();
// //     } catch (error) {
// //         console.error('Error:', error);
// //         res.status(500).json({ message: 'Internal Server Error' });
// //     }
// // };

// // module.exports = { checkDuplicateEntry };
// const ImpoterDetails = require('../model/phase-1/packageListExcel');

// const checkDuplicateEntry = async (req, res, next) => {
//     const { impoterName, invoiceNo } = req.body;
//     try {
//         // Check for existing entry with the same importer name and invoice number
//         const existingEntry = await ImpoterDetails.findOne({ impoterName, invoiceNo });

//         if (existingEntry) {
//             return res.status(400).json({ message: 'Entry with the same importer name and invoice number already exists' });
//         }

//         // Check for existing entry with the same invoice number
//         const existingInvoice = await ImpoterDetails.findOne({ invoiceNo });

//         if (existingInvoice) {
//             // Allow data entry if importer name is different for the same invoice number
//             return next();
//         }

//         // If no duplicate found, proceed with the next middleware
//         next();
//     } catch (error) {   
//         console.error('Error:', error);
//         res.status(500).json({ message: 'Internal Server Error' });
//     }
// };

// module.exports = { checkDuplicateEntry };
