// routes/deyingRateRoutes.js
const express = require('express');
const router = express.Router();
const deyingRateController = require('../../controller/deyingRate-controller');

router.post('/deyingRate', deyingRateController.createDeyingRate);
router.get('/deyingRate', deyingRateController.getAllDeyingRates);
router.get('/deyingRate/:id', deyingRateController.getDeyingRateById);
router.put('/deyingRate/:id', deyingRateController.updateDeyingRate);
router.delete('/deyingRate/:id', deyingRateController.deleteDeyingRate);

module.exports = router;
