// routes/userRoutes.js
const express = require('express');
const userController = require('../../controller/user-controller');

const router = express.Router();

// User routes
router.post('/create-user', userController.createUser); //done
router.post('/loginUser',userController.loginUser);
router.post('/add-role/:id', userController.addRole);   //done
router.delete('/remove-role/:id', userController.removeRole);   //done
router.post('/update-password/:id', userController.updateUserPassword); //done
router.get('/get-all-user', userController.getAllUsers); //done
router.get('/get-user/:id', userController.getUserById); //done
router.put('/update-user/:id', userController.updateUser); //done
router.delete('/delete-user/:id', userController.deleteUser); //done

module.exports = router;
