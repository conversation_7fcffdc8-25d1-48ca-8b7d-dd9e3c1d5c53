// services/impoterService.js
const{ ImpoterRepository, AddOnPriceUpdateHistoryRepository} = require('../repositories/impoter-repository');

// class ImpoterService {
//   constructor() {
//     this.impoterRepository = new ImpoterRepository();
//   }

//   async createImpoter(impoterData) {
//     try {
//       return await this.impoterRepository.create(impoterData);
//     } catch (error) {
//       throw error;
//     }
//   }

//   async getAllImpoters() {
//     try {
//       return await this.impoterRepository.findAll();
//     } catch (error) {
//       throw error;
//     }
//   }

//   async getImpoterById(id) {
//     try {
//       return await this.impoterRepository.findById(id);
//     } catch (error) {
//       throw error;
//     }
//   }

//   // async updateImpoter(id, impoterData) {
//   //   try {
//   //     return await this.impoterRepository.update(id, impoterData);
//   //   } catch (error) {
//   //     throw error;
//   //   }
//   // }
//   async updateImpoter(id, impoterData) {
//     try {
//         const updatedImpoter = await this.impoterRepository.update(id, impoterData);
//         return updatedImpoter;
//     } catch (error) {
//         throw error;
//     }
// }


//   async deleteImpoter(id) {
//     try {
//       return await this.impoterRepository.delete(id);
//     } catch (error) {
//       throw error;
//     }
//   }
// }

class ImpoterService {
  constructor() {
    this.impoterRepository = new ImpoterRepository();
  }

  async createImpoter(impoterData) {
    try {
      return await this.impoterRepository.create(impoterData);
    } catch (error) {
      throw error;
    }
  }

  async getAllImpoters() {
    try {
      const impoters = await this.impoterRepository.findAll();
      
      // Create an array to hold both old and updated data
      const impotersWithUpdatedPrices = [];
      
      // Loop through each Impoter record
      for (const impoter of impoters) {
        // Clone the addOnPrice array to avoid modifying the original data
        const oldPrices = JSON.parse(JSON.stringify(impoter.addOnPrice));
        
        // Apply any updates to the addOnPrice array
        // Here you can call the addOrUpdateAddOnPrice method if you need to update prices
        // const updatedPrices = impoter.addOrUpdateAddOnPrice(newPrices);
        
        // Push both the old and updated data into the array
        impotersWithUpdatedPrices.push({ impoter, oldPrices });
      }
      
      return impotersWithUpdatedPrices;
    } catch (error) {
      throw error;
    }
  }

  async getImpoterById(id) {
    try {
      return await this.impoterRepository.findById(id);
    } catch (error) {
      throw error;
    }
  }

  async getAddOnPriceByIdService(query) {
    try {
      return await this.impoterRepository.getAddOnePriceById(query);
    } catch (error) {
      throw error;
    }
  }


  async UpdateAddOnPrice(query, updatedData) {
    try {
      const updatedAddOnPrice = await this.impoterRepository.UpdateOmporterAddOnPrice(query, updatedData);
      return updatedAddOnPrice;
  } catch (error) {
      throw error;
  }
  }

  async updateImpoter(id, impoterData) {
    try {
        const updatedImpoter = await this.impoterRepository.update(id, impoterData);
        return updatedImpoter;
    } catch (error) {
        throw error;
    }
  }

  async deleteImpoter(id) {
    try {
      return await this.impoterRepository.delete(id);
    } catch (error) {
      throw error;
    }
  }
  //////////
  ////// here code for add on price after add importer
  ////
  async updateImpoterPrice(id,reqData){
    try {
      return await this.impoterRepository.updatePrice(id,reqData);
    } catch (error) {
      throw error
    }
  }
}
class AddOnPriceUpdateHistoryService {
  constructor(){
    this.AddOnPriceUpdateHistoryRepository = new AddOnPriceUpdateHistoryRepository()
  }

  async getAddOnPriceUpdateHistory(query) {

    try {
      return await this.AddOnPriceUpdateHistoryRepository.getAllAddOnPriceHistoryData(query);
    } catch (error) {
      return error;
    }

  }

  async createAddOnPriceUpdateHistory(body) {
    try {
      return await this.AddOnPriceUpdateHistoryRepository.create(body);
    } catch (error) {
      throw error;
    }
  }

  
}
module.exports = {ImpoterService,AddOnPriceUpdateHistoryService};
