import { Component, OnInit, ViewChild, AfterViewInit, ElementRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClient } from '@angular/common/http';
import { SweetalertService } from '../../../services/sweetalert.service';
import { Router, ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';

// Interface for packing list items
export interface PackingItem {
  SrNo: number;
  baleNo: string;
  pcsNo: string;
  carpetNo: string;
  quality: string;
  design: string;
  colour: string;
  size: string;
  area: number;
  tArea: number;
  remarks: string;
  areaDisplay?: string;
  areaIn?: string;
  isLastInBale?: boolean;
  serialNo?: number;
  addedAt?: Date;
  invoiceBaleKey?: string; // Added to track the composite key
  invoiceId?: string; // Added to track the invoice ID
  documentId?: string; // Added to store the MongoDB document ID for editing
}

// Interface for carpet data from API
export interface CarpetData {
  _id: string;
  carpetNo: string;
  quality: string;
  design: string;
  colour: string;
  size: string;
  area: number;
  areaIn?: string;
  sizeInYard?: string;
  sizeInMeter?: string;
}

@Component({
  selector: 'app-export-packing-list',
  templateUrl: './export-packing-list.component.html',
  styleUrls: ['./export-packing-list.component.css']
})
export class ExportPackingListComponent implements OnInit, AfterViewInit {
goToInvoicePkg() {
  const selectedInvoiceId = this.carpetEntryForm.get('invoiceNo')?.value;
  console.log('🚀 goToInvoicePkg called with invoice ID:', selectedInvoiceId);
  console.log('🚀 Form value for invoiceNo:', this.carpetEntryForm.get('invoiceNo')?.value);

  if (!selectedInvoiceId) {
    console.log('❌ No invoice selected');
    this.alertService.error('Error', 'Please select an invoice first.');
    return;
  }

  Swal.fire({
    title: 'Go to Invoice PKG?',
    text: 'Proceeding will load the goods section for the selected invoice. Do you want to continue?',
    icon: 'question',
    showCancelButton: true,
    confirmButtonText: 'Yes, go',
    cancelButtonText: 'Cancel',
    customClass: {
      confirmButton: 'mat-raised-button mat-primary',
      cancelButton: 'mat-raised-button mat-warn'
    }
  }).then((result) => {
    if (result.isConfirmed) {
      // Get packing list data for the selected invoice
      const packingListData = this.getPackingListDataForInvoice(selectedInvoiceId);

      // Store packing list data in localStorage for the invoice component to access
      localStorage.setItem('packingListGoodsData', JSON.stringify(packingListData.goods));

      // Store package information separately
      localStorage.setItem('packingListPackageInfo', JSON.stringify({
        noOfBales: packingListData.noOfBales,
        packageDescription: packingListData.packageDescription
      }));

      console.log('🚀 Navigating to invoice with state:', {
        invoiceId: selectedInvoiceId,
        scrollToGoods: true,
        fromPackingList: true,
        loadPackingListGoods: true,
        updateExistingInvoice: true
      });

      this.router.navigate(['/admin/export-invoice'], {
        state: {
          invoiceId: selectedInvoiceId,
          scrollToGoods: true,
          fromPackingList: true,
          loadPackingListGoods: true,
          updateExistingInvoice: true // Flag to indicate we want to update, not create new
        }
      });
    }
  });
}

private getPackingListDataForInvoice(invoiceId: string): any {
  // Get all completed bales for the selected invoice
  const completedBales = this.getCompletedBaleItemsForSelectedInvoice();

  // Group by quality and design to create goods rows
  const goodsMap = new Map<string, any>();

  // Count unique bales
  const uniqueBales = new Set<string>();

  completedBales.forEach(item => {
    const key = `${item.quality}-${item.design}`;

    // Add bale to unique bales set
    if (item.baleNo) {
      uniqueBales.add(item.baleNo);
    }

    if (goodsMap.has(key)) {
      const existing = goodsMap.get(key);
      existing.pieces += 1; // Each item is one piece
      existing.quantitySqMeter += item.area || 0;
    } else {
      goodsMap.set(key, {
        quality: item.quality || '',
        design: item.design || '',
        pieces: 1,
        quantitySqMeter: item.area || 0,
        rateFOB: 0, // Default rate, user can modify
        amountFOB: 0 // Will be calculated
      });
    }
  });

  const noOfBales = uniqueBales.size;
  const packageDescription = `${noOfBales} BALES`;

  console.log('📦 Packing list data calculation for invoice:');
  console.log('📦 Completed bales (items):', completedBales.length);
  console.log('📦 Unique bale numbers:', Array.from(uniqueBales));
  console.log('📦 Total unique bales count:', noOfBales);
  console.log('📦 Package description for invoice:', packageDescription);
  console.log('📦 This will be stored in localStorage as packingListPackageInfo');

  return {
    goods: Array.from(goodsMap.values()),
    noOfBales: noOfBales,
    packageDescription: packageDescription
  };
}
  // Form group
  carpetEntryForm!: FormGroup;

  // Data arrays
  packingItems: PackingItem[] = [];
  savedPackingItems: PackingItem[] = [];
  availableCarpets: CarpetData[] = [];
  availableInvoices: any[] = [];

  // Auto-generated values
  currentBaleNo: string = '1';
  currentPcsNo: number = 1;

  // Edit mode tracking
  isEditMode: boolean = false;
  editDocumentId: string = '';

  // Track if current bale is being saved
  isCurrentBaleSaving: boolean = false;

  // Table configuration for current bale
  displayedColumns: string[] = [
    'pcsNo', 'carpetNo', 'quality',
    'design', 'colour', 'size', 'area', 'remarks',
  ];

  // Table configuration for completed bales
  completedBaleColumns: string[] = [
    'SrNo', 'baleNo', 'pcsNo', 'carpetNo', 'quality',
    'design', 'colour', 'size', 'area', 'tArea', 'remarks', 'actions'
  ];
  dataSource = new MatTableDataSource<PackingItem>();
  completedBalesDataSource = new MatTableDataSource<PackingItem>();

  // Loading state
  isLoading: boolean = false;

  // Navigation state
  isFromInvoice: boolean = false;
  pendingInvoiceId: string | null = null;

  @ViewChild('currentBalePaginator') paginator!: MatPaginator;
  @ViewChild('completedBalesPaginator') completedBalesPaginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild('invoiceNoSelect') invoiceNoSelect!: ElementRef;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private alertService: SweetalertService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    // Reset edit mode
    this.isEditMode = false;
    this.editDocumentId = '';

    // Check if coming from invoice
    if (history.state && history.state.fromInvoice) {
      this.isFromInvoice = true;
      console.log('📋 Coming from invoice, dropdown will be disabled');
      console.log('📋 isFromInvoice flag set to:', this.isFromInvoice);
      console.log('📋 Invoice ID from navigation:', history.state.invoiceId);

      // Store the invoice ID to set after invoices are loaded
      if (history.state.invoiceId) {
        this.pendingInvoiceId = history.state.invoiceId;
      }
    } else {
      this.isFromInvoice = false;
      console.log('📋 Not coming from invoice, dropdown will be enabled');
      console.log('📋 isFromInvoice flag set to:', this.isFromInvoice);
    }

    // Initialize form
    this.initForms();

    // Load available invoices from API
    this.loadAvailableInvoices();

    // Load available carpets
    this.loadAvailableCarpets();

    // Load saved packing lists
    this.loadSavedPackingLists();

    // Focus Invoice No. if navigated from Export Invoice page
    if (history.state && history.state.focusInvoiceNo) {
      setTimeout(() => {
        if (this.invoiceNoSelect && this.invoiceNoSelect.nativeElement) {
          this.invoiceNoSelect.nativeElement.focus();
          this.invoiceNoSelect.nativeElement.open && this.invoiceNoSelect.nativeElement.open();
        }
      }, 500);
    }
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.updateCompletedBalesDataSource();
  }

  // Initialize form
  initForms(): void {
    // Carpet entry form with invoiceNo and date
    this.carpetEntryForm = this.fb.group({
      invoiceNo: ['', Validators.required],
      date: [new Date(), Validators.required],
      AreaIn: [{value: 'Sq.Feet', disabled: false}, Validators.required], // Add AreaIn with default value
      baleNo: [this.currentBaleNo, Validators.required],
      pcsNo: [this.currentPcsNo, Validators.required],
      carpetNo: ['', Validators.required],
      quality: [''],
      design: [''],
      colour: [''],
      size: ['' , Validators.required],
      area: [''],
      tArea: [''],
      remarks: ['']
    });

    // Listen for carpet selection changes
    this.carpetEntryForm.get('carpetNo')?.valueChanges.subscribe(carpetNo => {
      // Disable InvoiceNo, AreaIn, date, and baleNo as soon as a carpet is selected OR if there are any carpets in the table
      if (this.isFormHeaderDisabled) {
        this.carpetEntryForm.get('AreaIn')?.disable();
        this.carpetEntryForm.get('invoiceNo')?.disable();
        this.carpetEntryForm.get('date')?.disable();
        this.carpetEntryForm.get('baleNo')?.disable();
      } else {
        this.carpetEntryForm.get('AreaIn')?.enable();
        this.carpetEntryForm.get('invoiceNo')?.enable();
        this.carpetEntryForm.get('date')?.enable();
        this.carpetEntryForm.get('baleNo')?.enable();
      }
      if (carpetNo) {
        this.onCarpetNoChange(carpetNo);
      }
    });

    // Listen for size changes
    this.carpetEntryForm.get('size')?.valueChanges.subscribe(size => {
      if (size) {
        this.updateAreaFromSize(size);
      }
    });

    // Listen for AreaIn changes
    this.carpetEntryForm.get('AreaIn')?.valueChanges.subscribe(areaIn => {
      console.log('AreaIn changed to:', areaIn);
      const carpetNo = this.carpetEntryForm.get('carpetNo')?.value;
      if (carpetNo) {
        // Re-trigger carpet selection to update size based on new unit
        this.onCarpetNoChange(carpetNo);
      }
    });
  }


  showValidationErrors(): boolean {
  const errorFields = Object.keys(this.carpetEntryForm.controls)
    .filter(key => this.carpetEntryForm.get(key)?.invalid)
    .map(key => key.replace(/([A-Z])/g, ' $1').trim());

  if (errorFields.length > 0) {
    Swal.fire({
      title: 'Validation Error',
      text: `Please fill the following fields: ${errorFields.join(', ')}`,
      icon: 'warning',
      confirmButtonText: 'OK'
    });
    return false;
  }
  return true;
}


  // Load available invoices from API
loadAvailableInvoices(): void {
  this.http.get('http://localhost:2000/api/phase-four/exportInvoice/getAll').subscribe({
    next: (response: any) => {
      console.log('Loaded invoices:', response);

      // Try different possible keys for the invoice array
      const invoiceArray = response.invoices || response.data || response.result || response || [];
      if (Array.isArray(invoiceArray)) {
        this.availableInvoices = invoiceArray.map((invoice: any) => ({
          invoiceNo: invoice.invoiceNo || invoice.number || '', // Fallback for different property names
          id: invoice._id || invoice.id || ''
        }));
        console.log('Available invoices:', this.availableInvoices);

        // Handle pending invoice ID from navigation
        if (this.pendingInvoiceId) {
          console.log('📋 Setting pending invoice ID:', this.pendingInvoiceId);
          console.log('📋 Available invoices for matching:', this.availableInvoices);

          // Find the invoice that matches the pending ID
          const matchingInvoice = this.availableInvoices.find(inv => inv.id === this.pendingInvoiceId);
          if (matchingInvoice) {
            console.log('✅ Found matching invoice:', matchingInvoice);
            this.carpetEntryForm.patchValue({ invoiceNo: this.pendingInvoiceId });
          } else {
            console.log('❌ No matching invoice found for ID:', this.pendingInvoiceId);
            console.log('📋 Available invoice IDs:', this.availableInvoices.map(inv => inv.id));
          }

          this.pendingInvoiceId = null; // Clear after setting

          // Focus on carpet number field after setting invoice
          setTimeout(() => {
            const carpetNoField = document.querySelector('input[formControlName="carpetNo"]') as HTMLInputElement;
            if (carpetNoField) {
              carpetNoField.focus();
              console.log('🎯 Focused on carpet number field');
            }
          }, 500);
        } else if (this.availableInvoices.length > 0 && !this.isFromInvoice) {
          // Set default invoice only if not coming from invoice
          this.carpetEntryForm.patchValue({ invoiceNo: this.availableInvoices[0].id });
        }
      } else {
        console.error('Invoice data is not an array:', invoiceArray);
        this.availableInvoices = [];
      }
    },
    error: (error) => {
      console.error('Error loading invoices:', error);
      this.alertService.error('Error', 'Failed to load invoices');
    }
  });
}

  filteredAvailableCarpets: any[] = [];

  // Load available carpets from API
// loadAvailableCarpets(): void {
//   this.isLoading = true;
//   this.http.get('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied').subscribe({
//     next: (response: any) => {
//       console.log('Received carpet data from carpetRecevied:', response);
//       if (response && Array.isArray(response) && response.length > 0) {
//         const allCarpets = response.map((carpet: any) => {
//           const issueNo = carpet.issueNo || {};

//           console.log('Processing carpet:', carpet);
//           console.log('IssueNo:', issueNo);
//           console.log('Size object:', issueNo.size);

//           return {
//             _id: carpet._id,
//             carpetNo:carpet.receiveNo || '',
//             quality: issueNo.quality?.quality || '',
//             design: issueNo.design?.design || '',
//             colour: issueNo.borderColour || '',
//             // Use size from carpet or issueNo.size
//             size: carpet.size || issueNo.size?.sizeInYard || '',
//             area: parseFloat(carpet.area) || 0,

//             sizeInYard: issueNo.size?.sizeInYard || '',
//             sizeInMeter: issueNo.size?.sizeinMeter || issueNo.size?.sizeInMeter || ''
//           };
//         });

//         console.log('Filtered available carpets from carpetRecevied: ', allCarpets);
//         this.filteredAvailableCarpets = allCarpets;
//         this.availableCarpets = allCarpets; // Update the main array as well
//       }
//       this.isLoading = false;
//     },
//     error: err => {
//       console.error('Error loading carpets from carpetRecevied:', err);
//       this.isLoading = false;
//     }
//   });
// }

loadAvailableCarpets(): void {
  this.isLoading = true;

  this.http.get('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied').subscribe({
    next: (response: any) => {
      if (response && Array.isArray(response)) {
        const allCarpets = response.map((carpet: any) => {
          const issueNo = carpet.issueNo || {};
          return {
            _id: carpet._id,
            carpetNo: carpet.receiveNo || '',
            quality: issueNo.quality?.quality || '',
            design: issueNo.design?.design || '',
            colour: issueNo.borderColour || '',
            size: carpet.size || issueNo.size?.sizeInYard || '',
            area: parseFloat(carpet.area) || 0,
            sizeInYard: issueNo.size?.sizeInYard || '',
            sizeInMeter: issueNo.size?.sizeinMeter || issueNo.size?.sizeInMeter || ''
          };
        });

        // Get the list of already used carpetNos from savedPackingItems
        const usedCarpetNos = new Set(this.savedPackingItems.map(item => item.carpetNo));

        // Filter carpets to exclude already used ones
        this.filteredAvailableCarpets = allCarpets.filter(c => !usedCarpetNos.has(c.carpetNo));
        this.availableCarpets = this.filteredAvailableCarpets;
      }

      this.isLoading = false;
    },
    error: err => {
      console.error('Error loading carpets from carpetRecevied:', err);
      this.isLoading = false;
    }
  });
}


  // Load saved packing lists from the database
  loadSavedPackingLists(): void {
    this.http.get('http://localhost:2000/api/phase-four/exportPackingList/exportPacking').subscribe({
      next: (response: any) => {
        console.log('Loaded saved packing lists:', response);
        this.savedPackingItems = [];

        const responseData = Array.isArray(response) ? response :
                            (response.data && Array.isArray(response.data)) ? response.data : [];

        if (responseData.length > 0) {
          this.savedPackingItems = responseData.flatMap((list: any) => {
            if (list.items && Array.isArray(list.items)) {
              const itemsByBale = new Map<string, any[]>();

              list.items.forEach((item: any) => {
                const baleNo = item.baleNo || '';
                if (!itemsByBale.has(baleNo)) {
                  itemsByBale.set(baleNo, []);
                }
                itemsByBale.get(baleNo)?.push(item);
              });

              return Array.from(itemsByBale.entries()).flatMap(([_, baleItems]) => {
                baleItems.sort((a, b) => parseInt(a.pcsNo) - parseInt(b.pcsNo));
                const baleArea = baleItems.reduce((sum, item) => sum + (parseFloat(item.area) || 0), 0);

                return baleItems.map((item: any, index: number) => {
                  const isLastInBale = index === baleItems.length - 1;
                  
                  // Convert old Sq.Yard values to Sq.Feet
                  let displayedArea = item.area;
                  let areaIn = item.areaIn || 'Sq.Feet';
                  
                  // If it's Sq.Yard, convert to Sq.Feet
                  if (item.areaIn === 'Sq.Yard') {
                    areaIn = 'Sq.Feet';
                  }

                  return {
                    SrNo: index === 0 ? 1 : 0,
                    baleNo: item.baleNo || '',
                    pcsNo: item.pcsNo ? item.pcsNo.toString() : '0',
                    carpetNo: item.carpetNo,
                    quality: item.quality || '',
                    design: item.design || '',
                    colour: item.colour || '',
                    size: item.size || '',
                    area: parseFloat(item.area) || 0,
                    tArea: isLastInBale ? parseFloat(baleArea.toFixed(2)) : 0,
                    remarks: item.remarks || '',
                    areaDisplay: `${displayedArea} ${areaIn === 'Sq.Feet' ? 'Ft' : 'M'}`,
                    areaIn: areaIn,
                    addedAt: item.addedAt || list.updatedAt || list.createdAt,
                    invoiceId: list.invoiceNo,
                    isLastInBale: isLastInBale,
                    documentId: list._id
                  };
                });
              });
            }
            return [];
          });

          console.log('Saved packing items:', this.savedPackingItems);

          if (this.savedPackingItems.length > 0) {
            // Find the highest bale number to set the next bale number
            const baleNumbers = this.savedPackingItems.map(item => parseInt(item.baleNo) || 0);
            const maxBaleNo = Math.max(...baleNumbers);
            this.currentBaleNo = (maxBaleNo + 1).toString();

            // IMPORTANT: Find the highest piece number to ensure continuous incrementing
            const pcsNumbers = this.savedPackingItems.map(item => parseInt(item.pcsNo) || 0);
            const maxPcsNo = Math.max(...pcsNumbers);

            // Set the next piece number to be one more than the highest
            this.currentPcsNo = maxPcsNo + 1;
            console.log('Setting piece number to continue from:', this.currentPcsNo);

            // Update the form with the new values
            this.carpetEntryForm.patchValue({
              baleNo: this.currentBaleNo,
              pcsNo: this.currentPcsNo
            });

            // Disable the piece number field
            // this.carpetEntryForm.get('pcsNo')?.disable();
          }

          // Update available carpets and the completed bales table
          this.loadAvailableCarpets();
          this.updateCompletedBalesDataSource();
        }
      },
      error: (error) => {
        console.error('Error loading saved packing lists:', error);
        this.alertService.error('Error', 'Failed to load saved packing lists');
      }
    });
  }

  // Get the highest piece number across all bales
  getHighestPieceNumber(): number {
    // Get all piece numbers from saved items
    const savedPcsNumbers = this.savedPackingItems.map(item => parseInt(item.pcsNo) || 0);

    // Get all piece numbers from current items
    const currentPcsNumbers = this.packingItems.map(item => parseInt(item.pcsNo) || 0);

    // Combine all piece numbers
    const allPcsNumbers = [...savedPcsNumbers, ...currentPcsNumbers];

    // Find the highest piece number
    const maxPcsNo = allPcsNumbers.length > 0 ? Math.max(...allPcsNumbers) : 0;

    // Return the next piece number
    return maxPcsNo + 1;
  }

  // Update the completed bales data source
  updateCompletedBalesDataSource(): void {
    const completedItems = this.getCompletedBaleItems();
    const groupedItems = this.assignSrNoByBaleGroup(completedItems);
    this.completedBalesDataSource.data = groupedItems;

    if (this.completedBalesPaginator) {
      this.completedBalesDataSource.paginator = this.completedBalesPaginator;
    }
    if (this.sort) {
      this.completedBalesDataSource.sort = this.sort;
    }
  }

  // Assign SrNo based on bale groups
  assignSrNoByBaleGroup(items: PackingItem[]): PackingItem[] {
    const itemsByBale = new Map<string, PackingItem[]>();
    items.forEach(item => {
      const baleNo = item.baleNo;
      if (!itemsByBale.has(baleNo)) {
        itemsByBale.set(baleNo, []);
      }
      itemsByBale.get(baleNo)?.push({ ...item });
    });

    let srNoCounter = 1;
    const processedItems: PackingItem[] = [];
    const sortedBales = Array.from(itemsByBale.entries()).sort((a, b) => {
      const numA = parseInt(a[0]) || 0;
      const numB = parseInt(b[0]) || 0;
      return numA - numB;
    });

    sortedBales.forEach(([_, baleItems]) => {
      baleItems.sort((a, b) => parseInt(a.pcsNo) - parseInt(b.pcsNo));
      baleItems.forEach((item, index) => {
        item.SrNo = index === 0 ? srNoCounter : 0;
        item.isLastInBale = index === baleItems.length - 1;
        processedItems.push(item);
      });
      srNoCounter++;
    });

    return processedItems;
  }

  // Handle carpet selection change
  onCarpetNoChange(carpetNo: string): void {
    console.log('Carpet selection changed to:', carpetNo);
    const selectedCarpet = this.filteredAvailableCarpets.find(c => c.carpetNo === carpetNo);
    console.log('Selected carpet data:', selectedCarpet);

    if (selectedCarpet) {
      // Get the selected area unit
      const areaIn = this.carpetEntryForm.get('AreaIn')?.value || 'Sq.Feet';
      console.log('Selected area unit:', areaIn);

      // Choose the appropriate size based on area unit
      let sizeToUse = '';
      if (areaIn === 'Sq.Feet') {
        sizeToUse = selectedCarpet.sizeInYard || selectedCarpet.size;
        console.log('Using sizeInYard for Sq.Feet:', sizeToUse);
      } else {
        sizeToUse = selectedCarpet.sizeInMeter || selectedCarpet.size;
        console.log('Using sizeInMeter for Sq.Meter:', sizeToUse);
      }

      console.log('Final size to use:', sizeToUse);

      this.carpetEntryForm.patchValue({
        quality: selectedCarpet.quality,
        design: selectedCarpet.design,
        colour: selectedCarpet.colour,
        size: sizeToUse
      });

      // Only calculate area if we have a valid size
      if (sizeToUse) {
        this.updateAreaFromSize(sizeToUse);
      }
    }
  }

  // Calculate area from size
  updateAreaFromSize(size: string): void {
    if (!size) return;

    try {
      // Get the selected area unit
      const areaIn = this.carpetEntryForm.get('AreaIn')?.value || 'Sq.Feet';

      let areaValue: number;
      let formattedArea: string;

      if (areaIn === 'Sq.Feet') {
        // Calculate area in Sq.Feet (existing logic)
        const calcData = size.split(/[Xx]/);
        if (calcData.length !== 2) {
          console.error('Invalid size format. Expected format: width x length');
          return;
        }

        const width1 = calcData[0].split('.')[0] || '0';
        const width2 = calcData[0].split('.')[1] || '0';
        const length1 = calcData[1].split('.')[0] || '0';
        const length2 = calcData[1].split('.')[1] || '0';

        const widthInInches = parseInt(width1) * 12 + parseInt(width2);
        const lengthInInches = parseInt(length1) * 12 + parseInt(length2);

        const areaInFeet = (widthInInches * lengthInInches) / 144;
        areaValue = parseFloat(areaInFeet.toFixed(2));
        formattedArea = `${areaValue} Ft`;
      } else {
        // Calculate area in Sq.Meter (new logic)
        const parts = size.split(/[Xx]/);
        if (parts.length !== 2) {
          console.error('Invalid size format. Expected format: width x length');
          return;
        }

        const sizeMeterWidth = parseFloat(parts[0]) || 0;
        const sizeMeterLength = parseFloat(parts[1]) || 0;

        const totalSizeInMeter = (sizeMeterWidth * sizeMeterLength) / 10000;
        areaValue = parseFloat(totalSizeInMeter.toFixed(2));
        formattedArea = `${areaValue} M`;
      }

      this.carpetEntryForm.patchValue({
        area: areaValue,
        tArea: areaValue,
        areaDisplay: formattedArea
      });

      console.log('Calculated area:', formattedArea, 'for unit:', areaIn);
    } catch (error) {
      console.error('Error calculating area:', error);
      this.alertService.error('Error', 'Failed to calculate area from size');
    }
  }

  // Add carpet entry to the list
  addCarpetEntry(): void {
    if (!this.showValidationErrors()) {
      return;
    }

    const formValue = this.carpetEntryForm.getRawValue();
    this.currentBaleNo = formValue.baleNo;

    // Use the selected AreaIn value from the form (only Sq.Feet or Sq.Meter)
    const areaIn = formValue.AreaIn || 'Sq.Feet';

    // Format area display based on the area unit
    let areaDisplay = formValue.area?.toString() || '0';
    if (areaIn === 'Sq.Meter') {
      areaDisplay += ' M';
    } else {
      areaDisplay += ' Ft';
    }

    // Create the packing item with the current piece number
    const packingItem: PackingItem = {
      SrNo: 0, // Will be assigned later
      baleNo: formValue.baleNo,
      pcsNo: formValue.pcsNo,
      carpetNo: formValue.carpetNo,
      quality: formValue.quality,
      design: formValue.design,
      colour: formValue.colour,
      size: formValue.size,
      area: formValue.area || 0,
      areaDisplay: areaDisplay,
      areaIn: areaIn, // Use the validated area unit
      tArea: 0, // Will be updated when saving or starting new bale
      remarks: formValue.remarks || '',
      isLastInBale: true,
      invoiceId: formValue.invoiceNo,
      documentId: this.isEditMode ? this.editDocumentId : undefined
    };

    // Add the item to the list
    this.packingItems.push(packingItem);

    // Update the data source
    this.dataSource.data = this.packingItems;

    // Remove used carpet from available list
    this.availableCarpets = this.availableCarpets.filter(c => c.carpetNo !== formValue.carpetNo);

    // IMPORTANT: Get the highest piece number across ALL bales for the next carpet
    // This ensures piece numbers continue incrementing across bales
    this.currentPcsNo = this.getHighestPieceNumber();
    console.log('Next piece number (across all bales):', this.currentPcsNo);

    // Reset the form but keep the current bale number and the incremented piece number
    this.carpetEntryForm.reset({
      invoiceNo: formValue.invoiceNo,
      date: formValue.date,
      AreaIn: formValue.AreaIn, // Preserve the selected area unit
      baleNo: this.currentBaleNo,
      pcsNo: this.currentPcsNo
    });

    // If in edit mode, keep the form fields disabled
    if (this.isEditMode) {
      this.carpetEntryForm.get('baleNo')?.disable();
      this.carpetEntryForm.get('invoiceNo')?.disable();
    }

    // Disable the piece number field to prevent manual editing
    this.carpetEntryForm.get('pcsNo')?.disable();

    // Set flag to keep invoice/area disabled until save
    this.isCurrentBaleSaving = false;
  }

  // Start a new bale without saving to database
  startNewBale(): void {
    if (this.getCurrentBaleItems().length === 0) {
      this.alertService.error('Error', 'Current bale has no items. Add at least one item before starting a new bale.');
      return;
    }

    // Calculate total area for current bale
    const totalAreaForCurrentBale = this.getTotalAreaForCurrentBale();

    // Set tArea for all items in the current bale
    this.packingItems.forEach(item => {
      if (item.baleNo === this.currentBaleNo) {
        item.tArea = totalAreaForCurrentBale;
      }
    });

    // Save current bale to completed bales
    this.alertService.success('Success', `Bale ${this.currentBaleNo} completed with ${this.getCurrentBaleItems().length} items.`);

    // Reset edit mode
    this.isEditMode = false;
    this.editDocumentId = '';

    // Increment bale number
    this.currentBaleNo = (parseInt(this.currentBaleNo) + 1).toString();

    // IMPORTANT: Do NOT reset piece number - let it continue incrementing across bales
    console.log('Continuing with piece number:', this.currentPcsNo);

    // Update form with new bale number but keep the same piece number
    const currentInvoiceNo = this.carpetEntryForm.get('invoiceNo')?.value;
    const currentDate = this.carpetEntryForm.get('date')?.value;
    const currentAreaIn = this.carpetEntryForm.get('AreaIn')?.value;

    this.carpetEntryForm.reset({
      invoiceNo: currentInvoiceNo,
      date: currentDate,
      AreaIn: currentAreaIn, // Preserve the selected area unit
      baleNo: this.currentBaleNo,
      pcsNo: this.currentPcsNo
    });

    // Re-enable form fields
    this.carpetEntryForm.get('baleNo')?.enable();
    this.carpetEntryForm.get('invoiceNo')?.enable();

    // Disable piece number field
    this.carpetEntryForm.get('pcsNo')?.disable();

    // Update the data source
    this.updateCompletedBalesDataSource();
  }

  // Save packing list to database
  savePackingList(): void {
    if (this.getCurrentBaleItems().length === 0) {
      this.alertService.error('Error', 'Please add at least one carpet to the current bale');
      return;
    }

    if (this.carpetEntryForm.get('invoiceNo')?.invalid || this.carpetEntryForm.get('date')?.invalid) {
      this.alertService.error('Error', 'Please select an invoice and date');
      return;
    }

    const totalAreaForCurrentBale = this.getTotalAreaForCurrentBale();
    this.packingItems.forEach(item => {
      if (item.baleNo === this.currentBaleNo) {
        item.tArea = totalAreaForCurrentBale;
      }
    });

    // Get the invoice ID from the form
    const invoiceId = this.carpetEntryForm.get('invoiceNo')?.value;

    // Prepare the data for the current bale
    const currentBaleItems = this.packingItems
      .filter(item => item.baleNo === this.currentBaleNo)
      .map((item, index) => ({
        pcsNo: item.pcsNo,
        carpetNo: item.carpetNo,
        baleNo: this.currentBaleNo, // Use the current bale number
        quality: item.quality,
        design: item.design,
        colour: item.colour,
        size: item.size,
        area: item.area,
        tArea: item.tArea || 0,
        remarks: item.remarks || '',
        areaDisplay: item.areaDisplay,
        areaIn: item.areaIn ,
        serialNo: index + 1,
        addedAt: new Date()
      }));

    // If we're in edit mode, we need to get the existing items from the database
    // and merge them with the new items
    if (this.isEditMode && this.editDocumentId) {
      console.log('Edit mode active, updating existing document:', this.editDocumentId);

      // Instead of trying to filter from savedPackingItems, let's fetch the document directly
      // to get the most up-to-date data
      this.http.get(`http://localhost:2000/api/phase-four/exportPackingList/exportPacking/${this.editDocumentId}`).subscribe({
        next: (response: any) => {
          console.log('Fetched existing document:', response);

          // Extract the existing items from the response
          const existingDocument = response.data || response;
          const existingItems = existingDocument.items || [];

          // We don't need to check for duplicate piece numbers anymore
          // Each new item already has a unique piece number assigned from getHighestPieceNumber()
          // We just need to make sure we're not overwriting existing items

          console.log('Existing items:', existingItems);
          console.log('Current bale items to add:', currentBaleItems);

          // Combine existing items with new items
          const allItems = [...existingItems, ...currentBaleItems];

          // Calculate total area for all items
          const totalArea = allItems.reduce((sum, item) => sum + (parseFloat(String(item.area)) || 0), 0);
          const totalAreaFixed = parseFloat(totalArea.toFixed(2));

          // Create the update data
          const updateData = {
            invoiceNo: invoiceId,
            baleNo: this.currentBaleNo,
            date: this.carpetEntryForm.get('date')?.value,
            items: allItems,
            totalArea: totalAreaFixed,
            totalTArea: totalAreaFixed,
            totalPcses: allItems.length
          };

          console.log('Updating packing list with data:', updateData);

          // Update the existing document
          this.http.put(`http://localhost:2000/api/phase-four/exportPackingList/exportPacking/${this.editDocumentId}`, updateData).subscribe({
            next: (updateResponse: any) => {
              console.log('Packing list updated successfully:', updateResponse);
              this.alertService.success('Success', 'Packing list updated successfully');

              // Reset edit mode
              this.isEditMode = false;
              this.editDocumentId = '';

              // Clear current bale items
              this.packingItems = this.packingItems.filter(item => item.baleNo !== this.currentBaleNo);
              this.dataSource.data = this.packingItems;

              // Re-enable form fields
              this.carpetEntryForm.get('baleNo')?.enable();
              this.carpetEntryForm.get('invoiceNo')?.enable();

              // Increment bale number for next entry
              this.currentBaleNo = (parseInt(this.currentBaleNo) + 1).toString();

              // Reset form
              const currentInvoiceNo = this.carpetEntryForm.get('invoiceNo')?.value;
              const currentDate = this.carpetEntryForm.get('date')?.value;
              const currentAreaIn = this.carpetEntryForm.get('AreaIn')?.value;
              this.carpetEntryForm.reset({
                invoiceNo: currentInvoiceNo,
                date: currentDate,
                AreaIn: currentAreaIn, // Preserve the selected area unit
                baleNo: this.currentBaleNo,
                pcsNo: this.currentPcsNo
              });
              this.carpetEntryForm.get('pcsNo')?.disable();

              // Reload data
              this.loadAvailableCarpets();
              this.loadSavedPackingLists();
            },
            error: (error: any) => {
              console.error('Error updating packing list:', error);
              this.alertService.error('Error', 'Failed to update packing list: ' + (error.error?.error || error.message));
            }
          });
        },
        error: (error: any) => {
          console.error('Error fetching existing document:', error);
          this.alertService.error('Error', 'Failed to fetch existing document: ' + (error.error?.error || error.message));
        }
      });

      return;
    }

    // If not in edit mode, create a new document
    // Create the packing list data
    const packingListData = {
      invoiceNo: invoiceId,
      baleNo: this.currentBaleNo, // Use the actual bale number now that we've removed the unique constraint
      date: this.carpetEntryForm.get('date')?.value,
      items: currentBaleItems,
      totalArea: totalAreaForCurrentBale,
      totalTArea: totalAreaForCurrentBale,
      totalPcses: currentBaleItems.length,
      createdAt: new Date()
    };

    console.log('Creating new packing list with data:', packingListData);

    // Save the packing list
    this.http.post('http://localhost:2000/api/phase-four/exportPackingList/exportPacking', packingListData).subscribe({
      next: (response: any) => {
        this.alertService.success('Success', 'Packing list saved successfully');

        // Clear current bale items
        this.packingItems = this.packingItems.filter(item => item.baleNo !== this.currentBaleNo);
        this.dataSource.data = this.packingItems;

        // Increment bale number
        this.currentBaleNo = (parseInt(this.currentBaleNo) + 1).toString();

        // IMPORTANT: Do NOT reset piece number - let it continue incrementing
        console.log('Continuing with piece number after save:', this.currentPcsNo);

        // Reset form
        const currentInvoiceNo = this.carpetEntryForm.get('invoiceNo')?.value;
        const currentDate = this.carpetEntryForm.get('date')?.value;
        const currentAreaIn = this.carpetEntryForm.get('AreaIn')?.value;
        this.carpetEntryForm.reset({
          invoiceNo: currentInvoiceNo,
          date: currentDate,
          AreaIn: currentAreaIn, // Preserve the selected area unit
          baleNo: this.currentBaleNo,
          pcsNo: this.currentPcsNo
        });
        this.carpetEntryForm.get('pcsNo')?.disable();

        // Reload data
        this.loadAvailableCarpets();
        this.loadSavedPackingLists();
      },
      error: (error: any) => {
        console.error('Error saving packing list:', error);
        this.alertService.error('Error', 'Failed to save packing list: ' + (error.error?.error || error.message));
      }
    });
  }

  // Edit a bale from the completed bale list
  editBale(item: PackingItem): void {
    this.currentBaleNo = item.baleNo;

    // Determine the area unit based on the area display - only allow Sq.Feet and Sq.Meter
    let areaIn = 'Sq.Feet'; // default value
    if (item.areaDisplay) {
      if (item.areaDisplay.endsWith('M')) {
        areaIn = 'Sq.Meter';
      }
    }

    // Use the invoice ID from the item if available
    const invoiceNo = item.invoiceId || this.carpetEntryForm.get('invoiceNo')?.value;
    const currentDate = this.carpetEntryForm.get('date')?.value;

    // Set edit mode and store the document ID if available for editing
    this.isEditMode = true;
    if (item.documentId) {
      console.log('Editing document with ID:', item.documentId);
      this.editDocumentId = item.documentId;

      // Fetch the document to get the latest data
      this.http.get(`http://localhost:2000/api/phase-four/exportPackingList/exportPacking/${this.editDocumentId}`).subscribe({
        next: (response: any) => {
          console.log('Fetched document for editing:', response);

          // Extract the existing document data
          const existingDocument = response.data || response;

          // IMPORTANT: Get the highest piece number across ALL bales, not just this one
          // This ensures we continue the sequence properly
          this.currentPcsNo = this.getHighestPieceNumber();
          console.log('Next piece number for editing (across all bales):', this.currentPcsNo);

          // Update the form with the determined area unit
          this.carpetEntryForm.reset({
            invoiceNo: invoiceNo,
            date: currentDate || existingDocument.date,
            AreaIn: areaIn,
            baleNo: this.currentBaleNo,
            pcsNo: this.currentPcsNo
          });

          // Disable fields
          this.carpetEntryForm.get('baleNo')?.disable();
          this.carpetEntryForm.get('invoiceNo')?.disable();
          this.carpetEntryForm.get('pcsNo')?.disable();
          this.carpetEntryForm.get('AreaIn')?.disable();

          this.alertService.success('Info', `Editing Bale ${item.baleNo}. Select a carpet to add to this bale.`);
        },
        error: (error) => {
          console.error('Error fetching document for editing:', error);
          this.alertService.error('Error', 'Failed to fetch document for editing');
          this.currentPcsNo = this.getHighestPieceNumber();

          this.carpetEntryForm.reset({
            invoiceNo: invoiceNo,
            date: currentDate,
            AreaIn: areaIn,
            baleNo: this.currentBaleNo,
            pcsNo: this.currentPcsNo
          });

          // Disable fields
          this.carpetEntryForm.get('baleNo')?.disable();
          this.carpetEntryForm.get('invoiceNo')?.disable();
          this.carpetEntryForm.get('pcsNo')?.disable();
          this.carpetEntryForm.get('AreaIn')?.disable();

          this.alertService.success('Info', `Editing Bale ${item.baleNo}. Select a carpet to add to this bale.`);
        }
      });
    } else {
      console.warn('Document ID not available for editing');
      this.editDocumentId = '';
      this.currentPcsNo = this.getHighestPieceNumber();

      this.carpetEntryForm.reset({
        invoiceNo: invoiceNo,
        date: currentDate,
        AreaIn: areaIn,
        baleNo: this.currentBaleNo,
        pcsNo: this.currentPcsNo
      });

      // Disable fields
      this.carpetEntryForm.get('baleNo')?.disable();
      this.carpetEntryForm.get('invoiceNo')?.disable();
      this.carpetEntryForm.get('pcsNo')?.disable();
      this.carpetEntryForm.get('AreaIn')?.disable();

      this.alertService.success('Info', `Editing Bale ${item.baleNo}. Select a carpet to add to this bale.`);
    }
  }

  // Calculate total area for current bale
  getTotalAreaForCurrentBale(): number {
    const total = this.packingItems
      .filter(item => item.baleNo === this.currentBaleNo)
      .reduce((sum, item) => sum + (parseFloat(String(item.area)) || 0), 0);
    return parseFloat(total.toFixed(2));
  }

  // Get items for current bale
  getCurrentBaleItems(): PackingItem[] {
    return this.packingItems.filter(item => item.baleNo === this.currentBaleNo);
  }

  // Get items for completed bales
  getCompletedBaleItems(): PackingItem[] {
    const currentSessionCompletedItems = this.packingItems.filter(item => item.baleNo !== this.currentBaleNo);
    return [...currentSessionCompletedItems, ...this.savedPackingItems];
  }

  // Returns only completed bales for the selected invoice
  getCompletedBaleItemsForSelectedInvoice(): PackingItem[] {
    const selectedInvoiceId = this.carpetEntryForm.get('invoiceNo')?.value;
    return this.getCompletedBaleItems().filter(item => item.invoiceId === selectedInvoiceId);
  }

  // Get total pieces for selected invoice's completed bales
  getTotalPiecesForSelectedInvoice(): number {
    return this.getCompletedBaleItemsForSelectedInvoice().length;
  }

  // Get total area for selected invoice's completed bales
  getTotalAreaForSelectedInvoice(): number {
    return this.getCompletedBaleItemsForSelectedInvoice().reduce((sum, item) => sum + (parseFloat(String(item.area)) || 0), 0);
  }

  // Get total area for a specific bale
  getTotalAreaForBale(baleNo: string): number {
    // Filter for the selected invoice only
    const selectedInvoiceId = this.carpetEntryForm.get('invoiceNo')?.value;
    const totalFromCurrentSession = this.packingItems
      .filter(item => item.baleNo === baleNo && item.invoiceId === selectedInvoiceId)
      .reduce((sum, item) => sum + (parseFloat(String(item.area)) || 0), 0);

    const totalFromSavedItems = this.savedPackingItems
      .filter(item => item.baleNo === baleNo && item.invoiceId === selectedInvoiceId)
      .reduce((sum, item) => sum + (parseFloat(String(item.area)) || 0), 0);

    return parseFloat((totalFromCurrentSession + totalFromSavedItems).toFixed(2));
  }

  // Get unique bale count for selected invoice
  getUniqueBaleCountForSelectedInvoice(): number {
    const items = this.getCompletedBaleItemsForSelectedInvoice();
    const uniqueBales = new Set(items.map(item => item.baleNo));
    return uniqueBales.size;
  }

  // Helper: should invoice/area fields be disabled?
  get isInvoiceAndAreaDisabled(): boolean {
    // Disable if at least one carpet is added and not yet saved
    return this.getCurrentBaleItems().length > 0;
  }

  // Helper: should invoice/area/date/bale fields be disabled?
  get isFormHeaderDisabled(): boolean {
    // Disable if at least one carpet is added and not yet saved, or a carpet is selected
    return this.getCurrentBaleItems().length > 0 || !!this.carpetEntryForm?.get('carpetNo')?.value;
  }

  // Remove a carpet from the current bale and return it to the dropdown
  // removeCarpetFromBale(item: PackingItem): void {
  //   // Remove from packingItems
  //   this.packingItems = this.packingItems.filter(i => i !== item);
  //   this.dataSource.data = this.packingItems;
  //   // Add back to availableCarpets if not already present
  //   if (!this.availableCarpets.find(c => c.carpetNo === item.carpetNo)) {
  //     this.availableCarpets.push({
  //       _id: '', // You can set correct _id if needed
  //       carpetNo: item.carpetNo,
  //       quality: item.quality,
  //       design: item.design,
  //       colour: item.colour,
  //       size: item.size,
  //       area: item.area,
  //       areaIn: item.areaIn
  //     });
  //   }
    // Optionally, sort availableCarpets
 //   this.availableCarpets = [...this.availableCarpets].sort((a, b) => a.carpetNo.localeCompare(b.carpetNo));
  //}

  onSaveShippingDetails() {
    Swal.fire({
      title: 'Save Shipping Details?',
      text: 'Are you sure you want to save and go to Goods Section?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes, proceed',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'mat-raised-button mat-primary',
        cancelButton: 'mat-raised-button mat-warn'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        // this.router.navigate(['/admin/export-packing-list'], { state: { focusInvoiceNo: true } });
        this.scrollToGoodsTable();
      }
    });
  }

  // Scrolls to the Goods Section table in the template
  scrollToGoodsTable(): void {
    const element = document.getElementById('goods-section-table');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  // Get the selected invoice number for display when coming from invoice
  getSelectedInvoiceNumber(): string {
    const selectedInvoiceId = this.carpetEntryForm.get('invoiceNo')?.value;
    if (selectedInvoiceId && this.availableInvoices.length > 0) {
      const selectedInvoice = this.availableInvoices.find(inv => inv.id === selectedInvoiceId);
      return selectedInvoice ? selectedInvoice.invoiceNo : 'Loading...';
    }
    return 'Loading...';
  }

}