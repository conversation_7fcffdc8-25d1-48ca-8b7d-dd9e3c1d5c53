const CarpetOrderissue = require('../../model/phase-4/carpetOrderIssue');
const BuyerOrder= require('../../model/phase-4/buyerOrder');
 require('../../model/phase-2/quality');
require('../../model/phase-2/design');
require('../../model/phase-3/masterSize');
const createCarpetOrderissue = async (data) => {
  const newOrder = new CarpetOrderissue(data);
  let _data = await newOrder.save();
  if(_data){
    let updateBuyerOrder = await BuyerOrder.findOneAndUpdate(
      { "items._id": data.itemId},
      {
        $inc: {
          "items.$.PcsesAssigned": data.pcs,
          "items.$.PcsesPending": -data.pcs
        }
      },
      { returnNewDocument: true }
    )

    if(updateBuyerOrder){
      return _data;
    }
  }

};

const getAllCarpetOrderissues = async () => {
  return await CarpetOrderissue.find()
    .populate('buyerOrder')
    .populate('weaver')
    .populate('quality')
    .populate('design')
    .populate('size')
    .populate('branch');

};








const getCarpetOrderissueById = async (id) => {
  return await CarpetOrderissue.findById(id)
    .populate('buyerOrder')
    .populate('weaver')
    .populate('quality')
    .populate('design')
    .populate('size')
    .populate('branch');
};

const updateCarpetOrderissue = async (id, data) => {
  console.log('🔍 Update data received:', data);

  // Fetch current data from database instead of relying on frontend data
  const currentIssue = await CarpetOrderissue.findById(id);
  if (!currentIssue) {
    throw new Error('Carpet order issue not found');
  }

  console.log('🔍 Current issue from database:', currentIssue);

  // Get current values from database
  let existId = currentIssue.itemId;
  let existPcs = parseInt(currentIssue.pcs) || 0;

  console.log('🔍 Current item ID from DB:', existId, 'Type:', typeof existId);
  console.log('🔍 Current PCS from DB:', existPcs, 'Type:', typeof existPcs);
  console.log('🔍 New item ID:', data.itemId, 'Type:', typeof data.itemId);
  console.log('🔍 New PCS:', data.pcs, 'Type:', typeof data.pcs);

  // Ensure data.pcs is a valid number
  const newPcs = parseInt(data.pcs) || 0;
  console.log('🔍 Parsed new PCS:', newPcs, 'Type:', typeof newPcs);

  // Convert itemId to string for comparison (in case one is ObjectId and other is string)
  const existIdStr = String(existId);
  const newItemIdStr = String(data.itemId);

  console.log('🔍 Comparing itemIds:', existIdStr, '===', newItemIdStr, '?', existIdStr === newItemIdStr);
  console.log('🔍 Comparing PCS:', existPcs, '===', newPcs, '?', existPcs === newPcs);

  // Additional debugging to check for null/undefined values
  console.log('🔍 existId is null/undefined?', existId == null);
  console.log('🔍 data.itemId is null/undefined?', data.itemId == null);
  console.log('🔍 existIdStr length:', existIdStr.length);
  console.log('🔍 newItemIdStr length:', newItemIdStr.length);

  // Safety check: if data.itemId is null/undefined, use existId to maintain consistency
  if (!data.itemId) {
    console.log('⚠️ data.itemId is null/undefined, using existId to maintain consistency');
    data.itemId = existId;
  }

  // Re-calculate newItemIdStr after potential update
  const finalNewItemIdStr = String(data.itemId);

  console.log('🔍 Final comparison - existIdStr:', existIdStr, 'vs finalNewItemIdStr:', finalNewItemIdStr);
  console.log('🔍 Are they equal?', existIdStr === finalNewItemIdStr);

  // Case 1: Same item, different pcs
  if (existIdStr === finalNewItemIdStr) {
    console.log('✅ Same item, updating PCS');

    if(existPcs === newPcs){
      // Same item, same PCS - just update the carpet order issue without touching buyer order
      console.log('Same item, same PCS - updating only carpet order issue fields');

      // Still need to ensure PCSWaitingToBeReceived is correct
      const currentPCSReceived = currentIssue.PCSReceived || 0;
      const newPCSWaitingToBeReceived = newPcs - currentPCSReceived;
      data.PCSWaitingToBeReceived = Math.max(0, newPCSWaitingToBeReceived);

      return await CarpetOrderissue.findByIdAndUpdate(id, data, { new: true });
    }
    else if(existPcs < newPcs){
      let x = newPcs - existPcs;

      // First check if the buyer order item exists and has valid PcsesPending
      const buyerOrderItem = await BuyerOrder.findOne(
        { "items._id": data.itemId },
        { "items.$": 1 }
      );

      if (buyerOrderItem && buyerOrderItem.items && buyerOrderItem.items.length > 0) {
        const currentItem = buyerOrderItem.items[0];
        const currentPcsesPending = parseInt(currentItem.PcsesPending) || 0;
        const currentPcsesAssigned = parseInt(currentItem.PcsesAssigned) || 0;

        // Ensure we don't go below 0 for PcsesPending
        const newPcsesPending = Math.max(0, currentPcsesPending - x);
        const newPcsesAssigned = currentPcsesAssigned + x;

        await BuyerOrder.findOneAndUpdate(
          { "items._id": data.itemId},
          {
            $set: {
              "items.$.PcsesAssigned": newPcsesAssigned,
              "items.$.PcsesPending": newPcsesPending
            }
          },
          { returnNewDocument: true }
        );
      }

      // Update PCSWaitingToBeReceived when PCS increases
      const currentPCSReceived = currentIssue.PCSReceived || 0;
      const newPCSWaitingToBeReceived = newPcs - currentPCSReceived;
      data.PCSWaitingToBeReceived = Math.max(0, newPCSWaitingToBeReceived);

      return await CarpetOrderissue.findByIdAndUpdate(id, data, { new: true });
    }else{
      let x = existPcs - newPcs;

      // First check if the buyer order item exists and has valid fields
      const buyerOrderItem = await BuyerOrder.findOne(
        { "items._id": data.itemId },
        { "items.$": 1 }
      );

      if (buyerOrderItem && buyerOrderItem.items && buyerOrderItem.items.length > 0) {
        const currentItem = buyerOrderItem.items[0];
        const currentPcsesPending = parseInt(currentItem.PcsesPending) || 0;
        const currentPcsesAssigned = parseInt(currentItem.PcsesAssigned) || 0;

        // Ensure we don't go below 0 for PcsesAssigned
        const newPcsesAssigned = Math.max(0, currentPcsesAssigned - x);
        const newPcsesPending = currentPcsesPending + x;

        await BuyerOrder.findOneAndUpdate(
          { "items._id": data.itemId},
          {
            $set: {
              "items.$.PcsesAssigned": newPcsesAssigned,
              "items.$.PcsesPending": newPcsesPending
            }
          },
          { returnNewDocument: true }
        );
      }

      // Update PCSWaitingToBeReceived when PCS decreases
      const currentPCSReceived = currentIssue.PCSReceived || 0;
      const newPCSWaitingToBeReceived = newPcs - currentPCSReceived;
      data.PCSWaitingToBeReceived = Math.max(0, newPCSWaitingToBeReceived);

      return await CarpetOrderissue.findByIdAndUpdate(id, data, { new: true });
    }
  }else{
    // Case 2: Different item - revert old item and assign to new item
    console.log('❌ Different item, updating both buyer orders');
    console.log('Old item ID (string):', existIdStr);
    console.log('New item ID (string):', finalNewItemIdStr);

    // Revert the old item
    const oldBuyerOrderItem = await BuyerOrder.findOne(
      { "items._id": existId },
      { "items.$": 1 }
    );

    if (oldBuyerOrderItem && oldBuyerOrderItem.items && oldBuyerOrderItem.items.length > 0) {
      const oldCurrentItem = oldBuyerOrderItem.items[0];
      const oldCurrentPcsesPending = parseInt(oldCurrentItem.PcsesPending) || 0;
      const oldCurrentPcsesAssigned = parseInt(oldCurrentItem.PcsesAssigned) || 0;

      const oldNewPcsesAssigned = Math.max(0, oldCurrentPcsesAssigned - existPcs);
      const oldNewPcsesPending = oldCurrentPcsesPending + existPcs;

      await BuyerOrder.findOneAndUpdate(
        { "items._id": existId},
        {
          $set: {
            "items.$.PcsesAssigned": oldNewPcsesAssigned,
            "items.$.PcsesPending": oldNewPcsesPending
          }
        },
        { returnNewDocument: true }
      );
    }

    // Assign to the new item
    const newBuyerOrderItem = await BuyerOrder.findOne(
      { "items._id": data.itemId },
      { "items.$": 1 }
    );

    if (newBuyerOrderItem && newBuyerOrderItem.items && newBuyerOrderItem.items.length > 0) {
      const newCurrentItem = newBuyerOrderItem.items[0];
      const newCurrentPcsesPending = parseInt(newCurrentItem.PcsesPending) || 0;
      const newCurrentPcsesAssigned = parseInt(newCurrentItem.PcsesAssigned) || 0;

      const finalNewPcsesPending = Math.max(0, newCurrentPcsesPending - newPcs);
      const finalNewPcsesAssigned = newCurrentPcsesAssigned + newPcs;

      await BuyerOrder.findOneAndUpdate(
        { "items._id": data.itemId},
        {
          $set: {
            "items.$.PcsesAssigned": finalNewPcsesAssigned,
            "items.$.PcsesPending": finalNewPcsesPending
          }
        },
        { returnNewDocument: true }
      );
    }

    // Update PCSWaitingToBeReceived for different item case
    const currentPCSReceived = currentIssue.PCSReceived || 0;
    const newPCSWaitingToBeReceived = newPcs - currentPCSReceived;
    data.PCSWaitingToBeReceived = Math.max(0, newPCSWaitingToBeReceived);

    return await CarpetOrderissue.findByIdAndUpdate(id, data, { new: true });
  }













};

const updateCarpetOrderissueField = async (id, field, value) => {
  const update = { [field]: value };
  return await CarpetOrderissue.findByIdAndUpdate(id, { $set: update }, { new: true });
};

// Function to update PCS fields based on received carpets
const updatePCSFields = async (issueId) => {
  try {
    // Get the carpet order issue
    const issue = await CarpetOrderissue.findById(issueId);
    if (!issue) return null;

    // Count received pieces for this issue
    const CarpetReceivedRepo = require('./carpetReceived-repository');
    const receivedCount = await CarpetReceivedRepo.countReceivedPiecesByIssueId(issueId);

    // Calculate waiting pieces
    const totalPcs = parseInt(issue.pcs) || 0;
    const waitingPcs = Math.max(0, totalPcs - receivedCount);

    // Update the fields
    const updatedIssue = await CarpetOrderissue.findByIdAndUpdate(
      issueId,
      {
        $set: {
          PCSReceived: receivedCount,
          PCSWaitingToBeReceived: waitingPcs
        }
      },
      { new: true }
    );

    return updatedIssue;
  } catch (error) {
    console.error('Error updating PCS fields:', error);
    throw error;
  }
};

// Function to fix NaN values in buyer orders
const fixBuyerOrderNaNValues = async () => {
  try {
    const BuyerOrder = require('../../model/phase-4/buyerOrder');

    // Find all buyer orders with NaN values
    const buyerOrders = await BuyerOrder.find({});
    let fixedCount = 0;

    for (const order of buyerOrders) {
      let needsUpdate = false;

      for (const item of order.items) {
        if (isNaN(item.PcsesPending) || item.PcsesPending === null || item.PcsesPending === undefined) {
          item.PcsesPending = Math.max(0, (item.pcs || 0) - (item.PcsesAssigned || 0));
          needsUpdate = true;
        }
        if (isNaN(item.PcsesAssigned) || item.PcsesAssigned === null || item.PcsesAssigned === undefined) {
          item.PcsesAssigned = 0;
          needsUpdate = true;
        }
      }

      if (needsUpdate) {
        await order.save();
        fixedCount++;
      }
    }

    console.log(`Fixed ${fixedCount} buyer orders with NaN values`);
    return { fixedCount, message: `Fixed ${fixedCount} buyer orders with NaN values` };
  } catch (error) {
    console.error('Error fixing NaN values:', error);
    throw error;
  }
};

// Function to initialize PCS fields for existing records
const initializePCSFields = async () => {
  try {
    const issues = await CarpetOrderissue.find({
      $or: [
        { PCSWaitingToBeReceived: { $exists: false } },
        { PCSReceived: { $exists: false } }
      ]
    });

    for (const issue of issues) {
      await updatePCSFields(issue._id);
    }

    return { message: 'PCS fields initialized successfully', count: issues.length };
  } catch (error) {
    console.error('Error initializing PCS fields:', error);
    throw error;
  }
};

const deleteCarpetOrderissueField = async (id, field) => {
  let data = await CarpetOrderissue.findOne({_id:id});
  if(data){
    let {itemId, pcs} = data;
    await BuyerOrder.findOneAndUpdate(
      { "items._id": itemId},
      {
        $inc: {
          "items.$.PcsesAssigned": -parseInt(pcs),
          "items.$.PcsesPending": parseInt(pcs)
        }
      },
      { returnNewDocument: true }
    )
    console.log(data);
    return await CarpetOrderissue.deleteOne({_id:id}, { new: true });
  }else{
    return null
  }

};

const deleteCarpetOrderissue = async (id) => {
  // First get the carpet order issue data
  let data = await CarpetOrderissue.findOne({_id: id});
  if (!data) {
    throw new Error('Carpet order issue not found');
  }

  // Check if PCSReceived > 0, if so, don't allow deletion
  if (data.PCSReceived && data.PCSReceived > 0) {
    throw new Error(`Cannot delete this issue. ${data.PCSReceived} pieces have already been received.`);
  }

  let {itemId, pcs} = data;

  // Revert the buyer order changes
  await BuyerOrder.findOneAndUpdate(
    { "items._id": itemId},
    {
      $inc: {
        "items.$.PcsesAssigned": -parseInt(pcs),
        "items.$.PcsesPending": parseInt(pcs)
      }
    },
    { returnNewDocument: true }
  );

  // Now delete the carpet order issue
  return await CarpetOrderissue.findByIdAndDelete(id);
};


const getUsingPopulate = async ()=>{

//   const results = await CarpetOrderissue.find()
//   .populate({
//     path: 'buyerOrder', // Populate BuyerOrder reference
//     populate: {
//       path: 'items', // Populate items array in BuyerOrder

//       populate: [
//         {
//           path: 'quality', // Populate AddQuality reference in items
//           model: 'AddQuality'
//         },
//         {
//           path: 'design', // Populate AddDesign reference in items
//           model: 'AddDesign'
//         },
//         {
//           path: 'sizeId', // Populate SizeMaster reference in items
//           model: 'SizeMaster'
//         }
//       ]
//     }
//   })
//   .populate('weaver') // Populate WeaverEmployee reference
//   .populate('branch'); // Populate Branch reference

// if (!results || results.length === 0) {
//   console.log('No results found.');
//   return [];
// }

const results = await CarpetOrderissue.find()
.populate({
  path: 'buyerOrder', // Populate BuyerOrder reference
  populate: {
    path: 'items', // Populate items array in BuyerOrder
    populate: [
      {
        path: 'quality', // Populate AddQuality reference in items
        model: 'AddQuality'
      },
      {
        path: 'design', // Populate AddDesign reference in items
        model: 'AddDesign'
      },
      {
        path: 'sizeId', // Populate SizeMaster reference in items
        model: 'SizeMaster'
      }
    ]
  }
})
.populate({
  path: 'weaver',
  populate: {
    path: 'branch',
    model: 'branch'
  }
}) // Populate WeaverEmployee reference with branch
.populate('quality') // Populate quality reference
.populate('design') // Populate design reference
.populate('size') // Populate size reference
.populate('branch'); // Populate Branch reference

if (!results || results.length === 0) {
console.log('No results found.');
return [];
}

// Return complete carpet order issue data with populated fields
return results;
// Flatten the results
// const flattenedResults = results.flatMap(order => {
//   if (!order.buyerOrder || !order.buyerOrder.items) {
//     console.log('Missing buyer order or items for order:', order._id);
//     return [];
//   }

//   return order.buyerOrder.items
//     .filter(item => item._id.equals(order.itemId)) // Ensure we are matching the correct item ID
//     .map(item => ({
//       Br_issueNo: order.Br_issueNo,
//       branch: order.branch ? order.branch.name : null, // Adjust based on the actual field in the Branch model
//       date: order.date,
//       rate: order.rate,
//       MapOrderNo: order.MapOrderNo,
//       buyerOrderNo: order.buyerOrder ? order.buyerOrder.orderNo : null,
//       weaver: order.weaver ? order.weaver.name : null, // Adjust based on the actual field in the WeaverEmployee model
//       groundColour: item.groundColour,
//       quality: item.quality ? item.quality.quality : null, // Handle potential nulls
//       design: item.design ? item.design.design : null,
//       sizeInYard: item.sizeId ? item.sizeId.sizeInYard : null,
//       areaInYard: item.sizeId ? item.sizeId.areaInYard : null,
//       areaInfeet: item.sizeId ? item.sizeId.areaInfeet : null
//     }));
// });

// console.log('Flattened Results:', flattenedResults);
// return flattenedResults;

}

module.exports = {
  createCarpetOrderissue,
  getAllCarpetOrderissues,
  getCarpetOrderissueById,
  updateCarpetOrderissue,
  updateCarpetOrderissueField,
  deleteCarpetOrderissueField,
  deleteCarpetOrderissue,
  getUsingPopulate,
  updatePCSFields,
  initializePCSFields,
  fixBuyerOrderNaNValues
};
