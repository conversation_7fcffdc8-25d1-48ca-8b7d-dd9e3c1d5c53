const finishingHeadRepository = require('../repositories/finishingHead-repository');

const createFinishingHead = async (finishingHeadData) => {
  return await finishingHeadRepository.createFinishingHead(finishingHeadData);
};

const getAllFinishingHeads = async () => {
  return await finishingHeadRepository.getAllFinishingHeads();
};

const getFinishingHeadById = async (id) => {
  return await finishingHeadRepository.getFinishingHeadById(id);
};

const updateFinishingHead = async (id, finishingHeadData) => {
  return await finishingHeadRepository.updateFinishingHead(id, finishingHeadData);
};

const deleteFinishingHead = async (id) => {
  return await finishingHeadRepository.deleteFinishingHead(id);
};

module.exports = {
  createFinishingHead,
  getAllFinishingHeads,
  getFinishingHeadById,
  updateFinishingHead,
  deleteFinishingHead
};
