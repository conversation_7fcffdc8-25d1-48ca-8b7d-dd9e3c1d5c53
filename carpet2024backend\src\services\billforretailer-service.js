const BillForRetailerRepository = require('../repositories/billforretailer-repository');

class BillForRetailerService {
    constructor() {
        this.billForRetailerRepository = new BillForRetailerRepository();
    }
   
    async createBill(billData) {
        try {
            // If challanNo is not an array, convert it into an array
            if (!Array.isArray(billData.challanNo)) {
                billData.challanNo = [billData.challanNo];
            }
            return await this.billForRetailerRepository.create(billData);
        } catch (error) {
            throw error;
        }
    }

    async getAllBills() {
        try {
            return await this.billForRetailerRepository.findAll();
        } catch (error) {
            throw error;
        }
    }

    async getBillById(id) {
        try {
            return await this.billForRetailerRepository.findById(id);
        } catch (error) {
            throw error;
        }
    }

    async updateBill(id, billData) {
        try {
            return await this.billForRetailerRepository.update(id, billData);
        } catch (error) {
            throw error;
        }
    }

    async deleteBill(id) {
        try {
            return await this.billForRetailerRepository.delete(id);
        } catch (error) {
            throw error;
        }
    }
}

module.exports = BillForRetailerService;
