/*! Buttons for DataTables 2.3.4
 * ©2016-2023 SpryMedia Ltd - datatables.net/license
 */
import $ from"jquery";import DataTable from"datatables.net";var _instCounter=0,_buttonCounter=0,_dtButtons=DataTable.ext.buttons;function _fadeIn(t,n,e){$.fn.animate?t.stop().fadeIn(n,e):(t.css("display","block"),e&&e.call(t))}function _fadeOut(t,n,e){$.fn.animate?t.stop().fadeOut(n,e):(t.css("display","none"),e&&e.call(t))}var _infoTimer,Buttons=function(n,t){if(!(this instanceof Buttons))return function(t){return new Buttons(t,n).container()};!0===(t=void 0===t?{}:t)&&(t={}),Array.isArray(t)&&(t={buttons:t}),this.c=$.extend(!0,{},Buttons.defaults,t),t.buttons&&(this.c.buttons=t.buttons),this.s={dt:new DataTable.Api(n),buttons:[],listenKeys:"",namespace:"dtb"+_instCounter++},this.dom={container:$("<"+this.c.dom.container.tag+"/>").addClass(this.c.dom.container.className)},this._constructor()},_filename=($.extend(Buttons.prototype,{action:function(t,n){t=this._nodeToButton(t);return void 0===n?t.conf.action:(t.conf.action=n,this)},active:function(t,n){var t=this._nodeToButton(t),e=this.c.dom.button.active,t=$(t.node);return void 0===n?t.hasClass(e):(t.toggleClass(e,void 0===n||n),this)},add:function(t,n,e){var o=this.s.buttons;if("string"==typeof n){for(var i=n.split("-"),s=this.s,a=0,r=i.length-1;a<r;a++)s=s.buttons[+i[a]];o=s.buttons,n=+i[i.length-1]}return this._expandButton(o,t,void 0!==t?t.split:void 0,(void 0===t||void 0===t.split||0===t.split.length)&&void 0!==s,!1,n),void 0!==e&&!0!==e||this._draw(),this},collectionRebuild:function(t,n){var e=this._nodeToButton(t);if(void 0!==n){for(var o=e.buttons.length-1;0<=o;o--)this.remove(e.buttons[o].node);for(e.conf.prefixButtons&&n.unshift.apply(n,e.conf.prefixButtons),e.conf.postfixButtons&&n.push.apply(n,e.conf.postfixButtons),o=0;o<n.length;o++){var i=n[o];this._expandButton(e.buttons,i,void 0!==i&&void 0!==i.config&&void 0!==i.config.split,!0,void 0!==i.parentConf&&void 0!==i.parentConf.split,null,i.parentConf)}}this._draw(e.collection,e.buttons)},container:function(){return this.dom.container},disable:function(t){t=this._nodeToButton(t);return $(t.node).addClass(this.c.dom.button.disabled).prop("disabled",!0),this},destroy:function(){$("body").off("keyup."+this.s.namespace);for(var t=this.s.buttons.slice(),n=0,e=t.length;n<e;n++)this.remove(t[n].node);this.dom.container.remove();var o=this.s.dt.settings()[0];for(n=0,e=o.length;n<e;n++)if(o.inst===this){o.splice(n,1);break}return this},enable:function(t,n){return!1===n?this.disable(t):(n=this._nodeToButton(t),$(n.node).removeClass(this.c.dom.button.disabled).prop("disabled",!1),this)},index:function(t,n,e){n||(n="",e=this.s.buttons);for(var o=0,i=e.length;o<i;o++){var s=e[o].buttons;if(e[o].node===t)return n+o;if(s&&s.length){s=this.index(t,o+"-",s);if(null!==s)return s}}return null},name:function(){return this.c.name},node:function(t){return t?(t=this._nodeToButton(t),$(t.node)):this.dom.container},processing:function(t,n){var e=this.s.dt,o=this._nodeToButton(t);return void 0===n?$(o.node).hasClass("processing"):($(o.node).toggleClass("processing",n),$(e.table().node()).triggerHandler("buttons-processing.dt",[n,e.button(t),e,$(t),o.conf]),this)},remove:function(t){var n=this._nodeToButton(t),e=this._nodeToHost(t),o=this.s.dt;if(n.buttons.length)for(var i=n.buttons.length-1;0<=i;i--)this.remove(n.buttons[i].node);n.conf.destroying=!0,n.conf.destroy&&n.conf.destroy.call(o.button(t),o,$(t),n.conf),this._removeKey(n.conf),$(n.node).remove();o=$.inArray(n,e);return e.splice(o,1),this},text:function(t,n){function e(t){return"function"==typeof t?t(i,s,o.conf):t}var o=this._nodeToButton(t),t=this.c.dom.collection.buttonLiner,t=(o.inCollection&&t&&t.tag?t:this.c.dom.buttonLiner).tag,i=this.s.dt,s=$(o.node);return void 0===n?e(o.conf.text):(o.conf.text=n,(t?s.children(t).eq(0).filter(":not(.dt-down-arrow)"):s).html(e(n)),this)},_constructor:function(){var e=this,t=this.s.dt,o=t.settings()[0],n=this.c.buttons;o._buttons||(o._buttons=[]),o._buttons.push({inst:this,name:this.c.name});for(var i=0,s=n.length;i<s;i++)this.add(n[i]);t.on("destroy",function(t,n){n===o&&e.destroy()}),$("body").on("keyup."+this.s.namespace,function(t){var n;document.activeElement&&document.activeElement!==document.body||(n=String.fromCharCode(t.keyCode).toLowerCase(),-1!==e.s.listenKeys.toLowerCase().indexOf(n)&&e._keypress(n,t))})},_addKey:function(t){t.key&&(this.s.listenKeys+=($.isPlainObject(t.key)?t.key:t).key)},_draw:function(t,n){t||(t=this.dom.container,n=this.s.buttons),t.children().detach();for(var e=0,o=n.length;e<o;e++)t.append(n[e].inserter),t.append(" "),n[e].buttons&&n[e].buttons.length&&this._draw(n[e].collection,n[e].buttons)},_expandButton:function(t,n,e,o,i,s,a){var r=this.s.dt,l=!1,u=Array.isArray(n)?n:[n];void 0===n&&(u=Array.isArray(e)?e:[e]),void 0!==n&&void 0!==n.split&&(l=!0);for(var c=0,d=u.length;c<d;c++){var f=this._resolveExtends(u[c]);if(f)if(l=!(void 0===f.config||!f.config.split),Array.isArray(f))this._expandButton(t,f,void 0!==p&&void 0!==p.conf?p.conf.split:void 0,o,void 0!==a&&void 0!==a.split,s,a);else{var p=this._buildButton(f,o,void 0!==f.split||void 0!==f.config&&void 0!==f.config.split,i);if(p){if(null!=s?(t.splice(s,0,p),s++):t.push(p),p.conf.buttons||p.conf.split){if(p.collection=$("<"+(l?this.c.dom.splitCollection:this.c.dom.collection).tag+"/>"),p.conf._collection=p.collection,p.conf.split)for(var b=0;b<p.conf.split.length;b++)"object"==typeof p.conf.split[b]&&(p.conf.split[b].parent=a,void 0===p.conf.split[b].collectionLayout&&(p.conf.split[b].collectionLayout=p.conf.collectionLayout),void 0===p.conf.split[b].dropup&&(p.conf.split[b].dropup=p.conf.dropup),void 0===p.conf.split[b].fade&&(p.conf.split[b].fade=p.conf.fade));else $(p.node).append($('<span class="dt-down-arrow">'+this.c.dom.splitDropdown.text+"</span>"));this._expandButton(p.buttons,p.conf.buttons,p.conf.split,!l,l,s,p.conf)}p.conf.parent=a,f.init&&f.init.call(r.button(p.node),r,$(p.node),f),0}}}},_buildButton:function(n,t,e,o){function i(t){return"function"==typeof t?t(b,l,n):t}var s,a,r,l,u=this.c.dom.button,c=this.c.dom.buttonLiner,d=this.c.dom.collection,f=(this.c.dom.split,this.c.dom.splitCollection),p=this.c.dom.splitDropdownButton,b=this.s.dt;if(n.spacer)return a=$("<span></span>").addClass("dt-button-spacer "+n.style+" "+u.spacerClass).html(i(n.text)),{conf:n,node:a,inserter:a,buttons:[],inCollection:t,isSplit:e,inSplit:o,collection:null};if(!e&&o&&f?u=p:!e&&t&&d.button&&(u=d.button),!e&&o&&f.buttonLiner?c=f.buttonLiner:!e&&t&&d.buttonLiner&&(c=d.buttonLiner),n.available&&!n.available(b,n)&&!n.hasOwnProperty("html"))return!1;n.hasOwnProperty("html")?l=$(n.html):(s=function(t,n,e,o){o.action.call(n.button(e),t,n,e,o),$(n.table().node()).triggerHandler("buttons-action.dt",[n.button(e),n,e,o])},a=n.tag||u.tag,r=void 0===n.clickBlurs||n.clickBlurs,l=$("<"+a+"/>").addClass(u.className).addClass(o?this.c.dom.splitDropdownButton.className:"").attr("tabindex",this.s.dt.settings()[0].iTabIndex).attr("aria-controls",this.s.dt.table().node().id).on("click.dtb",function(t){t.preventDefault(),!l.hasClass(u.disabled)&&n.action&&s(t,b,l,n),r&&l.trigger("blur")}).on("keypress.dtb",function(t){13===t.keyCode&&(t.preventDefault(),!l.hasClass(u.disabled)&&n.action&&s(t,b,l,n))}),"a"===a.toLowerCase()&&l.attr("href","#"),"button"===a.toLowerCase()&&l.attr("type","button"),c.tag?(p=$("<"+c.tag+"/>").html(i(n.text)).addClass(c.className),"a"===c.tag.toLowerCase()&&p.attr("href","#"),l.append(p)):l.html(i(n.text)),!1===n.enabled&&l.addClass(u.disabled),n.className&&l.addClass(n.className),n.titleAttr&&l.attr("title",i(n.titleAttr)),n.attr&&l.attr(n.attr),n.namespace||(n.namespace=".dt-button-"+_buttonCounter++),void 0!==n.config&&n.config.split&&(n.split=n.config.split));var h,g,m,v,f=this.c.dom.buttonContainer,d=f&&f.tag?$("<"+f.tag+"/>").addClass(f.className).append(l):l;return this._addKey(n),this.c.buttonCreated&&(d=this.c.buttonCreated(n,d)),e&&((h=$("<div/>").addClass(this.c.dom.splitWrapper.className)).append(l),g=$.extend(n,{text:this.c.dom.splitDropdown.text,className:this.c.dom.splitDropdown.className,closeButton:!1,attr:{"aria-haspopup":"dialog","aria-expanded":!1},align:this.c.dom.splitDropdown.align,splitAlignClass:this.c.dom.splitDropdown.splitAlignClass}),this._addKey(g),m=function(t,n,e,o){_dtButtons.split.action.call(n.button(h),t,n,e,o),$(n.table().node()).triggerHandler("buttons-action.dt",[n.button(e),n,e,o]),e.attr("aria-expanded",!0)},v=$('<button class="'+this.c.dom.splitDropdown.className+' dt-button"><span class="dt-btn-split-drop-arrow">'+this.c.dom.splitDropdown.text+"</span></button>").on("click.dtb",function(t){t.preventDefault(),t.stopPropagation(),v.hasClass(u.disabled)||m(t,b,v,g),r&&v.trigger("blur")}).on("keypress.dtb",function(t){13===t.keyCode&&(t.preventDefault(),v.hasClass(u.disabled)||m(t,b,v,g))}),0===n.split.length&&v.addClass("dtb-hide-drop"),h.append(v).attr(g.attr)),{conf:n,node:(e?h:l).get(0),inserter:e?h:d,buttons:[],inCollection:t,isSplit:e,inSplit:o,collection:null}},_nodeToButton:function(t,n){for(var e=0,o=(n=n||this.s.buttons).length;e<o;e++){if(n[e].node===t)return n[e];if(n[e].buttons.length){var i=this._nodeToButton(t,n[e].buttons);if(i)return i}}},_nodeToHost:function(t,n){for(var e=0,o=(n=n||this.s.buttons).length;e<o;e++){if(n[e].node===t)return n;if(n[e].buttons.length){var i=this._nodeToHost(t,n[e].buttons);if(i)return i}}},_keypress:function(s,a){var r;a._buttonsHandled||(r=function(t){for(var n,e,o=0,i=t.length;o<i;o++)n=t[o].conf,e=t[o].node,!n.key||n.key!==s&&(!$.isPlainObject(n.key)||n.key.key!==s||n.key.shiftKey&&!a.shiftKey||n.key.altKey&&!a.altKey||n.key.ctrlKey&&!a.ctrlKey||n.key.metaKey&&!a.metaKey)||(a._buttonsHandled=!0,$(e).click()),t[o].buttons.length&&r(t[o].buttons)})(this.s.buttons)},_removeKey:function(t){var n;t.key&&(t=($.isPlainObject(t.key)?t.key:t).key,n=this.s.listenKeys.split(""),t=$.inArray(t,n),n.splice(t,1),this.s.listenKeys=n.join(""))},_resolveExtends:function(e){function t(t){for(var n=0;!$.isPlainObject(t)&&!Array.isArray(t);){if(void 0===t)return;if("function"==typeof t){if(!(t=t.call(i,s,e)))return!1}else if("string"==typeof t){if(!_dtButtons[t])return{html:t};t=_dtButtons[t]}if(30<++n)throw"Buttons: Too many iterations"}return Array.isArray(t)?t:$.extend({},t)}var n,o,i=this,s=this.s.dt;for(e=t(e);e&&e.extend;){if(!_dtButtons[e.extend])throw"Cannot extend unknown button type: "+e.extend;var a=t(_dtButtons[e.extend]);if(Array.isArray(a))return a;if(!a)return!1;var r=a.className;void 0!==e.config&&void 0!==a.config&&(e.config=$.extend({},a.config,e.config)),e=$.extend({},a,e),r&&e.className!==r&&(e.className=r+" "+e.className),e.extend=a.extend}var l=e.postfixButtons;if(l)for(e.buttons||(e.buttons=[]),n=0,o=l.length;n<o;n++)e.buttons.push(l[n]);var u=e.prefixButtons;if(u)for(e.buttons||(e.buttons=[]),n=0,o=u.length;n<o;n++)e.buttons.splice(n,0,u[n]);return e},_popover:function(o,t,n,e){function i(){b=!0,_fadeOut($(".dt-button-collection"),h.fade,function(){$(this).detach()}),$(f.buttons('[aria-haspopup="dialog"][aria-expanded="true"]').nodes()).attr("aria-expanded","false"),$("div.dt-button-background").off("click.dtb-collection"),Buttons.background(!1,h.backgroundClassName,h.fade,g),$(window).off("resize.resize.dtb-collection"),$("body").off(".dtb-collection"),f.off("buttons-action.b-internal"),f.off("destroy")}var s,a,r,l,u,c,d,f=t,p=this.c,b=!1,h=$.extend({align:"button-left",autoClose:!1,background:!0,backgroundClassName:"dt-button-background",closeButton:!0,contentClassName:p.dom.collection.className,collectionLayout:"",collectionTitle:"",dropup:!1,fade:400,popoverTitle:"",rightAlignClassName:"dt-button-right",tag:p.dom.collection.tag},n),g=t.node();!1===o?i():((p=$(f.buttons('[aria-haspopup="dialog"][aria-expanded="true"]').nodes())).length&&(g.closest("div.dt-button-collection").length&&(g=p.eq(0)),i()),n=$(".dt-button",o).length,p="",3===n?p="dtb-b3":2===n?p="dtb-b2":1===n&&(p="dtb-b1"),s=$("<div/>").addClass("dt-button-collection").addClass(h.collectionLayout).addClass(h.splitAlignClass).addClass(p).css("display","none").attr({"aria-modal":!0,role:"dialog"}),o=$(o).addClass(h.contentClassName).attr("role","menu").appendTo(s),g.attr("aria-expanded","true"),g.parents("body")[0]!==document.body&&(g=document.body.lastChild),h.popoverTitle?s.prepend('<div class="dt-button-collection-title">'+h.popoverTitle+"</div>"):h.collectionTitle&&s.prepend('<div class="dt-button-collection-title">'+h.collectionTitle+"</div>"),h.closeButton&&s.prepend('<div class="dtb-popover-close">x</div>').addClass("dtb-collection-closeable"),_fadeIn(s.insertAfter(g),h.fade),n=$(t.table().container()),d=s.css("position"),"container"!==h.span&&"dt-container"!==h.align||(g=g.parent(),s.css("width",n.width())),"absolute"===d?(p=$(g[0].offsetParent),t=g.position(),n=g.offset(),a=p.offset(),r=p.position(),l=window.getComputedStyle(p[0]),a.height=p.outerHeight(),a.width=p.width()+parseFloat(l.paddingLeft),a.right=a.left+a.width,a.bottom=a.top+a.height,p=t.top+g.outerHeight(),u=t.left,s.css({top:p,left:u}),l=window.getComputedStyle(s[0]),(c=s.offset()).height=s.outerHeight(),c.width=s.outerWidth(),c.right=c.left+c.width,c.bottom=c.top+c.height,c.marginTop=parseFloat(l.marginTop),c.marginBottom=parseFloat(l.marginBottom),h.dropup&&(p=t.top-c.height-c.marginTop-c.marginBottom),"button-right"!==h.align&&!s.hasClass(h.rightAlignClassName)||(u=t.left-c.width+g.outerWidth()),"dt-container"!==h.align&&"container"!==h.align||(u=u<t.left?-t.left:u)+c.width>a.width&&(u=a.width-c.width),r.left+u+c.width>$(window).width()&&(u=$(window).width()-c.width-r.left),n.left+u<0&&(u=-n.left),r.top+p+c.height>$(window).height()+$(window).scrollTop()&&(p=t.top-c.height-c.marginTop-c.marginBottom),r.top+p<$(window).scrollTop()&&(p=t.top+g.outerHeight()),s.css({top:p,left:u})):((d=function(){var t=$(window).height()/2,n=s.height()/2;s.css("marginTop",-1*(n=t<n?t:n))})(),$(window).on("resize.dtb-collection",function(){d()})),h.background&&Buttons.background(!0,h.backgroundClassName,h.fade,h.backgroundHost||g),$("div.dt-button-background").on("click.dtb-collection",function(){}),h.autoClose&&setTimeout(function(){f.on("buttons-action.b-internal",function(t,n,e,o){o[0]!==g[0]&&i()})},0),$(s).trigger("buttons-popover.dt"),f.on("destroy",i),setTimeout(function(){b=!1,$("body").on("click.dtb-collection",function(t){var n,e;b||(n=$.fn.addBack?"addBack":"andSelf",e=$(t.target).parent()[0],($(t.target).parents()[n]().filter(o).length||$(e).hasClass("dt-buttons"))&&!$(t.target).hasClass("dt-button-background")||i())}).on("keyup.dtb-collection",function(t){27===t.keyCode&&i()}).on("keydown.dtb-collection",function(t){var n=$("a, button",o),e=document.activeElement;9===t.keyCode&&(-1===n.index(e)?(n.first().focus(),t.preventDefault()):t.shiftKey?e===n[0]&&(n.last().focus(),t.preventDefault()):e===n.last()[0]&&(n.first().focus(),t.preventDefault()))})},0))}}),Buttons.background=function(t,n,e,o){void 0===e&&(e=400),o=o||document.body,t?_fadeIn($("<div/>").addClass(n).css("display","none").insertAfter(o),e):_fadeOut($("div."+n),e,function(){$(this).removeClass(n).remove()})},Buttons.instanceSelector=function(t,i){var s,a,r;return null==t?$.map(i,function(t){return t.inst}):(s=[],a=$.map(i,function(t){return t.name}),(r=function(t){var n;if(Array.isArray(t))for(var e=0,o=t.length;e<o;e++)r(t[e]);else"string"==typeof t?-1!==t.indexOf(",")?r(t.split(",")):-1!==(n=$.inArray(t.trim(),a))&&s.push(i[n].inst):"number"==typeof t?s.push(i[t].inst):"object"==typeof t&&s.push(t)})(t),s)},Buttons.buttonSelector=function(t,n){function u(t,n){var e=[],o=(d(e,n.s.buttons),$.map(e,function(t){return t.node}));if(Array.isArray(t)||t instanceof $)for(s=0,a=t.length;s<a;s++)u(t[s],n);else if(null==t||"*"===t)for(s=0,a=e.length;s<a;s++)c.push({inst:n,node:e[s].node});else if("number"==typeof t)n.s.buttons[t]&&c.push({inst:n,node:n.s.buttons[t].node});else if("string"==typeof t)if(-1!==t.indexOf(","))for(var i=t.split(","),s=0,a=i.length;s<a;s++)u(i[s].trim(),n);else if(t.match(/^\d+(\-\d+)*$/)){var r=$.map(e,function(t){return t.idx});c.push({inst:n,node:e[$.inArray(t,r)].node})}else if(-1!==t.indexOf(":name")){var l=t.replace(":name","");for(s=0,a=e.length;s<a;s++)e[s].name===l&&c.push({inst:n,node:e[s].node})}else $(o).filter(t).each(function(){c.push({inst:n,node:this})});else"object"!=typeof t||!t.nodeName||-1!==(r=$.inArray(t,o))&&c.push({inst:n,node:o[r]})}for(var c=[],d=function(t,n,e){for(var o,i,s=0,a=n.length;s<a;s++)(o=n[s])&&(t.push({node:o.node,name:o.conf.name,idx:i=void 0!==e?e+s:s+""}),o.buttons&&d(t,o.buttons,i+"-"))},e=0,o=t.length;e<o;e++){var i=t[e];u(n,i)}return c},Buttons.stripData=function(t,n){return"string"==typeof t&&(t=(t=t.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"")).replace(/<!\-\-.*?\-\->/g,""),n&&!n.stripHtml||(t=t.replace(/<[^>]*>/g,"")),n&&!n.trim||(t=t.replace(/^\s+|\s+$/g,"")),n&&!n.stripNewlines||(t=t.replace(/\n/g," ")),n&&!n.decodeEntities||(_exportTextarea.innerHTML=t,t=_exportTextarea.value)),t},Buttons.defaults={buttons:["copy","excel","csv","pdf","print"],name:"main",tabIndex:0,dom:{container:{tag:"div",className:"dt-buttons"},collection:{tag:"div",className:""},button:{tag:"button",className:"dt-button",active:"active",disabled:"disabled",spacerClass:""},buttonLiner:{tag:"span",className:""},split:{tag:"div",className:"dt-button-split"},splitWrapper:{tag:"div",className:"dt-btn-split-wrapper"},splitDropdown:{tag:"button",text:"&#x25BC;",className:"dt-btn-split-drop",align:"split-right",splitAlignClass:"dt-button-split-left"},splitDropdownButton:{tag:"button",className:"dt-btn-split-drop-button dt-button"},splitCollection:{tag:"div",className:"dt-button-split-collection"}}},Buttons.version="2.3.4",$.extend(_dtButtons,{collection:{text:function(t){return t.i18n("buttons.collection","Collection")},className:"buttons-collection",closeButton:!1,init:function(t,n,e){n.attr("aria-expanded",!1)},action:function(t,n,e,o){o._collection.parents("body").length?this.popover(!1,o):this.popover(o._collection,o),"keypress"===t.type&&$("a, button",o._collection).eq(0).focus()},attr:{"aria-haspopup":"dialog"}},split:{text:function(t){return t.i18n("buttons.split","Split")},className:"buttons-split",closeButton:!1,init:function(t,n,e){return n.attr("aria-expanded",!1)},action:function(t,n,e,o){this.popover(o._collection,o)},attr:{"aria-haspopup":"dialog"}},copy:function(t,n){if(_dtButtons.copyHtml5)return"copyHtml5"},csv:function(t,n){if(_dtButtons.csvHtml5&&_dtButtons.csvHtml5.available(t,n))return"csvHtml5"},excel:function(t,n){if(_dtButtons.excelHtml5&&_dtButtons.excelHtml5.available(t,n))return"excelHtml5"},pdf:function(t,n){if(_dtButtons.pdfHtml5&&_dtButtons.pdfHtml5.available(t,n))return"pdfHtml5"},pageLength:function(t){var n=t.settings()[0].aLengthMenu,e=[],o=[];if(Array.isArray(n[0]))e=n[0],o=n[1];else for(var i=0;i<n.length;i++){var s=n[i];$.isPlainObject(s)?(e.push(s.value),o.push(s.label)):(e.push(s),o.push(s))}return{extend:"collection",text:function(t){return t.i18n("buttons.pageLength",{"-1":"Show all rows",_:"Show %d rows"},t.page.len())},className:"buttons-page-length",autoClose:!0,buttons:$.map(e,function(s,t){return{text:o[t],className:"button-page-length",action:function(t,n){n.page.len(s).draw()},init:function(t,n,e){function o(){i.active(t.page.len()===s)}var i=this;t.on("length.dt"+e.namespace,o),o()},destroy:function(t,n,e){t.off("length.dt"+e.namespace)}}}),init:function(t,n,e){var o=this;t.on("length.dt"+e.namespace,function(){o.text(e.text)})},destroy:function(t,n,e){t.off("length.dt"+e.namespace)}}},spacer:{style:"empty",spacer:!0,text:function(t){return t.i18n("buttons.spacer","")}}}),DataTable.Api.register("buttons()",function(n,e){void 0===e&&(e=n,n=void 0),this.selector.buttonGroup=n;var t=this.iterator(!0,"table",function(t){if(t._buttons)return Buttons.buttonSelector(Buttons.instanceSelector(n,t._buttons),e)},!0);return t._groupSelector=n,t}),DataTable.Api.register("button()",function(t,n){t=this.buttons(t,n);return 1<t.length&&t.splice(1,t.length),t}),DataTable.Api.registerPlural("buttons().active()","button().active()",function(n){return void 0===n?this.map(function(t){return t.inst.active(t.node)}):this.each(function(t){t.inst.active(t.node,n)})}),DataTable.Api.registerPlural("buttons().action()","button().action()",function(n){return void 0===n?this.map(function(t){return t.inst.action(t.node)}):this.each(function(t){t.inst.action(t.node,n)})}),DataTable.Api.registerPlural("buttons().collectionRebuild()","button().collectionRebuild()",function(e){return this.each(function(t){for(var n=0;n<e.length;n++)"object"==typeof e[n]&&(e[n].parentConf=t);t.inst.collectionRebuild(t.node,e)})}),DataTable.Api.register(["buttons().enable()","button().enable()"],function(n){return this.each(function(t){t.inst.enable(t.node,n)})}),DataTable.Api.register(["buttons().disable()","button().disable()"],function(){return this.each(function(t){t.inst.disable(t.node)})}),DataTable.Api.register("button().index()",function(){var n=null;return this.each(function(t){t=t.inst.index(t.node);null!==t&&(n=t)}),n}),DataTable.Api.registerPlural("buttons().nodes()","button().node()",function(){var n=$();return $(this.each(function(t){n=n.add(t.inst.node(t.node))})),n}),DataTable.Api.registerPlural("buttons().processing()","button().processing()",function(n){return void 0===n?this.map(function(t){return t.inst.processing(t.node)}):this.each(function(t){t.inst.processing(t.node,n)})}),DataTable.Api.registerPlural("buttons().text()","button().text()",function(n){return void 0===n?this.map(function(t){return t.inst.text(t.node)}):this.each(function(t){t.inst.text(t.node,n)})}),DataTable.Api.registerPlural("buttons().trigger()","button().trigger()",function(){return this.each(function(t){t.inst.node(t.node).trigger("click")})}),DataTable.Api.register("button().popover()",function(n,e){return this.map(function(t){return t.inst._popover(n,this.button(this[0].node),e)})}),DataTable.Api.register("buttons().containers()",function(){var i=$(),s=this._groupSelector;return this.iterator(!0,"table",function(t){if(t._buttons)for(var n=Buttons.instanceSelector(s,t._buttons),e=0,o=n.length;e<o;e++)i=i.add(n[e].container())}),i}),DataTable.Api.register("buttons().container()",function(){return this.containers().eq(0)}),DataTable.Api.register("button().add()",function(t,n,e){var o=this.context;return o.length&&(o=Buttons.instanceSelector(this._groupSelector,o[0]._buttons)).length&&o[0].add(n,t,e),this.button(this._groupSelector,t)}),DataTable.Api.register("buttons().destroy()",function(){return this.pluck("inst").unique().each(function(t){t.destroy()}),this}),DataTable.Api.registerPlural("buttons().remove()","buttons().remove()",function(){return this.each(function(t){t.inst.remove(t.node)}),this}),DataTable.Api.register("buttons.info()",function(t,n,e){var o=this;return!1===t?(this.off("destroy.btn-info"),_fadeOut($("#datatables_buttons_info"),400,function(){$(this).remove()}),clearTimeout(_infoTimer),_infoTimer=null):(_infoTimer&&clearTimeout(_infoTimer),$("#datatables_buttons_info").length&&$("#datatables_buttons_info").remove(),t=t?"<h2>"+t+"</h2>":"",_fadeIn($('<div id="datatables_buttons_info" class="dt-button-info"/>').html(t).append($("<div/>")["string"==typeof n?"html":"append"](n)).css("display","none").appendTo("body")),void 0!==e&&0!==e&&(_infoTimer=setTimeout(function(){o.buttons.info(!1)},e)),this.on("destroy.btn-info",function(){o.buttons.info(!1)})),this}),DataTable.Api.register("buttons.exportData()",function(t){if(this.context.length)return _exportData(new DataTable.Api(this.context[0]),t)}),DataTable.Api.register("buttons.exportInfo()",function(t){return{filename:_filename(t=t||{}),title:_title(t),messageTop:_message(this,t.message||t.messageTop,"top"),messageBottom:_message(this,t.messageBottom,"bottom")}}),function(t){var n;return null==(n="function"==typeof(n="*"===t.filename&&"*"!==t.title&&void 0!==t.title&&null!==t.title&&""!==t.title?t.title:t.filename)?n():n)?null:(n=(n=-1!==n.indexOf("*")?n.replace("*",$("head > title").text()).trim():n).replace(/[^a-zA-Z0-9_\u00A1-\uFFFF\.,\-_ !\(\)]/g,""))+(_stringOrFunction(t.extension)||"")}),_stringOrFunction=function(t){return null==t?null:"function"==typeof t?t():t},_title=function(t){t=_stringOrFunction(t.title);return null===t?null:-1!==t.indexOf("*")?t.replace("*",$("head > title").text()||"Exported data"):t},_message=function(t,n,e){n=_stringOrFunction(n);return null===n?null:(t=$("caption",t.table().container()).eq(0),"*"===n?t.css("caption-side")!==e?null:t.length?t.text():"":n)},_exportTextarea=$("<textarea/>")[0],_exportData=function(e,t){for(var o=$.extend(!0,{},{rows:null,columns:"",modifier:{search:"applied",order:"applied"},orthogonal:"display",stripHtml:!0,stripNewlines:!0,decodeEntities:!0,trim:!0,format:{header:function(t){return Buttons.stripData(t,o)},footer:function(t){return Buttons.stripData(t,o)},body:function(t){return Buttons.stripData(t,o)}},customizeData:null},t),t=e.columns(o.columns).indexes().map(function(t){var n=e.column(t).header();return o.format.header(n.innerHTML,t,n)}).toArray(),n=e.table().footer()?e.columns(o.columns).indexes().map(function(t){var n=e.column(t).footer();return o.format.footer(n?n.innerHTML:"",t,n)}).toArray():null,i=$.extend({},o.modifier),i=(e.select&&"function"==typeof e.select.info&&void 0===i.selected&&e.rows(o.rows,$.extend({selected:!0},i)).any()&&$.extend(i,{selected:!0}),e.rows(o.rows,i).indexes().toArray()),i=e.cells(i,o.columns),s=i.render(o.orthogonal).toArray(),a=i.nodes().toArray(),r=t.length,l=[],u=0,c=0,d=0<r?s.length/r:0;c<d;c++){for(var f=[r],p=0;p<r;p++)f[p]=o.format.body(s[u],c,p,a[u]),u++;l[c]=f}i={header:t,footer:n,body:l};return o.customizeData&&o.customizeData(i),i};function _init(t,n){t=new DataTable.Api(t),n=n||t.init().buttons||DataTable.defaults.buttons;return new Buttons(t,n).container()}$.fn.dataTable.Buttons=Buttons,$.fn.DataTable.Buttons=Buttons,$(document).on("init.dt plugin-init.dt",function(t,n){"dt"===t.namespace&&(t=n.oInit.buttons||DataTable.defaults.buttons)&&!n._buttons&&new Buttons(n,t).container()}),DataTable.ext.feature.push({fnInit:_init,cFeature:"B"}),DataTable.ext.features&&DataTable.ext.features.register("buttons",_init);export default DataTable;