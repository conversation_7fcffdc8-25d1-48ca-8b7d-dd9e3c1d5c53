<div class="container mt-4">

  <section>
        <fieldset>
            <legend><b class="d-flex justify-content-center">View Packing List</b></legend>
            <div class="row">
                <div class="col-12">
                    <div *ngIf="isLoading" class="text-center p-4">
                        <mat-spinner diameter="40" class="mx-auto"></mat-spinner>
                        <p class="mt-2">Loading packing lists...</p>
                    </div>

                    <div *ngIf="!isLoading" class="mat-elevation-z8" style="overflow: auto;">
                        <table mat-table [dataSource]="dataSource" matSort class="w-100">

                          <!-- Sr No Column - Only show for first item of each bale -->
                          <ng-container matColumnDef="SrNo">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header class="SrNo-Tw"> Sr.No </th>
                                <td mat-cell *matCellDef="let element" class="SrNo-Tw">
                                    <span *ngIf="element.SrNo > 0">{{element.SrNo}}</span>
                                </td>
                            </ng-container>

                            <!-- Bale No Column -->
                            <ng-container matColumnDef="baleNo">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header class="baleNo-Tw">Bale No</th>
                                <td mat-cell *matCellDef="let element" class="baleNo-Tw">{{element.baleNo}}</td>
                            </ng-container>

                            <!-- Pcs No Column -->
                            <ng-container matColumnDef="pcsNo">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header class="pcsNo-Tw">Pcs No.</th>
                                <td mat-cell *matCellDef="let element" class="pcsNo-Tw">{{element.pcsNo}}</td>
                            </ng-container>

                            <!-- Quality Column -->
                            <ng-container matColumnDef="quality">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
                                <td mat-cell *matCellDef="let element">{{element.quality}}</td>
                            </ng-container>

                            <!-- Design Column -->
                            <ng-container matColumnDef="design">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Design</th>
                                <td mat-cell *matCellDef="let element">{{element.design}}</td>
                            </ng-container>

                            <!-- Colour Column -->
                            <ng-container matColumnDef="colour">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Colour</th>
                                <td mat-cell *matCellDef="let element">{{element.colour}}</td>
                            </ng-container>

                            <!-- Size Column -->
                            <ng-container matColumnDef="size">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header class="size-Tw">Size</th>
                                <td mat-cell *matCellDef="let element" class="size-Tw">{{element.size}}</td>
                            </ng-container>

                            <!-- Area Column -->
                            <ng-container matColumnDef="area">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Area</th>
                                <td mat-cell *matCellDef="let element">
                                    <span *ngIf="element.areaDisplay">{{element.areaDisplay}}</span>
                                    <span *ngIf="!element.areaDisplay && element.areaIn === 'Sq.Feet'">{{element.area}} Ft</span>
                                    <span *ngIf="!element.areaDisplay && element.areaIn === 'Sq.Yard'">{{element.area}} Yd</span>
                                </td>
                            </ng-container>

                            <!-- Total Area Column - Only show for last item of each bale -->
                            <ng-container matColumnDef="tArea">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>T. Area</th>
                                <td mat-cell *matCellDef="let element">
                                    <!-- Show total area only for the last item of each bale -->
                                    <span *ngIf="element.isLastInBale">
                                        {{element.tArea | number:'1.2-2'}}
                                    </span>
                                </td>
                            </ng-container>

                            <!-- Remarks Column -->
                            <ng-container matColumnDef="remarks">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Remarks</th>
                                <td mat-cell *matCellDef="let element">
                                  {{element.remarks}}
                                </td>
                            </ng-container>
                             <ng-container matColumnDef="actions">
                               <th mat-header-cell *matHeaderCellDef>Actions</th>
                                <td mat-cell *matCellDef="let element">
                                  <ng-container *ngIf="element.isLastInBale">
                                    <button mat-icon-button color="warn" (click)="deleteBale(element.baleNo)">
                                      <mat-icon>delete</mat-icon>
                                    </button>
                                  </ng-container>
                                </td>
                            </ng-container>

                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                            <!-- Row shown when there is no matching data -->
                            <tr class="mat-row" *matNoDataRow>
                                <td class="mat-cell text-center" colspan="11">No packing list data available</td>
                            </tr>
                        </table>

                        <mat-paginator [pageSizeOptions]="[50, 10, 25, 100]" aria-label="Select page of packing items"></mat-paginator>
                    </div>
                </div>
            </div>
        </fieldset>
    </section>

</div>