const CarpetReceivedUpdate = require('../../model/phase-4/carpetReceivedUpdates');

class CarpetReceivedUpdatesRepository {
  async createUpdate(updateData) {
    console.log('Repository: Creating/updating carpet record with data:', JSON.stringify(updateData, null, 2));
    console.log('Repository: Carpet ID:', updateData.carpetReceivedId);
    console.log('Repository: Update type:', updateData.updateType);

    // Set the updatedAt timestamp to current time
    updateData.updatedAt = new Date();

    // Check if there's already an update for this carpet (regardless of update type)
    const existingUpdate = await CarpetReceivedUpdate.findOne({
      carpetReceivedId: updateData.carpetReceivedId
    });

    console.log('Repository: Existing update found?', !!existingUpdate);
    if (existingUpdate) {
      console.log('Repository: Existing update details:', JSON.stringify(existingUpdate, null, 2));

      // Create a history entry with the current values before updating
      const historyEntry = {
        updatedAt: existingUpdate.updatedAt
      };

      // Add relevant fields to history entry based on update type
      if (updateData.updateType === 'edit') {
        // Only add to history if there are existing values to store
        if (existingUpdate.size || existingUpdate.deduction) {
          historyEntry.size = existingUpdate.size;
          historyEntry.deduction = existingUpdate.deduction;
          historyEntry.area = existingUpdate.area;
          historyEntry.amount = existingUpdate.amount;
          historyEntry.tds = existingUpdate.tds;
          historyEntry.commission = existingUpdate.commission;
          historyEntry.netAmount = existingUpdate.netAmount;

          // Add to the edit history array
          if (!existingUpdate.editHistory) {
            existingUpdate.editHistory = [];
          }
          existingUpdate.editHistory.push(historyEntry);
          console.log('Repository: Added entry to edit history, new length:', existingUpdate.editHistory.length);
        } else {
          console.log('Repository: No existing edit values to store in history');
        }

        // Update the lastEditAt timestamp
        existingUpdate.lastEditAt = updateData.updatedAt;
      } else if (updateData.updateType === 'payment') {
        // Only add to history if there are existing values to store
        if (existingUpdate.paymentDt || existingUpdate.remarks || existingUpdate.bankName || existingUpdate.chequeNoOrRGSno || existingUpdate.payedAmount) {
          historyEntry.paymentDt = existingUpdate.paymentDt;
          historyEntry.remarks = existingUpdate.remarks;
          historyEntry.bankName = existingUpdate.bankName;
          historyEntry.chequeNoOrRGSno = existingUpdate.chequeNoOrRGSno;
          historyEntry.payedAmount = existingUpdate.payedAmount;
          // Payment history should only include payment-related fields, not size or calculated fields

          // Add to the payment history array
          if (!existingUpdate.paymentHistory) {
            existingUpdate.paymentHistory = [];
          }
          existingUpdate.paymentHistory.push(historyEntry);
          console.log('Repository: Added entry to payment history, new length:', existingUpdate.paymentHistory.length);
        } else {
          console.log('Repository: No existing payment values to store in history');
        }

        // Update the lastPaymentAt timestamp
        existingUpdate.lastPaymentAt = updateData.updatedAt;
      }

      console.log('Repository: Created history entry:', JSON.stringify(historyEntry, null, 2));

      // Update the main record with new values
      if (updateData.updateType === 'edit') {
        // For edit updates, update size, deduction and calculated fields
        existingUpdate.size = updateData.size || existingUpdate.size;
        existingUpdate.deduction = updateData.deduction || existingUpdate.deduction;
        existingUpdate.area = updateData.area || existingUpdate.area;
        existingUpdate.amount = updateData.amount || existingUpdate.amount;
        existingUpdate.tds = updateData.tds || existingUpdate.tds;
        existingUpdate.commission = updateData.commission || existingUpdate.commission;
        existingUpdate.netAmount = updateData.netAmount || existingUpdate.netAmount;
        console.log('Repository: Updated edit values - Size:', existingUpdate.size, 'Deduction:', existingUpdate.deduction);
        console.log('Repository: Updated calculated values - Area:', existingUpdate.area, 'Amount:', existingUpdate.amount);
      } else if (updateData.updateType === 'payment') {
        // For payment updates, update paymentDt, remarks, and new payment fields
        existingUpdate.paymentDt = updateData.paymentDt || existingUpdate.paymentDt;
        existingUpdate.remarks = updateData.remarks || existingUpdate.remarks;
        existingUpdate.bankName = updateData.bankName || existingUpdate.bankName;
        existingUpdate.chequeNoOrRGSno = updateData.chequeNoOrRGSno || existingUpdate.chequeNoOrRGSno;
        existingUpdate.payedAmount = updateData.payedAmount || existingUpdate.payedAmount;
        console.log('Repository: Updated payment values - Date:', existingUpdate.paymentDt, 'Remarks:', existingUpdate.remarks);
        console.log('Repository: Updated payment fields - Bank:', existingUpdate.bankName, 'Cheque/RGS:', existingUpdate.chequeNoOrRGSno, 'Amount:', existingUpdate.payedAmount);
      } else if (updateData.updateType === 'border-size') {
        // For border-size updates, update all border fields
        existingUpdate.date = updateData.date || existingUpdate.date;
        existingUpdate.weight = updateData.weight || existingUpdate.weight;
        existingUpdate.yes = updateData.yes || existingUpdate.yes;
        existingUpdate.no = updateData.no || existingUpdate.no;
        existingUpdate.BottomBorder1 = updateData.BottomBorder1 || existingUpdate.BottomBorder1;
        existingUpdate.BottomBorder2 = updateData.BottomBorder2 || existingUpdate.BottomBorder2;
        existingUpdate.BottomBorder3 = updateData.BottomBorder3 || existingUpdate.BottomBorder3;
        existingUpdate.TopBorder1 = updateData.TopBorder1 || existingUpdate.TopBorder1;
        existingUpdate.TopBorder2 = updateData.TopBorder2 || existingUpdate.TopBorder2;
        existingUpdate.TopBorder3 = updateData.TopBorder3 || existingUpdate.TopBorder3;
        existingUpdate.leftBorder1 = updateData.leftBorder1 || existingUpdate.leftBorder1;
        existingUpdate.leftBorder2 = updateData.leftBorder2 || existingUpdate.leftBorder2;
        existingUpdate.leftBorder3 = updateData.leftBorder3 || existingUpdate.leftBorder3;
        existingUpdate.rightBorder1 = updateData.rightBorder1 || existingUpdate.rightBorder1;
        existingUpdate.rightBorder2 = updateData.rightBorder2 || existingUpdate.rightBorder2;
        existingUpdate.rightBorder3 = updateData.rightBorder3 || existingUpdate.rightBorder3;
        existingUpdate.halfBorder1 = updateData.halfBorder1 || existingUpdate.halfBorder1;
        existingUpdate.halfBorder2 = updateData.halfBorder2 || existingUpdate.halfBorder2;
        existingUpdate.halfBorder3 = updateData.halfBorder3 || existingUpdate.halfBorder3;
        existingUpdate.groundBorder1 = updateData.groundBorder1 || existingUpdate.groundBorder1;
        existingUpdate.groundBorder2 = updateData.groundBorder2 || existingUpdate.groundBorder2;
        existingUpdate.groundBorder3 = updateData.groundBorder3 || existingUpdate.groundBorder3;
        existingUpdate.boxArray = updateData.boxArray || existingUpdate.boxArray;
        existingUpdate.imagesArray = updateData.imagesArray || existingUpdate.imagesArray;
        existingUpdate.Remarks = updateData.Remarks || existingUpdate.Remarks;
      }

      // Update the carpet data if provided
      if (updateData.carpetData) {
        existingUpdate.carpetData = updateData.carpetData;
        console.log('Repository: Updated carpet data');
      }

      // Update the lastUpdateType and updatedAt timestamp
      existingUpdate.lastUpdateType = updateData.updateType;
      existingUpdate.updatedAt = updateData.updatedAt;

      // Clean up any empty history entries
      this.cleanHistoryArrays(existingUpdate);

      // Save and return the updated record
      console.log('Repository: Saving updated record');
      return await existingUpdate.save();
    } else {
      // Create a new record if none exists
      // Initialize with empty history arrays
      const newUpdateData = {
        carpetReceivedId: updateData.carpetReceivedId,
        carpetData: updateData.carpetData,
        lastUpdateType: updateData.updateType,
        updatedAt: updateData.updatedAt
      };

      // Set the appropriate timestamp based on update type
      if (updateData.updateType === 'edit') {
        newUpdateData.size = updateData.size;
        newUpdateData.deduction = updateData.deduction;
        newUpdateData.area = updateData.area;
        newUpdateData.amount = updateData.amount;
        newUpdateData.tds = updateData.tds;
        newUpdateData.commission = updateData.commission;
        newUpdateData.netAmount = updateData.netAmount;
        newUpdateData.lastEditAt = updateData.updatedAt;
        newUpdateData.editHistory = [];
      } else if (updateData.updateType === 'payment') {
        newUpdateData.paymentDt = updateData.paymentDt;
        newUpdateData.remarks = updateData.remarks;
        newUpdateData.bankName = updateData.bankName;
        newUpdateData.chequeNoOrRGSno = updateData.chequeNoOrRGSno;
        newUpdateData.payedAmount = updateData.payedAmount;
        newUpdateData.lastPaymentAt = updateData.updatedAt;
        newUpdateData.paymentHistory = [];
      } else if (updateData.updateType === 'border-size') {
        newUpdateData.date = updateData.date;
        newUpdateData.weight = updateData.weight;
        newUpdateData.yes = updateData.yes;
        newUpdateData.no = updateData.no;
        newUpdateData.BottomBorder1 = updateData.BottomBorder1;
        newUpdateData.BottomBorder2 = updateData.BottomBorder2;
        newUpdateData.BottomBorder3 = updateData.BottomBorder3;
        newUpdateData.TopBorder1 = updateData.TopBorder1;
        newUpdateData.TopBorder2 = updateData.TopBorder2;
        newUpdateData.TopBorder3 = updateData.TopBorder3;
        newUpdateData.leftBorder1 = updateData.leftBorder1;
        newUpdateData.leftBorder2 = updateData.leftBorder2;
        newUpdateData.leftBorder3 = updateData.leftBorder3;
        newUpdateData.rightBorder1 = updateData.rightBorder1;
        newUpdateData.rightBorder2 = updateData.rightBorder2;
        newUpdateData.rightBorder3 = updateData.rightBorder3;
        newUpdateData.halfBorder1 = updateData.halfBorder1;
        newUpdateData.halfBorder2 = updateData.halfBorder2;
        newUpdateData.halfBorder3 = updateData.halfBorder3;
        newUpdateData.groundBorder1 = updateData.groundBorder1;
        newUpdateData.groundBorder2 = updateData.groundBorder2;
        newUpdateData.groundBorder3 = updateData.groundBorder3;
        newUpdateData.boxArray = updateData.boxArray;
        newUpdateData.imagesArray = updateData.imagesArray;
        newUpdateData.Remarks = updateData.Remarks;
      }

      console.log('Repository: Creating new record with empty history');
      const update = new CarpetReceivedUpdate(newUpdateData);
      return await update.save();
    }
  }

  async findByCarpetReceivedId(carpetReceivedId) {
    // Return all updates for this carpet, sorted by most recent first
    return await CarpetReceivedUpdate.find({ carpetReceivedId }).sort({ updatedAt: -1 });
  }

  async getAllCarpetReceivedUpdates() {
    console.log('Repository: Getting all carpet received updates');

    // Find all carpet received updates
    const updates = await CarpetReceivedUpdate.find({})
      .sort({ updatedAt: -1 });

    console.log(`Repository: Found ${updates.length} carpet received updates`);
    return updates;
  }

  async getHistoryByCarpetAndType(carpetReceivedId, updateType) {
    console.log(`Repository: Getting history for carpet ${carpetReceivedId} with type ${updateType}`);

    // Find the document for this carpet (regardless of update type)
    const update = await CarpetReceivedUpdate.findOne({
      carpetReceivedId
    });

    console.log(`Repository: Found update record? ${!!update}`);

    if (!update) {
      console.log('Repository: No update record found, returning empty history');
      return { current: null, history: [] };
    }

    // Clean up the history arrays before returning them
    this.cleanHistoryArrays(update);
    await update.save();

    // Determine which history array to use based on update type
    const historyArray = updateType === 'edit' ? update.editHistory || [] : update.paymentHistory || [];
    console.log(`Repository: Found update record with ${historyArray.length} ${updateType} history entries`);
    console.log('Repository: Update record details:', JSON.stringify(update, null, 2));

    // Extract the current values and history
    const current = {
      updateType,
      updatedAt: updateType === 'edit' ? update.lastEditAt : update.lastPaymentAt,
      carpetReceivedId: update.carpetReceivedId
    };

    // Add the complete carpet data if available
    if (update.carpetData) {
      current.carpetData = update.carpetData;
      console.log('Repository: Including complete carpet data in response');
    }

    // Add type-specific fields
    if (updateType === 'edit') {
      current.size = update.size;
      current.deduction = update.deduction;
      current.area = update.area;
      current.amount = update.amount;
      current.tds = update.tds;
      current.commission = update.commission;
      current.netAmount = update.netAmount;
      console.log(`Repository: Current edit values - Size: ${update.size}, Deduction: ${update.deduction}`);
      console.log(`Repository: Current calculated values - Area: ${update.area}, Amount: ${update.amount}`);
    } else if (updateType === 'payment') {
      current.paymentDt = update.paymentDt;
      current.remarks = update.remarks;
      current.bankName = update.bankName;
      current.chequeNoOrRGSno = update.chequeNoOrRGSno;
      current.payedAmount = update.payedAmount;
      current.size = update.size;
      current.area = update.area;
      current.amount = update.amount;
      console.log(`Repository: Current payment values - Date: ${update.paymentDt}, Remarks: ${update.remarks}`);
    }

    // Sort history by most recent first
    const sortedHistory = historyArray.sort((a, b) => b.updatedAt - a.updatedAt);
    console.log(`Repository: Sorted history has ${sortedHistory.length} entries`);

    // Return the current values and history
    const result = {
      current,
      history: sortedHistory
    };

    console.log('Repository: Returning history result:', JSON.stringify(result, null, 2));
    return result;
  }

  async addCarpetBorderSizeHistory(carpetReceivedId, borderData) {
    const update = await CarpetReceivedUpdate.findOne({ carpetReceivedId });
    if (!update) throw new Error('CarpetReceivedUpdate not found');
    update.carpetBorderSizeHistory = update.carpetBorderSizeHistory || [];
    update.carpetBorderSizeHistory.push({ ...borderData, updatedAt: new Date() });
    await update.save();
    return update.carpetBorderSizeHistory[update.carpetBorderSizeHistory.length - 1];
  }

  async getCarpetBorderSizeHistory(carpetReceivedId) {
    const update = await CarpetReceivedUpdate.findOne({ carpetReceivedId });
    if (!update) throw new Error('CarpetReceivedUpdate not found');
    return update.carpetBorderSizeHistory || [];
  }

  // Helper method to clean history arrays
  cleanHistoryArrays(update) {
    if (update.paymentHistory && update.paymentHistory.length > 0) {
      // First filter out completely empty entries
      update.paymentHistory = update.paymentHistory.filter(entry =>
        entry.paymentDt || entry.remarks
      );

      // Then remove null fields from remaining entries
      update.paymentHistory.forEach(entry => {
        // Remove any size or calculated fields that might exist in older records
        if (entry.size) delete entry.size;
        if (entry.area) delete entry.area;
        if (entry.amount) delete entry.amount;
        if (entry.tds) delete entry.tds;
        if (entry.commission) delete entry.commission;
        if (entry.netAmount) delete entry.netAmount;

        // Only keep payment-related fields
        if (entry.paymentDt === null) delete entry.paymentDt;
        if (entry.remarks === null) delete entry.remarks;
        if (entry.bankName === null) delete entry.bankName;
        if (entry.chequeNoOrRGSno === null) delete entry.chequeNoOrRGSno;
        if (entry.payedAmount === null) delete entry.payedAmount;
      });

      console.log('Repository: Cleaned payment history, new length:', update.paymentHistory.length);
    }

    if (update.editHistory && update.editHistory.length > 0) {
      // First filter out completely empty entries
      update.editHistory = update.editHistory.filter(entry =>
        entry.size || entry.deduction
      );

      // Then remove null fields from remaining entries
      update.editHistory.forEach(entry => {
        if (entry.size === null) delete entry.size;
        if (entry.deduction === null) delete entry.deduction;
        if (entry.area === null) delete entry.area;
        if (entry.amount === null) delete entry.amount;
        if (entry.tds === null) delete entry.tds;
        if (entry.commission === null) delete entry.commission;
        if (entry.netAmount === null) delete entry.netAmount;
        if (entry.paymentDt === null) delete entry.paymentDt;
        if (entry.remarks === null) delete entry.remarks;
      });

      console.log('Repository: Cleaned edit history, new length:', update.editHistory.length);
    }

    return update;
  }
}

module.exports = new CarpetReceivedUpdatesRepository();