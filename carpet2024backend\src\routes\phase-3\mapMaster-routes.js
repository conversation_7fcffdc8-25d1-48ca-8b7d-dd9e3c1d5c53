// routes/mapMasterRoutes.js
const express = require('express');
const mapMasterController = require('../../controller/mapMaster-controller');
const router = express.Router();

const uploadFiles = require('../../middleware/allfile-middleware');

// Route to create a new mapMaster
router.post('/add-mapMaster', uploadFiles, (req, res) => mapMasterController.createMapMaster(req, res));

// Route to get all mapMasters
router.get('/mapMasters', (req, res) => mapMasterController.getAllMapMasters(req, res));

// Route to get a mapMaster by ID
router.get('/mapMasters/:id', (req, res) => mapMasterController.getMapMasterById(req, res));

// Route to update a mapMaster
router.put('/mapMasters/:id', uploadFiles,(req, res) => mapMasterController.updateMapMaster(req, res));

// Route to delete a mapMaster
router.delete('/mapMasters/:id', (req, res) => mapMasterController.deleteMapMaster(req, res));

module.exports = router;
