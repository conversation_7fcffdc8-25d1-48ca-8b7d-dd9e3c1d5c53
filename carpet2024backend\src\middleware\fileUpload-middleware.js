const multer = require('multer');
const path = require('path');

const storagePdf = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.resolve(__dirname, '../../public/all-pdf'));
  },
  filename: function (req, file, cb) {
    cb(null, file.originalname + '-' + Date.now() + '.pdf');
  }
});

const storageExcel = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.resolve(__dirname, '../../public/all-excel'));
  },
  filename: function (req, file, cb) {
    cb(null, file.originalname.replace(/\s/g, '-') + '-' + Date.now() + '.xlsx');
  }
});

const uploadPdf = multer({ storage: storagePdf });

const uploadExcel = multer({
  storage: storageExcel,
  fileFilter: function (req, file, cb) {
    console.log(file);
    if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Please upload an Excel file in XLSX format.'));
    }
  }
});

module.exports = {
  uploadPdf,
  uploadExcel
};

