
fieldset {
    font-family: sans-serif;
    border: 2px solid #1F497D;
    background: #ffffff;
    border-radius: 5px;
    padding: 15px;
  }

  fieldset legend {
    background: #ffffff;
    color: #000000;
    padding: 5px 10px ;
    font-size: 20px;
    border-radius: 5px;
    /* box-shadow: 0 0 0 5px #ddd; */
    margin-left: 20px;
  }

  legend {
    float: left;
    width: auto;
    padding: 0;
    margin-top: -32px;

    margin-bottom: 0.5rem;
    font-size: calc(1.275rem + .3vw);
    line-height: inherit;
  }
  .ex-width{
    width:100%;
  }
  .side{
    display: flex;
    justify-content:space-evenly;

  }

  .max-height{
    max-height: 350px;
    overflow: auto;
  }
.row{
  margin: auto;
  width: 100%;
}
.mat-mdc-form-field {
  width: 100%;
  /* font-size: smaller; */
}
.input-group {
  display: flex;
  gap: 8px; /* Adjust the gap between inputs as needed */
}
/* styles.scss */
.mat-raised-button.danger {
  background-color: #f44336; /* Red color */
  color: white;
}

.mat-raised-button.danger:hover {
  background-color: #d32f2f; /* Darker red color for hover effect */
}
.fa-eye{
  color: #03822d;
}
/* Center the text within the cell */
.others-cell {
  position: relative;
  text-align: center;
  border-top: 1px solid #000; /* Add top border */
}

/* Position the text in the center of the line */
.others-text {
  position: relative;
  top: -0.5em; /* Adjust this value to position the text correctly */
  background: white; /* Match the background color of your table */
  padding: 0 10px; /* Add padding to the left and right of the text */
}


.modal-body{
  overflow: auto;
    height: 400px;
}
table {
  border: 1px solid;
  text-transform: capitalize;
}
table, th, td {
  border: 1px solid;
}
