<div class="container mt-5">
    <section>
    <fieldset>
      <legend><b>Finishing Head</b></legend>
      <div class="row">
        <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Work</mat-label>
                <mat-select [(value)]="work">
                 
                  <mat-option value="option1">Option 1</mat-option>
                  <mat-option value="option2">Option 2</mat-option>
                  <mat-option value="option3">Option 3</mat-option>
                </mat-select>
              </mat-form-field>
        </div>

        <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Name</mat-label>
                <mat-select [(value)]="Name">
                 
                  <mat-option value="option1">Option 1</mat-option>
                  <mat-option value="option2">Option 2</mat-option>
                  <mat-option value="option3">Option 3</mat-option>
                </mat-select>
              </mat-form-field>
        </div>

        <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
                <mat-label>To Date</mat-label>
                <input matInput [matDatepicker]="picker">
                
                <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
              </mat-form-field>
              
        </div>
        <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
                <mat-label>From Date</mat-label>
                <input matInput [matDatepicker]="picker1">
              
                <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
                <mat-datepicker #picker1></mat-datepicker>
              </mat-form-field>
              
        </div>

        <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Quality</mat-label>
                <mat-select [(value)]="Quality">
                 
                  <mat-option value="option1">Option 1</mat-option>
                  <mat-option value="option2">Option 2</mat-option>
                  <mat-option value="option3">Option 3</mat-option>
                </mat-select>
              </mat-form-field>
        </div>
       
        <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Design</mat-label>
                <mat-select [(value)]="Design">
                 
                  <mat-option value="option1">Option 1</mat-option>
                  <mat-option value="option2">Option 2</mat-option>
                  <mat-option value="option3">Option 3</mat-option>
                </mat-select>
              </mat-form-field>
        </div>

        <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Rate</mat-label>
                <input matInput placeholder="Rate">
            </mat-form-field>
        </div>
        <div class="mb-2 col-md-3 mt-2 ">
            <button mat-flat-button color="primary">Add</button>
        </div>

      </div>  
    </fieldset>
</section>
</div>



<div class="container mt-4">
    <section>
      <fieldset>
        <legend><b>List</b></legend>
        <div class="container-fluid">
          <div class="py-3 py-lg-4">
            <div class="row mt-3">
               <div class="col-12">
                <mat-form-field   appearance="outline">
                  <mat-label>Seacrh</mat-label>
                  <input
                  matInput
                  (keyup)="applyFilter($event)"
                  placeholder="Ex. Mia"
                  #input
                />
                </mat-form-field>
  
              <div class="mat-elevation-z8">
                <table mat-table [dataSource]="dataSource" matSort>
                  <!-- srno Column -->
                  <ng-container matColumnDef="SrNo">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Sr.No.</th>
                    <td mat-cell *matCellDef="let row">{{ row.SrNo}}</td>
                  </ng-container>
                  <!-- work Column -->
                  <ng-container matColumnDef="Work">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Work</th>
                    <td mat-cell *matCellDef="let row">{{ row.Work }}</td>
                  </ng-container>
  
                  <!-- Name Column -->
                  <ng-container matColumnDef="Name">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                    <td mat-cell *matCellDef="let row">
                      {{ row.Name }}
                    </td>
                  </ng-container>
  
                  <!-- ToDate Column -->
                  <ng-container matColumnDef="ToDate">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> To Date </th>
                    <td mat-cell *matCellDef="let row">{{ row.ToDate}}</td>
                  </ng-container>
                   

                  <ng-container matColumnDef="FromDate">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                        FromDate
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.FromDate }}</td>
                  </ng-container>

                  <ng-container matColumnDef="Quality">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                        Quality
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.Quality }}</td>
                  </ng-container>

                  <ng-container matColumnDef="Design">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                        Design
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.Design }}</td>
                  </ng-container>

                  <ng-container matColumnDef="Rate">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                        Rate
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.Rate }}</td>
                  </ng-container>
                  <!-- Action Column -->
                  <ng-container matColumnDef="Action">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Action
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.Action }}</td>
                  </ng-container>
  
                  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                  <tr
                    mat-row
                    *matRowDef="let row; columns: displayedColumns"
                  ></tr>
  
                  <!-- Row shown when there is no matching data. -->
                  <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" colspan="4">
                      No data matching the filter "{{ input.value }}"
                    </td>
                  </tr>
                </table>
  
                <mat-paginator
                  [pageSizeOptions]="[5, 10, 25, 100]"
                  aria-label="Select page of users"
                ></mat-paginator>
              </div>
              </div>
            </div>
          </div>
        </div>
      </fieldset>
    </section>
  </div>