import { Component, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { SweetalertService } from '../../../services/sweetalert.service';
import { MasterService } from '../../../services/master.service';

export interface UserData {
  id: string;
  index: number;
  name: string;
  toDate: string;
  fromDate: string;
  quality: string;
  design: string;
  area: string;
  calculate_area: string;
  rate: string;
}

const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-map-rate',
  templateUrl: './map-rate.component.html',
  styleUrl: './map-rate.component.css',
})
export class MapRateComponent {
  displayedColumns: string[] = [
    'id',
    'name',
    'toDate',
    'fromDate',
    'quality',
    'design',
    'area',
    'calculate_area',
    'rate',
    'action',
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);
  constructor(
    private service: MasterService,
    private alert: SweetalertService,
    private fb: FormBuilder,
    private customeService: CustomeServiceService
  ) {}

  frmMapRate!: FormGroup;
  editId!: string;
  isUpdated: boolean = false;
  ngOnInit(): void {
    this.frmMapRate = this.fb.group({
      name: [],
      toDate: [],
      fromDate: [],
      quality: [],
      design: [],
      area: [],
      calculate_area: [],
      rate: [],
    });
    this.getMapDesigner();
    this.getsMapMaster();
    this.getsMapRate();
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  areaList: any = ['Sq. Yard', 'Sq. Feet'];
  calculateAreaList: any = ['Quarter', 'Half', 'Full'];
  designerList: any = [];
  getMapDesigner() {
    this.service.getAllWeaverEmployee().subscribe({
      next: (value: any) => {
        debugger;
        this.designerList = value.filter(
          (x: any) => x.groupName === 'Map Designer'
        );
      },
    });
  }
  qualityList: any = [];
  designLists: any = [];
  getsMapMaster() {
    this.service.getsMapMaster().subscribe({
      next: (value: any) => {
        if (value) {
          value.map((v: any) => {
            this.qualityList.push({
              id: v.quality._id,
              quality: v.quality.quality,
            });
            this.designLists.push({
              id: v.addDesign._id,
              design: v.addDesign.design,
              quality: v.quality.quality,
              qId: v.quality._id,
            });
          });
        }
      },
    });
  }
  designList: any = [];
  design(id: string) {
    debugger;
    this.designList = this.designLists.filter((x: any) => x.qId === id);
  }
  getsMapRate() {
    this.service.getsMapRate().subscribe({
      next: (value: any) => {
        ELEMENT_DATA.length = 0;
        value.map((x: any, i: number) => {
          debugger;

          ELEMENT_DATA.push({
            id: x._id,
            index: i + 1,
            name: x.name?.name || 'N/A',
            toDate: this.customeService.convertDate(x.toDate),
            fromDate: this.customeService.convertDate(x.fromDate),
            quality: x.quality?.quality || 'N/A',
            design: x.design?.design || 'N/A',
            area: x.area,
            calculate_area: x.calculate_area,
            rate: x.rate,
          });
        });
        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        return;
      },
    });
  }
  addMapRate() {
    let formData = this.frmMapRate.value;
    this.service.addMapRate(formData).subscribe({
      next: (value) => {
        this.alert.success('success', 'Map rate has been saved');
        this.getsMapRate();
        this.clear();
      },
      error: (err) => {
        this.alert.error('warning', 'something went wrong');
      },
    });
  }
  update(){
    let formData = this.frmMapRate.value;
    this.service.updateMapRate(this.editId,formData).subscribe({
      next: (value) => {
        this.alert.success('success', 'Map rate has been updated');
        this.getsMapRate();
        this.clear();
      },
      error: (err) => {
        this.alert.error('warning', 'something went wrong');
      },
    });
  }
  edit(id: string) {
    this.service.getMapRate(id).subscribe({
      next: (value:any) => {
        this.editId = id;
        this.isUpdated = true;
        this.design(value.quality)
        this.frmMapRate.patchValue(value);
      },
    });
  }
  delete(id: string) {
    this.alert.confirm({}).then((result: any) => {
      if (result.isConfirmed) {
        this.service.deleteMapRate(id).subscribe({
          next: (value: any) => {
            this.alert.success('success', 'Record has been deleted ');
            this.getsMapRate();
          },
          error: (err) => {
            this.alert.error('warning', ' Something went wrong');
          },
        });
      }
    });
  }

  clear() {
    this.isUpdated=false;
    this.frmMapRate.reset();
  }
}
