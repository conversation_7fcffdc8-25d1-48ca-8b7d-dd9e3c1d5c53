const express = require('express');
const router = express.Router();
const sampleExcelReceivedController = require('../../controller/sampleExcelReceived-controller');


// sampleExcelReceived

// Create
router.post('/', sampleExcelReceivedController.createSampleExcelReceived);

// Read
router.get('/', sampleExcelReceivedController.getSampleExcelReceived);

// Update
router.put('/:id', sampleExcelReceivedController.updateSampleExcelReceived);

// Delete
router.delete('/:id', sampleExcelReceivedController.deleteSampleExcelReceived);

module.exports = router;
