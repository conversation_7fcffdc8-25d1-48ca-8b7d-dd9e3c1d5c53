/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},y={},b={}.propertyIsEnumerable,g=Object.getOwnPropertyDescriptor,m=g&&!b.call({1:2},1);y.f=m?function(t){var n=g(this,t);return!!n&&n.enumerable}:b;var h,d,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,x=T({}.toString),A=T("".slice),E=function(t){return A(x(t),8,-1)},C=E,F=P,M=function(t){if("Function"===C(t))return F(t)},I=c,R=E,D=Object,L=M("".split),N=I((function(){return!D("z").propertyIsEnumerable(0)}))?function(t){return"String"==R(t)?L(t,""):D(t)}:D,_=function(t){return null==t},k=_,z=TypeError,G=function(t){if(k(t))throw z("Can't call method on "+t);return t},q=N,B=G,H=function(t){return q(B(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},J=W.all,K=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===J}:function(t){return"function"==typeof t},Q=K,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,$=K,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=M({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(d=(h=ct.split("."))[0]>0&&h[0]<4?1:+(h[0]+h[1])),!d&&et&&(!(h=et.match(/Edge\/(\d+)/))||h[1]>=74)&&(h=et.match(/Chrome\/(\d+)/))&&(d=+h[1]);var at=d,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,yt=tt,bt=K,gt=nt,mt=Object,ht=pt?function(t){return"symbol"==typeof t}:function(t){var n=yt("Symbol");return bt(n)&&gt(n.prototype,mt(t))},dt=String,vt=K,St=function(t){try{return dt(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=_,Pt=p,Tt=K,xt=X,At=TypeError,Et={exports:{}},Ct=i,Ft=Object.defineProperty,Mt=function(t,n){try{Ft(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},It=Mt,Rt="__core-js_shared__",Dt=i[Rt]||It(Rt,{}),Lt=Dt;(Et.exports=function(t,n){return Lt[t]||(Lt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Nt=G,_t=Object,kt=function(t){return _t(Nt(t))},zt=kt,Gt=M({}.hasOwnProperty),qt=Object.hasOwn||function(t,n){return Gt(zt(t),n)},Bt=M,Ht=0,Ut=Math.random(),Wt=Bt(1..toString),Jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Ht+Ut,36)},Kt=i,Qt=Et.exports,Vt=qt,Xt=Jt,Yt=st,$t=pt,Zt=Qt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,cn=ht,an=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!xt(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!xt(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!xt(e=Pt(r,t)))return e;throw At("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},yn=ht,bn=function(t){var n=pn(t,"string");return yn(n)?n:n+""},gn=X,mn=i.document,hn=gn(mn)&&gn(mn.createElement),dn=function(t){return hn?mn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(dn("div"),"a",{get:function(){return 7}}).a})),Sn=a,wn=p,On=y,jn=v,Pn=H,Tn=bn,xn=qt,An=vn,En=Object.getOwnPropertyDescriptor;u.f=Sn?En:function(t,n){if(t=Pn(t),n=Tn(n),An)try{return En(t,n)}catch(t){}if(xn(t,n))return jn(!wn(On.f,t,n),t[n])};var Cn={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mn=X,In=String,Rn=TypeError,Dn=function(t){if(Mn(t))return t;throw Rn(In(t)+" is not an object")},Ln=a,Nn=vn,_n=Fn,kn=Dn,zn=bn,Gn=TypeError,qn=Object.defineProperty,Bn=Object.getOwnPropertyDescriptor,Hn="enumerable",Un="configurable",Wn="writable";Cn.f=Ln?_n?function(t,n,r){if(kn(t),n=zn(n),kn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=Bn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Hn in r?r.enumerable:e.enumerable,writable:!1})}return qn(t,n,r)}:qn:function(t,n,r){if(kn(t),n=zn(n),kn(r),Nn)try{return qn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Gn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Jn=Cn,Kn=v,Qn=a?function(t,n,r){return Jn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=a,Yn=qt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=K,er=Dt,or=M(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,yr=Jt,br=pr("keys"),gr={},mr=sr,hr=i,dr=X,vr=Qn,Sr=qt,wr=Dt,Or=function(t){return br[t]||(br[t]=yr(t))},jr=gr,Pr="Object already initialized",Tr=hr.TypeError,xr=hr.WeakMap;if(mr||wr.state){var Ar=wr.state||(wr.state=new xr);Ar.get=Ar.get,Ar.has=Ar.has,Ar.set=Ar.set,ir=function(t,n){if(Ar.has(t))throw Tr(Pr);return n.facade=t,Ar.set(t,n),n},ur=function(t){return Ar.get(t)||{}},cr=function(t){return Ar.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(Sr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return Sr(t,Er)?t[Er]:{}},cr=function(t){return Sr(t,Er)}}var Cr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!dr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=c,Mr=K,Ir=qt,Rr=a,Dr=nr.CONFIGURABLE,Lr=ar,Nr=Cr.enforce,_r=Cr.get,kr=Object.defineProperty,zr=Rr&&!Fr((function(){return 8!==kr((function(){}),"length",{value:8}).length})),Gr=String(String).split("String"),qr=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Ir(t,"name")||Dr&&t.name!==n)&&(Rr?kr(t,"name",{value:n,configurable:!0}):t.name=n),zr&&r&&Ir(r,"arity")&&t.length!==r.arity&&kr(t,"length",{value:r.arity});try{r&&Ir(r,"constructor")&&r.constructor?Rr&&kr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Nr(t);return Ir(e,"source")||(e.source=Gr.join("string"==typeof n?n:"")),t};Function.prototype.toString=qr((function(){return Mr(this)&&_r(this).source||Lr(this)}),"toString");var Br=K,Hr=Cn,Ur=Vn.exports,Wr=Mt,Jr={},Kr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Kr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=H,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=qt,le=H,se=ae.indexOf,pe=gr,ye=M([].push),be=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ye(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ye(i,r));return i},ge=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Jr.f=Object.getOwnPropertyNames||function(t){return be(t,ge)};var me={};me.f=Object.getOwnPropertySymbols;var he=tt,de=Jr,ve=me,Se=Dn,we=M([].concat),Oe=he("Reflect","ownKeys")||function(t){var n=de.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},je=qt,Pe=Oe,Te=u,xe=Cn,Ae=c,Ee=K,Ce=/#|\.prototype\./,Fe=function(t,n){var r=Ie[Me(t)];return r==De||r!=Re&&(Ee(n)?Ae(n):!!n)},Me=Fe.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Ie=Fe.data={},Re=Fe.NATIVE="N",De=Fe.POLYFILL="P",Le=Fe,Ne=i,_e=u.f,ke=Qn,ze=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Br(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Hr.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},Ge=Mt,qe=function(t,n,r){for(var e=Pe(n),o=xe.f,i=Te.f,u=0;u<e.length;u++){var c=e[u];je(t,c)||r&&je(r,c)||o(t,c,i(n,c))}},Be=Le,He=E,Ue=Array.isArray||function(t){return"Array"==He(t)},We=TypeError,Je=bn,Ke=Cn,Qe=v,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=K,$e=E,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=M,eo=c,oo=K,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),yo=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},bo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};bo.sham=!0;var go=!fo||eo((function(){var t;return yo(yo.call)||!yo(Object)||!yo((function(){t=!0}))||t}))?bo:yo,mo=Ue,ho=go,vo=X,So=en("species"),wo=Array,Oo=function(t){var n;return mo(t)&&(n=t.constructor,(ho(n)&&(n===wo||mo(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=c,Po=at,To=en("species"),xo=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Ne:f?Ne[c]||Ge(c,{}):(Ne[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=_e(r,e))&&u.value:r[e],!Be(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;qe(i,o)}(t.sham||o&&o.sham)&&ke(i,"sham",!0),ze(r,e,i,t)}},Ao=c,Eo=Ue,Co=X,Fo=kt,Mo=ee,Io=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Ro=function(t,n,r){var e=Je(n);e in t?Ke.f(t,e,Qe(0,r)):t[e]=r},Do=function(t,n){return new(Oo(t))(0===n?0:n)},Lo=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},No=at,_o=en("isConcatSpreadable"),ko=No>=51||!Ao((function(){var t=[];return t[_o]=!1,t.concat()[0]!==t})),zo=Lo("concat"),Go=function(t){if(!Co(t))return!1;var n=t[_o];return void 0!==n?!!n:Eo(t)};xo({target:"Array",proto:!0,arity:1,forced:!ko||!zo},{concat:function(t){var n,r,e,o,i,u=Fo(this),c=Do(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(Go(i=-1===n?u:arguments[n]))for(o=Mo(i),Io(a+o),r=0;r<o;r++,a++)r in i&&Ro(c,a,i[r]);else Io(a+1),Ro(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["hi-IN"]={formatCopyRows:function(){return"पंक्तियों की कॉपी करें"},formatPrint:function(){return"प्रिंट"},formatLoadingMessage:function(){return"लोड हो रहा है कृपया प्रतीक्षा करें"},formatRecordsPerPage:function(t){return"".concat(t," प्रति पृष्ठ पंक्तियाँ")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"".concat(t," - ").concat(n," पक्तिया ").concat(r," में से ( ").concat(e," पक्तिया)"):"".concat(t," - ").concat(n," पक्तिया ").concat(r," में से")},formatSRPaginationPreText:function(){return"पिछला पृष्ठ"},formatSRPaginationPageText:function(t){return"".concat(t," पृष्ठ पर")},formatSRPaginationNextText:function(){return"अगला पृष्ठ"},formatDetailPagination:function(t){return"".concat(t," पंक्तियां")},formatClearSearch:function(){return"सर्च क्लिअर करें"},formatSearch:function(){return"सर्च"},formatNoMatches:function(){return"मेल खाते रिकॉर्ड नही मिले"},formatPaginationSwitch:function(){return"छुपाओ/दिखाओ पृष्ठ संख्या"},formatPaginationSwitchDown:function(){return"दिखाओ पृष्ठ संख्या"},formatPaginationSwitchUp:function(){return"छुपाओ पृष्ठ संख्या"},formatRefresh:function(){return"रिफ्रेश"},formatToggleOn:function(){return"कार्ड दृश्य दिखाएं"},formatToggleOff:function(){return"कार्ड दृश्य छुपाएं"},formatColumns:function(){return"कॉलम"},formatColumnsToggleAll:function(){return"टॉगल आल"},formatFullscreen:function(){return"पूर्ण स्क्रीन"},formatAllRows:function(){return"सब"},formatAutoRefresh:function(){return"ऑटो रिफ्रेश"},formatExport:function(){return"एक्सपोर्ट डाटा"},formatJumpTo:function(){return"जाओ"},formatAdvancedSearch:function(){return"एडवांस सर्च"},formatAdvancedCloseButton:function(){return"बंद करे"},formatFilterControlSwitch:function(){return"छुपाओ/दिखाओ कंट्रोल्स"},formatFilterControlSwitchHide:function(){return"छुपाओ कंट्रोल्स"},formatFilterControlSwitchShow:function(){return"दिखाओ कंट्रोल्स"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["hi-IN"])}));
