import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-challan-modal',
  templateUrl: './challan-modal.component.html',
  styleUrl: './challan-modal.component.css'
})
export class ChallanModalComponent implements OnInit {
  challanDetails: any[] = [];
  displayedColumns: string[] = ['issueNo', 'challanNo', 'cDate', 'katiIssued', 'tanaIssued', 'sootIssued', 'tharriIssued', 'silkIssued', 'otherIssued'];

  constructor(
    public dialogRef: MatDialogRef<ChallanModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    console.log('🔍 Challan Modal Data:', this.data);
    this.processChallanDetails();
  }

  processChallanDetails(): void {
    if (this.data && this.data.challanDetails) {
      this.challanDetails = this.data.challanDetails.map((challan: any) => ({
        issueNo: this.data.issueNo || 'N/A',
        challanNo: challan.challanNo || 'N/A',
        cDate: challan.date || new Date(),
        katiIssued: challan.materials?.kati?.issue || 0,
        tanaIssued: challan.materials?.tana?.issue || 0,
        sootIssued: challan.materials?.soot?.issue || 0,
        tharriIssued: challan.materials?.thari?.issue || 0,
        silkIssued: challan.materials?.silk?.issue || 0,
        otherIssued: challan.materials?.other?.issue || 0
      }));
    }
    console.log('✅ Processed challan details:', this.challanDetails);
  }
}
