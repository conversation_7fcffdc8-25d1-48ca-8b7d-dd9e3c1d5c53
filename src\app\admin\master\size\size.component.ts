import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MasterService } from '../../../services/master.service';
import Swal from 'sweetalert2';
import { environment } from '../../../../environments/environment.development';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
export interface PeriodicElement {
  id: string;
  index: number;
  sizeInYard: string;
  areaInYard: string;
  areaInfeet: string;
  khapSize: string;
  sizeinMeter: string;
  sqMeter: string;
  srNo: string;
  uploadedFile: string;
}

const ELEMENT_DATA: PeriodicElement[] = [];
@Component({
  selector: 'app-size',
  templateUrl: './size.component.html',
  styleUrl: './size.component.css',
})
export class SizeComponent implements OnInit {
  displayedColumns: string[] = [
    'id',
    'sizeInYard',
    'areaInYard',
    'areaInfeet',
    'khapSize',
    'sizeinMeter',
    'sqMeter',
    'srNo',
    'uploadedFile',
    'action',
  ];
  dataSource = new MatTableDataSource(ELEMENT_DATA);
  sizeForm!: FormGroup;
  data: any;
  editId: any;
  isEditMode = false;
  file: any;
  constructor(
    public _fb: FormBuilder,
    public masterservice: MasterService,
    private sanitizer: DomSanitizer
  ) {}
  sizeFormSubmit() {
    let frmData = this.sizeForm.value;
    let formData = new FormData();

    formData.append('sizeInYard', frmData.sizeInYard);
    formData.append('areaInYard', frmData.areaInYard);
    formData.append('areaInfeet', frmData.areaInfeet);
    formData.append('khapSize', frmData.khapSize);
    formData.append('sizeinMeter', frmData.sizeinMeter);
    formData.append('sqMeter', frmData.sqMeter);
    formData.append('srNo', frmData.srNo);
    formData.append('uploadFile', this.file);

    debugger;
    if (!this.isUpdated && !this.editItemId) {
      this.masterservice.addSizes(formData).subscribe({
        next: (res) => {
          Swal.fire({
            title: 'Success!',
            text: 'Size added successfuly!',
            icon: 'success',
          });
          this.getAllSizesList();
          this.sizeForm.reset();
        },
        error: console.log,
      });
    } else {
      if (this.editItemId) {
        this.masterservice.updateSizes(this.editItemId, formData).subscribe({
          next: (value) => {
            if (value) {
              Swal.fire({
                title: 'Success!',
                text: 'Size updated successfuly!',
                icon: 'success',
              });
              this.getAllSizesList();
              this.sizeForm.reset();
            }
          },
          error(err) {
            Swal.fire({
              title: 'Success!',
              text: err,
              icon: 'success',
            });
          },
        });
      } else {
        console.log('undefined');
      }
    }
  }

  // getAllSizesList() {
  //   this.masterservice.getAllSizesList().subscribe({
  //     next: (res) => {
  //       ELEMENT_DATA.length = 0;
  //       res.map((v: any, i: number) => {
  //         debugger;

  //         const imageUrl = environment.imageUrl + v.uploadedFile;
  //         let img: any = this.getSafeUrl(imageUrl);
  //         ELEMENT_DATA.push({
  //           id: v._id,
  //           index: i + 1,

  //           sizeInYard: v.sizeInYard,
  //           areaInYard: v.areaInYard,
  //           areaInfeet: v.areaInfeet,
  //           khapSize: v.khapSize,
  //           sizeinMeter: v.sizeinMeter,
  //           sqMeter: v.sqMeter,
  //           srNo: v.srNo,
  //           uploadedFile: img,
  //         });
  //       });
  //       this.dataSource = new MatTableDataSource(ELEMENT_DATA);
  //       this.dataSource.paginator = this.paginator;
  //       this.dataSource.sort = this.sort;
  //       return;
  //     },
  //     error: console.log,
  //   });
  // }
  getAllSizesList() {
  this.masterservice.getAllSizesList().subscribe({
    next: (res) => {
      ELEMENT_DATA.length = 0;

      // First sort the data
      const sorted = res.sort((a: any, b: any) => Number(a.srNo) - Number(b.srNo));

      // Then map and push to ELEMENT_DATA
      sorted.map((v: any, i: number) => {
        const imageUrl = environment.imageUrl + v.uploadedFile;
        let img: any = this.getSafeUrl(imageUrl);

        ELEMENT_DATA.push({
          id: v._id,
          index: i + 1,
          sizeInYard: v.sizeInYard,
          areaInYard: v.areaInYard,
          areaInfeet: v.areaInfeet,
          khapSize: v.khapSize,
          sizeinMeter: v.sizeinMeter,
          sqMeter: v.sqMeter,
          srNo: v.srNo,
          uploadedFile: img,
        });
      });

      // Assign to dataSource
      this.dataSource = new MatTableDataSource(ELEMENT_DATA);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    },
    error: console.log,
  });
}

  getSafeUrl(url: string): SafeUrl {
    return this.sanitizer.bypassSecurityTrustUrl(url);
  }

  ngOnInit(): void {
    this.sizeForm = this._fb.group({
      sizeInYard: [''],
      areaInYard: [''],
      areaInfeet: [''],
      khapSize: [''],
      sizeinMeter: [''],
      sqMeter: [''],
      srNo: [''],
    });
    this.getAllSizesList();
  }
  isUpdated: boolean = false;
  editItemId!: string;
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  setSizeYard() {
    debugger;
    const formData = this.sizeForm.value;

    const calcData = formData.sizeInYard.split(/[Xx]/);

    const width1 = calcData[0].split('.')[0];
    const width2 = calcData[0].split('.')[1];

    console.log('before ', width1, 'after ', width2);

    console.log('cal',calcData);

    const length1 = calcData[1].split('.')[0];
    const length2 = calcData[1].split('.')[1];

    const sizeInYaardCalc1 = parseInt(width1) * 12 + parseInt(width2);

    const sizeInYaardCalc2 = parseInt(length1) * 12 + parseInt(length2);
    console.log(sizeInYaardCalc1);
    console.log(sizeInYaardCalc2);

    const totalSizeInYaard = (sizeInYaardCalc1 * sizeInYaardCalc2) / 1296;
    const feet = (sizeInYaardCalc1 * sizeInYaardCalc2) / 144;

    console.log(totalSizeInYaard);
    this.sizeForm.get('areaInYard')?.setValue(totalSizeInYaard.toFixed(2));

    this.sizeForm.get('areaInfeet')?.setValue(feet.toFixed(2));
    console.log(this.sizeForm.value)
  }

  setSizeMeter() {
    const formData = this.sizeForm.value;
    let parts = formData.sizeinMeter.split(/[Xx]/);
    let sizeMeterWidth = parts[0];
    let sizeMeterLength = parts[1];

    const totalSizeInMeter = (sizeMeterWidth * sizeMeterLength) / 10000;
    console.log('size in meter  ', sizeMeterWidth);

    console.log('size in meter  ', sizeMeterLength);
    console.log('totalSizeInMeter  ', totalSizeInMeter);

    this.sizeForm.get('sqMeter')?.setValue(totalSizeInMeter.toFixed(2));
  }

  edit(data: any) {
    this.isUpdated = true;
    this.editItemId = data.id;
    this.sizeForm.patchValue(data);
    debugger;
  }
  deleteSize(id: string) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        this.masterservice.deleteSizes(id).subscribe({
          next:(value) =>{
            Swal.fire({
              title: 'Deleted!',
              text: 'Your size has been deleted.',
              icon: 'success',
            });
            this.getAllSizesList();
          },
          error(err) {
            debugger
            Swal.fire({
              title: 'Deleted!',
              text: err,
              icon: 'warning',
            });
          },
        });

      }
    });
  }

  upload(file: any) {
    this.file = file.target.files[0];
  }

  readPdf(imageUrl: any) {
    debugger;
    window.open(imageUrl.changingThisBreaksApplicationSecurity, '_blank');
  }
}
