// const mongoose = require('mongoose');

// const excelSchema = new mongoose.Schema({
//     Date:{
//         type:String
//     },
//     BaleNo:{
//         type: String
//     },
//     PcsNo:{
//         type: String
//     },
//     Quality:{
//         type: String
//     },
//     Design:{
//         type: String
//     },
//     Colour:{
//         type: String
//     },
//     Width:{
//         type: String
//     },
//     Length:{
//         type: String
//     },
//     Area:{
//         type:String
//     },
//     InvoiceNo:{
//         type: String
//     },
//     ImpoterCode:{
//         type:String
//     },
//     Remark:{
//         type:String
//     }


// });

// const PackageListExcel= mongoose.model('PackageListExcel', excelSchema);

// module.exports = PackageListExcel;
const mongoose = require('mongoose');

const excelSchema = new mongoose.Schema({
    Date: {
        type: String
    },
    BaleNo: {
        type: String
    },
    PcsNo: {
        type: String
    },
    Quality: {
        type: String
    },
    Design: {
        type: String
    },
    Colour: {
        type: String
    },
    Width: {
        type: String
    },
    Length: {
        type: String
    },
    Area: {
        type: String
    },
    InvoiceNo: {
        type: String
    },
    ImpoterCode: {
        type: String
    },
    Remark: {
        type: String
    },
    ImpoterNo: { // Added ImpoterNo field
        type: String
    },
    containerNo: { type: String },
    impoterName:
    {
        type:String
    }
});

const PackageListExcel = mongoose.model('PackageListExcel', excelSchema);

module.exports = PackageListExcel;
