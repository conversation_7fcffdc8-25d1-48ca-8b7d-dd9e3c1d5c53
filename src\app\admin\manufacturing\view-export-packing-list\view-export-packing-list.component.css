
fieldset {
  font-family: sans-serif;
  border: 2px solid #1F497D;
  background: #ffffff;
  border-radius: 5px;
  padding: 10px;
}

fieldset legend {
  background: #ffffff;
  color: #000000;
  padding: 5px 10px ;
  font-size: 20px;
  border-radius: 5px;
  /* box-shadow: 0 0 0 5px #ddd; */
  margin-left: 20px;
}

legend {
  float: left;
  width: auto;
  padding: 0;
  margin-top: -32px;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + .3vw);
  line-height: inherit;
}
.ex-width{
  width: 100%;
}
/* .issue{

} */
::ng-deep .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper {
  padding-right: 0 !important;
  padding-left: 6.25px !important;
}
::ng-deep .mat-mdc-icon-button.mat-mdc-button-base {
  --mdc-icon-button-state-layer-size: 37px !important;
  width: var(--mdc-icon-button-state-layer-size);
  height: var(--mdc-icon-button-state-layer-size);
  padding: 2px !important;
}

.save-m{
  margin-right: 65px;
}

/* Material table styles */
.mat-elevation-z8 {
  box-shadow: 0 5px 5px -3px rgba(0,0,0,.2), 0 8px 10px 1px rgba(0,0,0,.14), 0 3px 14px 2px rgba(0,0,0,.12);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 20px;
}

table.mat-table {
  width: 100%;
}

.mat-mdc-header-cell {
  background-color: #f5f5f5;
  color: #1F497D;
  font-weight: bold;
  font-size: 14px;
}

.mat-mdc-row:nth-child(even) {
  background-color: #f9f9f9;
}

.mat-mdc-row:hover {
  background-color: #f0f0f0;
}

/* Add a subtle border between rows */
.mat-mdc-row {
  border-bottom: 1px solid #e0e0e0;
}

/* Style for the paginator */
.mat-mdc-paginator {
  background-color: #f5f5f5;
}


.SrNo-Tw{
  width: 40px !important;
}

.baleNo-Tw{
  width: 100px !important;
}

.pcsNo-Tw{
  width: 100px !important;
}

.size-Tw{
  width: 135px !important;
}



/* .quality-w{
  width: 120px;
}

.design-w{
  width: 110px;
}

.colour-w{
  width: 120px;
} 
.area-Tw{
  width: 70px;
}

.tArea-w{
  width: 120px;
}

.remarks-w{
  width: 150px;
} */
