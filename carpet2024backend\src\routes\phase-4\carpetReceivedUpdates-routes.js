const express = require("express");
const carpetReceivedUpdatesController = require("../../controller/manifacturing/carpetReceivedUpdates-controller");

const router = express.Router();

// Get all carpet received updates
router.get("/carpetReceivedUpdates", carpetReceivedUpdatesController.getAllCarpetReceivedUpdates);

// Create a new update
router.post("/carpetReceivedUpdates", carpetReceivedUpdatesController.createCarpetReceivedUpdate);

// Get all updates for a carpet
router.get("/carpetReceivedUpdates/:carpetReceivedId", carpetReceivedUpdatesController.getUpdatesByCarpetReceivedId);

// Get history for a specific carpet and update type
router.get("/carpetReceivedUpdates/:carpetReceivedId/history/:updateType", carpetReceivedUpdatesController.getHistoryByCarpetAndType);

// Add a border size history entry
router.post("/carpetReceivedUpdates/:carpetReceivedId/border-size-history", carpetReceivedUpdatesController.addCarpetBorderSizeHistory);

// Get all border size history entries for a carpet
router.get("/carpetReceivedUpdates/:carpetReceivedId/border-size-history", carpetReceivedUpdatesController.getCarpetBorderSizeHistory);

// Utility route to clean up all records (admin only)
router.post("/carpetReceivedUpdates/cleanup", carpetReceivedUpdatesController.cleanupAllRecords);

module.exports = router;