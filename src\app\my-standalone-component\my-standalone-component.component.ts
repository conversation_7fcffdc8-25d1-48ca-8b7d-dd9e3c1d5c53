import { Component, OnInit } from '@angular/core';
import { ImporterService } from '../services/importer.service';
import { CustomeServiceService } from '../services/custome-service.service';
import { ActivatedRoute } from '@angular/router';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { firstValueFrom } from 'rxjs';
import { SweetalertService } from '../services/sweetalert.service';

@Component({
  selector: 'app-my-standalone-component',
  templateUrl: './my-standalone-component.component.html',
  styleUrl: './my-standalone-component.component.css'
})
export class MyStandaloneComponentComponent implements OnInit{
  constructor(
    private _service: ImporterService,
    private customeService: CustomeServiceService,
    private activeRoute: ActivatedRoute,
    private ngxLoader: NgxUiLoaderService,
    private pdfService: SweetalertService
  ) {

  }
  allBills: any;
  billId: string = '';
  _isChallan: boolean = false;
  ngOnInit(): void {

    // this.ngxLoader.start();
    this.billId = this.activeRoute.snapshot.paramMap.get('id') || '';
    this.initializeData(this.billId);
  }

  initializeData(id: string) {
    // this.ngxLoader.start();
    Promise.all([
      this.getBills(),
      this.getChallans(),
      this.getContainerReceived(),
      this.getImporterName(),
    ]).then(()=>{
      let _bill = id.split(' ');
      if (_bill[1] === 'print') {
        this.viewBill(_bill[0]);
      } else {
        // this.viewChallan(_bill[0]);
      }
       this.ngxLoader.stop()
    }).catch((error)=>{console.log('something went wrong')})


  }


 async getBills() :Promise<any>{
  const resp = await firstValueFrom(this._service.getsBill());
  this.allBills = resp;
}
allChallans: any;
allContainerStock: any;



async getChallans(): Promise<void> {
  const resp = await firstValueFrom(this._service.getAllChallan());
  this.allChallans = resp;
}

async getContainerReceived(): Promise<void> {
  const resp = await firstValueFrom(this._service.getAllContainerRecieved());
  this.allContainerStock = resp;
}
allImporterDetails:any;
async getImporterName(){
  const resp = await firstValueFrom(this._service.getsWholesalerList());
  this.allImporterDetails = resp;
}
billDetails: any = [];
_bill: any = {};
viewBill(id: any) {
  let pcs: number = 0;
  let amount: number = 0;
  let area: number = 0;
  let profit: number = 0;
  let gst: number = 0;
  this.ngxLoader.start();
  let getChallans = this.allBills.find((x: any) => x._id === id.toString());

  let getImporter = this.allImporterDetails.find((name:any)=> name.customerName===getChallans.wholesellerName)

  console.log(getImporter);
  let getDate =this.customeService.convertDate(getChallans.chooseAdate)
  let getDate2 = getDate.split('.');
    this._bill = {
    billNo: getChallans.billNo,
    date: getDate,
    customer: getChallans.wholesellerName,
    street:getImporter.address,
    // city:getImporter.impoter.address.city,
    // state:getImporter.impoter.address.state,
    zipCode:getImporter.zipCode,
    country:getImporter.country,
    date2:'01.'+getDate2[1]+'.'+getDate2[2]
  };
  debugger
  getChallans.challanNo.forEach((element: any) => {



    let getChallanDetails = this.allChallans.find(
      (x: any) => x.challanNo === element.challanNumber
    );
    if (!getChallanDetails || !getChallanDetails.carpetList) {
      console.error(`Challan data or carpet list is undefined for challan number`);
      return; // Skip to the next iteration if carpetList is undefined
    }
    getChallanDetails.carpetList.forEach((elem: any) => {
      // Find the item details in allContainerStock
      let matchedContainers = this.allContainerStock.filter(
        (container: any) =>
          container.containerItem.some(
            (item: any) => parseInt(item.GerCarpetNo) === elem.barcodeNo && elem.isDeleted!=true
          )
      );
      matchedContainers.forEach((container: any) => {
        container.containerItem.forEach((item: any) => {
          if (parseInt(item.GerCarpetNo) === elem.barcodeNo) {


pcs = 0;
amount = 0;
area = 0;
profit = 0;
gst = 0;

              const carpetArea = parseFloat(
                elem.area ? elem.area : item.Area
              );
              const carpetPrice = parseFloat(
                elem.evkPrice ? elem.evkPrice : item.EvKPrice
              );
              const carpetAmount = carpetArea * carpetPrice;
              pcs++;

              if (elem.status != 'return') {
                area += carpetArea;
                amount += carpetAmount;
              } else {
                area -= Math.abs(carpetArea);
                amount -= Math.abs(carpetAmount);
              }



            let date = this.customeService.convertDate(
              getChallanDetails.chooseAdate
            );
            const includeChallanDetails = this.billDetails.some(
              (a: any) => a.challanNo == getChallanDetails.challanNo
            );



            this.billDetails.push({
              challanNo: includeChallanDetails
                ? undefined
                : getChallanDetails.challanNo,
              challanDate: includeChallanDetails ? undefined : date,
              customer: includeChallanDetails
                ? undefined
                : getChallanDetails.retailerOutlet,
              carpetNo: item.GerCarpetNo,
              qualityDesign: item.QualityDesign,
              colour: item.Color,
              colourCode: item.CCode,
              qualityCode: item.QCode,
              size: elem.size ? elem.size : item.Size,
              // area: elem.area ? elem.area : item.Area,
              area:area,
              evkPrice: elem.evkPrice ? elem.evkPrice : item.EvKPrice,
              // amount:
              //   parseFloat(elem.evkPrice ? elem.evkPrice : item.EvKPrice) *
              //   parseFloat(elem.area ? elem.area : item.Area),
              amount:amount,
              invoiceNo: item.InvoiceNo,
              saleStatus: item.status,
            });
          }
        });
      });
    });
  });

  this.calculation(this.billDetails);
  console.log(this.billDetails);
  this.ngxLoader.stop();
}

totalCalculation = {
  totalAmount: 0,
  // totalArea: 0,
  // totalEvkPrice: 0,
  profit:0,
  grossAmt:0,
  gstAmt:0
};
 total=0;
 profit=0;
 gst=0;
calculation(data: any) {

  data.forEach((element: any) => {
    this.total =this.total + parseFloat(element.area) * parseFloat(element.evkPrice)
  });
  this.profit = this.total/100*13;
    this.gst=( this.total +this.profit) /100*19;
    this.totalCalculation = {
      totalAmount:this.total,
      profit:this.profit,
      gstAmt:this.gst,
      grossAmt:this.total +this.profit+this.gst
    };
  console.log(this.totalCalculation);

  ;
}
isPrint:boolean=false;
public generatePDF(): void {
  // this.isPrint=true
  window.print();
  // this.pdfService.generatePDF('contentToConvert');
}







// -------------------------------------------



}
