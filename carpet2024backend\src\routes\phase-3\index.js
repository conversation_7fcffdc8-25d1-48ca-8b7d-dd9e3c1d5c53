const express = require('express');

const router = express.Router();
const buyerRouter=require('../phase-3/masterBuyer-routes');
const weaver_employee = require('../phase-3/weaver_employee-routes');
const sizeMaster = require('../phase-3/sizeMaster-routes');
const rawMaterial = require('../phase-3/rawMaterial-routes');
const purchaserDetails=require('../phase-3/purchaserDetails-routes');
const mapMaster = require('../phase-3/mapMaster-routes');
const colourDeying = require('../phase-3/colourDeying-routes');
const deyingRate = require('../phase-3/deyingRate-routes');
const mapRate = require('../phase-3/mapRate-routes');
const finishingHead = require('../phase-3/finishingHead-routes');
const branch = require('../phase-3/branch-routes');
const materialLagat= require('../phase-3/materialLagat-routes');



router.use ('/materialLagat',materialLagat);
router.use('/branch',branch);
router.use('/finishingHead',finishingHead);
router.use('/mapRate',mapRate);
router.use('/deyingRate',deyingRate);
router.use('/colourcodeDeying',colourDeying);
router.use('/mapMaster',mapMaster);
router.use('/purchaserDetails',purchaserDetails);
router.use('/buyer',buyerRouter);//done
router.use('/weaver',weaver_employee);
router.use('/sizeMaster',sizeMaster);
router.use('/rawMaterial',rawMaterial);

module.exports = router;
