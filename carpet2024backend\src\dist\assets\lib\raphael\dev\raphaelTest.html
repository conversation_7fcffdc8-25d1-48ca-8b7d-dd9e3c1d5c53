<!DOCTYPE html>
<head>
    <meta content="text/html;charset=utf-8" http-equiv="Content-Type">
    <meta content="utf-8" http-equiv="encoding">
    <title><PERSON> Dev testing html</title>

    <!-- HTML to try new developments in Raphael -->

    <!-- Global use -->

    <!-- To work with full version -->
    <script type="text/javascript" src="../bower_components/requirejs/require.js"></script>
    <script type="text/javascript" src="../bower_components/eve/eve.js"></script>

    <!-- To work with minified version -->
    <!--<script type="text/javascript" src="../raphael-min.js"></script>-->

    <!-- Comment this script tag if you are testing with AMD -->
    <script type="text/javascript">
        // Initialize container when document is loaded
        window.onload = function () {
            require(['../raphael'], function(<PERSON>) {
                var paper = Raphael("container", 640, 720);
                paper.circle(100, 100, 100).attr({'fill':'270-#FAE56B:0-#E56B6B:100'}); //example
            });
        };
        //Work here, in a separate script file or via command line
    </script>

    <!-- Use amdDev.js to work with AMD and Raphael -->
    <!-- You need to do a 'bower install -D' first to get requirejs -->
    

</head>
<body>
<!-- Container for svg/vml root element -->
<div id="container"></div>
</body>
</html>
