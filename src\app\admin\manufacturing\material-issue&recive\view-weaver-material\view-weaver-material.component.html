<div class="container mt-4">
  <section>
    <fieldset>
      <legend><b>View Material Issues</b></legend>

      <!-- Filters Section -->
      <form [formGroup]="filterForm" class="mb-4">
        <div class="row">
         
             <!-- Search Filter -->
          <div class="search-width">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Search</mat-label>
              <input matInput (keyup)="applyFilter($event)" placeholder="Search in table...">
            </mat-form-field>
          </div>

          <div class="col-md-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Weaver</mat-label>
              <mat-select formControlName="weaver">
                <mat-option value="">All Weavers</mat-option>
                <mat-option *ngFor="let weaver of weaverList" [value]="weaver.name">
                  {{weaver.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="issues-width">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Issue No</mat-label>
              <mat-select formControlName="issueNo">
                <mat-option value="">All Issues</mat-option>
                <mat-option *ngFor="let issue of issueList" [value]="issue.Br_issueNo">
                  {{issue.Br_issueNo}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="fromDate-width">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>From Date</mat-label>
              <input matInput [matDatepicker]="fromPicker" formControlName="fromDate">
              <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
              <mat-datepicker #fromPicker></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="toDate-width">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>To Date</mat-label>
              <input matInput [matDatepicker]="toPicker" formControlName="toDate">
              <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
              <mat-datepicker #toPicker></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="clearB-width">
            <button mat-raised-button color="warn" (click)="clearFilters()" class="w-100 mt-2">
              Clear Filters
            </button>
          </div>
        </div>

       
      </form>

      <!-- Table Section -->
      <div class="row">
        <div class="col-md-12 scroll-container" style="overflow: auto;">
          <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8" style="width: max-content;">

            <!-- Sr.No Column -->
            <ng-container matColumnDef="srNo">
              <th mat-header-cell *matHeaderCellDef>Sr.No</th>
              <td mat-cell *matCellDef="let element">{{element.srNo}}</td>
            </ng-container>

            <!-- Weaver Column -->
            <ng-container matColumnDef="weaver">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Weaver</th>
              <td mat-cell *matCellDef="let element">{{element.weaver}}</td>
            </ng-container>

            <!-- Issue No Column -->
            <ng-container matColumnDef="issueNo">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Issue No</th>
              <td mat-cell *matCellDef="let element">{{element.issueNo}}</td>
            </ng-container>

            <!-- Challan No Column -->
            <ng-container matColumnDef="challanNo">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Challan No</th>
              <td mat-cell *matCellDef="let element">{{element.challanNo}}</td>
            </ng-container>

            <!-- Date Column -->
            <ng-container matColumnDef="date">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
              <td mat-cell *matCellDef="let element">{{element.date}}</td>
            </ng-container>

            <!-- Quality Column -->
            <ng-container matColumnDef="quality">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
              <td mat-cell *matCellDef="let element">{{element.quality}}</td>
            </ng-container>

            <!-- Design Column -->
            <ng-container matColumnDef="design">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Design</th>
              <td mat-cell *matCellDef="let element">{{element.design}}</td>
            </ng-container>

            <!-- Colour Column -->
            <ng-container matColumnDef="colour">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Colour</th>
              <td mat-cell *matCellDef="let element">{{element.colour}}</td>
            </ng-container>

            <!-- Size Column -->
            <ng-container matColumnDef="size">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Size</th>
              <td mat-cell *matCellDef="let element">{{element.size}}</td>
            </ng-container>

            <!-- Pcs Column -->
            <ng-container matColumnDef="pcs">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Pcs</th>
              <td mat-cell *matCellDef="let element">{{element.pcs}}</td>
            </ng-container>

            <!-- Area Column -->
            <ng-container matColumnDef="area">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Area</th>
              <td mat-cell *matCellDef="let element">{{element.area}}</td>
            </ng-container>

            <!-- Kati Column -->
            <ng-container matColumnDef="kati">
              <th mat-header-cell *matHeaderCellDef>Kati</th>
              <td mat-cell *matCellDef="let element">{{element.kati}}</td>
            </ng-container>

            <!-- Tana Column -->
            <ng-container matColumnDef="tana">
              <th mat-header-cell *matHeaderCellDef>Tana</th>
              <td mat-cell *matCellDef="let element">{{element.tana}}</td>
            </ng-container>

            <!-- Soot Column -->
            <ng-container matColumnDef="soot">
              <th mat-header-cell *matHeaderCellDef>Soot</th>
              <td mat-cell *matCellDef="let element">{{element.soot}}</td>
            </ng-container>

            <!-- Thari Column -->
            <ng-container matColumnDef="thari">
              <th mat-header-cell *matHeaderCellDef>Thari</th>
              <td mat-cell *matCellDef="let element">{{element.thari}}</td>
            </ng-container>

            <!-- Silk Column -->
            <ng-container matColumnDef="silk">
              <th mat-header-cell *matHeaderCellDef>Silk</th>
              <td mat-cell *matCellDef="let element">{{element.silk}}</td>
            </ng-container>

            <!-- Action Column -->
            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef>Action</th>
              <td mat-cell *matCellDef="let element">
                <button mat-icon-button color="primary" (click)="editMaterialIssue(element._id)"
                        matTooltip="Edit">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" (click)="deleteMaterialIssue(element._id)"
                        matTooltip="Delete">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

            <!-- No Data Row -->
            <tr class="mat-row" *matNoDataRow>
              <td class="mat-cell" colspan="17" style="text-align: center; padding: 20px;">
                No data matching the filter
              </td>
            </tr>
          </table>
        </div>

      <!-- Paginator -->
      <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]"aria-label="Select page of material issues"></mat-paginator>
</div>
    </fieldset>
  </section>
</div>


