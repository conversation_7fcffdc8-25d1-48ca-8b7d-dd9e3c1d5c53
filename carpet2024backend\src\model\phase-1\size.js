const mongoose = require('mongoose');

// Enum for unit of measurement
const UnitEnum = ['cm', 'ft'];

const sizeSchema = new mongoose.Schema({
  size: {
    type: String,
    required: true,
  },
  unit: {
    type: String,
    
    // required: true
  },
  sizeCode: {
    type: String,
    required: true,
  },
  khapSize: {
    type: String,
  },
  orderSize: {
    type: String,
  },
  sequenceCode: {
    type: Number,
    required: true,
    unique:true
  },
  area:{
    type:Number
  }
});

const Size = mongoose.model('Size', sizeSchema);

module.exports = Size;
