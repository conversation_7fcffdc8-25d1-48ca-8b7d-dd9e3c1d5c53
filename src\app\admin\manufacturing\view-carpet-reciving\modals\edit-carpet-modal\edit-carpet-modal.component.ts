import { Component, Inject, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-edit-carpet-modal',
  templateUrl: './edit-carpet-modal.component.html',
  styleUrls: ['./edit-carpet-modal.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule
  ]
})
export class EditCarpetModalComponent implements OnInit, AfterViewInit {
  @ViewChild('sizeInput') sizeInput!: ElementRef;
  @ViewChild('deductionInput') deductionInput!: ElementRef;
  @ViewChild('saveButton') saveButton!: ElementRef;
  numericArea: number = 0;

  constructor(
    public dialogRef: MatDialogRef<EditCarpetModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    console.log('Modal data received:', this.data);

    // Ensure all required fields exist with proper types
    this.data.Pcs = this.data.Pcs;
    this.data.AreaIn = this.data.AreaIn || 'Sq.Feet';
    this.data.Rate = this.parseFloat(this.data.Rate);
    // Initialize Deduction as empty string to show placeholder
    this.data.Deduction = this.data.Deduction === '0.00' || this.data.Deduction === 0 ? '' : this.data.Deduction;
    this.data.TDS_percent = this.parseFloat(this.data.TDS_percent);
    this.data.Commission_percent = this.parseFloat(this.data.Commission_percent);

    this.calculateArea(); // Initial calculation
  }  ngOnInit() {
    // Initial setup if needed
  }
  ngAfterViewInit() {
    // Focus on the size input field after view is initialized
    requestAnimationFrame(() => {
      if (this.sizeInput) {
        this.sizeInput.nativeElement.focus();
        this.sizeInput.nativeElement.select(); // This will select all text if there's any
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (!this.showValidationErrors()) {
      return; // Stop execution if validation fails
    }

    this.calculateArea(); // Ensure calculations are up to date

    // Show success message
    Swal.fire({
      title: 'Success!',
      text: 'Carpet data updated successfully',
      icon: 'success',
      confirmButtonText: 'OK'
    }).then(() => {
      this.dialogRef.close(this.data);
    });
  }

  showValidationErrors(): boolean {
    let errorFields: string[] = [];

    // Check required fields
    if (!this.data.Size || this.data.Size.toString().trim() === '') {
      errorFields.push('Size');
    }
    if (!this.data.Rate || this.parseFloat(this.data.Rate) <= 0) {
      errorFields.push('Rate');
    }

    if (errorFields.length > 0) {
      Swal.fire({
        title: 'Validation Error',
        text: `Please fill the following fields: ${errorFields.join(', ')}`,
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return false;  // Stop further execution
    }
    return true;  // Proceed if valid
  }

  onSizeEnter(): void {
    // Move focus to deduction field when Enter is pressed on Size field
    if (this.deductionInput) {
      this.deductionInput.nativeElement.focus();
      this.deductionInput.nativeElement.select();
    }
  }

  onDeductionEnter(): void {
    // Move focus to save button when Enter is pressed on Deduction field
    if (this.saveButton) {
      this.saveButton.nativeElement.focus();
    }
  }

  onSaveEnter(): void {
    // Trigger save when Enter is pressed on Save button
    this.onSave();
  }

  debugCalculations(): void {
    console.log('Debug calculations triggered');
    console.log('Current data:', JSON.stringify(this.data, null, 2));

    // Force recalculation
    this.calculateArea();

    // Show alert with calculation results
    alert(`Calculation Results:
Size: ${this.data.Size}
Area: ${this.data.Area}
Rate: ${this.data.Rate}
Amount: ${this.data.Amount}
TDS: ${this.data.TDS}
Commission: ${this.data.Commission}
Net Amount: ${this.data.NetAmount}`);
  }

  parseFloat(value: string | number): number {
    return parseFloat((value || '0').toString());
  }

  onDeductionBlur(): void {
    const val = this.data.Deduction;

    if (val === null || val === undefined || val === '') {
      this.data.Deduction = '0.00'; // Default to 0.00 if empty
      this.calculateAmounts();
      return;
    }

    const deduction = this.parseFloat(val);
    this.data.Deduction = deduction.toFixed(2);
    this.calculateAmounts();
  }

  calculateArea(): void {
    this.numericArea = 0;
    console.log('Calculating area for Size:', this.data.Size, 'AreaIn:', this.data.AreaIn);

    if (!this.data.Size || this.data.Size.trim() === '') {
      console.warn('Size is empty');
      this.data.Area = '0.00 Ft';
      this.calculateAmounts();
      return;
    }

    try {
      // Handle different possible size formats (2.3x4.5, 2.3X4.5, 2.3 x 4.5, etc.)
      const sizeStr = this.data.Size.toString().trim();
      const parts = sizeStr.split(/[Xx×*]/);

      if (parts.length !== 2) {
        console.warn('Invalid size format:', sizeStr);
        this.data.Area = '0.00 Ft';
        this.calculateAmounts();
        return;
      }

      // Parse width
      const width = parts[0].trim();
      let widthFeet = 0;
      let widthInches = 0;

      if (width.includes('.')) {
        const [feet, inches] = width.split('.');
        widthFeet = parseInt(feet) || 0;
        widthInches = parseInt(inches) || 0;
      } else {
        widthFeet = parseInt(width) || 0;
      }

      // Parse length
      const length = parts[1].trim();
      let lengthFeet = 0;
      let lengthInches = 0;

      if (length.includes('.')) {
        const [feet, inches] = length.split('.');
        lengthFeet = parseInt(feet) || 0;
        lengthInches = parseInt(inches) || 0;
      } else {
        lengthFeet = parseInt(length) || 0;
      }

      // Convert to total inches
      const totalWidthInches = (widthFeet * 12) + widthInches;
      const totalLengthInches = (lengthFeet * 12) + lengthInches;

      console.log('Parsed dimensions:', {
        widthFeet, widthInches, totalWidthInches,
        lengthFeet, lengthInches, totalLengthInches
      });

      if (totalWidthInches <= 0 || totalLengthInches <= 0) {
        console.warn('Invalid dimensions (zero or negative)');
        this.data.Area = '0.00 Ft';
        this.calculateAmounts();
        return;
      }

      // Calculate area based on unit
      const areaIn = this.data.AreaIn || 'Sq.Feet';

      if (areaIn === 'Sq.Feet') {
        // Square feet = (width in inches * length in inches) / 144
        this.numericArea = (totalWidthInches * totalLengthInches) / 144;
        this.data.Area = `${(this.numericArea * (this.data.Pcs || 1)).toFixed(2)} Ft`;
      } else if (areaIn === 'Sq.Yard') {
        // Square yards = (width in inches * length in inches) / 1296
        this.numericArea = (totalWidthInches * totalLengthInches) / 1296;
        this.data.Area = `${(this.numericArea * (this.data.Pcs || 1)).toFixed(2)} Yd`;
      } else {
        // Default to square feet
        this.numericArea = (totalWidthInches * totalLengthInches) / 144;
        this.data.Area = `${(this.numericArea * (this.data.Pcs || 1)).toFixed(2)} Ft`;
      }

      console.log('Calculated area:', this.numericArea, 'Total area:', this.data.Area);
    } catch (error) {
      console.error('Error calculating area:', error);
      this.data.Area = '0.00 Ft';
    }

    this.calculateAmounts();
  }

  calculateAmounts(): void {
    console.log('Calculating amounts...');

    try {
      // Get values with fallbacks to prevent NaN
      const area = this.numericArea * (this.data.Pcs || 1);
      const rate = this.parseFloat(this.data.Rate);
      const deduction = this.parseFloat(this.data.Deduction);
      const tdsPercent = this.parseFloat(this.data.TDS_percent);
      const commissionPercent = this.parseFloat(this.data.Commission_percent);

      console.log('Values for calculation:', {
        area,
        rate,
        deduction,
        tdsPercent,
        commissionPercent
      });

      // Calculate amount (area * rate - deduction)
      const amount = area * rate - deduction;
      this.data.Amount = Math.round(amount).toFixed(2); // Round to nearest integer, show .00
      console.log('Calculated amount:', amount);      // Calculate TDS (amount * tdsPercent / 100)
      const tds = (amount * tdsPercent) / 100;
      this.data.TDS = Math.round(tds).toFixed(2); // Round to nearest integer, show .00
      console.log('Calculated TDS:', tds);     
       // Calculate commission (area * commissionPercent / 100)
      const commission = (area * commissionPercent) / 100;
      this.data.Commission = Math.round(commission).toFixed(2); // Round to nearest integer, show .00
      console.log('Calculated commission:', commission);

      // Calculate net amount (amount - tds - commission)
      const netAmount = amount - tds - commission;
      this.data.NetAmount = Math.round(netAmount).toFixed(2); // Round to nearest integer, show .00
      console.log('Calculated net amount:', netAmount);
    } catch (error) {
      console.error('Error calculating amounts:', error);
      // Set default values in case of error
      this.data.Amount = '0.00';
      this.data.TDS = '0.00';
      this.data.Commission = '0.00';
      this.data.NetAmount = '0.00';
    }
  }
}