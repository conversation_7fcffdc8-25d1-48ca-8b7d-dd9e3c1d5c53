import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SweetalertService } from '../../../services/sweetalert.service';

// Interface for packing list items
export interface PackingItem {
  SrNo: number;
  baleNo: string;
  pcsNo: number;
  carpetNo: string;
  quality: string;
  design: string;
  colour: string;
  size: string;
  area: number;
  tArea: number;
  remarks: string;
  areaDisplay?: string;
  areaIn?: string;
  invoiceNo?: string;
  isLastInBale?: boolean; // Flag to indicate if this is the last item in a bale group
}

// Interface for export packing list
export interface ExportPackingList {
  _id: string;
  invoiceNo: string;
  date: string;
  items: PackingItem[];
  totalArea: number;
  totalTArea: number;
  totalItems: number;
  createdAt: string;
  updatedAt: string;
}

@Component({
  selector: 'app-view-export-packing-list',
  templateUrl: './view-export-packing-list.component.html',
  styleUrl: './view-export-packing-list.component.css'
})
export class ViewExportPackingListComponent implements OnInit, AfterViewInit {
  // Data arrays
  packingLists: ExportPackingList[] = [];
  packingItems: PackingItem[] = [];

  // Table configuration
  displayedColumns: string[] = [
    'SrNo', 'baleNo', 'pcsNo', 'quality',
    'design', 'colour', 'size', 'area', 'tArea', 'remarks', 'actions'
  ];

  dataSource = new MatTableDataSource<PackingItem>();
  isLoading: boolean = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private http: HttpClient,
    private alertService: SweetalertService
  ) {}

  ngOnInit(): void {
    this.loadPackingLists();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  // Load packing lists from the API
  loadPackingLists(): void {
    this.isLoading = true;
    this.http.get<ExportPackingList[]>('http://localhost:2000/api/phase-four/exportPackingList/exportPacking')
      .subscribe({
        next: (response: any) => {
          console.log('Loaded packing lists:', response);

          if (response && response.data && Array.isArray(response.data)) {
            // Extract the data array from the response
            this.packingLists = response.data;

            // Process all items from all packing lists
            this.processPackingItems();
          } else if (response && Array.isArray(response)) {
            // Direct array response
            this.packingLists = response;

            // Process all items from all packing lists
            this.processPackingItems();
          } else {
            console.error('Invalid response format:', response);
            this.alertService.error('error', 'Failed to load packing lists: Invalid response format');
            // Set empty data to show "No packing list data available" message
            this.dataSource.data = [];
          }

          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading packing lists:', error);
          this.alertService.error('error', 'Failed to load packing lists: ' + (error.message || 'Unknown error'));
          this.isLoading = false;
          // Set empty data to show "No packing list data available" message
          this.dataSource.data = [];
        }
      });
  }

  // Process packing items from all packing lists
  processPackingItems(): void {
    this.packingItems = [];

    // Process each packing list
    this.packingLists.forEach(list => {
      // Process each item in the packing list
      if (list.items && Array.isArray(list.items)) {
        // Group items by bale number
        const itemsByBale = new Map<string, any[]>();

        // First, group all items by bale number
        list.items.forEach((item: any) => {
          const baleNo = item.baleNo;
          if (!itemsByBale.has(baleNo)) {
            itemsByBale.set(baleNo, []);
          }
          itemsByBale.get(baleNo)?.push(item);
        });

        // Now process each group with the same SrNo for all items in the same bale
        const processedItems: PackingItem[] = [];

        // Convert the Map to an array of entries and sort by bale number
        const sortedBales = Array.from(itemsByBale.entries())
          .sort((a, b) => {
            // Extract numeric part from bale numbers for proper sorting
            const numA = parseInt(a[0].replace(/\D/g, '') || '0');
            const numB = parseInt(b[0].replace(/\D/g, '') || '0');
            return numA - numB;
          });

        // Process each bale group
        sortedBales.forEach(([baleNo, items]) => {
          // Sort items within the bale by pcsNo
          items.sort((a, b) => a.pcsNo - b.pcsNo);

          // Extract numeric part from bale number for SrNo
          const baleNumeric = parseInt(baleNo.replace(/\D/g, '') || '0');

          // Assign SrNo only to the first item in each bale group
          items.forEach((item: any, index: number) => {
            // Only set SrNo for the first item in each bale group
            // Use the bale number as the SrNo instead of an incremental counter
            const srNo = index === 0 ? baleNumeric : 0;

            // Mark the last item in each bale group for T.Area display
            const isLastInBale = index === items.length - 1;

            processedItems.push({
              SrNo: srNo,
              baleNo: item.baleNo,
              pcsNo: item.pcsNo,
              carpetNo: item.carpetNo,
              quality: item.quality,
              design: item.design,
              colour: item.colour,
              size: item.size,
              area: item.area,
              tArea: isLastInBale ? (list.totalArea || this.calculateTotalAreaForBale(items)) : 0, // Set tArea only for the last item in the bale
              remarks: item.remarks,
              areaDisplay: item.areaDisplay,
              areaIn: item.areaIn,
              invoiceNo: list.invoiceNo, // Add the invoice number from the parent list
              isLastInBale: isLastInBale // Flag to indicate if this is the last item in a bale group
            });
          });
        });

        this.packingItems.push(...processedItems);
      }
    });

    // Update the data source
    this.dataSource.data = this.packingItems;

    console.log('Processed packing items with bale-based SrNo:', this.packingItems);
  }

  // Check if this is the first item with this bale number
  isFirstItemWithBaleNo(element: PackingItem): boolean {
    const data = this.dataSource.filteredData;
    const index = data.indexOf(element);
    if (index === 0) return true;
    return element.baleNo !== data[index - 1].baleNo;
  }

  // Check if this is the last item with this bale number
  isLastItemWithBaleNo(element: PackingItem): boolean {
    const data = this.dataSource.filteredData;
    const index = data.indexOf(element);
    if (index === data.length - 1) return true;
    return element.baleNo !== data[index + 1].baleNo;
  }

  // Calculate total area for a bale
  calculateTotalAreaForBale(items: any[]): number {
    if (!items || items.length === 0) return 0;

    // Sum up the areas of all items in the bale
    return items.reduce((total, item) => {
      const area = parseFloat(item.area) || 0;
      return total + area;
    }, 0);
  }


  // Delete all items of a bale (by baleNo) with confirmation and backend API call
  deleteBale(baleNo: string): void {
    // Find the parent packing list _id for this bale
    const parentPacking = this.packingLists.find(list => list.items.some(item => item.baleNo === baleNo));
    if (!parentPacking) {
      this.alertService.error('Error', 'Could not find packing list for this bale.');
      return;
    }
    this.alertService.confirm({
      title: 'Are you sure?',
      text: `Do you want to delete all items of Bale No. ${baleNo}?`,
      icon: 'warning',
      confirmButtonText: 'Yes, delete bale!',
      cancelButtonText: 'Cancel'
    }).then((result: any) => {
      if (result.isConfirmed) {
        // Call backend API to delete the packing list document
        this.http.delete(`http://localhost:2000/api/phase-four/exportPackingList/exportPacking/${parentPacking._id}`)
          .subscribe({
            next: () => {
              // Remove from local arrays
              this.packingLists = this.packingLists.filter(list => list._id !== parentPacking._id);
              this.packingItems = this.packingItems.filter(item => item.baleNo !== baleNo);
              this.dataSource.data = this.packingItems;
              this.alertService.success('Deleted', `Bale No. ${baleNo} deleted successfully!`);
            },
            error: () => {
              this.alertService.error('Error', 'Failed to delete from server.');
            }
          });
      }
    });
  }
}
