<div class="container-fluid vh-100">
  <!-- start page title -->
  <div class="py-3 py-lg-4">
    <div class="row">
      <div class="col-lg-6">
        <h4 class="page-title mb-0">
          {{ isUpdated ? "Update Gst" : "Add New" }}
        </h4>
      </div>
    </div>
    <hr />

    <!-- Form row -->
    <div class="row">
      <div class="col-xl-12">
        <div class="card">
          <div class="card-body">
            <form [formGroup]="frmGst" (ngSubmit)="addGst()">
              <div class="row">
                <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline">
                    <mat-label>To date</mat-label>
                    <input
                      matInput
                      [matDatepicker]="picker"
                      formControlName="toDate"
                    />
                    <mat-hint>MM/DD/YYYY</mat-hint>
                    <mat-datepicker-toggle
                      matIconSuffix
                      [for]="picker"
                    ></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                  </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline">
                    <mat-label>From date</mat-label>
                    <input
                      matInput
                      [matDatepicker]="picker1"
                      formControlName="fromDate"
                    />
                    <mat-hint>MM/DD/YYYY</mat-hint>
                    <mat-datepicker-toggle
                      matIconSuffix
                      [for]="picker1"
                    ></mat-datepicker-toggle>
                    <mat-datepicker #picker1></mat-datepicker>
                  </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                  <mat-form-field
                    appearance="outline"
                    class="example-full-width"
                  >
                    <mat-label>GST Rate %</mat-label>
                    <input
                      matInput
                      placeholder="GST Rate %"
                      formControlName="gstRate"
                    />
                  </mat-form-field>
                </div>
                @if(!isUpdated){

                  <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">Save</button>
                  </div>
                  }@else{
                  <div class="col-md-3">
                    <button
                      type="button"
                      (click)="updateGst()"
                      class="btn btn-primary"
                    >
                      Update
                    </button>
                  </div>
                  }
              </div>


            </form>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12">
        <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">
          <!--- Note that these columns can be defined in any order.
                The actual rendered columns are set as a property on the row definition" -->

          <!-- Position Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef>No.</th>
            <td mat-cell *matCellDef="let element">{{ element.index }}</td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="gstRate">
            <th mat-header-cell *matHeaderCellDef>Gst Rate</th>
            <td mat-cell *matCellDef="let element">{{ element.gstRate }}</td>
          </ng-container>

          <!-- Weight Column -->
          <ng-container matColumnDef="fromDate">
            <th mat-header-cell *matHeaderCellDef>From Date</th>
            <td mat-cell *matCellDef="let element">{{ element.fromDate }}</td>
          </ng-container>

          <!-- Symbol Column -->
          <ng-container matColumnDef="toDate">
            <th mat-header-cell *matHeaderCellDef>To Date</th>
            <td mat-cell *matCellDef="let element">{{ element.toDate }}</td>
          </ng-container>

          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef>Action</th>
            <td mat-cell *matCellDef="let element">
              <a (click)="editGst(element.id)"
                ><i
                  class="fa fa-pencil-square-o fa-edit"
                  title="Edit Bill "
                  aria-hidden="true"
                ></i
              ></a>
              &nbsp;
              <a (click)="deleteGst(element.id)"
                ><i class="fa fa-trash-o" aria-hidden="true"></i
              ></a>
            </td>
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </div>
    </div>
  </div>
</div>
