const express = require('express');
const sizeController = require('../../controller/size-controller');
const sizeMiddleware = require('../../middleware/size-middleware');

const router = express.Router();

router.post('/create-size', sizeMiddleware.validateSizeData, sizeController.createSize); //done
router.get('/get-all-size', sizeController.getAllSizes); //done
router.get('/get-size/:id', sizeController.getSizeById); //done
router.put('/update-size/:id', sizeMiddleware.validateSizeData, sizeController.updateSize); //done
router.delete('/delete-size/:id', sizeController.deleteSize); //done

module.exports = router;








