



<mat-tab-group>
  <mat-tab label="Carpet Stock"> <div class="container mt-2">
    <section style="width: -webkit-fill-available;">
        <fieldset >
            <legend><b>Stock</b></legend>
  <form [formGroup]="frmCarpetStock" (ngSubmit)="getStockDetails()">
  <div class="row">
    <div class="col-md-4">
      <mat-form-field appearance="outline"class="row">
        <mat-label>Quality Design</mat-label>
        <mat-select formControlName="qualityDesign" (valueChange)="getColor($event)" >
          @for (qd of qualityDesignList; track qd) {

        <mat-option [value]="qd">{{qd}}</mat-option>
      }
        </mat-select>
      </mat-form-field>

    </div>
    <div class="col-md-4">

    <mat-form-field appearance="outline" class="row">
        <mat-label>Colour</mat-label>
        <mat-select formControlName="colour" (valueChange)="selectColour($event)">
          @for (color of colorList; track color) {
        <mat-option [value]="color">{{color}}</mat-option>
      }
        </mat-select>
      </mat-form-field>

    </div>
    <div class="col-md-4">
      <mat-form-field appearance="outline"class="row">
        <mat-label>Size</mat-label>
        <mat-select formControlName="size" >
          @for (size of sizeList; track size) {
        <mat-option [value]="size">{{size}}</mat-option>
      }
        </mat-select>
      </mat-form-field>

    </div>
    <div class="col-md-1">
      <button mat-flat-button color="primary" >Search</button>

    </div>
    &nbsp;&nbsp;
    <div class="col-md-1">

      <button mat-flat-button color="primary" type="button" (click)="reset()" >Reset</button>
    </div>
  </div>
  </form>
        </fieldset>
    </section>

    <section style="width: -webkit-fill-available;" class="mt-5">
      <fieldset >
          <legend><b>Carpet Stock Details</b></legend>
          <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">

            <!--- Note that these columns can be defined in any order.
                  The actual rendered columns are set as a property on the row definition" -->

            <!-- Position Column -->
            <ng-container matColumnDef="index">
              <th mat-header-cell *matHeaderCellDef> No. </th>
              <td mat-cell *matCellDef="let element"> {{element.index}} </td>
            </ng-container>
            <!-- <ng-container matColumnDef="barCode">
              <th mat-header-cell *matHeaderCellDef> Carpet No </th>
              <td mat-cell *matCellDef="let element"> {{element.bcrNo}} </td>
            </ng-container> -->

            <ng-container matColumnDef="size">
              <th mat-header-cell *matHeaderCellDef> Size </th>
              <td mat-cell *matCellDef="let element"> {{element.size}} </td>
            </ng-container>

            <!-- <ng-container matColumnDef="sCode">
              <th mat-header-cell *matHeaderCellDef> Size Code </th>
              <td mat-cell *matCellDef="let element"> {{element.sCode}} </td>
            </ng-container> -->



            <!-- Name Column -->
            <ng-container matColumnDef="qd">
              <th mat-header-cell *matHeaderCellDef> Quality Design </th>
              <td mat-cell *matCellDef="let row"> {{ row.qualityDesign }} <br />
                </td>
            </ng-container>
            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef> Action </th>
              <td mat-cell *matCellDef="let row"><a  (click)="navigate(row)"
                ><i class="fa fa-eye" aria-hidden="true"></i
              ></a> <br />
                </td>
            </ng-container>
            <!-- <ng-container matColumnDef="qdCode">
              <th mat-header-cell *matHeaderCellDef> QD Code </th>
              <td mat-cell *matCellDef="let element"> {{element.qdCode}} </td>
            </ng-container> -->
            <!-- Weight Column -->
            <ng-container matColumnDef="colour">
              <th mat-header-cell *matHeaderCellDef> Colour </th>
              <td mat-cell *matCellDef="let element"> {{element.color}} </td>
            </ng-container>

            <!-- Symbol Column -->
            <ng-container matColumnDef="amount">
              <th mat-header-cell *matHeaderCellDef> Amount </th>
              <td mat-cell *matCellDef="let element" class="text-end"> {{element.amount}} </td>
            </ng-container>

            <ng-container matColumnDef="area">
              <th mat-header-cell *matHeaderCellDef>Total Area </th>
              <td mat-cell *matCellDef="let element" class="text-end"> {{element.area}} </td>
            </ng-container>


            <ng-container matColumnDef="evkPrice">
              <th mat-header-cell *matHeaderCellDef> Evk Price </th>
              <td mat-cell *matCellDef="let element" class="text-end"> {{element.evkPrice}} </td>
            </ng-container>
            <ng-container matColumnDef="pcs">
              <th mat-header-cell *matHeaderCellDef> No. of Pcs </th>
              <td mat-cell *matCellDef="let element"class="text-center"> {{element.pcs}} </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
          <mat-paginator [length]="100"
          [pageSize]="10"
          [pageSizeOptions]="[5, 10, 25, 100]"
          aria-label="Select page">
  </mat-paginator>


          </fieldset>
    </section>
    </div> </mat-tab>
  <mat-tab label="All Stocks"> <app-stock-report></app-stock-report> </mat-tab>
  <mat-tab label="Third"> Content 3 </mat-tab>
</mat-tab-group>


