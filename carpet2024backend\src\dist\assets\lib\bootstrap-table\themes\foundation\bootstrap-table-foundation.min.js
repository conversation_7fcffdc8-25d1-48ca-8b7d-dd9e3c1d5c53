/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var e=n(t);function r(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function o(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,n){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},u(t,n)}function a(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function c(t){var n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var e,r=i(t);if(n){var o=i(this).constructor;e=Reflect.construct(r,arguments,o)}else e=r.apply(this,arguments);return a(this,e)}}function f(t,n){for(;!Object.prototype.hasOwnProperty.call(t,n)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,n,e){var r=f(t,n);if(r){var o=Object.getOwnPropertyDescriptor(r,n);return o.get?o.get.call(arguments.length<3?t:e):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},p=function(t){return t&&t.Math==Math&&t},d=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof s&&s)||function(){return this}()||Function("return this")(),h={},y=function(t){try{return!!t()}catch(t){return!0}},b=!y((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),g=!y((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),v=g,m=Function.prototype.call,w=v?m.bind(m):function(){return m.apply(m,arguments)},O={},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,P=S&&!j.call({1:2},1);O.f=P?function(t){var n=S(this,t);return!!n&&n.enumerable}:j;var T,E,x=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},D=g,A=Function.prototype,_=A.call,k=D&&A.bind.bind(_,_),I=function(t){return D?k(t):function(){return _.apply(t,arguments)}},F=I,R=F({}.toString),C=F("".slice),M=function(t){return C(R(t),8,-1)},L=M,z=I,G=function(t){if("Function"===L(t))return z(t)},N=y,B=M,$=Object,W=G("".split),q=N((function(){return!$("z").propertyIsEnumerable(0)}))?function(t){return"String"==B(t)?W(t,""):$(t)}:$,H=function(t){return null==t},U=H,X=TypeError,K=function(t){if(U(t))throw X("Can't call method on "+t);return t},Q=q,V=K,Y=function(t){return Q(V(t))},J="object"==typeof document&&document.all,Z={all:J,IS_HTMLDDA:void 0===J&&void 0!==J},tt=Z.all,nt=Z.IS_HTMLDDA?function(t){return"function"==typeof t||t===tt}:function(t){return"function"==typeof t},et=nt,rt=Z.all,ot=Z.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:et(t)||t===rt}:function(t){return"object"==typeof t?null!==t:et(t)},it=d,ut=nt,at=function(t){return ut(t)?t:void 0},ct=function(t,n){return arguments.length<2?at(it[t]):it[t]&&it[t][n]},ft=G({}.isPrototypeOf),lt=d,st=ct("navigator","userAgent")||"",pt=lt.process,dt=lt.Deno,ht=pt&&pt.versions||dt&&dt.version,yt=ht&&ht.v8;yt&&(E=(T=yt.split("."))[0]>0&&T[0]<4?1:+(T[0]+T[1])),!E&&st&&(!(T=st.match(/Edge\/(\d+)/))||T[1]>=74)&&(T=st.match(/Chrome\/(\d+)/))&&(E=+T[1]);var bt=E,gt=y,vt=!!Object.getOwnPropertySymbols&&!gt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&bt&&bt<41})),mt=vt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,wt=ct,Ot=nt,jt=ft,St=Object,Pt=mt?function(t){return"symbol"==typeof t}:function(t){var n=wt("Symbol");return Ot(n)&&jt(n.prototype,St(t))},Tt=String,Et=nt,xt=function(t){try{return Tt(t)}catch(t){return"Object"}},Dt=TypeError,At=function(t){if(Et(t))return t;throw Dt(xt(t)+" is not a function")},_t=At,kt=H,It=w,Ft=nt,Rt=ot,Ct=TypeError,Mt={exports:{}},Lt=d,zt=Object.defineProperty,Gt=function(t,n){try{zt(Lt,t,{value:n,configurable:!0,writable:!0})}catch(e){Lt[t]=n}return n},Nt=Gt,Bt="__core-js_shared__",$t=d[Bt]||Nt(Bt,{}),Wt=$t;(Mt.exports=function(t,n){return Wt[t]||(Wt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var qt=K,Ht=Object,Ut=function(t){return Ht(qt(t))},Xt=Ut,Kt=G({}.hasOwnProperty),Qt=Object.hasOwn||function(t,n){return Kt(Xt(t),n)},Vt=G,Yt=0,Jt=Math.random(),Zt=Vt(1..toString),tn=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Zt(++Yt+Jt,36)},nn=d,en=Mt.exports,rn=Qt,on=tn,un=vt,an=mt,cn=en("wks"),fn=nn.Symbol,ln=fn&&fn.for,sn=an?fn:fn&&fn.withoutSetter||on,pn=function(t){if(!rn(cn,t)||!un&&"string"!=typeof cn[t]){var n="Symbol."+t;un&&rn(fn,t)?cn[t]=fn[t]:cn[t]=an&&ln?ln(n):sn(n)}return cn[t]},dn=w,hn=ot,yn=Pt,bn=function(t,n){var e=t[n];return kt(e)?void 0:_t(e)},gn=function(t,n){var e,r;if("string"===n&&Ft(e=t.toString)&&!Rt(r=It(e,t)))return r;if(Ft(e=t.valueOf)&&!Rt(r=It(e,t)))return r;if("string"!==n&&Ft(e=t.toString)&&!Rt(r=It(e,t)))return r;throw Ct("Can't convert object to primitive value")},vn=TypeError,mn=pn("toPrimitive"),wn=function(t,n){if(!hn(t)||yn(t))return t;var e,r=bn(t,mn);if(r){if(void 0===n&&(n="default"),e=dn(r,t,n),!hn(e)||yn(e))return e;throw vn("Can't convert object to primitive value")}return void 0===n&&(n="number"),gn(t,n)},On=Pt,jn=function(t){var n=wn(t,"string");return On(n)?n:n+""},Sn=ot,Pn=d.document,Tn=Sn(Pn)&&Sn(Pn.createElement),En=function(t){return Tn?Pn.createElement(t):{}},xn=En,Dn=!b&&!y((function(){return 7!=Object.defineProperty(xn("div"),"a",{get:function(){return 7}}).a})),An=b,_n=w,kn=O,In=x,Fn=Y,Rn=jn,Cn=Qt,Mn=Dn,Ln=Object.getOwnPropertyDescriptor;h.f=An?Ln:function(t,n){if(t=Fn(t),n=Rn(n),Mn)try{return Ln(t,n)}catch(t){}if(Cn(t,n))return In(!_n(kn.f,t,n),t[n])};var zn={},Gn=b&&y((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Nn=ot,Bn=String,$n=TypeError,Wn=function(t){if(Nn(t))return t;throw $n(Bn(t)+" is not an object")},qn=b,Hn=Dn,Un=Gn,Xn=Wn,Kn=jn,Qn=TypeError,Vn=Object.defineProperty,Yn=Object.getOwnPropertyDescriptor,Jn="enumerable",Zn="configurable",te="writable";zn.f=qn?Un?function(t,n,e){if(Xn(t),n=Kn(n),Xn(e),"function"==typeof t&&"prototype"===n&&"value"in e&&te in e&&!e.writable){var r=Yn(t,n);r&&r.writable&&(t[n]=e.value,e={configurable:Zn in e?e.configurable:r.configurable,enumerable:Jn in e?e.enumerable:r.enumerable,writable:!1})}return Vn(t,n,e)}:Vn:function(t,n,e){if(Xn(t),n=Kn(n),Xn(e),Hn)try{return Vn(t,n,e)}catch(t){}if("get"in e||"set"in e)throw Qn("Accessors not supported");return"value"in e&&(t[n]=e.value),t};var ne=zn,ee=x,re=b?function(t,n,e){return ne.f(t,n,ee(1,e))}:function(t,n,e){return t[n]=e,t},oe={exports:{}},ie=b,ue=Qt,ae=Function.prototype,ce=ie&&Object.getOwnPropertyDescriptor,fe=ue(ae,"name"),le={EXISTS:fe,PROPER:fe&&"something"===function(){}.name,CONFIGURABLE:fe&&(!ie||ie&&ce(ae,"name").configurable)},se=nt,pe=$t,de=G(Function.toString);se(pe.inspectSource)||(pe.inspectSource=function(t){return de(t)});var he,ye,be,ge=pe.inspectSource,ve=nt,me=d.WeakMap,we=ve(me)&&/native code/.test(String(me)),Oe=Mt.exports,je=tn,Se=Oe("keys"),Pe=function(t){return Se[t]||(Se[t]=je(t))},Te={},Ee=we,xe=d,De=ot,Ae=re,_e=Qt,ke=$t,Ie=Pe,Fe=Te,Re="Object already initialized",Ce=xe.TypeError,Me=xe.WeakMap;if(Ee||ke.state){var Le=ke.state||(ke.state=new Me);Le.get=Le.get,Le.has=Le.has,Le.set=Le.set,he=function(t,n){if(Le.has(t))throw Ce(Re);return n.facade=t,Le.set(t,n),n},ye=function(t){return Le.get(t)||{}},be=function(t){return Le.has(t)}}else{var ze=Ie("state");Fe[ze]=!0,he=function(t,n){if(_e(t,ze))throw Ce(Re);return n.facade=t,Ae(t,ze,n),n},ye=function(t){return _e(t,ze)?t[ze]:{}},be=function(t){return _e(t,ze)}}var Ge={set:he,get:ye,has:be,enforce:function(t){return be(t)?ye(t):he(t,{})},getterFor:function(t){return function(n){var e;if(!De(n)||(e=ye(n)).type!==t)throw Ce("Incompatible receiver, "+t+" required");return e}}},Ne=y,Be=nt,$e=Qt,We=b,qe=le.CONFIGURABLE,He=ge,Ue=Ge.enforce,Xe=Ge.get,Ke=Object.defineProperty,Qe=We&&!Ne((function(){return 8!==Ke((function(){}),"length",{value:8}).length})),Ve=String(String).split("String"),Ye=oe.exports=function(t,n,e){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),e&&e.getter&&(n="get "+n),e&&e.setter&&(n="set "+n),(!$e(t,"name")||qe&&t.name!==n)&&(We?Ke(t,"name",{value:n,configurable:!0}):t.name=n),Qe&&e&&$e(e,"arity")&&t.length!==e.arity&&Ke(t,"length",{value:e.arity});try{e&&$e(e,"constructor")&&e.constructor?We&&Ke(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=Ue(t);return $e(r,"source")||(r.source=Ve.join("string"==typeof n?n:"")),t};Function.prototype.toString=Ye((function(){return Be(this)&&Xe(this).source||He(this)}),"toString");var Je=nt,Ze=zn,tr=oe.exports,nr=Gt,er=function(t,n,e,r){r||(r={});var o=r.enumerable,i=void 0!==r.name?r.name:n;if(Je(e)&&tr(e,i,r),r.global)o?t[n]=e:nr(n,e);else{try{r.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=e:Ze.f(t,n,{value:e,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return t},rr={},or=Math.ceil,ir=Math.floor,ur=Math.trunc||function(t){var n=+t;return(n>0?ir:or)(n)},ar=function(t){var n=+t;return n!=n||0===n?0:ur(n)},cr=ar,fr=Math.max,lr=Math.min,sr=ar,pr=Math.min,dr=function(t){return t>0?pr(sr(t),9007199254740991):0},hr=function(t){return dr(t.length)},yr=Y,br=function(t,n){var e=cr(t);return e<0?fr(e+n,0):lr(e,n)},gr=hr,vr=function(t){return function(n,e,r){var o,i=yr(n),u=gr(i),a=br(r,u);if(t&&e!=e){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===e)return t||a||0;return!t&&-1}},mr={includes:vr(!0),indexOf:vr(!1)},wr=Qt,Or=Y,jr=mr.indexOf,Sr=Te,Pr=G([].push),Tr=function(t,n){var e,r=Or(t),o=0,i=[];for(e in r)!wr(Sr,e)&&wr(r,e)&&Pr(i,e);for(;n.length>o;)wr(r,e=n[o++])&&(~jr(i,e)||Pr(i,e));return i},Er=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],xr=Tr,Dr=Er.concat("length","prototype");rr.f=Object.getOwnPropertyNames||function(t){return xr(t,Dr)};var Ar={};Ar.f=Object.getOwnPropertySymbols;var _r=ct,kr=rr,Ir=Ar,Fr=Wn,Rr=G([].concat),Cr=_r("Reflect","ownKeys")||function(t){var n=kr.f(Fr(t)),e=Ir.f;return e?Rr(n,e(t)):n},Mr=Qt,Lr=Cr,zr=h,Gr=zn,Nr=y,Br=nt,$r=/#|\.prototype\./,Wr=function(t,n){var e=Hr[qr(t)];return e==Xr||e!=Ur&&(Br(n)?Nr(n):!!n)},qr=Wr.normalize=function(t){return String(t).replace($r,".").toLowerCase()},Hr=Wr.data={},Ur=Wr.NATIVE="N",Xr=Wr.POLYFILL="P",Kr=Wr,Qr=d,Vr=h.f,Yr=re,Jr=er,Zr=Gt,to=function(t,n,e){for(var r=Lr(n),o=Gr.f,i=zr.f,u=0;u<r.length;u++){var a=r[u];Mr(t,a)||e&&Mr(e,a)||o(t,a,i(n,a))}},no=Kr,eo=function(t,n){var e,r,o,i,u,a=t.target,c=t.global,f=t.stat;if(e=c?Qr:f?Qr[a]||Zr(a,{}):(Qr[a]||{}).prototype)for(r in n){if(i=n[r],o=t.dontCallGetSet?(u=Vr(e,r))&&u.value:e[r],!no(c?r:a+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;to(i,o)}(t.sham||o&&o.sham)&&Yr(i,"sham",!0),Jr(e,r,i,t)}},ro=At,oo=g,io=G(G.bind),uo=M,ao=Array.isArray||function(t){return"Array"==uo(t)},co={};co[pn("toStringTag")]="z";var fo="[object z]"===String(co),lo=fo,so=nt,po=M,ho=pn("toStringTag"),yo=Object,bo="Arguments"==po(function(){return arguments}()),go=lo?po:function(t){var n,e,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,n){try{return t[n]}catch(t){}}(n=yo(t),ho))?e:bo?po(n):"Object"==(r=po(n))&&so(n.callee)?"Arguments":r},vo=G,mo=y,wo=nt,Oo=go,jo=ge,So=function(){},Po=[],To=ct("Reflect","construct"),Eo=/^\s*(?:class|function)\b/,xo=vo(Eo.exec),Do=!Eo.exec(So),Ao=function(t){if(!wo(t))return!1;try{return To(So,Po,t),!0}catch(t){return!1}},_o=function(t){if(!wo(t))return!1;switch(Oo(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Do||!!xo(Eo,jo(t))}catch(t){return!0}};_o.sham=!0;var ko=!To||mo((function(){var t;return Ao(Ao.call)||!Ao(Object)||!Ao((function(){t=!0}))||t}))?_o:Ao,Io=ao,Fo=ko,Ro=ot,Co=pn("species"),Mo=Array,Lo=function(t){var n;return Io(t)&&(n=t.constructor,(Fo(n)&&(n===Mo||Io(n.prototype))||Ro(n)&&null===(n=n[Co]))&&(n=void 0)),void 0===n?Mo:n},zo=function(t,n){return ro(t),void 0===n?t:oo?io(t,n):function(){return t.apply(n,arguments)}},Go=q,No=Ut,Bo=hr,$o=function(t,n){return new(Lo(t))(0===n?0:n)},Wo=G([].push),qo=function(t){var n=1==t,e=2==t,r=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,f,l,s){for(var p,d,h=No(c),y=Go(h),b=zo(f,l),g=Bo(y),v=0,m=s||$o,w=n?m(c,g):e||u?m(c,0):void 0;g>v;v++)if((a||v in y)&&(d=b(p=y[v],v,h),t))if(n)w[v]=d;else if(d)switch(t){case 3:return!0;case 5:return p;case 6:return v;case 2:Wo(w,p)}else switch(t){case 4:return!1;case 7:Wo(w,p)}return i?-1:r||o?o:w}},Ho={forEach:qo(0),map:qo(1),filter:qo(2),some:qo(3),every:qo(4),find:qo(5),findIndex:qo(6),filterReject:qo(7)},Uo={},Xo=Tr,Ko=Er,Qo=Object.keys||function(t){return Xo(t,Ko)},Vo=b,Yo=Gn,Jo=zn,Zo=Wn,ti=Y,ni=Qo;Uo.f=Vo&&!Yo?Object.defineProperties:function(t,n){Zo(t);for(var e,r=ti(n),o=ni(n),i=o.length,u=0;i>u;)Jo.f(t,e=o[u++],r[e]);return t};var ei,ri=ct("document","documentElement"),oi=Wn,ii=Uo,ui=Er,ai=Te,ci=ri,fi=En,li=Pe("IE_PROTO"),si=function(){},pi=function(t){return"<script>"+t+"</"+"script>"},di=function(t){t.write(pi("")),t.close();var n=t.parentWindow.Object;return t=null,n},hi=function(){try{ei=new ActiveXObject("htmlfile")}catch(t){}var t,n;hi="undefined"!=typeof document?document.domain&&ei?di(ei):((n=fi("iframe")).style.display="none",ci.appendChild(n),n.src=String("javascript:"),(t=n.contentWindow.document).open(),t.write(pi("document.F=Object")),t.close(),t.F):di(ei);for(var e=ui.length;e--;)delete hi.prototype[ui[e]];return hi()};ai[li]=!0;var yi=pn,bi=Object.create||function(t,n){var e;return null!==t?(si.prototype=oi(t),e=new si,si.prototype=null,e[li]=t):e=hi(),void 0===n?e:ii.f(e,n)},gi=zn.f,vi=yi("unscopables"),mi=Array.prototype;null==mi[vi]&&gi(mi,vi,{configurable:!0,value:bi(null)});var wi=function(t){mi[vi][t]=!0},Oi=eo,ji=Ho.find,Si=wi,Pi="find",Ti=!0;Pi in[]&&Array(1).find((function(){Ti=!1})),Oi({target:"Array",proto:!0,forced:Ti},{find:function(t){return ji(this,t,arguments.length>1?arguments[1]:void 0)}}),Si(Pi);var Ei=go,xi=fo?{}.toString:function(){return"[object "+Ei(this)+"]"};fo||er(Object.prototype,"toString",xi,{unsafe:!0});var Di=mr.includes,Ai=wi;eo({target:"Array",proto:!0,forced:y((function(){return!Array(1).includes()}))},{includes:function(t){return Di(this,t,arguments.length>1?arguments[1]:void 0)}}),Ai("includes");var _i=ot,ki=M,Ii=pn("match"),Fi=function(t){var n;return _i(t)&&(void 0!==(n=t[Ii])?!!n:"RegExp"==ki(t))},Ri=TypeError,Ci=go,Mi=String,Li=pn("match"),zi=eo,Gi=function(t){if(Fi(t))throw Ri("The method doesn't accept regular expressions");return t},Ni=K,Bi=function(t){if("Symbol"===Ci(t))throw TypeError("Cannot convert a Symbol value to a string");return Mi(t)},$i=function(t){var n=/./;try{"/./"[t](n)}catch(e){try{return n[Li]=!1,"/./"[t](n)}catch(t){}}return!1},Wi=G("".indexOf);zi({target:"String",proto:!0,forced:!$i("includes")},{includes:function(t){return!!~Wi(Bi(Ni(this)),Bi(Gi(t)),arguments.length>1?arguments[1]:void 0)}}),e.default.extend(e.default.fn.bootstrapTable.defaults,{classes:"table hover",buttonsPrefix:"",buttonsClass:"button"}),e.default.fn.bootstrapTable.theme="foundation",e.default.BootstrapTable=function(t){!function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&u(t,n)}(p,t);var n,a,f,s=c(p);function p(){return r(this,p),s.apply(this,arguments)}return n=p,a=[{key:"initConstants",value:function(){l(i(p.prototype),"initConstants",this).call(this),this.constants.classes.buttonsGroup="button-group",this.constants.classes.buttonsDropdown="dropdown-container",this.constants.classes.paginationDropdown="",this.constants.classes.dropdownActive="is-active",this.constants.classes.paginationActive="current",this.constants.classes.buttonActive="success",this.constants.html.toolbarDropdown=['<div class="dropdown-pane" data-dropdown><ul class="vertical menu">',"</ul></div>"],this.constants.html.toolbarDropdownItem='<li class="dropdown-item-marker"><label class="dropdown-item">%s</label></li>',this.constants.html.toolbarDropdownSeparator="<li><hr></li>",this.constants.html.pageDropdown=['<div class="dropdown-pane" id="pagination-list-id" data-dropdown><ul class="vertical menu">',"</ul></div>"],this.constants.html.pageDropdownItem='<li class="dropdown-item %s"><a href="#">%s</a></li>',this.constants.html.dropdownCaret='<i class="fa fa-angle-down"></i>',this.constants.html.pagination=['<ul class="pagination%s">',"</ul>"],this.constants.html.paginationItem='<li><a class="page-item%s" aria-label="%s" href="#">%s</a></li>',this.constants.html.inputGroup='<div class="input-group">%s <div class="input-group-button">%s</div></div>',this.constants.html.searchInput='<input class="%s input-%s input-group-field" type="search" placeholder="%s">'}},{key:"initToolbar",value:function(){l(i(p.prototype),"initToolbar",this).call(this),this.handleToolbar()}},{key:"handleToolbar",value:function(){this.$toolbar.find(".dropdown-toggle").length&&(this.$toolbar.find(".dropdown-toggle").each((function(t,n){if(e.default(n).next().length){var r="toolbar-columns-id".concat(t);e.default(n).next().attr("id",r),e.default(n).attr("data-toggle",r);var o=e.default(n).next().attr("data-position","bottom").attr("data-alignment","right");new window.Foundation.Dropdown(o)}})),this._initDropdown())}},{key:"initPagination",value:function(){if(l(i(p.prototype),"initPagination",this).call(this),this.options.pagination&&this.paginationParts.includes("pageSize")){var t=this.$pagination.find(".dropdown-toggle");t.attr("data-toggle",t.next().attr("id"));var n=this.$pagination.find(".dropdown-pane").attr("data-position","top").attr("data-alignment","left");new window.Foundation.Dropdown(n),this._initDropdown()}}},{key:"_initDropdown",value:function(){var t=this.$container.find(".dropdown-toggle");t.off("click").on("click",(function(n){var r=e.default(n.currentTarget);n.stopPropagation(),r.next().foundation("toggle"),t.not(r).length&&t.not(r).next().foundation("close")})),e.default(document).off("click.bs.dropdown.foundation").on("click.bs.dropdown.foundation",(function(){t.next().foundation("close")}))}}],a&&o(n.prototype,a),f&&o(n,f),Object.defineProperty(n,"prototype",{writable:!1}),p}(e.default.BootstrapTable)}));
