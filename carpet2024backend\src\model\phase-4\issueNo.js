const mongoose = require("mongoose");

const issueNoSchema = new mongoose.Schema(
  {
    issueNo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "CarpetOrderissue",
      required: true,
    },
    materialLagat: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "MaterialLagat",
      required: true,
    },
    issueNo_receive: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "AddDesign",
      required: true,
    },
    receving: {
      type: String,
      required: true,
    },
  },
  {
    autoCreate: true,
    timestamps: true,
  }
);

module.exports = mongoose.model("IssueNo", issueNoSchema);
