<div class="container mt-4">
  <section style="margin: 10px">
    <fieldset >
      <legend><b>Add Quality</b></legend>
      <form [formGroup]="qualityForm"(ngSubmit)="qualityFormSubmit()">
      <div class="row">
        <div class="mb-2 col-md-3">
          <mat-form-field appearance="outline" class="ex-width">
            <mat-label> Quality Name </mat-label>
            <input formControlName="quality" matInput placeholder="Quality Name"/>
          </mat-form-field>
        </div>
        <div class="mb-2 col-md-3">
          <mat-form-field appearance="outline" class="ex-width">
            <mat-label> Export Quality </mat-label>
            <input formControlName="exportQuality" matInput placeholder="Export Quality"/>
          </mat-form-field>
        </div>

        <div class="mb-2 col-md-3">
          <mat-form-field appearance="outline" class="ex-width">
            <mat-label>Quality In Inch</mat-label>
            <input formControlName="qualityInInch" matInput placeholder="Quality In Inch"/>
          </mat-form-field>
        </div>
        <div class="mb-2 col-md-3 mt-2">
          <button mat-flat-button color="primary" type="submit">{{isEditMode ? 'Update' : 'Add' }}</button>
        </div>
    
      </div>
    
    </form> 
    </fieldset>
  </section>
</div>
<div class="container mt-4">
  <section>
    <fieldset>
      <legend><b>List</b></legend>
      <div class="container-fluid">
        <div class="py-3 py-lg-4">
          <div class="row mt-3">
             <div class="col-12">
              <mat-form-field  appearance="outline"><mat-label>Seacrh</mat-label>
                <input matInput (keyup)="applyFilter($event)"placeholder="Ex. Mia" #input/>
              </mat-form-field>

            <div class="mat-elevation-z8">
              <table mat-table [dataSource]="dataSource" matSort>
                <!-- ID Column -->
                <ng-container matColumnDef="id">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>No</th>
                  <td mat-cell *matCellDef="let row,let i = index">{{getSerialNumber(i)}}</td>
                </ng-container>
                <!-- Quality Column -->
                <ng-container matColumnDef="quality">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
                  <td mat-cell *matCellDef="let row">{{row.quality}}</td>
                </ng-container>

                <!-- Export Quality Column -->
                <ng-container matColumnDef="exportQuality">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Export Quality</th>
                  <td mat-cell *matCellDef="let row">{{row.exportQuality}}</td>
                </ng-container>

                <!-- Quality Column -->
                <ng-container matColumnDef="qualityInInch">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality In Inch</th>
                  <td mat-cell *matCellDef="let row">{{row.qualityInInch}}</td>
                </ng-container>

                <!-- action Column -->
                <ng-container matColumnDef="action">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                  <td mat-cell *matCellDef="let row">
                    <button mat-icon-button color="primary"(click)="editquality(row)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn"(click)="deleteConformation(row._id)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </td>
                
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr
                  mat-row
                  *matRowDef="let row; columns: displayedColumns"
                ></tr>

                <!-- Row shown when there is no matching data. -->
                <tr class="mat-row" *matNoDataRow>
                  <td class="mat-cell" colspan="4">
                    No data matching the filter "{{ input.value }}"
                  </td>
                </tr>
              </table>

              <mat-paginator 
              [pageSizeOptions]="[5, 10, 25, 100]"aria-label="Select page of users">
            </mat-paginator>
            </div>
            </div>
          </div>
        </div>
      </div>
    </fieldset>
  </section>
</div>

 