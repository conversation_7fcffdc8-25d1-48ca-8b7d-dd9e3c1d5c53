import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import { SweetalertService } from '../../../services/sweetalert.service';
import { CustomeServiceService } from '../../../services/custome-service.service';
export interface UserData {
  name: string;
  id: string;
  index: number;
  toDate: string;
  fromDate: string;
  material: string;
  quality: string;
  count: string;
  color: string;
  yarnColor:string;
  rate: string;
  colorCode: string;
  companyColorCode: string;
}

const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-dyeing-rate',
  templateUrl: './dyeing-rate.component.html',
  styleUrl: './dyeing-rate.component.css',
})
export class DyeingRateComponent implements OnInit {
  displayedColumns: string[] = [
    'id',
    'name',
    'toDate',
    'fromDate',
    'material',
    'quality',
    'count',
    'yarnColor',
    'color',
    // colorCode
    'companyColorCode',
    'rate',
    'action',
  ];
  qualityList: any = [];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);
  constructor(
    private service: MasterService,
    private alert: SweetalertService,
    private fb: FormBuilder,
    private customeService: CustomeServiceService
  ) {}
  frmDyeingRate!: FormGroup;
  ngOnInit(): void {
    this.frmDyeingRate = this.fb.group({
      name: [''],
      toDate: [''],
      fromDate: [''],
      material: [''],
      quality: [''],
      companyColorCode: [''],
      count: [''],
      yarnColor:[''],
      color: [''],
      rate: [''],
    });
    this.gets();
    this.qualitys();
    this.getMaterial();
    this.getDyers();
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  ids!: string;
  isUpdated: boolean = false;
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  gets() {
    this.service.getsDyeingRate().subscribe({
      next: (value: any) => {
        ELEMENT_DATA.length = 0;
        value.map((v: any, i: number) => {
          ELEMENT_DATA.push({
            id: v._id,
            index: i + 1,
            name: v.name?.name,
            toDate: this.customeService.convertDate(v.toDate),
            fromDate: this.customeService.convertDate(v.fromDate),
            material: v.material?.Group || 'N/A',
            quality: v.quality?.quality,
            count: v.count,
            yarnColor:v.yarnColor?.Color,
            color: v.color?.newColor,
            colorCode: v.color?.colorCode,
            companyColorCode: v.color?.companyColorCode,
            rate: v.rate,
          });
        });
        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        return;
      },
    });
  }

  qualitys() {
    this.service.getqualityList().subscribe({
      next: (value: any) => {
        value.map((v: any) => {
          this.qualityList.push({
            quality: v.quality,
            id: v._id,
          });
        });
      },
      error: (err) => {},
    });
  }
  qty: any;
  onQuality(data: any) {
    this.qty = data;
    this.getsColor(this.qty);
  }
  allColorList: any = [];
  colorList: any = [];
  getsColor(id: string) {
    this.service.getsColourCodeDetails().subscribe({
      next: (value: any) => {
        this.allColorList.length = 0;

          this.allColorList=value


          this.colorList = this.allColorList.filter((i: any) => i.quality?._id === id)
          .map((x: any) => ({
            id: x._id,
            newColor: x.newColor,
          }));

        console.log(value);
      },
    });
  }
  dyersList: any = [];

  getDyers() {
    this.service.getAllWeaverEmployee().subscribe({
      next: (value: any) => {
        this.dyersList = value.filter((x: any) => x.groupName === 'Dyers');
      },
    });
  }
  color: any;
  onColor(id: string) {
    const colorObject = this.allColorList.find((x: any) => x._id === id);
    debugger
    // If the color object is found, return the colorCode property; otherwise, return a default value (e.g., an empty string)
    this.color = colorObject ? colorObject.colorCode : '';
    this.frmDyeingRate
      .get('companyColorCode')
      ?.setValue(colorObject.companyColorCode);
  }
  groupList: any = [];
  yarnList:any=[];
  yarnColorList:any=[];
  getMaterial() {
    this.service.getsRawMaterial().subscribe({
      next: (value: any) => {
        this.yarnList = value;
        value.map((x: any) => {
          this.groupList.push({
            id: x._id,
            Group: x.Group,
          });
        });

        // Filter out duplicates based on 'id' and 'Group'
        this.groupList = this.groupList.filter(
          (value: any, index: any, self: any) =>
            index === self.findIndex((t: any) => t.Group === value.Group)
        );
        ;
      },
      error: (err: any) => {
        console.error('Error fetching raw materials:', err);
      },
      complete: () => {
        console.log('Raw materials fetched successfully.');
      },
    });
  }

  countList: any = [];
  onCount(id: string) {
    this.service.getsRawMaterial().subscribe({
      next: (value: any) => {
        let countLists: any = [];
        value.map((x: any) => {
          countLists.push({
            id: x._id,
            Group: x.Group,
            count: x.Count,
          });
        });
        countLists.map((v: any) => {
          this.countList.push({
            id: v.id,
            count: v.count,
          });
        });
        // Filter out duplicates based on 'id' and 'Group'
        ;
        this.countList = this.countList.filter(
          (value: any, index: any, self: any) =>
            index === self.findIndex((t: any) => t.count === value.count)
        );
        ;
      },
      error: (err: any) => {
        console.error('Error fetching raw materials:', err);
      },
      complete: () => {
        console.log('Raw materials fetched successfully.');
      },
    });
    this.countList = this.groupList.filter((x: any) => x.id === id);
  }
  selectCount(count: string) {
    debugger
    this.yarnColorList=this.yarnList.filter((x:any)=>x.Count ===count || x._id ===count);
  }

  addDyng() {
    console.log(this.frmDyeingRate.value);
    let formData = this.frmDyeingRate.value;
    debugger
    this.service.addDyeingRate(formData).subscribe({
      next: (value) => {
        this.alert.success('success', 'Dyeing rate has been added');
        this.clear();
        this.gets();
      },
      error: (err) => {
        this.alert.error('warning', 'Something');
        this.clear();
        this.gets();
      },
    });
  }
  update() {
    let formData = this.frmDyeingRate.value;
    if (this.ids) {
      this.service.updateDyeingRate(this.ids, formData).subscribe({
        next: (value) => {
          this.alert.success('success', 'Dyeing rate has been updated');
          this.clear();
          this.gets();
        },
        error: (err) => {
          this.alert.error('warning', 'Something');
          this.clear();
          this.gets();
        },
      });
    }
  }
  edit(id: string) {
    this.service.getDyeingRate(id).subscribe({
      next: (value: any) => {
        this.ids = id;

        this.isUpdated = true;
        this.onCount(value.material);
        this.getsColor(value.quality);

        setTimeout(() => {
          this.onColor(value.color);
          this.selectCount(value.yarnColor)
          this.frmDyeingRate.patchValue(value);
        }, 1000);
      },
    });
  }
  delete(id: string) {
    this.alert.confirm({}).then((res: any) => {
      if (res.isConfirmed) {
        this.service.deleteDyeingRate(id).subscribe({
          next: (value) => {
            this.alert.success('success', 'Dyeing rate has been deleted');
            this.gets();
          },
          error: (err) => {
            this.alert.error('warning', 'Something');
            this.gets();
          },
        });
      }
    });
  }
  clear() {
    this.frmDyeingRate.reset();
  }
}
