// services/wholeSellerService.js

const wholeSellerRepository = require('../repositories/wholeseller-repository');

async function createWholeSeller(wholeSellerData) {
    return wholeSellerRepository.createWholeSeller(wholeSellerData);
}

async function getWholeSellerById(wholeSellerId) {
    return wholeSellerRepository.getWholeSellerById(wholeSellerId);
}

async function getAllWholeSellers() {
    return wholeSellerRepository.getAllWholeSellers();
}

async function updateWholeSeller(wholeSellerId, wholeSellerData) {
    return wholeSellerRepository.updateWholeSeller(wholeSellerId, wholeSellerData);
}

async function deleteWholeSeller(wholeSellerId) {
    return wholeSellerRepository.deleteWholeSeller(wholeSellerId);
}

module.exports = {
    createWholeSeller,
    getWholeSellerById,
    getAllWholeSellers,
    updateWholeSeller,
    deleteWholeSeller
};
