// Script to import H-format data without touching existing K- records
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Import models
const CarpetReceived = require('../src/model/phase-4/carpetReceived');
const Branch = require('../src/model/phase-3/manageBranch');
const WeaverEmployee = require('../src/model/phase-3/weaver_employee');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function getDefaultReferences() {
  const defaultBranch = await Branch.findOne({ branchName: 'Main Branch' });
  const defaultWeaver = await WeaverEmployee.findOne({ name: 'Stock Import Weaver' });
  return { defaultBranch, defaultWeaver };
}

// H-format data from the image
const hFormatData = [
  { issueNo: 'H-220169', weaverName: 'Stock March 2024', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220169' },
  { issueNo: 'H-220030', weaverName: 'Stock March 2025', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220030' },
  { issueNo: 'H-220031', weaverName: 'Stock March 2026', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220031' },
  { issueNo: 'H-220012', weaverName: 'Stock March 2027', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220012' },
  { issueNo: 'H-220032', weaverName: 'Stock March 2028', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220032' },
  { issueNo: 'H-220019', weaverName: 'Stock March 2029', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220019' },
  { issueNo: 'H-220011', weaverName: 'Stock March 2030', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220011' },
  { issueNo: 'H-220130', weaverName: 'Stock March 2031', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220130' },
  { issueNo: 'H-220116', weaverName: 'Stock March 2032', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220116' },
  { issueNo: 'H-220111', weaverName: 'Stock March 2033', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220111' },
  { issueNo: 'H-220168', weaverName: 'Stock March 2034', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220168' },
  { issueNo: 'H-220129', weaverName: 'Stock March 2035', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220129' },
  { issueNo: 'H-220112', weaverName: 'Stock March 2036', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220112' },
  { issueNo: 'H-220017', weaverName: 'Stock March 2037', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220017' },
  { issueNo: 'H-220018', weaverName: 'Stock March 2038', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220018' },
  { issueNo: 'H-220009', weaverName: 'Stock March 2039', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '23 X 45', pcs: 1, area: '1.10', carpetNo: 'H-220009' },
  { issueNo: 'H-220010', weaverName: 'Stock March 2040', date: '04-01-2024', quality: '210 X 51', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-220010' },
  { issueNo: 'H-210534', weaverName: 'Stock March 2041', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210534' },
  { issueNo: 'H-210325', weaverName: 'Stock March 2042', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210325' },
  { issueNo: 'H-210966', weaverName: 'Stock March 2043', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210966' },
  { issueNo: 'H-210977', weaverName: 'Stock March 2044', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210977' },
  { issueNo: 'H-210535', weaverName: 'Stock March 2045', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210535' },
  { issueNo: 'H-210223', weaverName: 'Stock March 2046', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210223' },
  { issueNo: 'H-210967', weaverName: 'Stock March 2047', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210967' },
  { issueNo: 'H-210735', weaverName: 'Stock March 2048', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210735' },
  { issueNo: 'H-210178', weaverName: 'Stock March 2049', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210178' },
  { issueNo: 'H-210176', weaverName: 'Stock March 2050', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210176' },
  { issueNo: 'H-220026', weaverName: 'Stock March 2051', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '110 X 210', pcs: 1, area: '0.58', carpetNo: 'H-220026' },
  { issueNo: 'H-220027', weaverName: 'Stock March 2052', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '110 X 210', pcs: 1, area: '0.58', carpetNo: 'H-220027' },
  { issueNo: 'H-220028', weaverName: 'Stock March 2053', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '110 X 210', pcs: 1, area: '0.58', carpetNo: 'H-220028' },
  { issueNo: 'H-220029', weaverName: 'Stock March 2054', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '110 X 210', pcs: 1, area: '0.58', carpetNo: 'H-220029' },
  { issueNo: 'H-220030', weaverName: 'Stock March 2055', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '110 X 210', pcs: 1, area: '0.58', carpetNo: 'H-220030' },
  { issueNo: 'H-220031', weaverName: 'Stock March 2056', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '110 X 210', pcs: 1, area: '0.58', carpetNo: 'H-220031' },
  { issueNo: 'H-880012', weaverName: 'Stock March 2057', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', size: '46 X 66', pcs: 1, area: '3.25', carpetNo: 'H-880012' },
  { issueNo: 'H-250018', weaverName: 'Stock March 2058', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Red', colour2: 'Cream', size: '311 X 511', pcs: 1, area: '2.57', carpetNo: 'H-250018' },
  { issueNo: 'H-220158', weaverName: 'Stock March 2059', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Cream', size: '110 X 210', pcs: 1, area: '0.58', carpetNo: 'H-220158' }
];

async function importHFormatData() {
  console.log('🔄 IMPORTING H-FORMAT DATA');
  console.log('='.repeat(60));
  
  try {
    const { defaultBranch, defaultWeaver } = await getDefaultReferences();
    
    // Check existing K- records count (should not be touched)
    const existingKRecords = await CarpetReceived.countDocuments({
      receiveNo: { $regex: /^K-/ }
    });
    console.log(`📊 Existing K- records: ${existingKRecords} (will not be touched)`);
    
    const results = { success: [], errors: [] };
    
    // Import each H-format record
    for (let i = 0; i < hFormatData.length; i++) {
      try {
        const record = hFormatData[i];
        
        // Check if H- record already exists
        const existingRecord = await CarpetReceived.findOne({
          receiveNo: record.issueNo
        });
        
        if (existingRecord) {
          console.log(`⚠️ H-record ${record.issueNo} already exists, skipping...`);
          continue;
        }
        
        // Create issueNo object
        const issueNoObject = {
          Br_issueNo: record.issueNo,
          date: new Date(record.date).toISOString(),
          quality: { quality: record.quality },
          design: { design: record.design },
          borderColour: `${record.colour}/${record.colour2}`,
          size: { 
            sizeInYard: record.size, 
            sizeinMeter: record.size 
          },
          rate: '400',
          amount: (parseFloat(record.area) * 400).toString(),
          areaIn: 'Sq.Ft'
        };

        const carpetReceivedData = {
          K: defaultBranch._id,
          receivingDate: new Date(record.date),
          issueNo: issueNoObject,
          weaverNumber: defaultWeaver._id,
          receiveNo: record.issueNo, // H-format
          area: record.area + ' Ft',
          amount: (parseFloat(record.area) * 400).toString(),
          pcs: parseInt(record.pcs) || 1,
          weaverName: record.weaverName, // Stock March format
          quality: record.quality,
          design: record.design,
          colour: record.colour,
          colour2: record.colour2,
          size: record.size,
          carpetNo: record.carpetNo
        };

        const newRecord = await CarpetReceived.create(carpetReceivedData);
        
        results.success.push({
          index: i + 1,
          receiveNo: record.issueNo,
          weaverName: record.weaverName,
          area: record.area,
          amount: carpetReceivedData.amount,
          id: newRecord._id.toString()
        });

        if ((i + 1) % 10 === 0) {
          console.log(`✅ Imported ${i + 1}/${hFormatData.length} H-records`);
        }

      } catch (error) {
        console.error(`❌ Error importing H-record ${i + 1}:`, error.message);
        results.errors.push({
          index: i + 1,
          issueNo: hFormatData[i].issueNo,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error importing H-format data:', error);
    return { success: [], errors: [] };
  }
}

async function verifyImport() {
  console.log('\n🔍 Verifying import...');
  
  try {
    const totalRecords = await CarpetReceived.countDocuments();
    const kRecords = await CarpetReceived.countDocuments({
      receiveNo: { $regex: /^K-/ }
    });
    const hRecords = await CarpetReceived.countDocuments({
      receiveNo: { $regex: /^H-/ }
    });
    
    console.log(`📊 Total records: ${totalRecords}`);
    console.log(`📊 K- records: ${kRecords} (original, untouched)`);
    console.log(`📊 H- records: ${hRecords} (newly imported)`);
    
    // Show sample H- records
    const sampleHRecords = await CarpetReceived.find({
      receiveNo: { $regex: /^H-/ }
    }).limit(5).lean();
    
    console.log('\n📋 Sample imported H- records:');
    sampleHRecords.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver: ${record.weaverName}`);
      console.log(`   Quality: ${record.quality}`);
      console.log(`   Design: ${record.design}`);
      console.log(`   Area: ${record.area}`);
      console.log(`   Amount: ${record.amount}`);
      console.log('   ' + '-'.repeat(50));
    });
    
  } catch (error) {
    console.error('❌ Error verifying import:', error);
  }
}

async function main() {
  console.log('🔄 IMPORTING H-FORMAT DATA (KEEPING K- RECORDS INTACT)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Import H-format data
    const results = await importHFormatData();

    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 H-FORMAT DATA IMPORT COMPLETE');
    console.log('='.repeat(60));
    console.log(`✅ Successfully imported: ${results.success.length} H-records`);
    console.log(`❌ Failed: ${results.errors.length} H-records`);
    
    if (results.success.length > 0) {
      console.log('\n✅ SAMPLE IMPORTED H-RECORDS:');
      results.success.slice(0, 10).forEach(record => {
        console.log(`  - ${record.receiveNo}: ${record.weaverName} | ${record.area} Ft | ₹${record.amount}`);
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.slice(0, 5).forEach(error => {
        console.log(`  - ${error.issueNo}: ${error.error}`);
      });
    }
    
    // Verify import
    await verifyImport();

  } catch (error) {
    console.error('❌ Import failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 H-FORMAT DATA IMPORTED SUCCESSFULLY!');
    console.log('✅ K- records (53) remain untouched');
    console.log('✅ H- records imported with Stock March weaver names');
    console.log('✅ Both K- and H- records now available in system');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
