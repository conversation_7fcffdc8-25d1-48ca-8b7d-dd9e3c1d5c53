const mongoose = require('mongoose');

async function checkAllCollections() {
  try {
    // Connect to MongoDB
    await mongoose.connect("mongodb+srv://infosarthaktech:<EMAIL>/test", {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('Connected to MongoDB Atlas');

    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`Found ${collections.length} collections`);
    
    // Check each collection for indexes
    for (const collection of collections) {
      const collectionName = collection.name;
      console.log(`\nChecking collection: ${collectionName}`);
      
      const coll = mongoose.connection.db.collection(collectionName);
      const indexes = await coll.indexes();
      
      console.log(`Indexes for ${collectionName}:`);
      console.log(JSON.stringify(indexes, null, 2));
      
      // Check for any unique indexes
      const uniqueIndexes = indexes.filter(index => index.unique === true && index.name !== '_id_');
      if (uniqueIndexes.length > 0) {
        console.log(`Found ${uniqueIndexes.length} unique indexes in ${collectionName}:`);
        console.log(JSON.stringify(uniqueIndexes, null, 2));
      }
    }
    
    console.log('\nCollection check completed');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the function
checkAllCollections();
