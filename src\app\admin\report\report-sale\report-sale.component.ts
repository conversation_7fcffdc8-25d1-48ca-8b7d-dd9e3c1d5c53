import { Component, OnInit, ViewChild } from '@angular/core';
import { ImporterService } from '../../../services/importer.service';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { ActivatedRoute } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { NgxUiLoaderService } from 'ngx-ui-loader';

@Component({
  selector: 'app-report-sale',
  templateUrl: './report-sale.component.html',
  styleUrl: './report-sale.component.css',
})
export class ReportSaleComponent implements OnInit {
  constructor(
    private _service: ImporterService,
    private customeService: CustomeServiceService,
    private activeRoute: ActivatedRoute,
    private ngxLoader: NgxUiLoaderService
  ) {}
  allBills: any;
  billId: string = '';
  _isChallan: boolean = false;
  ngOnInit(): void {
    // this.getBills();
    // this.getChallans();
    // this.getContainerReceived();
    //  this.billId = this.activeRoute.snapshot.paramMap.getAll('id') || '';
    // this.view();
    this.billId = this.activeRoute.snapshot.paramMap.get('id') || '';
    this.initializeData(this.billId);
  }

  initializeData(id: string) {
    this.ngxLoader.start();
    Promise.all([
      this.getBills(),
      this.getChallans(),
      this.getContainerReceived(),
    ])
      .then(() => {
        let _bill = id.split(' ');
        if (_bill[1] === 'bill') {
          this.viewBill(_bill[0]);
        } else {
          this.viewChallan(_bill[0]);
        }
        this.ngxLoader.stop();
      })
      .catch((error) => {
        console.log('something went wrong');
      });

    // this.billId = this.activeRoute.snapshot.paramMap.get('id') || '';
    // setTimeout(() => {
    //   if (_bill[1] === 'bill') {
    //     this.viewBill(_bill[0]);
    //   } else {
    //     this.viewChallan(_bill[0]);
    //   }
    // }, 5000);
  }
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngAfterViewInit() {}

  async getBills(): Promise<any> {
    const resp = await firstValueFrom(this._service.getsBill());
    this.allBills = resp;
  }
  allChallans: any;
  allContainerStock: any;

  // getChallans() {
  //   this._service.getAllChallan().subscribe((resp: any) => {
  //     this.allChallans = resp;
  //   });
  // }
  // getContainerReceived() {
  //   this._service.getAllContainerRecieved().subscribe((resp: any) => {
  //     this.allContainerStock = resp;
  //   });
  // }

  async getChallans(): Promise<void> {
    const resp = await firstValueFrom(this._service.getAllChallan());
    this.allChallans = resp;
  }

  async getContainerReceived(): Promise<void> {
    const resp = await firstValueFrom(this._service.getAllContainerRecieved());
    this.allContainerStock = resp;
  }
  billDetails: any = [];
  _bill: any = {};
  viewBill(id: any) {
    let getChallans = this.allBills.find((x: any) => x._id === id.toString());
    this._bill = {
      billNo: getChallans.billNo,
      date: this.customeService.convertDate(getChallans.chooseAdate),
      customer: getChallans.wholesellerName,
    };
    getChallans.challanNo.forEach((element: any) => {
      let getChallanDetails = this.allChallans.find(
        (x: any) => x.challanNo === element.challanNumber
      );
      if (!getChallanDetails || !getChallanDetails.carpetList) {
        console.error(`Challan data or carpet list is undefined for challan number: ${element.challanNumber}`);
        return; // Skip to the next iteration if carpetList is undefined
      }
      getChallanDetails.carpetList.forEach((elem: any) => {
        // Find the item details in allContainerStock
        let matchedContainers = this.allContainerStock.filter(
          (container: any) =>
            container.containerItem.some(
              (item: any) => parseInt(item.GerCarpetNo) === elem.barcodeNo && elem.isDeleted!=true
            )
        );
        matchedContainers.forEach((container: any) => {
          container.containerItem.forEach((item: any) => {
            if (parseInt(item.GerCarpetNo) === elem.barcodeNo) {
              let date = this.customeService.convertDate(
                getChallanDetails.chooseAdate
              );
              const includeChallanDetails = this.billDetails.some(
                (a: any) => a.challanNo == getChallanDetails.challanNo
              );

              // let area =parseFloat(elem.area ? elem.area : item.Area);
              const area = parseFloat(elem.area ? elem.area : item.Area);
              const evkPrice = parseFloat(
                elem.evkPrice ? elem.evkPrice : item.EvKPrice
              );

              // Adjust area and amount based on status
              const adjustedArea =
                elem.status === 'return' ? (area > 0 ? -area : area) : area;
              const adjustedAmount = evkPrice * adjustedArea;
              this.billDetails.push({
                challanNo: includeChallanDetails
                  ? undefined
                  : getChallanDetails.challanNo,
                challanDate: includeChallanDetails ? undefined : date,
                customer: includeChallanDetails
                  ? undefined
                  : getChallanDetails.retailerOutlet,
                carpetNo: item.GerCarpetNo,
                qualityDesign: item.QualityDesign,
                colour: item.Color,
                colourCode: item.CCode,
                qualityCode: item.QCode,
                size: elem.size ? elem.size : item.Size,
                area: adjustedArea,
                evkPrice: elem.evkPrice ? elem.evkPrice : item.EvKPrice,
                amount: adjustedAmount,
                invoiceNo: item.InvoiceNo,
                saleStatus: item.status,
              });
            }
          });
        });
      });
    });

    this.calculation(this.billDetails);
    console.log(this.billDetails);
  }

  viewChallan(id: any) {
    this._isChallan = true;
    let challan = this.allChallans.find((x: any) => x._id === id.toString());
    if (challan) {
      challan.carpetList.forEach((elem: any) => {
        this._bill = {
          wholeseller: challan.wholeseller
            ? challan.wholeseller
            : challan.RetailerName,
          chooseAdate: this.customeService.convertDate(challan.chooseAdate),
          RetailerName: challan.retailerOutlet,
          challanNo: challan.challanNo,
        };
        this.allContainerStock.forEach((container: any) => {
          let item = container.containerItem.find(
            (p: any) => parseInt(p.GerCarpetNo) === elem.barcodeNo && elem.isDeleted===false
          );
          if (item) {
            let area = 0;
            if (elem.status === 'return') {
              area = parseFloat(item.Area);
            }
            debugger;
            let date = this.customeService.convertDate(container.chooseAdate);
            this.billDetails.push({
              challanNo: container.challanNo,
              challanDate: date,
              customer: container.retailerOutlet,
              carpetNo: item.GerCarpetNo,
              qualityDesign: item.QualityDesign,
              colour: item.Color,
              colourCode: item.CCode,
              qualityCode: item.QCode,
              size: elem.size ? elem.size : item.Size,
              area:
                elem.status === 'sale'
                  ? elem.area
                    ? elem.area
                    : item.Area
                  : elem.area
                  ? elem.area
                  : -area,
              evkPrice: elem.evkPrice ? elem.evkPrice : item.EvKPrice,
              amount:
                elem.status === 'sale'
                  ? parseFloat(elem.evkPrice ? elem.evkPrice : item.EvKPrice) *
                    parseFloat(elem.area ? elem.area : item.Area)
                  : parseFloat(elem.evkPrice ? elem.evkPrice : item.EvKPrice) *
                    parseFloat(elem.area ? elem.area : -area),
              invoiceNo: item.InvoiceNo,
              saleStatus: item.status,
            });
          }
        });
      });
      this.calculation(this.billDetails);
    }
  }
  totalCalculation = {
    totalAmount: 0,
    totalArea: 0,
    totalEvkPrice: 0,
  };

  calculation(data: any) {
    data.forEach((element: any) => {
      this.totalCalculation = {
        totalAmount:
          this.totalCalculation.totalAmount +
          parseFloat(element.area) * parseFloat(element.evkPrice),
        totalArea: this.totalCalculation.totalArea + parseFloat(element.area),
        totalEvkPrice:
          this.totalCalculation.totalEvkPrice + parseFloat(element.evkPrice),
      };
    });

    console.log(this.totalCalculation);

    debugger;
  }
}
