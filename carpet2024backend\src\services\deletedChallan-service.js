// const deletedChallanRepository = require('../repositories/deletedChallanRepository');

// const deletedChallanService = 
//     async (challanId) => {
//         try {
//             // Find the document to be moved
//             const challanData = await CreateChallan.findById(challanId);
//             if (!challanData) {
//                 throw new Error("Challan not found");
//             }
    
//             // Create a copy of the document without the _id field
//             const deleteHistoryData = { ...challanData.toObject(), _id: undefined };
    
//             // Save the document to DeletedChallanHistory
//             await DeletedChallanHistory.create(deleteHistoryData);
    
//             // Delete the document from CreateChallan
//             await CreateChallan.findByIdAndDelete(challanId);
    
//             return { message: "Cha<PERSON> moved and deleted successfully" };
//         } catch (error) {
//             throw error;
//         }
// };

// module.exports = deletedChallanService;
// Adjust the path according to your project structure
const DeletedChallanHistory = require('../repositories/deletedChallan-repository'); // Adjust the path according to your project structure

const deletedChallanService = async (challanId) => {
    try {
        // Find the document to be moved
        const challanData = await CreateChallan.findById(challanId);
        if (!challanData) {
            throw new Error("Challan not found");
        }

        // Create a copy of the document without the _id field
        const deleteHistoryData = { ...challanData.toObject(), _id: undefined };

        // Save the document to DeletedChallanHistory
        await DeletedChallanHistory.create(deleteHistoryData);

        // Delete the document from CreateChallan
         await CreateChallan.findByIdAndDelete(challanId);

        return { message: "Challan moved and deleted successfully" };
    } catch (error) {
        console.error("Error in deletedChallanService: ", error.message);
        throw new Error("An error occurred while deleting the challan");
    }
};

module.exports = deletedChallanService;
