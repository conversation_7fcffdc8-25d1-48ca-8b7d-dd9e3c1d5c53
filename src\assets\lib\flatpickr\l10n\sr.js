(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.sr = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Serbian = {
      weekdays: {
          shorthand: ["<PERSON>", "Pon", "<PERSON><PERSON>", "Sr<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sub"],
          longhand: [
              "Nedel<PERSON>",
              "Ponedeljak",
              "Utorak",
              "Sreda",
              "Četvrtak",
              "Petak",
              "Subota",
          ],
      },
      months: {
          shorthand: [
              "Jan",
              "Feb",
              "Mar",
              "Apr",
              "Maj",
              "Jun",
              "Jul",
              "Avg",
              "Sep",
              "Okt",
              "Nov",
              "Dec",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>",
              "April",
              "<PERSON>",
              "<PERSON>",
              "<PERSON>",
              "Avgus<PERSON>",
              "Sept<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>embar",
              "<PERSON>embar",
          ],
      },
      firstDayOfWeek: 1,
      weekAbbreviation: "Ned.",
      rangeSeparator: " do ",
      time_24hr: true,
  };
  fp.l10ns.sr = Serbian;
  var sr = fp.l10ns;

  exports.Serbian = Serbian;
  exports.default = sr;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
