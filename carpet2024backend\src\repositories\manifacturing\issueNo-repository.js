const IssueNo = require('../../model/phase-4/issueNo');

class IssueNoRepository {
  async create(issueNoData) {
    const issueNo = new IssueNo(issueNoData);
    return await issueNo.save();
  }

  async findAll() {
    return await IssueNo.find().populate('issueNo').populate('materialLagat').populate({
      path: 'issueNo_receive',
      populate: {
        path: 'noOfcolour',
      },
    });
  }

  async findById(id) {
    return await IssueNo.findById(id).populate('issueNo').populate('materialLagat').populate({
      path: 'issueNo_receive',
      populate: {
        path: 'noOfcolour',
      },
    });
  }

  async update(id, issueNoData) {
    return await IssueNo.findByIdAndUpdate(id, issueNoData, { new: true });
  }

  async delete(id) {
    return await IssueNo.findByIdAndDelete(id);
  }
}

module.exports = new IssueNoRepository();
