const Invoice = require('../../model/phase-4/invoice');

class InvoiceRepository {
    async createInvoice(data) {
        const invoice = new Invoice(data);
        return await invoice.save();
    }

    async getAllInvoices(sortBy = 'invoiceDate', order = 'asc') {
        const sortOrder = order === 'asc' ? 1 : -1; // Ascending or descending
        return await Invoice.find()
            .populate('consignee.name')       // Populates the consignee name (Buyer model)
            .populate('buyerOrderNo')         // Populates the buyerOrderNo (BuyerOrder model)
            .sort({ [sortBy]: sortOrder });   // Apply sorting
    }

    async getInvoiceById(id) {
        return await Invoice.findById(id)
            .populate('consignee.name')       // Populates the consignee name (Buyer model)
            .populate('buyerOrderNo');        // Populates the buyerOrderNo (BuyerOrder model)
    }

    async updateInvoice(id, data) {
        return await Invoice.findByIdAndUpdate(id, data, { new: true })
            .populate('consignee.name')       // Populates the consignee name (Buyer model)
            .populate('buyerOrderNo');        // Populates the buyerOrderNo (BuyerOrder model)
    }

    async deleteInvoice(id) {
        return await Invoice.findByIdAndDelete(id);
    }
}

module.exports = new InvoiceRepository();

