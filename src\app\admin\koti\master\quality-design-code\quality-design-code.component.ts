import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MatTableDataSource, } from '@angular/material/table';
import { AfterViewInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { ImporterService } from '../../../../services/importer.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import Swal from 'sweetalert2';
import { error } from 'console';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

export interface UserData {
  id: string;
  index: number;
  oldQualityandDesign: String;
  newQualityandDesign: String;
  qualityCodeandDesign: String;
  oldColor: String;
  newColor: String;
  colorCode: String;


}



const ELEMENT_DATA: UserData[] = [];


@Component({
  selector: 'app-quality-design-code',
  templateUrl: './quality-design-code.component.html',
  styleUrl: './quality-design-code.component.css'
})
export class QualityDesignCodeComponent implements OnInit {


  OldQualityAndDesign = 'option2'

  qualityList: any = [];
  designList: any = [];


  newAssignData: any = [];
  displayedColumns: string[] = ['id', 'oldQualityandDesign', 'newQualityandDesign', 'qualityCodeandDesign', 'oldColor', 'newColor', 'colorCode'];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);

  frmQualityDesign!: FormGroup;

  ngOnInit(): void {
    this.frmQualityDesign = this._fb.group({
      oldQualityandDesign: [],
      newQualityandDesign: [],
      qualityCodeandDesign: [],
      oldColor: [],
      newColor: [],
      colorCode: [],
    })

    this.getAllQualityDesignList()
    this.getExcelDataList();
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  // public dialogRef: MatDialogRef<QualityDesignCodeComponent>
  constructor(private _services: ImporterService, private _fb: FormBuilder,
    @Optional() public dialogRef: MatDialogRef<QualityDesignCodeComponent>,

    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  uniqueColoursArray: any = [];
  uniqueQDArray: any = [];
  getExcelDataList() {
    this._services.getAllExcelList('').subscribe((resp: any) => {
      debugger

      if (resp.length > 0) {
        resp.map((x: any) => {
          this.qualityList.push({
            qualityDesign: x.Quality + ' ' + x.Design,

          })
          this.designList.push({ colour: x.Colour })
        })

        const uniqueColours = new Set();
        this.designList.forEach((item: any) => {
          uniqueColours.add(item.colour);
        });
        this.uniqueColoursArray = Array.from(uniqueColours);

        const uniqueQD = new Set();
        this.qualityList.forEach((item: any) => {
          uniqueQD.add(item.qualityDesign);
        });
        this.uniqueQDArray = Array.from(uniqueQD);

      }

    })
  }


  addQualityDesign() {
    console.log(this.frmQualityDesign.value);
    let formData = this.frmQualityDesign.value;

    this.newAssignData.push(formData);
    this._services.addQualityDesign(formData).subscribe((resp: any) => {
      Swal.fire({
        title: "Success!",
        text: "Code has been assigned successfull!",
        icon: "success"
      });
      this.getAllQualityDesignList()
    }, (error) => {
      Swal.fire({
        title: "Warning!",
        text: "Something went wrong!",
        icon: "warning"
      });
    })

  }

  getAllQualityDesignList() {
    this._services.getAllQualityDesign().subscribe((resp: any) => {
      if (resp.length > 0) {
        ELEMENT_DATA.length = 0;
        resp.map((val: any, i: number) => {
          ELEMENT_DATA.push({
            id: val._id,
            index: i + 1,
            oldQualityandDesign: val.oldQualityandDesign,
            newQualityandDesign: val.newQualityandDesign,
            qualityCodeandDesign: val.qualityCodeandDesign,
            oldColor: val.oldColor,
            newColor: val.newColor,
            colorCode: val.colorCode,
          })
        })
        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.ngAfterViewInit();
        return;
      }
    })
  }


  closeDialogWithData(): void {
    this.dialogRef.close(this.newAssignData);
  }
}


