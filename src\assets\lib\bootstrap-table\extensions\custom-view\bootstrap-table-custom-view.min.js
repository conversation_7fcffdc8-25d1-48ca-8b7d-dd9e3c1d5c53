/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return c(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=f(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},p=function(t){return t&&t.Math==Math&&t},y=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof s&&s)||function(){return this}()||Function("return this")(),b={},d=function(t){try{return!!t()}catch(t){return!0}},h=!d((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),v=!d((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),g=v,m=Function.prototype.call,w=g?m.bind(m):function(){return m.apply(m,arguments)},O={},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,T=S&&!j.call({1:2},1);O.f=T?function(t){var e=S(this,t);return!!e&&e.enumerable}:j;var P,V,E=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},x=v,C=Function.prototype,A=C.call,D=x&&C.bind.bind(A,A),_=function(t){return x?D(t):function(){return A.apply(t,arguments)}},F=_,R=F({}.toString),M=F("".slice),k=function(t){return M(R(t),8,-1)},B=k,I=_,L=function(t){if("Function"===B(t))return I(t)},z=d,N=k,q=Object,G=L("".split),W=z((function(){return!q("z").propertyIsEnumerable(0)}))?function(t){return"String"==N(t)?G(t,""):q(t)}:q,$=function(t){return null==t},H=$,U=TypeError,X=function(t){if(H(t))throw U("Can't call method on "+t);return t},K=W,Q=X,Y=function(t){return K(Q(t))},J="object"==typeof document&&document.all,Z={all:J,IS_HTMLDDA:void 0===J&&void 0!==J},tt=Z.all,et=Z.IS_HTMLDDA?function(t){return"function"==typeof t||t===tt}:function(t){return"function"==typeof t},nt=et,rt=Z.all,ot=Z.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:nt(t)||t===rt}:function(t){return"object"==typeof t?null!==t:nt(t)},it=y,ut=et,ct=function(t){return ut(t)?t:void 0},at=function(t,e){return arguments.length<2?ct(it[t]):it[t]&&it[t][e]},ft=L({}.isPrototypeOf),lt=y,st=at("navigator","userAgent")||"",pt=lt.process,yt=lt.Deno,bt=pt&&pt.versions||yt&&yt.version,dt=bt&&bt.v8;dt&&(V=(P=dt.split("."))[0]>0&&P[0]<4?1:+(P[0]+P[1])),!V&&st&&(!(P=st.match(/Edge\/(\d+)/))||P[1]>=74)&&(P=st.match(/Chrome\/(\d+)/))&&(V=+P[1]);var ht=V,vt=ht,gt=d,mt=!!Object.getOwnPropertySymbols&&!gt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&vt&&vt<41})),wt=mt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Ot=at,jt=et,St=ft,Tt=Object,Pt=wt?function(t){return"symbol"==typeof t}:function(t){var e=Ot("Symbol");return jt(e)&&St(e.prototype,Tt(t))},Vt=String,Et=et,xt=function(t){try{return Vt(t)}catch(t){return"Object"}},Ct=TypeError,At=function(t){if(Et(t))return t;throw Ct(xt(t)+" is not a function")},Dt=At,_t=$,Ft=w,Rt=et,Mt=ot,kt=TypeError,Bt={exports:{}},It=y,Lt=Object.defineProperty,zt=function(t,e){try{Lt(It,t,{value:e,configurable:!0,writable:!0})}catch(n){It[t]=e}return e},Nt=zt,qt="__core-js_shared__",Gt=y[qt]||Nt(qt,{}),Wt=Gt;(Bt.exports=function(t,e){return Wt[t]||(Wt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var $t=X,Ht=Object,Ut=function(t){return Ht($t(t))},Xt=Ut,Kt=L({}.hasOwnProperty),Qt=Object.hasOwn||function(t,e){return Kt(Xt(t),e)},Yt=L,Jt=0,Zt=Math.random(),te=Yt(1..toString),ee=function(t){return"Symbol("+(void 0===t?"":t)+")_"+te(++Jt+Zt,36)},ne=y,re=Bt.exports,oe=Qt,ie=ee,ue=mt,ce=wt,ae=re("wks"),fe=ne.Symbol,le=fe&&fe.for,se=ce?fe:fe&&fe.withoutSetter||ie,pe=function(t){if(!oe(ae,t)||!ue&&"string"!=typeof ae[t]){var e="Symbol."+t;ue&&oe(fe,t)?ae[t]=fe[t]:ae[t]=ce&&le?le(e):se(e)}return ae[t]},ye=w,be=ot,de=Pt,he=function(t,e){var n=t[e];return _t(n)?void 0:Dt(n)},ve=function(t,e){var n,r;if("string"===e&&Rt(n=t.toString)&&!Mt(r=Ft(n,t)))return r;if(Rt(n=t.valueOf)&&!Mt(r=Ft(n,t)))return r;if("string"!==e&&Rt(n=t.toString)&&!Mt(r=Ft(n,t)))return r;throw kt("Can't convert object to primitive value")},ge=TypeError,me=pe("toPrimitive"),we=function(t,e){if(!be(t)||de(t))return t;var n,r=he(t,me);if(r){if(void 0===e&&(e="default"),n=ye(r,t,e),!be(n)||de(n))return n;throw ge("Can't convert object to primitive value")}return void 0===e&&(e="number"),ve(t,e)},Oe=Pt,je=function(t){var e=we(t,"string");return Oe(e)?e:e+""},Se=ot,Te=y.document,Pe=Se(Te)&&Se(Te.createElement),Ve=function(t){return Pe?Te.createElement(t):{}},Ee=Ve,xe=!h&&!d((function(){return 7!=Object.defineProperty(Ee("div"),"a",{get:function(){return 7}}).a})),Ce=h,Ae=w,De=O,_e=E,Fe=Y,Re=je,Me=Qt,ke=xe,Be=Object.getOwnPropertyDescriptor;b.f=Ce?Be:function(t,e){if(t=Fe(t),e=Re(e),ke)try{return Be(t,e)}catch(t){}if(Me(t,e))return _e(!Ae(De.f,t,e),t[e])};var Ie={},Le=h&&d((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),ze=ot,Ne=String,qe=TypeError,Ge=function(t){if(ze(t))return t;throw qe(Ne(t)+" is not an object")},We=h,$e=xe,He=Le,Ue=Ge,Xe=je,Ke=TypeError,Qe=Object.defineProperty,Ye=Object.getOwnPropertyDescriptor,Je="enumerable",Ze="configurable",tn="writable";Ie.f=We?He?function(t,e,n){if(Ue(t),e=Xe(e),Ue(n),"function"==typeof t&&"prototype"===e&&"value"in n&&tn in n&&!n.writable){var r=Ye(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Ze in n?n.configurable:r.configurable,enumerable:Je in n?n.enumerable:r.enumerable,writable:!1})}return Qe(t,e,n)}:Qe:function(t,e,n){if(Ue(t),e=Xe(e),Ue(n),$e)try{return Qe(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Ke("Accessors not supported");return"value"in n&&(t[e]=n.value),t};var en=Ie,nn=E,rn=h?function(t,e,n){return en.f(t,e,nn(1,n))}:function(t,e,n){return t[e]=n,t},on={exports:{}},un=h,cn=Qt,an=Function.prototype,fn=un&&Object.getOwnPropertyDescriptor,ln=cn(an,"name"),sn={EXISTS:ln,PROPER:ln&&"something"===function(){}.name,CONFIGURABLE:ln&&(!un||un&&fn(an,"name").configurable)},pn=et,yn=Gt,bn=L(Function.toString);pn(yn.inspectSource)||(yn.inspectSource=function(t){return bn(t)});var dn,hn,vn,gn=yn.inspectSource,mn=et,wn=y.WeakMap,On=mn(wn)&&/native code/.test(String(wn)),jn=Bt.exports,Sn=ee,Tn=jn("keys"),Pn=function(t){return Tn[t]||(Tn[t]=Sn(t))},Vn={},En=On,xn=y,Cn=ot,An=rn,Dn=Qt,_n=Gt,Fn=Pn,Rn=Vn,Mn="Object already initialized",kn=xn.TypeError,Bn=xn.WeakMap;if(En||_n.state){var In=_n.state||(_n.state=new Bn);In.get=In.get,In.has=In.has,In.set=In.set,dn=function(t,e){if(In.has(t))throw kn(Mn);return e.facade=t,In.set(t,e),e},hn=function(t){return In.get(t)||{}},vn=function(t){return In.has(t)}}else{var Ln=Fn("state");Rn[Ln]=!0,dn=function(t,e){if(Dn(t,Ln))throw kn(Mn);return e.facade=t,An(t,Ln,e),e},hn=function(t){return Dn(t,Ln)?t[Ln]:{}},vn=function(t){return Dn(t,Ln)}}var zn={set:dn,get:hn,has:vn,enforce:function(t){return vn(t)?hn(t):dn(t,{})},getterFor:function(t){return function(e){var n;if(!Cn(e)||(n=hn(e)).type!==t)throw kn("Incompatible receiver, "+t+" required");return n}}},Nn=d,qn=et,Gn=Qt,Wn=h,$n=sn.CONFIGURABLE,Hn=gn,Un=zn.enforce,Xn=zn.get,Kn=Object.defineProperty,Qn=Wn&&!Nn((function(){return 8!==Kn((function(){}),"length",{value:8}).length})),Yn=String(String).split("String"),Jn=on.exports=function(t,e,n){"Symbol("===String(e).slice(0,7)&&(e="["+String(e).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(e="get "+e),n&&n.setter&&(e="set "+e),(!Gn(t,"name")||$n&&t.name!==e)&&(Wn?Kn(t,"name",{value:e,configurable:!0}):t.name=e),Qn&&n&&Gn(n,"arity")&&t.length!==n.arity&&Kn(t,"length",{value:n.arity});try{n&&Gn(n,"constructor")&&n.constructor?Wn&&Kn(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=Un(t);return Gn(r,"source")||(r.source=Yn.join("string"==typeof e?e:"")),t};Function.prototype.toString=Jn((function(){return qn(this)&&Xn(this).source||Hn(this)}),"toString");var Zn=et,tr=Ie,er=on.exports,nr=zt,rr=function(t,e,n,r){r||(r={});var o=r.enumerable,i=void 0!==r.name?r.name:e;if(Zn(n)&&er(n,i,r),r.global)o?t[e]=n:nr(e,n);else{try{r.unsafe?t[e]&&(o=!0):delete t[e]}catch(t){}o?t[e]=n:tr.f(t,e,{value:n,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return t},or={},ir=Math.ceil,ur=Math.floor,cr=Math.trunc||function(t){var e=+t;return(e>0?ur:ir)(e)},ar=function(t){var e=+t;return e!=e||0===e?0:cr(e)},fr=ar,lr=Math.max,sr=Math.min,pr=function(t,e){var n=fr(t);return n<0?lr(n+e,0):sr(n,e)},yr=ar,br=Math.min,dr=function(t){return t>0?br(yr(t),9007199254740991):0},hr=function(t){return dr(t.length)},vr=Y,gr=pr,mr=hr,wr=function(t){return function(e,n,r){var o,i=vr(e),u=mr(i),c=gr(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},Or={includes:wr(!0),indexOf:wr(!1)},jr=Qt,Sr=Y,Tr=Or.indexOf,Pr=Vn,Vr=L([].push),Er=function(t,e){var n,r=Sr(t),o=0,i=[];for(n in r)!jr(Pr,n)&&jr(r,n)&&Vr(i,n);for(;e.length>o;)jr(r,n=e[o++])&&(~Tr(i,n)||Vr(i,n));return i},xr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Cr=Er,Ar=xr.concat("length","prototype");or.f=Object.getOwnPropertyNames||function(t){return Cr(t,Ar)};var Dr={};Dr.f=Object.getOwnPropertySymbols;var _r=at,Fr=or,Rr=Dr,Mr=Ge,kr=L([].concat),Br=_r("Reflect","ownKeys")||function(t){var e=Fr.f(Mr(t)),n=Rr.f;return n?kr(e,n(t)):e},Ir=Qt,Lr=Br,zr=b,Nr=Ie,qr=d,Gr=et,Wr=/#|\.prototype\./,$r=function(t,e){var n=Ur[Hr(t)];return n==Kr||n!=Xr&&(Gr(e)?qr(e):!!e)},Hr=$r.normalize=function(t){return String(t).replace(Wr,".").toLowerCase()},Ur=$r.data={},Xr=$r.NATIVE="N",Kr=$r.POLYFILL="P",Qr=$r,Yr=y,Jr=b.f,Zr=rn,to=rr,eo=zt,no=function(t,e,n){for(var r=Lr(e),o=Nr.f,i=zr.f,u=0;u<r.length;u++){var c=r[u];Ir(t,c)||n&&Ir(n,c)||o(t,c,i(e,c))}},ro=Qr,oo=function(t,e){var n,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(n=a?Yr:f?Yr[c]||eo(c,{}):(Yr[c]||{}).prototype)for(r in e){if(i=e[r],o=t.dontCallGetSet?(u=Jr(n,r))&&u.value:n[r],!ro(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;no(i,o)}(t.sham||o&&o.sham)&&Zr(i,"sham",!0),to(n,r,i,t)}},io=Er,uo=xr,co=Object.keys||function(t){return io(t,uo)},ao=h,fo=L,lo=w,so=d,po=co,yo=Dr,bo=O,ho=Ut,vo=W,go=Object.assign,mo=Object.defineProperty,wo=fo([].concat),Oo=!go||so((function(){if(ao&&1!==go({b:1},go(mo({},"a",{enumerable:!0,get:function(){mo(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!=go({},t)[n]||po(go({},e)).join("")!=r}))?function(t,e){for(var n=ho(t),r=arguments.length,o=1,i=yo.f,u=bo.f;r>o;)for(var c,a=vo(arguments[o++]),f=i?wo(po(a),i(a)):po(a),l=f.length,s=0;l>s;)c=f[s++],ao&&!lo(u,a,c)||(n[c]=a[c]);return n}:go,jo=Oo;oo({target:"Object",stat:!0,arity:2,forced:Object.assign!==jo},{assign:jo});var So=k,To=Array.isArray||function(t){return"Array"==So(t)},Po=TypeError,Vo=je,Eo=Ie,xo=E,Co=function(t,e,n){var r=Vo(e);r in t?Eo.f(t,r,xo(0,n)):t[r]=n},Ao={};Ao[pe("toStringTag")]="z";var Do="[object z]"===String(Ao),_o=Do,Fo=et,Ro=k,Mo=pe("toStringTag"),ko=Object,Bo="Arguments"==Ro(function(){return arguments}()),Io=_o?Ro:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=ko(t),Mo))?n:Bo?Ro(e):"Object"==(r=Ro(e))&&Fo(e.callee)?"Arguments":r},Lo=L,zo=d,No=et,qo=Io,Go=gn,Wo=function(){},$o=[],Ho=at("Reflect","construct"),Uo=/^\s*(?:class|function)\b/,Xo=Lo(Uo.exec),Ko=!Uo.exec(Wo),Qo=function(t){if(!No(t))return!1;try{return Ho(Wo,$o,t),!0}catch(t){return!1}},Yo=function(t){if(!No(t))return!1;switch(qo(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Ko||!!Xo(Uo,Go(t))}catch(t){return!0}};Yo.sham=!0;var Jo=!Ho||zo((function(){var t;return Qo(Qo.call)||!Qo(Object)||!Qo((function(){t=!0}))||t}))?Yo:Qo,Zo=To,ti=Jo,ei=ot,ni=pe("species"),ri=Array,oi=function(t){var e;return Zo(t)&&(e=t.constructor,(ti(e)&&(e===ri||Zo(e.prototype))||ei(e)&&null===(e=e[ni]))&&(e=void 0)),void 0===e?ri:e},ii=function(t,e){return new(oi(t))(0===e?0:e)},ui=d,ci=ht,ai=pe("species"),fi=function(t){return ci>=51||!ui((function(){var e=[];return(e.constructor={})[ai]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},li=oo,si=d,pi=To,yi=ot,bi=Ut,di=hr,hi=function(t){if(t>9007199254740991)throw Po("Maximum allowed index exceeded");return t},vi=Co,gi=ii,mi=fi,wi=ht,Oi=pe("isConcatSpreadable"),ji=wi>=51||!si((function(){var t=[];return t[Oi]=!1,t.concat()[0]!==t})),Si=mi("concat"),Ti=function(t){if(!yi(t))return!1;var e=t[Oi];return void 0!==e?!!e:pi(t)};li({target:"Array",proto:!0,arity:1,forced:!ji||!Si},{concat:function(t){var e,n,r,o,i,u=bi(this),c=gi(u,0),a=0;for(e=-1,r=arguments.length;e<r;e++)if(Ti(i=-1===e?u:arguments[e]))for(o=di(i),hi(a+o),n=0;n<o;n++,a++)n in i&&vi(c,a,i[n]);else hi(a+1),vi(c,a++,i);return c.length=a,c}});var Pi=At,Vi=v,Ei=L(L.bind),xi=function(t,e){return Pi(t),void 0===e?t:Vi?Ei(t,e):function(){return t.apply(e,arguments)}},Ci=W,Ai=Ut,Di=hr,_i=ii,Fi=L([].push),Ri=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,l,s){for(var p,y,b=Ai(a),d=Ci(b),h=xi(f,l),v=Di(d),g=0,m=s||_i,w=e?m(a,v):n||u?m(a,0):void 0;v>g;g++)if((c||g in d)&&(y=h(p=d[g],g,b),t))if(e)w[g]=y;else if(y)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:Fi(w,p)}else switch(t){case 4:return!1;case 7:Fi(w,p)}return i?-1:r||o?o:w}},Mi={forEach:Ri(0),map:Ri(1),filter:Ri(2),some:Ri(3),every:Ri(4),find:Ri(5),findIndex:Ri(6),filterReject:Ri(7)},ki={},Bi=h,Ii=Le,Li=Ie,zi=Ge,Ni=Y,qi=co;ki.f=Bi&&!Ii?Object.defineProperties:function(t,e){zi(t);for(var n,r=Ni(e),o=qi(e),i=o.length,u=0;i>u;)Li.f(t,n=o[u++],r[n]);return t};var Gi,Wi=at("document","documentElement"),$i=Ge,Hi=ki,Ui=xr,Xi=Vn,Ki=Wi,Qi=Ve,Yi=Pn("IE_PROTO"),Ji=function(){},Zi=function(t){return"<script>"+t+"</"+"script>"},tu=function(t){t.write(Zi("")),t.close();var e=t.parentWindow.Object;return t=null,e},eu=function(){try{Gi=new ActiveXObject("htmlfile")}catch(t){}var t,e;eu="undefined"!=typeof document?document.domain&&Gi?tu(Gi):((e=Qi("iframe")).style.display="none",Ki.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Zi("document.F=Object")),t.close(),t.F):tu(Gi);for(var n=Ui.length;n--;)delete eu.prototype[Ui[n]];return eu()};Xi[Yi]=!0;var nu=pe,ru=Object.create||function(t,e){var n;return null!==t?(Ji.prototype=$i(t),n=new Ji,Ji.prototype=null,n[Yi]=t):n=eu(),void 0===e?n:Hi.f(n,e)},ou=Ie.f,iu=nu("unscopables"),uu=Array.prototype;null==uu[iu]&&ou(uu,iu,{configurable:!0,value:ru(null)});var cu=oo,au=Mi.find,fu=function(t){uu[iu][t]=!0},lu="find",su=!0;lu in[]&&Array(1).find((function(){su=!1})),cu({target:"Array",proto:!0,forced:su},{find:function(t){return au(this,t,arguments.length>1?arguments[1]:void 0)}}),fu(lu);var pu=Io,yu=Do?{}.toString:function(){return"[object "+pu(this)+"]"};Do||rr(Object.prototype,"toString",yu,{unsafe:!0});var bu=L([].slice),du=oo,hu=To,vu=Jo,gu=ot,mu=pr,wu=hr,Ou=Y,ju=Co,Su=pe,Tu=bu,Pu=fi("slice"),Vu=Su("species"),Eu=Array,xu=Math.max;du({target:"Array",proto:!0,forced:!Pu},{slice:function(t,e){var n,r,o,i=Ou(this),u=wu(i),c=mu(t,u),a=mu(void 0===e?u:e,u);if(hu(i)&&(n=i.constructor,(vu(n)&&(n===Eu||hu(n.prototype))||gu(n)&&null===(n=n[Vu]))&&(n=void 0),n===Eu||void 0===n))return Tu(i,c,a);for(r=new(void 0===n?Eu:n)(xu(a-c,0)),o=0;c<a;c++,o++)c in i&&ju(r,o,i[c]);return r.length=o,r}});var Cu=n.default.fn.bootstrapTable.utils;n.default.extend(n.default.fn.bootstrapTable.defaults,{customView:!1,showCustomView:!1,customViewDefaultView:!1}),n.default.extend(n.default.fn.bootstrapTable.defaults.icons,{customView:{bootstrap3:"glyphicon glyphicon-eye-open",bootstrap5:"bi-eye",bootstrap4:"fa fa-eye",semantic:"fa fa-eye",foundation:"fa fa-eye",bulma:"fa fa-eye",materialize:"remove_red_eye"}[n.default.fn.bootstrapTable.theme]||"fa-eye"}),n.default.extend(n.default.fn.bootstrapTable.defaults,{onCustomViewPostBody:function(){return!1},onCustomViewPreBody:function(){return!1},onToggleCustomView:function(){return!1}}),n.default.extend(n.default.fn.bootstrapTable.locales,{formatToggleCustomView:function(){return"Toggle custom view"}}),n.default.extend(n.default.fn.bootstrapTable.defaults,n.default.fn.bootstrapTable.locales),n.default.fn.bootstrapTable.methods.push("toggleCustomView"),n.default.extend(n.default.fn.bootstrapTable.Constructor.EVENTS,{"custom-view-post-body.bs.table":"onCustomViewPostBody","custom-view-pre-body.bs.table":"onCustomViewPreBody","toggle-custom-view.bs.table":"onToggleCustomView"}),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(s,t);var e,n,c,f=a(s);function s(){return r(this,s),f.apply(this,arguments)}return e=s,n=[{key:"init",value:function(){this.customViewDefaultView=this.options.customViewDefaultView,l(i(s.prototype),"init",this).call(this)}},{key:"initToolbar",value:function(){var t;this.options.customView&&this.options.showCustomView&&(this.buttons=Object.assign(this.buttons,{customView:{text:this.options.formatToggleCustomView(),icon:this.options.icons.customView,event:this.toggleCustomView,attributes:{"aria-label":this.options.formatToggleCustomView(),title:this.options.formatToggleCustomView()}}}));for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=l(i(s.prototype),"initToolbar",this)).call.apply(t,[this].concat(n))}},{key:"initBody",value:function(){if(l(i(s.prototype),"initBody",this).call(this),this.options.customView){var t=this.$el,e=this.$container.find(".fixed-table-custom-view");if(t.hide(),e.hide(),this.options.customView&&this.customViewDefaultView){var n=this.getData().slice(this.pageFrom-1,this.pageTo),r=Cu.calculateObjectValue(this,this.options.customView,[n],"");this.trigger("custom-view-pre-body",n,r),1===e.length?e.show().html(r):this.$tableBody.after('<div class="fixed-table-custom-view">'.concat(r,"</div>")),this.trigger("custom-view-post-body",n,r)}else t.show()}}},{key:"toggleCustomView",value:function(){this.customViewDefaultView=!this.customViewDefaultView,this.initBody(),this.trigger("toggle-custom-view",this.customViewDefaultView)}}],n&&o(e.prototype,n),c&&o(e,c),Object.defineProperty(e,"prototype",{writable:!1}),s}(n.default.BootstrapTable)}));
