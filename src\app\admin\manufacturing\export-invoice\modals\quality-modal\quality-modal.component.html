<div class="quality-modal-container">
  <h2 mat-dialog-title class="modal-title">Quality Management</h2>

  <mat-dialog-content class="modal-content">
    <form [formGroup]="qualityForm">
      <div class="quality-comparison-container">

     

        <!-- Left Side: Old Quality Display -->
        <div class="old-quality-section">
          <mat-card class="section-card">
            <mat-card-header>
              <mat-card-title class="section-title">Current Packing Quality</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="old-quality-table-container">
                <table mat-table [dataSource]="oldQualityData" class="old-quality-table">

                  <!-- Quality Column -->
                  <ng-container matColumnDef="quality">
                    <th mat-header-cell *matHeaderCellDef>Quality</th>
                    <td mat-cell *matCellDef="let element">{{element.quality || 'N/A'}}</td>
                  </ng-container>

                  <!-- Design Column -->
                  <ng-container matColumnDef="design">
                    <th mat-header-cell *matHeaderCellDef>Design</th>
                    <td mat-cell *matCellDef="let element">{{element.design || 'N/A'}}</td>
                  </ng-container>

                  <!-- Color Column -->
                  <ng-container matColumnDef="color">
                    <th mat-header-cell *matHeaderCellDef>Color</th>
                    <td mat-cell *matCellDef="let element">{{element.color || 'N/A'}}</td>
                  </ng-container>

                  <!-- Size Column -->
                  <ng-container matColumnDef="size">
                    <th mat-header-cell *matHeaderCellDef>Size</th>
                    <td mat-cell *matCellDef="let element">{{element.size || 'N/A'}}</td>
                  </ng-container>

                  <!-- Pieces Column -->
                  <ng-container matColumnDef="pieces">
                    <th mat-header-cell *matHeaderCellDef>Pieces</th>
                    <td mat-cell *matCellDef="let element">{{element.pieces || 0}}</td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="['quality', 'design', 'color', 'size', 'pieces']"></tr>
                  <tr mat-row *matRowDef="let row; columns: ['quality', 'design', 'color', 'size', 'pieces'];"></tr>
                </table>

                <!-- No data message -->
                <div *ngIf="oldQualityData.length === 0" class="no-data-message">
                  <p>No packing quality data available</p>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

           <!-- Right Side: New Quality Inputs -->
        <div class="new-quality-section">
          <mat-card class="section-card">
            <mat-card-header>
              <mat-card-title class="section-title">New Quality</mat-card-title>
            </mat-card-header>
            <mat-card-content class="mt-3">
              <div class="quality-inputs" formArrayName="newQualities">
                <div *ngFor="let control of newQualities.controls; let i = index" class="quality-input-row" [formGroupName]="i">
                  <div class="input-group">
                    <mat-form-field appearance="outline" class="quality-input">
                      <mat-label>New Quality {{i + 1}}</mat-label>
                      <input matInput formControlName="quality" placeholder="Enter new quality">
                    </mat-form-field>
                    <mat-form-field appearance="outline" class="design-input">
                      <mat-label>New Design {{i + 1}}</mat-label>
                      <input matInput formControlName="design" placeholder="Enter new design">
                    </mat-form-field>
                  </div>
                  <button
                    mat-icon-button
                    color="warn"
                    type="button"
                    (click)="removeQualityRow(i)"
                    *ngIf="newQualities.length > 1"
                    class="remove-btn">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>

              <div class="add-quality-section">
                <button
                  mat-raised-button
                  color="primary"
                  type="button"
                  (click)="addNewQualityRow()"
                  class="add-btn">
                  <mat-icon>add</mat-icon>
                  Add Quality
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end" class="modal-actions">
    <button mat-button (click)="onCancel()" class="cancel-btn">Cancel</button>
    <button mat-raised-button color="primary" (click)="onSave()" class="save-btn">Save Changes</button>
  </mat-dialog-actions>
</div>