// Script to import missing H- format records that failed due to duplicates
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

// Missing records that failed due to duplicates
const missingRecords = [
  { sNo: 77, issueNo: 'H-210977', weaverName: 'Stock March 2044', date: '04-01-2024', quality: '7 X 52', design: 'MIR', colour: 'Cream', colour2: 'Brown', sizeInYard: '210 X 51', pcs: 1, area: '1.60', carpetNo: 'H-210977', rate: 265 },
  { sNo: 267, issueNo: 'H-220003', weaverName: 'Stock March 2234', date: '04-01-2024', quality: '12 X 60', design: 'MUGHAL EMPIRE', colour: 'Cream', colour2: 'Cream', sizeInYard: '6 6 X 6 6', pcs: 1, area: '4.69', carpetNo: 'H-220003', rate: 1215 },
  { sNo: 270, issueNo: 'H-220009', weaverName: 'Stock March 2237', date: '04-01-2024', quality: '12 X 60', design: 'MUGHAL EMPIRE', colour: 'Cream', colour2: 'Cream', sizeInYard: '6 6 X 6 6', pcs: 1, area: '4.69', carpetNo: 'H-220009', rate: 1230 },
  { sNo: 271, issueNo: 'H-220010', weaverName: 'Stock March 2238', date: '04-01-2024', quality: '12 X 60', design: 'MUGHAL EMPIRE', colour: 'Cream', colour2: 'Cream', sizeInYard: '6 6 X 6 6', pcs: 1, area: '4.69', carpetNo: 'H-220010', rate: 1235 },
  { sNo: 277, issueNo: 'H-220005', weaverName: 'Stock March 2244', date: '04-01-2024', quality: '12 X 60', design: 'MUGHAL EMPIRE', colour: 'Cream', colour2: 'Cream', sizeInYard: '6 6 X 6 6', pcs: 1, area: '4.69', carpetNo: 'H-220005', rate: 1265 },
  { sNo: 279, issueNo: 'H-170003', weaverName: 'Stock March 2246', date: '04-01-2024', quality: '12 X 60', design: 'SONAM BHAKTRI', colour: 'Cream', colour2: 'Cream', sizeInYard: '2 7 X 9 9', pcs: 1, area: '2.80', carpetNo: 'H-170003', rate: 1275 },
  { sNo: 302, issueNo: 'H-180067', weaverName: 'Stock March 2269', date: '04-01-2024', quality: '12 X 60', design: 'SONAM BHAKTRI', colour: 'Cream', colour2: 'Cream', sizeInYard: '2 7 X 9 9', pcs: 1, area: '2.80', carpetNo: 'H-180067', rate: 1390 },
  { sNo: 314, issueNo: 'H-180033', weaverName: 'Stock March 2281', date: '04-01-2024', quality: '12 X 60', design: 'SONAM BHAKTRI', colour: 'Cream', colour2: 'Cream', sizeInYard: '2 7 X 9 9', pcs: 1, area: '2.80', carpetNo: 'H-180033', rate: 1450 },
  { sNo: 322, issueNo: 'H-180034', weaverName: 'Stock March 2289', date: '04-01-2024', quality: '12 X 60', design: 'SONAM BHAKTRI', colour: 'Cream', colour2: 'Cream', sizeInYard: '2 7 X 9 9', pcs: 1, area: '2.80', carpetNo: 'H-180034', rate: 1490 },
  { sNo: 334, issueNo: 'H-180291', weaverName: 'Stock March 2301', date: '04-01-2024', quality: '12 X 60', design: 'SONAM BHAKTRI', colour: 'Cream', colour2: 'Cream', sizeInYard: '2 7 X 9 9', pcs: 1, area: '2.80', carpetNo: 'H-180291', rate: 1550 },
  { sNo: 366, issueNo: 'H-170060', weaverName: 'Stock March 2390', date: '04-01-2024', quality: '12 X 60', design: 'SONAM BHAKTRI', colour: 'Cream', colour2: 'Cream', sizeInYard: '2 7 X 9 9', pcs: 1, area: '2.80', carpetNo: 'H-170060', rate: 1990 }
];

async function importMissingRecords() {
  console.log('🔄 IMPORTING MISSING H- FORMAT RECORDS');
  console.log('='.repeat(80));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    console.log(`📊 Importing ${missingRecords.length} missing H- format records...`);
    
    const results = { success: [], errors: [] };
    
    // Get H- branch (Head Office) and sample weaver ID
    const branchesCollection = db.collection('branches');
    const hBranch = await branchesCollection.findOne({ branchCode: 'H' });
    const branchId = hBranch?._id || new mongoose.Types.ObjectId('66dea018ce4353ab90095193');

    // Get a sample weaver ID
    const sampleKRecord = await collection.findOne({ receiveNo: { $regex: /^K-/ } });
    const weaverId = sampleKRecord?.weaverNumber || new mongoose.Types.ObjectId();
    
    // Import each missing H- record
    for (let i = 0; i < missingRecords.length; i++) {
      try {
        const record = missingRecords[i];
        
        // Calculate rate and amount
        const areaValue = parseFloat(record.area);
        const rate = record.rate;
        const amount = Math.round(areaValue * rate);
        
        // Create proper issueNo object
        const issueNoObject = {
          Br_issueNo: record.issueNo,
          date: new Date(record.date.split('-').reverse().join('-')).toISOString(),
          quality: { quality: record.quality },
          design: { design: record.design },
          borderColour: `${record.colour}/${record.colour2}`,
          size: { 
            sizeInYard: record.sizeInYard, 
            sizeinMeter: record.sizeInYard
          },
          rate: rate.toString(),
          amount: amount.toString(),
          areaIn: 'Sq.Ft'
        };

        // Create carpet record
        const carpetRecord = {
          K: branchId,
          receivingDate: new Date(record.date.split('-').reverse().join('-')),
          issueNo: issueNoObject,
          weaverNumber: weaverId,
          receiveNo: record.carpetNo,
          area: `${record.area} Ft`,
          amount: amount.toString(),
          pcs: record.pcs,
          weaverName: record.weaverName,
          quality: record.quality,
          design: record.design,
          colour: record.colour,
          colour2: record.colour2,
          size: record.sizeInYard,
          carpetNo: record.carpetNo,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Replace existing record or insert new one
        await collection.replaceOne(
          { receiveNo: record.carpetNo },
          carpetRecord,
          { upsert: true }
        );
        
        results.success.push({
          receiveNo: record.carpetNo,
          issueNo: record.issueNo,
          weaverName: record.weaverName,
          quality: record.quality,
          design: record.design,
          area: record.area,
          rate: rate,
          amount: amount
        });
        
        console.log(`✅ Imported/Updated ${i + 1}/${missingRecords.length}: ${record.carpetNo}`);
        
      } catch (error) {
        console.error(`❌ Error importing record ${i + 1}:`, error.message);
        results.errors.push({
          record: missingRecords[i]?.issueNo || `Record ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error importing missing H- format records:', error);
    return { success: [], errors: [] };
  }
}

async function main() {
  console.log('🚀 IMPORTING MISSING H- FORMAT RECORDS');
  console.log('='.repeat(80));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) {
      console.log('❌ Database connection failed, exiting...');
      return;
    }

    // Import missing H- format records
    const results = await importMissingRecords();

    // Display results
    console.log('\n' + '='.repeat(80));
    console.log('📊 MISSING H- FORMAT RECORDS IMPORT COMPLETE');
    console.log('='.repeat(80));
    console.log(`✅ Successfully imported/updated: ${results.success.length} H- records`);
    console.log(`❌ Failed: ${results.errors.length} H- records`);
    
    if (results.success.length > 0) {
      console.log('\n✅ IMPORTED/UPDATED RECORDS:');
      results.success.forEach(record => {
        console.log(`  - ${record.receiveNo}: ${record.weaverName} | ${record.design} | ₹${record.amount}`);
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.forEach(error => {
        console.log(`  - ${error.record}: ${error.error}`);
      });
    }

  } catch (error) {
    console.error('❌ Import failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    console.log('🎉 MISSING H- FORMAT RECORDS IMPORT COMPLETE!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
