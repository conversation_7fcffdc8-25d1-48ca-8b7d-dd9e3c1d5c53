import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AuthServicesService } from '../../services/auth-services.service';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrl: './login.component.css',
})
export class LoginComponent implements OnInit {
  frmLogin!: FormGroup;
  constructor(
    private fb: FormBuilder,
    private authService: AuthServicesService,
    private route: Router
  ) {}
  ngOnInit(): void {
    this.frmLogin = this.fb.group({
      email: [],
      password: [],
    });
  }
  login() {
    // debugger;
    let frmData = this.frmLogin.value;
    this.authService.login(frmData).subscribe({
      next: (value) => {
        Swal.fire({
          title: 'Success',
          text: 'Login successfull',
          icon: 'success',
        });
        this.route.navigate(['/admin']);
      },
      error: (err) => {
        Swal.fire({
          title: 'Failed',
          text: 'Something went wrong',
          icon: 'warning',
        });
      },
    });
  }
}
