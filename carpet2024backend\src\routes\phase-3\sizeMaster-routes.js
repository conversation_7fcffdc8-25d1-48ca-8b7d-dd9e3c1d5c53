const express = require('express');
const sizeController = require('../../controller/sizeMaster-controller');
const uploadFiles = require('../../middleware/allfile-middleware');
const router = express.Router();

router.post('/create-sizes', uploadFiles, (req, res) => sizeController.createSize(req, res));
router.put('/update-sizes/:id', uploadFiles, (req, res) => sizeController.updateSize(req, res));
router.get('/get-sizes/:id', (req, res) => sizeController.getSizeById(req, res));
router.get('/getAll-sizes', (req, res) => sizeController.getAllSizes(req, res));
router.delete('/delete-sizes/:id', (req, res) => sizeController.deleteSize(req, res));

module.exports = router;
