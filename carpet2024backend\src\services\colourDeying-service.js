const colourcodeDeyingRepository = require('../repositories/colourDeying-repository');

const createColourcodeDeying = async (colourcodeDeyingData) => {
  return await colourcodeDeyingRepository.createColourcodeDeying(colourcodeDeyingData);
};

const getAllColourcodeDeyings = async () => {
  return await colourcodeDeyingRepository.getAllColourcodeDeyings();
};

const getColourcodeDeyingById = async (id) => {
  return await colourcodeDeyingRepository.getColourcodeDeyingById(id);
};

const updateColourcodeDeying = async (id, colourcodeDeyingData) => {
  return await colourcodeDeyingRepository.updateColourcodeDeying(id, colourcodeDeyingData);
};

const deleteColourcodeDeying = async (id) => {
  return await colourcodeDeyingRepository.deleteColourcodeDeying(id);
};

const updateField = async (id, chemicalIndex, field, value) => {
  const update = {
    [`chemicals.${chemicalIndex}.${field}`]: value
  };
  return await colourcodeDeyingRepository.updateField(id, update);
};

const deleteField = async (id, chemicalIndex, field) => {
  return await colourcodeDeyingRepository.deleteField(id, chemicalIndex, field);
};

module.exports = {
  createColourcodeDeying,
  getAllColourcodeDeyings,
  getColourcodeDeyingById,
  updateColourcodeDeying,
  deleteColourcodeDeying,
  updateField,
  deleteField
};
