const Carpet = require('../model/phase-1/carpet');

class CarpetRepository {
  async createCarpet(carpetData) {
    try {
      const carpet = new Carpet(carpetData);
      await carpet.save();
      return carpet;
    } catch (error) {
      throw error;
    }
  }

  async getCarpetById(carpetId) {
    try {
      const carpet = await Carpet.findById(carpetId);
      return carpet;
    } catch (error) {
      throw error;
    }
  }

  async getAllCarpets() {
    try {
      const carpets = await Carpet.find();
      return carpets;
    } catch (error) {
      throw error;
    }
  }

  async updateCarpet(carpetId, carpetData) {
    try {
      const carpet = await Carpet.findByIdAndUpdate(carpetId, carpetData, { new: true });
      return carpet;
    } catch (error) {
      throw error;
    }
  }

  async deleteCarpet(carpetId) {
    try {
      const carpet = await Carpet.findByIdAndDelete(carpetId);
      return carpet;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new CarpetRepository();
