import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthComponent } from './auth/auth.component';
import { AdminComponent } from './admin/admin.component';
import { NotfoundComponent } from './notfound/notfound.component';
import { MyStandaloneComponentComponent } from './my-standalone-component/my-standalone-component.component';
import { ViewCarpetRecivingComponent } from './admin/manufacturing/view-carpet-reciving/view-carpet-reciving.component';


const routes: Routes = [
  {
    path: '',
    component: AuthComponent,
    loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule) // Lazy loading feature module
   },
   {
    path: 'admin',
    component: AdminComponent,
    loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule),data:{title:'Admin'} // Lazy loading feature module
   },
   {
    path:"my/:id",component:MyStandaloneComponentComponent
   },
   {
    path: '**', component: NotfoundComponent, pathMatch:'full'

   },
   { path: 'admin/view-carpet-reciving/:id', component: ViewCarpetRecivingComponent }

  

];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
