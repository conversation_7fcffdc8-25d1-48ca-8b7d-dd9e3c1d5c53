<div class="navbar-custom">
    <div class="topbar">
        <div class="topbar-menu d-flex align-items-center gap-lg-2 gap-1">

            <!-- Brand Logo -->
            <!-- <div class="logo-box"> -->
                <!-- Brand Logo Light -->
                <!-- <a routerLink="" class="logo-light">
                    <img src="../../assets/images/logo-light.png" alt="logo" class="logo-lg" height="22">
                    <img src="../../assets/images/logo-sm.png" alt="small logo" class="logo-sm" height="22">
                </a> -->

                <!-- Brand Logo Dark -->
                <!-- <a routerLink="" class="logo-dark">
                    <img src="../../assets/images/logo-dark.png" alt="dark logo" class="logo-lg" height="22">
                    <img src="../../assets/images/logo-sm.png" alt="small logo" class="logo-sm" height="22">
                </a>
            </div> -->

            <!-- Sidebar Menu Toggle Button -->
            <button class="button-toggle-menu">
                <i class="mdi mdi-menu" (click)="toggleSideBar()"></i>
            </button>



                <!-- <span>
              <a [routerLink]="['/admin']" routerLinkActive="active-link">Dashboard</a>


              <span *ngIf="currentModule && currentModule.data && currentModule.data.title">
                /
                <a [routerLink]="['/admin', currentModule.path]" routerLinkActive="active-link">
                  {{ currentModule.data.title }}
                </a>
              </span>
            </span> -->

        </div>

        <ul class="topbar-menu d-flex align-items-center gap-4">
          <!-- <li class="d-none d-md-inline-block">
            <a (click)="goBack()" title="Goto Back">
                <mat-icon aria-hidden="false" [ngStyle]="{'color':'#1a1a50'}" aria-label="Example keyboard_backspace icon" fontIcon="keyboard_backspace"></mat-icon>

                </a>
        </li> -->
            <!-- <li class="d-none d-md-inline-block">
                <a (click)="refresh()" title="Refresh"><mat-icon aria-hidden="false" [ngStyle]="{'color':'#1a1a50'}" aria-label="Example autorenew icon" fontIcon="autorenew"></mat-icon></a>

            </li> -->

            <li class="d-none d-md-inline-block">
                <a class="nav-link" href="" data-bs-toggle="fullscreen" title="Full screen">
                    <i class="mdi mdi-fullscreen font-size-24"></i>
                </a>
            </li>


            <li class="dropdown">
                <a class="nav-link dropdown-toggle waves-effect waves-light arrow-none" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="false" aria-expanded="false">
                    <i class="mdi mdi-magnify font-size-24" title="Search"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-animated dropdown-menu-end dropdown-lg p-0">
                    <form class="p-3">
                        <input type="search" class="form-control" placeholder="Search ..." aria-label="Recipient's username">
                    </form>
                </div>
            </li>


            <li class="dropdown d-none d-md-inline-block">
                <a class="nav-link dropdown-toggle waves-effect waves-light arrow-none" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="false" aria-expanded="false">
                    <img src="../../assets/images/flags/us.jpg" alt="user-image" class="me-0 me-sm-1" height="18">
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated">

                    <!-- item-->
                    <a href="javascript:void(0);" class="dropdown-item">
                        <img src="../../assets/images/flags/germany.jpg" alt="user-image" class="me-1" height="12"> <span class="align-middle">German</span>
                    </a>



                </div>
            </li>


            <!-- <li class="nav-link" id="theme-mode" (click)="toggleTheme()" style="cursor: pointer;" title="Toggle Theme">
                <i class="bx font-size-24" [ngClass]="isDarkTheme ? 'bx-sun' : 'bx-moon'"></i>
            </li> -->

            <li class="dropdown">
                <a class="nav-link dropdown-toggle nav-user me-0 waves-effect waves-light" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="false" aria-expanded="false">
                    <img src="../../assets/images/users/avatar-4.jpg" alt="user-image" class="rounded-circle">
                    <span class="ms-1 d-none d-md-inline-block">
                        Anurag K. <i class="mdi mdi-chevron-down"></i>
                    </span>
                </a>
                <div class="dropdown-menu dropdown-menu-end profile-dropdown ">
                    <!-- item-->
                    <div class="dropdown-header noti-title">
                        <h6 class="text-overflow m-0">Welcome Admin!</h6>
                    </div>

                    <!-- item-->
                    <a href="javascript:void(0);" class="dropdown-item notify-item">
                        <i class="fe-user"></i>
                        <span>My Account</span>
                    </a>

                    <!-- item-->
                    <a href="javascript:void(0);" class="dropdown-item notify-item">
                        <i class="fe-settings"></i>
                        <span>Settings</span>
                    </a>


                    <div class="dropdown-divider"></div>

                    <!-- item-->
                    <a routerLink="" class="dropdown-item notify-item">
                        <i class="fe-log-out"></i>
                        <span>Logout</span>
                    </a>

                </div>
            </li>

        </ul>
    </div>
</div>
