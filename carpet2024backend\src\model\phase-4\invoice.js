const mongoose = require('mongoose');

// Create schema for the invoice
const invoiceSchema = new mongoose.Schema({
 
    exporter: {
        name: { 
            type: String, 
            default: 'M/S Rachin Exports',               // Fixed exporter name
            required: true 
        },
        
        address: { 
            type: String, 
            default: 'Mad<PERSON>ing<PERSON>, Santravidas Nagar, Dist Bhadohi, U.P. India', // Fixed exporter address
            required: true 
        }
    },

    consignee: {
        name: { type: mongoose.Schema.Types.ObjectId, 
         ref:'Buyer'   ,
            required: true },          // Consignee name (e.g., Keshari Orient Teppiche Import GMBH)
        address: { type: String, required: true },       // Consignee address (e.g., Stokum 2A, D-48653 Cosefeild, Germany)
        country: { type: String, required: true }        // Consignee country (e.g., Germany)
    },
    invoiceNo: { type: String, required: true },         // Invoice number (e.g., RE-741)
    invoiceDate: { type: String, required: true },         // Invoice date (e.g., 04/03/2023)
    exportersRef: { type: String },                      // Exporter's reference number
    buyerOrderNo: { type: mongoose.Schema.Types.ObjectId,
        ref: 'BuyerOrder',
        required: true },                        // Buyer's order number,
                      
    buyerOrderDate: { type: String},  
    gstNo: { type: String, required: true },             // GSTIN number
    panNo: { type: String, required: true },             // PAN number 

    shippingDetails: {
        preCarriageBy: { type: String },                 // Pre-carriage by (e.g., Lorry)
        placeOfReceipt: { type: String },                // Place of receipt (e.g., By Pre-carrier)
        vesselNo: { type: String },                      // Vessel/flight number (e.g., Sea)
        portOfLoading: { type: String, default: 'India' }, // Port of loading (e.g., India)
        countryOfOrigin: { 
            type: String, 
            default: 'India',                             // Fixed: Country of origin of goods
            required: true 
        },
        countryOfDestination: { type: String, required: true }, // Country of destination (e.g., Germany)
        portOfDischarge: { type: String, required: true },  // Port of discharge (e.g., Rotterdam)
        finalDestination: { type: String, required: true }, // Final destination (e.g., Germany)
        deliveryTerms: { type: String },                 // Terms of delivery and payment (e.g., 360 DAYS D.A. FROM DATE OF B/L)
    },

    goodsDescription: { type: String, required: true },  // Description of goods (e.g., Indian Hand-Knotted Woolen Carpets)
    marksAndContNo: { type: String },                    // Marks and container number (e.g., RE-2106-2153)
    kindOfPkgs: { type: String },                        // Number and kind of packages (e.g., 48 Rolls)
    rollNumbers: { type: String },                       // Roll numbers (e.g., 2106-2153)

    area: { type: mongoose.Schema.Types.ObjectId,
            ref: 'SizeMaster',
            required: true,
     },                              // Area field (added after Roll numbers)


     goodsDetails: [{
        quality: { type: String },                       // Quality of the item (e.g., Himla, Mahavir)
        design: { type: String },                        // Design of the item (e.g., Bidjar, Herati)
        pieces: { type: Number },                        // Number of pieces
        quantitySqMeter: { type: Number },               // Quantity in square meters
        rateFOB: { type: Number },                       // Rate per square meter (FOB in EUR)
        amountFOB: { type: Number }                      // Amount in EUR (FOB)
    }],

    totalFOB: { type: Number },                          // Total FOB amount (e.g., 17157.98)
    
    woolPercentage: { type: Number, default: 80 },       // Wool percentage (e.g., 80%)
    cottonPercentage: { type: Number, default: 20 },     // Cotton percentage (e.g., 20%)

    grossWeight: { type: Number },                       // Gross weight (e.g., 1092.00 kg)
    netWeight: { type: Number },                         // Net weight (e.g., 1044.00 kg)

    additionalCharges: {
        insurance: { type: Number },                     // Insurance amount (e.g., 55.00 EUR)
        igst: { type: Number },                          // IGST 5% amount (e.g., 5%)
        igstPercentage: { type: Number },                // IGST percentage
    },

    finalAmount: { type: Number },                       // Final total amount (e.g., 17212.98)
    amountInWords: { type: String },                     // Final amount in words (e.g., Seventeen Thousand Two Hundred Twelve Point Ninety Eight Only)

    rexRegistrationNo: { 
        type: String, 
        default: 'INREX1502000776DG015',                 // Fixed REX registration number
        required: true 
    },

    declaration: { 
        type: String, 
        default: "We declare that this invoice shows the actual price of the goods described and that all particulars are true and correct." 
    }
}, { timestamps: true });

// Create and export the invoice model
const Invoice = mongoose.model('Invoice', invoiceSchema);

module.exports = Invoice;
