import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { ImporterService } from '../../../../services/importer.service';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { CustomeServiceService } from '../../../../services/custome-service.service';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { Observable, firstValueFrom, map, startWith } from 'rxjs';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import Swal from 'sweetalert2';
export interface UserData {
  index: number;
  GerCarpetNo: string;
  QualityDesign: string;
  QCode: string;
  Size: string;
  CCode: string;
  Color: string;

  SCore: string;
  Area: any;
  Rate: string;
  EvKPrice: any;
  Amount: any;
  InvoiceNo: string;
  ImporterCode: string;
  Remarks: string;
  exid: string;
  citemId: string;
}

let ELEMENT_DATA2: UserData[] = [];
@Component({
  selector: 'app-new-challan-sale',
  templateUrl: './new-challan-sale.component.html',
  styleUrl: './new-challan-sale.component.css',
})
export class NewChallanSaleComponent implements OnInit {
  [x: string]: any;
  financialData: any = {};
  BarcodeNo = 'option1';
  SaleRetailer = 'option2';
  Group1 = 'option1';
  CustomerName = 'option1';

  itemTypes: string[] = ['Sale', 'Return'];

  _isSelectedGroup: boolean = false;
  isEditAmount: boolean = false;
  barcode: any = [];
  wholesalerList: any = [];
  newChallanNo: number = 0;
  challanList: number[] = [];
  editRowIdx: number | null = null;
  groupList: string[] = ['Whole Seller', 'Retailer'];
  retailerName: any = [];
  @ViewChild('setDiscountAmt') setDiscountAmt!: ElementRef;
  displayedColumns: string[] = [
    'exid',
    'carpetno',
    'carpetDetails',
    'size',

    'area',
    'evkPrice',
    'amount',
    'action',
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA2);
  @ViewChild('myTextField') myTextField!: ElementRef;
  @ViewChild('mySizeField') mySizeField!: ElementRef;
  @ViewChild('myEvkPriceField') myEvkPriceField!: ElementRef;

  _isUpdatedChallan: boolean = false;
  constructor(
    private _services: ImporterService,
    private fb: FormBuilder,
    private customeService: CustomeServiceService,
    private activeRoute: ActivatedRoute,
    private router: Router,
    private ngxLoader: NgxUiLoaderService
  ) {}
  myControl = new FormControl('');
  getEditId: any;

  frmChallan!: FormGroup;
  isRetailer: boolean = false;

  ngOnInit(): void {
    this.filteredOptions = this.myControl.valueChanges.pipe(
      startWith(''),
      map((value) => this._filter(value || ''))
    );

    this.frmChallan = this.fb.group({
      group: [],
      wholeseller: [],
      chooseAdate: [],
      retailerOutlet: [],
      RetailerName: [],
      challanNo: [],
      saleRetailer: [this.itemTypes[0]],
    });

    let id = this.activeRoute.snapshot.paramMap.getAll('id') || '';
    this.getEditId = id;
    // this.getChallanType(this.itemTypes[0]);
    // this.getAllContainerRecieved();
    // this.getWholesalerList();
    // this.getChallanList();
    // this.getRetailerList();
    // setTimeout(() => {
    //   this.editChallan(id);
    // }, 2000);

    this.resolveAllPromise(id);
  }
  filteredOptions!: Observable<string[]>;
  options: string[] = [];
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  response: any;
  itemList: any = [];
  retailerList: any = [];
  private _filter(value: string): string[] {
    const filterValue = value.toLowerCase();

    return this.options.filter((option) => {
      return (
        typeof option === 'string' && option.toLowerCase().includes(filterValue)
      );
      // option.toLowerCase().includes(filterValue);
    });
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  a: number = 0;
  async getChallanList() {
    const resp = await firstValueFrom(this._services.getAllChallan());
    if (Array.isArray(resp)) {
      // setTimeout(() => {
      let editChallan = this.frmChallan.value.challanNo;
      if (resp.length > 0) {
        this.challanList = resp.map((x: any) => parseInt(x.challanNo));

        console.log(this.challanList);
        let maxItem = Math.max(...this.challanList);
        console.log(maxItem);

        // this.a = editChallan ? editChallan : ++maxItem;
        // this.frmChallan
        //   .get('challanNo')
        //   ?.patchValue(editChallan ? editChallan : ++maxItem);
      } else {
        let value = 2400001;
        // this.a = editChallan ? editChallan : value;
        // this.frmChallan
        //   .get('challanNo')
        //   ?.patchValue(editChallan ? editChallan : value);
      }
      // }, 1000);
    }
  }
  selectedGroup(event: any) {
    if (event !== 'Retailer') {
      this._isSelectedGroup = false;
      this.isRetailer = false;
    } else {
      this.isRetailer = true;
      this._isSelectedGroup = true;
    }
  }

  selectedValue(event: any) {
    this.onInputChange(event.target.value);
  }
  onOptionSelected(event: any) {
    console.log(event.option.value); // Log the selected value
    this.onInputChange(event.option.value);
  }
  onInputChange(event: any) {
    if (this.response) {
      let startIndex = ELEMENT_DATA2.length;

      this.response.forEach((x: any) => {
        x.containerItem.forEach((y: any) => {
          if (y.GerCarpetNo === event) {
            // Check if the GerCarpetNo already exists in ELEMENT_DATA2
            if (
              !ELEMENT_DATA2.some((item) => item.GerCarpetNo === y.GerCarpetNo)
            ) {
              let area;
              if (this.challanType == 'Return') {
                area = -y.Area;
              }
              let newPriceData;
              if (this.isRetailer == true) {
                let date = this.customeService.convertDate(
                  this.frmChallan.value.chooseAdate
                );

                newPriceData = this.priceList.find((price: any) => {
                  let toDate = this.customeService.convertDate(price.toDate);
                  let fromDate = this.customeService.convertDate(
                    price.fromDate
                  );
                  return (
                    fromDate >= date &&
                    toDate <= date &&
                    price.quality + ' ' + price.design === y.QualityDesign
                  );
                });
                console.log(newPriceData);
              }

              let carpetPrice =
                newPriceData && newPriceData.orderPrice
                  ? newPriceData.orderPrice
                  : y.EvKPrice;

              ELEMENT_DATA2.push({
                index: ++startIndex,
                GerCarpetNo: y.GerCarpetNo,
                QualityDesign: y.QualityDesign,
                CCode: y.CCode,
                Color: y.Color,

                QCode: y.QCode,
                Size: y.Size,
                SCore: y.SCore,
                Area: area ? area : y.Area,
                Rate: y.Rate,
                EvKPrice: carpetPrice,
                Amount: (carpetPrice * (area ? area : y.Area)).toFixed(2),
                InvoiceNo: y.InvoiceNo,
                ImporterCode: y.ImporterCode,
                Remarks: y.Remarks,
                exid: y._id,
                citemId: 'undefined',
              });
            } else {
              Swal.fire({
                title: 'Warning',
                text: 'It is already there!',
                icon: 'warning',
              });
            }
          }
        });
      });

      ELEMENT_DATA2 = ELEMENT_DATA2.sort(
        (x: any, y: any) => y.GerCarpetNo - x.GerCarpetNo
      );
      this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
      this.calculateAmount(ELEMENT_DATA2);
      this.ngAfterViewInit();
    }
  }

  getChallanType(event: any) {
    this.challanType = event;
    if (this.challanType) {
      setTimeout(() => {
        this.getAllContainerRecieved();
      }, 1000);
    }
  }
  challanType: any;
  async getAllContainerRecieved() {
    const resp = await firstValueFrom(this._services.getAllContainerRecieved());

    if (Array.isArray(resp)) {
      console.log(resp);

      this.response = resp;
      this.options.length = 0;
      resp.map((x: any) => {
        x.containerItem.map((y: any) => {
          if (this.challanType === 'Sale') {
            if (y.status == 'stock' || y.status == 'return') {
              this.barcode.push(y.GerCarpetNo);
              this.options = this.barcode;
            }
          } else {
            if (y.status === 'sale') {
              this.barcode.push(y.GerCarpetNo);
              this.options = this.barcode;
            }
          }
        });
      });
    }
  }
  oldSize: any;
  oldArea: any;
  editAmount(ind: number): void {
    this.isEditAmount = true;
    this.editRowIdx = ind;
    this.oldSize = this.dataSource.filteredData[ind - 1].Size;
    // this.oldSize
    this.oldArea = this.dataSource.filteredData[ind - 1].Area;
  }
  deleteItem(i: string): void {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't to delete carpet!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        let getBcr: any = ELEMENT_DATA2.filter((x: any) => x.exid == i);

        if (getBcr[0].citemId == 'undefined') {
          ELEMENT_DATA2 = ELEMENT_DATA2.filter((x: any) => x.exid != i);
          for (let i = 0; i < ELEMENT_DATA2.length; i++) {
            ELEMENT_DATA2[i].index = i + 1;
          }
          this.calculateAmount(ELEMENT_DATA2);
          this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
          this.ngAfterViewInit();
          Swal.fire({
            title: 'Removed!',
            text: 'Carpet has been removed.',
            icon: 'success',
          });

          return;
        }

        debugger;
        this._services
          .deleteChallan(this.getEditId, getBcr[0].citemId)
          .subscribe(
            (resp: any) => {
              ELEMENT_DATA2 = ELEMENT_DATA2.filter((x: any) => x.exid != i);
              for (let i = 0; i < ELEMENT_DATA2.length; i++) {
                ELEMENT_DATA2[i].index = i + 1;
              }
              this.calculateAmount(ELEMENT_DATA2);
              this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
              this.ngAfterViewInit();
              Swal.fire({
                title: 'Deleted!',
                text: 'Carpet has been deleted.',
                icon: 'success',
              });
              this.getAllContainerRecieved();
              return;
            },
            (error) => {
              Swal.fire({
                title: 'Deleted!',
                text: error.error.message,
                icon: 'success',
              });
            }
          );
      }
    });
  }
  deletedItem(i: number): void {}
  updateAmount(i: string): void {
    debugger;
    const newAmt = this.myTextField.nativeElement.value;
    const newSize = this.mySizeField.nativeElement.value;
    const newEvkPrice = this.myEvkPriceField.nativeElement.value;
    const index = ELEMENT_DATA2.findIndex((item) => item.exid === i);
    if (index !== -1) {
      ELEMENT_DATA2[index].Amount = newAmt;
      ELEMENT_DATA2[index].Size = newSize;
      ELEMENT_DATA2[index].EvKPrice = newEvkPrice;
      let calcArea =
        (parseInt(newSize.split('X' || 'x')[0]) *
          parseInt(newSize.split('X' || 'x')[1])) /
        10000;

      let newArea: any =
        this.oldArea > 0
          ? calcArea.toFixed(2).toString()
          : -calcArea.toFixed(2).toString();

      // ELEMENT_DATA2[index].Area = calcArea.toFixed(2).toString();
      let evk = ELEMENT_DATA2[index].EvKPrice;
      let amount =
        this.oldSize !== newSize
          ? newArea * parseFloat(evk)
          : newArea * parseFloat(evk);
      ELEMENT_DATA2[index].Amount = amount.toFixed(2);
      // this.isEditAmount = false;
      this.editRowIdx = null;
    }
    this.calculateAmount(ELEMENT_DATA2);
    this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
  }

  cancel(): void {
    this.isEditAmount = false;
    this.editRowIdx = null;
  }

  async getWholesalerList() {
    const resp = await firstValueFrom(this._services.getsWholesalerList());
    if (Array.isArray(resp)) {
      if (resp) {
        resp.map((x: any) => {
          this.wholesalerList.push({
            id: x._id,
            wholesalerName: x.customerName,
          });
        });
      }
    }
  }

  async getRetailerList() {
    const resp = await firstValueFrom(this._services.getRetailerList());
    if (Array.isArray(resp)) {
      this.retailerList = resp;
      if (resp) {
        resp.map((x: any) => {
          this.retailerName.push({
            name: x.retailerStoreName,
            id: x._id,
          });
        });
      }
    }
  }

  createChallan() {
    let carpetList: any = [];
    console.log(this.frmChallan.value);
    console.log(ELEMENT_DATA2);
    ELEMENT_DATA2.forEach((element) => {
      carpetList.push({
        barcodeNo: element.GerCarpetNo,
        amount: element.Amount,
        size: element.Size,
        area: parseFloat(element.Area),
        evkPrice: element.EvKPrice,
        status: parseFloat(element.Area) > 0 ? 'sale' : 'return',
      });
    });
    console.log(this.financialData);

    let formData = this.frmChallan.value;
    formData['carpetList'] = carpetList;
    formData['total'] = this.financialData.amount;
    formData['profit'] = this.financialData.profit;
    formData['gst'] = this.financialData.gst;
    formData['discount'] = this.discountAmount;
    console.log(formData);
    if (carpetList.length === 0) {
      Swal.fire({
        title: 'warning!',
        text: 'Please enter carpet no!',
        icon: 'warning',
      });
    }else{
      this._services.createChallan(formData).subscribe(
        (resp: any) => {
          debugger

          if (resp) {
            if(resp==='already exist'){
              Swal.fire({
                title: 'Failed!',
                text: `Challan ${resp}!`,
                icon: 'warning',
              });
            }else{
            Swal.fire({
              title: 'Success!',
              text: 'Challan has been created successfully!',
              icon: 'success',
            });
            this.reset();
            this.getAllContainerRecieved()
            this.myControl.reset();
          }
          }
        },
        (error) => {
          debugger
          Swal.fire({
            title: 'Failed!',
            text: 'Something went wrong',
            icon: 'warning',
          });
        }
      );
    }

  }
  priceList: any = [];
  selectedRetailer(id: string) {
    let currentDate = new Date();
    if (this.retailerList) {
      const retailerPriceList = this.retailerList.find(
        (x: any) => x.retailerStoreName == id
      );
      retailerPriceList.addOnPrice.map((x: any) => {
        let currDate = this.customeService.convertDate(currentDate);
        let toDate = this.customeService.convertDate(x.toDate);
        let fromDate = this.customeService.convertDate(x.fromDate);
        if (toDate <= currDate && fromDate >= currDate) {
          this.priceList.push(x);
        }
      });
    }
  }
  newAmount: any = 0;
  calculateAmount(data: any) {
    let isExist = parseFloat(this.setDiscountAmt.nativeElement.value);
    debugger;
    isExist ? (this.discountAmount = isExist) : this.discountAmount;
    this.newAmount = 0;
    this.newAmount = data.reduce((total: any, item: any) => {
      const amount = parseFloat(item.Amount);
      return total + (isNaN(amount) ? 0 : amount);
    }, 0);
    this.financialData.amount = this._isDiscount
      ? this.newAmount - this.discountAmount
      : this.newAmount;
    this.financialData.profit = (this.financialData.amount / 100) * 13;
    this.financialData.gst =
      ((this.financialData.profit + this.financialData.amount) / 100) * 19;
    this.financialData.grossAmount =
      this.financialData.amount +
      this.financialData.gst +
      this.financialData.profit;

    console.log('```````', this.financialData);
  }
  discountAmount = 0;
  _isDiscount: boolean = false;
  setDiscount() {
    this._isDiscount = true;
    this.discountAmount = this.setDiscountAmt.nativeElement.value;
    this.calculateAmount(ELEMENT_DATA2);
  }
  challanId: any;

  // async editChallan(id: any) {
  //   this.challanId = id;
  //   if (this.challanId.length > 0) {
  //     this._services.getChallan(id).subscribe(
  //       (resp: any) => {
  //         console.log(resp);
  //         this.frmChallan.patchValue(resp);
  //         ;
  //         let itemList = resp.carpetList;
  //         let startIndex = ELEMENT_DATA2.length;
  //         itemList.forEach((el: any) => {
  //           this.response.forEach((elem: any) => {
  //             elem.containerItem.forEach((element: any) => {
  //               if (parseInt(element.GerCarpetNo) === el.barcodeNo && el.status !='return') {
  //                 ELEMENT_DATA2.push({
  //                   index: ++startIndex,
  //                   GerCarpetNo: element.GerCarpetNo,
  //                   QualityDesign: element.QualityDesign,

  //                   CCode: element.CCode,
  //                   Color: element.Color,
  //                   QCode: element.QCode,
  //                   Size: el.size ? el.size : element.Size,
  //                   SCore: element.SCore,
  //                   Area: el.area ? el.area : element.Area,
  //                   Rate: element.Rate,
  //                   EvKPrice: el.evkPrice ? el.evkPrice : element.EvKPrice,
  //                   Amount: el.amount
  //                     ? el.amount
  //                     : (element.EvKPrice * element.Area).toString(),
  //                   InvoiceNo: element.InvoiceNo,
  //                   ImporterCode: element.ImporterCode,
  //                   Remarks: element.Remarks,
  //                   exid: element._id,
  //                   citemId:el._id
  //                 });
  //               }
  //             });
  //           });
  //         });
  //         this._isUpdatedChallan = true;
  //         this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
  //         this.calculateAmount(ELEMENT_DATA2);
  //         this.ngAfterViewInit();
  //       },
  //       (error) => {
  //         Swal.fire({
  //           title: 'Warning',
  //           text: 'Something went wrong!',
  //           icon: 'warning',
  //         });
  //       }
  //     );
  //   }
  // }
  async editChallan(id: any) {
    this.challanId = id;
    debugger;
    if (this.challanId.length > 0) {
      let resp: any = await firstValueFrom(this._services.getChallan(id));

      console.log(resp);
      this.frmChallan.patchValue(resp);
      let itemList = resp.carpetList;
      // let itemList:any
      let startIndex = ELEMENT_DATA2.length;
      debugger;
      itemList.forEach((el: any) => {
        this.response.forEach((elem: any) => {
          elem.containerItem.forEach((element: any) => {
            if (parseInt(element.GerCarpetNo) === el.barcodeNo && el.isDeleted!=true) {
              const area =
                el.status === 'return'
                  ? el.area
                    ? el.area
                    : -element.Area
                  : parseFloat(el.area ? el.area : element.Area);
              const evkPrice = el.evkPrice
                ? parseFloat(el.evkPrice)
                : parseFloat(element.EvKPrice);
              const amount = area * evkPrice;
              ELEMENT_DATA2.push({
                index: ++startIndex,
                GerCarpetNo: element.GerCarpetNo,
                QualityDesign: element.QualityDesign,

                CCode: element.CCode,
                Color: element.Color,
                QCode: element.QCode,
                Size: el.size ? el.size : element.Size,
                SCore: element.SCore,
                Area: area,
                //  el.status === 'return' ? -parseFloat(el.area ? el.area : element.Area) : parseFloat(el.area ? el.area : element.Area),
                Rate: element.Rate,
                EvKPrice: el.status === 'return' ? evkPrice : evkPrice,
                // el.status === 'return' ? -parseFloat(el.evkPrice ? el.evkPrice : element.EvKPrice) : parseFloat(el.evkPrice ? el.evkPrice : element.EvKPrice),
                Amount: el.status === 'return' ? amount : amount,
                // el.amount
                //   ? el.amount
                //   : (element.EvKPrice * element.Area).toString(),
                InvoiceNo: element.InvoiceNo,
                ImporterCode: element.ImporterCode,
                Remarks: element.Remarks,
                exid: element._id,
                citemId: el._id,
              });
            }
          });
        });
      });
      this.setDiscountAmt.nativeElement.value = resp.discount?resp.discount:'';
      resp.discount ? (this._isDiscount = true) : '';

      this._isUpdatedChallan = true;
      this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
      this.calculateAmount(ELEMENT_DATA2);
      this.ngAfterViewInit();
    }
  }

  updateChallan() {
    let carpetList: any = [];
    ELEMENT_DATA2.forEach((element) => {
      carpetList.push({
        barcodeNo: element.GerCarpetNo,
        amount: element.Amount,
        size: element.Size,
        area: element.Area,
        evkPrice: element.EvKPrice,
        id: element.citemId,
        status: parseFloat(element.Area) > 0 ? 'sale' : 'return',
      });
    });

    let formData = this.frmChallan.value;
    formData['carpetList'] = carpetList;
    formData['total'] = this.financialData.amount;
    formData['profit'] = this.financialData.profit;
    formData['gst'] = this.financialData.gst;
    formData['discount'] = this.discountAmount;
    this._services.updateChallan(this.challanId, formData).subscribe(
      (resp: any) => {
        console.log(resp);
        if (resp) {
          Swal.fire({
            title: 'success',
            text: 'Challan has been updated!',
            icon: 'success',
          });
          this.getAllContainerRecieved();
          this.reset();
          this.myControl.reset();
          this._isUpdatedChallan = false;
        }
      },
      (error) => {
        Swal.fire({
          title: 'Warning',
          text: 'Something went wrong!',
          icon: 'warning',
        });
      }
    );
  }

  reset() {
    this.frmChallan.reset();
    this.getChallanList();
    this.getAllContainerRecieved();
    this.frmChallan.get('saleRetailer')?.setValue(this.itemTypes[0]);
    this.frmChallan.get('myControl')?.setValue('');
    this.dataSource.data = [];
    ELEMENT_DATA2 = [];
    this.calculateAmount(ELEMENT_DATA2);
    this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
  }
  lastSelected: any = '';

  resolveAllPromise(id: any) {
    this.ngxLoader.start();
    Promise.all([
      this.getChallanType(this.itemTypes[0]),
      this.getAllContainerRecieved(),
      this.getWholesalerList(),
      this.getChallanList(),
      this.getRetailerList(),
    ])
      .then(() => {
        this.editChallan(id);
        this.ngxLoader.stop();
      })
      .catch((erro) => {
        Swal.fire({
          title: 'Warning',
          text: 'Something went wrong!',
          icon: 'warning',
        });
      });
  }
}
