/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!y.call({1:2},1);g.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:y;var h,m,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),x=T("".slice),E=function(t){return x(A(t),8,-1)},C=E,F=P,M=function(t){if("Function"===C(t))return F(t)},R=c,D=E,I=Object,k=M("".split),L=R((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"==D(t)?k(t,""):I(t)}:I,N=function(t){return null==t},H=N,_=TypeError,z=function(t){if(H(t))throw _("Can't call method on "+t);return t},G=L,q=z,B=function(t){return G(q(t))},U="object"==typeof document&&document.all,V={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},W=V.all,K=V.IS_HTMLDDA?function(t){return"function"==typeof t||t===W}:function(t){return"function"==typeof t},J=K,Q=V.all,X=V.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:J(t)||t===Q}:function(t){return"object"==typeof t?null!==t:J(t)},Y=i,$=K,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=M({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(m=(h=ct.split("."))[0]>0&&h[0]<4?1:+(h[0]+h[1])),!m&&et&&(!(h=et.match(/Edge\/(\d+)/))||h[1]>=74)&&(h=et.match(/Chrome\/(\d+)/))&&(m=+h[1]);var at=m,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=K,bt=nt,dt=Object,ht=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&bt(n.prototype,dt(t))},mt=String,vt=K,St=function(t){try{return mt(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=N,Pt=p,Tt=K,At=X,xt=TypeError,Et={exports:{}},Ct=i,Ft=Object.defineProperty,Mt=function(t,n){try{Ft(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Rt=Mt,Dt="__core-js_shared__",It=i[Dt]||Rt(Dt,{}),kt=It;(Et.exports=function(t,n){return kt[t]||(kt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Lt=z,Nt=Object,Ht=function(t){return Nt(Lt(t))},_t=Ht,zt=M({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return zt(_t(t),n)},qt=M,Bt=0,Ut=Math.random(),Vt=qt(1..toString),Wt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Vt(++Bt+Ut,36)},Kt=i,Jt=Et.exports,Qt=Gt,Xt=Wt,Yt=st,$t=pt,Zt=Jt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Qt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Qt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,cn=ht,an=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=ht,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},bn=X,dn=i.document,hn=bn(dn)&&bn(dn.createElement),mn=function(t){return hn?dn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(mn("div"),"a",{get:function(){return 7}}).a})),Sn=a,wn=p,On=g,jn=v,Pn=B,Tn=yn,An=Gt,xn=vn,En=Object.getOwnPropertyDescriptor;u.f=Sn?En:function(t,n){if(t=Pn(t),n=Tn(n),xn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var Cn={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mn=X,Rn=String,Dn=TypeError,In=function(t){if(Mn(t))return t;throw Dn(Rn(t)+" is not an object")},kn=a,Ln=vn,Nn=Fn,Hn=In,_n=yn,zn=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Un="configurable",Vn="writable";Cn.f=kn?Nn?function(t,n,r){if(Hn(t),n=_n(n),Hn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Vn in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(Hn(t),n=_n(n),Hn(r),Ln)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw zn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Wn=Cn,Kn=v,Jn=a?function(t,n,r){return Wn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Qn={exports:{}},Xn=a,Yn=Gt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=K,er=It,or=M(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,gr=Wt,yr=pr("keys"),br={},dr=sr,hr=i,mr=X,vr=Jn,Sr=Gt,wr=It,Or=function(t){return yr[t]||(yr[t]=gr(t))},jr=br,Pr="Object already initialized",Tr=hr.TypeError,Ar=hr.WeakMap;if(dr||wr.state){var xr=wr.state||(wr.state=new Ar);xr.get=xr.get,xr.has=xr.has,xr.set=xr.set,ir=function(t,n){if(xr.has(t))throw Tr(Pr);return n.facade=t,xr.set(t,n),n},ur=function(t){return xr.get(t)||{}},cr=function(t){return xr.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(Sr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return Sr(t,Er)?t[Er]:{}},cr=function(t){return Sr(t,Er)}}var Cr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!mr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=c,Mr=K,Rr=Gt,Dr=a,Ir=nr.CONFIGURABLE,kr=ar,Lr=Cr.enforce,Nr=Cr.get,Hr=Object.defineProperty,_r=Dr&&!Fr((function(){return 8!==Hr((function(){}),"length",{value:8}).length})),zr=String(String).split("String"),Gr=Qn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Rr(t,"name")||Ir&&t.name!==n)&&(Dr?Hr(t,"name",{value:n,configurable:!0}):t.name=n),_r&&r&&Rr(r,"arity")&&t.length!==r.arity&&Hr(t,"length",{value:r.arity});try{r&&Rr(r,"constructor")&&r.constructor?Dr&&Hr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Lr(t);return Rr(e,"source")||(e.source=zr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Mr(this)&&Nr(this).source||kr(this)}),"toString");var qr=K,Br=Cn,Ur=Qn.exports,Vr=Mt,Wr={},Kr=Math.ceil,Jr=Math.floor,Qr=Math.trunc||function(t){var n=+t;return(n>0?Jr:Kr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Qr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=Gt,le=B,se=ae.indexOf,pe=br,ge=M([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Wr.f=Object.getOwnPropertyNames||function(t){return ye(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var he=tt,me=Wr,ve=de,Se=In,we=M([].concat),Oe=he("Reflect","ownKeys")||function(t){var n=me.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},je=Gt,Pe=Oe,Te=u,Ae=Cn,xe=c,Ee=K,Ce=/#|\.prototype\./,Fe=function(t,n){var r=Re[Me(t)];return r==Ie||r!=De&&(Ee(n)?xe(n):!!n)},Me=Fe.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Re=Fe.data={},De=Fe.NATIVE="N",Ie=Fe.POLYFILL="P",ke=Fe,Le=i,Ne=u.f,He=Jn,_e=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Vr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},ze=Mt,Ge=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var c=e[u];je(t,c)||r&&je(r,c)||o(t,c,i(n,c))}},qe=ke,Be=E,Ue=Array.isArray||function(t){return"Array"==Be(t)},Ve=TypeError,We=yn,Ke=Cn,Je=v,Qe={};Qe[en("toStringTag")]="z";var Xe="[object z]"===String(Qe),Ye=K,$e=E,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=M,eo=c,oo=K,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,ho=Ue,mo=bo,vo=X,So=en("species"),wo=Array,Oo=function(t){var n;return ho(t)&&(n=t.constructor,(mo(n)&&(n===wo||ho(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=c,Po=at,To=en("species"),Ao=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Le:f?Le[c]||ze(c,{}):(Le[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Ne(r,e))&&u.value:r[e],!qe(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&He(i,"sham",!0),_e(r,e,i,t)}},xo=c,Eo=Ue,Co=X,Fo=Ht,Mo=ee,Ro=function(t){if(t>9007199254740991)throw Ve("Maximum allowed index exceeded");return t},Do=function(t,n,r){var e=We(n);e in t?Ke.f(t,e,Je(0,r)):t[e]=r},Io=function(t,n){return new(Oo(t))(0===n?0:n)},ko=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=at,No=en("isConcatSpreadable"),Ho=Lo>=51||!xo((function(){var t=[];return t[No]=!1,t.concat()[0]!==t})),_o=ko("concat"),zo=function(t){if(!Co(t))return!1;var n=t[No];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!Ho||!_o},{concat:function(t){var n,r,e,o,i,u=Fo(this),c=Io(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(zo(i=-1===n?u:arguments[n]))for(o=Mo(i),Ro(a+o),r=0;r<o;r++,a++)r in i&&Do(c,a,i[r]);else Ro(a+1),Do(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["nb-NO"]=r.default.fn.bootstrapTable.locales.nb={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Oppdaterer, vennligst vent"},formatRecordsPerPage:function(t){return"".concat(t," poster pr side")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Viser ".concat(t," til ").concat(n," av ").concat(r," rekker (filtered from ").concat(e," total rows)"):"Viser ".concat(t," til ").concat(n," av ").concat(r," rekker")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Søk"},formatNoMatches:function(){return"Ingen poster funnet"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Oppdater"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolonner"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["nb-NO"])}));
