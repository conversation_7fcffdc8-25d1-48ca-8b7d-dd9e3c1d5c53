/*! jQuery v3.7.1 | (c) OpenJS Foundation and other contributors | jquery.org/license */(function(F,ot){"use strict";typeof module=="object"&&typeof module.exports=="object"?module.exports=F.document?ot(F,!0):function(Se){if(!Se.document)throw new Error("jQuery requires a window with a document");return ot(Se)}:ot(F)})(typeof window<"u"?window:this,function(F,ot){"use strict";var Se=[],Ei=Object.getPrototypeOf,Xe=Se.slice,yt=Se.flat?function(e){return Se.flat.call(e)}:function(e){return Se.concat.apply([],e)},st=Se.push,Ue=Se.indexOf,at={},Ci=at.toString,Mt=at.hasOwnProperty,on=Mt.toString,tr=on.call(Object),K={},B=function(e){return typeof e=="function"&&typeof e.nodeType!="number"&&typeof e.item!="function"},Ie=function(e){return e!=null&&e===e.window},q=F.document,nr={type:!0,src:!0,nonce:!0,noModule:!0};function ti(e,n,i){var r,a,c=(i=i||q).createElement("script");if(c.text=e,n)for(r in nr)(a=n[r]||n.getAttribute&&n.getAttribute(r))&&c.setAttribute(r,a);i.head.appendChild(c).parentNode.removeChild(c)}function sn(e){return e==null?e+"":typeof e=="object"||typeof e=="function"?at[Ci.call(e)]||"object":typeof e}var ir="3.7.1",Lr=/HTML$/i,o=function(e,n){return new o.fn.init(e,n)};function ni(e){var n=!!e&&"length"in e&&e.length,i=sn(e);return!B(e)&&!Ie(e)&&(i==="array"||n===0||typeof n=="number"&&0<n&&n-1 in e)}function he(e,n){return e.nodeName&&e.nodeName.toLowerCase()===n.toLowerCase()}o.fn=o.prototype={jquery:ir,constructor:o,length:0,toArray:function(){return Xe.call(this)},get:function(e){return e==null?Xe.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var n=o.merge(this.constructor(),e);return n.prevObject=this,n},each:function(e){return o.each(this,e)},map:function(e){return this.pushStack(o.map(this,function(n,i){return e.call(n,i,n)}))},slice:function(){return this.pushStack(Xe.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(o.grep(this,function(e,n){return(n+1)%2}))},odd:function(){return this.pushStack(o.grep(this,function(e,n){return n%2}))},eq:function(e){var n=this.length,i=+e+(e<0?n:0);return this.pushStack(0<=i&&i<n?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:st,sort:Se.sort,splice:Se.splice},o.extend=o.fn.extend=function(){var e,n,i,r,a,c,u=arguments[0]||{},p=1,h=arguments.length,v=!1;for(typeof u=="boolean"&&(v=u,u=arguments[p]||{},p++),typeof u=="object"||B(u)||(u={}),p===h&&(u=this,p--);p<h;p++)if((e=arguments[p])!=null)for(n in e)r=e[n],n!=="__proto__"&&u!==r&&(v&&r&&(o.isPlainObject(r)||(a=Array.isArray(r)))?(i=u[n],c=a&&!Array.isArray(i)?[]:a||o.isPlainObject(i)?i:{},a=!1,u[n]=o.extend(v,c,r)):r!==void 0&&(u[n]=r));return u},o.extend({expando:"jQuery"+(ir+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var n,i;return!(!e||Ci.call(e)!=="[object Object]")&&(!(n=Ei(e))||typeof(i=Mt.call(n,"constructor")&&n.constructor)=="function"&&on.call(i)===tr)},isEmptyObject:function(e){var n;for(n in e)return!1;return!0},globalEval:function(e,n,i){ti(e,{nonce:n&&n.nonce},i)},each:function(e,n){var i,r=0;if(ni(e))for(i=e.length;r<i&&n.call(e[r],r,e[r])!==!1;r++);else for(r in e)if(n.call(e[r],r,e[r])===!1)break;return e},text:function(e){var n,i="",r=0,a=e.nodeType;if(!a)for(;n=e[r++];)i+=o.text(n);return a===1||a===11?e.textContent:a===9?e.documentElement.textContent:a===3||a===4?e.nodeValue:i},makeArray:function(e,n){var i=n||[];return e!=null&&(ni(Object(e))?o.merge(i,typeof e=="string"?[e]:e):st.call(i,e)),i},inArray:function(e,n,i){return n==null?-1:Ue.call(n,e,i)},isXMLDoc:function(e){var n=e&&e.namespaceURI,i=e&&(e.ownerDocument||e).documentElement;return!Lr.test(n||i&&i.nodeName||"HTML")},merge:function(e,n){for(var i=+n.length,r=0,a=e.length;r<i;r++)e[a++]=n[r];return e.length=a,e},grep:function(e,n,i){for(var r=[],a=0,c=e.length,u=!i;a<c;a++)!n(e[a],a)!==u&&r.push(e[a]);return r},map:function(e,n,i){var r,a,c=0,u=[];if(ni(e))for(r=e.length;c<r;c++)(a=n(e[c],c,i))!=null&&u.push(a);else for(c in e)(a=n(e[c],c,i))!=null&&u.push(a);return yt(u)},guid:1,support:K}),typeof Symbol=="function"&&(o.fn[Symbol.iterator]=Se[Symbol.iterator]),o.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,n){at["[object "+n+"]"]=n.toLowerCase()});var jr=Se.pop,rr=Se.sort,or=Se.splice,ce="[\\x20\\t\\r\\n\\f]",an=new RegExp("^"+ce+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ce+"+$","g");o.contains=function(e,n){var i=n&&n.parentNode;return e===i||!(!i||i.nodeType!==1||!(e.contains?e.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))};var sr=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function Ai(e,n){return n?e==="\0"?"\uFFFD":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}o.escapeSelector=function(e){return(e+"").replace(sr,Ai)};var At=q,ii=st;(function(){var e,n,i,r,a,c,u,p,h,v,w=ii,E=o.expando,_=0,A=0,H=Te(),z=Te(),X=Te(),_e=Te(),ye=function(f,g){return f===g&&(a=!0),0},it="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",xt="(?:\\\\[\\da-fA-F]{1,6}"+ce+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",ne="\\["+ce+"*("+xt+")(?:"+ce+"*([*^$|!~]?=)"+ce+`*(?:'((?:\\\\.|[^\\\\'])*)'|"((?:\\\\.|[^\\\\"])*)"|(`+xt+"))|)"+ce+"*\\]",Zt=":("+xt+`)(?:\\((('((?:\\\\.|[^\\\\'])*)'|"((?:\\\\.|[^\\\\"])*)")|((?:\\\\.|[^\\\\()[\\]]|`+ne+")*)|.*)\\)|)",se=new RegExp(ce+"+","g"),we=new RegExp("^"+ce+"*,"+ce+"*"),Wn=new RegExp("^"+ce+"*([>+~]|"+ce+")"+ce+"*"),mi=new RegExp(ce+"|>"),gt=new RegExp(Zt),Bn=new RegExp("^"+xt+"$"),Qe={ID:new RegExp("^#("+xt+")"),CLASS:new RegExp("^\\.("+xt+")"),TAG:new RegExp("^("+xt+"|[*])"),ATTR:new RegExp("^"+ne),PSEUDO:new RegExp("^"+Zt),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ce+"*(even|odd|(([+-]|)(\\d*)n|)"+ce+"*(?:([+-]|)"+ce+"*(\\d+)|))"+ce+"*\\)|)","i"),bool:new RegExp("^(?:"+it+")$","i"),needsContext:new RegExp("^"+ce+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ce+"*((?:-\\d)?\\d*)"+ce+"*\\)|)(?=[^-]|$)","i")},Ke=/^(?:input|select|textarea|button)$/i,kn=/^h\d$/i,Be=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Ne=/[+~]/,Ce=new RegExp("\\\\[\\da-fA-F]{1,6}"+ce+"?|\\\\([^\\r\\n\\f])","g"),ze=function(f,g){var b="0x"+f.slice(1)-65536;return g||(b<0?String.fromCharCode(b+65536):String.fromCharCode(b>>10|55296,1023&b|56320))},Rt=function(){Nt()},Ve=Un(function(f){return f.disabled===!0&&he(f,"fieldset")},{dir:"parentNode",next:"legend"});try{w.apply(Se=Xe.call(At.childNodes),At.childNodes),Se[At.childNodes.length].nodeType}catch{w={apply:function(g,b){ii.apply(g,Xe.call(b))},call:function(g){ii.apply(g,Xe.call(arguments,1))}}}function W(f,g,b,x){var C,L,j,$,N,le,Y,G=g&&g.ownerDocument,oe=g?g.nodeType:9;if(b=b||[],typeof f!="string"||!f||oe!==1&&oe!==9&&oe!==11)return b;if(!x&&(Nt(g),g=g||c,p)){if(oe!==11&&(N=Be.exec(f)))if(C=N[1]){if(oe===9){if(!(j=g.getElementById(C)))return b;if(j.id===C)return w.call(b,j),b}else if(G&&(j=G.getElementById(C))&&W.contains(g,j)&&j.id===C)return w.call(b,j),b}else{if(N[2])return w.apply(b,g.getElementsByTagName(f)),b;if((C=N[3])&&g.getElementsByClassName)return w.apply(b,g.getElementsByClassName(C)),b}if(!(_e[f+" "]||h&&h.test(f))){if(Y=f,G=g,oe===1&&(mi.test(f)||Wn.test(f))){for((G=Ne.test(f)&&Yi(g.parentNode)||g)==g&&K.scope||(($=g.getAttribute("id"))?$=o.escapeSelector($):g.setAttribute("id",$=E)),L=(le=Xn(f)).length;L--;)le[L]=($?"#"+$:":scope")+" "+vi(le[L]);Y=le.join(",")}try{return w.apply(b,G.querySelectorAll(Y)),b}catch{_e(f,!0)}finally{$===E&&g.removeAttribute("id")}}}return Yn(f.replace(an,"$1"),g,b,x)}function Te(){var f=[];return function g(b,x){return f.push(b+" ")>n.cacheLength&&delete g[f.shift()],g[b+" "]=x}}function Ee(f){return f[E]=!0,f}function re(f){var g=c.createElement("fieldset");try{return!!f(g)}catch{return!1}finally{g.parentNode&&g.parentNode.removeChild(g),g=null}}function mt(f){return function(g){return he(g,"input")&&g.type===f}}function en(f){return function(g){return(he(g,"input")||he(g,"button"))&&g.type===f}}function zn(f){return function(g){return"form"in g?g.parentNode&&g.disabled===!1?"label"in g?"label"in g.parentNode?g.parentNode.disabled===f:g.disabled===f:g.isDisabled===f||g.isDisabled!==!f&&Ve(g)===f:g.disabled===f:"label"in g&&g.disabled===f}}function tn(f){return Ee(function(g){return g=+g,Ee(function(b,x){for(var C,L=f([],b.length,g),j=L.length;j--;)b[C=L[j]]&&(b[C]=!(x[C]=b[C]))})})}function Yi(f){return f&&typeof f.getElementsByTagName<"u"&&f}function Nt(f){var g,b=f?f.ownerDocument||f:At;return b!=c&&b.nodeType===9&&b.documentElement&&(u=(c=b).documentElement,p=!o.isXMLDoc(c),v=u.matches||u.webkitMatchesSelector||u.msMatchesSelector,u.msMatchesSelector&&At!=c&&(g=c.defaultView)&&g.top!==g&&g.addEventListener("unload",Rt),K.getById=re(function(x){return u.appendChild(x).id=o.expando,!c.getElementsByName||!c.getElementsByName(o.expando).length}),K.disconnectedMatch=re(function(x){return v.call(x,"*")}),K.scope=re(function(){return c.querySelectorAll(":scope")}),K.cssHas=re(function(){try{return c.querySelector(":has(*,:jqfake)"),!1}catch{return!0}}),K.getById?(n.filter.ID=function(x){var C=x.replace(Ce,ze);return function(L){return L.getAttribute("id")===C}},n.find.ID=function(x,C){if(typeof C.getElementById<"u"&&p){var L=C.getElementById(x);return L?[L]:[]}}):(n.filter.ID=function(x){var C=x.replace(Ce,ze);return function(L){var j=typeof L.getAttributeNode<"u"&&L.getAttributeNode("id");return j&&j.value===C}},n.find.ID=function(x,C){if(typeof C.getElementById<"u"&&p){var L,j,$,N=C.getElementById(x);if(N){if((L=N.getAttributeNode("id"))&&L.value===x)return[N];for($=C.getElementsByName(x),j=0;N=$[j++];)if((L=N.getAttributeNode("id"))&&L.value===x)return[N]}return[]}}),n.find.TAG=function(x,C){return typeof C.getElementsByTagName<"u"?C.getElementsByTagName(x):C.querySelectorAll(x)},n.find.CLASS=function(x,C){if(typeof C.getElementsByClassName<"u"&&p)return C.getElementsByClassName(x)},h=[],re(function(x){var C;u.appendChild(x).innerHTML="<a id='"+E+"' href='' disabled='disabled'></a><select id='"+E+"-\r\\' disabled='disabled'><option selected=''></option></select>",x.querySelectorAll("[selected]").length||h.push("\\["+ce+"*(?:value|"+it+")"),x.querySelectorAll("[id~="+E+"-]").length||h.push("~="),x.querySelectorAll("a#"+E+"+*").length||h.push(".#.+[+~]"),x.querySelectorAll(":checked").length||h.push(":checked"),(C=c.createElement("input")).setAttribute("type","hidden"),x.appendChild(C).setAttribute("name","D"),u.appendChild(x).disabled=!0,x.querySelectorAll(":disabled").length!==2&&h.push(":enabled",":disabled"),(C=c.createElement("input")).setAttribute("name",""),x.appendChild(C),x.querySelectorAll("[name='']").length||h.push("\\["+ce+"*name"+ce+"*="+ce+`*(?:''|"")`)}),K.cssHas||h.push(":has"),h=h.length&&new RegExp(h.join("|")),ye=function(x,C){if(x===C)return a=!0,0;var L=!x.compareDocumentPosition-!C.compareDocumentPosition;return L||(1&(L=(x.ownerDocument||x)==(C.ownerDocument||C)?x.compareDocumentPosition(C):1)||!K.sortDetached&&C.compareDocumentPosition(x)===L?x===c||x.ownerDocument==At&&W.contains(At,x)?-1:C===c||C.ownerDocument==At&&W.contains(At,C)?1:r?Ue.call(r,x)-Ue.call(r,C):0:4&L?-1:1)}),c}for(e in W.matches=function(f,g){return W(f,null,null,g)},W.matchesSelector=function(f,g){if(Nt(f),p&&!_e[g+" "]&&(!h||!h.test(g)))try{var b=v.call(f,g);if(b||K.disconnectedMatch||f.document&&f.document.nodeType!==11)return b}catch{_e(g,!0)}return 0<W(g,c,null,[f]).length},W.contains=function(f,g){return(f.ownerDocument||f)!=c&&Nt(f),o.contains(f,g)},W.attr=function(f,g){(f.ownerDocument||f)!=c&&Nt(f);var b=n.attrHandle[g.toLowerCase()],x=b&&Mt.call(n.attrHandle,g.toLowerCase())?b(f,g,!p):void 0;return x!==void 0?x:f.getAttribute(g)},W.error=function(f){throw new Error("Syntax error, unrecognized expression: "+f)},o.uniqueSort=function(f){var g,b=[],x=0,C=0;if(a=!K.sortStable,r=!K.sortStable&&Xe.call(f,0),rr.call(f,ye),a){for(;g=f[C++];)g===f[C]&&(x=b.push(C));for(;x--;)or.call(f,b[x],1)}return r=null,f},o.fn.uniqueSort=function(){return this.pushStack(o.uniqueSort(Xe.apply(this)))},(n=o.expr={cacheLength:50,createPseudo:Ee,match:Qe,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(f){return f[1]=f[1].replace(Ce,ze),f[3]=(f[3]||f[4]||f[5]||"").replace(Ce,ze),f[2]==="~="&&(f[3]=" "+f[3]+" "),f.slice(0,4)},CHILD:function(f){return f[1]=f[1].toLowerCase(),f[1].slice(0,3)==="nth"?(f[3]||W.error(f[0]),f[4]=+(f[4]?f[5]+(f[6]||1):2*(f[3]==="even"||f[3]==="odd")),f[5]=+(f[7]+f[8]||f[3]==="odd")):f[3]&&W.error(f[0]),f},PSEUDO:function(f){var g,b=!f[6]&&f[2];return Qe.CHILD.test(f[0])?null:(f[3]?f[2]=f[4]||f[5]||"":b&&gt.test(b)&&(g=Xn(b,!0))&&(g=b.indexOf(")",b.length-g)-b.length)&&(f[0]=f[0].slice(0,g),f[2]=b.slice(0,g)),f.slice(0,3))}},filter:{TAG:function(f){var g=f.replace(Ce,ze).toLowerCase();return f==="*"?function(){return!0}:function(b){return he(b,g)}},CLASS:function(f){var g=H[f+" "];return g||(g=new RegExp("(^|"+ce+")"+f+"("+ce+"|$)"))&&H(f,function(b){return g.test(typeof b.className=="string"&&b.className||typeof b.getAttribute<"u"&&b.getAttribute("class")||"")})},ATTR:function(f,g,b){return function(x){var C=W.attr(x,f);return C==null?g==="!=":!g||(C+="",g==="="?C===b:g==="!="?C!==b:g==="^="?b&&C.indexOf(b)===0:g==="*="?b&&-1<C.indexOf(b):g==="$="?b&&C.slice(-b.length)===b:g==="~="?-1<(" "+C.replace(se," ")+" ").indexOf(b):g==="|="&&(C===b||C.slice(0,b.length+1)===b+"-"))}},CHILD:function(f,g,b,x,C){var L=f.slice(0,3)!=="nth",j=f.slice(-4)!=="last",$=g==="of-type";return x===1&&C===0?function(N){return!!N.parentNode}:function(N,le,Y){var G,oe,V,Ae,Pe,Me=L!==j?"nextSibling":"previousSibling",Ge=N.parentNode,Oe=$&&N.nodeName.toLowerCase(),De=!Y&&!$,pe=!1;if(Ge){if(L){for(;Me;){for(V=N;V=V[Me];)if($?he(V,Oe):V.nodeType===1)return!1;Pe=Me=f==="only"&&!Pe&&"nextSibling"}return!0}if(Pe=[j?Ge.firstChild:Ge.lastChild],j&&De){for(pe=(Ae=(G=(oe=Ge[E]||(Ge[E]={}))[f]||[])[0]===_&&G[1])&&G[2],V=Ae&&Ge.childNodes[Ae];V=++Ae&&V&&V[Me]||(pe=Ae=0)||Pe.pop();)if(V.nodeType===1&&++pe&&V===N){oe[f]=[_,Ae,pe];break}}else if(De&&(pe=Ae=(G=(oe=N[E]||(N[E]={}))[f]||[])[0]===_&&G[1]),pe===!1)for(;(V=++Ae&&V&&V[Me]||(pe=Ae=0)||Pe.pop())&&!(($?he(V,Oe):V.nodeType===1)&&++pe&&(De&&((oe=V[E]||(V[E]={}))[f]=[_,pe]),V===N)););return(pe-=C)===x||pe%x==0&&0<=pe/x}}},PSEUDO:function(f,g){var b,x=n.pseudos[f]||n.setFilters[f.toLowerCase()]||W.error("unsupported pseudo: "+f);return x[E]?x(g):1<x.length?(b=[f,f,"",g],n.setFilters.hasOwnProperty(f.toLowerCase())?Ee(function(C,L){for(var j,$=x(C,g),N=$.length;N--;)C[j=Ue.call(C,$[N])]=!(L[j]=$[N])}):function(C){return x(C,0,b)}):x}},pseudos:{not:Ee(function(f){var g=[],b=[],x=wi(f.replace(an,"$1"));return x[E]?Ee(function(C,L,j,$){for(var N,le=x(C,null,$,[]),Y=C.length;Y--;)(N=le[Y])&&(C[Y]=!(L[Y]=N))}):function(C,L,j){return g[0]=C,x(g,null,j,b),g[0]=null,!b.pop()}}),has:Ee(function(f){return function(g){return 0<W(f,g).length}}),contains:Ee(function(f){return f=f.replace(Ce,ze),function(g){return-1<(g.textContent||o.text(g)).indexOf(f)}}),lang:Ee(function(f){return Bn.test(f||"")||W.error("unsupported lang: "+f),f=f.replace(Ce,ze).toLowerCase(),function(g){var b;do if(b=p?g.lang:g.getAttribute("xml:lang")||g.getAttribute("lang"))return(b=b.toLowerCase())===f||b.indexOf(f+"-")===0;while((g=g.parentNode)&&g.nodeType===1);return!1}}),target:function(f){var g=F.location&&F.location.hash;return g&&g.slice(1)===f.id},root:function(f){return f===u},focus:function(f){return f===function(){try{return c.activeElement}catch{}}()&&c.hasFocus()&&!!(f.type||f.href||~f.tabIndex)},enabled:zn(!1),disabled:zn(!0),checked:function(f){return he(f,"input")&&!!f.checked||he(f,"option")&&!!f.selected},selected:function(f){return f.parentNode&&f.parentNode.selectedIndex,f.selected===!0},empty:function(f){for(f=f.firstChild;f;f=f.nextSibling)if(f.nodeType<6)return!1;return!0},parent:function(f){return!n.pseudos.empty(f)},header:function(f){return kn.test(f.nodeName)},input:function(f){return Ke.test(f.nodeName)},button:function(f){return he(f,"input")&&f.type==="button"||he(f,"button")},text:function(f){var g;return he(f,"input")&&f.type==="text"&&((g=f.getAttribute("type"))==null||g.toLowerCase()==="text")},first:tn(function(){return[0]}),last:tn(function(f,g){return[g-1]}),eq:tn(function(f,g,b){return[b<0?b+g:b]}),even:tn(function(f,g){for(var b=0;b<g;b+=2)f.push(b);return f}),odd:tn(function(f,g){for(var b=1;b<g;b+=2)f.push(b);return f}),lt:tn(function(f,g,b){var x;for(x=b<0?b+g:g<b?g:b;0<=--x;)f.push(x);return f}),gt:tn(function(f,g,b){for(var x=b<0?b+g:b;++x<g;)f.push(x);return f})}}).pseudos.nth=n.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})n.pseudos[e]=mt(e);for(e in{submit:!0,reset:!0})n.pseudos[e]=en(e);function Vn(){}function Xn(f,g){var b,x,C,L,j,$,N,le=z[f+" "];if(le)return g?0:le.slice(0);for(j=f,$=[],N=n.preFilter;j;){for(L in b&&!(x=we.exec(j))||(x&&(j=j.slice(x[0].length)||j),$.push(C=[])),b=!1,(x=Wn.exec(j))&&(b=x.shift(),C.push({value:b,type:x[0].replace(an," ")}),j=j.slice(b.length)),n.filter)!(x=Qe[L].exec(j))||N[L]&&!(x=N[L](x))||(b=x.shift(),C.push({value:b,type:L,matches:x}),j=j.slice(b.length));if(!b)break}return g?j.length:j?W.error(f):z(f,$).slice(0)}function vi(f){for(var g=0,b=f.length,x="";g<b;g++)x+=f[g].value;return x}function Un(f,g,b){var x=g.dir,C=g.next,L=C||x,j=b&&L==="parentNode",$=A++;return g.first?function(N,le,Y){for(;N=N[x];)if(N.nodeType===1||j)return f(N,le,Y);return!1}:function(N,le,Y){var G,oe,V=[_,$];if(Y){for(;N=N[x];)if((N.nodeType===1||j)&&f(N,le,Y))return!0}else for(;N=N[x];)if(N.nodeType===1||j)if(oe=N[E]||(N[E]={}),C&&he(N,C))N=N[x]||N;else{if((G=oe[L])&&G[0]===_&&G[1]===$)return V[2]=G[2];if((oe[L]=V)[2]=f(N,le,Y))return!0}return!1}}function Qi(f){return 1<f.length?function(g,b,x){for(var C=f.length;C--;)if(!f[C](g,b,x))return!1;return!0}:f[0]}function yi(f,g,b,x,C){for(var L,j=[],$=0,N=f.length,le=g!=null;$<N;$++)(L=f[$])&&(b&&!b(L,x,C)||(j.push(L),le&&g.push($)));return j}function bi(f,g,b,x,C,L){return x&&!x[E]&&(x=bi(x)),C&&!C[E]&&(C=bi(C,L)),Ee(function(j,$,N,le){var Y,G,oe,V,Ae=[],Pe=[],Me=$.length,Ge=j||function(De,pe,nn){for(var rt=0,Qn=pe.length;rt<Qn;rt++)W(De,pe[rt],nn);return nn}(g||"*",N.nodeType?[N]:N,[]),Oe=!f||!j&&g?Ge:yi(Ge,Ae,f,N,le);if(b?b(Oe,V=C||(j?f:Me||x)?[]:$,N,le):V=Oe,x)for(Y=yi(V,Pe),x(Y,[],N,le),G=Y.length;G--;)(oe=Y[G])&&(V[Pe[G]]=!(Oe[Pe[G]]=oe));if(j){if(C||f){if(C){for(Y=[],G=V.length;G--;)(oe=V[G])&&Y.push(Oe[G]=oe);C(null,V=[],Y,le)}for(G=V.length;G--;)(oe=V[G])&&-1<(Y=C?Ue.call(j,oe):Ae[G])&&(j[Y]=!($[Y]=oe))}}else V=yi(V===$?V.splice(Me,V.length):V),C?C(null,$,V,le):w.apply($,V)})}function _i(f){for(var g,b,x,C=f.length,L=n.relative[f[0].type],j=L||n.relative[" "],$=L?1:0,N=Un(function(G){return G===g},j,!0),le=Un(function(G){return-1<Ue.call(g,G)},j,!0),Y=[function(G,oe,V){var Ae=!L&&(V||oe!=i)||((g=oe).nodeType?N(G,oe,V):le(G,oe,V));return g=null,Ae}];$<C;$++)if(b=n.relative[f[$].type])Y=[Un(Qi(Y),b)];else{if((b=n.filter[f[$].type].apply(null,f[$].matches))[E]){for(x=++$;x<C&&!n.relative[f[x].type];x++);return bi(1<$&&Qi(Y),1<$&&vi(f.slice(0,$-1).concat({value:f[$-2].type===" "?"*":""})).replace(an,"$1"),b,$<x&&_i(f.slice($,x)),x<C&&_i(f=f.slice(x)),x<C&&vi(f))}Y.push(b)}return Qi(Y)}function wi(f,g){var b,x,C,L,j,$,N=[],le=[],Y=X[f+" "];if(!Y){for(g||(g=Xn(f)),b=g.length;b--;)(Y=_i(g[b]))[E]?N.push(Y):le.push(Y);(Y=X(f,(x=le,L=0<(C=N).length,j=0<x.length,$=function(G,oe,V,Ae,Pe){var Me,Ge,Oe,De=0,pe="0",nn=G&&[],rt=[],Qn=i,Ki=G||j&&n.find.TAG("*",Pe),Gi=_+=Qn==null?1:Math.random()||.1,qr=Ki.length;for(Pe&&(i=oe==c||oe||Pe);pe!==qr&&(Me=Ki[pe])!=null;pe++){if(j&&Me){for(Ge=0,oe||Me.ownerDocument==c||(Nt(Me),V=!p);Oe=x[Ge++];)if(Oe(Me,oe||c,V)){w.call(Ae,Me);break}Pe&&(_=Gi)}L&&((Me=!Oe&&Me)&&De--,G&&nn.push(Me))}if(De+=pe,L&&pe!==De){for(Ge=0;Oe=C[Ge++];)Oe(nn,rt,oe,V);if(G){if(0<De)for(;pe--;)nn[pe]||rt[pe]||(rt[pe]=jr.call(Ae));rt=yi(rt)}w.apply(Ae,rt),Pe&&!G&&0<rt.length&&1<De+C.length&&o.uniqueSort(Ae)}return Pe&&(_=Gi,i=Qn),nn},L?Ee($):$))).selector=f}return Y}function Yn(f,g,b,x){var C,L,j,$,N,le=typeof f=="function"&&f,Y=!x&&Xn(f=le.selector||f);if(b=b||[],Y.length===1){if(2<(L=Y[0]=Y[0].slice(0)).length&&(j=L[0]).type==="ID"&&g.nodeType===9&&p&&n.relative[L[1].type]){if(!(g=(n.find.ID(j.matches[0].replace(Ce,ze),g)||[])[0]))return b;le&&(g=g.parentNode),f=f.slice(L.shift().value.length)}for(C=Qe.needsContext.test(f)?0:L.length;C--&&(j=L[C],!n.relative[$=j.type]);)if((N=n.find[$])&&(x=N(j.matches[0].replace(Ce,ze),Ne.test(L[0].type)&&Yi(g.parentNode)||g))){if(L.splice(C,1),!(f=x.length&&vi(L)))return w.apply(b,x),b;break}}return(le||wi(f,Y))(x,g,!p,b,!g||Ne.test(f)&&Yi(g.parentNode)||g),b}Vn.prototype=n.filters=n.pseudos,n.setFilters=new Vn,K.sortStable=E.split("").sort(ye).join("")===E,Nt(),K.sortDetached=re(function(f){return 1&f.compareDocumentPosition(c.createElement("fieldset"))}),o.find=W,o.expr[":"]=o.expr.pseudos,o.unique=o.uniqueSort,W.compile=wi,W.select=Yn,W.setDocument=Nt,W.tokenize=Xn,W.escape=o.escapeSelector,W.getText=o.text,W.isXML=o.isXMLDoc,W.selectors=o.expr,W.support=o.support,W.uniqueSort=o.uniqueSort})();var S=function(e,n,i){for(var r=[],a=i!==void 0;(e=e[n])&&e.nodeType!==9;)if(e.nodeType===1){if(a&&o(e).is(i))break;r.push(e)}return r},ri=function(e,n){for(var i=[];e;e=e.nextSibling)e.nodeType===1&&e!==n&&i.push(e);return i},ki=o.expr.match.needsContext,oi=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function lt(e,n,i){return B(n)?o.grep(e,function(r,a){return!!n.call(r,a,r)!==i}):n.nodeType?o.grep(e,function(r){return r===n!==i}):typeof n!="string"?o.grep(e,function(r){return-1<Ue.call(n,r)!==i}):o.filter(n,e,i)}o.filter=function(e,n,i){var r=n[0];return i&&(e=":not("+e+")"),n.length===1&&r.nodeType===1?o.find.matchesSelector(r,e)?[r]:[]:o.find.matches(e,o.grep(n,function(a){return a.nodeType===1}))},o.fn.extend({find:function(e){var n,i,r=this.length,a=this;if(typeof e!="string")return this.pushStack(o(e).filter(function(){for(n=0;n<r;n++)if(o.contains(a[n],this))return!0}));for(i=this.pushStack([]),n=0;n<r;n++)o.find(e,a[n],i);return 1<r?o.uniqueSort(i):i},filter:function(e){return this.pushStack(lt(this,e||[],!1))},not:function(e){return this.pushStack(lt(this,e||[],!0))},is:function(e){return!!lt(this,typeof e=="string"&&ki.test(e)?o(e):e||[],!1).length}});var ln,ct=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(o.fn.init=function(e,n,i){var r,a;if(!e)return this;if(i=i||ln,typeof e=="string"){if(!(r=e[0]==="<"&&e[e.length-1]===">"&&3<=e.length?[null,e,null]:ct.exec(e))||!r[1]&&n)return!n||n.jquery?(n||i).find(e):this.constructor(n).find(e);if(r[1]){if(n=n instanceof o?n[0]:n,o.merge(this,o.parseHTML(r[1],n&&n.nodeType?n.ownerDocument||n:q,!0)),oi.test(r[1])&&o.isPlainObject(n))for(r in n)B(this[r])?this[r](n[r]):this.attr(r,n[r]);return this}return(a=q.getElementById(r[2]))&&(this[0]=a,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):B(e)?i.ready!==void 0?i.ready(e):e(o):o.makeArray(e,this)}).prototype=o.fn,ln=o(q);var Si=/^(?:parents|prev(?:Until|All))/,R={children:!0,contents:!0,next:!0,prev:!0};function Dn(e,n){for(;(e=e[n])&&e.nodeType!==1;);return e}o.fn.extend({has:function(e){var n=o(e,this),i=n.length;return this.filter(function(){for(var r=0;r<i;r++)if(o.contains(this,n[r]))return!0})},closest:function(e,n){var i,r=0,a=this.length,c=[],u=typeof e!="string"&&o(e);if(!ki.test(e)){for(;r<a;r++)for(i=this[r];i&&i!==n;i=i.parentNode)if(i.nodeType<11&&(u?-1<u.index(i):i.nodeType===1&&o.find.matchesSelector(i,e))){c.push(i);break}}return this.pushStack(1<c.length?o.uniqueSort(c):c)},index:function(e){return e?typeof e=="string"?Ue.call(o(e),this[0]):Ue.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,n){return this.pushStack(o.uniqueSort(o.merge(this.get(),o(e,n))))},addBack:function(e){return this.add(e==null?this.prevObject:this.prevObject.filter(e))}}),o.each({parent:function(e){var n=e.parentNode;return n&&n.nodeType!==11?n:null},parents:function(e){return S(e,"parentNode")},parentsUntil:function(e,n,i){return S(e,"parentNode",i)},next:function(e){return Dn(e,"nextSibling")},prev:function(e){return Dn(e,"previousSibling")},nextAll:function(e){return S(e,"nextSibling")},prevAll:function(e){return S(e,"previousSibling")},nextUntil:function(e,n,i){return S(e,"nextSibling",i)},prevUntil:function(e,n,i){return S(e,"previousSibling",i)},siblings:function(e){return ri((e.parentNode||{}).firstChild,e)},children:function(e){return ri(e.firstChild)},contents:function(e){return e.contentDocument!=null&&Ei(e.contentDocument)?e.contentDocument:(he(e,"template")&&(e=e.content||e),o.merge([],e.childNodes))}},function(e,n){o.fn[e]=function(i,r){var a=o.map(this,n,i);return e.slice(-5)!=="Until"&&(r=i),r&&typeof r=="string"&&(a=o.filter(r,a)),1<this.length&&(R[e]||o.uniqueSort(a),Si.test(e)&&a.reverse()),this.pushStack(a)}});var ut=/[^\x20\t\r\n\f]+/g;function cn(e){return e}function si(e){throw e}function un(e,n,i,r){var a;try{e&&B(a=e.promise)?a.call(e).done(n).fail(i):e&&B(a=e.then)?a.call(e,n,i):n.apply(void 0,[e].slice(r))}catch(c){i.apply(void 0,[c])}}o.Callbacks=function(e){var n,i;e=typeof e=="string"?(n=e,i={},o.each(n.match(ut)||[],function(_,A){i[A]=!0}),i):o.extend({},e);var r,a,c,u,p=[],h=[],v=-1,w=function(){for(u=u||e.once,c=r=!0;h.length;v=-1)for(a=h.shift();++v<p.length;)p[v].apply(a[0],a[1])===!1&&e.stopOnFalse&&(v=p.length,a=!1);e.memory||(a=!1),r=!1,u&&(p=a?[]:"")},E={add:function(){return p&&(a&&!r&&(v=p.length-1,h.push(a)),function _(A){o.each(A,function(H,z){B(z)?e.unique&&E.has(z)||p.push(z):z&&z.length&&sn(z)!=="string"&&_(z)})}(arguments),a&&!r&&w()),this},remove:function(){return o.each(arguments,function(_,A){for(var H;-1<(H=o.inArray(A,p,H));)p.splice(H,1),H<=v&&v--}),this},has:function(_){return _?-1<o.inArray(_,p):0<p.length},empty:function(){return p&&(p=[]),this},disable:function(){return u=h=[],p=a="",this},disabled:function(){return!p},lock:function(){return u=h=[],a||r||(p=a=""),this},locked:function(){return!!u},fireWith:function(_,A){return u||(A=[_,(A=A||[]).slice?A.slice():A],h.push(A),r||w()),this},fire:function(){return E.fireWith(this,arguments),this},fired:function(){return!!c}};return E},o.extend({Deferred:function(e){var n=[["notify","progress",o.Callbacks("memory"),o.Callbacks("memory"),2],["resolve","done",o.Callbacks("once memory"),o.Callbacks("once memory"),0,"resolved"],["reject","fail",o.Callbacks("once memory"),o.Callbacks("once memory"),1,"rejected"]],i="pending",r={state:function(){return i},always:function(){return a.done(arguments).fail(arguments),this},catch:function(c){return r.then(null,c)},pipe:function(){var c=arguments;return o.Deferred(function(u){o.each(n,function(p,h){var v=B(c[h[4]])&&c[h[4]];a[h[1]](function(){var w=v&&v.apply(this,arguments);w&&B(w.promise)?w.promise().progress(u.notify).done(u.resolve).fail(u.reject):u[h[0]+"With"](this,v?[w]:arguments)})}),c=null}).promise()},then:function(c,u,p){var h=0;function v(w,E,_,A){return function(){var H=this,z=arguments,X=function(){var ye,it;if(!(w<h)){if((ye=_.apply(H,z))===E.promise())throw new TypeError("Thenable self-resolution");it=ye&&(typeof ye=="object"||typeof ye=="function")&&ye.then,B(it)?A?it.call(ye,v(h,E,cn,A),v(h,E,si,A)):(h++,it.call(ye,v(h,E,cn,A),v(h,E,si,A),v(h,E,cn,E.notifyWith))):(_!==cn&&(H=void 0,z=[ye]),(A||E.resolveWith)(H,z))}},_e=A?X:function(){try{X()}catch(ye){o.Deferred.exceptionHook&&o.Deferred.exceptionHook(ye,_e.error),h<=w+1&&(_!==si&&(H=void 0,z=[ye]),E.rejectWith(H,z))}};w?_e():(o.Deferred.getErrorHook?_e.error=o.Deferred.getErrorHook():o.Deferred.getStackHook&&(_e.error=o.Deferred.getStackHook()),F.setTimeout(_e))}}return o.Deferred(function(w){n[0][3].add(v(0,w,B(p)?p:cn,w.notifyWith)),n[1][3].add(v(0,w,B(c)?c:cn)),n[2][3].add(v(0,w,B(u)?u:si))}).promise()},promise:function(c){return c!=null?o.extend(c,r):r}},a={};return o.each(n,function(c,u){var p=u[2],h=u[5];r[u[1]]=p.add,h&&p.add(function(){i=h},n[3-c][2].disable,n[3-c][3].disable,n[0][2].lock,n[0][3].lock),p.add(u[3].fire),a[u[0]]=function(){return a[u[0]+"With"](this===a?void 0:this,arguments),this},a[u[0]+"With"]=p.fireWith}),r.promise(a),e&&e.call(a,a),a},when:function(e){var n=arguments.length,i=n,r=Array(i),a=Xe.call(arguments),c=o.Deferred(),u=function(p){return function(h){r[p]=this,a[p]=1<arguments.length?Xe.call(arguments):h,--n||c.resolveWith(r,a)}};if(n<=1&&(un(e,c.done(u(i)).resolve,c.reject,!n),c.state()==="pending"||B(a[i]&&a[i].then)))return c.then();for(;i--;)un(a[i],u(i),c.reject);return c.promise()}});var ar=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;o.Deferred.exceptionHook=function(e,n){F.console&&F.console.warn&&e&&ar.test(e.name)&&F.console.warn("jQuery.Deferred exception: "+e.message,e.stack,n)},o.readyException=function(e){F.setTimeout(function(){throw e})};var zt=o.Deferred();function kt(){q.removeEventListener("DOMContentLoaded",kt),F.removeEventListener("load",kt),o.ready()}o.fn.ready=function(e){return zt.then(e).catch(function(n){o.readyException(n)}),this},o.extend({isReady:!1,readyWait:1,ready:function(e){(e===!0?--o.readyWait:o.isReady)||(o.isReady=!0)!==e&&0<--o.readyWait||zt.resolveWith(q,[o])}}),o.ready.then=zt.then,q.readyState==="complete"||q.readyState!=="loading"&&!q.documentElement.doScroll?F.setTimeout(o.ready):(q.addEventListener("DOMContentLoaded",kt),F.addEventListener("load",kt));var St=function(e,n,i,r,a,c,u){var p=0,h=e.length,v=i==null;if(sn(i)==="object")for(p in a=!0,i)St(e,n,p,i[p],!0,c,u);else if(r!==void 0&&(a=!0,B(r)||(u=!0),v&&(u?(n.call(e,r),n=null):(v=n,n=function(w,E,_){return v.call(o(w),_)})),n))for(;p<h;p++)n(e[p],i,u?r:r.call(e[p],p,n(e[p],i)));return a?e:v?n.call(e):h?n(e[0],i):c},Nr=/^-ms-/,Pr=/-([a-z])/g;function Mr(e,n){return n.toUpperCase()}function bt(e){return e.replace(Nr,"ms-").replace(Pr,Mr)}var Ln=function(e){return e.nodeType===1||e.nodeType===9||!+e.nodeType};function jn(){this.expando=o.expando+jn.uid++}jn.uid=1,jn.prototype={cache:function(e){var n=e[this.expando];return n||(n={},Ln(e)&&(e.nodeType?e[this.expando]=n:Object.defineProperty(e,this.expando,{value:n,configurable:!0}))),n},set:function(e,n,i){var r,a=this.cache(e);if(typeof n=="string")a[bt(n)]=i;else for(r in n)a[bt(r)]=n[r];return a},get:function(e,n){return n===void 0?this.cache(e):e[this.expando]&&e[this.expando][bt(n)]},access:function(e,n,i){return n===void 0||n&&typeof n=="string"&&i===void 0?this.get(e,n):(this.set(e,n,i),i!==void 0?i:n)},remove:function(e,n){var i,r=e[this.expando];if(r!==void 0){if(n!==void 0)for(i=(n=Array.isArray(n)?n.map(bt):(n=bt(n))in r?[n]:n.match(ut)||[]).length;i--;)delete r[n[i]];(n===void 0||o.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var n=e[this.expando];return n!==void 0&&!o.isEmptyObject(n)}};var I=new jn,xe=new jn,lr=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Nn=/[A-Z]/g;function Vt(e,n,i){var r,a;if(i===void 0&&e.nodeType===1)if(r="data-"+n.replace(Nn,"-$&").toLowerCase(),typeof(i=e.getAttribute(r))=="string"){try{i=(a=i)==="true"||a!=="false"&&(a==="null"?null:a===+a+""?+a:lr.test(a)?JSON.parse(a):a)}catch{}xe.set(e,n,i)}else i=void 0;return i}o.extend({hasData:function(e){return xe.hasData(e)||I.hasData(e)},data:function(e,n,i){return xe.access(e,n,i)},removeData:function(e,n){xe.remove(e,n)},_data:function(e,n,i){return I.access(e,n,i)},_removeData:function(e,n){I.remove(e,n)}}),o.fn.extend({data:function(e,n){var i,r,a,c=this[0],u=c&&c.attributes;if(e===void 0){if(this.length&&(a=xe.get(c),c.nodeType===1&&!I.get(c,"hasDataAttrs"))){for(i=u.length;i--;)u[i]&&(r=u[i].name).indexOf("data-")===0&&(r=bt(r.slice(5)),Vt(c,r,a[r]));I.set(c,"hasDataAttrs",!0)}return a}return typeof e=="object"?this.each(function(){xe.set(this,e)}):St(this,function(p){var h;if(c&&p===void 0)return(h=xe.get(c,e))!==void 0||(h=Vt(c,e))!==void 0?h:void 0;this.each(function(){xe.set(this,e,p)})},null,n,1<arguments.length,null,!0)},removeData:function(e){return this.each(function(){xe.remove(this,e)})}}),o.extend({queue:function(e,n,i){var r;if(e)return n=(n||"fx")+"queue",r=I.get(e,n),i&&(!r||Array.isArray(i)?r=I.access(e,n,o.makeArray(i)):r.push(i)),r||[]},dequeue:function(e,n){n=n||"fx";var i=o.queue(e,n),r=i.length,a=i.shift(),c=o._queueHooks(e,n);a==="inprogress"&&(a=i.shift(),r--),a&&(n==="fx"&&i.unshift("inprogress"),delete c.stop,a.call(e,function(){o.dequeue(e,n)},c)),!r&&c&&c.empty.fire()},_queueHooks:function(e,n){var i=n+"queueHooks";return I.get(e,i)||I.access(e,i,{empty:o.Callbacks("once memory").add(function(){I.remove(e,[n+"queue",i])})})}}),o.fn.extend({queue:function(e,n){var i=2;return typeof e!="string"&&(n=e,e="fx",i--),arguments.length<i?o.queue(this[0],e):n===void 0?this:this.each(function(){var r=o.queue(this,e,n);o._queueHooks(this,e),e==="fx"&&r[0]!=="inprogress"&&o.dequeue(this,e)})},dequeue:function(e){return this.each(function(){o.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,n){var i,r=1,a=o.Deferred(),c=this,u=this.length,p=function(){--r||a.resolveWith(c,[c])};for(typeof e!="string"&&(n=e,e=void 0),e=e||"fx";u--;)(i=I.get(c[u],e+"queueHooks"))&&i.empty&&(r++,i.empty.add(p));return p(),a.promise(n)}});var Xt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,It=new RegExp("^(?:([+-])=|)("+Xt+")([a-z%]*)$","i"),Ot=["Top","Right","Bottom","Left"],Dt=q.documentElement,dn=function(e){return o.contains(e.ownerDocument,e)},Ir={composed:!0};Dt.getRootNode&&(dn=function(e){return o.contains(e.ownerDocument,e)||e.getRootNode(Ir)===e.ownerDocument});var ai=function(e,n){return(e=n||e).style.display==="none"||e.style.display===""&&dn(e)&&o.css(e,"display")==="none"};function cr(e,n,i,r){var a,c,u=20,p=r?function(){return r.cur()}:function(){return o.css(e,n,"")},h=p(),v=i&&i[3]||(o.cssNumber[n]?"":"px"),w=e.nodeType&&(o.cssNumber[n]||v!=="px"&&+h)&&It.exec(o.css(e,n));if(w&&w[3]!==v){for(h/=2,v=v||w[3],w=+h||1;u--;)o.style(e,n,w+v),(1-c)*(1-(c=p()/h||.5))<=0&&(u=0),w/=c;w*=2,o.style(e,n,w+v),i=i||[]}return i&&(w=+w||+h||0,a=i[1]?w+(i[1]+1)*i[2]:+i[2],r&&(r.unit=v,r.start=w,r.end=a)),a}var ur={};function fn(e,n){for(var i,r,a,c,u,p,h,v=[],w=0,E=e.length;w<E;w++)(r=e[w]).style&&(i=r.style.display,n?(i==="none"&&(v[w]=I.get(r,"display")||null,v[w]||(r.style.display="")),r.style.display===""&&ai(r)&&(v[w]=(h=u=c=void 0,u=(a=r).ownerDocument,p=a.nodeName,(h=ur[p])||(c=u.body.appendChild(u.createElement(p)),h=o.css(c,"display"),c.parentNode.removeChild(c),h==="none"&&(h="block"),ur[p]=h)))):i!=="none"&&(v[w]="none",I.set(r,"display",i)));for(w=0;w<E;w++)v[w]!=null&&(e[w].style.display=v[w]);return e}o.fn.extend({show:function(){return fn(this,!0)},hide:function(){return fn(this)},toggle:function(e){return typeof e=="boolean"?e?this.show():this.hide():this.each(function(){ai(this)?o(this).show():o(this).hide()})}});var $t,Ut,hn=/^(?:checkbox|radio)$/i,Oi=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,dr=/^$|^module$|\/(?:java|ecma)script/i;$t=q.createDocumentFragment().appendChild(q.createElement("div")),(Ut=q.createElement("input")).setAttribute("type","radio"),Ut.setAttribute("checked","checked"),Ut.setAttribute("name","t"),$t.appendChild(Ut),K.checkClone=$t.cloneNode(!0).cloneNode(!0).lastChild.checked,$t.innerHTML="<textarea>x</textarea>",K.noCloneChecked=!!$t.cloneNode(!0).lastChild.defaultValue,$t.innerHTML="<option></option>",K.option=!!$t.lastChild;var tt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Ye(e,n){var i;return i=typeof e.getElementsByTagName<"u"?e.getElementsByTagName(n||"*"):typeof e.querySelectorAll<"u"?e.querySelectorAll(n||"*"):[],n===void 0||n&&he(e,n)?o.merge([e],i):i}function Di(e,n){for(var i=0,r=e.length;i<r;i++)I.set(e[i],"globalEval",!n||I.get(n[i],"globalEval"))}tt.tbody=tt.tfoot=tt.colgroup=tt.caption=tt.thead,tt.th=tt.td,K.option||(tt.optgroup=tt.option=[1,"<select multiple='multiple'>","</select>"]);var pn=/<|&#?\w+;/;function gn(e,n,i,r,a){for(var c,u,p,h,v,w,E=n.createDocumentFragment(),_=[],A=0,H=e.length;A<H;A++)if((c=e[A])||c===0)if(sn(c)==="object")o.merge(_,c.nodeType?[c]:c);else if(pn.test(c)){for(u=u||E.appendChild(n.createElement("div")),p=(Oi.exec(c)||["",""])[1].toLowerCase(),h=tt[p]||tt._default,u.innerHTML=h[1]+o.htmlPrefilter(c)+h[2],w=h[0];w--;)u=u.lastChild;o.merge(_,u.childNodes),(u=E.firstChild).textContent=""}else _.push(n.createTextNode(c));for(E.textContent="",A=0;c=_[A++];)if(r&&-1<o.inArray(c,r))a&&a.push(c);else if(v=dn(c),u=Ye(E.appendChild(c),"script"),v&&Di(u),i)for(w=0;c=u[w++];)dr.test(c.type||"")&&i.push(c);return E}var fr=/^([^.]*)(?:\.(.+)|)/;function mn(){return!0}function vn(){return!1}function Li(e,n,i,r,a,c){var u,p;if(typeof n=="object"){for(p in typeof i!="string"&&(r=r||i,i=void 0),n)Li(e,p,i,r,n[p],c);return e}if(r==null&&a==null?(a=i,r=i=void 0):a==null&&(typeof i=="string"?(a=r,r=void 0):(a=r,r=i,i=void 0)),a===!1)a=vn;else if(!a)return e;return c===1&&(u=a,(a=function(h){return o().off(h),u.apply(this,arguments)}).guid=u.guid||(u.guid=o.guid++)),e.each(function(){o.event.add(this,n,a,r,i)})}function li(e,n,i){i?(I.set(e,n,!1),o.event.add(e,n,{namespace:!1,handler:function(r){var a,c=I.get(this,n);if(1&r.isTrigger&&this[n]){if(c)(o.event.special[n]||{}).delegateType&&r.stopPropagation();else if(c=Xe.call(arguments),I.set(this,n,c),this[n](),a=I.get(this,n),I.set(this,n,!1),c!==a)return r.stopImmediatePropagation(),r.preventDefault(),a}else c&&(I.set(this,n,o.event.trigger(c[0],c.slice(1),this)),r.stopPropagation(),r.isImmediatePropagationStopped=mn)}})):I.get(e,n)===void 0&&o.event.add(e,n,mn)}o.event={global:{},add:function(e,n,i,r,a){var c,u,p,h,v,w,E,_,A,H,z,X=I.get(e);if(Ln(e))for(i.handler&&(i=(c=i).handler,a=c.selector),a&&o.find.matchesSelector(Dt,a),i.guid||(i.guid=o.guid++),(h=X.events)||(h=X.events=Object.create(null)),(u=X.handle)||(u=X.handle=function(_e){return typeof o<"u"&&o.event.triggered!==_e.type?o.event.dispatch.apply(e,arguments):void 0}),v=(n=(n||"").match(ut)||[""]).length;v--;)A=z=(p=fr.exec(n[v])||[])[1],H=(p[2]||"").split(".").sort(),A&&(E=o.event.special[A]||{},A=(a?E.delegateType:E.bindType)||A,E=o.event.special[A]||{},w=o.extend({type:A,origType:z,data:r,handler:i,guid:i.guid,selector:a,needsContext:a&&o.expr.match.needsContext.test(a),namespace:H.join(".")},c),(_=h[A])||((_=h[A]=[]).delegateCount=0,E.setup&&E.setup.call(e,r,H,u)!==!1||e.addEventListener&&e.addEventListener(A,u)),E.add&&(E.add.call(e,w),w.handler.guid||(w.handler.guid=i.guid)),a?_.splice(_.delegateCount++,0,w):_.push(w),o.event.global[A]=!0)},remove:function(e,n,i,r,a){var c,u,p,h,v,w,E,_,A,H,z,X=I.hasData(e)&&I.get(e);if(X&&(h=X.events)){for(v=(n=(n||"").match(ut)||[""]).length;v--;)if(A=z=(p=fr.exec(n[v])||[])[1],H=(p[2]||"").split(".").sort(),A){for(E=o.event.special[A]||{},_=h[A=(r?E.delegateType:E.bindType)||A]||[],p=p[2]&&new RegExp("(^|\\.)"+H.join("\\.(?:.*\\.|)")+"(\\.|$)"),u=c=_.length;c--;)w=_[c],!a&&z!==w.origType||i&&i.guid!==w.guid||p&&!p.test(w.namespace)||r&&r!==w.selector&&(r!=="**"||!w.selector)||(_.splice(c,1),w.selector&&_.delegateCount--,E.remove&&E.remove.call(e,w));u&&!_.length&&(E.teardown&&E.teardown.call(e,H,X.handle)!==!1||o.removeEvent(e,A,X.handle),delete h[A])}else for(A in h)o.event.remove(e,A+n[v],i,r,!0);o.isEmptyObject(h)&&I.remove(e,"handle events")}},dispatch:function(e){var n,i,r,a,c,u,p=new Array(arguments.length),h=o.event.fix(e),v=(I.get(this,"events")||Object.create(null))[h.type]||[],w=o.event.special[h.type]||{};for(p[0]=h,n=1;n<arguments.length;n++)p[n]=arguments[n];if(h.delegateTarget=this,!w.preDispatch||w.preDispatch.call(this,h)!==!1){for(u=o.event.handlers.call(this,h,v),n=0;(a=u[n++])&&!h.isPropagationStopped();)for(h.currentTarget=a.elem,i=0;(c=a.handlers[i++])&&!h.isImmediatePropagationStopped();)h.rnamespace&&c.namespace!==!1&&!h.rnamespace.test(c.namespace)||(h.handleObj=c,h.data=c.data,(r=((o.event.special[c.origType]||{}).handle||c.handler).apply(a.elem,p))!==void 0&&(h.result=r)===!1&&(h.preventDefault(),h.stopPropagation()));return w.postDispatch&&w.postDispatch.call(this,h),h.result}},handlers:function(e,n){var i,r,a,c,u,p=[],h=n.delegateCount,v=e.target;if(h&&v.nodeType&&!(e.type==="click"&&1<=e.button)){for(;v!==this;v=v.parentNode||this)if(v.nodeType===1&&(e.type!=="click"||v.disabled!==!0)){for(c=[],u={},i=0;i<h;i++)u[a=(r=n[i]).selector+" "]===void 0&&(u[a]=r.needsContext?-1<o(a,this).index(v):o.find(a,this,null,[v]).length),u[a]&&c.push(r);c.length&&p.push({elem:v,handlers:c})}}return v=this,h<n.length&&p.push({elem:v,handlers:n.slice(h)}),p},addProp:function(e,n){Object.defineProperty(o.Event.prototype,e,{enumerable:!0,configurable:!0,get:B(n)?function(){if(this.originalEvent)return n(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(i){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:i})}})},fix:function(e){return e[o.expando]?e:new o.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var n=this||e;return hn.test(n.type)&&n.click&&he(n,"input")&&li(n,"click",!0),!1},trigger:function(e){var n=this||e;return hn.test(n.type)&&n.click&&he(n,"input")&&li(n,"click"),!0},_default:function(e){var n=e.target;return hn.test(n.type)&&n.click&&he(n,"input")&&I.get(n,"click")||he(n,"a")}},beforeunload:{postDispatch:function(e){e.result!==void 0&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},o.removeEvent=function(e,n,i){e.removeEventListener&&e.removeEventListener(n,i)},o.Event=function(e,n){if(!(this instanceof o.Event))return new o.Event(e,n);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||e.defaultPrevented===void 0&&e.returnValue===!1?mn:vn,this.target=e.target&&e.target.nodeType===3?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,n&&o.extend(this,n),this.timeStamp=e&&e.timeStamp||Date.now(),this[o.expando]=!0},o.Event.prototype={constructor:o.Event,isDefaultPrevented:vn,isPropagationStopped:vn,isImmediatePropagationStopped:vn,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=mn,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=mn,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=mn,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},o.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},o.event.addProp),o.each({focus:"focusin",blur:"focusout"},function(e,n){function i(r){if(q.documentMode){var a=I.get(this,"handle"),c=o.event.fix(r);c.type=r.type==="focusin"?"focus":"blur",c.isSimulated=!0,a(r),c.target===c.currentTarget&&a(c)}else o.event.simulate(n,r.target,o.event.fix(r))}o.event.special[e]={setup:function(){var r;if(li(this,e,!0),!q.documentMode)return!1;(r=I.get(this,n))||this.addEventListener(n,i),I.set(this,n,(r||0)+1)},trigger:function(){return li(this,e),!0},teardown:function(){var r;if(!q.documentMode)return!1;(r=I.get(this,n)-1)?I.set(this,n,r):(this.removeEventListener(n,i),I.remove(this,n))},_default:function(r){return I.get(r.target,e)},delegateType:n},o.event.special[n]={setup:function(){var r=this.ownerDocument||this.document||this,a=q.documentMode?this:r,c=I.get(a,n);c||(q.documentMode?this.addEventListener(n,i):r.addEventListener(e,i,!0)),I.set(a,n,(c||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,a=q.documentMode?this:r,c=I.get(a,n)-1;c?I.set(a,n,c):(q.documentMode?this.removeEventListener(n,i):r.removeEventListener(e,i,!0),I.remove(a,n))}}}),o.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,n){o.event.special[e]={delegateType:n,bindType:n,handle:function(i){var r,a=i.relatedTarget,c=i.handleObj;return a&&(a===this||o.contains(this,a))||(i.type=c.origType,r=c.handler.apply(this,arguments),i.type=n),r}}}),o.fn.extend({on:function(e,n,i,r){return Li(this,e,n,i,r)},one:function(e,n,i,r){return Li(this,e,n,i,r,1)},off:function(e,n,i){var r,a;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,o(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if(typeof e=="object"){for(a in e)this.off(a,n,e[a]);return this}return n!==!1&&typeof n!="function"||(i=n,n=void 0),i===!1&&(i=vn),this.each(function(){o.event.remove(this,e,i,n)})}});var ji=/<script|<style|<link/i,yn=/checked\s*(?:[^=]|=\s*.checked.)/i,ci=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function hr(e,n){return he(e,"table")&&he(n.nodeType!==11?n:n.firstChild,"tr")&&o(e).children("tbody")[0]||e}function Ni(e){return e.type=(e.getAttribute("type")!==null)+"/"+e.type,e}function $r(e){return(e.type||"").slice(0,5)==="true/"?e.type=e.type.slice(5):e.removeAttribute("type"),e}function pr(e,n){var i,r,a,c,u,p;if(n.nodeType===1){if(I.hasData(e)&&(p=I.get(e).events))for(a in I.remove(n,"handle events"),p)for(i=0,r=p[a].length;i<r;i++)o.event.add(n,a,p[a][i]);xe.hasData(e)&&(c=xe.access(e),u=o.extend({},c),xe.set(n,u))}}function dt(e,n,i,r){n=yt(n);var a,c,u,p,h,v,w=0,E=e.length,_=E-1,A=n[0],H=B(A);if(H||1<E&&typeof A=="string"&&!K.checkClone&&yn.test(A))return e.each(function(z){var X=e.eq(z);H&&(n[0]=A.call(this,z,X.html())),dt(X,n,i,r)});if(E&&(c=(a=gn(n,e[0].ownerDocument,!1,e,r)).firstChild,a.childNodes.length===1&&(a=c),c||r)){for(p=(u=o.map(Ye(a,"script"),Ni)).length;w<E;w++)h=a,w!==_&&(h=o.clone(h,!0,!0),p&&o.merge(u,Ye(h,"script"))),i.call(e[w],h,w);if(p)for(v=u[u.length-1].ownerDocument,o.map(u,$r),w=0;w<p;w++)h=u[w],dr.test(h.type||"")&&!I.access(h,"globalEval")&&o.contains(v,h)&&(h.src&&(h.type||"").toLowerCase()!=="module"?o._evalUrl&&!h.noModule&&o._evalUrl(h.src,{nonce:h.nonce||h.getAttribute("nonce")},v):ti(h.textContent.replace(ci,""),h,v))}return e}function $e(e,n,i){for(var r,a=n?o.filter(n,e):e,c=0;(r=a[c])!=null;c++)i||r.nodeType!==1||o.cleanData(Ye(r)),r.parentNode&&(i&&dn(r)&&Di(Ye(r,"script")),r.parentNode.removeChild(r));return e}o.extend({htmlPrefilter:function(e){return e},clone:function(e,n,i){var r,a,c,u,p,h,v,w=e.cloneNode(!0),E=dn(e);if(!(K.noCloneChecked||e.nodeType!==1&&e.nodeType!==11||o.isXMLDoc(e)))for(u=Ye(w),r=0,a=(c=Ye(e)).length;r<a;r++)p=c[r],h=u[r],(v=h.nodeName.toLowerCase())==="input"&&hn.test(p.type)?h.checked=p.checked:v!=="input"&&v!=="textarea"||(h.defaultValue=p.defaultValue);if(n)if(i)for(c=c||Ye(e),u=u||Ye(w),r=0,a=c.length;r<a;r++)pr(c[r],u[r]);else pr(e,w);return 0<(u=Ye(w,"script")).length&&Di(u,!E&&Ye(e,"script")),w},cleanData:function(e){for(var n,i,r,a=o.event.special,c=0;(i=e[c])!==void 0;c++)if(Ln(i)){if(n=i[I.expando]){if(n.events)for(r in n.events)a[r]?o.event.remove(i,r):o.removeEvent(i,r,n.handle);i[I.expando]=void 0}i[xe.expando]&&(i[xe.expando]=void 0)}}}),o.fn.extend({detach:function(e){return $e(this,e,!0)},remove:function(e){return $e(this,e)},text:function(e){return St(this,function(n){return n===void 0?o.text(this):this.empty().each(function(){this.nodeType!==1&&this.nodeType!==11&&this.nodeType!==9||(this.textContent=n)})},null,e,arguments.length)},append:function(){return dt(this,arguments,function(e){this.nodeType!==1&&this.nodeType!==11&&this.nodeType!==9||hr(this,e).appendChild(e)})},prepend:function(){return dt(this,arguments,function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var n=hr(this,e);n.insertBefore(e,n.firstChild)}})},before:function(){return dt(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return dt(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,n=0;(e=this[n])!=null;n++)e.nodeType===1&&(o.cleanData(Ye(e,!1)),e.textContent="");return this},clone:function(e,n){return e=e!=null&&e,n=n??e,this.map(function(){return o.clone(this,e,n)})},html:function(e){return St(this,function(n){var i=this[0]||{},r=0,a=this.length;if(n===void 0&&i.nodeType===1)return i.innerHTML;if(typeof n=="string"&&!ji.test(n)&&!tt[(Oi.exec(n)||["",""])[1].toLowerCase()]){n=o.htmlPrefilter(n);try{for(;r<a;r++)(i=this[r]||{}).nodeType===1&&(o.cleanData(Ye(i,!1)),i.innerHTML=n);i=0}catch{}}i&&this.empty().append(n)},null,e,arguments.length)},replaceWith:function(){var e=[];return dt(this,arguments,function(n){var i=this.parentNode;o.inArray(this,e)<0&&(o.cleanData(Ye(this)),i&&i.replaceChild(n,this))},e)}}),o.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,n){o.fn[e]=function(i){for(var r,a=[],c=o(i),u=c.length-1,p=0;p<=u;p++)r=p===u?this:this.clone(!0),o(c[p])[n](r),st.apply(a,r.get());return this.pushStack(a)}});var qe=new RegExp("^("+Xt+")(?!px)[a-z%]+$","i"),Fe=/^--/,je=function(e){var n=e.ownerDocument.defaultView;return n&&n.opener||(n=F),n.getComputedStyle(e)},Pn=function(e,n,i){var r,a,c={};for(a in n)c[a]=e.style[a],e.style[a]=n[a];for(a in r=i.call(e),n)e.style[a]=c[a];return r},bn=new RegExp(Ot.join("|"),"i");function ft(e,n,i){var r,a,c,u,p=Fe.test(n),h=e.style;return(i=i||je(e))&&(u=i.getPropertyValue(n)||i[n],p&&u&&(u=u.replace(an,"$1")||void 0),u!==""||dn(e)||(u=o.style(e,n)),!K.pixelBoxStyles()&&qe.test(u)&&bn.test(n)&&(r=h.width,a=h.minWidth,c=h.maxWidth,h.minWidth=h.maxWidth=h.width=u,u=i.width,h.width=r,h.minWidth=a,h.maxWidth=c)),u!==void 0?u+"":u}function Yt(e,n){return{get:function(){if(!e())return(this.get=n).apply(this,arguments);delete this.get}}}(function(){function e(){if(v){h.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",v.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",Dt.appendChild(h).appendChild(v);var w=F.getComputedStyle(v);i=w.top!=="1%",p=n(w.marginLeft)===12,v.style.right="60%",c=n(w.right)===36,r=n(w.width)===36,v.style.position="absolute",a=n(v.offsetWidth/3)===12,Dt.removeChild(h),v=null}}function n(w){return Math.round(parseFloat(w))}var i,r,a,c,u,p,h=q.createElement("div"),v=q.createElement("div");v.style&&(v.style.backgroundClip="content-box",v.cloneNode(!0).style.backgroundClip="",K.clearCloneStyle=v.style.backgroundClip==="content-box",o.extend(K,{boxSizingReliable:function(){return e(),r},pixelBoxStyles:function(){return e(),c},pixelPosition:function(){return e(),i},reliableMarginLeft:function(){return e(),p},scrollboxSize:function(){return e(),a},reliableTrDimensions:function(){var w,E,_,A;return u==null&&(w=q.createElement("table"),E=q.createElement("tr"),_=q.createElement("div"),w.style.cssText="position:absolute;left:-11111px;border-collapse:separate",E.style.cssText="box-sizing:content-box;border:1px solid",E.style.height="1px",_.style.height="9px",_.style.display="block",Dt.appendChild(w).appendChild(E).appendChild(_),A=F.getComputedStyle(E),u=parseInt(A.height,10)+parseInt(A.borderTopWidth,10)+parseInt(A.borderBottomWidth,10)===E.offsetHeight,Dt.removeChild(w)),u}}))})();var Pi=["Webkit","Moz","ms"],ui=q.createElement("div").style,Qt={};function di(e){var n=o.cssProps[e]||Qt[e];return n||(e in ui?e:Qt[e]=function(i){for(var r=i[0].toUpperCase()+i.slice(1),a=Pi.length;a--;)if((i=Pi[a]+r)in ui)return i}(e)||e)}var Mi=/^(none|table(?!-c[ea]).+)/,Ii={position:"absolute",visibility:"hidden",display:"block"},$i={letterSpacing:"0",fontWeight:"400"};function Hi(e,n,i){var r=It.exec(n);return r?Math.max(0,r[2]-(i||0))+(r[3]||"px"):n}function fi(e,n,i,r,a,c){var u=n==="width"?1:0,p=0,h=0,v=0;if(i===(r?"border":"content"))return 0;for(;u<4;u+=2)i==="margin"&&(v+=o.css(e,i+Ot[u],!0,a)),r?(i==="content"&&(h-=o.css(e,"padding"+Ot[u],!0,a)),i!=="margin"&&(h-=o.css(e,"border"+Ot[u]+"Width",!0,a))):(h+=o.css(e,"padding"+Ot[u],!0,a),i!=="padding"?h+=o.css(e,"border"+Ot[u]+"Width",!0,a):p+=o.css(e,"border"+Ot[u]+"Width",!0,a));return!r&&0<=c&&(h+=Math.max(0,Math.ceil(e["offset"+n[0].toUpperCase()+n.slice(1)]-c-h-p-.5))||0),h+v}function qi(e,n,i){var r=je(e),a=(!K.boxSizingReliable()||i)&&o.css(e,"boxSizing",!1,r)==="border-box",c=a,u=ft(e,n,r),p="offset"+n[0].toUpperCase()+n.slice(1);if(qe.test(u)){if(!i)return u;u="auto"}return(!K.boxSizingReliable()&&a||!K.reliableTrDimensions()&&he(e,"tr")||u==="auto"||!parseFloat(u)&&o.css(e,"display",!1,r)==="inline")&&e.getClientRects().length&&(a=o.css(e,"boxSizing",!1,r)==="border-box",(c=p in e)&&(u=e[p])),(u=parseFloat(u)||0)+fi(e,n,i||(a?"border":"content"),c,r,u)+"px"}function nt(e,n,i,r,a){return new nt.prototype.init(e,n,i,r,a)}o.extend({cssHooks:{opacity:{get:function(e,n){if(n){var i=ft(e,"opacity");return i===""?"1":i}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,n,i,r){if(e&&e.nodeType!==3&&e.nodeType!==8&&e.style){var a,c,u,p=bt(n),h=Fe.test(n),v=e.style;if(h||(n=di(p)),u=o.cssHooks[n]||o.cssHooks[p],i===void 0)return u&&"get"in u&&(a=u.get(e,!1,r))!==void 0?a:v[n];(c=typeof i)=="string"&&(a=It.exec(i))&&a[1]&&(i=cr(e,n,a),c="number"),i!=null&&i==i&&(c!=="number"||h||(i+=a&&a[3]||(o.cssNumber[p]?"":"px")),K.clearCloneStyle||i!==""||n.indexOf("background")!==0||(v[n]="inherit"),u&&"set"in u&&(i=u.set(e,i,r))===void 0||(h?v.setProperty(n,i):v[n]=i))}},css:function(e,n,i,r){var a,c,u,p=bt(n);return Fe.test(n)||(n=di(p)),(u=o.cssHooks[n]||o.cssHooks[p])&&"get"in u&&(a=u.get(e,!0,i)),a===void 0&&(a=ft(e,n,r)),a==="normal"&&n in $i&&(a=$i[n]),i===""||i?(c=parseFloat(a),i===!0||isFinite(c)?c||0:a):a}}),o.each(["height","width"],function(e,n){o.cssHooks[n]={get:function(i,r,a){if(r)return!Mi.test(o.css(i,"display"))||i.getClientRects().length&&i.getBoundingClientRect().width?qi(i,n,a):Pn(i,Ii,function(){return qi(i,n,a)})},set:function(i,r,a){var c,u=je(i),p=!K.scrollboxSize()&&u.position==="absolute",h=(p||a)&&o.css(i,"boxSizing",!1,u)==="border-box",v=a?fi(i,n,a,h,u):0;return h&&p&&(v-=Math.ceil(i["offset"+n[0].toUpperCase()+n.slice(1)]-parseFloat(u[n])-fi(i,n,"border",!1,u)-.5)),v&&(c=It.exec(r))&&(c[3]||"px")!=="px"&&(i.style[n]=r,r=o.css(i,n)),Hi(0,r,v)}}}),o.cssHooks.marginLeft=Yt(K.reliableMarginLeft,function(e,n){if(n)return(parseFloat(ft(e,"marginLeft"))||e.getBoundingClientRect().left-Pn(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),o.each({margin:"",padding:"",border:"Width"},function(e,n){o.cssHooks[e+n]={expand:function(i){for(var r=0,a={},c=typeof i=="string"?i.split(" "):[i];r<4;r++)a[e+Ot[r]+n]=c[r]||c[r-2]||c[0];return a}},e!=="margin"&&(o.cssHooks[e+n].set=Hi)}),o.fn.extend({css:function(e,n){return St(this,function(i,r,a){var c,u,p={},h=0;if(Array.isArray(r)){for(c=je(i),u=r.length;h<u;h++)p[r[h]]=o.css(i,r[h],!1,c);return p}return a!==void 0?o.style(i,r,a):o.css(i,r)},e,n,1<arguments.length)}}),((o.Tween=nt).prototype={constructor:nt,init:function(e,n,i,r,a,c){this.elem=e,this.prop=i,this.easing=a||o.easing._default,this.options=n,this.start=this.now=this.cur(),this.end=r,this.unit=c||(o.cssNumber[i]?"":"px")},cur:function(){var e=nt.propHooks[this.prop];return e&&e.get?e.get(this):nt.propHooks._default.get(this)},run:function(e){var n,i=nt.propHooks[this.prop];return this.options.duration?this.pos=n=o.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=n=e,this.now=(this.end-this.start)*n+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):nt.propHooks._default.set(this),this}}).init.prototype=nt.prototype,(nt.propHooks={_default:{get:function(e){var n;return e.elem.nodeType!==1||e.elem[e.prop]!=null&&e.elem.style[e.prop]==null?e.elem[e.prop]:(n=o.css(e.elem,e.prop,""))&&n!=="auto"?n:0},set:function(e){o.fx.step[e.prop]?o.fx.step[e.prop](e):e.elem.nodeType!==1||!o.cssHooks[e.prop]&&e.elem.style[di(e.prop)]==null?e.elem[e.prop]=e.now:o.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=nt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},o.easing={linear:function(e){return e},swing:function(e){return .5-Math.cos(e*Math.PI)/2},_default:"swing"},o.fx=nt.prototype.init,o.fx.step={};var Kt,Mn,Gt,Fi,gr=/^(?:toggle|show|hide)$/,_t=/queueHooks$/;function Re(){Mn&&(q.hidden===!1&&F.requestAnimationFrame?F.requestAnimationFrame(Re):F.setTimeout(Re,o.fx.interval),o.fx.tick())}function Ht(){return F.setTimeout(function(){Kt=void 0}),Kt=Date.now()}function We(e,n){var i,r=0,a={height:e};for(n=n?1:0;r<4;r+=2-n)a["margin"+(i=Ot[r])]=a["padding"+i]=e;return n&&(a.opacity=a.width=e),a}function hi(e,n,i){for(var r,a=(Je.tweeners[n]||[]).concat(Je.tweeners["*"]),c=0,u=a.length;c<u;c++)if(r=a[c].call(i,n,e))return r}function Je(e,n,i){var r,a,c=0,u=Je.prefilters.length,p=o.Deferred().always(function(){delete h.elem}),h=function(){if(a)return!1;for(var E=Kt||Ht(),_=Math.max(0,v.startTime+v.duration-E),A=1-(_/v.duration||0),H=0,z=v.tweens.length;H<z;H++)v.tweens[H].run(A);return p.notifyWith(e,[v,A,_]),A<1&&z?_:(z||p.notifyWith(e,[v,1,0]),p.resolveWith(e,[v]),!1)},v=p.promise({elem:e,props:o.extend({},n),opts:o.extend(!0,{specialEasing:{},easing:o.easing._default},i),originalProperties:n,originalOptions:i,startTime:Kt||Ht(),duration:i.duration,tweens:[],createTween:function(E,_){var A=o.Tween(e,v.opts,E,_,v.opts.specialEasing[E]||v.opts.easing);return v.tweens.push(A),A},stop:function(E){var _=0,A=E?v.tweens.length:0;if(a)return this;for(a=!0;_<A;_++)v.tweens[_].run(1);return E?(p.notifyWith(e,[v,1,0]),p.resolveWith(e,[v,E])):p.rejectWith(e,[v,E]),this}}),w=v.props;for(!function(E,_){var A,H,z,X,_e;for(A in E)if(z=_[H=bt(A)],X=E[A],Array.isArray(X)&&(z=X[1],X=E[A]=X[0]),A!==H&&(E[H]=X,delete E[A]),(_e=o.cssHooks[H])&&"expand"in _e)for(A in X=_e.expand(X),delete E[H],X)A in E||(E[A]=X[A],_[A]=z);else _[H]=z}(w,v.opts.specialEasing);c<u;c++)if(r=Je.prefilters[c].call(v,e,w,v.opts))return B(r.stop)&&(o._queueHooks(v.elem,v.opts.queue).stop=r.stop.bind(r)),r;return o.map(w,hi,v),B(v.opts.start)&&v.opts.start.call(e,v),v.progress(v.opts.progress).done(v.opts.done,v.opts.complete).fail(v.opts.fail).always(v.opts.always),o.fx.timer(o.extend(h,{elem:e,anim:v,queue:v.opts.queue})),v}o.Animation=o.extend(Je,{tweeners:{"*":[function(e,n){var i=this.createTween(e,n);return cr(i.elem,e,It.exec(n),i),i}]},tweener:function(e,n){B(e)?(n=e,e=["*"]):e=e.match(ut);for(var i,r=0,a=e.length;r<a;r++)i=e[r],Je.tweeners[i]=Je.tweeners[i]||[],Je.tweeners[i].unshift(n)},prefilters:[function(e,n,i){var r,a,c,u,p,h,v,w,E="width"in n||"height"in n,_=this,A={},H=e.style,z=e.nodeType&&ai(e),X=I.get(e,"fxshow");for(r in i.queue||((u=o._queueHooks(e,"fx")).unqueued==null&&(u.unqueued=0,p=u.empty.fire,u.empty.fire=function(){u.unqueued||p()}),u.unqueued++,_.always(function(){_.always(function(){u.unqueued--,o.queue(e,"fx").length||u.empty.fire()})})),n)if(a=n[r],gr.test(a)){if(delete n[r],c=c||a==="toggle",a===(z?"hide":"show")){if(a!=="show"||!X||X[r]===void 0)continue;z=!0}A[r]=X&&X[r]||o.style(e,r)}if((h=!o.isEmptyObject(n))||!o.isEmptyObject(A))for(r in E&&e.nodeType===1&&(i.overflow=[H.overflow,H.overflowX,H.overflowY],(v=X&&X.display)==null&&(v=I.get(e,"display")),(w=o.css(e,"display"))==="none"&&(v?w=v:(fn([e],!0),v=e.style.display||v,w=o.css(e,"display"),fn([e]))),(w==="inline"||w==="inline-block"&&v!=null)&&o.css(e,"float")==="none"&&(h||(_.done(function(){H.display=v}),v==null&&(w=H.display,v=w==="none"?"":w)),H.display="inline-block")),i.overflow&&(H.overflow="hidden",_.always(function(){H.overflow=i.overflow[0],H.overflowX=i.overflow[1],H.overflowY=i.overflow[2]})),h=!1,A)h||(X?"hidden"in X&&(z=X.hidden):X=I.access(e,"fxshow",{display:v}),c&&(X.hidden=!z),z&&fn([e],!0),_.done(function(){for(r in z||fn([e]),I.remove(e,"fxshow"),A)o.style(e,r,A[r])})),h=hi(z?X[r]:0,r,_),r in X||(X[r]=h.start,z&&(h.end=h.start,h.start=0))}],prefilter:function(e,n){n?Je.prefilters.unshift(e):Je.prefilters.push(e)}}),o.speed=function(e,n,i){var r=e&&typeof e=="object"?o.extend({},e):{complete:i||!i&&n||B(e)&&e,duration:e,easing:i&&n||n&&!B(n)&&n};return o.fx.off?r.duration=0:typeof r.duration!="number"&&(r.duration in o.fx.speeds?r.duration=o.fx.speeds[r.duration]:r.duration=o.fx.speeds._default),r.queue!=null&&r.queue!==!0||(r.queue="fx"),r.old=r.complete,r.complete=function(){B(r.old)&&r.old.call(this),r.queue&&o.dequeue(this,r.queue)},r},o.fn.extend({fadeTo:function(e,n,i,r){return this.filter(ai).css("opacity",0).show().end().animate({opacity:n},e,i,r)},animate:function(e,n,i,r){var a=o.isEmptyObject(e),c=o.speed(n,i,r),u=function(){var p=Je(this,o.extend({},e),c);(a||I.get(this,"finish"))&&p.stop(!0)};return u.finish=u,a||c.queue===!1?this.each(u):this.queue(c.queue,u)},stop:function(e,n,i){var r=function(a){var c=a.stop;delete a.stop,c(i)};return typeof e!="string"&&(i=n,n=e,e=void 0),n&&this.queue(e||"fx",[]),this.each(function(){var a=!0,c=e!=null&&e+"queueHooks",u=o.timers,p=I.get(this);if(c)p[c]&&p[c].stop&&r(p[c]);else for(c in p)p[c]&&p[c].stop&&_t.test(c)&&r(p[c]);for(c=u.length;c--;)u[c].elem!==this||e!=null&&u[c].queue!==e||(u[c].anim.stop(i),a=!1,u.splice(c,1));!a&&i||o.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var n,i=I.get(this),r=i[e+"queue"],a=i[e+"queueHooks"],c=o.timers,u=r?r.length:0;for(i.finish=!0,o.queue(this,e,[]),a&&a.stop&&a.stop.call(this,!0),n=c.length;n--;)c[n].elem===this&&c[n].queue===e&&(c[n].anim.stop(!0),c.splice(n,1));for(n=0;n<u;n++)r[n]&&r[n].finish&&r[n].finish.call(this);delete i.finish})}}),o.each(["toggle","show","hide"],function(e,n){var i=o.fn[n];o.fn[n]=function(r,a,c){return r==null||typeof r=="boolean"?i.apply(this,arguments):this.animate(We(n,!0),r,a,c)}}),o.each({slideDown:We("show"),slideUp:We("hide"),slideToggle:We("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,n){o.fn[e]=function(i,r,a){return this.animate(n,i,r,a)}}),o.timers=[],o.fx.tick=function(){var e,n=0,i=o.timers;for(Kt=Date.now();n<i.length;n++)(e=i[n])()||i[n]!==e||i.splice(n--,1);i.length||o.fx.stop(),Kt=void 0},o.fx.timer=function(e){o.timers.push(e),o.fx.start()},o.fx.interval=13,o.fx.start=function(){Mn||(Mn=!0,Re())},o.fx.stop=function(){Mn=null},o.fx.speeds={slow:600,fast:200,_default:400},o.fn.delay=function(e,n){return e=o.fx&&o.fx.speeds[e]||e,n=n||"fx",this.queue(n,function(i,r){var a=F.setTimeout(i,e);r.stop=function(){F.clearTimeout(a)}})},Gt=q.createElement("input"),Fi=q.createElement("select").appendChild(q.createElement("option")),Gt.type="checkbox",K.checkOn=Gt.value!=="",K.optSelected=Fi.selected,(Gt=q.createElement("input")).value="t",Gt.type="radio",K.radioValue=Gt.value==="t";var ht,pt=o.expr.attrHandle;o.fn.extend({attr:function(e,n){return St(this,o.attr,e,n,1<arguments.length)},removeAttr:function(e){return this.each(function(){o.removeAttr(this,e)})}}),o.extend({attr:function(e,n,i){var r,a,c=e.nodeType;if(c!==3&&c!==8&&c!==2)return typeof e.getAttribute>"u"?o.prop(e,n,i):(c===1&&o.isXMLDoc(e)||(a=o.attrHooks[n.toLowerCase()]||(o.expr.match.bool.test(n)?ht:void 0)),i!==void 0?i===null?void o.removeAttr(e,n):a&&"set"in a&&(r=a.set(e,i,n))!==void 0?r:(e.setAttribute(n,i+""),i):a&&"get"in a&&(r=a.get(e,n))!==null?r:(r=o.find.attr(e,n))==null?void 0:r)},attrHooks:{type:{set:function(e,n){if(!K.radioValue&&n==="radio"&&he(e,"input")){var i=e.value;return e.setAttribute("type",n),i&&(e.value=i),n}}}},removeAttr:function(e,n){var i,r=0,a=n&&n.match(ut);if(a&&e.nodeType===1)for(;i=a[r++];)e.removeAttribute(i)}}),ht={set:function(e,n,i){return n===!1?o.removeAttr(e,i):e.setAttribute(i,i),i}},o.each(o.expr.match.bool.source.match(/\w+/g),function(e,n){var i=pt[n]||o.find.attr;pt[n]=function(r,a,c){var u,p,h=a.toLowerCase();return c||(p=pt[h],pt[h]=u,u=i(r,a,c)!=null?h:null,pt[h]=p),u}});var pi=/^(?:input|select|textarea|button)$/i,_n=/^(?:a|area)$/i;function Lt(e){return(e.match(ut)||[]).join(" ")}function qt(e){return e.getAttribute&&e.getAttribute("class")||""}function Ft(e){return Array.isArray(e)?e:typeof e=="string"&&e.match(ut)||[]}o.fn.extend({prop:function(e,n){return St(this,o.prop,e,n,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[o.propFix[e]||e]})}}),o.extend({prop:function(e,n,i){var r,a,c=e.nodeType;if(c!==3&&c!==8&&c!==2)return c===1&&o.isXMLDoc(e)||(n=o.propFix[n]||n,a=o.propHooks[n]),i!==void 0?a&&"set"in a&&(r=a.set(e,i,n))!==void 0?r:e[n]=i:a&&"get"in a&&(r=a.get(e,n))!==null?r:e[n]},propHooks:{tabIndex:{get:function(e){var n=o.find.attr(e,"tabindex");return n?parseInt(n,10):pi.test(e.nodeName)||_n.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),K.optSelected||(o.propHooks.selected={get:function(e){var n=e.parentNode;return n&&n.parentNode&&n.parentNode.selectedIndex,null},set:function(e){var n=e.parentNode;n&&(n.selectedIndex,n.parentNode&&n.parentNode.selectedIndex)}}),o.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){o.propFix[this.toLowerCase()]=this}),o.fn.extend({addClass:function(e){var n,i,r,a,c,u;return B(e)?this.each(function(p){o(this).addClass(e.call(this,p,qt(this)))}):(n=Ft(e)).length?this.each(function(){if(r=qt(this),i=this.nodeType===1&&" "+Lt(r)+" "){for(c=0;c<n.length;c++)a=n[c],i.indexOf(" "+a+" ")<0&&(i+=a+" ");u=Lt(i),r!==u&&this.setAttribute("class",u)}}):this},removeClass:function(e){var n,i,r,a,c,u;return B(e)?this.each(function(p){o(this).removeClass(e.call(this,p,qt(this)))}):arguments.length?(n=Ft(e)).length?this.each(function(){if(r=qt(this),i=this.nodeType===1&&" "+Lt(r)+" "){for(c=0;c<n.length;c++)for(a=n[c];-1<i.indexOf(" "+a+" ");)i=i.replace(" "+a+" "," ");u=Lt(i),r!==u&&this.setAttribute("class",u)}}):this:this.attr("class","")},toggleClass:function(e,n){var i,r,a,c,u=typeof e,p=u==="string"||Array.isArray(e);return B(e)?this.each(function(h){o(this).toggleClass(e.call(this,h,qt(this),n),n)}):typeof n=="boolean"&&p?n?this.addClass(e):this.removeClass(e):(i=Ft(e),this.each(function(){if(p)for(c=o(this),a=0;a<i.length;a++)r=i[a],c.hasClass(r)?c.removeClass(r):c.addClass(r);else e!==void 0&&u!=="boolean"||((r=qt(this))&&I.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||e===!1?"":I.get(this,"__className__")||""))}))},hasClass:function(e){var n,i,r=0;for(n=" "+e+" ";i=this[r++];)if(i.nodeType===1&&-1<(" "+Lt(qt(i))+" ").indexOf(n))return!0;return!1}});var Ri=/\r/g;o.fn.extend({val:function(e){var n,i,r,a=this[0];return arguments.length?(r=B(e),this.each(function(c){var u;this.nodeType===1&&((u=r?e.call(this,c,o(this).val()):e)==null?u="":typeof u=="number"?u+="":Array.isArray(u)&&(u=o.map(u,function(p){return p==null?"":p+""})),(n=o.valHooks[this.type]||o.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&n.set(this,u,"value")!==void 0||(this.value=u))})):a?(n=o.valHooks[a.type]||o.valHooks[a.nodeName.toLowerCase()])&&"get"in n&&(i=n.get(a,"value"))!==void 0?i:typeof(i=a.value)=="string"?i.replace(Ri,""):i??"":void 0}}),o.extend({valHooks:{option:{get:function(e){var n=o.find.attr(e,"value");return n??Lt(o.text(e))}},select:{get:function(e){var n,i,r,a=e.options,c=e.selectedIndex,u=e.type==="select-one",p=u?null:[],h=u?c+1:a.length;for(r=c<0?h:u?c:0;r<h;r++)if(((i=a[r]).selected||r===c)&&!i.disabled&&(!i.parentNode.disabled||!he(i.parentNode,"optgroup"))){if(n=o(i).val(),u)return n;p.push(n)}return p},set:function(e,n){for(var i,r,a=e.options,c=o.makeArray(n),u=a.length;u--;)((r=a[u]).selected=-1<o.inArray(o.valHooks.option.get(r),c))&&(i=!0);return i||(e.selectedIndex=-1),c}}}}),o.each(["radio","checkbox"],function(){o.valHooks[this]={set:function(e,n){if(Array.isArray(n))return e.checked=-1<o.inArray(o(e).val(),n)}},K.checkOn||(o.valHooks[this].get=function(e){return e.getAttribute("value")===null?"on":e.value})});var wn=F.location,wt={guid:Date.now()},Wi=/\?/;o.parseXML=function(e){var n,i;if(!e||typeof e!="string")return null;try{n=new F.DOMParser().parseFromString(e,"text/xml")}catch{}return i=n&&n.getElementsByTagName("parsererror")[0],n&&!i||o.error("Invalid XML: "+(i?o.map(i.childNodes,function(r){return r.textContent}).join(`
`):e)),n};var jt=/^(?:focusinfocus|focusoutblur)$/,In=function(e){e.stopPropagation()};o.extend(o.event,{trigger:function(e,n,i,r){var a,c,u,p,h,v,w,E,_=[i||q],A=Mt.call(e,"type")?e.type:e,H=Mt.call(e,"namespace")?e.namespace.split("."):[];if(c=E=u=i=i||q,i.nodeType!==3&&i.nodeType!==8&&!jt.test(A+o.event.triggered)&&(-1<A.indexOf(".")&&(A=(H=A.split(".")).shift(),H.sort()),h=A.indexOf(":")<0&&"on"+A,(e=e[o.expando]?e:new o.Event(A,typeof e=="object"&&e)).isTrigger=r?2:3,e.namespace=H.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+H.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=i),n=n==null?[e]:o.makeArray(n,[e]),w=o.event.special[A]||{},r||!w.trigger||w.trigger.apply(i,n)!==!1)){if(!r&&!w.noBubble&&!Ie(i)){for(p=w.delegateType||A,jt.test(p+A)||(c=c.parentNode);c;c=c.parentNode)_.push(c),u=c;u===(i.ownerDocument||q)&&_.push(u.defaultView||u.parentWindow||F)}for(a=0;(c=_[a++])&&!e.isPropagationStopped();)E=c,e.type=1<a?p:w.bindType||A,(v=(I.get(c,"events")||Object.create(null))[e.type]&&I.get(c,"handle"))&&v.apply(c,n),(v=h&&c[h])&&v.apply&&Ln(c)&&(e.result=v.apply(c,n),e.result===!1&&e.preventDefault());return e.type=A,r||e.isDefaultPrevented()||w._default&&w._default.apply(_.pop(),n)!==!1||!Ln(i)||h&&B(i[A])&&!Ie(i)&&((u=i[h])&&(i[h]=null),o.event.triggered=A,e.isPropagationStopped()&&E.addEventListener(A,In),i[A](),e.isPropagationStopped()&&E.removeEventListener(A,In),o.event.triggered=void 0,u&&(i[h]=u)),e.result}},simulate:function(e,n,i){var r=o.extend(new o.Event,i,{type:e,isSimulated:!0});o.event.trigger(r,null,n)}}),o.fn.extend({trigger:function(e,n){return this.each(function(){o.event.trigger(e,n,this)})},triggerHandler:function(e,n){var i=this[0];if(i)return o.event.trigger(e,n,i,!0)}});var mr=/\[\]$/,xn=/\r?\n/g,Bi=/^(?:submit|button|image|reset|file)$/i,$n=/^(?:input|select|textarea|keygen)/i;function gi(e,n,i,r){var a;if(Array.isArray(n))o.each(n,function(c,u){i||mr.test(e)?r(e,u):gi(e+"["+(typeof u=="object"&&u!=null?c:"")+"]",u,i,r)});else if(i||sn(n)!=="object")r(e,n);else for(a in n)gi(e+"["+a+"]",n[a],i,r)}o.param=function(e,n){var i,r=[],a=function(c,u){var p=B(u)?u():u;r[r.length]=encodeURIComponent(c)+"="+encodeURIComponent(p??"")};if(e==null)return"";if(Array.isArray(e)||e.jquery&&!o.isPlainObject(e))o.each(e,function(){a(this.name,this.value)});else for(i in e)gi(i,e[i],n,a);return r.join("&")},o.fn.extend({serialize:function(){return o.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=o.prop(this,"elements");return e?o.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!o(this).is(":disabled")&&$n.test(this.nodeName)&&!Bi.test(e)&&(this.checked||!hn.test(e))}).map(function(e,n){var i=o(this).val();return i==null?null:Array.isArray(i)?o.map(i,function(r){return{name:n.name,value:r.replace(xn,`\r
`)}}):{name:n.name,value:i.replace(xn,`\r
`)}}).get()}});var vr=/%20/g,yr=/#.*$/,Tn=/([?&])_=[^&]*/,Hr=/^(.*?):[ \t]*([^\r\n]*)$/gm,br=/^(?:GET|HEAD)$/,zi=/^\/\//,Hn={},qn={},_r="*/".concat("*"),En=q.createElement("a");function wr(e){return function(n,i){typeof n!="string"&&(i=n,n="*");var r,a=0,c=n.toLowerCase().match(ut)||[];if(B(i))for(;r=c[a++];)r[0]==="+"?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(i)):(e[r]=e[r]||[]).push(i)}}function Vi(e,n,i,r){var a={},c=e===qn;function u(p){var h;return a[p]=!0,o.each(e[p]||[],function(v,w){var E=w(n,i,r);return typeof E!="string"||c||a[E]?c?!(h=E):void 0:(n.dataTypes.unshift(E),u(E),!1)}),h}return u(n.dataTypes[0])||!a["*"]&&u("*")}function Fn(e,n){var i,r,a=o.ajaxSettings.flatOptions||{};for(i in n)n[i]!==void 0&&((a[i]?e:r||(r={}))[i]=n[i]);return r&&o.extend(!0,e,r),e}En.href=wn.href,o.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:wn.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(wn.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":_r,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":o.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,n){return n?Fn(Fn(e,o.ajaxSettings),n):Fn(o.ajaxSettings,e)},ajaxPrefilter:wr(Hn),ajaxTransport:wr(qn),ajax:function(e,n){typeof e=="object"&&(n=e,e=void 0),n=n||{};var i,r,a,c,u,p,h,v,w,E,_=o.ajaxSetup({},n),A=_.context||_,H=_.context&&(A.nodeType||A.jquery)?o(A):o.event,z=o.Deferred(),X=o.Callbacks("once memory"),_e=_.statusCode||{},ye={},it={},xt="canceled",ne={readyState:0,getResponseHeader:function(se){var we;if(h){if(!c)for(c={};we=Hr.exec(a);)c[we[1].toLowerCase()+" "]=(c[we[1].toLowerCase()+" "]||[]).concat(we[2]);we=c[se.toLowerCase()+" "]}return we==null?null:we.join(", ")},getAllResponseHeaders:function(){return h?a:null},setRequestHeader:function(se,we){return h==null&&(se=it[se.toLowerCase()]=it[se.toLowerCase()]||se,ye[se]=we),this},overrideMimeType:function(se){return h==null&&(_.mimeType=se),this},statusCode:function(se){var we;if(se)if(h)ne.always(se[ne.status]);else for(we in se)_e[we]=[_e[we],se[we]];return this},abort:function(se){var we=se||xt;return i&&i.abort(we),Zt(0,we),this}};if(z.promise(ne),_.url=((e||_.url||wn.href)+"").replace(zi,wn.protocol+"//"),_.type=n.method||n.type||_.method||_.type,_.dataTypes=(_.dataType||"*").toLowerCase().match(ut)||[""],_.crossDomain==null){p=q.createElement("a");try{p.href=_.url,p.href=p.href,_.crossDomain=En.protocol+"//"+En.host!=p.protocol+"//"+p.host}catch{_.crossDomain=!0}}if(_.data&&_.processData&&typeof _.data!="string"&&(_.data=o.param(_.data,_.traditional)),Vi(Hn,_,n,ne),h)return ne;for(w in(v=o.event&&_.global)&&o.active++==0&&o.event.trigger("ajaxStart"),_.type=_.type.toUpperCase(),_.hasContent=!br.test(_.type),r=_.url.replace(yr,""),_.hasContent?_.data&&_.processData&&(_.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&(_.data=_.data.replace(vr,"+")):(E=_.url.slice(r.length),_.data&&(_.processData||typeof _.data=="string")&&(r+=(Wi.test(r)?"&":"?")+_.data,delete _.data),_.cache===!1&&(r=r.replace(Tn,"$1"),E=(Wi.test(r)?"&":"?")+"_="+wt.guid+++E),_.url=r+E),_.ifModified&&(o.lastModified[r]&&ne.setRequestHeader("If-Modified-Since",o.lastModified[r]),o.etag[r]&&ne.setRequestHeader("If-None-Match",o.etag[r])),(_.data&&_.hasContent&&_.contentType!==!1||n.contentType)&&ne.setRequestHeader("Content-Type",_.contentType),ne.setRequestHeader("Accept",_.dataTypes[0]&&_.accepts[_.dataTypes[0]]?_.accepts[_.dataTypes[0]]+(_.dataTypes[0]!=="*"?", "+_r+"; q=0.01":""):_.accepts["*"]),_.headers)ne.setRequestHeader(w,_.headers[w]);if(_.beforeSend&&(_.beforeSend.call(A,ne,_)===!1||h))return ne.abort();if(xt="abort",X.add(_.complete),ne.done(_.success),ne.fail(_.error),i=Vi(qn,_,n,ne)){if(ne.readyState=1,v&&H.trigger("ajaxSend",[ne,_]),h)return ne;_.async&&0<_.timeout&&(u=F.setTimeout(function(){ne.abort("timeout")},_.timeout));try{h=!1,i.send(ye,Zt)}catch(se){if(h)throw se;Zt(-1,se)}}else Zt(-1,"No Transport");function Zt(se,we,Wn,mi){var gt,Bn,Qe,Ke,kn,Be=we;h||(h=!0,u&&F.clearTimeout(u),i=void 0,a=mi||"",ne.readyState=0<se?4:0,gt=200<=se&&se<300||se===304,Wn&&(Ke=function(Ne,Ce,ze){for(var Rt,Ve,W,Te,Ee=Ne.contents,re=Ne.dataTypes;re[0]==="*";)re.shift(),Rt===void 0&&(Rt=Ne.mimeType||Ce.getResponseHeader("Content-Type"));if(Rt){for(Ve in Ee)if(Ee[Ve]&&Ee[Ve].test(Rt)){re.unshift(Ve);break}}if(re[0]in ze)W=re[0];else{for(Ve in ze){if(!re[0]||Ne.converters[Ve+" "+re[0]]){W=Ve;break}Te||(Te=Ve)}W=W||Te}if(W)return W!==re[0]&&re.unshift(W),ze[W]}(_,ne,Wn)),!gt&&-1<o.inArray("script",_.dataTypes)&&o.inArray("json",_.dataTypes)<0&&(_.converters["text script"]=function(){}),Ke=function(Ne,Ce,ze,Rt){var Ve,W,Te,Ee,re,mt={},en=Ne.dataTypes.slice();if(en[1])for(Te in Ne.converters)mt[Te.toLowerCase()]=Ne.converters[Te];for(W=en.shift();W;)if(Ne.responseFields[W]&&(ze[Ne.responseFields[W]]=Ce),!re&&Rt&&Ne.dataFilter&&(Ce=Ne.dataFilter(Ce,Ne.dataType)),re=W,W=en.shift()){if(W==="*")W=re;else if(re!=="*"&&re!==W){if(!(Te=mt[re+" "+W]||mt["* "+W])){for(Ve in mt)if((Ee=Ve.split(" "))[1]===W&&(Te=mt[re+" "+Ee[0]]||mt["* "+Ee[0]])){Te===!0?Te=mt[Ve]:mt[Ve]!==!0&&(W=Ee[0],en.unshift(Ee[1]));break}}if(Te!==!0)if(Te&&Ne.throws)Ce=Te(Ce);else try{Ce=Te(Ce)}catch(zn){return{state:"parsererror",error:Te?zn:"No conversion from "+re+" to "+W}}}}return{state:"success",data:Ce}}(_,Ke,ne,gt),gt?(_.ifModified&&((kn=ne.getResponseHeader("Last-Modified"))&&(o.lastModified[r]=kn),(kn=ne.getResponseHeader("etag"))&&(o.etag[r]=kn)),se===204||_.type==="HEAD"?Be="nocontent":se===304?Be="notmodified":(Be=Ke.state,Bn=Ke.data,gt=!(Qe=Ke.error))):(Qe=Be,!se&&Be||(Be="error",se<0&&(se=0))),ne.status=se,ne.statusText=(we||Be)+"",gt?z.resolveWith(A,[Bn,Be,ne]):z.rejectWith(A,[ne,Be,Qe]),ne.statusCode(_e),_e=void 0,v&&H.trigger(gt?"ajaxSuccess":"ajaxError",[ne,_,gt?Bn:Qe]),X.fireWith(A,[ne,Be]),v&&(H.trigger("ajaxComplete",[ne,_]),--o.active||o.event.trigger("ajaxStop")))}return ne},getJSON:function(e,n,i){return o.get(e,n,i,"json")},getScript:function(e,n){return o.get(e,void 0,n,"script")}}),o.each(["get","post"],function(e,n){o[n]=function(i,r,a,c){return B(r)&&(c=c||a,a=r,r=void 0),o.ajax(o.extend({url:i,type:n,dataType:c,data:r,success:a},o.isPlainObject(i)&&i))}}),o.ajaxPrefilter(function(e){var n;for(n in e.headers)n.toLowerCase()==="content-type"&&(e.contentType=e.headers[n]||"")}),o._evalUrl=function(e,n,i){return o.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(r){o.globalEval(r,n,i)}})},o.fn.extend({wrapAll:function(e){var n;return this[0]&&(B(e)&&(e=e.call(this[0])),n=o(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&n.insertBefore(this[0]),n.map(function(){for(var i=this;i.firstElementChild;)i=i.firstElementChild;return i}).append(this)),this},wrapInner:function(e){return B(e)?this.each(function(n){o(this).wrapInner(e.call(this,n))}):this.each(function(){var n=o(this),i=n.contents();i.length?i.wrapAll(e):n.append(e)})},wrap:function(e){var n=B(e);return this.each(function(i){o(this).wrapAll(n?e.call(this,i):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){o(this).replaceWith(this.childNodes)}),this}}),o.expr.pseudos.hidden=function(e){return!o.expr.pseudos.visible(e)},o.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},o.ajaxSettings.xhr=function(){try{return new F.XMLHttpRequest}catch{}};var Xi={0:200,1223:204},Jt=o.ajaxSettings.xhr();K.cors=!!Jt&&"withCredentials"in Jt,K.ajax=Jt=!!Jt,o.ajaxTransport(function(e){var n,i;if(K.cors||Jt&&!e.crossDomain)return{send:function(r,a){var c,u=e.xhr();if(u.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(c in e.xhrFields)u[c]=e.xhrFields[c];for(c in e.mimeType&&u.overrideMimeType&&u.overrideMimeType(e.mimeType),e.crossDomain||r["X-Requested-With"]||(r["X-Requested-With"]="XMLHttpRequest"),r)u.setRequestHeader(c,r[c]);n=function(p){return function(){n&&(n=i=u.onload=u.onerror=u.onabort=u.ontimeout=u.onreadystatechange=null,p==="abort"?u.abort():p==="error"?typeof u.status!="number"?a(0,"error"):a(u.status,u.statusText):a(Xi[u.status]||u.status,u.statusText,(u.responseType||"text")!=="text"||typeof u.responseText!="string"?{binary:u.response}:{text:u.responseText},u.getAllResponseHeaders()))}},u.onload=n(),i=u.onerror=u.ontimeout=n("error"),u.onabort!==void 0?u.onabort=i:u.onreadystatechange=function(){u.readyState===4&&F.setTimeout(function(){n&&i()})},n=n("abort");try{u.send(e.hasContent&&e.data||null)}catch(p){if(n)throw p}},abort:function(){n&&n()}}}),o.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),o.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return o.globalEval(e),e}}}),o.ajaxPrefilter("script",function(e){e.cache===void 0&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),o.ajaxTransport("script",function(e){var n,i;if(e.crossDomain||e.scriptAttrs)return{send:function(r,a){n=o("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",i=function(c){n.remove(),i=null,c&&a(c.type==="error"?404:200,c.type)}),q.head.appendChild(n[0])},abort:function(){i&&i()}}});var Ui,Cn=[],Rn=/(=)\?(?=&|$)|\?\?/;o.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Cn.pop()||o.expando+"_"+wt.guid++;return this[e]=!0,e}}),o.ajaxPrefilter("json jsonp",function(e,n,i){var r,a,c,u=e.jsonp!==!1&&(Rn.test(e.url)?"url":typeof e.data=="string"&&(e.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&Rn.test(e.data)&&"data");if(u||e.dataTypes[0]==="jsonp")return r=e.jsonpCallback=B(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,u?e[u]=e[u].replace(Rn,"$1"+r):e.jsonp!==!1&&(e.url+=(Wi.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return c||o.error(r+" was not called"),c[0]},e.dataTypes[0]="json",a=F[r],F[r]=function(){c=arguments},i.always(function(){a===void 0?o(F).removeProp(r):F[r]=a,e[r]&&(e.jsonpCallback=n.jsonpCallback,Cn.push(r)),c&&B(a)&&a(c[0]),c=a=void 0}),"script"}),K.createHTMLDocument=((Ui=q.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",Ui.childNodes.length===2),o.parseHTML=function(e,n,i){return typeof e!="string"?[]:(typeof n=="boolean"&&(i=n,n=!1),n||(K.createHTMLDocument?((r=(n=q.implementation.createHTMLDocument("")).createElement("base")).href=q.location.href,n.head.appendChild(r)):n=q),c=!i&&[],(a=oi.exec(e))?[n.createElement(a[1])]:(a=gn([e],n,c),c&&c.length&&o(c).remove(),o.merge([],a.childNodes)));var r,a,c},o.fn.load=function(e,n,i){var r,a,c,u=this,p=e.indexOf(" ");return-1<p&&(r=Lt(e.slice(p)),e=e.slice(0,p)),B(n)?(i=n,n=void 0):n&&typeof n=="object"&&(a="POST"),0<u.length&&o.ajax({url:e,type:a||"GET",dataType:"html",data:n}).done(function(h){c=arguments,u.html(r?o("<div>").append(o.parseHTML(h)).find(r):h)}).always(i&&function(h,v){u.each(function(){i.apply(this,c||[h.responseText,v,h])})}),this},o.expr.pseudos.animated=function(e){return o.grep(o.timers,function(n){return e===n.elem}).length},o.offset={setOffset:function(e,n,i){var r,a,c,u,p,h,v=o.css(e,"position"),w=o(e),E={};v==="static"&&(e.style.position="relative"),p=w.offset(),c=o.css(e,"top"),h=o.css(e,"left"),(v==="absolute"||v==="fixed")&&-1<(c+h).indexOf("auto")?(u=(r=w.position()).top,a=r.left):(u=parseFloat(c)||0,a=parseFloat(h)||0),B(n)&&(n=n.call(e,i,o.extend({},p))),n.top!=null&&(E.top=n.top-p.top+u),n.left!=null&&(E.left=n.left-p.left+a),"using"in n?n.using.call(e,E):w.css(E)}},o.fn.extend({offset:function(e){if(arguments.length)return e===void 0?this:this.each(function(a){o.offset.setOffset(this,e,a)});var n,i,r=this[0];return r?r.getClientRects().length?(n=r.getBoundingClientRect(),i=r.ownerDocument.defaultView,{top:n.top+i.pageYOffset,left:n.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,n,i,r=this[0],a={top:0,left:0};if(o.css(r,"position")==="fixed")n=r.getBoundingClientRect();else{for(n=this.offset(),i=r.ownerDocument,e=r.offsetParent||i.documentElement;e&&(e===i.body||e===i.documentElement)&&o.css(e,"position")==="static";)e=e.parentNode;e&&e!==r&&e.nodeType===1&&((a=o(e).offset()).top+=o.css(e,"borderTopWidth",!0),a.left+=o.css(e,"borderLeftWidth",!0))}return{top:n.top-a.top-o.css(r,"marginTop",!0),left:n.left-a.left-o.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&o.css(e,"position")==="static";)e=e.offsetParent;return e||Dt})}}),o.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,n){var i=n==="pageYOffset";o.fn[e]=function(r){return St(this,function(a,c,u){var p;if(Ie(a)?p=a:a.nodeType===9&&(p=a.defaultView),u===void 0)return p?p[n]:a[c];p?p.scrollTo(i?p.pageXOffset:u,i?u:p.pageYOffset):a[c]=u},e,r,arguments.length)}}),o.each(["top","left"],function(e,n){o.cssHooks[n]=Yt(K.pixelPosition,function(i,r){if(r)return r=ft(i,n),qe.test(r)?o(i).position()[n]+"px":r})}),o.each({Height:"height",Width:"width"},function(e,n){o.each({padding:"inner"+e,content:n,"":"outer"+e},function(i,r){o.fn[r]=function(a,c){var u=arguments.length&&(i||typeof a!="boolean"),p=i||(a===!0||c===!0?"margin":"border");return St(this,function(h,v,w){var E;return Ie(h)?r.indexOf("outer")===0?h["inner"+e]:h.document.documentElement["client"+e]:h.nodeType===9?(E=h.documentElement,Math.max(h.body["scroll"+e],E["scroll"+e],h.body["offset"+e],E["offset"+e],E["client"+e])):w===void 0?o.css(h,v,p):o.style(h,v,w,p)},n,u?a:void 0,u)}})}),o.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,n){o.fn[n]=function(i){return this.on(n,i)}}),o.fn.extend({bind:function(e,n,i){return this.on(e,null,n,i)},unbind:function(e,n){return this.off(e,null,n)},delegate:function(e,n,i,r){return this.on(n,e,i,r)},undelegate:function(e,n,i){return arguments.length===1?this.off(e,"**"):this.off(n,e||"**",i)},hover:function(e,n){return this.on("mouseenter",e).on("mouseleave",n||e)}}),o.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){o.fn[n]=function(i,r){return 0<arguments.length?this.on(n,null,i,r):this.trigger(n)}});var xr=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;o.proxy=function(e,n){var i,r,a;if(typeof n=="string"&&(i=e[n],n=e,e=i),B(e))return r=Xe.call(arguments,2),(a=function(){return e.apply(n||this,r.concat(Xe.call(arguments)))}).guid=e.guid=e.guid||o.guid++,a},o.holdReady=function(e){e?o.readyWait++:o.ready(!0)},o.isArray=Array.isArray,o.parseJSON=JSON.parse,o.nodeName=he,o.isFunction=B,o.isWindow=Ie,o.camelCase=bt,o.type=sn,o.now=Date.now,o.isNumeric=function(e){var n=o.type(e);return(n==="number"||n==="string")&&!isNaN(e-parseFloat(e))},o.trim=function(e){return e==null?"":(e+"").replace(xr,"$1")},typeof define=="function"&&define.amd&&define("jquery",[],function(){return o});var Tr=F.jQuery,An=F.$;return o.noConflict=function(e){return F.$===o&&(F.$=An),e&&F.jQuery===o&&(F.jQuery=Tr),o},typeof ot>"u"&&(F.jQuery=F.$=o),o});/*!
  * Bootstrap v5.3.3 (https://getbootstrap.com/)
  * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */(function(F,ot){typeof exports=="object"&&typeof module<"u"?module.exports=ot():typeof define=="function"&&define.amd?define(ot):(F=typeof globalThis<"u"?globalThis:F||self).bootstrap=ot()})(this,function(){"use strict";const F=new Map,ot={set(l,t,s){F.has(l)||F.set(l,new Map);const d=F.get(l);d.has(t)||d.size===0?d.set(t,s):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(d.keys())[0]}.`)},get:(l,t)=>F.has(l)&&F.get(l).get(t)||null,remove(l,t){if(!F.has(l))return;const s=F.get(l);s.delete(t),s.size===0&&F.delete(l)}},Se="transitionend",Ei=l=>(l&&window.CSS&&window.CSS.escape&&(l=l.replace(/#([^\s"#']+)/g,(t,s)=>`#${CSS.escape(s)}`)),l),Xe=l=>{l.dispatchEvent(new Event(Se))},yt=l=>!(!l||typeof l!="object")&&(l.jquery!==void 0&&(l=l[0]),l.nodeType!==void 0),st=l=>yt(l)?l.jquery?l[0]:l:typeof l=="string"&&l.length>0?document.querySelector(Ei(l)):null,Ue=l=>{if(!yt(l)||l.getClientRects().length===0)return!1;const t=getComputedStyle(l).getPropertyValue("visibility")==="visible",s=l.closest("details:not([open])");if(!s)return t;if(s!==l){const d=l.closest("summary");if(d&&d.parentNode!==s||d===null)return!1}return t},at=l=>!l||l.nodeType!==Node.ELEMENT_NODE||!!l.classList.contains("disabled")||(l.disabled!==void 0?l.disabled:l.hasAttribute("disabled")&&l.getAttribute("disabled")!=="false"),Ci=l=>{if(!document.documentElement.attachShadow)return null;if(typeof l.getRootNode=="function"){const t=l.getRootNode();return t instanceof ShadowRoot?t:null}return l instanceof ShadowRoot?l:l.parentNode?Ci(l.parentNode):null},Mt=()=>{},on=l=>{l.offsetHeight},tr=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,K=[],B=()=>document.documentElement.dir==="rtl",Ie=l=>{var t;t=()=>{const s=tr();if(s){const d=l.NAME,m=s.fn[d];s.fn[d]=l.jQueryInterface,s.fn[d].Constructor=l,s.fn[d].noConflict=()=>(s.fn[d]=m,l.jQueryInterface)}},document.readyState==="loading"?(K.length||document.addEventListener("DOMContentLoaded",()=>{for(const s of K)s()}),K.push(t)):t()},q=(l,t=[],s=l)=>typeof l=="function"?l(...t):s,nr=(l,t,s=!0)=>{if(!s)return void q(l);const d=(T=>{if(!T)return 0;let{transitionDuration:k,transitionDelay:O}=window.getComputedStyle(T);const P=Number.parseFloat(k),M=Number.parseFloat(O);return P||M?(k=k.split(",")[0],O=O.split(",")[0],1e3*(Number.parseFloat(k)+Number.parseFloat(O))):0})(t)+5;let m=!1;const y=({target:T})=>{T===t&&(m=!0,t.removeEventListener(Se,y),q(l))};t.addEventListener(Se,y),setTimeout(()=>{m||Xe(t)},d)},ti=(l,t,s,d)=>{const m=l.length;let y=l.indexOf(t);return y===-1?!s&&d?l[m-1]:l[0]:(y+=s?1:-1,d&&(y=(y+m)%m),l[Math.max(0,Math.min(y,m-1))])},sn=/[^.]*(?=\..*)\.|.*/,ir=/\..*/,Lr=/::\d+$/,o={};let ni=1;const he={mouseenter:"mouseover",mouseleave:"mouseout"},jr=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function rr(l,t){return t&&`${t}::${ni++}`||l.uidEvent||ni++}function or(l){const t=rr(l);return l.uidEvent=t,o[t]=o[t]||{},o[t]}function ce(l,t,s=null){return Object.values(l).find(d=>d.callable===t&&d.delegationSelector===s)}function an(l,t,s){const d=typeof t=="string",m=d?s:t||s;let y=ii(l);return jr.has(y)||(y=l),[d,m,y]}function sr(l,t,s,d,m){if(typeof t!="string"||!l)return;let[y,T,k]=an(t,s,d);t in he&&(T=(J=>function(Q){if(!Q.relatedTarget||Q.relatedTarget!==Q.delegateTarget&&!Q.delegateTarget.contains(Q.relatedTarget))return J.call(this,Q)})(T));const O=or(l),P=O[k]||(O[k]={}),M=ce(P,T,y?s:null);if(M)return void(M.oneOff=M.oneOff&&m);const D=rr(T,t.replace(sn,"")),ee=y?function(U,J,Q){return function Z(ge){const be=U.querySelectorAll(J);for(let{target:ie}=ge;ie&&ie!==this;ie=ie.parentNode)for(const ue of be)if(ue===ie)return ri(ge,{delegateTarget:ie}),Z.oneOff&&S.off(U,ge.type,J,Q),Q.apply(ie,[ge])}}(l,s,T):function(U,J){return function Q(Z){return ri(Z,{delegateTarget:U}),Q.oneOff&&S.off(U,Z.type,J),J.apply(U,[Z])}}(l,T);ee.delegationSelector=y?s:null,ee.callable=T,ee.oneOff=m,ee.uidEvent=D,P[D]=ee,l.addEventListener(k,ee,y)}function Ai(l,t,s,d,m){const y=ce(t[s],d,m);y&&(l.removeEventListener(s,y,!!m),delete t[s][y.uidEvent])}function At(l,t,s,d){const m=t[s]||{};for(const[y,T]of Object.entries(m))y.includes(d)&&Ai(l,t,s,T.callable,T.delegationSelector)}function ii(l){return l=l.replace(ir,""),he[l]||l}const S={on(l,t,s,d){sr(l,t,s,d,!1)},one(l,t,s,d){sr(l,t,s,d,!0)},off(l,t,s,d){if(typeof t!="string"||!l)return;const[m,y,T]=an(t,s,d),k=T!==t,O=or(l),P=O[T]||{},M=t.startsWith(".");if(y===void 0){if(M)for(const D of Object.keys(O))At(l,O,D,t.slice(1));for(const[D,ee]of Object.entries(P)){const U=D.replace(Lr,"");k&&!t.includes(U)||Ai(l,O,T,ee.callable,ee.delegationSelector)}}else{if(!Object.keys(P).length)return;Ai(l,O,T,y,m?s:null)}},trigger(l,t,s){if(typeof t!="string"||!l)return null;const d=tr();let m=null,y=!0,T=!0,k=!1;t!==ii(t)&&d&&(m=d.Event(t,s),d(l).trigger(m),y=!m.isPropagationStopped(),T=!m.isImmediatePropagationStopped(),k=m.isDefaultPrevented());const O=ri(new Event(t,{bubbles:y,cancelable:!0}),s);return k&&O.preventDefault(),T&&l.dispatchEvent(O),O.defaultPrevented&&m&&m.preventDefault(),O}};function ri(l,t={}){for(const[s,d]of Object.entries(t))try{l[s]=d}catch{Object.defineProperty(l,s,{configurable:!0,get:()=>d})}return l}function ki(l){if(l==="true")return!0;if(l==="false")return!1;if(l===Number(l).toString())return Number(l);if(l===""||l==="null")return null;if(typeof l!="string")return l;try{return JSON.parse(decodeURIComponent(l))}catch{return l}}function oi(l){return l.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const lt={setDataAttribute(l,t,s){l.setAttribute(`data-bs-${oi(t)}`,s)},removeDataAttribute(l,t){l.removeAttribute(`data-bs-${oi(t)}`)},getDataAttributes(l){if(!l)return{};const t={},s=Object.keys(l.dataset).filter(d=>d.startsWith("bs")&&!d.startsWith("bsConfig"));for(const d of s){let m=d.replace(/^bs/,"");m=m.charAt(0).toLowerCase()+m.slice(1,m.length),t[m]=ki(l.dataset[d])}return t},getDataAttribute:(l,t)=>ki(l.getAttribute(`data-bs-${oi(t)}`))};class ln{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,s){const d=yt(s)?lt.getDataAttribute(s,"config"):{};return{...this.constructor.Default,...typeof d=="object"?d:{},...yt(s)?lt.getDataAttributes(s):{},...typeof t=="object"?t:{}}}_typeCheckConfig(t,s=this.constructor.DefaultType){for(const[m,y]of Object.entries(s)){const T=t[m],k=yt(T)?"element":(d=T)==null?`${d}`:Object.prototype.toString.call(d).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(y).test(k))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${m}" provided type "${k}" but expected type "${y}".`)}var d}}class ct extends ln{constructor(t,s){super(),(t=st(t))&&(this._element=t,this._config=this._getConfig(s),ot.set(this._element,this.constructor.DATA_KEY,this))}dispose(){ot.remove(this._element,this.constructor.DATA_KEY),S.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,s,d=!0){nr(t,s,d)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return ot.get(st(t),this.DATA_KEY)}static getOrCreateInstance(t,s={}){return this.getInstance(t)||new this(t,typeof s=="object"?s:null)}static get VERSION(){return"5.3.3"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const Si=l=>{let t=l.getAttribute("data-bs-target");if(!t||t==="#"){let s=l.getAttribute("href");if(!s||!s.includes("#")&&!s.startsWith("."))return null;s.includes("#")&&!s.startsWith("#")&&(s=`#${s.split("#")[1]}`),t=s&&s!=="#"?s.trim():null}return t?t.split(",").map(s=>Ei(s)).join(","):null},R={find:(l,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,l)),findOne:(l,t=document.documentElement)=>Element.prototype.querySelector.call(t,l),children:(l,t)=>[].concat(...l.children).filter(s=>s.matches(t)),parents(l,t){const s=[];let d=l.parentNode.closest(t);for(;d;)s.push(d),d=d.parentNode.closest(t);return s},prev(l,t){let s=l.previousElementSibling;for(;s;){if(s.matches(t))return[s];s=s.previousElementSibling}return[]},next(l,t){let s=l.nextElementSibling;for(;s;){if(s.matches(t))return[s];s=s.nextElementSibling}return[]},focusableChildren(l){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(s=>`${s}:not([tabindex^="-"])`).join(",");return this.find(t,l).filter(s=>!at(s)&&Ue(s))},getSelectorFromElement(l){const t=Si(l);return t&&R.findOne(t)?t:null},getElementFromSelector(l){const t=Si(l);return t?R.findOne(t):null},getMultipleElementsFromSelector(l){const t=Si(l);return t?R.find(t):[]}},Dn=(l,t="hide")=>{const s=`click.dismiss${l.EVENT_KEY}`,d=l.NAME;S.on(document,s,`[data-bs-dismiss="${d}"]`,function(m){if(["A","AREA"].includes(this.tagName)&&m.preventDefault(),at(this))return;const y=R.getElementFromSelector(this)||this.closest(`.${d}`);l.getOrCreateInstance(y)[t]()})},ut=".bs.alert",cn=`close${ut}`,si=`closed${ut}`;class un extends ct{static get NAME(){return"alert"}close(){if(S.trigger(this._element,cn).defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,t)}_destroyElement(){this._element.remove(),S.trigger(this._element,si),this.dispose()}static jQueryInterface(t){return this.each(function(){const s=un.getOrCreateInstance(this);if(typeof t=="string"){if(s[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);s[t](this)}})}}Dn(un,"close"),Ie(un);const ar='[data-bs-toggle="button"]';class zt extends ct{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each(function(){const s=zt.getOrCreateInstance(this);t==="toggle"&&s[t]()})}}S.on(document,"click.bs.button.data-api",ar,l=>{l.preventDefault();const t=l.target.closest(ar);zt.getOrCreateInstance(t).toggle()}),Ie(zt);const kt=".bs.swipe",St=`touchstart${kt}`,Nr=`touchmove${kt}`,Pr=`touchend${kt}`,Mr=`pointerdown${kt}`,bt=`pointerup${kt}`,Ln={endCallback:null,leftCallback:null,rightCallback:null},jn={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class I extends ln{constructor(t,s){super(),this._element=t,t&&I.isSupported()&&(this._config=this._getConfig(s),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return Ln}static get DefaultType(){return jn}static get NAME(){return"swipe"}dispose(){S.off(this._element,kt)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),q(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const s=t/this._deltaX;this._deltaX=0,s&&q(s>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(S.on(this._element,Mr,t=>this._start(t)),S.on(this._element,bt,t=>this._end(t)),this._element.classList.add("pointer-event")):(S.on(this._element,St,t=>this._start(t)),S.on(this._element,Nr,t=>this._move(t)),S.on(this._element,Pr,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType==="pen"||t.pointerType==="touch")}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const xe=".bs.carousel",lr=".data-api",Nn="next",Vt="prev",Xt="left",It="right",Ot=`slide${xe}`,Dt=`slid${xe}`,dn=`keydown${xe}`,Ir=`mouseenter${xe}`,ai=`mouseleave${xe}`,cr=`dragstart${xe}`,ur=`load${xe}${lr}`,fn=`click${xe}${lr}`,$t="carousel",Ut="active",hn=".active",Oi=".carousel-item",dr=hn+Oi,tt={ArrowLeft:It,ArrowRight:Xt},Ye={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Di={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class pn extends ct{constructor(t,s){super(t,s),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=R.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===$t&&this.cycle()}static get Default(){return Ye}static get DefaultType(){return Di}static get NAME(){return"carousel"}next(){this._slide(Nn)}nextWhenVisible(){!document.hidden&&Ue(this._element)&&this.next()}prev(){this._slide(Vt)}pause(){this._isSliding&&Xe(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?S.one(this._element,Dt,()=>this.cycle()):this.cycle())}to(t){const s=this._getItems();if(t>s.length-1||t<0)return;if(this._isSliding)return void S.one(this._element,Dt,()=>this.to(t));const d=this._getItemIndex(this._getActive());if(d===t)return;const m=t>d?Nn:Vt;this._slide(m,s[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&S.on(this._element,dn,t=>this._keydown(t)),this._config.pause==="hover"&&(S.on(this._element,Ir,()=>this.pause()),S.on(this._element,ai,()=>this._maybeEnableCycle())),this._config.touch&&I.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const s of R.find(".carousel-item img",this._element))S.on(s,cr,d=>d.preventDefault());const t={leftCallback:()=>this._slide(this._directionToOrder(Xt)),rightCallback:()=>this._slide(this._directionToOrder(It)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}};this._swipeHelper=new I(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const s=tt[t.key];s&&(t.preventDefault(),this._slide(this._directionToOrder(s)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const s=R.findOne(hn,this._indicatorsElement);s.classList.remove(Ut),s.removeAttribute("aria-current");const d=R.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);d&&(d.classList.add(Ut),d.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const s=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=s||this._config.defaultInterval}_slide(t,s=null){if(this._isSliding)return;const d=this._getActive(),m=t===Nn,y=s||ti(this._getItems(),d,m,this._config.wrap);if(y===d)return;const T=this._getItemIndex(y),k=D=>S.trigger(this._element,D,{relatedTarget:y,direction:this._orderToDirection(t),from:this._getItemIndex(d),to:T});if(k(Ot).defaultPrevented||!d||!y)return;const O=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(T),this._activeElement=y;const P=m?"carousel-item-start":"carousel-item-end",M=m?"carousel-item-next":"carousel-item-prev";y.classList.add(M),on(y),d.classList.add(P),y.classList.add(P),this._queueCallback(()=>{y.classList.remove(P,M),y.classList.add(Ut),d.classList.remove(Ut,M,P),this._isSliding=!1,k(Dt)},d,this._isAnimated()),O&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return R.findOne(dr,this._element)}_getItems(){return R.find(Oi,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return B()?t===Xt?Vt:Nn:t===Xt?Nn:Vt}_orderToDirection(t){return B()?t===Vt?Xt:It:t===Vt?It:Xt}static jQueryInterface(t){return this.each(function(){const s=pn.getOrCreateInstance(this,t);if(typeof t!="number"){if(typeof t=="string"){if(s[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);s[t]()}}else s.to(t)})}}S.on(document,fn,"[data-bs-slide], [data-bs-slide-to]",function(l){const t=R.getElementFromSelector(this);if(!t||!t.classList.contains($t))return;l.preventDefault();const s=pn.getOrCreateInstance(t),d=this.getAttribute("data-bs-slide-to");return d?(s.to(d),void s._maybeEnableCycle()):lt.getDataAttribute(this,"slide")==="next"?(s.next(),void s._maybeEnableCycle()):(s.prev(),void s._maybeEnableCycle())}),S.on(window,ur,()=>{const l=R.find('[data-bs-ride="carousel"]');for(const t of l)pn.getOrCreateInstance(t)}),Ie(pn);const gn=".bs.collapse",fr=`show${gn}`,mn=`shown${gn}`,vn=`hide${gn}`,Li=`hidden${gn}`,li=`click${gn}.data-api`,ji="show",yn="collapse",ci="collapsing",hr=`:scope .${yn} .${yn}`,Ni='[data-bs-toggle="collapse"]',$r={parent:null,toggle:!0},pr={parent:"(null|element)",toggle:"boolean"};class dt extends ct{constructor(t,s){super(t,s),this._isTransitioning=!1,this._triggerArray=[];const d=R.find(Ni);for(const m of d){const y=R.getSelectorFromElement(m),T=R.find(y).filter(k=>k===this._element);y!==null&&T.length&&this._triggerArray.push(m)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return $r}static get DefaultType(){return pr}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(m=>m!==this._element).map(m=>dt.getOrCreateInstance(m,{toggle:!1}))),t.length&&t[0]._isTransitioning||S.trigger(this._element,fr).defaultPrevented)return;for(const m of t)m.hide();const s=this._getDimension();this._element.classList.remove(yn),this._element.classList.add(ci),this._element.style[s]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const d=`scroll${s[0].toUpperCase()+s.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(ci),this._element.classList.add(yn,ji),this._element.style[s]="",S.trigger(this._element,mn)},this._element,!0),this._element.style[s]=`${this._element[d]}px`}hide(){if(this._isTransitioning||!this._isShown()||S.trigger(this._element,vn).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,on(this._element),this._element.classList.add(ci),this._element.classList.remove(yn,ji);for(const s of this._triggerArray){const d=R.getElementFromSelector(s);d&&!this._isShown(d)&&this._addAriaAndCollapsedClass([s],!1)}this._isTransitioning=!0,this._element.style[t]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(ci),this._element.classList.add(yn),S.trigger(this._element,Li)},this._element,!0)}_isShown(t=this._element){return t.classList.contains(ji)}_configAfterMerge(t){return t.toggle=!!t.toggle,t.parent=st(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(Ni);for(const s of t){const d=R.getElementFromSelector(s);d&&this._addAriaAndCollapsedClass([s],this._isShown(d))}}_getFirstLevelChildren(t){const s=R.find(hr,this._config.parent);return R.find(t,this._config.parent).filter(d=>!s.includes(d))}_addAriaAndCollapsedClass(t,s){if(t.length)for(const d of t)d.classList.toggle("collapsed",!s),d.setAttribute("aria-expanded",s)}static jQueryInterface(t){const s={};return typeof t=="string"&&/show|hide/.test(t)&&(s.toggle=!1),this.each(function(){const d=dt.getOrCreateInstance(this,s);if(typeof t=="string"){if(d[t]===void 0)throw new TypeError(`No method named "${t}"`);d[t]()}})}}S.on(document,li,Ni,function(l){(l.target.tagName==="A"||l.delegateTarget&&l.delegateTarget.tagName==="A")&&l.preventDefault();for(const t of R.getMultipleElementsFromSelector(this))dt.getOrCreateInstance(t,{toggle:!1}).toggle()}),Ie(dt);var $e="top",qe="bottom",Fe="right",je="left",Pn="auto",bn=[$e,qe,Fe,je],ft="start",Yt="end",Pi="clippingParents",ui="viewport",Qt="popper",di="reference",Mi=bn.reduce(function(l,t){return l.concat([t+"-"+ft,t+"-"+Yt])},[]),Ii=[].concat(bn,[Pn]).reduce(function(l,t){return l.concat([t,t+"-"+ft,t+"-"+Yt])},[]),$i="beforeRead",Hi="read",fi="afterRead",qi="beforeMain",nt="main",Kt="afterMain",Mn="beforeWrite",Gt="write",Fi="afterWrite",gr=[$i,Hi,fi,qi,nt,Kt,Mn,Gt,Fi];function _t(l){return l?(l.nodeName||"").toLowerCase():null}function Re(l){if(l==null)return window;if(l.toString()!=="[object Window]"){var t=l.ownerDocument;return t&&t.defaultView||window}return l}function Ht(l){return l instanceof Re(l).Element||l instanceof Element}function We(l){return l instanceof Re(l).HTMLElement||l instanceof HTMLElement}function hi(l){return typeof ShadowRoot<"u"&&(l instanceof Re(l).ShadowRoot||l instanceof ShadowRoot)}const Je={name:"applyStyles",enabled:!0,phase:"write",fn:function(l){var t=l.state;Object.keys(t.elements).forEach(function(s){var d=t.styles[s]||{},m=t.attributes[s]||{},y=t.elements[s];We(y)&&_t(y)&&(Object.assign(y.style,d),Object.keys(m).forEach(function(T){var k=m[T];k===!1?y.removeAttribute(T):y.setAttribute(T,k===!0?"":k)}))})},effect:function(l){var t=l.state,s={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,s.popper),t.styles=s,t.elements.arrow&&Object.assign(t.elements.arrow.style,s.arrow),function(){Object.keys(t.elements).forEach(function(d){var m=t.elements[d],y=t.attributes[d]||{},T=Object.keys(t.styles.hasOwnProperty(d)?t.styles[d]:s[d]).reduce(function(k,O){return k[O]="",k},{});We(m)&&_t(m)&&(Object.assign(m.style,T),Object.keys(y).forEach(function(k){m.removeAttribute(k)}))})}},requires:["computeStyles"]};function ht(l){return l.split("-")[0]}var pt=Math.max,pi=Math.min,_n=Math.round;function Lt(){var l=navigator.userAgentData;return l!=null&&l.brands&&Array.isArray(l.brands)?l.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function qt(){return!/^((?!chrome|android).)*safari/i.test(Lt())}function Ft(l,t,s){t===void 0&&(t=!1),s===void 0&&(s=!1);var d=l.getBoundingClientRect(),m=1,y=1;t&&We(l)&&(m=l.offsetWidth>0&&_n(d.width)/l.offsetWidth||1,y=l.offsetHeight>0&&_n(d.height)/l.offsetHeight||1);var T=(Ht(l)?Re(l):window).visualViewport,k=!qt()&&s,O=(d.left+(k&&T?T.offsetLeft:0))/m,P=(d.top+(k&&T?T.offsetTop:0))/y,M=d.width/m,D=d.height/y;return{width:M,height:D,top:P,right:O+M,bottom:P+D,left:O,x:O,y:P}}function Ri(l){var t=Ft(l),s=l.offsetWidth,d=l.offsetHeight;return Math.abs(t.width-s)<=1&&(s=t.width),Math.abs(t.height-d)<=1&&(d=t.height),{x:l.offsetLeft,y:l.offsetTop,width:s,height:d}}function wn(l,t){var s=t.getRootNode&&t.getRootNode();if(l.contains(t))return!0;if(s&&hi(s)){var d=t;do{if(d&&l.isSameNode(d))return!0;d=d.parentNode||d.host}while(d)}return!1}function wt(l){return Re(l).getComputedStyle(l)}function Wi(l){return["table","td","th"].indexOf(_t(l))>=0}function jt(l){return((Ht(l)?l.ownerDocument:l.document)||window.document).documentElement}function In(l){return _t(l)==="html"?l:l.assignedSlot||l.parentNode||(hi(l)?l.host:null)||jt(l)}function mr(l){return We(l)&&wt(l).position!=="fixed"?l.offsetParent:null}function xn(l){for(var t=Re(l),s=mr(l);s&&Wi(s)&&wt(s).position==="static";)s=mr(s);return s&&(_t(s)==="html"||_t(s)==="body"&&wt(s).position==="static")?t:s||function(d){var m=/firefox/i.test(Lt());if(/Trident/i.test(Lt())&&We(d)&&wt(d).position==="fixed")return null;var y=In(d);for(hi(y)&&(y=y.host);We(y)&&["html","body"].indexOf(_t(y))<0;){var T=wt(y);if(T.transform!=="none"||T.perspective!=="none"||T.contain==="paint"||["transform","perspective"].indexOf(T.willChange)!==-1||m&&T.willChange==="filter"||m&&T.filter&&T.filter!=="none")return y;y=y.parentNode}return null}(l)||t}function Bi(l){return["top","bottom"].indexOf(l)>=0?"x":"y"}function $n(l,t,s){return pt(l,pi(t,s))}function gi(l){return Object.assign({},{top:0,right:0,bottom:0,left:0},l)}function vr(l,t){return t.reduce(function(s,d){return s[d]=l,s},{})}const yr={name:"arrow",enabled:!0,phase:"main",fn:function(l){var t,s=l.state,d=l.name,m=l.options,y=s.elements.arrow,T=s.modifiersData.popperOffsets,k=ht(s.placement),O=Bi(k),P=[je,Fe].indexOf(k)>=0?"height":"width";if(y&&T){var M=function(me,fe){return gi(typeof(me=typeof me=="function"?me(Object.assign({},fe.rects,{placement:fe.placement})):me)!="number"?me:vr(me,bn))}(m.padding,s),D=Ri(y),ee=O==="y"?$e:je,U=O==="y"?qe:Fe,J=s.rects.reference[P]+s.rects.reference[O]-T[O]-s.rects.popper[P],Q=T[O]-s.rects.reference[O],Z=xn(y),ge=Z?O==="y"?Z.clientHeight||0:Z.clientWidth||0:0,be=J/2-Q/2,ie=M[ee],ue=ge-D[P]-M[U],te=ge/2-D[P]/2+be,ae=$n(ie,te,ue),de=O;s.modifiersData[d]=((t={})[de]=ae,t.centerOffset=ae-te,t)}},effect:function(l){var t=l.state,s=l.options.element,d=s===void 0?"[data-popper-arrow]":s;d!=null&&(typeof d!="string"||(d=t.elements.popper.querySelector(d)))&&wn(t.elements.popper,d)&&(t.elements.arrow=d)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Tn(l){return l.split("-")[1]}var Hr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function br(l){var t,s=l.popper,d=l.popperRect,m=l.placement,y=l.variation,T=l.offsets,k=l.position,O=l.gpuAcceleration,P=l.adaptive,M=l.roundOffsets,D=l.isFixed,ee=T.x,U=ee===void 0?0:ee,J=T.y,Q=J===void 0?0:J,Z=typeof M=="function"?M({x:U,y:Q}):{x:U,y:Q};U=Z.x,Q=Z.y;var ge=T.hasOwnProperty("x"),be=T.hasOwnProperty("y"),ie=je,ue=$e,te=window;if(P){var ae=xn(s),de="clientHeight",me="clientWidth";ae===Re(s)&&wt(ae=jt(s)).position!=="static"&&k==="absolute"&&(de="scrollHeight",me="scrollWidth"),(m===$e||(m===je||m===Fe)&&y===Yt)&&(ue=qe,Q-=(D&&ae===te&&te.visualViewport?te.visualViewport.height:ae[de])-d.height,Q*=O?1:-1),m!==je&&(m!==$e&&m!==qe||y!==Yt)||(ie=Fe,U-=(D&&ae===te&&te.visualViewport?te.visualViewport.width:ae[me])-d.width,U*=O?1:-1)}var fe,Le=Object.assign({position:k},P&&Hr),vt=M===!0?function(Pt,Ze){var Tt=Pt.x,Et=Pt.y,ke=Ze.devicePixelRatio||1;return{x:_n(Tt*ke)/ke||0,y:_n(Et*ke)/ke||0}}({x:U,y:Q},Re(s)):{x:U,y:Q};return U=vt.x,Q=vt.y,O?Object.assign({},Le,((fe={})[ue]=be?"0":"",fe[ie]=ge?"0":"",fe.transform=(te.devicePixelRatio||1)<=1?"translate("+U+"px, "+Q+"px)":"translate3d("+U+"px, "+Q+"px, 0)",fe)):Object.assign({},Le,((t={})[ue]=be?Q+"px":"",t[ie]=ge?U+"px":"",t.transform="",t))}const zi={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(l){var t=l.state,s=l.options,d=s.gpuAcceleration,m=d===void 0||d,y=s.adaptive,T=y===void 0||y,k=s.roundOffsets,O=k===void 0||k,P={placement:ht(t.placement),variation:Tn(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:m,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,br(Object.assign({},P,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:T,roundOffsets:O})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,br(Object.assign({},P,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:O})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var Hn={passive:!0};const qn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(l){var t=l.state,s=l.instance,d=l.options,m=d.scroll,y=m===void 0||m,T=d.resize,k=T===void 0||T,O=Re(t.elements.popper),P=[].concat(t.scrollParents.reference,t.scrollParents.popper);return y&&P.forEach(function(M){M.addEventListener("scroll",s.update,Hn)}),k&&O.addEventListener("resize",s.update,Hn),function(){y&&P.forEach(function(M){M.removeEventListener("scroll",s.update,Hn)}),k&&O.removeEventListener("resize",s.update,Hn)}},data:{}};var _r={left:"right",right:"left",bottom:"top",top:"bottom"};function En(l){return l.replace(/left|right|bottom|top/g,function(t){return _r[t]})}var wr={start:"end",end:"start"};function Vi(l){return l.replace(/start|end/g,function(t){return wr[t]})}function Fn(l){var t=Re(l);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Xi(l){return Ft(jt(l)).left+Fn(l).scrollLeft}function Jt(l){var t=wt(l),s=t.overflow,d=t.overflowX,m=t.overflowY;return/auto|scroll|overlay|hidden/.test(s+m+d)}function Ui(l){return["html","body","#document"].indexOf(_t(l))>=0?l.ownerDocument.body:We(l)&&Jt(l)?l:Ui(In(l))}function Cn(l,t){var s;t===void 0&&(t=[]);var d=Ui(l),m=d===((s=l.ownerDocument)==null?void 0:s.body),y=Re(d),T=m?[y].concat(y.visualViewport||[],Jt(d)?d:[]):d,k=t.concat(T);return m?k:k.concat(Cn(In(T)))}function Rn(l){return Object.assign({},l,{left:l.x,top:l.y,right:l.x+l.width,bottom:l.y+l.height})}function xr(l,t,s){return t===ui?Rn(function(d,m){var y=Re(d),T=jt(d),k=y.visualViewport,O=T.clientWidth,P=T.clientHeight,M=0,D=0;if(k){O=k.width,P=k.height;var ee=qt();(ee||!ee&&m==="fixed")&&(M=k.offsetLeft,D=k.offsetTop)}return{width:O,height:P,x:M+Xi(d),y:D}}(l,s)):Ht(t)?function(d,m){var y=Ft(d,!1,m==="fixed");return y.top=y.top+d.clientTop,y.left=y.left+d.clientLeft,y.bottom=y.top+d.clientHeight,y.right=y.left+d.clientWidth,y.width=d.clientWidth,y.height=d.clientHeight,y.x=y.left,y.y=y.top,y}(t,s):Rn(function(d){var m,y=jt(d),T=Fn(d),k=(m=d.ownerDocument)==null?void 0:m.body,O=pt(y.scrollWidth,y.clientWidth,k?k.scrollWidth:0,k?k.clientWidth:0),P=pt(y.scrollHeight,y.clientHeight,k?k.scrollHeight:0,k?k.clientHeight:0),M=-T.scrollLeft+Xi(d),D=-T.scrollTop;return wt(k||y).direction==="rtl"&&(M+=pt(y.clientWidth,k?k.clientWidth:0)-O),{width:O,height:P,x:M,y:D}}(jt(l)))}function Tr(l){var t,s=l.reference,d=l.element,m=l.placement,y=m?ht(m):null,T=m?Tn(m):null,k=s.x+s.width/2-d.width/2,O=s.y+s.height/2-d.height/2;switch(y){case $e:t={x:k,y:s.y-d.height};break;case qe:t={x:k,y:s.y+s.height};break;case Fe:t={x:s.x+s.width,y:O};break;case je:t={x:s.x-d.width,y:O};break;default:t={x:s.x,y:s.y}}var P=y?Bi(y):null;if(P!=null){var M=P==="y"?"height":"width";switch(T){case ft:t[P]=t[P]-(s[M]/2-d[M]/2);break;case Yt:t[P]=t[P]+(s[M]/2-d[M]/2)}}return t}function An(l,t){t===void 0&&(t={});var s=t,d=s.placement,m=d===void 0?l.placement:d,y=s.strategy,T=y===void 0?l.strategy:y,k=s.boundary,O=k===void 0?Pi:k,P=s.rootBoundary,M=P===void 0?ui:P,D=s.elementContext,ee=D===void 0?Qt:D,U=s.altBoundary,J=U!==void 0&&U,Q=s.padding,Z=Q===void 0?0:Q,ge=gi(typeof Z!="number"?Z:vr(Z,bn)),be=ee===Qt?di:Qt,ie=l.rects.popper,ue=l.elements[J?be:ee],te=function(Ze,Tt,Et,ke){var Wt=Tt==="clippingParents"?function(ve){var et=Cn(In(ve)),Ct=["absolute","fixed"].indexOf(wt(ve).position)>=0&&We(ve)?xn(ve):ve;return Ht(Ct)?et.filter(function(On){return Ht(On)&&wn(On,Ct)&&_t(On)!=="body"}):[]}(Ze):[].concat(Tt),Bt=[].concat(Wt,[Et]),Ti=Bt[0],He=Bt.reduce(function(ve,et){var Ct=xr(Ze,et,ke);return ve.top=pt(Ct.top,ve.top),ve.right=pi(Ct.right,ve.right),ve.bottom=pi(Ct.bottom,ve.bottom),ve.left=pt(Ct.left,ve.left),ve},xr(Ze,Ti,ke));return He.width=He.right-He.left,He.height=He.bottom-He.top,He.x=He.left,He.y=He.top,He}(Ht(ue)?ue:ue.contextElement||jt(l.elements.popper),O,M,T),ae=Ft(l.elements.reference),de=Tr({reference:ae,element:ie,strategy:"absolute",placement:m}),me=Rn(Object.assign({},ie,de)),fe=ee===Qt?me:ae,Le={top:te.top-fe.top+ge.top,bottom:fe.bottom-te.bottom+ge.bottom,left:te.left-fe.left+ge.left,right:fe.right-te.right+ge.right},vt=l.modifiersData.offset;if(ee===Qt&&vt){var Pt=vt[m];Object.keys(Le).forEach(function(Ze){var Tt=[Fe,qe].indexOf(Ze)>=0?1:-1,Et=[$e,qe].indexOf(Ze)>=0?"y":"x";Le[Ze]+=Pt[Et]*Tt})}return Le}function e(l,t){t===void 0&&(t={});var s=t,d=s.placement,m=s.boundary,y=s.rootBoundary,T=s.padding,k=s.flipVariations,O=s.allowedAutoPlacements,P=O===void 0?Ii:O,M=Tn(d),D=M?k?Mi:Mi.filter(function(J){return Tn(J)===M}):bn,ee=D.filter(function(J){return P.indexOf(J)>=0});ee.length===0&&(ee=D);var U=ee.reduce(function(J,Q){return J[Q]=An(l,{placement:Q,boundary:m,rootBoundary:y,padding:T})[ht(Q)],J},{});return Object.keys(U).sort(function(J,Q){return U[J]-U[Q]})}const n={name:"flip",enabled:!0,phase:"main",fn:function(l){var t=l.state,s=l.options,d=l.name;if(!t.modifiersData[d]._skip){for(var m=s.mainAxis,y=m===void 0||m,T=s.altAxis,k=T===void 0||T,O=s.fallbackPlacements,P=s.padding,M=s.boundary,D=s.rootBoundary,ee=s.altBoundary,U=s.flipVariations,J=U===void 0||U,Q=s.allowedAutoPlacements,Z=t.options.placement,ge=ht(Z),be=O||(ge!==Z&&J?function(ve){if(ht(ve)===Pn)return[];var et=En(ve);return[Vi(ve),et,Vi(et)]}(Z):[En(Z)]),ie=[Z].concat(be).reduce(function(ve,et){return ve.concat(ht(et)===Pn?e(t,{placement:et,boundary:M,rootBoundary:D,padding:P,flipVariations:J,allowedAutoPlacements:Q}):et)},[]),ue=t.rects.reference,te=t.rects.popper,ae=new Map,de=!0,me=ie[0],fe=0;fe<ie.length;fe++){var Le=ie[fe],vt=ht(Le),Pt=Tn(Le)===ft,Ze=[$e,qe].indexOf(vt)>=0,Tt=Ze?"width":"height",Et=An(t,{placement:Le,boundary:M,rootBoundary:D,altBoundary:ee,padding:P}),ke=Ze?Pt?Fe:je:Pt?qe:$e;ue[Tt]>te[Tt]&&(ke=En(ke));var Wt=En(ke),Bt=[];if(y&&Bt.push(Et[vt]<=0),k&&Bt.push(Et[ke]<=0,Et[Wt]<=0),Bt.every(function(ve){return ve})){me=Le,de=!1;break}ae.set(Le,Bt)}if(de)for(var Ti=function(ve){var et=ie.find(function(Ct){var On=ae.get(Ct);if(On)return On.slice(0,ve).every(function(Sr){return Sr})});if(et)return me=et,"break"},He=J?3:1;He>0&&Ti(He)!=="break";He--);t.placement!==me&&(t.modifiersData[d]._skip=!0,t.placement=me,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function i(l,t,s){return s===void 0&&(s={x:0,y:0}),{top:l.top-t.height-s.y,right:l.right-t.width+s.x,bottom:l.bottom-t.height+s.y,left:l.left-t.width-s.x}}function r(l){return[$e,Fe,qe,je].some(function(t){return l[t]>=0})}const a={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(l){var t=l.state,s=l.name,d=t.rects.reference,m=t.rects.popper,y=t.modifiersData.preventOverflow,T=An(t,{elementContext:"reference"}),k=An(t,{altBoundary:!0}),O=i(T,d),P=i(k,m,y),M=r(O),D=r(P);t.modifiersData[s]={referenceClippingOffsets:O,popperEscapeOffsets:P,isReferenceHidden:M,hasPopperEscaped:D},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":M,"data-popper-escaped":D})}},c={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(l){var t=l.state,s=l.options,d=l.name,m=s.offset,y=m===void 0?[0,0]:m,T=Ii.reduce(function(M,D){return M[D]=function(ee,U,J){var Q=ht(ee),Z=[je,$e].indexOf(Q)>=0?-1:1,ge=typeof J=="function"?J(Object.assign({},U,{placement:ee})):J,be=ge[0],ie=ge[1];return be=be||0,ie=(ie||0)*Z,[je,Fe].indexOf(Q)>=0?{x:ie,y:be}:{x:be,y:ie}}(D,t.rects,y),M},{}),k=T[t.placement],O=k.x,P=k.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=O,t.modifiersData.popperOffsets.y+=P),t.modifiersData[d]=T}},u={name:"popperOffsets",enabled:!0,phase:"read",fn:function(l){var t=l.state,s=l.name;t.modifiersData[s]=Tr({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},p={name:"preventOverflow",enabled:!0,phase:"main",fn:function(l){var t=l.state,s=l.options,d=l.name,m=s.mainAxis,y=m===void 0||m,T=s.altAxis,k=T!==void 0&&T,O=s.boundary,P=s.rootBoundary,M=s.altBoundary,D=s.padding,ee=s.tether,U=ee===void 0||ee,J=s.tetherOffset,Q=J===void 0?0:J,Z=An(t,{boundary:O,rootBoundary:P,padding:D,altBoundary:M}),ge=ht(t.placement),be=Tn(t.placement),ie=!be,ue=Bi(ge),te=ue==="x"?"y":"x",ae=t.modifiersData.popperOffsets,de=t.rects.reference,me=t.rects.popper,fe=typeof Q=="function"?Q(Object.assign({},t.rects,{placement:t.placement})):Q,Le=typeof fe=="number"?{mainAxis:fe,altAxis:fe}:Object.assign({mainAxis:0,altAxis:0},fe),vt=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,Pt={x:0,y:0};if(ae){if(y){var Ze,Tt=ue==="y"?$e:je,Et=ue==="y"?qe:Fe,ke=ue==="y"?"height":"width",Wt=ae[ue],Bt=Wt+Z[Tt],Ti=Wt-Z[Et],He=U?-me[ke]/2:0,ve=be===ft?de[ke]:me[ke],et=be===ft?-me[ke]:-de[ke],Ct=t.elements.arrow,On=U&&Ct?Ri(Ct):{width:0,height:0},Sr=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},fo=Sr[Tt],ho=Sr[Et],Or=$n(0,de[ke],On[ke]),gs=ie?de[ke]/2-He-Or-fo-Le.mainAxis:ve-Or-fo-Le.mainAxis,ms=ie?-de[ke]/2+He+Or+ho+Le.mainAxis:et+Or+ho+Le.mainAxis,Yr=t.elements.arrow&&xn(t.elements.arrow),vs=Yr?ue==="y"?Yr.clientTop||0:Yr.clientLeft||0:0,po=(Ze=vt?.[ue])!=null?Ze:0,ys=Wt+ms-po,go=$n(U?pi(Bt,Wt+gs-po-vs):Bt,Wt,U?pt(Ti,ys):Ti);ae[ue]=go,Pt[ue]=go-Wt}if(k){var mo,bs=ue==="x"?$e:je,_s=ue==="x"?qe:Fe,ei=ae[te],Dr=te==="y"?"height":"width",vo=ei+Z[bs],yo=ei-Z[_s],Qr=[$e,je].indexOf(ge)!==-1,bo=(mo=vt?.[te])!=null?mo:0,_o=Qr?vo:ei-de[Dr]-me[Dr]-bo+Le.altAxis,wo=Qr?ei+de[Dr]+me[Dr]-bo-Le.altAxis:yo,xo=U&&Qr?function(ws,xs,Kr){var To=$n(ws,xs,Kr);return To>Kr?Kr:To}(_o,ei,wo):$n(U?_o:vo,ei,U?wo:yo);ae[te]=xo,Pt[te]=xo-ei}t.modifiersData[d]=Pt}},requiresIfExists:["offset"]};function h(l,t,s){s===void 0&&(s=!1);var d,m,y=We(t),T=We(t)&&function(D){var ee=D.getBoundingClientRect(),U=_n(ee.width)/D.offsetWidth||1,J=_n(ee.height)/D.offsetHeight||1;return U!==1||J!==1}(t),k=jt(t),O=Ft(l,T,s),P={scrollLeft:0,scrollTop:0},M={x:0,y:0};return(y||!y&&!s)&&((_t(t)!=="body"||Jt(k))&&(P=(d=t)!==Re(d)&&We(d)?{scrollLeft:(m=d).scrollLeft,scrollTop:m.scrollTop}:Fn(d)),We(t)?((M=Ft(t,!0)).x+=t.clientLeft,M.y+=t.clientTop):k&&(M.x=Xi(k))),{x:O.left+P.scrollLeft-M.x,y:O.top+P.scrollTop-M.y,width:O.width,height:O.height}}function v(l){var t=new Map,s=new Set,d=[];function m(y){s.add(y.name),[].concat(y.requires||[],y.requiresIfExists||[]).forEach(function(T){if(!s.has(T)){var k=t.get(T);k&&m(k)}}),d.push(y)}return l.forEach(function(y){t.set(y.name,y)}),l.forEach(function(y){s.has(y.name)||m(y)}),d}var w={placement:"bottom",modifiers:[],strategy:"absolute"};function E(){for(var l=arguments.length,t=new Array(l),s=0;s<l;s++)t[s]=arguments[s];return!t.some(function(d){return!(d&&typeof d.getBoundingClientRect=="function")})}function _(l){l===void 0&&(l={});var t=l,s=t.defaultModifiers,d=s===void 0?[]:s,m=t.defaultOptions,y=m===void 0?w:m;return function(T,k,O){O===void 0&&(O=y);var P,M,D={placement:"bottom",orderedModifiers:[],options:Object.assign({},w,y),modifiersData:{},elements:{reference:T,popper:k},attributes:{},styles:{}},ee=[],U=!1,J={state:D,setOptions:function(Z){var ge=typeof Z=="function"?Z(D.options):Z;Q(),D.options=Object.assign({},y,D.options,ge),D.scrollParents={reference:Ht(T)?Cn(T):T.contextElement?Cn(T.contextElement):[],popper:Cn(k)};var be,ie,ue=function(te){var ae=v(te);return gr.reduce(function(de,me){return de.concat(ae.filter(function(fe){return fe.phase===me}))},[])}((be=[].concat(d,D.options.modifiers),ie=be.reduce(function(te,ae){var de=te[ae.name];return te[ae.name]=de?Object.assign({},de,ae,{options:Object.assign({},de.options,ae.options),data:Object.assign({},de.data,ae.data)}):ae,te},{}),Object.keys(ie).map(function(te){return ie[te]})));return D.orderedModifiers=ue.filter(function(te){return te.enabled}),D.orderedModifiers.forEach(function(te){var ae=te.name,de=te.options,me=de===void 0?{}:de,fe=te.effect;if(typeof fe=="function"){var Le=fe({state:D,name:ae,instance:J,options:me});ee.push(Le||function(){})}}),J.update()},forceUpdate:function(){if(!U){var Z=D.elements,ge=Z.reference,be=Z.popper;if(E(ge,be)){D.rects={reference:h(ge,xn(be),D.options.strategy==="fixed"),popper:Ri(be)},D.reset=!1,D.placement=D.options.placement,D.orderedModifiers.forEach(function(fe){return D.modifiersData[fe.name]=Object.assign({},fe.data)});for(var ie=0;ie<D.orderedModifiers.length;ie++)if(D.reset!==!0){var ue=D.orderedModifiers[ie],te=ue.fn,ae=ue.options,de=ae===void 0?{}:ae,me=ue.name;typeof te=="function"&&(D=te({state:D,options:de,name:me,instance:J})||D)}else D.reset=!1,ie=-1}}},update:(P=function(){return new Promise(function(Z){J.forceUpdate(),Z(D)})},function(){return M||(M=new Promise(function(Z){Promise.resolve().then(function(){M=void 0,Z(P())})})),M}),destroy:function(){Q(),U=!0}};if(!E(T,k))return J;function Q(){ee.forEach(function(Z){return Z()}),ee=[]}return J.setOptions(O).then(function(Z){!U&&O.onFirstUpdate&&O.onFirstUpdate(Z)}),J}}var A=_(),H=_({defaultModifiers:[qn,u,zi,Je]}),z=_({defaultModifiers:[qn,u,zi,Je,c,n,p,yr,a]});const X=Object.freeze(Object.defineProperty({__proto__:null,afterMain:Kt,afterRead:fi,afterWrite:Fi,applyStyles:Je,arrow:yr,auto:Pn,basePlacements:bn,beforeMain:qi,beforeRead:$i,beforeWrite:Mn,bottom:qe,clippingParents:Pi,computeStyles:zi,createPopper:z,createPopperBase:A,createPopperLite:H,detectOverflow:An,end:Yt,eventListeners:qn,flip:n,hide:a,left:je,main:nt,modifierPhases:gr,offset:c,placements:Ii,popper:Qt,popperGenerator:_,popperOffsets:u,preventOverflow:p,read:Hi,reference:di,right:Fe,start:ft,top:$e,variationPlacements:Mi,viewport:ui,write:Gt},Symbol.toStringTag,{value:"Module"})),_e="dropdown",ye=".bs.dropdown",it=".data-api",xt="ArrowUp",ne="ArrowDown",Zt=`hide${ye}`,se=`hidden${ye}`,we=`show${ye}`,Wn=`shown${ye}`,mi=`click${ye}${it}`,gt=`keydown${ye}${it}`,Bn=`keyup${ye}${it}`,Qe="show",Ke='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',kn=`${Ke}.${Qe}`,Be=".dropdown-menu",Ne=B()?"top-end":"top-start",Ce=B()?"top-start":"top-end",ze=B()?"bottom-end":"bottom-start",Rt=B()?"bottom-start":"bottom-end",Ve=B()?"left-start":"right-start",W=B()?"right-start":"left-start",Te={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Ee={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class re extends ct{constructor(t,s){super(t,s),this._popper=null,this._parent=this._element.parentNode,this._menu=R.next(this._element,Be)[0]||R.prev(this._element,Be)[0]||R.findOne(Be,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Te}static get DefaultType(){return Ee}static get NAME(){return _e}toggle(){return this._isShown()?this.hide():this.show()}show(){if(at(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!S.trigger(this._element,we,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const s of[].concat(...document.body.children))S.on(s,"mouseover",Mt);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Qe),this._element.classList.add(Qe),S.trigger(this._element,Wn,t)}}hide(){if(at(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!S.trigger(this._element,Zt,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const s of[].concat(...document.body.children))S.off(s,"mouseover",Mt);this._popper&&this._popper.destroy(),this._menu.classList.remove(Qe),this._element.classList.remove(Qe),this._element.setAttribute("aria-expanded","false"),lt.removeDataAttribute(this._menu,"popper"),S.trigger(this._element,se,t)}}_getConfig(t){if(typeof(t=super._getConfig(t)).reference=="object"&&!yt(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${_e.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(X===void 0)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;this._config.reference==="parent"?t=this._parent:yt(this._config.reference)?t=st(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);const s=this._getPopperConfig();this._popper=z(t,this._menu,s)}_isShown(){return this._menu.classList.contains(Qe)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return Ve;if(t.classList.contains("dropstart"))return W;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const s=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains("dropup")?s?Ce:Ne:s?Rt:ze}_detectNavbar(){return this._element.closest(".navbar")!==null}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(s=>Number.parseInt(s,10)):typeof t=="function"?s=>t(s,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(lt.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...q(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:s}){const d=R.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(m=>Ue(m));d.length&&ti(d,s,t===ne,!d.includes(s)).focus()}static jQueryInterface(t){return this.each(function(){const s=re.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0)throw new TypeError(`No method named "${t}"`);s[t]()}})}static clearMenus(t){if(t.button===2||t.type==="keyup"&&t.key!=="Tab")return;const s=R.find(kn);for(const d of s){const m=re.getInstance(d);if(!m||m._config.autoClose===!1)continue;const y=t.composedPath(),T=y.includes(m._menu);if(y.includes(m._element)||m._config.autoClose==="inside"&&!T||m._config.autoClose==="outside"&&T||m._menu.contains(t.target)&&(t.type==="keyup"&&t.key==="Tab"||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const k={relatedTarget:m._element};t.type==="click"&&(k.clickEvent=t),m._completeHide(k)}}static dataApiKeydownHandler(t){const s=/input|textarea/i.test(t.target.tagName),d=t.key==="Escape",m=[xt,ne].includes(t.key);if(!m&&!d||s&&!d)return;t.preventDefault();const y=this.matches(Ke)?this:R.prev(this,Ke)[0]||R.next(this,Ke)[0]||R.findOne(Ke,t.delegateTarget.parentNode),T=re.getOrCreateInstance(y);if(m)return t.stopPropagation(),T.show(),void T._selectMenuItem(t);T._isShown()&&(t.stopPropagation(),T.hide(),y.focus())}}S.on(document,gt,Ke,re.dataApiKeydownHandler),S.on(document,gt,Be,re.dataApiKeydownHandler),S.on(document,mi,re.clearMenus),S.on(document,Bn,re.clearMenus),S.on(document,mi,Ke,function(l){l.preventDefault(),re.getOrCreateInstance(this).toggle()}),Ie(re);const mt="backdrop",en="show",zn=`mousedown.bs.${mt}`,tn={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Yi={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Nt extends ln{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return tn}static get DefaultType(){return Yi}static get NAME(){return mt}show(t){if(!this._config.isVisible)return void q(t);this._append();const s=this._getElement();this._config.isAnimated&&on(s),s.classList.add(en),this._emulateAnimation(()=>{q(t)})}hide(t){this._config.isVisible?(this._getElement().classList.remove(en),this._emulateAnimation(()=>{this.dispose(),q(t)})):q(t)}dispose(){this._isAppended&&(S.off(this._element,zn),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=st(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),S.on(t,zn,()=>{q(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){nr(t,this._getElement(),this._config.isAnimated)}}const Vn=".bs.focustrap",Xn=`focusin${Vn}`,vi=`keydown.tab${Vn}`,Un="backward",Qi={autofocus:!0,trapElement:null},yi={autofocus:"boolean",trapElement:"element"};class bi extends ln{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Qi}static get DefaultType(){return yi}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),S.off(document,Vn),S.on(document,Xn,t=>this._handleFocusin(t)),S.on(document,vi,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,S.off(document,Vn))}_handleFocusin(t){const{trapElement:s}=this._config;if(t.target===document||t.target===s||s.contains(t.target))return;const d=R.focusableChildren(s);d.length===0?s.focus():this._lastTabNavDirection===Un?d[d.length-1].focus():d[0].focus()}_handleKeydown(t){t.key==="Tab"&&(this._lastTabNavDirection=t.shiftKey?Un:"forward")}}const _i=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",wi=".sticky-top",Yn="padding-right",f="margin-right";class g{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,Yn,s=>s+t),this._setElementAttributes(_i,Yn,s=>s+t),this._setElementAttributes(wi,f,s=>s-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,Yn),this._resetElementAttributes(_i,Yn),this._resetElementAttributes(wi,f)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,s,d){const m=this.getWidth();this._applyManipulationCallback(t,y=>{if(y!==this._element&&window.innerWidth>y.clientWidth+m)return;this._saveInitialAttribute(y,s);const T=window.getComputedStyle(y).getPropertyValue(s);y.style.setProperty(s,`${d(Number.parseFloat(T))}px`)})}_saveInitialAttribute(t,s){const d=t.style.getPropertyValue(s);d&&lt.setDataAttribute(t,s,d)}_resetElementAttributes(t,s){this._applyManipulationCallback(t,d=>{const m=lt.getDataAttribute(d,s);m!==null?(lt.removeDataAttribute(d,s),d.style.setProperty(s,m)):d.style.removeProperty(s)})}_applyManipulationCallback(t,s){if(yt(t))s(t);else for(const d of R.find(t,this._element))s(d)}}const b=".bs.modal",x=`hide${b}`,C=`hidePrevented${b}`,L=`hidden${b}`,j=`show${b}`,$=`shown${b}`,N=`resize${b}`,le=`click.dismiss${b}`,Y=`mousedown.dismiss${b}`,G=`keydown.dismiss${b}`,oe=`click${b}.data-api`,V="modal-open",Ae="show",Pe="modal-static",Me={backdrop:!0,focus:!0,keyboard:!0},Ge={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Oe extends ct{constructor(t,s){super(t,s),this._dialog=R.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new g,this._addEventListeners()}static get Default(){return Me}static get DefaultType(){return Ge}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||S.trigger(this._element,j,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(V),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){this._isShown&&!this._isTransitioning&&(S.trigger(this._element,x).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Ae),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated())))}dispose(){S.off(window,b),S.off(this._dialog,b),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Nt({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new bi({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const s=R.findOne(".modal-body",this._dialog);s&&(s.scrollTop=0),on(this._element),this._element.classList.add(Ae),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,S.trigger(this._element,$,{relatedTarget:t})},this._dialog,this._isAnimated())}_addEventListeners(){S.on(this._element,G,t=>{t.key==="Escape"&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),S.on(window,N,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),S.on(this._element,Y,t=>{S.one(this._element,le,s=>{this._element===t.target&&this._element===s.target&&(this._config.backdrop!=="static"?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(V),this._resetAdjustments(),this._scrollBar.reset(),S.trigger(this._element,L)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(S.trigger(this._element,C).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,s=this._element.style.overflowY;s==="hidden"||this._element.classList.contains(Pe)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(Pe),this._queueCallback(()=>{this._element.classList.remove(Pe),this._queueCallback(()=>{this._element.style.overflowY=s},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,s=this._scrollBar.getWidth(),d=s>0;if(d&&!t){const m=B()?"paddingLeft":"paddingRight";this._element.style[m]=`${s}px`}if(!d&&t){const m=B()?"paddingRight":"paddingLeft";this._element.style[m]=`${s}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,s){return this.each(function(){const d=Oe.getOrCreateInstance(this,t);if(typeof t=="string"){if(d[t]===void 0)throw new TypeError(`No method named "${t}"`);d[t](s)}})}}S.on(document,oe,'[data-bs-toggle="modal"]',function(l){const t=R.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&l.preventDefault(),S.one(t,j,d=>{d.defaultPrevented||S.one(t,L,()=>{Ue(this)&&this.focus()})});const s=R.findOne(".modal.show");s&&Oe.getInstance(s).hide(),Oe.getOrCreateInstance(t).toggle(this)}),Dn(Oe),Ie(Oe);const De=".bs.offcanvas",pe=".data-api",nn=`load${De}${pe}`,rt="show",Qn="showing",Ki="hiding",Gi=".offcanvas.show",qr=`show${De}`,Eo=`shown${De}`,Co=`hide${De}`,Gr=`hidePrevented${De}`,Jr=`hidden${De}`,Ao=`resize${De}`,ko=`click${De}${pe}`,So=`keydown.dismiss${De}`,Oo={backdrop:!0,keyboard:!0,scroll:!1},Do={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class rn extends ct{constructor(t,s){super(t,s),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Oo}static get DefaultType(){return Do}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||S.trigger(this._element,qr,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||new g().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Qn),this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(rt),this._element.classList.remove(Qn),S.trigger(this._element,Eo,{relatedTarget:t})},this._element,!0))}hide(){this._isShown&&(S.trigger(this._element,Co).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Ki),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove(rt,Ki),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new g().reset(),S.trigger(this._element,Jr)},this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=!!this._config.backdrop;return new Nt({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{this._config.backdrop!=="static"?this.hide():S.trigger(this._element,Gr)}:null})}_initializeFocusTrap(){return new bi({trapElement:this._element})}_addEventListeners(){S.on(this._element,So,t=>{t.key==="Escape"&&(this._config.keyboard?this.hide():S.trigger(this._element,Gr))})}static jQueryInterface(t){return this.each(function(){const s=rn.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);s[t](this)}})}}S.on(document,ko,'[data-bs-toggle="offcanvas"]',function(l){const t=R.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&l.preventDefault(),at(this))return;S.one(t,Jr,()=>{Ue(this)&&this.focus()});const s=R.findOne(Gi);s&&s!==t&&rn.getInstance(s).hide(),rn.getOrCreateInstance(t).toggle(this)}),S.on(window,nn,()=>{for(const l of R.find(Gi))rn.getOrCreateInstance(l).show()}),S.on(window,Ao,()=>{for(const l of R.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(l).position!=="fixed"&&rn.getOrCreateInstance(l).hide()}),Dn(rn),Ie(rn);const Zr={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Lo=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),jo=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,No=(l,t)=>{const s=l.nodeName.toLowerCase();return t.includes(s)?!Lo.has(s)||!!jo.test(l.nodeValue):t.filter(d=>d instanceof RegExp).some(d=>d.test(s))},Po={allowList:Zr,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Mo={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Io={entry:"(string|element|function|null)",selector:"(string|element)"};class $o extends ln{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Po}static get DefaultType(){return Mo}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[m,y]of Object.entries(this._config.content))this._setContent(t,y,m);const s=t.children[0],d=this._resolvePossibleFunction(this._config.extraClass);return d&&s.classList.add(...d.split(" ")),s}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[s,d]of Object.entries(t))super._typeCheckConfig({selector:s,entry:d},Io)}_setContent(t,s,d){const m=R.findOne(d,t);m&&((s=this._resolvePossibleFunction(s))?yt(s)?this._putElementInTemplate(st(s),m):this._config.html?m.innerHTML=this._maybeSanitize(s):m.textContent=s:m.remove())}_maybeSanitize(t){return this._config.sanitize?function(s,d,m){if(!s.length)return s;if(m&&typeof m=="function")return m(s);const y=new window.DOMParser().parseFromString(s,"text/html"),T=[].concat(...y.body.querySelectorAll("*"));for(const k of T){const O=k.nodeName.toLowerCase();if(!Object.keys(d).includes(O)){k.remove();continue}const P=[].concat(...k.attributes),M=[].concat(d["*"]||[],d[O]||[]);for(const D of P)No(D,M)||k.removeAttribute(D.nodeName)}return y.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return q(t,[this])}_putElementInTemplate(t,s){if(this._config.html)return s.innerHTML="",void s.append(t);s.textContent=t.textContent}}const Ho=new Set(["sanitize","allowList","sanitizeFn"]),Fr="fade",Er="show",eo=".modal",to="hide.bs.modal",Ji="hover",Rr="focus",qo={AUTO:"auto",TOP:"top",RIGHT:B()?"left":"right",BOTTOM:"bottom",LEFT:B()?"right":"left"},Fo={allowList:Zr,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Ro={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class Kn extends ct{constructor(t,s){if(X===void 0)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,s),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Fo}static get DefaultType(){return Ro}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),S.off(this._element.closest(eo),to,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=S.trigger(this._element,this.constructor.eventName("show")),s=(Ci(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!s)return;this._disposePopper();const d=this._getTipElement();this._element.setAttribute("aria-describedby",d.getAttribute("id"));const{container:m}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(m.append(d),S.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(d),d.classList.add(Er),"ontouchstart"in document.documentElement)for(const y of[].concat(...document.body.children))S.on(y,"mouseover",Mt);this._queueCallback(()=>{S.trigger(this._element,this.constructor.eventName("shown")),this._isHovered===!1&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!S.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(Er),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))S.off(t,"mouseover",Mt);this._activeTrigger.click=!1,this._activeTrigger[Rr]=!1,this._activeTrigger[Ji]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),S.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const s=this._getTemplateFactory(t).toHtml();if(!s)return null;s.classList.remove(Fr,Er),s.classList.add(`bs-${this.constructor.NAME}-auto`);const d=(m=>{do m+=Math.floor(1e6*Math.random());while(document.getElementById(m));return m})(this.constructor.NAME).toString();return s.setAttribute("id",d),this._isAnimated()&&s.classList.add(Fr),s}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new $o({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Fr)}_isShown(){return this.tip&&this.tip.classList.contains(Er)}_createPopper(t){const s=q(this._config.placement,[this,t,this._element]),d=qo[s.toUpperCase()];return z(this._element,t,this._getPopperConfig(d))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(s=>Number.parseInt(s,10)):typeof t=="function"?s=>t(s,this._element):t}_resolvePossibleFunction(t){return q(t,[this._element])}_getPopperConfig(t){const s={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:d=>{this._getTipElement().setAttribute("data-popper-placement",d.state.placement)}}]};return{...s,...q(this._config.popperConfig,[s])}}_setListeners(){const t=this._config.trigger.split(" ");for(const s of t)if(s==="click")S.on(this._element,this.constructor.eventName("click"),this._config.selector,d=>{this._initializeOnDelegatedTarget(d).toggle()});else if(s!=="manual"){const d=s===Ji?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),m=s===Ji?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");S.on(this._element,d,this._config.selector,y=>{const T=this._initializeOnDelegatedTarget(y);T._activeTrigger[y.type==="focusin"?Rr:Ji]=!0,T._enter()}),S.on(this._element,m,this._config.selector,y=>{const T=this._initializeOnDelegatedTarget(y);T._activeTrigger[y.type==="focusout"?Rr:Ji]=T._element.contains(y.relatedTarget),T._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},S.on(this._element.closest(eo),to,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,s){clearTimeout(this._timeout),this._timeout=setTimeout(t,s)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const s=lt.getDataAttributes(this._element);for(const d of Object.keys(s))Ho.has(d)&&delete s[d];return t={...s,...typeof t=="object"&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:st(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[s,d]of Object.entries(this._config))this.constructor.Default[s]!==d&&(t[s]=d);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const s=Kn.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0)throw new TypeError(`No method named "${t}"`);s[t]()}})}}Ie(Kn);const Wo={...Kn.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Bo={...Kn.DefaultType,content:"(null|string|element|function)"};class Cr extends Kn{static get Default(){return Wo}static get DefaultType(){return Bo}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const s=Cr.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0)throw new TypeError(`No method named "${t}"`);s[t]()}})}}Ie(Cr);const Wr=".bs.scrollspy",zo=`activate${Wr}`,no=`click${Wr}`,Vo=`load${Wr}.data-api`,xi="active",Br="[href]",io=".nav-link",Xo=`${io}, .nav-item > ${io}, .list-group-item`,Uo={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Yo={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Zi extends ct{constructor(t,s){super(t,s),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Uo}static get DefaultType(){return Yo}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=st(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(s=>Number.parseFloat(s))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(S.off(this._config.target,no),S.on(this._config.target,no,Br,t=>{const s=this._observableSections.get(t.target.hash);if(s){t.preventDefault();const d=this._rootElement||window,m=s.offsetTop-this._element.offsetTop;if(d.scrollTo)return void d.scrollTo({top:m,behavior:"smooth"});d.scrollTop=m}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(s=>this._observerCallback(s),t)}_observerCallback(t){const s=T=>this._targetLinks.get(`#${T.target.id}`),d=T=>{this._previousScrollData.visibleEntryTop=T.target.offsetTop,this._process(s(T))},m=(this._rootElement||document.documentElement).scrollTop,y=m>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=m;for(const T of t){if(!T.isIntersecting){this._activeTarget=null,this._clearActiveClass(s(T));continue}const k=T.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(y&&k){if(d(T),!m)return}else y||k||d(T)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=R.find(Br,this._config.target);for(const s of t){if(!s.hash||at(s))continue;const d=R.findOne(decodeURI(s.hash),this._element);Ue(d)&&(this._targetLinks.set(decodeURI(s.hash),s),this._observableSections.set(s.hash,d))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(xi),this._activateParents(t),S.trigger(this._element,zo,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))R.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(xi);else for(const s of R.parents(t,".nav, .list-group"))for(const d of R.prev(s,Xo))d.classList.add(xi)}_clearActiveClass(t){t.classList.remove(xi);const s=R.find(`${Br}.${xi}`,t);for(const d of s)d.classList.remove(xi)}static jQueryInterface(t){return this.each(function(){const s=Zi.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);s[t]()}})}}S.on(window,Vo,()=>{for(const l of R.find('[data-bs-spy="scroll"]'))Zi.getOrCreateInstance(l)}),Ie(Zi);const Gn=".bs.tab",Qo=`hide${Gn}`,Ko=`hidden${Gn}`,Go=`show${Gn}`,Jo=`shown${Gn}`,Zo=`click${Gn}`,es=`keydown${Gn}`,ts=`load${Gn}`,ns="ArrowLeft",ro="ArrowRight",is="ArrowUp",oo="ArrowDown",zr="Home",so="End",Jn="active",ao="fade",Vr="show",lo=".dropdown-toggle",Xr=`:not(${lo})`,co='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Ur=`.nav-link${Xr}, .list-group-item${Xr}, [role="tab"]${Xr}, ${co}`,rs=`.${Jn}[data-bs-toggle="tab"], .${Jn}[data-bs-toggle="pill"], .${Jn}[data-bs-toggle="list"]`;class Zn extends ct{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),S.on(this._element,es,s=>this._keydown(s)))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const s=this._getActiveElem(),d=s?S.trigger(s,Qo,{relatedTarget:t}):null;S.trigger(t,Go,{relatedTarget:s}).defaultPrevented||d&&d.defaultPrevented||(this._deactivate(s,t),this._activate(t,s))}_activate(t,s){t&&(t.classList.add(Jn),this._activate(R.getElementFromSelector(t)),this._queueCallback(()=>{t.getAttribute("role")==="tab"?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),S.trigger(t,Jo,{relatedTarget:s})):t.classList.add(Vr)},t,t.classList.contains(ao)))}_deactivate(t,s){t&&(t.classList.remove(Jn),t.blur(),this._deactivate(R.getElementFromSelector(t)),this._queueCallback(()=>{t.getAttribute("role")==="tab"?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),S.trigger(t,Ko,{relatedTarget:s})):t.classList.remove(Vr)},t,t.classList.contains(ao)))}_keydown(t){if(![ns,ro,is,oo,zr,so].includes(t.key))return;t.stopPropagation(),t.preventDefault();const s=this._getChildren().filter(m=>!at(m));let d;if([zr,so].includes(t.key))d=s[t.key===zr?0:s.length-1];else{const m=[ro,oo].includes(t.key);d=ti(s,t.target,m,!0)}d&&(d.focus({preventScroll:!0}),Zn.getOrCreateInstance(d).show())}_getChildren(){return R.find(Ur,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,s){this._setAttributeIfNotExists(t,"role","tablist");for(const d of s)this._setInitialAttributesOnChild(d)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const s=this._elemIsActive(t),d=this._getOuterElement(t);t.setAttribute("aria-selected",s),d!==t&&this._setAttributeIfNotExists(d,"role","presentation"),s||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const s=R.getElementFromSelector(t);s&&(this._setAttributeIfNotExists(s,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(s,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,s){const d=this._getOuterElement(t);if(!d.classList.contains("dropdown"))return;const m=(y,T)=>{const k=R.findOne(y,d);k&&k.classList.toggle(T,s)};m(lo,Jn),m(".dropdown-menu",Vr),d.setAttribute("aria-expanded",s)}_setAttributeIfNotExists(t,s,d){t.hasAttribute(s)||t.setAttribute(s,d)}_elemIsActive(t){return t.classList.contains(Jn)}_getInnerElement(t){return t.matches(Ur)?t:R.findOne(Ur,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each(function(){const s=Zn.getOrCreateInstance(this);if(typeof t=="string"){if(s[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);s[t]()}})}}S.on(document,Zo,co,function(l){["A","AREA"].includes(this.tagName)&&l.preventDefault(),at(this)||Zn.getOrCreateInstance(this).show()}),S.on(window,ts,()=>{for(const l of R.find(rs))Zn.getOrCreateInstance(l)}),Ie(Zn);const Sn=".bs.toast",os=`mouseover${Sn}`,ss=`mouseout${Sn}`,as=`focusin${Sn}`,ls=`focusout${Sn}`,cs=`hide${Sn}`,us=`hidden${Sn}`,ds=`show${Sn}`,fs=`shown${Sn}`,uo="hide",Ar="show",kr="showing",hs={animation:"boolean",autohide:"boolean",delay:"number"},ps={animation:!0,autohide:!0,delay:5e3};class er extends ct{constructor(t,s){super(t,s),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return ps}static get DefaultType(){return hs}static get NAME(){return"toast"}show(){S.trigger(this._element,ds).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(uo),on(this._element),this._element.classList.add(Ar,kr),this._queueCallback(()=>{this._element.classList.remove(kr),S.trigger(this._element,fs),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&(S.trigger(this._element,cs).defaultPrevented||(this._element.classList.add(kr),this._queueCallback(()=>{this._element.classList.add(uo),this._element.classList.remove(kr,Ar),S.trigger(this._element,us)},this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Ar),super.dispose()}isShown(){return this._element.classList.contains(Ar)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,s){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=s;break;case"focusin":case"focusout":this._hasKeyboardInteraction=s}if(s)return void this._clearTimeout();const d=t.relatedTarget;this._element===d||this._element.contains(d)||this._maybeScheduleHide()}_setListeners(){S.on(this._element,os,t=>this._onInteraction(t,!0)),S.on(this._element,ss,t=>this._onInteraction(t,!1)),S.on(this._element,as,t=>this._onInteraction(t,!0)),S.on(this._element,ls,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const s=er.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0)throw new TypeError(`No method named "${t}"`);s[t](this)}})}}return Dn(er),Ie(er),{Alert:un,Button:zt,Carousel:pn,Collapse:dt,Dropdown:re,Modal:Oe,Offcanvas:rn,Popover:Cr,ScrollSpy:Zi,Tab:Zn,Toast:er,Tooltip:Kn}});
