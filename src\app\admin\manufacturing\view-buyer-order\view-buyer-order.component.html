 
 <div class="container mt-4">

   <section >
      <fieldset>
          <legend><b>View Buyer Order</b></legend>

    <div class="row">
        <div class="col-md-12">

                     <mat-form-field>
                        <mat-label>Filter</mat-label>
                        <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Mia" #input>
                      </mat-form-field>

                      <div class="mat-elevation-z8 table-responsive scroll-container">
                        <table mat-table [dataSource]="dataSource" matSort style="width: max-content;">

                          <!-- ID Column -->
                          <ng-container matColumnDef="SrNo">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Sr.No </th>
                            <td mat-cell *matCellDef="let row "> {{row.index}} </td>
                          </ng-container>

                          <!-- Progress Column -->
                          <ng-container matColumnDef="buyerName">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> buyerName </th>
                            <td mat-cell *matCellDef="let row"> {{row.buyerName?.customerName}}</td>
                          </ng-container>

                          <!-- Name Column -->
                          <ng-container matColumnDef="orderNo">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> orderNo </th>
                            <td mat-cell *matCellDef="let row"><a (click)="editBuyer(row.id)">{{row.orderNo}}</a>  </td>
                          </ng-container>

                          <!-- Fruit Column -->
                          <ng-container matColumnDef="companyOrderNo">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> companyOrderNo </th>
                            <td mat-cell *matCellDef="let row"> {{row.companyOrderNo}} </td>
                          </ng-container>

                           <!-- Fruit Column -->
                           <ng-container matColumnDef="orderDate">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> orderDate </th>
                            <td mat-cell *matCellDef="let row"> {{row.orderDate}} </td>
                          </ng-container>

                           <!-- Fruit Column -->
                           <ng-container matColumnDef="shippingDate">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> shippingDate </th>
                            <td mat-cell *matCellDef="let row"> {{row.shippingDate}} </td>
                          </ng-container>

                           <!-- Fruit Column -->
                           <ng-container matColumnDef="orderType">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> orderType </th>
                            <td mat-cell *matCellDef="let row"> {{row.orderType}} </td>
                          </ng-container>

                           <!-- Fruit Column -->
                           <ng-container matColumnDef="customerOrder">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> customerOrder </th>
                            <td mat-cell *matCellDef="let row"> {{row.customerOrder}} </td>
                          </ng-container>

                           <!-- Fruit Column -->
                           <!-- <ng-container matColumnDef="weavingUnit">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> weavingUnit </th>
                            <td mat-cell *matCellDef="let row"> {{row.weavingUnit}} </td>
                          </ng-container> -->

                           <!-- Fruit Column -->
                           <ng-container matColumnDef="priority">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> priority </th>
                            <td mat-cell *matCellDef="let row"> {{row.priority}} </td>
                          </ng-container>

                           <!-- Fruit Column -->
                           <ng-container matColumnDef="area">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> area </th>
                            <td mat-cell *matCellDef="let row"> {{row.area}} </td>
                          </ng-container>
                           <ng-container matColumnDef="orderedItem">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Ordered Item </th>
                            <td mat-cell *matCellDef="let row"> {{row.orderedItem}} </td>
                          </ng-container>

                          <ng-container matColumnDef="action">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                            <td mat-cell *matCellDef="let row">
                               &nbsp;
                              <a (click)="editOrderList(row.id)"><i class="fa fa-list" aria-hidden="true"></i></a>
                              &nbsp;&nbsp;&nbsp;
                              <a (click)="editBuyer(row.id)"><i class="fa-sharp fa-solid fa-pen-to-square" aria-hidden="true"></i></a>
                              &nbsp;&nbsp;&nbsp;
                              <a  (click)="delete(row.id)"><i class="fa-solid fa-trash"></i></a>
                               &nbsp;
                            </td>
                          </ng-container>

                          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                          <!-- Row shown when there is no matching data. -->
                          <tr class="mat-row" *matNoDataRow>
                            <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>
                          </tr>
                        </table>
    </div>
                        <mat-paginator matSort  [pageSizeOptions]="[ 50, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>
                     



        </div>
    </div>
    </fieldset>
  </section>
 </div>
