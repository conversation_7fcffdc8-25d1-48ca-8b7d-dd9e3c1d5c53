const mongoose = require('mongoose');

const sizeMasterSchema = new mongoose.Schema({
  sizeInYard: {
    type: String,
    required: true,
  },
  areaInYard: {
    type: String,
    // required: true
  },
  areaInfeet: {
    type: String,
    
  },
  khapSize: {
    type: String,
  },
  sizeinMeter: {
    type: String,
    
  },
  sqMeter: {
    type: String,
    
  },
  srNo:{
    type: Number
  },
  uploadedFile: {
    type: String, // or you can use Buffer for binary data, but String is typical for file paths/URLs
    //required: true
},
});

const Size = mongoose.model('SizeMaster', sizeMasterSchema);

module.exports = Size;
