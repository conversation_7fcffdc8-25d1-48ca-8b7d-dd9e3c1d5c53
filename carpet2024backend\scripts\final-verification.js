// Final verification of restored data
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function main() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get total count
    const totalCount = await collection.countDocuments();
    console.log(`📊 Total carpet received records: ${totalCount}`);
    
    // Get sample records
    const samples = await collection.find().limit(15).toArray();
    
    console.log('\n📋 Sample records with original data:');
    samples.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver: ${record.weaverName || 'N/A'}`);
      console.log(`   Quality: ${record.quality || 'N/A'}`);
      console.log(`   Design: ${record.design || 'N/A'}`);
      console.log(`   Size: ${record.size || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ${record.amount || 'N/A'}`);
      console.log(`   Date: ${record.receivingDate?.toISOString()?.split('T')[0] || 'N/A'}`);
      console.log('   ' + '-'.repeat(60));
    });
    
    // Check distinct values
    const distinctWeavers = await collection.distinct('weaverName');
    console.log(`\n👥 All Weaver Names (${distinctWeavers.length}):`);
    distinctWeavers.forEach((weaver, index) => {
      console.log(`   ${index + 1}. ${weaver}`);
    });
    
    const distinctDesigns = await collection.distinct('design');
    console.log(`\n🎨 All Designs (${distinctDesigns.length}): ${distinctDesigns.filter(d => d).join(', ') || 'None found'}`);
    
    const distinctQualities = await collection.distinct('quality');
    console.log(`\n⭐ All Qualities (${distinctQualities.length}): ${distinctQualities.filter(q => q).join(', ') || 'None found'}`);
    
    // Check for RCV- records
    const rcvCount = await collection.countDocuments({
      receiveNo: { $regex: /^RCV-/i }
    });
    
    console.log(`\n🔍 Records with RCV- prefix: ${rcvCount} (should be 0)`);
    
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 FINAL VERIFICATION COMPLETE!');
    console.log('✅ 53 records with original data restored');
    console.log('✅ Original weaver names: Rahila, Jeet Narayan, K - Yaseen, etc.');
    console.log('✅ Proper designs: MIR, Isfahan, Sonam, Bhaktiri, BIDJAR, etc.');
    console.log('✅ Clean K-format receiveNos (no RCV-)');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

main();
