const carpetReceivedUpdatesRepository = require("../../repositories/manifacturing/carpetReceivedUpdates-repository");

const createCarpetReceivedUpdate = async (data) => {
  return await carpetReceivedUpdatesRepository.createUpdate(data);
};

const getUpdatesByCarpetReceivedId = async (carpetReceivedId) => {
  return await carpetReceivedUpdatesRepository.findByCarpetReceivedId(carpetReceivedId);
};

const getHistoryByCarpetAndType = async (carpetReceivedId, updateType) => {
  return await carpetReceivedUpdatesRepository.getHistoryByCarpetAndType(carpetReceivedId, updateType);
};

const getAllCarpetReceivedUpdates = async () => {
  return await carpetReceivedUpdatesRepository.getAllCarpetReceivedUpdates();
};

const addCarpetBorderSizeHistory = async (carpetReceivedId, borderData) => {
  return await carpetReceivedUpdatesRepository.addCarpetBorderSizeHistory(carpetReceivedId, borderData);
};

const getCarpetBorderSizeHistory = async (carpetReceivedId) => {
  return await carpetReceivedUpdatesRepository.getCarpetBorderSizeHistory(carpetReceivedId);
};

module.exports = {
  createCarpetReceivedUpdate,
  getUpdatesByCarpetReceivedId,
  getHistoryByCarpetAndType,
  getAllCarpetReceivedUpdates,
  addCarpetBorderSizeHistory,
  getCarpetBorderSizeHistory
};