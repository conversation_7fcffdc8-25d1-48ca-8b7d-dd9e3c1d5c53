export class AreaCalc {
  constructor(){}
  totalSizeInYaard: number=0;
  setSizeYard(size: any):any {
    const formData = size;
debugger
    const calcData = formData.split(/[Xx]/);

    const width1 = calcData[0].split('.')[0];
    const width2 = calcData[0].split('.')[1];

    console.log('before ', width1, 'after ', width2);

    console.log(calcData);

    const length1 = calcData[1].split('.')[0];
    const length2 = calcData[1].split('.')[1];

    const sizeInYaardCalc1 = parseInt(width1) * 12 + parseInt(width2);

    const sizeInYaardCalc2 = parseInt(length1) * 12 + parseInt(length2);
    console.log(sizeInYaardCalc1);
    console.log(sizeInYaardCalc2);

    this.totalSizeInYaard = (sizeInYaardCalc1 * sizeInYaardCalc2) / 1296;


let pcs = 1;

  let totalVal = pcs * this.totalSizeInYaard;

    return totalVal
  }
}
