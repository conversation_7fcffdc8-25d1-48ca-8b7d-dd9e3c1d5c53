<h2 mat-dialog-title class="title mt-2">Assigned Pieces Details</h2>
<mat-dialog-content>
  <ng-container *ngIf="issueDetails.length > 0; else noData">
    <table mat-table [dataSource]="issueDetails" class="mat-elevation-z8">
      <!-- Weaver Column -->
      <ng-container matColumnDef="weaver">
        <th mat-header-cell *matHeaderCellDef>Weaver</th>
        <td mat-cell *matCellDef="let detail">{{detail.branch?.branchCode}}-{{detail.weaver?.name || detail.weaver?.weaverName || detail.weaver}}</td>
      </ng-container>
      <!-- Issue No. Column -->
      <ng-container matColumnDef="issueNo">
        <th mat-header-cell *matHeaderCellDef>Issue No.</th>
        <td mat-cell *matCellDef="let detail">{{detail.Br_issueNo }}</td>
      </ng-container>
      <!-- Date Column -->
      <ng-container matColumnDef="date">
        <th mat-header-cell *matHeaderCellDef>Date</th>
        <td mat-cell *matCellDef="let detail">{{detail.date | date:'dd-MM-yyyy'}}</td>
      </ng-container>
      <!-- Quality Column -->
      <ng-container matColumnDef="quality">
        <th mat-header-cell *matHeaderCellDef>Quality</th>
        <td mat-cell *matCellDef="let detail">{{detail.quality?.quality || detail.quality}}</td>
      </ng-container>
      <!-- Design Column -->
      <ng-container matColumnDef="design">
        <th mat-header-cell *matHeaderCellDef>Design</th>
        <td mat-cell *matCellDef="let detail">{{detail.design?.design || detail.design}}</td>
      </ng-container>
      <!-- Colour Column -->
      <ng-container matColumnDef="colour">
        <th mat-header-cell *matHeaderCellDef>Colour</th>
        <td mat-cell *matCellDef="let detail">{{detail.borderColour || detail.colour}}</td>
      </ng-container>
      <!-- Size Column -->
      <ng-container matColumnDef="size">
        <th mat-header-cell *matHeaderCellDef>Size</th>
        <td mat-cell *matCellDef="let detail">{{detail.size?.sizeInYard || detail.size || detail.khapSize}}</td>
      </ng-container>
      <!-- Pcs Column -->
      <ng-container matColumnDef="pcs">
        <th mat-header-cell *matHeaderCellDef>Pcs</th>
        <td mat-cell *matCellDef="let detail">{{detail.pcs}}</td>
      </ng-container>
      <!-- Area Column -->
      <ng-container matColumnDef="area">
        <th mat-header-cell *matHeaderCellDef>Area</th>
        <td mat-cell *matCellDef="let detail">{{detail.area}}</td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="['weaver','issueNo','date','quality','design','colour','size','pcs','area']"></tr>
      <tr mat-row *matRowDef="let row; columns: ['weaver','issueNo','date','quality','design','colour','size','pcs','area'];"></tr>
    </table>
  </ng-container>
  <ng-template #noData>
    <p>No detailed assignment data available.</p>
  </ng-template>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button mat-dialog-close>Close</button>
</mat-dialog-actions>
