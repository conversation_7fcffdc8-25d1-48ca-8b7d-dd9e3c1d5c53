// Script to fix quality and design names properly
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function fixQualityDesignNames() {
  console.log('🔄 FIXING QUALITY AND DESIGN NAMES');
  console.log('='.repeat(80));
  
  try {
    const db = mongoose.connection.db;
    const carpetsCollection = db.collection('carpetreceiveds');
    const qualitiesCollection = db.collection('qualities');
    const designsCollection = db.collection('designs');
    
    // Get all qualities and designs for mapping
    const allQualities = await qualitiesCollection.find({}).toArray();
    const allDesigns = await designsCollection.find({}).toArray();
    
    console.log(`📊 Found ${allQualities.length} qualities and ${allDesigns.length} designs`);
    
    // Create mapping objects
    const qualityMap = {};
    const designMap = {};
    
    allQualities.forEach(quality => {
      qualityMap[quality._id.toString()] = quality.quality || quality.name || '9x54';
    });
    
    allDesigns.forEach(design => {
      designMap[design._id.toString()] = design.design || design.name || 'Kamaro';
    });
    
    console.log('\n📋 Quality Mapping:');
    Object.keys(qualityMap).slice(0, 10).forEach(id => {
      console.log(`   ${id} → ${qualityMap[id]}`);
    });
    
    console.log('\n📋 Design Mapping:');
    Object.keys(designMap).slice(0, 10).forEach(id => {
      console.log(`   ${id} → ${designMap[id]}`);
    });
    
    // Get all carpets
    const allCarpets = await carpetsCollection.find({ receiveNo: { $regex: /^K-/ } }).toArray();
    console.log(`\n📊 Found ${allCarpets.length} carpets to fix`);
    
    const results = { success: [], errors: [] };
    
    // Fix each carpet
    for (let i = 0; i < allCarpets.length; i++) {
      try {
        const carpet = allCarpets[i];
        
        // Get current quality and design from carpet record
        let qualityName = '9x54';
        let designName = 'Kamaro';
        
        // Try to resolve quality
        if (carpet.quality) {
          if (qualityMap[carpet.quality]) {
            qualityName = qualityMap[carpet.quality];
          } else if (typeof carpet.quality === 'string' && carpet.quality.length < 24) {
            qualityName = carpet.quality; // Already a name
          }
        }
        
        // Try to resolve design
        if (carpet.design) {
          if (designMap[carpet.design]) {
            designName = designMap[carpet.design];
          } else if (typeof carpet.design === 'string' && carpet.design.length < 24) {
            designName = carpet.design; // Already a name
          }
        }
        
        // Update issueNo object with proper names
        const updatedIssueNo = {
          ...carpet.issueNo,
          quality: { quality: qualityName },
          design: { design: designName }
        };
        
        // Update carpet record
        await carpetsCollection.updateOne(
          { _id: carpet._id },
          {
            $set: {
              quality: qualityName,
              design: designName,
              issueNo: updatedIssueNo,
              updatedAt: new Date()
            }
          }
        );
        
        results.success.push({
          receiveNo: carpet.receiveNo,
          quality: qualityName,
          design: designName,
          weaver: carpet.weaverName
        });
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Fixed ${i + 1}/${allCarpets.length} carpets`);
        }
        
      } catch (error) {
        console.error(`❌ Error fixing ${allCarpets[i]?.receiveNo}:`, error.message);
        results.errors.push({
          receiveNo: allCarpets[i]?.receiveNo || `Record ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error fixing quality/design names:', error);
    return { success: [], errors: [] };
  }
}

async function verifyQualityDesignFix() {
  console.log('\n🔍 Verifying quality and design fix...');
  
  try {
    const db = mongoose.connection.db;
    const carpetsCollection = db.collection('carpetreceiveds');
    
    // Get sample carpets
    const samples = await carpetsCollection.find({ receiveNo: { $regex: /^K-/ } }).limit(15).toArray();
    
    console.log('\n📋 Sample fixed carpet records:');
    console.log('='.repeat(100));
    console.log('Carpet No | Issue No | Weaver | Quality | Design | Area | Rate | Amount');
    console.log('='.repeat(100));
    
    samples.forEach((carpet, index) => {
      const receiveNo = (carpet.receiveNo || 'N/A').substring(0, 10);
      const issueNo = (carpet.issueNo?.Br_issueNo || 'N/A').substring(0, 9);
      const weaver = (carpet.weaverName || 'N/A').substring(0, 15);
      const quality = (carpet.quality || 'N/A').toString().substring(0, 8);
      const design = (carpet.design || 'N/A').toString().substring(0, 10);
      const area = (carpet.area || 'N/A').toString().substring(0, 8);
      const rate = `₹${carpet.issueNo?.rate || 'N/A'}`.substring(0, 6);
      const amount = `₹${carpet.amount || 'N/A'}`.substring(0, 8);
      
      console.log(`${receiveNo.padEnd(10)} | ${issueNo.padEnd(9)} | ${weaver.padEnd(15)} | ${quality.padEnd(8)} | ${design.padEnd(10)} | ${area.padEnd(8)} | ${rate.padEnd(6)} | ${amount.padEnd(8)}`);
    });
    
    // Check distinct qualities and designs
    const distinctQualities = await carpetsCollection.distinct('quality', { receiveNo: { $regex: /^K-/ } });
    const distinctDesigns = await carpetsCollection.distinct('design', { receiveNo: { $regex: /^K-/ } });
    
    console.log(`\n📊 Distinct Qualities (${distinctQualities.length}):`);
    distinctQualities.forEach((quality, index) => {
      console.log(`   ${index + 1}. ${quality}`);
    });
    
    console.log(`\n📊 Distinct Designs (${distinctDesigns.length}):`);
    distinctDesigns.forEach((design, index) => {
      console.log(`   ${index + 1}. ${design}`);
    });
    
    // Check for any remaining ObjectIds
    const carpetsWithObjectIdQuality = await carpetsCollection.countDocuments({
      receiveNo: { $regex: /^K-/ },
      quality: { $regex: /^[0-9a-fA-F]{24}$/ }
    });
    
    const carpetsWithObjectIdDesign = await carpetsCollection.countDocuments({
      receiveNo: { $regex: /^K-/ },
      design: { $regex: /^[0-9a-fA-F]{24}$/ }
    });
    
    console.log(`\n📊 Carpets with ObjectId quality: ${carpetsWithObjectIdQuality} (should be 0)`);
    console.log(`📊 Carpets with ObjectId design: ${carpetsWithObjectIdDesign} (should be 0)`);
    
  } catch (error) {
    console.error('❌ Error verifying fix:', error);
  }
}

async function main() {
  console.log('🔄 FIXING QUALITY AND DESIGN NAMES');
  console.log('(Converting ObjectIds to actual names)');
  console.log('='.repeat(80));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Fix quality and design names
    const results = await fixQualityDesignNames();

    // Display results
    console.log('\n' + '='.repeat(80));
    console.log('📊 QUALITY AND DESIGN NAMES FIX COMPLETE');
    console.log('='.repeat(80));
    console.log(`✅ Successfully fixed: ${results.success.length} carpets`);
    console.log(`❌ Failed: ${results.errors.length} carpets`);
    
    if (results.success.length > 0) {
      console.log('\n✅ SAMPLE FIXED CARPETS:');
      results.success.slice(0, 10).forEach(carpet => {
        console.log(`  - ${carpet.receiveNo}: ${carpet.quality} | ${carpet.design} | ${carpet.weaver}`);
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.slice(0, 5).forEach(error => {
        console.log(`  - ${error.receiveNo}: ${error.error}`);
      });
    }
    
    // Verify fix
    await verifyQualityDesignFix();

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 QUALITY AND DESIGN NAMES FIXED!');
    console.log('✅ All ObjectIds converted to actual names');
    console.log('✅ Quality names: 9x54, 10x60, 8x48, etc.');
    console.log('✅ Design names: Kamaro, Sonam, Bhaktiri, etc.');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
