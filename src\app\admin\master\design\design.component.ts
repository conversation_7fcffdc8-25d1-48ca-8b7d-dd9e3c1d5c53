import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import Swal from 'sweetalert2';
import { environment } from '../../../../environments/environment.development';
import { DomSanitizer } from '@angular/platform-browser';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { error } from 'node:console';
import { NgxUiLoaderService } from 'ngx-ui-loader';
declare var $: any;
@Component({
  selector: 'app-design',
  templateUrl: './design.component.html',
  styleUrl: './design.component.css',
})
export class DesignComponent implements OnInit {
  designform!: FormGroup;

  editId: any;
  isEditMode = false;
  qualityList: any = [];
  isOpenDialog: boolean = true;
  dataSource!: MatTableDataSource<any>;
  displayedColumns!: string[];
  groundList: any = [];
  borderList: any = [];
  constructor(
    public _fb: FormBuilder,
    public masterservice: MasterService,
    private sanitizer: DomSanitizer,
    private ngxLoader: NgxUiLoaderService,
    @Optional() public dialogRef: MatDialogRef<DesignComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  createColorGroup(): FormGroup {
    return this._fb.group({
      colorName: ['', [Validators.required]],
      lagat: ['', [Validators.required]],
    });
  }

  get colors(): FormArray {
    return this.designform.get('colors') as FormArray;
  }

  addColor(): void {
    this.colors.push(this.createColorGroup());
  }

  removeColor(index: number): void {
    this.colors.removeAt(index);
    this.calcLagat();
  }
  totalLagat: any;
  calcLagat() {
    this.totalLagat = 0;
    this.colors.value.forEach((element: any, i: number) => {
      this.totalLagat = parseFloat(this.totalLagat) + parseFloat(element.lagat);
      this.colors
        .at(i)
        .get('lagat')
        ?.setValue(parseFloat(element.lagat).toFixed(3));
    });
  }
  handleCalc(i: number) {
    let a = this.colors.value[i].lagat;
    if (isNaN(a)) {
      Swal.fire({
        title: 'Warning!',
        text: `Lagat value is not a number. Please enter a valid number.`,
        icon: 'warning',
      });
    } else {
      this.calcLagat();
    }
  }
  setDisplayedColumns(): void {
    this.displayedColumns = this.isOpenDialog
      ? [
          'id',
          'productQuality',
          'design',
          'ground',
          'border',
          'noOfcolour',
          'file',
          'action',
        ]
      : ['design', 'ground', 'border'];
  }
  files!: File;
  productQuality: any = ['', ''];
  file(file: any) {
    this.files = file.target.files[0];
  }
  designFormSubmit() {
    if (this.designform.valid) {
      let frmData = this.designform.value;
      let formData = new FormData();
      formData.append('design', frmData.design);
      formData.append('productQuality', frmData.productQuality);
      formData.append('ground', frmData.ground);
      formData.append('border', frmData.border);
      formData.append('uploadedFile', this.files);
      formData.append('noOfcolour', frmData.noOfcolour);
      let colorLagats = this.colors.controls.map((colorGroup, index) => {
        return {
          colour: colorGroup.get('colorName')?.value,
          lagat: colorGroup.get('lagat')?.value,
        };
      });

      // Stringify the array and append it
      formData.append('colourLagats', JSON.stringify(colorLagats));
      if (this.isEditMode && this.editId) {
        // console.log(this.editId);
        let frmData = this.designform.value;
        this.masterservice.updateDesign(this.editId, formData).subscribe({
          next: () => {
            Swal.fire({
              title: 'Success',
              text: 'Successfully Updated!',
              icon: 'success',
            });
            this.getAllDesignList();
            this.resetForm();
          },
          error: () => {
            Swal.fire({
              icon: 'error',
              title: 'Failed ! ',
              text: 'Something went wrong!',
            });
          },
        });
      } else {
        this.masterservice.addDesign(formData).subscribe({
          next: () => {
            Swal.fire({
              title: 'Success',
              text: 'Successfully Added!!',
              icon: 'success',
            });
            this.getAllDesignList();
            this.resetForm();
          },
          error: () => {
            Swal.fire({
              title: 'Failed !',
              text: 'Something went wrong ?',
              icon: 'error',
            });
          },
        });
      }
    }
  }
  resetForm() {
    this.designform.reset();
    this.isEditMode = false;
  }
  editDesign(data: any) {
    this.isEditMode = true;
    this.editId = data._id;
    this.masterservice.getDesign(data._id).subscribe({
      next: (val: any) => {
        this.selectedQuality(val.productQuality);

        this.designform.patchValue({
          productQuality: val.productQuality,
          design: val.design,
          ground: val.ground,
          border: val.border,
          noOfcolour: val.noOfcolour,
          uploadedFile: val.uploadedFile,
        });

        // Clear the current colors FormArray
        this.colors.clear();

        // Populate the colors FormArray with the new values
        val.colourLagats.forEach((element: any) => {
          this.colors.push(
            this._fb.group({
              colorName: element.colour,
              lagat: element.lagat,
            })
          );
        });
        this.calcLagat();
      },
    });

    // this.designform.patchValue(data);
  }

  getSerialNumber(index: number): number {
    if (!this.paginator) {
      return index + 1;
    }
    return index + 1 + this.paginator.pageIndex * this.paginator.pageSize;
  }
  allDesign: any = [];
  sortedData: any;
  getAllDesignList() {
    this.masterservice.getAllDesignList().subscribe({
      next: (res) => {
        this.allDesign = res;
        let arr: any = [];
        res.map((v: any, i: number) => {
          const imageUrl = environment.imageUrl + v.uploadedFile;
          const sanitizedUrl = this.sanitizer.bypassSecurityTrustUrl(imageUrl);

          arr.push({
            index: i + 1,
            _id: v._id,
            productQuality: v.productQuality?.quality,
            design: v.design,
            ground: v.ground?.newColor,
            groundId: v.ground?._id,
            borderId: v.border?._id,
            border: v.border?.newColor,
            noOfcolour: v.noOfcolour,
            uploadedFile: environment.imageUrl + v.uploadedFile,
          });
        });

        const extractQuality = (quality: string) => {
          const match = quality.match(/\d+/g);
          return match ? parseInt(match[0], 10) : 0;
        };

        // Sorting function
        const sortByQualityDesignGroundColour = (a: any, b: any) => {
          const qualityA = extractQuality(a.productQuality);
          const qualityB = extractQuality(b.productQuality);

          if (qualityA !== qualityB) {
            return qualityA - qualityB;
          }

          if (a.design !== b.design) {
            return a.design.localeCompare(b.design);
          }

          if (a.ground !== b.ground) {
            return a.ground.localeCompare(b.ground);
          }

          return a.noOfcolour - b.noOfcolour;
        };

        // Sort the data
        this.sortedData = arr.sort(sortByQualityDesignGroundColour);
        
        this.dataSource = new MatTableDataSource(this.sortedData);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.ngxLoader.stopAll();
      },

      error: console.log,
    });
  }

  deleteConformation(id: any) {
    Swal.fire({
      icon: 'warning',
      text: 'Do you want to Delete this Data ?',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        this.deleteDesign(id);
      } else if (result.isDenied) {
        Swal.fire('Changes are not save', '', 'info');
      }
    });
  }
  deleteDesign(id: any) {
    this.masterservice.deleteDesign(id).subscribe({
      next: () => {
        Swal.fire('Deleted !', 'Your data has been deleted.', 'success');
        this.getAllDesignList();
      },
      error: () => {
        // alert('Error !');
        Swal.fire({
          title: 'Failed !',
          text: 'Something went wrong ?',
          icon: 'error',
        });
      },
    });
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngOnInit(): void {
    
    this.designform = this._fb.group({
      productQuality: ['', [Validators.required]],
      design: ['', [Validators.required]],
      ground: ['', [Validators.required]],
      border: ['', [Validators.required]],
      noOfcolour: ['', [Validators.required]],
      uploadedFile: [''],
      colors: this._fb.array([]),
    });

    this.getAllDesignList();
    this.quality();
    this.dialog();
    // this.dataSource = new MatTableDataSource<any>([]);
    this.setDisplayedColumns();
    // this.colorList();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  quality() {
    this.masterservice.getqualityList().subscribe((resp: any) => {
      // console.log(resp);

      resp.map((v: any) => {
        this.qualityList.push({
          id: v._id,
          quality: v.quality,
        });
      });

      this.qualityList.sort((a: any, b: any) => {
        let numA = this.extractFirstNumber(a.quality);
        let numB = this.extractFirstNumber(b.quality);
        return numA - numB;
      });
    });
  }
  extractFirstNumber(str: any) {
    let parts = str.split('x').map((part: any) => parseInt(part.trim(), 10));
    return parts[0];
  }

  readPdf(imageUrl: any) {
    let x = imageUrl.split('.');
    if (x.length > 1) {
      window.open(imageUrl, '_blank');
    } else {
      Swal.fire({
        title: 'Warning!',
        text: 'File does not exist!',
        icon: 'warning',
      });
    }
  }
  inputItem!: string;

  dialog() {
     
    this.ngxLoader.start();
    if (this.dialogRef) {
      
      this.isOpenDialog = false;
      setTimeout(() => {
         
        this.inputItem = this.dialogRef.componentInstance.data.quality.quality;
            let data =  this.dataSource.filteredData.filter((x:any)=>x.productQuality === this.inputItem)
        this.dataSource = new MatTableDataSource(data);
        console.log('this ', this.sortedData);
        console.log(this.dialogRef);
        this.ngxLoader.stopAll();
      }, 1000);
    }
  }

  selectedRow(row: any) {
    console.log('selectedRow', row);
    if (this.dialogRef) {
      this.dialogRef.close(row);
    }
  }

  selectedQuality(id: string) {
    this.masterservice.getsColourCodeDetails().subscribe({
      next: (value: any) => {
        this.colorList(id);
        this.groundList = value.filter((x: any) => x.quality?._id === id);
        this.borderList = value.filter((x: any) => x.quality?._id === id);

        const uniqueColors = new Set();
        this.groundList = this.groundList.filter((item: any) => {
          const newColorLower = item.newColor.toLowerCase();
          if (uniqueColors.has(newColorLower)) {
            return false;
          } else {
            uniqueColors.add(newColorLower);
            return true;
          }
        });

        this.groundList.sort((a: any, b: any) =>
          a.newColor.toLowerCase().localeCompare(b.newColor.toLowerCase())
        );

        this.borderList = this.borderList.filter((item: any) => {
          const newColorLower = item.newColor.toLowerCase();
          if (uniqueColors.has(newColorLower)) {
            return false;
          } else {
            uniqueColors.add(newColorLower);
            return true;
          }
        });
      },
    });
  }

  //   selectedQuality(id: string) {
  //     this.masterservice.getsColourCodeDetails().subscribe({
  //       next: (value: any) => {
  //         this.colorList(id);
  //         this.groundList = value.filter((x: any) =>
  //           x.quality?._id === id ? x.newColor : ''
  //         );
  //         this.borderList = value.filter((x: any) =>
  //           x.quality?._id === id ? x.newColor : ''
  //         );
  //
  //         this.groundList = this.groundList
  //           .filter((x: any) => x.quality?._id === id)
  //           .map((x: any) => x.newColor);

  //         this.borderList = this.borderList
  //           .filter((x: any) => x.quality?._id === id)
  //           .map((x: any) => x.newColor);

  //           this.groundList = [...new Set(this.groundList.map((color:any) => color.toLowerCase()))];
  //           this.borderList = [...new Set(this.borderList.map((color:any) => color.toLowerCase()))];

  //           this.groundList = this.groundList.sort((a: any, b: any) => a.localeCompare(b));
  // this.borderList = this.borderList.sort((a: any, b: any) => a.localeCompare(b));
  //       },
  //     });
  //   }
  handleBlurEvent(e: any) {
    let a = parseInt(e.target.value);
    for (let x = 1; x <= a; x++) {
      this.addColor();
    }
  }
  colorsList: any = [];
  colorList(qid: string) {
    this.masterservice.getsColourCodeDetails().subscribe({
      next: (value) => {
        this.colorsList.length = 0;
        this.colorsList = value;

        this.colorsList = this.colorsList.filter(
          (x: any) => x.quality._id === qid
        );
        const sortByNewColorCompanyColorCodeRemark = (a: any, b: any) => {
          if (a.newColor.toLowerCase() !== b.newColor.toLowerCase()) {
            return a.newColor
              .toLowerCase()
              .localeCompare(b.newColor.toLowerCase());
          }

          if (
            a.companyColorCode.toLowerCase() !==
            b.companyColorCode.toLowerCase()
          ) {
            return a.companyColorCode
              .toLowerCase()
              .localeCompare(b.companyColorCode.toLowerCase());
          }

          return a.remark.toLowerCase().localeCompare(b.remark.toLowerCase());
        };

        // Sort the data
        const sortedData = this.colorsList.sort(
          sortByNewColorCompanyColorCodeRemark
        );

        this.colorsList = sortedData;
        // console.log(value);
      },
      error: (err) => {
        // console.log(error)
      },
    });
  }
  _viewLagat: any;
  _viewTotalLagat: string = '0.000';
  view(id: string) {
    this._viewLagat = this.allDesign.find((x: any) => x._id === id);
    const totalLagat = this._viewLagat.colourLagats.reduce(
      (acc: number, cuu: any) => {
        const lagatValue = parseFloat(cuu.lagat);
        return acc + (isNaN(lagatValue) ? 0 : lagatValue);
      },
      0
    );

    // Round to three decimal places and convert to string
    this._viewTotalLagat = totalLagat.toFixed(3);
  }
}
