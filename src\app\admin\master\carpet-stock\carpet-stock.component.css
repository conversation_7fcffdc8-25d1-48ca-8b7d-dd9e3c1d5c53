fieldset {
  font-family: sans-serif;
  border: 2px solid #1F497D;
  background: #ffffff;
  border-radius: 5px;
  padding: 15px;
}

fieldset legend {
  background: #ffffff;
  color: #000000;
  padding: 5px 10px ;
  font-size: 20px;
  border-radius: 5px;
  /* box-shadow: 0 0 0 5px #ddd; */
  margin-left: 20px;
}

legend {
  float: left;
  width: auto;
  padding: 0;
  margin-top: -32px;

  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + .3vw);
  line-height: inherit;
}
.scroll{
  overflow-x: scroll;
  overflow-y: scroll;
}
.row{
  width: 100%;
}



section {
  display: table;
}

.example-label {
  display: table-cell;
  font-size: 14px;
  margin-left: 8px;
  min-width: 120px;
}

.example-button-row {
  display: table-cell;
  max-width: 600px;
}

.example-button-row .mat-mdc-button-base {
  margin: 8px 8px 8px 0;
}

.example-flex-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.example-button-container {
  display: flex;
  justify-content: center;
  width: 120px;
}
