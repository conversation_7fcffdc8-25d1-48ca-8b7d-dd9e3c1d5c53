/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,m=Object.getOwnPropertyDescriptor,b=m&&!y.call({1:2},1);g.f=b?function(t){var n=m(this,t);return!!n&&n.enumerable}:y;var d,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),x=T("".slice),E=function(t){return x(A(t),8,-1)},C=E,M=P,F=function(t){if("Function"===C(t))return M(t)},k=a,R=E,D=Object,I=F("".split),L=k((function(){return!D("z").propertyIsEnumerable(0)}))?function(t){return"String"==R(t)?I(t,""):D(t)}:D,N=function(t){return null==t},_=N,z=TypeError,H=function(t){if(_(t))throw z("Can't call method on "+t);return t},G=L,B=H,q=function(t){return G(B(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},J=W.all,K=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===J}:function(t){return"function"==typeof t},Q=K,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,$=K,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=F({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(h=(d=at.split("."))[0]>0&&d[0]<4?1:+(d[0]+d[1])),!h&&et&&(!(d=et.match(/Edge\/(\d+)/))||d[1]>=74)&&(d=et.match(/Chrome\/(\d+)/))&&(h=+d[1]);var ct=h,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=K,mt=nt,bt=Object,dt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&mt(n.prototype,bt(t))},ht=String,vt=K,St=function(t){try{return ht(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=N,Pt=p,Tt=K,At=X,xt=TypeError,Et={exports:{}},Ct=i,Mt=Object.defineProperty,Ft=function(t,n){try{Mt(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},kt=Ft,Rt="__core-js_shared__",Dt=i[Rt]||kt(Rt,{}),It=Dt;(Et.exports=function(t,n){return It[t]||(It[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Lt=H,Nt=Object,_t=function(t){return Nt(Lt(t))},zt=_t,Ht=F({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return Ht(zt(t),n)},Bt=F,qt=0,Ut=Math.random(),Wt=Bt(1..toString),Jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++qt+Ut,36)},Kt=i,Qt=Et.exports,Vt=Gt,Xt=Jt,Yt=st,$t=pt,Zt=Qt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,an=dt,cn=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=dt,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},mn=X,bn=i.document,dn=mn(bn)&&mn(bn.createElement),hn=function(t){return dn?bn.createElement(t):{}},vn=!c&&!a((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),Sn=c,wn=p,On=g,jn=v,Pn=q,Tn=yn,An=Gt,xn=vn,En=Object.getOwnPropertyDescriptor;u.f=Sn?En:function(t,n){if(t=Pn(t),n=Tn(n),xn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var Cn={},Mn=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Fn=X,kn=String,Rn=TypeError,Dn=function(t){if(Fn(t))return t;throw Rn(kn(t)+" is not an object")},In=c,Ln=vn,Nn=Mn,_n=Dn,zn=yn,Hn=TypeError,Gn=Object.defineProperty,Bn=Object.getOwnPropertyDescriptor,qn="enumerable",Un="configurable",Wn="writable";Cn.f=In?Nn?function(t,n,r){if(_n(t),n=zn(n),_n(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=Bn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:qn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(_n(t),n=zn(n),_n(r),Ln)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Hn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Jn=Cn,Kn=v,Qn=c?function(t,n,r){return Jn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=c,Yn=Gt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=K,er=Dt,or=F(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,gr=Jt,yr=pr("keys"),mr={},br=sr,dr=i,hr=X,vr=Qn,Sr=Gt,wr=Dt,Or=function(t){return yr[t]||(yr[t]=gr(t))},jr=mr,Pr="Object already initialized",Tr=dr.TypeError,Ar=dr.WeakMap;if(br||wr.state){var xr=wr.state||(wr.state=new Ar);xr.get=xr.get,xr.has=xr.has,xr.set=xr.set,ir=function(t,n){if(xr.has(t))throw Tr(Pr);return n.facade=t,xr.set(t,n),n},ur=function(t){return xr.get(t)||{}},ar=function(t){return xr.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(Sr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return Sr(t,Er)?t[Er]:{}},ar=function(t){return Sr(t,Er)}}var Cr={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Mr=a,Fr=K,kr=Gt,Rr=c,Dr=nr.CONFIGURABLE,Ir=cr,Lr=Cr.enforce,Nr=Cr.get,_r=Object.defineProperty,zr=Rr&&!Mr((function(){return 8!==_r((function(){}),"length",{value:8}).length})),Hr=String(String).split("String"),Gr=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!kr(t,"name")||Dr&&t.name!==n)&&(Rr?_r(t,"name",{value:n,configurable:!0}):t.name=n),zr&&r&&kr(r,"arity")&&t.length!==r.arity&&_r(t,"length",{value:r.arity});try{r&&kr(r,"constructor")&&r.constructor?Rr&&_r(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Lr(t);return kr(e,"source")||(e.source=Hr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Fr(this)&&Nr(this).source||Ir(this)}),"toString");var Br=K,qr=Cn,Ur=Vn.exports,Wr=Ft,Jr={},Kr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Kr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=q,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=Gt,le=q,se=ce.indexOf,pe=mr,ge=F([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},me=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Jr.f=Object.getOwnPropertyNames||function(t){return ye(t,me)};var be={};be.f=Object.getOwnPropertySymbols;var de=tt,he=Jr,ve=be,Se=Dn,we=F([].concat),Oe=de("Reflect","ownKeys")||function(t){var n=he.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},je=Gt,Pe=Oe,Te=u,Ae=Cn,xe=a,Ee=K,Ce=/#|\.prototype\./,Me=function(t,n){var r=ke[Fe(t)];return r==De||r!=Re&&(Ee(n)?xe(n):!!n)},Fe=Me.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},ke=Me.data={},Re=Me.NATIVE="N",De=Me.POLYFILL="P",Ie=Me,Le=i,Ne=u.f,_e=Qn,ze=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Br(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:qr.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},He=Ft,Ge=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var a=e[u];je(t,a)||r&&je(r,a)||o(t,a,i(n,a))}},Be=Ie,qe=E,Ue=Array.isArray||function(t){return"Array"==qe(t)},We=TypeError,Je=yn,Ke=Cn,Qe=v,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=K,$e=E,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=F,eo=a,oo=K,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),go=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var mo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,bo=Ue,ho=mo,vo=X,So=en("species"),wo=Array,Oo=function(t){var n;return bo(t)&&(n=t.constructor,(ho(n)&&(n===wo||bo(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=a,Po=ct,To=en("species"),Ao=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?Le:f?Le[a]||He(a,{}):(Le[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Ne(r,e))&&u.value:r[e],!Be(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&_e(i,"sham",!0),ze(r,e,i,t)}},xo=a,Eo=Ue,Co=X,Mo=_t,Fo=ee,ko=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Ro=function(t,n,r){var e=Je(n);e in t?Ke.f(t,e,Qe(0,r)):t[e]=r},Do=function(t,n){return new(Oo(t))(0===n?0:n)},Io=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=ct,No=en("isConcatSpreadable"),_o=Lo>=51||!xo((function(){var t=[];return t[No]=!1,t.concat()[0]!==t})),zo=Io("concat"),Ho=function(t){if(!Co(t))return!1;var n=t[No];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!_o||!zo},{concat:function(t){var n,r,e,o,i,u=Mo(this),a=Do(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(Ho(i=-1===n?u:arguments[n]))for(o=Fo(i),ko(c+o),r=0;r<o;r++,c++)r in i&&Ro(a,c,i[r]);else ko(c+1),Ro(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["id-ID"]=r.default.fn.bootstrapTable.locales.id={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Memuat, mohon tunggu"},formatRecordsPerPage:function(t){return"".concat(t," baris per halaman")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Menampilkan ".concat(t," sampai ").concat(n," dari ").concat(r," baris (filtered from ").concat(e," total rows)"):"Menampilkan ".concat(t," sampai ").concat(n," dari ").concat(r," baris")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Bersihkan filter"},formatSearch:function(){return"Pencarian"},formatNoMatches:function(){return"Tidak ditemukan data yang cocok"},formatPaginationSwitch:function(){return"Sembunyikan/Tampilkan halaman"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Muat ulang"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"kolom"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Semua"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Ekspor data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["id-ID"])}));
