// repositories/container-rcv-repository.js

const { Container } = require('winston');
const ContainerReceived = require('../model/phase-1/container-rvc');

async function createContainerReceived(containerReceivedData) {
    return ContainerReceived.create(containerReceivedData);
}

async function getContainerReceivedById(containerReceivedId) {
    return ContainerReceived.findById(containerReceivedId);
}

async function getAllContainerReceived() {
    return ContainerReceived.find();
}

async function updateContainerReceived(containerReceivedId, containerReceivedData) {
    return ContainerReceived.findByIdAndUpdate(containerReceivedId, containerReceivedData, { new: true });
}
async function getCompleteStockDetails() {
  return ContainerReceived.aggregate([
      { $unwind: "$containerItem" },
      {
          $group: {
              _id: "$_id",
              impoterName: { $first: "$impoterName" },
              totalArea: { $first: "$totalArea" },
              totalItems: { $sum: 1 },
              totalSold: {
                  $sum: {
                      $cond: [{ $eq: ["$containerItem.status", "sale"] }, 1, 0]
                  }
              },
              totalStock: {
                  $sum: {
                      $cond: [{ $eq: ["$containerItem.status", "stock"] }, 1, 0]
                  }
              },
              containerItems: { $push: "$containerItem" }
          }
      }
  ]);
}
async function deleteContainerReceived(containerReceivedId) {
    return ContainerReceived.findByIdAndDelete(containerReceivedId);
}

async function stockDetails(carpet){
    let data = carpet.body;
    let qd = data.qualityDesign;
    let colour = data.colour;
    let sizeStr = data.size;

   

   

if(qd==='All'){

let _data = await ContainerReceived.aggregate([
  {
    $addFields: {
      containerItem: {
        $filter: {
          input: "$containerItem",
          as: "item",
          cond: { $ne: ["$$item.status", "sale"] }
        }
      }
    }
  }
]);   
return _data;
}else if(qd!=='All' && colour==='All' && sizeStr==='All'){
   let _data = await ContainerReceived.aggregate([
      {
        $match: {
          "containerItem": {
            $elemMatch: {
              "QualityDesign": qd,
              "status": { $ne: "sale" }
            }
          }
        }
      },
      {
        $project: {
          containerItem: {
            $filter: {
              input: "$containerItem",
              as: "item",
              cond: {
                $and: [
                  { $eq: ["$$item.QualityDesign", qd] },
                  { $ne: ["$$item.status", "sale"] }
                ]
              }
            }
          }
        }
      }
    ]);
    return _data
}else if(qd!=='All' && colour!=='All' && sizeStr==='All'){
  let _data =await ContainerReceived.aggregate([
    {
      $match: {
        "containerItem": {
          $elemMatch: {
            "QualityDesign": qd,
            "status": { $ne: "sale" },
            "Color": colour // Add color condition here
          }
        }
      }
    },
    {
      $project: {
        containerItem: {
          $filter: {
            input: "$containerItem",
            as: "item",
            cond: {
              $and: [
                { $eq: ["$$item.QualityDesign", qd] },
                { $ne: ["$$item.status", "sale"] },
                { $eq: ["$$item.Color", colour] } // Add color condition here
              ]
            }
          }
        }
      }
    }
  ]);

  return _data;
}else if(qd!=='All' && colour!=='All' && sizeStr!=='All'){
       
        let str = sizeStr.split("X");

        let w2 = parseInt(str[0]) + 10;
        let w1 = parseInt(str[0]) - 10;
    
        let h2 = parseInt(str[1]) + 10;
        let h1 = parseInt(str[1]) - 10;
    
        let reqData = {
            all:data.all,
          qd: data.qualityDesign,
          colour: data.colour,
          sizeStr: data.size,
          h2: parseInt(str[0]) + 10,
          h1: parseInt(str[0]) - 10,
          w2: parseInt(str[1]) + 10,
          w1: parseInt(str[1]) - 10,
        
        };
        let _data= await ContainerReceived.aggregate([
            { $unwind: "$containerItem" },
            {
              $match: {
                "containerItem.QualityDesign": reqData.qd,
                "containerItem.Color": reqData.colour,
                $expr: {
                  $and: [
                    // Extract and trim height and check the range
                    {
                      $and: [
                        {
                          $gte: [
                            { $toInt: { $trim: { input: { $arrayElemAt: [{ $split: ["$containerItem.Size", " X"] }, 0] }, chars: " " } } },
                            reqData.h1
                          ]
                        },
                        {
                          $lte: [
                            { $toInt: { $trim: { input: { $arrayElemAt: [{ $split: ["$containerItem.Size", " X"] }, 0] }, chars: " " } } },
                            reqData.h2
                          ]
                        }
                      ]
                    },
                    // Extract and trim width and check the range
                    {
                      $and: [
                        {
                          $gte: [
                            { $toInt: { $trim: { input: { $arrayElemAt: [{ $split: ["$containerItem.Size", " X"] }, 1] }, chars: " " } } },
                            reqData.w1
                          ]
                        },
                        {
                          $lte: [
                            { $toInt: { $trim: { input: { $arrayElemAt: [{ $split: ["$containerItem.Size", " X"] }, 1] }, chars: " " } } },
                            reqData.w2
                          ]
                        }
                      ]
                    }
                  ]
                }
              }
            },
            {
              $group: {
                _id: "$_id",
                impoterName: { $first: "$impoterName" },
                expensesAmount: { $first: "$expensesAmount" },
                totalArea: { $first: "$totalArea" },
                espPrice: { $first: "$espPrice" },
                blPdf: { $first: "$blPdf" },
                containerItem: { $push: "$containerItem" }
              }
            }
          ]);
    
        return _data;
    }

      
}
module.exports = {
    createContainerReceived,
    getContainerReceivedById,
    getAllContainerReceived,
    updateContainerReceived,
    deleteContainerReceived,
    stockDetails,
    getCompleteStockDetails // New function exported
};
