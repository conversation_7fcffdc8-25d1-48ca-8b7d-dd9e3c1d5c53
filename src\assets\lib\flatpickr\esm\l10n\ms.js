var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Malaysian = {
    weekdays: {
        shorthand: ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>b"],
        longhand: ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ju<PERSON><PERSON>", "Sabtu"],
    },
    months: {
        shorthand: [
            "<PERSON>",
            "Feb",
            "<PERSON>",
            "Apr",
            "<PERSON>",
            "<PERSON>",
            "Jul",
            "Ogo",
            "Sep",
            "Okt",
            "Nov",
            "Dis",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "April",
            "<PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>gos",
            "September",
            "Oktober",
            "November",
            "Disember",
        ],
    },
    firstDayOfWeek: 1,
    ordinal: function () {
        return "";
    },
};
export default fp.l10ns;
