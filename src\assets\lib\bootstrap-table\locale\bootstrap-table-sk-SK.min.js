/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},y={},b={}.propertyIsEnumerable,v=Object.getOwnPropertyDescriptor,d=v&&!b.call({1:2},1);y.f=d?function(t){var n=v(this,t);return!!n&&n.enumerable}:b;var g,m,h=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,j=w.call,O=S&&w.bind.bind(j,j),P=function(t){return S?O(t):function(){return j.apply(t,arguments)}},k=P,T=k({}.toString),z=k("".slice),x=function(t){return z(T(t),8,-1)},A=x,E=P,C=function(t){if("Function"===A(t))return E(t)},F=a,M=x,R=Object,D=C("".split),I=F((function(){return!R("z").propertyIsEnumerable(0)}))?function(t){return"String"==M(t)?D(t,""):R(t)}:R,L=function(t){return null==t},N=L,Z=TypeError,_=function(t){if(N(t))throw Z("Can't call method on "+t);return t},G=I,q=_,B=function(t){return G(q(t))},H="object"==typeof document&&document.all,U={all:H,IS_HTMLDDA:void 0===H&&void 0!==H},V=U.all,K=U.IS_HTMLDDA?function(t){return"function"==typeof t||t===V}:function(t){return"function"==typeof t},W=K,J=U.all,Q=U.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:W(t)||t===J}:function(t){return"object"==typeof t?null!==t:W(t)},X=i,Y=K,$=function(t){return Y(t)?t:void 0},tt=function(t,n){return arguments.length<2?$(X[t]):X[t]&&X[t][n]},nt=C({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(m=(g=at.split("."))[0]>0&&g[0]<4?1:+(g[0]+g[1])),!m&&et&&(!(g=et.match(/Edge\/(\d+)/))||g[1]>=74)&&(g=et.match(/Chrome\/(\d+)/))&&(m=+g[1]);var ct=m,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,yt=tt,bt=K,vt=nt,dt=Object,gt=pt?function(t){return"symbol"==typeof t}:function(t){var n=yt("Symbol");return bt(n)&&vt(n.prototype,dt(t))},mt=String,ht=K,St=function(t){try{return mt(t)}catch(t){return"Object"}},wt=TypeError,jt=function(t){if(ht(t))return t;throw wt(St(t)+" is not a function")},Ot=L,Pt=p,kt=K,Tt=Q,zt=TypeError,xt={exports:{}},At=i,Et=Object.defineProperty,Ct=function(t,n){try{Et(At,t,{value:n,configurable:!0,writable:!0})}catch(r){At[t]=n}return n},Ft=Ct,Mt="__core-js_shared__",Rt=i[Mt]||Ft(Mt,{}),Dt=Rt;(xt.exports=function(t,n){return Dt[t]||(Dt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var It=_,Lt=Object,Nt=function(t){return Lt(It(t))},Zt=Nt,_t=C({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return _t(Zt(t),n)},qt=C,Bt=0,Ht=Math.random(),Ut=qt(1..toString),Vt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Ut(++Bt+Ht,36)},Kt=i,Wt=xt.exports,Jt=Gt,Qt=Vt,Xt=st,Yt=pt,$t=Wt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=Yt?tn:tn&&tn.withoutSetter||Qt,en=function(t){if(!Jt($t,t)||!Xt&&"string"!=typeof $t[t]){var n="Symbol."+t;Xt&&Jt(tn,t)?$t[t]=tn[t]:$t[t]=Yt&&nn?nn(n):rn(n)}return $t[t]},on=p,un=Q,an=gt,cn=function(t,n){var r=t[n];return Ot(r)?void 0:jt(r)},fn=function(t,n){var r,e;if("string"===n&&kt(r=t.toString)&&!Tt(e=Pt(r,t)))return e;if(kt(r=t.valueOf)&&!Tt(e=Pt(r,t)))return e;if("string"!==n&&kt(r=t.toString)&&!Tt(e=Pt(r,t)))return e;throw zt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},yn=gt,bn=function(t){var n=pn(t,"string");return yn(n)?n:n+""},vn=Q,dn=i.document,gn=vn(dn)&&vn(dn.createElement),mn=function(t){return gn?dn.createElement(t):{}},hn=!c&&!a((function(){return 7!=Object.defineProperty(mn("div"),"a",{get:function(){return 7}}).a})),Sn=c,wn=p,jn=y,On=h,Pn=B,kn=bn,Tn=Gt,zn=hn,xn=Object.getOwnPropertyDescriptor;u.f=Sn?xn:function(t,n){if(t=Pn(t),n=kn(n),zn)try{return xn(t,n)}catch(t){}if(Tn(t,n))return On(!wn(jn.f,t,n),t[n])};var An={},En=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Cn=Q,Fn=String,Mn=TypeError,Rn=function(t){if(Cn(t))return t;throw Mn(Fn(t)+" is not an object")},Dn=c,In=hn,Ln=En,Nn=Rn,Zn=bn,_n=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Hn="configurable",Un="writable";An.f=Dn?Ln?function(t,n,r){if(Nn(t),n=Zn(n),Nn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Un in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Hn in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(Nn(t),n=Zn(n),Nn(r),In)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw _n("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Vn=An,Kn=h,Wn=c?function(t,n,r){return Vn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Jn={exports:{}},Qn=c,Xn=Gt,Yn=Function.prototype,$n=Qn&&Object.getOwnPropertyDescriptor,tr=Xn(Yn,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Qn||Qn&&$n(Yn,"name").configurable)},rr=K,er=Rt,or=C(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=xt.exports,yr=Vt,br=pr("keys"),vr={},dr=sr,gr=i,mr=Q,hr=Wn,Sr=Gt,wr=Rt,jr=function(t){return br[t]||(br[t]=yr(t))},Or=vr,Pr="Object already initialized",kr=gr.TypeError,Tr=gr.WeakMap;if(dr||wr.state){var zr=wr.state||(wr.state=new Tr);zr.get=zr.get,zr.has=zr.has,zr.set=zr.set,ir=function(t,n){if(zr.has(t))throw kr(Pr);return n.facade=t,zr.set(t,n),n},ur=function(t){return zr.get(t)||{}},ar=function(t){return zr.has(t)}}else{var xr=jr("state");Or[xr]=!0,ir=function(t,n){if(Sr(t,xr))throw kr(Pr);return n.facade=t,hr(t,xr,n),n},ur=function(t){return Sr(t,xr)?t[xr]:{}},ar=function(t){return Sr(t,xr)}}var Ar={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!mr(n)||(r=ur(n)).type!==t)throw kr("Incompatible receiver, "+t+" required");return r}}},Er=a,Cr=K,Fr=Gt,Mr=c,Rr=nr.CONFIGURABLE,Dr=cr,Ir=Ar.enforce,Lr=Ar.get,Nr=Object.defineProperty,Zr=Mr&&!Er((function(){return 8!==Nr((function(){}),"length",{value:8}).length})),_r=String(String).split("String"),Gr=Jn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Fr(t,"name")||Rr&&t.name!==n)&&(Mr?Nr(t,"name",{value:n,configurable:!0}):t.name=n),Zr&&r&&Fr(r,"arity")&&t.length!==r.arity&&Nr(t,"length",{value:r.arity});try{r&&Fr(r,"constructor")&&r.constructor?Mr&&Nr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Ir(t);return Fr(e,"source")||(e.source=_r.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Cr(this)&&Lr(this).source||Dr(this)}),"toString");var qr=K,Br=An,Hr=Jn.exports,Ur=Ct,Vr={},Kr=Math.ceil,Wr=Math.floor,Jr=Math.trunc||function(t){var n=+t;return(n>0?Wr:Kr)(n)},Qr=function(t){var n=+t;return n!=n||0===n?0:Jr(n)},Xr=Qr,Yr=Math.max,$r=Math.min,te=Qr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Xr(t);return r<0?Yr(r+n,0):$r(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=Gt,le=B,se=ce.indexOf,pe=vr,ye=C([].push),be=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ye(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ye(i,r));return i},ve=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Vr.f=Object.getOwnPropertyNames||function(t){return be(t,ve)};var de={};de.f=Object.getOwnPropertySymbols;var ge=tt,me=Vr,he=de,Se=Rn,we=C([].concat),je=ge("Reflect","ownKeys")||function(t){var n=me.f(Se(t)),r=he.f;return r?we(n,r(t)):n},Oe=Gt,Pe=je,ke=u,Te=An,ze=a,xe=K,Ae=/#|\.prototype\./,Ee=function(t,n){var r=Fe[Ce(t)];return r==Re||r!=Me&&(xe(n)?ze(n):!!n)},Ce=Ee.normalize=function(t){return String(t).replace(Ae,".").toLowerCase()},Fe=Ee.data={},Me=Ee.NATIVE="N",Re=Ee.POLYFILL="P",De=Ee,Ie=i,Le=u.f,Ne=Wn,Ze=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Hr(r,i,e),e.global)o?t[n]=r:Ur(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},_e=Ct,Ge=function(t,n,r){for(var e=Pe(n),o=Te.f,i=ke.f,u=0;u<e.length;u++){var a=e[u];Oe(t,a)||r&&Oe(r,a)||o(t,a,i(n,a))}},qe=De,Be=x,He=Array.isArray||function(t){return"Array"==Be(t)},Ue=TypeError,Ve=bn,Ke=An,We=h,Je={};Je[en("toStringTag")]="z";var Qe="[object z]"===String(Je),Xe=K,Ye=x,$e=en("toStringTag"),to=Object,no="Arguments"==Ye(function(){return arguments}()),ro=C,eo=a,oo=K,io=Qe?Ye:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),$e))?r:no?Ye(n):"Object"==(e=Ye(n))&&Xe(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),yo=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},bo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};bo.sham=!0;var vo=!fo||eo((function(){var t;return yo(yo.call)||!yo(Object)||!yo((function(){t=!0}))||t}))?bo:yo,go=He,mo=vo,ho=Q,So=en("species"),wo=Array,jo=function(t){var n;return go(t)&&(n=t.constructor,(mo(n)&&(n===wo||go(n.prototype))||ho(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},Oo=a,Po=ct,ko=en("species"),To=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?Ie:f?Ie[a]||_e(a,{}):(Ie[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Le(r,e))&&u.value:r[e],!qe(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&Ne(i,"sham",!0),Ze(r,e,i,t)}},zo=a,xo=He,Ao=Q,Eo=Nt,Co=ee,Fo=function(t){if(t>9007199254740991)throw Ue("Maximum allowed index exceeded");return t},Mo=function(t,n,r){var e=Ve(n);e in t?Ke.f(t,e,We(0,r)):t[e]=r},Ro=function(t,n){return new(jo(t))(0===n?0:n)},Do=function(t){return Po>=51||!Oo((function(){var n=[];return(n.constructor={})[ko]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Io=ct,Lo=en("isConcatSpreadable"),No=Io>=51||!zo((function(){var t=[];return t[Lo]=!1,t.concat()[0]!==t})),Zo=Do("concat"),_o=function(t){if(!Ao(t))return!1;var n=t[Lo];return void 0!==n?!!n:xo(t)};To({target:"Array",proto:!0,arity:1,forced:!No||!Zo},{concat:function(t){var n,r,e,o,i,u=Eo(this),a=Ro(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(_o(i=-1===n?u:arguments[n]))for(o=Co(i),Fo(c+o),r=0;r<o;r++,c++)r in i&&Mo(a,c,i[r]);else Fo(c+1),Mo(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["sk-SK"]=r.default.fn.bootstrapTable.locales.sk={formatCopyRows:function(){return"Skopírovať riadky"},formatPrint:function(){return"Vytlačiť"},formatLoadingMessage:function(){return"Prosím čakajte"},formatRecordsPerPage:function(t){return"".concat(t," záznamov na stranu")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Zobrazená ".concat(t,". - ").concat(n,". položka z celkových ").concat(r," (filtered from ").concat(e," total rows)"):"Zobrazená ".concat(t,". - ").concat(n,". položka z celkových ").concat(r)},formatSRPaginationPreText:function(){return"Predchádzajúca strana"},formatSRPaginationPageText:function(t){return"na stranu ".concat(t)},formatSRPaginationNextText:function(){return"Nasledujúca strana"},formatDetailPagination:function(t){return"Zobrazuje sa ".concat(t," riadkov")},formatClearSearch:function(){return"Odstráň filtre"},formatSearch:function(){return"Vyhľadávanie"},formatNoMatches:function(){return"Nenájdená žiadna vyhovujúca položka"},formatPaginationSwitch:function(){return"Skry/Zobraz stránkovanie"},formatPaginationSwitchDown:function(){return"Zobraziť stránkovanie"},formatPaginationSwitchUp:function(){return"Skryť stránkovanie"},formatRefresh:function(){return"Obnoviť"},formatToggleOn:function(){return"Zobraziť kartové zobrazenie"},formatToggleOff:function(){return"skryť kartové zobrazenie"},formatColumns:function(){return"Stĺpce"},formatColumnsToggleAll:function(){return"Prepnúť všetky"},formatFullscreen:function(){return"Celá obrazovka"},formatAllRows:function(){return"Všetky"},formatAutoRefresh:function(){return"Automatické obnovenie"},formatExport:function(){return"Exportuj dáta"},formatJumpTo:function(){return"Ísť"},formatAdvancedSearch:function(){return"Pokročilé vyhľadávanie"},formatAdvancedCloseButton:function(){return"Zatvoriť"},formatFilterControlSwitch:function(){return"Zobraziť/Skryť tlačidlá"},formatFilterControlSwitchHide:function(){return"Skryť tlačidlá"},formatFilterControlSwitchShow:function(){return"Zobraziť tlačidlá"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["sk-SK"])}));
