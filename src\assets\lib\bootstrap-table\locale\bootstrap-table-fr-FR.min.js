/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,h=b&&!y.call({1:2},1);g.f=h?function(t){var n=b(this,t);return!!n&&n.enumerable}:y;var m,d,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},A=P,T=A({}.toString),E=A("".slice),x=function(t){return E(T(t),8,-1)},C=x,M=P,F=function(t){if("Function"===C(t))return M(t)},R=c,D=x,I=Object,L=F("".split),q=R((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"==D(t)?L(t,""):I(t)}:I,N=function(t){return null==t},_=N,k=TypeError,z=function(t){if(_(t))throw k("Can't call method on "+t);return t},G=q,B=z,H=function(t){return G(B(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},J=W.all,K=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===J}:function(t){return"function"==typeof t},Q=K,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,$=K,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=F({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(d=(m=ct.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!d&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(d=+m[1]);var at=d,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=K,bt=nt,ht=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&bt(n.prototype,ht(t))},dt=String,vt=K,St=function(t){try{return dt(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=N,Pt=p,At=K,Tt=X,Et=TypeError,xt={exports:{}},Ct=i,Mt=Object.defineProperty,Ft=function(t,n){try{Mt(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Rt=Ft,Dt="__core-js_shared__",It=i[Dt]||Rt(Dt,{}),Lt=It;(xt.exports=function(t,n){return Lt[t]||(Lt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var qt=z,Nt=Object,_t=function(t){return Nt(qt(t))},kt=_t,zt=F({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return zt(kt(t),n)},Bt=F,Ht=0,Ut=Math.random(),Wt=Bt(1..toString),Jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Ht+Ut,36)},Kt=i,Qt=xt.exports,Vt=Gt,Xt=Jt,Yt=st,$t=pt,Zt=Qt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,cn=mt,an=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&At(r=t.toString)&&!Tt(e=Pt(r,t)))return e;if(At(r=t.valueOf)&&!Tt(e=Pt(r,t)))return e;if("string"!==n&&At(r=t.toString)&&!Tt(e=Pt(r,t)))return e;throw Et("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=mt,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},bn=X,hn=i.document,mn=bn(hn)&&bn(hn.createElement),dn=function(t){return mn?hn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(dn("div"),"a",{get:function(){return 7}}).a})),Sn=a,wn=p,On=g,jn=v,Pn=H,An=yn,Tn=Gt,En=vn,xn=Object.getOwnPropertyDescriptor;u.f=Sn?xn:function(t,n){if(t=Pn(t),n=An(n),En)try{return xn(t,n)}catch(t){}if(Tn(t,n))return jn(!wn(On.f,t,n),t[n])};var Cn={},Mn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Fn=X,Rn=String,Dn=TypeError,In=function(t){if(Fn(t))return t;throw Dn(Rn(t)+" is not an object")},Ln=a,qn=vn,Nn=Mn,_n=In,kn=yn,zn=TypeError,Gn=Object.defineProperty,Bn=Object.getOwnPropertyDescriptor,Hn="enumerable",Un="configurable",Wn="writable";Cn.f=Ln?Nn?function(t,n,r){if(_n(t),n=kn(n),_n(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=Bn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Hn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(_n(t),n=kn(n),_n(r),qn)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw zn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Jn=Cn,Kn=v,Qn=a?function(t,n,r){return Jn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=a,Yn=Gt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=K,er=It,or=F(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=xt.exports,gr=Jt,yr=pr("keys"),br={},hr=sr,mr=i,dr=X,vr=Qn,Sr=Gt,wr=It,Or=function(t){return yr[t]||(yr[t]=gr(t))},jr=br,Pr="Object already initialized",Ar=mr.TypeError,Tr=mr.WeakMap;if(hr||wr.state){var Er=wr.state||(wr.state=new Tr);Er.get=Er.get,Er.has=Er.has,Er.set=Er.set,ir=function(t,n){if(Er.has(t))throw Ar(Pr);return n.facade=t,Er.set(t,n),n},ur=function(t){return Er.get(t)||{}},cr=function(t){return Er.has(t)}}else{var xr=Or("state");jr[xr]=!0,ir=function(t,n){if(Sr(t,xr))throw Ar(Pr);return n.facade=t,vr(t,xr,n),n},ur=function(t){return Sr(t,xr)?t[xr]:{}},cr=function(t){return Sr(t,xr)}}var Cr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!dr(n)||(r=ur(n)).type!==t)throw Ar("Incompatible receiver, "+t+" required");return r}}},Mr=c,Fr=K,Rr=Gt,Dr=a,Ir=nr.CONFIGURABLE,Lr=ar,qr=Cr.enforce,Nr=Cr.get,_r=Object.defineProperty,kr=Dr&&!Mr((function(){return 8!==_r((function(){}),"length",{value:8}).length})),zr=String(String).split("String"),Gr=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Rr(t,"name")||Ir&&t.name!==n)&&(Dr?_r(t,"name",{value:n,configurable:!0}):t.name=n),kr&&r&&Rr(r,"arity")&&t.length!==r.arity&&_r(t,"length",{value:r.arity});try{r&&Rr(r,"constructor")&&r.constructor?Dr&&_r(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=qr(t);return Rr(e,"source")||(e.source=zr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Fr(this)&&Nr(this).source||Lr(this)}),"toString");var Br=K,Hr=Cn,Ur=Vn.exports,Wr=Ft,Jr={},Kr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Kr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=H,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=Gt,le=H,se=ae.indexOf,pe=br,ge=F([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Jr.f=Object.getOwnPropertyNames||function(t){return ye(t,be)};var he={};he.f=Object.getOwnPropertySymbols;var me=tt,de=Jr,ve=he,Se=In,we=F([].concat),Oe=me("Reflect","ownKeys")||function(t){var n=de.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},je=Gt,Pe=Oe,Ae=u,Te=Cn,Ee=c,xe=K,Ce=/#|\.prototype\./,Me=function(t,n){var r=Re[Fe(t)];return r==Ie||r!=De&&(xe(n)?Ee(n):!!n)},Fe=Me.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Re=Me.data={},De=Me.NATIVE="N",Ie=Me.POLYFILL="P",Le=Me,qe=i,Ne=u.f,_e=Qn,ke=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Br(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Hr.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},ze=Ft,Ge=function(t,n,r){for(var e=Pe(n),o=Te.f,i=Ae.f,u=0;u<e.length;u++){var c=e[u];je(t,c)||r&&je(r,c)||o(t,c,i(n,c))}},Be=Le,He=x,Ue=Array.isArray||function(t){return"Array"==He(t)},We=TypeError,Je=yn,Ke=Cn,Qe=v,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=K,$e=x,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=F,eo=c,oo=K,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,ho=Ue,mo=bo,vo=X,So=en("species"),wo=Array,Oo=function(t){var n;return ho(t)&&(n=t.constructor,(mo(n)&&(n===wo||ho(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=c,Po=at,Ao=en("species"),To=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?qe:f?qe[c]||ze(c,{}):(qe[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Ne(r,e))&&u.value:r[e],!Be(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&_e(i,"sham",!0),ke(r,e,i,t)}},Eo=c,xo=Ue,Co=X,Mo=_t,Fo=ee,Ro=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Do=function(t,n,r){var e=Je(n);e in t?Ke.f(t,e,Qe(0,r)):t[e]=r},Io=function(t,n){return new(Oo(t))(0===n?0:n)},Lo=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[Ao]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},qo=at,No=en("isConcatSpreadable"),_o=qo>=51||!Eo((function(){var t=[];return t[No]=!1,t.concat()[0]!==t})),ko=Lo("concat"),zo=function(t){if(!Co(t))return!1;var n=t[No];return void 0!==n?!!n:xo(t)};To({target:"Array",proto:!0,arity:1,forced:!_o||!ko},{concat:function(t){var n,r,e,o,i,u=Mo(this),c=Io(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(zo(i=-1===n?u:arguments[n]))for(o=Fo(i),Ro(a+o),r=0;r<o;r++,a++)r in i&&Do(c,a,i[r]);else Ro(a+1),Do(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["fr-FR"]=r.default.fn.bootstrapTable.locales.fr={formatCopyRows:function(){return"Copier les lignes"},formatPrint:function(){return"Imprimer"},formatLoadingMessage:function(){return"Chargement en cours"},formatRecordsPerPage:function(t){return"".concat(t," lignes par page")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Affichage de ".concat(t," à ").concat(n," sur ").concat(r," lignes (filtrés à partir de ").concat(e," lignes)"):"Affichage de ".concat(t," à ").concat(n," sur ").concat(r," lignes")},formatSRPaginationPreText:function(){return"page précédente"},formatSRPaginationPageText:function(t){return"vers la page ".concat(t)},formatSRPaginationNextText:function(){return"page suivante"},formatDetailPagination:function(t){return"Affichage de ".concat(t," lignes")},formatClearSearch:function(){return"Effacer la recherche"},formatSearch:function(){return"Recherche"},formatNoMatches:function(){return"Aucun résultat"},formatPaginationSwitch:function(){return"Masquer/Afficher la pagination"},formatPaginationSwitchDown:function(){return"Afficher la pagination"},formatPaginationSwitchUp:function(){return"Masquer la pagination"},formatRefresh:function(){return"Actualiser"},formatToggleOn:function(){return"Afficher la vue carte"},formatToggleOff:function(){return"Masquer la vue carte"},formatColumns:function(){return"Colonnes"},formatColumnsToggleAll:function(){return"Tout basculer"},formatFullscreen:function(){return"Plein écran"},formatAllRows:function(){return"Tout"},formatAutoRefresh:function(){return"Actualisation automatique"},formatExport:function(){return"Exporter les données"},formatJumpTo:function(){return"ALLER"},formatAdvancedSearch:function(){return"Recherche avancée"},formatAdvancedCloseButton:function(){return"Fermer"},formatFilterControlSwitch:function(){return"Masquer/Afficher les contrôles"},formatFilterControlSwitchHide:function(){return"Masquer les contrôles"},formatFilterControlSwitchShow:function(){return"Afficher les contrôles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fr-FR"])}));
