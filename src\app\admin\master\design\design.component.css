
fieldset {
    font-family: sans-serif;
    border: 2px solid #1F497D;
    background: #ffffff;
    border-radius: 5px;
    padding: 15px;
  }

  fieldset legend {
    background: #ffffff;
    color: #000000;
    padding: 5px 10px ;
    font-size: 20px;
    border-radius: 5px;
    /* box-shadow: 0 0 0 5px #ddd; */
    margin-left: 20px;
  }

  legend {
    float: left;
    width: auto;
    padding: 0;
    margin-top: -32px;

    margin-bottom: 0.5rem;
    font-size: calc(1.275rem + .3vw);
    line-height: inherit;
  }
  .ex-width{
    width: 100%;
  }
   a {
    cursor: pointer !important;
   }
   .scrollable-container {
    max-height: 300px;
    min-height: 0px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* For Webkit-based browsers */
.scrollable-container::-webkit-scrollbar {
    width: 5px; /* Adjust the width to your preference */
}

.scrollable-container::-webkit-scrollbar-thumb {
    background-color: #888; /* Scrollbar thumb color */
    border-radius: 10px; /* Rounded corners */
}

.scrollable-container::-webkit-scrollbar-thumb:hover {
    background-color: #555; /* Scrollbar thumb color on hover */
}

/* For Firefox */
.scrollable-container {
    scrollbar-width: thin; /* "auto" or "thin" */
    scrollbar-color: #888 #e0e0e0; /* Scrollbar thumb and track color */
}
.mat-table-container {
  height: 400px; /* Set a fixed height for the container */
  overflow-y: auto; /* Enable vertical scrolling */
}

.sticky-header {
  position: sticky;
  top: 0; /* Stick to the top of the container */
  z-index: 1; /* Ensure the header is above the body rows */
  background-color: white; /* Ensure the header has a background */
}

table {
  width: 100%; /* Ensure table takes full width of container */
}
