import { Component, OnInit } from '@angular/core';
import { ImporterService } from '../../../../services/importer.service';
import { CustomeServiceService } from '../../../../services/custome-service.service';
import { ActivatedRoute } from '@angular/router';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-print-bill',
  templateUrl:'./print-bill.component.html',
  styleUrl: './print-bill.component.css',
 
})
export class PrintBillComponent implements OnInit {
  constructor(
    private _service: ImporterService,
    private customeService: CustomeServiceService,
    private activeRoute: ActivatedRoute,
    private ngxLoader: NgxUiLoaderService

  ) {}
  allBills: any;
  billId: string = '';
  _isChallan: boolean = false;
  ngOnInit(): void {

    this.billId = this.activeRoute.snapshot.paramMap.get('id') || '';
    this.initializeData(this.billId);
  }

  initializeData(id: string) {
    this.ngxLoader.start();
    Promise.all([
      this.getBills(),
      this.getChallans(),
      this.getContainerReceived(),
      this.getImporterName(),
    ]).then(()=>{
      let _bill = id.split(' ');
      if (_bill[1] === 'print') {
        this.viewBill(_bill[0]);
      } else {
        // this.viewChallan(_bill[0]);
      }
      this.ngxLoader.stop()
    }).catch((error)=>{console.log('something went wrong')})


  }


 async getBills() :Promise<any>{
  const resp = await firstValueFrom(this._service.getsBill());
  this.allBills = resp;
}
allChallans: any;
allContainerStock: any;



async getChallans(): Promise<void> {
  const resp = await firstValueFrom(this._service.getAllChallan());
  this.allChallans = resp;
}

async getContainerReceived(): Promise<void> {
  const resp = await firstValueFrom(this._service.getAllContainerRecieved());
  this.allContainerStock = resp;
}
allImporterDetails:any;
async getImporterName(){
  const resp = await firstValueFrom(this._service.getsWholesalerList());
  this.allImporterDetails = resp;
}
billDetails: any = [];
_bill: any = {};
viewBill(id: any) {
  debugger
  let getChallans = this.allBills.find((x: any) => x._id === id.toString());

  let getImporter = this.allImporterDetails.find((name:any)=> name.customerName===getChallans.wholesellerName)
  debugger
  console.log(getImporter);
    this._bill = {
    billNo: getChallans.billNo,
    date: this.customeService.convertDate(getChallans.chooseAdate),
    customer: getChallans.wholesellerName,
    street:getImporter.address,
    // city:getImporter.impoter.address.city,
    // state:getImporter.impoter.address.state,
    zipCode:getImporter.zipCode,
    country:getImporter.country
  };
  getChallans.challanNo.forEach((element: any) => {
    let getChallanDetails = this.allChallans.find(
      (x: any) => x.challanNo === element.challanNumber
    );

    getChallanDetails.carpetList.forEach((elem: any) => {
      // Find the item details in allContainerStock
      let matchedContainers = this.allContainerStock.filter(
        (container: any) =>
          container.containerItem.some(
            (item: any) => parseInt(item.GerCarpetNo) === elem.barcodeNo
          )
      );
      matchedContainers.forEach((container: any) => {
        container.containerItem.forEach((item: any) => {
          if (parseInt(item.GerCarpetNo) === elem.barcodeNo) {
            let date = this.customeService.convertDate(
              getChallanDetails.chooseAdate
            );
            const includeChallanDetails = this.billDetails.some(
              (a: any) => a.challanNo == getChallanDetails.challanNo
            );
            debugger
            this.billDetails.push({
              challanNo: includeChallanDetails
                ? undefined
                : getChallanDetails.challanNo,
              challanDate: includeChallanDetails ? undefined : date,
              customer: includeChallanDetails
                ? undefined
                : getChallanDetails.retailerOutlet,
              carpetNo: item.GerCarpetNo,
              qualityDesign: item.QualityDesign,
              colour: item.Color,
              colourCode: item.CCode,
              qualityCode: item.QCode,
              size: elem.size ? elem.size : item.Size,
              area: elem.area ? elem.area : item.Area,
              evkPrice: elem.evkPrice ? elem.evkPrice : item.EvKPrice,
              amount:
                parseFloat(elem.evkPrice ? elem.evkPrice : item.EvKPrice) *
                parseFloat(elem.area ? elem.area : item.Area),
              invoiceNo: item.InvoiceNo,
              saleStatus: item.status,
            });
          }
        });
      });
    });
  });
debugger
  this.calculation(this.billDetails);
  console.log(this.billDetails);
}

totalCalculation = {
  totalAmount: 0,
  // totalArea: 0,
  // totalEvkPrice: 0,
  profit:0,
  grossAmt:0,
  gstAmt:0
};

calculation(data: any) {
  data.forEach((element: any) => {
    this.totalCalculation = {
      totalAmount:this.totalCalculation.totalAmount + parseFloat(element.area) * parseFloat(element.evkPrice),
      // totalArea: this.totalCalculation.totalArea + parseFloat(element.area),
      // totalEvkPrice: this.totalCalculation.totalEvkPrice + parseFloat(element.evkPrice),
      profit:this.totalCalculation.totalAmount/100*13,
      gstAmt:(this.totalCalculation.totalAmount+this.totalCalculation.profit)/100*19,
      grossAmt:this.totalCalculation.totalAmount+this.totalCalculation.gstAmt+this.totalCalculation.profit
    };
  });

  console.log(this.totalCalculation);

  debugger;
}





}
