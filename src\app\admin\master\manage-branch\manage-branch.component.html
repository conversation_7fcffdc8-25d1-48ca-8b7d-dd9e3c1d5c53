<div class="container mt-5">
  <section>
    <fieldset>
      <legend><b>Manage Branch</b></legend>
      <form [formGroup]="frmBranch" (ngSubmit)="addBranch()">
        <div class="row">
          <div class="mb-2 col-md-6">
            <mat-form-field appearance="outline" class="ex-width custom-height">
              <mat-label>Branch Name</mat-label>
              <input matInput placeholder="Branch Name" formControlName="branchName" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmBranch.get('branchName')?.invalid && (frmBranch.get('branchName')?.dirty || frmBranch.get('branchName')?.touched)">
              <div *ngIf="frmBranch.get('branchName')?.errors?.['required']">
               <span class="errText"> Branch name is required.</span>
              </div>
              <div *ngIf="frmBranch.get('branchName')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>

          <div class="mb-2 col-md-6">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Branch Code</mat-label>
              <input matInput placeholder="Branch Code" formControlName="branchCode" />
            </mat-form-field>

            <div class="erroDiv" *ngIf="frmBranch.get('branchCode')?.invalid && (frmBranch.get('branchCode')?.dirty || frmBranch.get('branchCode')?.touched)">
              <div *ngIf="frmBranch.get('branchCode')?.errors?.['required']">
               <span class="errText"> Branch code is required.</span>
              </div>
              <div *ngIf="frmBranch.get('branchCode')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <button *ngIf="!isUpdated; else updateBtn" mat-flat-button color="primary" [disabled]="!frmBranch.valid">Add</button>

            <ng-template #updateBtn>
            <button  mat-flat-button type="button" color="primary" [disabled]="!frmBranch.valid" (click)="update()">Update</button>
            </ng-template>
          </div>
        </div>
      </form>
    </fieldset>
  </section>
</div>

<div class="container mt-4">
  <section>
    <fieldset>
      <legend><b>List</b></legend>
      <div class="container-fluid">
        <div class="py-3 py-lg-4">
          <div class="row mt-3">
            <div class="col-12">
              <mat-form-field appearance="outline">
                <mat-label>Seacrh</mat-label>
                <input
                  matInput
                  (keyup)="applyFilter($event)"
                  placeholder="Ex. Mia"
                  #input
                />
              </mat-form-field>

              <div class="mat-elevation-z8">
                <table mat-table [dataSource]="dataSource" matSort>
                  <!-- srno Column -->
                  <ng-container matColumnDef="id">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Sr.No.
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.index }}</td>
                  </ng-container>
                  <!-- work Column -->
                  <ng-container matColumnDef="branchName">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Branch Name
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.branchName }}</td>
                  </ng-container>

                  <!-- Action Column -->
                  <ng-container matColumnDef="branchCode">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Branch Code
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.branchCode }}</td>
                  </ng-container>

                  <ng-container matColumnDef="action">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Action
                    </th>
                    <td mat-cell *matCellDef="let row">

                      <a (click)="edit(row.id)"
                                  ><i
                                    class="fa fa-pencil-square-o fa-edit"
                                    title="Edit purchase details "
                                    aria-hidden="true"
                                  ></i
                                ></a>
                                &nbsp;
                                <a (click)="delete(row.id)"><i class="fa fa-trash-o" aria-hidden="true"></i></a>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                  <tr
                    mat-row
                    *matRowDef="let row; columns: displayedColumns"
                  ></tr>

                  <!-- Row shown when there is no matching data. -->
                  <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" colspan="4">
                      No data matching the filter "{{ input.value }}"
                    </td>
                  </tr>
                </table>

                <mat-paginator
                  [pageSizeOptions]="[5, 10, 25, 100]"
                  aria-label="Select page of users"
                ></mat-paginator>
              </div>
            </div>
          </div>
        </div>
      </div>
    </fieldset>
  </section>
</div>
