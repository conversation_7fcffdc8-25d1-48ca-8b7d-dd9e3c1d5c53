const mongoose = require('mongoose');

const weaverEmployeeSchema = new mongoose.Schema({
  branch:{type:mongoose.Schema.Types.ObjectId,
    ref:'branch'},
   
  groupName: { type: String, required: false },
  name: { type: String, required: false },
  address: { type: String, required: false },
  zipcode: { type: String, required: false },
  country:{ type: String, required:true},
  contactNo:{ type:String, require:true},
   bankAccountNo:{
    type: String,
    required: true,
    
   },
   ifscCode:{
    type: String,
    required: true,
   },
   tds:{
    type: String,
    required: true,
   },
   commission:{
    type: String,
    required: true,
   },
  bankName:{
    type: String,
    required: true,
   },
  aadhaarDetails: {
    aadhaarNo: { type: String, required: true },
    aadhaarFile:{type:String},
   
  },
  panDetails: {
    panNo: { type: String, required: false },
    panFile:{type:String}
  },
  epfoDetails: {
    epfoNo: { type: String, required: false },
     epfoFile:{type:String}
  }, 
  esicDetails: {
    esicNo: { type: String, required: false },
    esicFile:{type:String}
  },
  emailId: { type: String, required: false },
  password: { type: String, required: false }
});

module.exports = mongoose.model('WeaverEmployee', weaverEmployeeSchema);
