<div class="container-fluid vh-100">

    <!-- start page title -->
    <div class="py-3 py-lg-4">
        <div class="row">
            <div class="col-lg-6">
                <h4 class="page-title mb-0">Add Size Code</h4>
            </div>

        </div>
        <hr>

        <!-- Form row -->
        <div class="row">
            <div class="col-xl-12">
                <div class="card">
                    <div class="card-body">



                        <form [formGroup]="frmSizeCode" (ngSubmit)="addSize()">
                            <div class="row">
                                <div class="mb-2 col-md-2">

                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Size in CM</mat-label>
                                        <mat-select formControlName="size"  (valueChange)="getSize($event)">
                                            @for (size of uniqueSizeList; track size) {
                                              <mat-option [value]="size">{{size}}</mat-option>
                                            }
                                          </mat-select>

                                    </mat-form-field>
                                </div>
                                    <div class="mb-2 col-md-2">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Area</mat-label>
                                            <input matInput placeholder="Area" formControlName="unit">

                                        </mat-form-field>
                                    </div>


                                <div class="mb-2 col-md-2">
                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Sequence</mat-label>
                                        <input matInput placeholder="Sequence" formControlName="sequenceCode">

                                    </mat-form-field>
                                </div>
                                <div class="mb-2 col-md-2">
                                    <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Size Code</mat-label>
                                        <input matInput placeholder="Size Code" formControlName="sizeCode">

                                    </mat-form-field>
                                </div>



                                <div class="col-md-3">

                                    <button type="submit" class="btn btn-primary mt-4">Save</button>
                                    &nbsp;
                                    <button type="button" (click)="closeDialogWithData()" class="btn btn-secondary mt-4">Close</button>

                                </div>


                            </div>


                        </form>
                    </div>
                </div>
            </div>

             <div class="row">
            <div class="col-xl-12">

            <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">

  <!--- Note that these columns can be defined in any order.
        The actual rendered columns are set as a property on the row definition" -->

  <!-- Position Column -->
  <ng-container matColumnDef="id">
    <th mat-header-cell *matHeaderCellDef> No. </th>
    <td mat-cell *matCellDef="let element"> {{element.index}} </td>
  </ng-container>

  <!-- Name Column -->
  <ng-container matColumnDef="sequenceCode">
    <th mat-header-cell *matHeaderCellDef> Sequence Code
    </th>
    <td mat-cell *matCellDef="let element"> {{element.sequenceCode}} </td>
  </ng-container>

  <!-- Weight Column -->
  <ng-container matColumnDef="sizeCode">
    <th mat-header-cell *matHeaderCellDef> Size Code </th>
    <td mat-cell *matCellDef="let element"> {{element.sizeCode}} </td>
  </ng-container>

  <!-- Symbol Column -->
  <ng-container matColumnDef="size">
    <th mat-header-cell *matHeaderCellDef> Size </th>
    <td mat-cell *matCellDef="let element"> {{element.size}} </td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
</table>


                </div>
                </div>
