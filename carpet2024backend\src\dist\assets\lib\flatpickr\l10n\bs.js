(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.bs = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Bosnian = {
      firstDayOfWeek: 1,
      weekdays: {
          shorthand: ["<PERSON>", "Pon", "Uto", "Sri", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"],
          longhand: [
              "Ned<PERSON><PERSON><PERSON>",
              "Ponedjeljak",
              "Utorak",
              "Srije<PERSON>",
              "Četvrtak",
              "Petak",
              "Subota",
          ],
      },
      months: {
          shorthand: [
              "Jan",
              "Feb",
              "Mar",
              "Apr",
              "Maj",
              "<PERSON>",
              "Jul",
              "Avg",
              "Sep",
              "<PERSON><PERSON>",
              "Nov",
              "<PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>",
              "April",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>vgus<PERSON>",
              "Septem<PERSON>",
              "<PERSON><PERSON>bar",
              "Novembar",
              "Decembar",
          ],
      },
      time_24hr: true,
  };
  fp.l10ns.bs = Bosnian;
  var bs = fp.l10ns;

  exports.Bosnian = Bosnian;
  exports.default = bs;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
