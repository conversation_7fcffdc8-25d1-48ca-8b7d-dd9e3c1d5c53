Stack trace:
Frame         Function      Args
0007FFFF9F20  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8E20) msys-2.0.dll+0x1FEBA
0007FFFF9F20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA1F8) msys-2.0.dll+0x67F9
0007FFFF9F20  000210046832 (000210285FF9, 0007FFFF9DD8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F20  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9F20  0002100690B4 (0007FFFF9F30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA200  00021006A49D (0007FFFF9F30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8DFB60000 ntdll.dll
7FF8DE8F0000 KERNEL32.DLL
7FF8DD510000 KERNELBASE.dll
7FF8DDE10000 USER32.dll
7FF8DCE10000 win32u.dll
7FF8DE4B0000 GDI32.dll
7FF8DCE40000 gdi32full.dll
7FF8DCF80000 msvcp_win.dll
7FF8DD330000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8DE9C0000 advapi32.dll
7FF8DE3B0000 msvcrt.dll
7FF8DEA80000 sechost.dll
7FF8DE4F0000 RPCRT4.dll
7FF8DC3D0000 CRYPTBASE.DLL
7FF8DCCB0000 bcryptPrimitives.dll
7FF8DF1B0000 IMM32.DLL
