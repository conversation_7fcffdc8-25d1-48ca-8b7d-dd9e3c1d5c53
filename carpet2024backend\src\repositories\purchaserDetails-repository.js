const purchaserDetails = require('../model/phase-3/purchaserDetails');

const createpurchaserDetails = async (data) => {
  return await purchaserDetails.create(data);
};

const getAllpurchaserDetails = async () => {
  return await purchaserDetails.find().populate('Material').exec();
};

const getpurchaserDetailsById = async (id) => {
  return await purchaserDetails.findById(id).exec();
};

const updatepurchaserDetails = async (id, data) => {
  return await purchaserDetails.findByIdAndUpdate(id, data, { new: true })
};

const deletepurchaserDetails = async (id) => {
  return await purchaserDetails.findByIdAndDelete(id);
};

module.exports = {
  createpurchaserDetails,
  getAllpurchaserDetails,
  getpurchaserDetailsById,
  updatepurchaserDetails,
  deletepurchaserDetails
};
