<div class="row px-3 py-5">

    <div class="col-xl-12">
        <div class="card">
            <div class="card-body">

                <form [formGroup]="frmImportInvoice" (ngSubmit)="importInvoice()">
                    <div class="row">
                        <div class="mb-2 col-md-3">
                            <!-- <mat-form-field appearance="outline" class="example-full-width">
                            <mat-label>Import no.</mat-label>
                            <input matInput placeholder="Import no." formControlName="importNo">
                            
                          </mat-form-field> -->
                            <mat-form-field appearance="outline">
                                <mat-label>Importer No.</mat-label>
                                <mat-select formControlName="importNo" [(value)]="selectedImportNo">

                                    @for (importerNo of impotererNo; track importerNo) {
                                    <mat-option [value]="importerNo">{{importerNo}}</mat-option>



                                    }
                                </mat-select>
                            </mat-form-field>



                        </div>
                        <div class="mb-2 col-md-2">

                            <mat-form-field appearance="outline" class="example-full-width">
                                <mat-label>Importer Name</mat-label>
                                <mat-select formControlName="importerName" (valueChange)="getImporterName($event)">
                                    <mat-option *ngFor="let importerNames of importerName" value="{{importerNames.
                                  importerName}}">{{importerNames.importerName}}</mat-option>

                                </mat-select>
                            </mat-form-field>


                        </div>


                        <div class="mb-2 col-md-2">

                            <mat-form-field appearance="outline" class="example-full-width">
                                <mat-label>Invoice no.</mat-label>
                                <input matInput placeholder="Invoice no." formControlName="invoiceNo" readonly>

                            </mat-form-field>

                        </div>
                        <div class="col-md-3 mb-2">
                            <mat-form-field appearance="outline">
                                <mat-label>Choose a date</mat-label>
                                <input matInput [matDatepicker]="picker" formControlName="date">

                                <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                                <mat-datepicker #picker></mat-datepicker>
                            </mat-form-field>

                        </div>



                        <div class="mb-2 col-md-2">

                            <mat-form-field appearance="outline" class="example-full-width">
                                <mat-label>Area of Unit</mat-label>

                                <mat-select formControlName="areaOfUnit">
                                    <mat-option *ngFor="let area of areaOfUnit" value="{{area}}">{{area}}</mat-option>

                                </mat-select>
                            </mat-form-field>
                        </div>


                    </div>

                    <div class="row">
                        <div class="mb-2 col-md-2">
                            <mat-form-field appearance="outline" class="example-full-width">
                                <mat-label>Curency</mat-label>
                                <mat-select formControlName="curency" (valueChange)="selectCurrency($event)">
                                    <mat-option *ngFor="let currency of currencyArray"
                                        value="{{currency}}">{{currency}}</mat-option>

                                </mat-select>
                            </mat-form-field>
                        </div>
                        <div class="mb-2 col-md-3">

                            <mat-form-field appearance="outline" class="example-full-width">
                                <mat-label>Quantity</mat-label>
                                <input matInput placeholder="Quantity" formControlName="quantity" readonly>
                            </mat-form-field>
                        </div>

                        <div class="mb-2 col-md-3">

                            <mat-form-field appearance="outline" class="example-full-width">
                                <mat-label>Total Area</mat-label>
                                <input matInput placeholder="00.00" formControlName="totalArea" readonly>
                            </mat-form-field>
                        </div>

                        <div class="mb-2 col-md-3">

                            <mat-form-field appearance="outline" class="example-full-width">
                                <mat-label>Amount</mat-label>
                                <input matInput placeholder="00.00" formControlName="amount" readonly>
                            </mat-form-field>
                        </div>

                        <div class="mb-2 col-md-3">
                            <label for="inputPassword4" class="form-label">Upload Invoice ( .Pdf )</label>
                            <input type="file" class="form-control" accept=".jpeg, jpg,.pdf"
                                (change)="uploadpdf($event)" />

                        </div>

                        <div class="mb-2 col-md-3">
                            <label for="inputPassword4" class="form-label">Upload B/L ( .Pdf )</label>
                            <input type="file" class="form-control" accept=".jpeg, jpg,.pdf"
                                (change)="uploadpdf2($event)" />
                        </div>
                        <div class="mb-2 col-md-3">
                            <label for="inputPassword4" class="form-label">Upload CSV ( Excel )</label>
                            <input type="file" #chooseExcel class="form-control" (change)="readExcel($event)" />
                        </div>

                        <div class="col-md-3">

                            <button type="submit" class="btn btn-primary mt-4">Save</button>
                        </div>

                    </div>



                </form>
            </div>
        </div>
    </div>

    <div class="col-xl-12">

        <table class="table">
            <tr>
                <th style="width: 80px; padding-left: 10px;"> S. No </th>
                <th style="width: 250px ;padding-left: 10px;"> Quality </th>
                <th style="width: 250px ;padding-left: 10px;"> Design </th>
                <th style="width: 77px ;padding-right: 8px;" class="text-end"> Pcs </th>
                <th style="width: 90px; padding-right: 8px;" class="text-end"> Area </th>
                <th class="text-end ; padding-right: 8px;" style="width: 80px"> Rate </th>
                <th class="text-end ; padding-right: 8px;" style="width: 100px"> Amount </th>

            </tr>

            <tr *ngFor="let data of excelSummaryData; let i =  index">
                <td style=" padding-left: 10px;">{{i+1}}</td>
                <td style="text-transform: uppercase; padding-left: 10px;">{{data.quality}}</td>
                <td style="text-transform: uppercase; padding-left: 10px;">{{data.design}}</td>
                <td class="text-end" style="padding-right: 8px;">{{data.quantity}}</td>
                <td class="text-end" style="padding-right: 8px;">{{data.area}}</td>
                <td class="text-end" style="padding-right: 8px;">{{data.rate}}</td>
                <td class="text-end" style="padding-right: 8px;">{{data.amount}}</td>

            </tr>

            <tr>
                
                <td colspan="4" class="text-end" style="padding-right: 8px;"> Total - {{totalPcs}}
                </td>
                <td class="text-end" style="padding-right: 8px;"> {{totalArea}}</td>
                <td class="text-end" style="padding-right: 8px;"> {{currencySign}}</td>
                <td class="text-end" style="padding-right: 8px;"> {{totalAmount}}</td>

            </tr>


        </table>


        <hr class="hr-text" data-content="Packing list">

        <mat-form-field appearance="outline">
            <mat-label>Search</mat-label>
            <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Rit" #input>
        </mat-form-field>
        <table mat-table [dataSource]="dataSource" class="mat-elevation-z8" style="width: max-content;">

            <!--- Note that these columns can be defined in any order.
                  The actual rendered columns are set as a property on the row definition" -->
          
            <!-- Position Column -->
            <ng-container matColumnDef="Date">
              <th mat-header-cell *matHeaderCellDef> Date </th>
              <td mat-cell *matCellDef="let element"> {{element.Date}} </td>
            </ng-container>
            
            

            <ng-container matColumnDef="Design">
                <th mat-header-cell *matHeaderCellDef> Design </th>
                <td mat-cell *matCellDef="let element"> {{element.Design}} </td>
              </ng-container>

              <ng-container matColumnDef="Colour">
                <th mat-header-cell *matHeaderCellDef> Colour </th>
                <td mat-cell *matCellDef="let element"> {{element.Colour}} </td>
              </ng-container>

              <ng-container matColumnDef="Width">
                <th mat-header-cell *matHeaderCellDef> Width </th>
                <td mat-cell *matCellDef="let element"> {{element.Width}} </td>
              </ng-container>
              <ng-container matColumnDef="Length">
                <th mat-header-cell *matHeaderCellDef> Length </th>
                <td mat-cell *matCellDef="let element"> {{element.Length}} </td>
              </ng-container>
              <ng-container matColumnDef="Area">
                <th mat-header-cell *matHeaderCellDef> Area </th>
                <td mat-cell *matCellDef="let element"> {{element.Area}} </td>
              </ng-container>
              <ng-container matColumnDef="InvoiceNo">
                <th mat-header-cell *matHeaderCellDef> InvoiceNo </th>
                <td mat-cell *matCellDef="let element"> {{element.InvoiceNo}} </td>
              </ng-container>
              <ng-container matColumnDef="ImpoterCode">
                <th mat-header-cell *matHeaderCellDef> ImpoterCode </th>
                <td mat-cell *matCellDef="let element"> {{element.ImpoterCode}} </td>
              </ng-container>
              <ng-container matColumnDef="Remark">
                <th mat-header-cell *matHeaderCellDef> Remark </th>
                <td mat-cell *matCellDef="let element"> {{element.Remark}} </td>
              </ng-container>



            <!-- Name Column -->
            <ng-container matColumnDef="BaleNo">
              <th mat-header-cell *matHeaderCellDef> BaleNo </th>
              <td mat-cell *matCellDef="let element"> {{element.BaleNo}} </td>
            </ng-container>
          
            <!-- Weight Column -->
            <ng-container matColumnDef="PcsNo">
              <th mat-header-cell *matHeaderCellDef> PcsNo </th>
              <td mat-cell *matCellDef="let element"> {{element.PcsNo}} </td>
            </ng-container>
          
            <!-- Symbol Column -->
            <ng-container matColumnDef="Quality">
              <th mat-header-cell *matHeaderCellDef> Quality </th>
              <td mat-cell *matCellDef="let element"> {{element.Quality}} </td>
            </ng-container>
          
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
          <mat-paginator [length]="100"
              [pageSize]="10"
              [pageSizeOptions]="[5, 10, 25, 100]"
              aria-label="Select page">
</mat-paginator>

          
        <!-- <table class="table">
            <tr>
                <th>
                    Date
                </th>
                <th>
                    Bale No.
                </th>
                <th>
                    Pcs No.
                </th>
                <th>
                    Quality
                </th>
                <th>
                    Design
                </th>
                <th>
                    Colour
                </th>
                <th>
                    Width
                </th>
                <th>
                    Length
                </th>
                <th>
                    Area
                </th>
                <th>
                    Invoice No.
                </th>
                <th>
                    Importer Code
                </th>
                <th>
                    Remarks
                </th>
            </tr>

            <tr *ngFor="let data of excelData">
                <td>{{data.Date}}</td>
                <td>{{data.BaleNo
                    }}</td>
                <td>{{data.PcsNo}}</td>
                <td>{{data.Quality
                    }}</td>
                <td>{{data.Design
                    }}</td>
                <td>{{data.Colour
                    }}</td>
                <td>{{data.Width
                    }}</td>
                <td>{{data.Length
                    }}</td>
                <td>{{data.Area
                    }}</td>
                <td>{{data.InvoiceNo
                    }}</td>
                <td>{{data.ImpoterCode
                    }}</td>
                <td> </td>
            </tr>



        </table> -->


    </div>


</div>