const branchRepository = require('../repositories/branch-repository');

class BranchService {
  async createBranch(branchData) {
    return await branchRepository.create(branchData);
  }

  async getAllBranches() {
    return await branchRepository.findAll();
  }

  async getBranchById(id) {
    return await branchRepository.findById(id);
  }

  async updateBranch(id, branchData) {
    return await branchRepository.update(id, branchData);
  }

  async deleteBranch(id) {
    return await branchRepository.delete(id);
  }
}

module.exports = new BranchService();
