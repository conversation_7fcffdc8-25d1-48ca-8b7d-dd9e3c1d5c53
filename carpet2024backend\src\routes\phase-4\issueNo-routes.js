const express = require('express');
const issueNoController = require('../../controller/manifacturing/issueno.controller');

const router = express.Router();

router.post('/issueNo', issueNoController.create);
router.get('/issueNo', issueNoController.getAll);
router.get('/issueNo/:id', issueNoController.getById);
router.put('/issueNo/:id', issueNoController.update);
router.delete('/issueNo/:id', issueNoController.delete);

module.exports = router;
