import { Component, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import { SweetalertService } from '../../../services/sweetalert.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { environment } from '../../../../environments/environment.development';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { MatDialog } from '@angular/material/dialog';
import { DesignComponent } from '../design/design.component';
declare var bootstrap: any;
export interface UserData {
  id: string;
  index: number;
  quality: string;
  addDesign: string;
  color: string;
  groundColour: string;
  borderColour: string;
  sizeMaster: string;
  uploadFile: string;
}

const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-map-master',
  templateUrl: './map-master.component.html',
  styleUrl: './map-master.component.css',
})
export class MapMasterComponent {
  displayedColumns: string[] = [
    'id',
    'quality',
    'addDesign',
    // 'color',
    // 'groundColour',
    'borderColour',
    'sizeMaster',
    'uploadFile',
    'action',
  ];
  imgUrl: any;
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);
  constructor(
    private service: MasterService,
    private alert: SweetalertService,
    private fb: FormBuilder,
    private customeService: CustomeServiceService,
    private sanitizer: DomSanitizer,
    private matDig: MatDialog
  ) {}
  // designId: string | undefined;
  ngOnInit(): void {
    this.frmMapMaster = this.fb.group({
      quality: [],
      addDesign: [],
      groundColour: [],
      sizeMaster: [],
      uploadFile: [],
    });

    this.qualitys();
    this.getsSizeList();
    this.getsMapMaster();
    this.getsDesign();
  }
  qualityList: any = [];
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  color!: string;
  size!: string;
  design!: string;
  editId!: string;
  isUpdated: boolean = false;
  groundId!: string;
  borderId!: string;
  sizeList: any = [];
  frmMapMaster!: FormGroup;
  // 5006089 5005758 5005643
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  _qualitys: any = [];
  qualitys() {
    this.service.getqualityList().subscribe({
      
      next: (value: any) => {
        debugger
        this._qualitys = value;
        value.map((v: any) => {
          this.qualityList.push({
            quality: v.quality,
            id: v._id,
          });
        });

        this.qualityList.sort((a: any, b: any) => {
          let numA = this.extractFirstNumber(a.quality);
          let numB = this.extractFirstNumber(b.quality);
          return numA - numB;
        });
      },
      error: (err) => {},
    });
  }
  extractFirstNumber(str: any) {
    let parts = str.split('x').map((part: any) => parseInt(part.trim(), 10));
    return parts[0];
  }
  qty: any;
  getsSizeList() {
    this.service.getAllSizesList().subscribe({
      next: (value: any) => {
        debugger;
        value.map((x: any) => {
          this.sizeList.push({
            id: x._id,
            size: x.sizeInYard,
          });
        });
        console.log(value);
      },
    });
  }

  selectSize(id: string) {
    debugger;
    this.size = '';
    this.size = id;
  }
  file: any;
  chooseFile(file: any) {
    this.file = file.target.files[0];
  }
  private createFormData(frmData: any): FormData {
    let formData = new FormData();
    formData.append('quality', this.qty ? this.qty : frmData.quality);
    formData.append('addDesign', this.designId);
    // formData.append('color', this.color ? this.color : frmData.color);
    formData.append('groundColour', this.groundId);
    formData.append('borderColour', this.borderId);
    formData.append('sizeMaster', this.size ? this.size : frmData.sizeMaster);
    formData.append('uploadFile', this.file);
    return formData;
  }
  addMapMaster() {
    debugger;
    let frmData = this.frmMapMaster.value;
    let formData = this.createFormData(frmData);
    this.service.addMapMaster(formData).subscribe({
      next: (value: any) => {
        this.alert.success('success', 'Map master has been saved successfull');
        this.clear();
        this.getsMapMaster();
      },
      error: (err) => {
        this.alert.error('warning', ' Something went wrong');
      },
    });
  }
  update() {
    let frmData = this.frmMapMaster.value;
    let formData = this.createFormData(frmData);
    debugger;
    this.service.updateMapMaster(this.editId, formData).subscribe({
      next: (value: any) => {
        this.alert.success('success', 'Map master has been saved successfull');
        this.clear();
        this.getsMapMaster();
      },
      error: (err) => {
        this.alert.error('warning', ' Something went wrong');
      },
    });
  }
  imageUrl!: SafeUrl;

  getSafeUrl(url: string): SafeUrl {
    return this.sanitizer.bypassSecurityTrustUrl(url);
  }
  getsMapMaster() {
    this.service.getsMapMaster().subscribe({
      next: (value: any) => {
        if (value) {
          ELEMENT_DATA.length = 0;
          value.map((v: any, i: number) => {
            debugger;

            const imageUrl = `${environment.imageUrl}${v.uploadedFile}`;
            let img: any = this.getSafeUrl(imageUrl);
            debugger;
            ELEMENT_DATA.push({
              id: v._id,
              index: i + 1,
              quality: v.quality?.quality || 'N/A',
              addDesign: v.addDesign?.design || 'N/A',
              color: v.color?.newColor || 'N/A',
              groundColour: v.groundColour?.newColor,
              borderColour: v.borderColour?.newColor,
              sizeMaster: v.sizeMaster?.sizeInYard || 'N/A',
              uploadFile: imageUrl,
            });
          });
          this.dataSource = new MatTableDataSource(ELEMENT_DATA);
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
          return;
        }
      },
    });
  }

  readPdf(imageUrl: any) {
    debugger;
    window.open(imageUrl, '_blank');
  }

  edit(id: string) {
    this.service.getMapMaster(id).subscribe({
      next: (value: any) => {
        this.editId = id;
        this.isUpdated = true;
        let designData = this.allDesign.find(
          (x: any) => x._id === value.addDesign
        );
        debugger;
        this.groundId=value.groundColour;
this.borderId=value.borderColour;
this.designId=value.addDesign;
        this.frmMapMaster.patchValue({
          quality: value.quality,
          addDesign: designData.design,
          groundColour:
              designData.ground.newColor+'/'+designData.border.newColor,
          sizeMaster: value.sizeMaster,
        });
      },
    });
  }
  delete(id: string) {
    this.alert.confirm({}).then((result: any) => {
      if (result.isConfirmed) {
        this.service.deleteMapMaster(id).subscribe({
          next: (value: any) => {
            this.alert.success('success', 'Record has been deleted ');

            this.getsMapMaster();
          },
          error: (err) => {
            this.alert.error('warning', ' Something went wrong');
          },
        });
      }
    });
  }

  clear() {
    this.frmMapMaster.reset();
  }
  selectedImageUrl: any;
  openModal(imageUrl: string): void {
    this.selectedImageUrl = imageUrl;
    const modal = new bootstrap.Modal(document.getElementById('myModal'), {});
    modal.show();
  }
  designId!: string;
  selectQuality(qty: any) {
    debugger
    let quality = this.qualityList.find((x: any) => x.id === qty);
    let dialogRef = this.matDig.open(DesignComponent, {
      disableClose: true,
      width: '500px',
      data: { quality: quality },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        debugger;
        this.groundId = '';
        this.borderId = '';
        this.designId = '';
        this.frmMapMaster.get('addDesign')?.setValue(result.design);
        this.frmMapMaster
          .get('groundColour')
          ?.setValue(result.ground + '/' + result.border);
        this.groundId = result.groundId;
        this.borderId = result.borderId;
        this.designId = result._id;
      }
      console.log('result result result', result);
    });
  }
  allDesign: any;
  getsDesign() {
    debugger
    this.service.getAllDesignList().subscribe((resp: any) => {
      console.log(resp);
      this.allDesign = resp;
    });
  }
}
