<div class="row">
  <div class="col-lg-6 text-center">
<h4>Container Despatch</h4>
  </div>

  <div class="col-12">
    <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">

      <!--- Note that these columns can be defined in any order.
            The actual rendered columns are set as a property on the row definition" -->

      <!-- Position Column -->
      <ng-container matColumnDef="impoterNo">
        <th mat-header-cell *matHeaderCellDef>Impoter No </th>
        <td mat-cell *matCellDef="let element"> {{element.impoterNo}} </td>
      </ng-container>
      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef>Sr. No </th>
        <td mat-cell *matCellDef="let element"> {{element.index}} </td>
      </ng-container>
      <!-- Name Column -->
      <ng-container matColumnDef="chooseAdate">
        <th mat-header-cell *matHeaderCellDef> Date </th>
        <td mat-cell *matCellDef="let element"> {{element.chooseAdate}} </td>
      </ng-container>



        <ng-container matColumnDef="totalNumberOfPcs">
          <th mat-header-cell *matHeaderCellDef>  Pcs </th>
          <td mat-cell *matCellDef="let element"> {{element.totalNumberOfPcs}} </td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="noOfInvoice">
          <th mat-header-cell *matHeaderCellDef>  Invoice No. </th>
          <td mat-cell *matCellDef="let element"> {{element.noOfInvoice}} </td>
        </ng-container>

        <!-- Weight Column -->
        <ng-container matColumnDef="linerDetails">
          <th mat-header-cell *matHeaderCellDef> Liner Details </th>
          <td mat-cell *matCellDef="let element"> {{element.linerDetails}} </td>
        </ng-container>

        <!-- Symbol Column -->
        <ng-container matColumnDef="containerNumber">
          <th mat-header-cell *matHeaderCellDef> Container No </th>
          <td mat-cell *matCellDef="let element"> {{element.containerNumber}} </td>
        </ng-container>







        <ng-container matColumnDef="totalQuantity">
          <th mat-header-cell *matHeaderCellDef>  Quantity </th>
          <td mat-cell *matCellDef="let element"> {{element.totalQuantity}} </td>
        </ng-container>
        <ng-container matColumnDef="totalAmount">
          <th mat-header-cell *matHeaderCellDef> Amount </th>
          <td mat-cell *matCellDef="let element"> {{element.totalAmount}} </td>
        </ng-container>


      <!-- Weight Column -->
      <ng-container matColumnDef="country">
        <th mat-header-cell *matHeaderCellDef> Country </th>
        <td mat-cell *matCellDef="let element"> {{element.country}} </td>
      </ng-container>

      <!-- Symbol Column -->
      <ng-container matColumnDef="currency">
        <th mat-header-cell *matHeaderCellDef> currency </th>
        <td mat-cell *matCellDef="let element"> {{element.currency}} </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>


  </div>
</div>
