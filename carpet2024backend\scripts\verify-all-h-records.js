// Script to verify all H- format records are imported
const mongoose = require('mongoose');

const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

// All expected H- format records (393 total)
const expectedRecords = [
  'H-220169', 'H-220880', 'H-220881', 'H-220012', 'H-220882', 'H-220019', 'H-220011', 'H-220130', 'H-220016', 'H-220111',
  'H-220168', 'H-220129', 'H-220112', 'H-220017', 'H-220018', 'H-220009', 'H-220010', 'H-210534', 'H-210325', 'H-210966',
  'H-210977', 'H-210535', 'H-210223', 'H-210967', 'H-210735', 'H-210178', 'H-210736', 'H-210737', 'H-210738', 'H-210739',
  'H-210740', 'H-210741', 'H-210742', 'H-210743', 'H-210744', 'H-210745', 'H-210746', 'H-210747', 'H-210748', 'H-210749',
  'H-210750', 'H-210751', 'H-210752', 'H-210753', 'H-210754', 'H-210755', 'H-210756', 'H-210757', 'H-210758', 'H-210759',
  'H-210760', 'H-210761', 'H-210762', 'H-210763', 'H-210764', 'H-210765', 'H-210766', 'H-210767', 'H-210768', 'H-210769',
  'H-210770', 'H-210771', 'H-210772', 'H-210773', 'H-210774', 'H-210775', 'H-210776', 'H-210777', 'H-210778', 'H-210779',
  'H-210780', 'H-210781', 'H-210782', 'H-210783', 'H-210784', 'H-210785', 'H-210786', 'H-210787', 'H-210788', 'H-210789',
  'H-210790', 'H-210791', 'H-210792', 'H-210793', 'H-210794', 'H-210795', 'H-210796', 'H-210797', 'H-210798', 'H-210799',
  'H-210800', 'H-210801', 'H-210802', 'H-210803', 'H-210804', 'H-210805', 'H-210806', 'H-210807', 'H-210808', 'H-210809',
  'H-210810', 'H-210811', 'H-210812', 'H-210813', 'H-210814', 'H-210815', 'H-210816', 'H-210817', 'H-210818', 'H-210819',
  'H-210820', 'H-210821', 'H-210822', 'H-210823', 'H-210824', 'H-210825', 'H-210826', 'H-210827', 'H-210828', 'H-210829',
  'H-210830', 'H-210831', 'H-210832', 'H-210833', 'H-210834', 'H-210835', 'H-210836', 'H-210837', 'H-210838', 'H-210839',
  'H-210840', 'H-210841', 'H-210842', 'H-210843', 'H-210844', 'H-210845', 'H-210846', 'H-210847', 'H-210848', 'H-210849',
  'H-210850', 'H-210851', 'H-210852', 'H-210853', 'H-210854', 'H-210855', 'H-210856', 'H-210857', 'H-210858', 'H-210859',
  'H-210860', 'H-210861', 'H-210862', 'H-210863', 'H-210864', 'H-210865', 'H-210866', 'H-210867', 'H-210868', 'H-210869',
  'H-210870', 'H-210871', 'H-210872', 'H-210873', 'H-210874', 'H-210875', 'H-210876', 'H-210877', 'H-210878', 'H-210879',
  'H-210880', 'H-210881', 'H-210882', 'H-210883', 'H-210884', 'H-210885', 'H-210886', 'H-210887', 'H-210888', 'H-210889',
  'H-210890', 'H-210891', 'H-210892', 'H-210893', 'H-210894', 'H-210895', 'H-210896', 'H-210897', 'H-210898', 'H-210899',
  'H-210900', 'H-210901', 'H-210902', 'H-210903', 'H-210904', 'H-210905', 'H-210906', 'H-210907', 'H-210908', 'H-210909',
  'H-210910', 'H-210911', 'H-210912', 'H-210913', 'H-210914', 'H-210915', 'H-210916', 'H-210917', 'H-210918', 'H-210919',
  'H-210920', 'H-210921', 'H-210922', 'H-210923', 'H-210924', 'H-210925', 'H-210926', 'H-210927', 'H-210928', 'H-210929',
  'H-210930', 'H-210931', 'H-210932', 'H-210933', 'H-210934', 'H-210935', 'H-210936', 'H-210937', 'H-210938', 'H-210939',
  'H-210940', 'H-210941', 'H-210942', 'H-210943', 'H-210944', 'H-210945', 'H-210946', 'H-210947', 'H-210948', 'H-210949',
  'H-210950', 'H-210951', 'H-210952', 'H-210953', 'H-210954', 'H-210955', 'H-210956', 'H-210957', 'H-210958', 'H-210959',
  'H-210960', 'H-210961', 'H-210962', 'H-210963', 'H-210964', 'H-210965', 'H-220001', 'H-220002', 'H-220003', 'H-220004',
  'H-220005', 'H-220006', 'H-220007', 'H-220008', 'H-130656', 'H-140052', 'H-140108', 'H-140109', 'H-140126', 'H-140128',
  'H-140174', 'H-140231', 'H-140232', 'H-140233', 'H-140234', 'H-140235', 'H-140236', 'H-140237', 'H-140238', 'H-140239',
  'H-140240', 'H-140241', 'H-140242', 'H-140243', 'H-140244', 'H-140245', 'H-140246', 'H-140247', 'H-140248', 'H-140249',
  'H-140250', 'H-140251', 'H-140252', 'H-140253', 'H-140254', 'H-140255', 'H-140256', 'H-140257', 'H-140258', 'H-140259',
  'H-140260', 'H-140261', 'H-140262', 'H-140263', 'H-140264', 'H-140265', 'H-140266', 'H-140267', 'H-140268', 'H-140269',
  'H-140270', 'H-140271', 'H-140272', 'H-140273', 'H-140274', 'H-140275', 'H-140276', 'H-140277', 'H-140278', 'H-140279',
  'H-140280', 'H-140281', 'H-140282', 'H-140283', 'H-140284', 'H-140285', 'H-140286', 'H-140287', 'H-140288', 'H-140289',
  'H-140290', 'H-140291', 'H-140292', 'H-140293', 'H-140294', 'H-140295', 'H-140296', 'H-140297', 'H-140298', 'H-140299',
  'H-140300', 'H-170001', 'H-170002', 'H-170003', 'H-180001', 'H-180002', 'H-180003', 'H-180004', 'H-180005', 'H-180006',
  'H-180007', 'H-180008', 'H-180009', 'H-180010', 'H-180011', 'H-180012', 'H-180013', 'H-180014', 'H-180015', 'H-180016',
  'H-180017', 'H-180018', 'H-180019', 'H-180020', 'H-180021', 'H-180022', 'H-180023', 'H-180024', 'H-180025', 'H-180026',
  'H-180027', 'H-180028', 'H-180029', 'H-180030', 'H-180031', 'H-180032', 'H-180033', 'H-180034', 'H-180035', 'H-180036',
  'H-180037', 'H-180038', 'H-180039', 'H-180040', 'H-180041', 'H-180042', 'H-180043', 'H-180044', 'H-180045', 'H-180046',
  'H-180047', 'H-180048', 'H-180049', 'H-180050', 'H-180051', 'H-180052', 'H-180053', 'H-180054', 'H-180055', 'H-180056',
  'H-180057', 'H-180058', 'H-180059', 'H-180060', 'H-180061', 'H-180062', 'H-180063', 'H-180064', 'H-180065', 'H-180066',
  'H-180067', 'H-180068', 'H-180069', 'H-180070', 'H-180071', 'H-180072', 'H-180073', 'H-180074', 'H-180075', 'H-180076',
  'H-180077', 'H-180078', 'H-180079', 'H-180080', 'H-180081', 'H-180082', 'H-180083', 'H-180084', 'H-180085', 'H-180086',
  'H-180087', 'H-180088', 'H-180089', 'H-180090', 'H-180091', 'H-180092', 'H-180093', 'H-180094', 'H-180095', 'H-180096',
  'H-180097', 'H-180098', 'H-180099', 'H-180100', 'H-180101', 'H-180102', 'H-180103', 'H-180104', 'H-180105', 'H-180106',
  'H-180107', 'H-180108', 'H-180109', 'H-180110', 'H-180111', 'H-180112', 'H-180113', 'H-180114', 'H-180115', 'H-180116',
  'H-180117', 'H-180118', 'H-180119', 'H-180120', 'H-180121', 'H-180122', 'H-180123', 'H-180124', 'H-180125', 'H-180126',
  'H-180127', 'H-180128', 'H-180129', 'H-180130', 'H-180131', 'H-180132', 'H-180133', 'H-180134', 'H-180135', 'H-180136',
  'H-180137', 'H-180138', 'H-180139', 'H-180140', 'H-180141', 'H-180142', 'H-180143', 'H-180144', 'H-180145', 'H-180146',
  'H-180147', 'H-180148', 'H-180149', 'H-180150', 'H-180151', 'H-180152', 'H-180153', 'H-180154', 'H-180155', 'H-180156',
  'H-180157', 'H-180158', 'H-180159', 'H-180160', 'H-180161', 'H-180162', 'H-180163', 'H-180164', 'H-180165', 'H-180166',
  'H-180167', 'H-180168', 'H-180169', 'H-180170', 'H-180171', 'H-180172', 'H-180173', 'H-180174', 'H-180175', 'H-180176',
  'H-180177', 'H-180178', 'H-180179', 'H-180180', 'H-180181', 'H-180182', 'H-180183', 'H-180184', 'H-180185', 'H-180186',
  'H-180187', 'H-180188', 'H-180189', 'H-180190', 'H-180191', 'H-180192', 'H-180193', 'H-180194', 'H-180195', 'H-180196',
  'H-180197', 'H-180198', 'H-180199', 'H-180200', 'H-180201', 'H-180202', 'H-180203', 'H-180204', 'H-180205', 'H-180206',
  'H-180207', 'H-180208', 'H-180209', 'H-180210', 'H-180211', 'H-180212', 'H-180213', 'H-180214', 'H-180215', 'H-180216',
  'H-180217', 'H-180218', 'H-180219', 'H-180220', 'H-180221', 'H-180222', 'H-180223', 'H-180224', 'H-180225', 'H-180226',
  'H-180227', 'H-180228', 'H-180229', 'H-180230', 'H-180231', 'H-180232', 'H-180233', 'H-180234', 'H-180235', 'H-180236',
  'H-180237', 'H-180238', 'H-180239', 'H-180240', 'H-180241', 'H-180242', 'H-180243', 'H-180244', 'H-180245', 'H-180246',
  'H-180247', 'H-180248', 'H-180249', 'H-180250', 'H-180251', 'H-180252', 'H-180253', 'H-180254', 'H-180255', 'H-180256',
  'H-180257', 'H-180258', 'H-180259', 'H-180260', 'H-180261', 'H-180262', 'H-180263', 'H-180264', 'H-180265', 'H-180266',
  'H-180267', 'H-180268', 'H-180269', 'H-180270', 'H-180271', 'H-180272', 'H-180273', 'H-180274', 'H-180275', 'H-180276',
  'H-180277', 'H-180278', 'H-180279', 'H-180280', 'H-180281', 'H-180282', 'H-180283', 'H-180284', 'H-180285', 'H-180286',
  'H-180287', 'H-180288', 'H-180289', 'H-180290', 'H-180291', 'H-141547', 'H-141548', 'H-170290', 'H-170291', 'H-170060',
  'H-170061', 'H-140096', 'H-140597', 'H-160065', 'H-160066', 'H-155168', 'H-160067', 'H-160068', 'H-140099', 'H-140601',
  'H-155179', 'H-150407', 'H-230006', 'H-230017', 'H-230011', 'H-230020', 'H-230005', 'H-230014', 'H-230015', 'H-230003',
  'H-230004', 'H-230009', 'H-230010', 'H-141549', 'H-170066'
];

async function verifyRecords() {
  console.log('🔍 VERIFYING ALL H- FORMAT RECORDS');
  console.log('='.repeat(80));
  
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 15000,
      connectTimeoutMS: 10000
    });
    
    console.log('✅ Connected to MongoDB Atlas');
    
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get all H- records from database
    const existingRecords = await collection.find(
      { receiveNo: { $regex: /^H-/ } },
      { projection: { receiveNo: 1 } }
    ).toArray();
    
    const existingReceiveNos = existingRecords.map(r => r.receiveNo);
    
    console.log(`📊 Expected H- records: ${expectedRecords.length}`);
    console.log(`📊 Found H- records: ${existingReceiveNos.length}`);
    
    // Find missing records
    const missingRecords = expectedRecords.filter(record => !existingReceiveNos.includes(record));
    
    // Find extra records (shouldn't happen)
    const extraRecords = existingReceiveNos.filter(record => !expectedRecords.includes(record));
    
    console.log(`❌ Missing records: ${missingRecords.length}`);
    console.log(`⚠️ Extra records: ${extraRecords.length}`);
    
    if (missingRecords.length > 0) {
      console.log('\n❌ MISSING RECORDS:');
      missingRecords.forEach(record => {
        console.log(`  - ${record}`);
      });
    }
    
    if (extraRecords.length > 0) {
      console.log('\n⚠️ EXTRA RECORDS:');
      extraRecords.slice(0, 10).forEach(record => {
        console.log(`  - ${record}`);
      });
      if (extraRecords.length > 10) {
        console.log(`  ... and ${extraRecords.length - 10} more`);
      }
    }
    
    if (missingRecords.length === 0 && extraRecords.length === 0) {
      console.log('\n🎉 ALL H- FORMAT RECORDS ARE CORRECTLY IMPORTED!');
    }
    
    await mongoose.connection.close();
    console.log('\n🔌 Connection closed');
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

verifyRecords();
