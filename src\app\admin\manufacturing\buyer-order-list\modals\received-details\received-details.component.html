<h2 mat-dialog-title class="title mt-2">Received Pieces Details</h2>
<mat-dialog-content>
  <ng-container *ngIf="receivedDetails.length > 0; else noData">
    <table mat-table [dataSource]="receivedDetails" class="mat-elevation-z8">
         <!-- Weaver Column -->
      <ng-container matColumnDef="weaver">
        <th mat-header-cell *matHeaderCellDef>Weaver</th>
        <td mat-cell *matCellDef="let detail">{{detail.weaver}}</td>
      </ng-container>
      <!-- Carpet No Column -->
      <ng-container matColumnDef="carpetNo">
        <th mat-header-cell *matHeaderCellDef>Carpet No</th>
        <td mat-cell *matCellDef="let detail">{{detail.carpetNo}}</td>
      </ng-container>
      <!-- Date Column -->
      <ng-container matColumnDef="cDate">
        <th mat-header-cell *matHeaderCellDef>C Date</th>
        <td mat-cell *matCellDef="let detail">{{detail.cDate | date:'dd-MM-yyyy'}}</td>
      </ng-container>
      <!-- Issue No. Column -->
      <ng-container matColumnDef="issueNo">
        <th mat-header-cell *matHeaderCellDef>Issue No</th>
        <td mat-cell *matCellDef="let detail">{{detail.issueNo}}</td>
      </ng-container>
      <!-- I Date Column -->
      <ng-container matColumnDef="iDate">
        <th mat-header-cell *matHeaderCellDef>I Date</th>
        <td mat-cell *matCellDef="let detail">{{detail.iDate| date:'dd-MM-yyyy'}}</td>
      </ng-container>
     
      <!-- Quality Column -->
      <ng-container matColumnDef="quality">
        <th mat-header-cell *matHeaderCellDef>Quality</th>
        <td mat-cell *matCellDef="let detail">{{detail.quality}}</td>
      </ng-container>
      <!-- Design Column -->
      <ng-container matColumnDef="design">
        <th mat-header-cell *matHeaderCellDef>Design</th>
        <td mat-cell *matCellDef="let detail">{{detail.design}}</td>
      </ng-container>
      <!-- Color Column -->
      <ng-container matColumnDef="colour">
        <th mat-header-cell *matHeaderCellDef>Color</th>
        <td mat-cell *matCellDef="let detail">{{detail.colour}}</td>
      </ng-container>
      <!-- Size Column -->
      <ng-container matColumnDef="size">
        <th mat-header-cell *matHeaderCellDef>Size</th>
        <td mat-cell *matCellDef="let detail">{{detail.size}}</td>
      </ng-container>
      <!-- Pcs Column -->
      <ng-container matColumnDef="pcs">
        <th mat-header-cell *matHeaderCellDef>Pcs</th>
        <td mat-cell *matCellDef="let detail">{{detail.pcs}}</td>
      </ng-container>
      <!-- Area Column -->
      <ng-container matColumnDef="area">
        <th mat-header-cell *matHeaderCellDef>Area</th>
        <td mat-cell *matCellDef="let detail">{{detail.area}}</td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </ng-container>
  <ng-template #noData>
    <p>No received pieces data available.</p>
  </ng-template>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button mat-dialog-close>Close</button>
</mat-dialog-actions>

