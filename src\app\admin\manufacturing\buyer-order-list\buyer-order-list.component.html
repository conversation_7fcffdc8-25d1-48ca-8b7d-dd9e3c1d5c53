<div class="container mt-4">
      <fieldset style="min-height: 150px">
        <legend><b class="text-primary-emphasis">Buyer Order</b></legend>
        <div class="row mt-3">

              <div class="col-md-6">
                  <mat-label class=" fw-bold shadow-lg p-3 bg-body rounded ">BuyerName: {{this.viewData.buyerName.customerName}} </mat-label>
              </div>

              <div class="col-md-6 ">
                  <mat-label class=" fw-bold shadow-lg p-3 bg-body rounded  ">OrderNo: {{this.viewData.orderNo}} </mat-label>
              </div>
            </div>

             <div class="row mt-5">

              <div class="col-md-6 ">
                <mat-label class=" fw-bold shadow-lg p-3 bg-body rounded">CompanyOrderNo: {{this.viewData.companyOrderNo}} </mat-label>
            </div>

            <div class="col-md-6">
                <mat-label class=" fw-bold shadow-lg p-3 bg-body rounded ">OrderDate: {{orderDate}} </mat-label>
            </div>
             </div>
       </fieldset>
    </div>


  <div class="container mt-5">
    <div class="row">
      <fieldset style="min-height: 200px">


        <div class="mat-elevation-z8">
          <table mat-table [dataSource]="dataSource" matSort>


            <ng-container matColumnDef="SrNo">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> SrNo </th>
              <td mat-cell *matCellDef="let row">  {{row.index}}</td>
            </ng-container>


            <ng-container matColumnDef="Quality">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Quality </th>
              <td mat-cell *matCellDef="let row"> {{row.quality}}</td>
            </ng-container>


            <ng-container matColumnDef="Design">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Design </th>
              <td mat-cell *matCellDef="let row">{{row.Design}}</td>
            </ng-container>


            <ng-container matColumnDef="GroundBorder">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> GroundBorder </th>
              <td mat-cell *matCellDef="let row"> {{row.GroundBorder}}</td>
            </ng-container>


            <ng-container matColumnDef="size">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> size </th>
              <td mat-cell *matCellDef="let row"> {{row.size}}</td>
            </ng-container>


            <ng-container matColumnDef="khapSize">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> KhapSize </th>
              <td mat-cell *matCellDef="let row"> {{row.KhapSize}}</td>
            </ng-container>


            <ng-container matColumnDef="Pcs">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> TotalPcs</th>
              <td mat-cell *matCellDef="let row"> {{row.Pcs}}</td>
            </ng-container>

           

            <ng-container matColumnDef="PcsesPending">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> PcsPending </th>
              <td mat-cell *matCellDef="let row"> {{row.PcsesPending}} </td>
            </ng-container>

            <ng-container matColumnDef="PcsesAssigned">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> PcsAssigned </th>
              <td mat-cell *matCellDef="let row">
                <div class="d-flex align-items-center" style="gap: 30px;">
                  <span>{{row.PcsesAssigned}}</span>
                  <mat-icon *ngIf="row.PcsesAssigned > 0" 
                           class="info-circle-icon"
                           (click)="openAssignedDetails(row)">
                    info
                  </mat-icon>
                </div>
              </td>
            </ng-container>

             <ng-container matColumnDef="PcsesReceived">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> PcsReceived </th>
              <td mat-cell *matCellDef="let row">
                <div class="d-flex align-items-center" style="gap: 30px;">
                  <span>{{row.PcsesReceived}}</span>
                  <mat-icon *ngIf="row.PcsesReceived > 0"
                           class="info-circle-icon"
                           (click)="openReceivedDetails(row)">
                    info
                  </mat-icon>
                </div>
              </td>
            </ng-container>

            <ng-container matColumnDef="Area">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Area </th>
              <td mat-cell *matCellDef="let row"> {{row.Area}} </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>

          <mat-paginator [pageSizeOptions]="[50, 10, 25, 100]" aria-label="Select page"></mat-paginator>
        </div>
      </fieldset>
    </div>
  </div>
