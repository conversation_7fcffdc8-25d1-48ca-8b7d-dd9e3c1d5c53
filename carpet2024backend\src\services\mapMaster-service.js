// services/mapMasterService.js
const mapMasterRepository = require('../repositories/mapMaster-repository');

class MapMasterService {
    async create(data) {
        return await mapMasterRepository.create(data);
    }

    async findAll() {
        return await mapMasterRepository.findAll();
    }

    async findById(id) {
        return await mapMasterRepository.findById(id);
    }

    async update(id, data) {
        return await mapMasterRepository.update(id, data);
    }

    async delete(id) {
        return await mapMasterRepository.delete(id);
    }
}

module.exports = new MapMasterService();
