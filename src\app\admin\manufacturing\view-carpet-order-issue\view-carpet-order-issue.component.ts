import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ManufactureService } from '../../../services/manufacture.service';
import { MasterService } from '../../../services/master.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort} from '@angular/material/sort';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { environment } from '../../../../environments/environment.development';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { SweetalertService } from '../../../services/sweetalert.service';

declare var bootstrap: any;
@Component({
  selector: 'app-view-carpet-order-issue',
  templateUrl:'./view-carpet-order-issue.component.html',
  styleUrl: './view-carpet-order-issue.component.css',
})
export class ViewCarpetOrderIssueComponent implements OnInit {
  displayedColumns: string[] = [
    'Br_issueNo', 
    'date',
    'buyerOrder', 
    'weaver',
    // 'itemId',
    'quality',
    'design',
    'borderColour',
    'size',
    // 'khapSize',
    'pcs',
    'area',
    'rate',
    'amount',
    'MapOrderNo',
    // 'uploadFile',
  'action']

    dataSource = new MatTableDataSource<any>;
  constructor(
    private manufactureService: ManufactureService,
    private _fb: FormBuilder,
    private adminServices: MasterService,
    private alert: SweetalertService,
    private customeService: CustomeServiceService,
    private router: Router,
  ) {}
  ngOnInit(): void {
    this.getsIssuedOrder();

    const currentYear = new Date().getFullYear();
  this.availableYears = Array.from({ length: 10 }, (_, i) => currentYear - i);
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.sort.active = 'Br_issueNo'; // Sort by Issue No. column
    this.sort.direction = 'desc'; // Descending order
    this.sort.sortChange.emit(); // Refresh sorting
  }

  applyFilter(event: Event) {
      const filterValue = (event.target as HTMLInputElement).value;
      this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  selectedYear: number | null = null;
selectedMonth: number | null = null;
selectedAreaUnit: string = '';

availableYears: number[] = [];
availableMonths = [
  { value: null, label: 'All' }, // All option
  { value: 1, label: 'January' },
  { value: 2, label: 'February' },
  { value: 3, label: 'March' },
  { value: 4, label: 'April' },
  { value: 5, label: 'May' },
  { value: 6, label: 'June' },
  { value: 7, label: 'July' },
  { value: 8, label: 'August' },
  { value: 9, label: 'September' },
  { value: 10, label: 'October' },
  { value: 11, label: 'November' },
  { value: 12, label: 'December' },
];

selectedWeaver: string = '';
weaverList: string[] = [];

applyAllFilters() {
  const year = this.selectedYear;
  const month = this.selectedMonth;
  const unit = this.selectedAreaUnit;
  const weaver = this.selectedWeaver;

  this.dataSource.filterPredicate = (data: any, _: string) => {
    if (!data.date || !data.area) return false;

    const [day, monthStr, yearStr] = data.date.split('-');
    const rowMonth = parseInt(monthStr, 10);
    const rowYear = parseInt(yearStr, 10);

    const areaParts = data.area.trim().split(' ');
    const areaValue = parseFloat(areaParts[0] || '0');
    const areaUnit = areaParts[1] || '';

    const matchYear = !year || rowYear === year;
    const matchMonth = month === null || rowMonth === month;
    const matchUnit = !unit || areaUnit === unit;
    const matchWeaver = !weaver || data.weaver === weaver;

    return matchYear && matchMonth && matchUnit && matchWeaver;
  };

  // This forces the filterPredicate to run
  this.dataSource.filter = Math.random().toString();
}



formatDate(dateString: string): string {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString; // In case the date is already formatted

  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
}




 
  getsIssuedOrder() {
    this.manufactureService.getsOrderIssueList().subscribe({
      next: (value:any) => {
        debugger;
        console.log('value',value);
        let arr:any[]=[];
        let weaverSet = new Set<string>();
        value.map((x:any)=>{
          arr.push({
            id:x._id,
            Br_issueNo: x.Br_issueNo,
            date: this.formatDate(x.date),
            buyerOrder:x.buyerOrder?.orderNo,
            weaver: x.branch?.branchCode+' - '+x.weaver?.name,
            quality:x.quality?.quality,
            design:x.design?.design,
            borderColour:x.borderColour,
            size:x.size?.sizeInYard,
            pcs:x.pcs,
            area:x.area,
            rate:x.rate,
            amount: x.amount, 
            MapOrderNo:x.MapOrderNo,
          });
          if (x.branch?.branchCode && x.weaver?.name) {
            weaverSet.add(x.branch.branchCode + ' - ' + x.weaver.name);
          }
        });
        this.weaverList = Array.from(weaverSet).sort();
        // Sort by sequential number only (ignore branch prefix) - descending order
        console.log('🔍 Before sorting - sample items:', arr.slice(0, 5).map(item => item.Br_issueNo));

        arr.sort((a, b) => {
          const extractNumber = (issueNo: string) => {
            if (!issueNo) return 0;
            // Extract number after dash (K-2400001 -> 2400001, H-2400030 -> 2400030)
            if (issueNo.includes('-')) {
              const parts = issueNo.split('-');
              const num = parseInt(parts[1]);
              return isNaN(num) ? 0 : num;
            }
            // Fallback: extract all digits
            const num = parseInt(issueNo.replace(/\D/g, ''));
            return isNaN(num) ? 0 : num;
          };

          const numA = extractNumber(a.Br_issueNo || '');
          const numB = extractNumber(b.Br_issueNo || '');

          // Debug specific cases
          if (a.Br_issueNo?.includes('H-2400030') || b.Br_issueNo?.includes('H-2400030')) {
            console.log(`🔍 H-2400030 comparison: ${a.Br_issueNo} (${numA}) vs ${b.Br_issueNo} (${numB}) = ${numB - numA}`);
          }

          // Sort in descending order (latest/highest number first)
          return numB - numA;
        });

        console.log('🔍 After sorting - first 5 items:', arr.slice(0, 5).map(item => item.Br_issueNo));
        console.log('🔍 H-2400030 position:', arr.findIndex(item => item.Br_issueNo?.includes('H-2400030')));
        this.dataSource= new MatTableDataSource(arr);
        this.ngAfterViewInit();
        return;
      },
      error(err) {
        debugger;
        console.log(err);
      },
    });
  }
  selectedImageUrl: any;
  openModal(imageUrl: string): void {
    this.selectedImageUrl = imageUrl;
    const modal = new bootstrap.Modal(document.getElementById('myModal'), {});
    modal.show();
  }
  print(id: any) {
    if (!id) {
      this.alert.error('Warning', 'No order available for printing.');
      return;
    }
    this.router.navigate([`/admin/carpet-order-issue-print/${id}`]);
  }
delete(id: any) {
  // First check if this issue has received pieces
  this.manufactureService.getOrderIssue(id.id).subscribe({
    next: (issueData: any) => {
      const pcsReceived = issueData.PCSReceived || 0;

      if (pcsReceived > 0) {
        Swal.fire({
          title: 'Cannot Delete!',
          text: `This Issue No. ${id.Br_issueNo} cannot be deleted because ${pcsReceived} pieces have already been received.`,
          icon: 'error',
          confirmButtonText: 'OK'
        });
        return;
      }

      // If PCSReceived is 0, proceed with deletion confirmation
      Swal.fire({
        title: 'Are you sure?',
        text: `Do you want to delete this Issue No. ${id.Br_issueNo}?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
      }).then((result: any) => {
        if (result.isConfirmed) {
          this.manufactureService.deleteIssueOrder(id.id, id).subscribe({
            next: (value: any) => {
              this.alert.success('success', value.message);
              this.getsIssuedOrder(); // Refresh the list after successful delete
            },
            error: (err: any) => {
              this.alert.error('warning', err.error.message);
            },
          });
        }
      });
    },
    error: (err: any) => {
      this.alert.error('error', 'Failed to check issue status');
    }
  });
}
}
