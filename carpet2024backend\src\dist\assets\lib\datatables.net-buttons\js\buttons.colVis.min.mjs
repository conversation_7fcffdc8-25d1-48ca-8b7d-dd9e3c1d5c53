/*!
 * Column visibility buttons for Buttons and DataTables.
 * 2016 SpryMedia Ltd - datatables.net/license
 */
import $ from"jquery";import DataTable from"datatables.net";import Buttons from"datatables.net-buttons";$.extend(DataTable.ext.buttons,{colvis:function(e,i){var l=null,n={extend:"collection",init:function(n,o){l=o},text:function(n){return n.i18n("buttons.colvis","Column visibility")},className:"buttons-colvis",closeButton:!1,buttons:[{extend:"columnsToggle",columns:i.columns,columnText:i.columnText}]};return e.on("column-reorder.dt"+i.namespace,function(n,o,t){e.button(null,e.button(null,l).node()).collectionRebuild([{extend:"columnsToggle",columns:i.columns,columnText:i.columnText}])}),n},columnsToggle:function(n,o){return n.columns(o.columns).indexes().map(function(n){return{extend:"columnToggle",columns:n,columnText:o.columnText}}).toArray()},columnToggle:function(n,o){return{extend:"columnVisibility",columns:o.columns,columnText:o.columnText}},columnsVisibility:function(n,o){return n.columns(o.columns).indexes().map(function(n){return{extend:"columnVisibility",columns:n,visibility:o.visibility,columnText:o.columnText}}).toArray()},columnVisibility:{columns:void 0,text:function(n,o,t){return t._columnText(n,t)},className:"buttons-columnVisibility",action:function(n,o,t,e){var o=o.columns(e.columns),i=o.visible();o.visible(void 0!==e.visibility?e.visibility:!(i.length&&i[0]))},init:function(e,n,i){var l=this;n.attr("data-cv-idx",i.columns),e.on("column-visibility.dt"+i.namespace,function(n,o){o.bDestroying||o.nTable!=e.settings()[0].nTable||l.active(e.column(i.columns).visible())}).on("column-reorder.dt"+i.namespace,function(n,o,t){i.destroying||1===e.columns(i.columns).count()&&(l.text(i._columnText(e,i)),l.active(e.column(i.columns).visible()))}),this.active(e.column(i.columns).visible())},destroy:function(n,o,t){n.off("column-visibility.dt"+t.namespace).off("column-reorder.dt"+t.namespace)},_columnText:function(n,o){var t=n.column(o.columns).index(),e=n.settings()[0].aoColumns[t].sTitle;return e=(e=e||n.column(t).header().innerHTML).replace(/\n/g," ").replace(/<br\s*\/?>/gi," ").replace(/<select(.*?)<\/select>/g,"").replace(/<!\-\-.*?\-\->/g,"").replace(/<.*?>/g,"").replace(/^\s+|\s+$/g,""),o.columnText?o.columnText(n,t,e):e}},colvisRestore:{className:"buttons-colvisRestore",text:function(n){return n.i18n("buttons.colvisRestore","Restore visibility")},init:function(o,n,t){t._visOriginal=o.columns().indexes().map(function(n){return o.column(n).visible()}).toArray()},action:function(n,o,t,e){o.columns().every(function(n){n=o.colReorder&&o.colReorder.transpose?o.colReorder.transpose(n,"toOriginal"):n;this.visible(e._visOriginal[n])})}},colvisGroup:{className:"buttons-colvisGroup",action:function(n,o,t,e){o.columns(e.show).visible(!0,!1),o.columns(e.hide).visible(!1,!1),o.columns.adjust()},show:[],hide:[]}});export default DataTable;