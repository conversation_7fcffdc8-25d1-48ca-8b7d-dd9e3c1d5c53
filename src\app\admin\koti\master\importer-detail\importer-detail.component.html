<div class="px-3">

    <!-- Start Content-->
    <div class="container">

        <!-- start page title -->
        <div class="py-3 py-lg-4">
            <div class="row">
                <div class="col-lg-6">
                    <h2 class="page-title mb-0">Importer</h2>
                </div>

            </div>
            <hr>

            <!-- Form row -->
            <div class="row">
                <div class="col-xl-12">
                    <div class="card">
                        <div class="card-body ">

                            <form [formGroup]="frmImporterDetail" (ngSubmit)="importerDetail()">
                                <div class="row">
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Customer Code</mat-label>
                                            <input matInput placeholder="Customer Code"
                                                formControlName="customerCode" />
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Customer Name</mat-label>
                                            <input matInput placeholder="Customer Name"
                                                formControlName="customerName" />
                                        </mat-form-field>
                                    </div>


                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Address</mat-label>
                                            <input matInput placeholder="Address" formControlName="address" />
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Zip Code</mat-label>
                                            <input matInput placeholder="Zip Code" formControlName="zipCode" />
                                        </mat-form-field>
                                    </div>
                                    <!-- <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Street</mat-label>
                                        <input matInput placeholder="Street" formControlName="Street"/>
                                        </mat-form-field>
                                    </div>  -->
                                    <!-- <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                        <mat-label>Street</mat-label>
                                        <input matInput placeholder="Street" formControlName="Street"/>
                                        </mat-form-field>
                                    </div>  -->






                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Country</mat-label>
                                            <mat-select formControlName="country">
                                                @for (food of foods; track food) {
                                                <mat-option [value]="food">{{food}}</mat-option>
                                                }
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Contact No.</mat-label>
                                            <input matInput placeholder="Contact No" formControlName="contactNo" />
                                        </mat-form-field>
                                        `
                                    </div>

                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Email</mat-label>
                                            <input type="email" matInput [errorStateMatcher]="matcher"
                                                placeholder="Ex. <EMAIL>" formControlName="email">

                                            @if (emailFormControl.hasError('email') &&
                                            !emailFormControl.hasError('required')) {
                                            <mat-error>Please enter a valid email address</mat-error>
                                            }
                                            @if (emailFormControl.hasError('required')) {
                                            <mat-error>Email is <strong>required</strong></mat-error>
                                            }
                                        </mat-form-field>

                                    </div>
                                    <div class="mb-2 col-md-4">

                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Enter your password</mat-label>
                                            <input matInput [type]="hide ? 'password' : 'text'"
                                                formControlName="password">
                                            <button mat-icon-button matSuffix (click)="hide = !hide"
                                                [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hide">
                                                <mat-icon>{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
                                            </button>
                                        </mat-form-field>
                                    </div>
                                </div>






                                <h2>BANK DETAILS</h2>
                                <hr>
                                <div class="row">
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Bank Name</mat-label>
                                            <input matInput placeholder="Bank Name" formControlName="bankName" />
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Account Number</mat-label>
                                            <input matInput placeholder="Account Number"
                                                formControlName="accountNumber" />
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Bank Swift Code</mat-label>
                                            <input matInput placeholder="Bank Swift Code" formControlName="swiftCode" />
                                        </mat-form-field>
                                    </div>
                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>I.F.S.C. Code</mat-label>
                                            <input matInput placeholder="I.F.S.C. Code" formControlName="ifscCode" />
                                        </mat-form-field>
                                    </div>


                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Bank Address</mat-label>
                                            <input matInput placeholder="Bank Address" formControlName="bankAddress" />
                                        </mat-form-field>
                                    </div>





                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>bankZipCode</mat-label>
                                            <input matInput placeholder="Bank Trasfer Code"
                                                formControlName="bankZipCode" />
                                        </mat-form-field>
                                    </div>

                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Email</mat-label>
                                            <input type="email" matInput [errorStateMatcher]="matcher"
                                                placeholder="Ex. <EMAIL>" formControlName="bankEmail">

                                            @if (emailFormControl.hasError('email') &&
                                            !emailFormControl.hasError('required')) {
                                            <mat-error>Please enter a valid email address</mat-error>
                                            }
                                            @if (emailFormControl.hasError('required')) {
                                            <mat-error>Email is <strong>required</strong></mat-error>
                                            }
                                        </mat-form-field>

                                    </div>

                                    <div class="mb-2 col-md-4">
                                        <mat-form-field appearance="outline" class="example-full-width">
                                            <mat-label>Gst No.</mat-label>
                                            <input matInput placeholder="Gstn No." formControlName="gstnNo" />
                                        </mat-form-field>

                                    </div>

                                </div>

                                <button mat-flat-button color="primary">Add</button>
                                <!-- <button class="ms-2" routerLink="../importer-order-price-list" mat-flat-button
                                    color="primary">Add Order price</button> -->
                            </form>
                        </div>
                    </div>
                </div>
                <!-- <-- end col -->


                <!-- end col -->
            </div>
            <!-- end row -->

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">


                            <div class="row">
                                <div class="col-md-12">
                                    <mat-form-field appearance="outline">
                                        <mat-label>Filter</mat-label>
                                        <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Mia" #input>
                                    </mat-form-field>


                                </div>
                                <div class="mat-elevation-z8 overFlow col-md-12">
                                    <table mat-table [dataSource]="dataSource" matSort>


                                        <!-- ID Column -->
                                        <ng-container matColumnDef="id">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> No </th>
                                            <td mat-cell *matCellDef="let row"> {{row.index}} </td>
                                        </ng-container>

                                        <!-- Progress Column -->
                                        <ng-container matColumnDef="customerName">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Customer Name </th>
                                            <td mat-cell *matCellDef="let row"> {{row.customerName}} </td>
                                        </ng-container>

                                        <!-- Name Column -->
                                        <ng-container matColumnDef="customerCode">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Customer Code </th>
                                            <td mat-cell *matCellDef="let row"> {{row.customerCode}} </td>
                                        </ng-container>

                                        <!-- Fruit Column -->
                                        <ng-container matColumnDef="contactNo">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Contact No </th>
                                            <td mat-cell *matCellDef="let row"> {{row.contactNo}} </td>
                                        </ng-container>

                                        <!-- Fruit Column -->
                                        <ng-container matColumnDef="email">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> E-mail </th>
                                            <td mat-cell *matCellDef="let row"> {{row.email}} </td>
                                        </ng-container>

                                        <!-- ID Column -->
                                        <ng-container matColumnDef="password">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Password </th>
                                            <td mat-cell *matCellDef="let row"> {{row.password}} </td>
                                        </ng-container>

                                        <!-- Progress Column -->
                                        <ng-container matColumnDef="address">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Address </th>
                                            <td mat-cell *matCellDef="let row"> {{row.address}}</td>
                                        </ng-container>

                                        <!-- Name Column -->
                                        <ng-container matColumnDef="zipCode">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Zip Code </th>
                                            <td mat-cell *matCellDef="let row"> {{row.zipCode}} </td>
                                        </ng-container>


                                        <ng-container matColumnDef="action">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Action </th>
                                            <td mat-cell *matCellDef="let row"><a title="Edit" (click)="getImporter(row.id)">
                                                    <i class="fa fa-pencil-square-o fa-lg" aria-hidden="true"></i>
                                                </a> &nbsp;&nbsp; <a title="Delete" (click)="deleteImporter(row.id)"><i class="fa fa-trash-o fa-lg"
                                                        aria-hidden="true"></i></a> &nbsp; &nbsp; <a title="Add Price"
                                                    routerLink="../importer-order-price-list/{{row.id}}+importer"><i
                                                        class="fa fa-plus-square-o fa-lg" aria-hidden="true"></i></a> &nbsp; &nbsp; </td>
                                        </ng-container>


                                        <!-- Fruit Column -->
                                        <ng-container matColumnDef="country">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Country </th>
                                            <td mat-cell *matCellDef="let row"> {{row.country}} </td>
                                        </ng-container>




                                        <!-- ID Column -->
                                        <ng-container matColumnDef="bankName">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Bank Name </th>
                                            <td mat-cell *matCellDef="let row"> {{row.bankName}} </td>
                                        </ng-container>

                                        <!-- Progress Column -->
                                        <ng-container matColumnDef="bankAddress">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Bank Address </th>
                                            <td mat-cell *matCellDef="let row"> {{row.bankAddress}} </td>
                                        </ng-container>

                                        <!-- Fruit Column -->
                                        <ng-container matColumnDef="accountNumber">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Account Number </th>
                                            <td mat-cell *matCellDef="let row"> {{row.accountNumber}} </td>
                                        </ng-container>

                                        <!-- Fruit Column -->
                                        <ng-container matColumnDef="swiftCode">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Swift Code </th>
                                            <td mat-cell *matCellDef="let row"> {{row.swiftCode}} </td>
                                        </ng-container>

                                        <!-- Fruit Column -->
                                        <ng-container matColumnDef="ifscCode">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Ifsc Code </th>
                                            <td mat-cell *matCellDef="let row"> {{row.ifscCode}} </td>
                                        </ng-container>

                                        <!-- Name Column -->
                                        <ng-container matColumnDef="bankZipCode">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Zip Code </th>
                                            <td mat-cell *matCellDef="let row"> {{row.bankZipCode}} </td>
                                        </ng-container>

                                        <!-- Fruit Column -->
                                        <ng-container matColumnDef="bankEmail">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Bank E-mail </th>
                                            <td mat-cell *matCellDef="let row"> {{row.bankEmail}} </td>
                                        </ng-container>

                                        <!-- Fruit Column
             <ng-container matColumnDef="bankContactNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> bankContactNo </th>
                <td mat-cell *matCellDef="let row"> {{row.bankContactNo}} </td>
            </ng-container> -->

                                        <!-- Fruit Column -->
                                        <ng-container matColumnDef="gstnNo">
                                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Gstn No </th>
                                            <td mat-cell *matCellDef="let row"> {{row.gstnNo}} </td>
                                        </ng-container>




                                        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                                        <!-- Row shown when there is no matching data. -->
                                        <tr class="mat-row" *matNoDataRow>
                                            <td class="mat-cell" colspan="4">No data matching the filter
                                                "{{input.value}}"
                                            </td>
                                        </tr>
                                    </table>


                                </div>

                            </div>
                            <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]"
                                    aria-label="Select page of users"></mat-paginator>

                        </div> <!-- end card body-->
                    </div> <!-- end card -->
                </div><!-- end col-->
            </div>
            <!-- end row-->





        </div>
        <!-- end page title -->


    </div> <!-- container -->

</div> <!-- content -->
