// routes/materialDeyingRoutes.js
const express = require('express');
const materialDeyingController = require('../../controller/manifacturing/materialDeying-controller');

const router = express.Router();

router.post('/materialDeying', materialDeyingController.create);
router.get('/materialDeying/:id', materialDeyingController.findById);
router.get('/materialDeying', materialDeyingController.findAll);
router.put('/materialDeying/:id', materialDeyingController.update);
router.delete('/materialDeying/:id', materialDeyingController.delete);

module.exports = router;
