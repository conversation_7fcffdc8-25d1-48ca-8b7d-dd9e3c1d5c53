<div class="container mt-4">
  <section >
      <fieldset>
          <legend><b>{{isUpdated?'Update':'New'}} Carpet Order Issue</b></legend>
         <form [formGroup]="frmOrderIssue">
          <div class="row">
            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Weaver</mat-label>
                <mat-select (selectionChange)="onWeaverChange($event)" formControlName="weaver">
                  @for (weaver of weaverList; track weaver) {
                <mat-option [value]="weaver._id">{{weaver.branch?.branchCode}} - {{weaver.name}}</mat-option>
              }
                </mat-select>
              </mat-form-field>
            </div>

            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline" class="ex-width">
                  <mat-label>Br. Issue No.</mat-label>
                  <input matInput placeholder="Br. Issue No." formControlName="Br_issueNo">
              </mat-form-field>
          </div>
            <div class="mb-2 col-md-2">
                  <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Date</mat-label>
                    <input matInput [matDatepicker]="picker" formControlName="date">
                    <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                  </mat-form-field>
            </div>
            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Buyer Order No.</mat-label>
                <mat-select (valueChange)="selectedOrderNo($event)" formControlName="buyerOrder">
                  @for (orderno of ordernoList; track orderno) {
                    <mat-option [value]="orderno._id">{{ orderno.companyOrderNo }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
          </div>

          <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Area In</mat-label>
              <mat-select formControlName="AreaIn" (selectionChange)="onAreaChange($event)">
                <mat-option value="Sq.Feet">Sq.Feet</mat-option>
                <mat-option value="Sq.Yard">Sq.Yard</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

       <div class="mb-2 col-md-2">
                <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Quality</mat-label>
                    <input matInput placeholder="Quality" [value]="qualityDisplayName" readonly>
                </mat-form-field>
            </div>
            <div class="mb-2 col-md-2">
                <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Design</mat-label>
                    <input matInput placeholder="Design" [value]="designDisplayName" readonly>
                </mat-form-field>
            </div>
            <div class="mb-2 col-md-3">
                <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Border Colour</mat-label>
                    <input matInput placeholder="Border Colour" formControlName="borderColour" readonly>
                </mat-form-field>
            </div>
            <div class="mb-2 col-md-2">
                <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Size</mat-label>
                    <input matInput placeholder="Size" [value]="sizeDisplayName" readonly>
                </mat-form-field>
            </div>
            <div class="mb-2 col-md-2">
                <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Khap Size</mat-label>
                    <input matInput placeholder="Khap Size" formControlName="khapSize" readonly>
                </mat-form-field>
            </div>
            <div class="mb-2 col-md-1">
                <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Pcs.</mat-label>
                    <!-- No [min] or [disabled]; all validation in TS -->
                    <input matInput placeholder="Pcs." formControlName="pcs" (blur)="matchPcs()" #pcsInput>
                </mat-form-field>
            </div>
            <div class="mb-2 col-md-2">
                <mat-form-field appearance="outline" class="ex-width">
                  <mat-label>Rate</mat-label>
                  <input type="number" matInput placeholder=".00" formControlName="rate" (blur)="setDigit($event)" #rateInput>
                </mat-form-field>
          </div>
          <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Area</mat-label>
              <input matInput [value]="frmOrderIssue.get('area')?.value" readonly>
            </mat-form-field>
      </div>
          <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Amount</mat-label>
                <input type="number" matInput placeholder="Amount" formControlName="amount" readonly>
            </mat-form-field>
      </div>
            <div class="mb-2 col-md-3">
                <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Map Order No.</mat-label>
                    <input matInput placeholder="Map Order No." formControlName="MapOrderNo" >
                </mat-form-field>
            </div>
<div class="mb-2 col-md-2 space">  &nbsp;
  <button mat-flat-button color="primary" type="button" *ngIf="!isUpdated" (click)="issueCarpet()">Save</button>&nbsp;
  <button mat-flat-button color="primary" type="button" *ngIf="isUpdated" (click)="update()">Update</button>&nbsp;
 </div>
        </div>
         </form>
      </fieldset>
  </section>
</div>
<!-- Order List: controlled by showOrderList property -->
<div class="container mt-4" *ngIf="showOrderList">
  <section>
      <fieldset>
          <legend><b>Order List</b></legend>
          <div class="row">
              <div class="col-12">
                  <mat-form-field appearance="outline">
                      <mat-label>Search</mat-label>
                      <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Jack" #input>
                    </mat-form-field>
                    <div class="mat-elevation-z8">
                      <table mat-table [dataSource]="dataSource" matSort>
                        <!-- SrNo Column -->
                        <ng-container matColumnDef="SrNo">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Sr.No </th>
                          <td mat-cell *matCellDef="let row ; let i=index"> {{i+1}} </td>
                        </ng-container>
                        <!-- Quality Column -->
                        <ng-container matColumnDef="Quality">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
                          <td mat-cell *matCellDef="let row"> {{row.quality}}</td>
                        </ng-container>
                        <!-- Design Column -->
                        <ng-container matColumnDef="Design">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Design</th>
                          <td mat-cell *matCellDef="let row"> {{row.design}} </td>
                        </ng-container>
                        <!--GroundBorder Column -->
                        <ng-container matColumnDef="GroundBorder">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Border Colour</th>
                          <td mat-cell *matCellDef="let row"> {{row.borderColour}} </td>
                        </ng-container>
                       <!-- Size Column -->
                       <ng-container matColumnDef="Size">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Size</th>
                          <td mat-cell *matCellDef="let row"> {{row.size}} </td>
                        </ng-container>
                           <!-- KhapSize Column -->
                           <ng-container matColumnDef="KhapSize">
                              <th mat-header-cell *matHeaderCellDef mat-sort-header>KhapSize</th>
                              <td mat-cell *matCellDef="let row"> {{row.khapSize}} </td>
                            </ng-container>
                              <!-- Pcs Column -->
                              <ng-container matColumnDef="Pcs">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Pcs.</th>
                                <td mat-cell *matCellDef="let row"> {{ row.pcs }} </td>
                              </ng-container>
                               <!-- TotalAreaInYd Column -->
                            <ng-container matColumnDef="TotalAreaInYd">
                              <th mat-header-cell *matHeaderCellDef mat-sort-header>AreaInYd</th>
                              <td mat-cell *matCellDef="let row"> {{row.totalAreaInYd | number:'1.2-3'}} </td>
                            </ng-container>
                              <!-- Action Column -->
                            <ng-container matColumnDef="Action">
                              <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                              <td mat-cell *matCellDef="let row">
                                <a *ngIf="row.pcs !== 0" (click)="selectItem(row)" class="primary">
                                  <i class="fa fa-edit"></i>
                                </a>
                                </td>
                            </ng-container>
                        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                        <!-- Row shown when there is no matching data. -->
                        <tr class="mat-row" *matNoDataRow>
                          <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>
                        </tr>
                      </table>
                      <!-- <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of users"></mat-paginator> -->
              </div>
          </div>
          </div>
      </fieldset>
  </section>
</div>

