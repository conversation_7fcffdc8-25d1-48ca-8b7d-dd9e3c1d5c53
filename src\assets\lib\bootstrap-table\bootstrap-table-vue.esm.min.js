/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

const t=window.jQuery,e=e=>void 0===e?e:t.extend(!0,Array.isArray(e)?[]:{},e);function n(t,e,n,o,s,i,a,r,d,l){"boolean"!=typeof a&&(d=r,r=a,a=!1);const c="function"==typeof n?n.options:n;let h;if(t&&t.render&&(c.render=t.render,c.staticRenderFns=t.staticRenderFns,c._compiled=!0,s&&(c.functional=!0)),o&&(c._scopeId=o),i?(h=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),e&&e.call(this,d(t)),t&&t._registeredComponents&&t._registeredComponents.add(i)},c._ssrRegister=h):e&&(h=a?function(t){e.call(this,l(t,this.$root.$options.shadowRoot))}:function(t){e.call(this,r(t))}),h)if(c.functional){const t=c.render;c.render=function(e,n){return h.call(n),t(e,n)}}else{const t=c.beforeCreate;c.beforeCreate=t?[].concat(t,h):[h]}return n}const o=n({render:function(){var t=this.$createElement;return(this._self._c||t)("table")},staticRenderFns:[]},undefined,{name:"BootstrapTable",props:{columns:{type:Array,require:!0},data:{type:[Array,Object],default(){}},options:{type:Object,default:()=>({})}},mounted(){this.$table=t(this.$el),this.$table.on("all.bs.table",((e,n,o)=>{let s=t.fn.bootstrapTable.events[n];s=s.replace(/([A-Z])/g,"-$1").toLowerCase(),this.$emit("on-all",...o),this.$emit(s,...o)})),this._initTable()},methods:{_initTable(){const t={...e(this.options),columns:e(this.columns),data:e(this.data)};this._hasInit?this.refreshOptions(t):(this.$table.bootstrapTable(t),this._hasInit=!0)},...(()=>{const e={};for(const n of t.fn.bootstrapTable.methods)e[n]=function(...t){return this.$table.bootstrapTable(n,...t)};return e})()},watch:{options:{handler(){this._initTable()},deep:!0},columns:{handler(){this._initTable()},deep:!0},data:{handler(){this.load(e(this.data))},deep:!0}}},undefined,false,undefined,!1,void 0,void 0,void 0);export{o as default};
