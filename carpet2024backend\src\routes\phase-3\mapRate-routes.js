const express = require('express');
const router = express.Router();
const mapRateController = require('../../controller/mapRate-controller');

// Create a map rate
router.post('/mapRates', mapRateController.createMapRate);

// Get all map rates
router.get('/mapRates', mapRateController.getAllMapRates);

// Get a map rate by ID
router.get('/mapRates/:id', mapRateController.getMapRateById);

// Update a map rate by ID
router.put('/mapRates/:id', mapRateController.updateMapRate);

// Delete a map rate by ID
router.delete('/mapRates/:id', mapRateController.deleteMapRate);

module.exports = router;
