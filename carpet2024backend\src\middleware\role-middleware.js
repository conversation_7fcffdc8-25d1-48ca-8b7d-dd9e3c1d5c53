const roleRepository = require('../repositories/RoleRepository');

async function checkRoleExistence(req, res, next) {
  try {
    const roleName = req.params.name; 
    const role = await roleRepository.getRoleByName(roleName);

    if (role) {
      req.role = role;
      next();
    } else {
      res.status(404).json({ error: 'Role not found' });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}

module.exports = {
  checkRoleExistence,
};
