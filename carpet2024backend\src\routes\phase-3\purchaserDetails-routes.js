const express = require('express');
const router = express.Router();
const purchaserDetailsController = require('../../controller/purchaserDetails-controller');

// Create purchaser details
router.post('/purchaser-details', purchaserDetailsController.createpurchaserDetails);

// Get all purchaser details
router.get('/purchaser-details', purchaserDetailsController.getAllpurchaserDetails);

// Get purchaser details by ID
router.get('/purchaser-details/:id', purchaserDetailsController.getpurchaserDetailsById);

// Update purchaser details by ID
router.put('/purchaser-details/:id', purchaserDetailsController.updatepurchaserDetails);

// Delete purchaser details by ID
router.delete('/purchaser-details/:id', purchaserDetailsController.deletepurchaserDetails);

module.exports = router;
