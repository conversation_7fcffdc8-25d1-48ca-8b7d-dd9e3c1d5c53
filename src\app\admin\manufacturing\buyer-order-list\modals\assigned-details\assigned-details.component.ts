import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

interface IssueDetail {
  date: string | Date;
  weaver?: { name?: string; weaverName?: string } | string;
  branchCode?: string;
  [key: string]: any;
}

@Component({
  selector: 'app-assigned-details',
  templateUrl: './assigned-details.component.html',
  styleUrl: './assigned-details.component.css'
})
export class AssignedDetailsComponent {
  get issueDetails() {
    return (this.data.issues || []).map((issue: IssueDetail) => ({
      ...issue,
     
    }));
  }
  loading = false;
  error = '';

  constructor(
    public dialogRef: MatDialogRef<AssignedDetailsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { issues: IssueDetail[] }
  ) {}
}
