import{a as ue}from"./chunk-43RJOZUD.js";import{a as fe,b as xe,c as ve,d as Ce,e as be,f as ye,g as Se,k as Ee}from"./chunk-QFG3C3WV.js";import"./chunk-3FUPWI55.js";import"./chunk-7RV22KDK.js";import{c as se,d as de,e as me,f as ce,h as pe,i as ge,k as he,m as T,ta as _e}from"./chunk-CATJFVJK.js";import{$ as w,$a as X,Db as ne,Ec as oe,Ga as Y,Gb as ie,Ib as k,Mc as ae,Nc as re,Sb as n,Tb as P,Tc as le,Ub as m,Vb as D,Wb as j,X as H,Xa as d,Ya as b,Za as Z,_ as $,bb as J,db as ee,ea as K,eb as te,ec as u,gc as f,ja as C,ka as I,ma as q,mb as N,ob as A,p as _,ra as W,sa as Q,xb as t,yb as i,zb as h}from"./chunk-YIUCZFK7.js";import{i as E}from"./chunk-P2VZOJAX.js";var Me=(()=>{let e=class e{};e.\u0275fac=function(r){return new(r||e)},e.\u0275cmp=C({type:e,selectors:[["app-notfound"]],decls:11,vars:0,consts:[[1,"container","vh-100","d-flex","justify-content-center","align-items-center"],[1,"row"],[1,"col-lg-6"],[1,"error-number"],[1,"error-title"],[1,"error-text"],["routerLink","",1,"btn","btn-primary","btn-error"]],template:function(r,l){r&1&&(t(0,"div",0)(1,"div",1)(2,"div",2)(3,"h1",3),n(4,"404"),i(),t(5,"h2",4),n(6,"Page Not Found"),i(),t(7,"p",5),n(8,"Oopps. The page you were looking for doesn't exist. You may have mistyped the address or the page may have moved."),i(),t(9,"a",6),n(10,"Back to Dashboard"),i()()()())},dependencies:[he]});let s=e;return s})();function Fe(s,e){if(s&1){let o=ne();t(0,"button",73),ie("click",function(){W(o);let r=k();return Q(r.generatePDF())}),n(1,` Generate PDF
`),i()}}function ke(s,e){if(s&1&&(t(0,"div")(1,"h5",79),n(2),i()()),s&2){let o=k().$implicit;d(2),j(" RG:",o.challanNo," vom ",o.challanDate," an ",o.customer," ")}}function je(s,e){if(s&1&&(t(0,"div",74)(1,"div",55)(2,"div",56)(3,"h5",75),n(4),i()(),t(5,"div",57),N(6,ke,3,3,"div",76),t(7,"P",77),n(8),i(),t(9,"p",77),n(10),i(),t(11,"p",77),n(12),i()(),t(13,"div",58)(14,"p",78),n(15),u(16,"number"),i()(),t(17,"div",60)(18,"p",78),n(19),u(20,"number"),i()(),t(21,"div",61)(22,"p",78),n(23),u(24,"number"),i()()()()),s&2){let o=e.$implicit;d(4),m(" ",o.carpetNo," "),d(2),A("ngIf",o.challanNo),d(2),P(o.qualityDesign),d(2),j(" ",o.qualityCode,", ",o.colourCode," ",o.colour," "),d(2),m(" ",o.size," cm "),d(3),m(" ",f(16,10,o.area,"1.2-2")," QM "),d(4),m(" ",f(20,13,o.evkPrice,"1.2-2")," "),d(4),m(" ",f(24,16,o.amount,"1.2-2")," ")}}var Oe=(()=>{let e=class e{constructor(a,r,l,c,x){this._service=a,this.customeService=r,this.activeRoute=l,this.ngxLoader=c,this.pdfService=x,this.billId="",this._isChallan=!1,this.billDetails=[],this._bill={},this.totalCalculation={totalAmount:0,profit:0,grossAmt:0,gstAmt:0},this.total=0,this.profit=0,this.gst=0,this.isPrint=!1}ngOnInit(){this.billId=this.activeRoute.snapshot.paramMap.get("id")||"",this.initializeData(this.billId)}initializeData(a){Promise.all([this.getBills(),this.getChallans(),this.getContainerReceived(),this.getImporterName()]).then(()=>{let r=a.split(" ");r[1]==="print"&&this.viewBill(r[0]),this.ngxLoader.stop()}).catch(r=>{console.log("something went wrong")})}getBills(){return E(this,null,function*(){let a=yield _(this._service.getsBill());this.allBills=a})}getChallans(){return E(this,null,function*(){let a=yield _(this._service.getAllChallan());this.allChallans=a})}getContainerReceived(){return E(this,null,function*(){let a=yield _(this._service.getAllContainerRecieved());this.allContainerStock=a})}getImporterName(){return E(this,null,function*(){let a=yield _(this._service.getsWholesalerList());this.allImporterDetails=a})}viewBill(a){let r=0,l=0,c=0,x=0,z=0;this.ngxLoader.start();let y=this.allBills.find(S=>S._id===a.toString()),O=this.allImporterDetails.find(S=>S.customerName===y.wholesellerName);console.log(O);let L=this.customeService.convertDate(y.chooseAdate),G=L.split(".");this._bill={billNo:y.billNo,date:L,customer:y.wholesellerName,street:O.address,zipCode:O.zipCode,country:O.country,date2:"01."+G[1]+"."+G[2]};debugger;y.challanNo.forEach(S=>{let v=this.allChallans.find(p=>p.challanNo===S.challanNumber);if(!v||!v.carpetList){console.error("Challan data or carpet list is undefined for challan number");return}v.carpetList.forEach(p=>{this.allContainerStock.filter(R=>R.containerItem.some(g=>parseInt(g.GerCarpetNo)===p.barcodeNo&&p.isDeleted!=!0)).forEach(R=>{R.containerItem.forEach(g=>{if(parseInt(g.GerCarpetNo)===p.barcodeNo){r=0,l=0,c=0,x=0,z=0;let B=parseFloat(p.area?p.area:g.Area),De=parseFloat(p.evkPrice?p.evkPrice:g.EvKPrice),V=B*De;r++,p.status!="return"?(c+=B,l+=V):(c-=Math.abs(B),l-=Math.abs(V));let ze=this.customeService.convertDate(v.chooseAdate),F=this.billDetails.some(Re=>Re.challanNo==v.challanNo);this.billDetails.push({challanNo:F?void 0:v.challanNo,challanDate:F?void 0:ze,customer:F?void 0:v.retailerOutlet,carpetNo:g.GerCarpetNo,qualityDesign:g.QualityDesign,colour:g.Color,colourCode:g.CCode,qualityCode:g.QCode,size:p.size?p.size:g.Size,area:c,evkPrice:p.evkPrice?p.evkPrice:g.EvKPrice,amount:l,invoiceNo:g.InvoiceNo,saleStatus:g.status})}})})})}),this.calculation(this.billDetails),console.log(this.billDetails),this.ngxLoader.stop()}calculation(a){a.forEach(r=>{this.total=this.total+parseFloat(r.area)*parseFloat(r.evkPrice)}),this.profit=this.total/100*13,this.gst=(this.total+this.profit)/100*19,this.totalCalculation={totalAmount:this.total,profit:this.profit,gstAmt:this.gst,grossAmt:this.total+this.profit+this.gst},console.log(this.totalCalculation)}generatePDF(){window.print()}};e.\u0275fac=function(r){return new(r||e)(b(ye),b(Se),b(pe),b(xe),b(Ee))},e.\u0275cmp=C({type:e,selectors:[["app-my-standalone-component"]],decls:228,vars:57,consts:[["class","no-print",3,"click",4,"ngIf"],["id","contentToConvert",1,"foot1",2,"margin-left","50px","margin-right","50px"],[1,"container"],[1,"row",2,"margin-bottom","245px"],[1,"col-md-12",2,"margin-top","30px !important"],["src","../../assets/auth-assets/images/Untitled-3.png","alt","",1,"img-fluid"],[1,"mt-5"],[1,"col-md-12","d-flex"],[1,"col-md-8"],[1,"font1","h5txt"],[1,"col-md-12","mt","d-flex","justify-content-between"],[1,"card","card1"],[1,"hd-text"],[1,"pl-4",2,"font-weight","bold","font-size","27px"],[1,"card","card1","mt-5"],[1,"text-hd-n"],[2,"margin-top","10px !important","display","inline-block"],[1,"col-md-12","mt-3","d-flex","justify-content-between"],[1,"card1","line1"],[1,"textsize"],[1,"line"],[1,"col-md-12","d-flex","justify-content-between","mt-1"],[1,"card1","bill","line1"],[1,"d-flex"],[1,"col-md-4","d-flex","justify-content-end",2,"text-align","end","padding-right","0px"],[1,"invoice-table"],[1,"table-colour"],[1,"font-weight-bold","text-lg","textsize"],[1,"right-align","line-above","fw-bold","textsize"],[1,"right-align","textsize"],[1,"fw-bold","textsize"],[1,"right-align","line-above","line-bottom","fw-bold","textsize"],[1,"col-md-12","mt-5","mb-5"],[1,"mb-5",2,"color","rgb(92, 92, 92)","font-size","20px","font-weight","bold"],[1,"row"],[1,"col-md-12","mt-3","text-center"],["src","../../assets/auth-assets/images/Footer set file.png","alt","",1,"img-fluid"],[1,"container","mt-5"],[1,"col-md-12","bb"],[1,"second-page11","mt-5"],[1,"second-page"],[1,"textsize",2,"color","rgb(85, 84, 84)"],[1,"fs-4","h5txt",2,"margin-bottom","3px"],[1,"second-page1"],[1,"fs-4",2,"color","rgb(85, 84, 84)"],[1,"container","mt-3"],[1,"col-md-12"],[1,"card-text"],[1,"second-text"],[2,"font-weight","bold","font-size","27px"],[1,"fs-5",2,"font-weight","bold"],[1,"second-text22"],[1,"second-text2"],[1,"fs-5","textsize"],[1,"second-text1"],[1,"counter-heading-text"],[1,"counter-heading-text1"],[1,"counter-heading-text2"],[1,"counter-heading-text3"],[1,"textsize",2,"text-align","end"],[1,"counter-heading-text4"],[1,"counter-heading-text5"],["class","col-md-12 mt-2 ",4,"ngFor","ngForOf"],[1,"mt-3"],[1,"col-md-12","last-counter1"],[1,"col-md-8","mt-4"],[1,"fs-4",2,"font-weight","bolder"],[1,"font-weight-bold","text-lg"],[1,"right-align","fw-bold"],[1,""],[1,"right-align"],[1,"fw-bold"],[1,"right-align","line-above","line-bottom","fw-bold"],[1,"no-print",3,"click"],[1,"col-md-12","mt-2"],[2,"font-size","18px"],[4,"ngIf"],[2,"margin-bottom","10px","font-size","18px"],[2,"margin-top","35px","font-size","18px","text-align","end"],[2,"font-weight","bold","font-size","18px","font-style","italic","margin-bottom","10px"]],template:function(r,l){r&1&&(N(0,Fe,2,0,"button",0),t(1,"section",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div"),h(6,"img",5),i()(),t(7,"div",6)(8,"div",7)(9,"div",8)(10,"h5",9),n(11),i(),t(12,"h5",9),n(13),i(),t(14,"h5",9),n(15),i()()()(),t(16,"div",10)(17,"div",11)(18,"div",12)(19,"h4",13),n(20),i()()(),t(21,"div",14)(22,"div",15)(23,"p"),n(24," Datum "),h(25,"br"),t(26,"span",16),n(27),i()(),t(28,"p"),n(29,"Seite "),h(30,"br"),t(31,"span",16),n(32," 1 VON 1"),i()(),t(33,"p"),n(34,"Auftrags-Nr. "),h(35,"br"),t(36,"span",16),n(37," 10125298"),i()(),t(38,"p"),n(39,"Kunden"),h(40,"br"),t(41,"span",16),n(42,"31001"),i()(),t(43,"p"),n(44,"SB "),h(45,"br"),t(46,"span",16),n(47," HS"),i()()()()(),t(48,"div",17)(49,"div",18)(50,"ul")(51,"li",19),n(52,"Pos. ID-Nr"),i(),t(53,"li",19),n(54,"Artikelbezeichnung"),i()()(),t(55,"div",18)(56,"ul")(57,"li",19),n(58,"Menge."),i(),t(59,"li",19),n(60,"Einh."),i(),t(61,"li",19),n(62,"Einzel-Pr."),i(),t(63,"li",19),n(64,"Gesamt-Pr."),i()()()(),h(65,"span",20),t(66,"div",21)(67,"div",18)(68,"ul")(69,"li",19),n(70,"1"),i(),t(71,"li",19),n(72,"600"),i(),t(73,"li",19),n(74),i()()(),t(75,"div",22)(76,"ul")(77,"li",19),n(78,"1,00"),i(),t(79,"li",19),n(80,"ST"),i(),t(81,"li",19),n(82),u(83,"number"),i(),t(84,"li",19),n(85),u(86,"number"),i()()()(),t(87,"div",23),h(88,"div",8),t(89,"div",24)(90,"div",25)(91,"table",26)(92,"tr")(93,"td",27),n(94," Gesamtpreis Netto EUR "),i(),t(95,"td",28),n(96),u(97,"number"),i()(),t(98,"tr")(99,"td",19),n(100,"Profitieren"),i(),t(101,"td",29),n(102),u(103,"number"),i()(),t(104,"tr")(105,"td",19),n(106,"zzgl. 19% MwSt EUR"),i(),t(107,"td",29),n(108),u(109,"number"),i()(),t(110,"tr")(111,"td",30),n(112,"Gesamtpreis Brutto EUR"),i(),t(113,"td",31),n(114),u(115,"number"),i()()()()()(),t(116,"div",32)(117,"P"),n(118,"Ihr Ansprechpartner: OHNE KEINE BETREUUNG"),i(),t(119,"p"),n(120,"Wenn nicht gesondert angegeben, gilt: Rechnungsdatum gleich Liefer-/ Leistungsdatum. "),i(),t(121,"p"),n(122,"Die Lieferung erfolgt zu unseren Allgemainen Geschaftsbedingungen, einzusehen auf unserer Internetseite."),i(),t(123,"p"),n(124," Auf Wunsch senden wir Ihnen diese gern zu. "),i(),t(125,"h4",33),n(126," Bzgl der Entgeltminderungen verweisen wir auf die aktuellen Zahlungs- und Konditionsvereinbarungen. "),i()()(),t(127,"div",34)(128,"div",35),h(129,"img",36),i()(),t(130,"div",37)(131,"div",38)(132,"div",11)(133,"div",39)(134,"div",40)(135,"h5",41),n(136," Rechnungsempfanger : "),i(),t(137,"h5",42),n(138),i(),t(139,"h5",42),n(140),i(),t(141,"h5",42),n(142),i()(),t(143,"div",43)(144,"h5",44),n(145," Rechnungssteller: "),i(),t(146,"h5",42),n(147,"K.O.T.I. GmbH"),i(),t(148,"h5",42),n(149,"Stockum 2 a"),i(),t(150,"h5",42),n(151,"48653 Coesfeld"),i(),t(152,"h5",42),n(153,"Deutschland"),i()()()()()(),t(154,"div",45)(155,"div",34)(156,"div",46)(157,"div",11)(158,"div",47)(159,"div",48)(160,"h4",49),n(161),i(),t(162,"h5",50),n(163),i(),t(164,"h5",50),n(165," Berechnet mit einem Aufschlag Von: 0% "),i()(),t(166,"div",51)(167,"div",52)(168,"h5",53),n(169,"Datum"),i(),t(170,"h5",53),n(171),i()(),t(172,"div",54)(173,"h5",53),n(174,"Seite"),i(),t(175,"h5",53),n(176,"1 Von "),i()()()()()(),t(177,"div",46)(178,"div",55)(179,"div",56)(180,"h5",53),n(181,"Artikel-Nr."),i()(),t(182,"div",57)(183,"h5",53),n(184,"Bezeichnung"),i()(),t(185,"div",58)(186,"p",59),n(187,"Menge Einh."),i()(),t(188,"div",60)(189,"p",59),n(190,"Einzel-Pr."),i()(),t(191,"div",61)(192,"p",59),n(193,"Gesamt-Pr."),i()()()(),h(194,"span",20),N(195,je,25,19,"div",62),h(196,"span",63),t(197,"div",64)(198,"div",65)(199,"h6",66),n(200," Umlagerungen von Filiale3 zu Filiale 1 "),i()(),t(201,"div",24)(202,"div",25)(203,"table",26)(204,"tr")(205,"td",67),n(206," Gesamtpreis Netto EUR "),i(),t(207,"td",68),n(208),u(209,"number"),i()(),t(210,"tr")(211,"td",69),n(212,"Profitieren"),i(),t(213,"td",70),n(214),u(215,"number"),i()(),t(216,"tr")(217,"td"),n(218,"zzgl. 19% MwSt EUR"),i(),t(219,"td",70),n(220),u(221,"number"),i()(),t(222,"tr")(223,"td",71),n(224,"Gesamtpreis Brutto EUR"),i(),t(225,"td",72),n(226),u(227,"number"),i()()()()()()()()()()),r&2&&(A("ngIf",!l.isPrint),d(11),P(l._bill.customer),d(2),D("",l._bill.street,",",l._bill.zipCode,""),d(2),P(l._bill.country),d(5),m(" RECHNUNG ",l._bill.billNo," "),d(7),m(" ",l._bill.date,""),d(47),m("UMLAGERUNGSRECHNUNG ",l._bill.date,""),d(8),m(" ",f(83,27,l.totalCalculation.totalAmount,"1.2-2")," "),d(3),m(" ",f(86,30,l.totalCalculation.totalAmount,"1.2-2")," "),d(11),m(" ",f(97,33,l.totalCalculation.totalAmount,"1.2-2")," "),d(6),m(" ",f(103,36,l.totalCalculation.profit,"1.2-2")," "),d(6),m(" ",f(109,39,l.totalCalculation.gstAmt,"1.2-2")," "),d(6),m(" ",f(115,42,l.totalCalculation.grossAmt,"1.2-2")," "),d(24),m(" ",l._bill.customer," "),d(2),D(" ",l._bill.street,",",l._bill.zipCode," "),d(2),m(" ",l._bill.country," "),d(19),m(" ANLAGE ZUR RECHNUNG ",l._bill.billNo," "),d(2),D(" Abrechnung der Umlagerung Vom ",l._bill.date2," bis ",l._bill.date," "),d(8),P(l._bill.date2),d(24),A("ngForOf",l.billDetails),d(13),m(" ",f(209,45,l.totalCalculation.totalAmount,"1.2-2")," "),d(6),m(" ",f(215,48,l.totalCalculation.profit,"1.2-2")," "),d(6),m(" ",f(221,51,l.totalCalculation.gstAmt,"1.2-2")," "),d(6),m(" ",f(227,54,l.totalCalculation.grossAmt,"1.2-2")," "))},dependencies:[ae,re,le],styles:[".invoice-table[_ngcontent-%COMP%]{width:100%}.invoice-table[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}.table-colour[_ngcontent-%COMP%]{border-color:#fff}.right-align[_ngcontent-%COMP%]{text-align:right}.line-above[_ngcontent-%COMP%]{border-top:1px solid black;width:100px}.line-bottom[_ngcontent-%COMP%]{border-bottom:2px solid black}.bb[_ngcontent-%COMP%]{margin-top:600px}.text-hd-n[_ngcontent-%COMP%]{display:flex;gap:20px;width:100%;font-size:17px}.hd-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{width:100%}.card1[_ngcontent-%COMP%]{border:none}.line1[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:inline;font-size:15px}.line1[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{display:flex;gap:40px}.bill[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{display:flex;gap:60px}.mdle-hd[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.md-hd1[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin-left:24px}.space1[_ngcontent-%COMP%]{min-height:40vh}.footer[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:inline}.footer[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{display:flex;gap:215px}.footer11[_ngcontent-%COMP%]{display:flex}.footer12[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none}.footer12[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{font-size:10px}@media (max-width: 767px){.footer11[_ngcontent-%COMP%]{display:flex}}.second-page11[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.second-page[_ngcontent-%COMP%]{margin-top:180px}.card-text[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.second-text22[_ngcontent-%COMP%]{display:flex;gap:20px}.footer[_ngcontent-%COMP%]{display:flex;gap:18%;height:auto!important;margin-top:5px!important}.line[_ngcontent-%COMP%]{border:1px solid #000;margin-right:30px}.counter-heading-text[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:10px}.counter-heading-text1[_ngcontent-%COMP%]{width:24%}.counter-heading-text2[_ngcontent-%COMP%]{width:100%;line-height:10px}.counter-heading-text3[_ngcontent-%COMP%]{width:25%}.counter-heading-text3[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:15px}.counter-heading-text4[_ngcontent-%COMP%]{width:25%}.counter-heading-text4[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:15px}.counter-heading-text5[_ngcontent-%COMP%]{width:25%}.counter-heading-text5[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:15px}.last-counter1[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.mdle-hd[_ngcontent-%COMP%]{display:flex;gap:20px}.count[_ngcontent-%COMP%]{border:none}.mat-h5[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-h5[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font:400 calc(20px* .83) / 20px Roboto,sans-serif;margin:0 0 12px}@media print{.no-print[_ngcontent-%COMP%]{display:none}}.textsize[_ngcontent-%COMP%]{font-size:20px!important}.spacing-row[_ngcontent-%COMP%]{margin-bottom:15px}.page[_ngcontent-%COMP%]{page-break-after:always}@page{margin-bottom:10px}.font1[_ngcontent-%COMP%]{font-size:25px!important}p[_ngcontent-%COMP%]{font-size:20px}.h5txt[_ngcontent-%COMP%]{font-weight:700}.mt[_ngcontent-%COMP%]{margin-top:150px}"]});let s=e;return s})();var Te=[{path:"",component:ue,loadChildren:()=>import("./chunk-AHZK3UY4.js").then(s=>s.AuthModule)},{path:"admin",component:be,loadChildren:()=>import("./chunk-3OJLQFSX.js").then(s=>s.AdminModule),data:{title:"Admin"}},{path:"my/:id",component:Oe},{path:"**",component:Me,pathMatch:"full"}],we=(()=>{let e=class e{};e.\u0275fac=function(r){return new(r||e)},e.\u0275mod=I({type:e}),e.\u0275inj=w({imports:[T.forRoot(Te),T]});let s=e;return s})();var Ie=(()=>{let e=class e{};e.\u0275fac=function(r){return new(r||e)},e.\u0275cmp=C({type:e,selectors:[["app-root"]],decls:2,vars:0,template:function(r,l){r&1&&h(0,"router-outlet")(1,"ngx-ui-loader")},dependencies:[ge,ve]});let s=e;return s})();var Ue="@",Le=(()=>{let e=class e{constructor(a,r,l,c,x){this.doc=a,this.delegate=r,this.zone=l,this.animationType=c,this.moduleImpl=x,this._rendererFactoryPromise=null,this.scheduler=K(X,{optional:!0})}ngOnDestroy(){this._engine?.flush()}loadImpl(){return(this.moduleImpl??import("./chunk-RR3UDXTO.js")).catch(r=>{throw new H(5300,!1)}).then(({\u0275createEngine:r,\u0275AnimationRendererFactory:l})=>{this._engine=r(this.animationType,this.doc,this.scheduler);let c=new l(this.delegate,this._engine,this.zone);return this.delegate=c,c})}createRenderer(a,r){let l=this.delegate.createRenderer(a,r);if(l.\u0275type===0)return l;typeof l.throwOnSyntheticProps=="boolean"&&(l.throwOnSyntheticProps=!1);let c=new U(l);return r?.data?.animation&&!this._rendererFactoryPromise&&(this._rendererFactoryPromise=this.loadImpl()),this._rendererFactoryPromise?.then(x=>{let z=x.createRenderer(a,r);c.use(z)}).catch(x=>{c.use(l)}),c}begin(){this.delegate.begin?.()}end(){this.delegate.end?.()}whenRenderingDone(){return this.delegate.whenRenderingDone?.()??Promise.resolve()}};e.\u0275fac=function(r){Z()},e.\u0275prov=$({token:e,factory:e.\u0275fac});let s=e;return s})(),U=class{constructor(e){this.delegate=e,this.replay=[],this.\u0275type=1}use(e){if(this.delegate=e,this.replay!==null){for(let o of this.replay)o(e);this.replay=null}}get data(){return this.delegate.data}destroy(){this.replay=null,this.delegate.destroy()}createElement(e,o){return this.delegate.createElement(e,o)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}get destroyNode(){return this.delegate.destroyNode}appendChild(e,o){this.delegate.appendChild(e,o)}insertBefore(e,o,a,r){this.delegate.insertBefore(e,o,a,r)}removeChild(e,o,a){this.delegate.removeChild(e,o,a)}selectRootElement(e,o){return this.delegate.selectRootElement(e,o)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,o,a,r){this.delegate.setAttribute(e,o,a,r)}removeAttribute(e,o,a){this.delegate.removeAttribute(e,o,a)}addClass(e,o){this.delegate.addClass(e,o)}removeClass(e,o){this.delegate.removeClass(e,o)}setStyle(e,o,a,r){this.delegate.setStyle(e,o,a,r)}removeStyle(e,o,a){this.delegate.removeStyle(e,o,a)}setProperty(e,o,a){this.shouldReplay(o)&&this.replay.push(r=>r.setProperty(e,o,a)),this.delegate.setProperty(e,o,a)}setValue(e,o){this.delegate.setValue(e,o)}listen(e,o,a){return this.shouldReplay(o)&&this.replay.push(r=>r.listen(e,o,a)),this.delegate.listen(e,o,a)}shouldReplay(e){return this.replay!==null&&e.startsWith(Ue)}};function Ne(s="animations"){return ee("NgAsyncAnimations"),q([{provide:J,useFactory:(e,o,a)=>new Le(e,o,a,s),deps:[oe,de,te]},{provide:Y,useValue:s==="noop"?"NoopAnimations":"BrowserAnimations"}])}var Ge={bgsColor:"red",text:"Loading",textColor:"#85c5ed",textPosition:"center-center",pbColor:"#85c5ed",fgsColor:"#85c5ed",fgsType:"three-strings",fgsSize:100,pbDirection:fe.leftToRight,pbThickness:5},Ae=(()=>{let e=class e{};e.\u0275fac=function(r){return new(r||e)},e.\u0275mod=I({type:e,bootstrap:[Ie]}),e.\u0275inj=w({providers:[Ne(),{provide:_e,useValue:"en-GB"}],imports:[ce,we,se,Ce.forRoot(Ge)]});let s=e;return s})();me().bootstrapModule(Ae).catch(s=>console.error(s));
