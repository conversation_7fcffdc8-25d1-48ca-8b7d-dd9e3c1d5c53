import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';


export interface UserData {
  SrNo: string;
  Work: string;
  Name: string;
 ToDate: string;
 FromDate:string;
 Quality:string;
 Design:string;
 Rate:string;
  Action: string;
 }

const ELEMENT_DATA: UserData[] = [
  { SrNo: ' ',
  Work: ' ',
    Name: ' ',
   
    ToDate:'',
    FromDate:'',
    Quality:'',
    Design:'',
    Rate:'',
    Action: ' ',

},
];
 


@Component({
  selector: 'app-finishing-head',
  templateUrl: './finishing-head.component.html',
  styleUrl: './finishing-head.component.css'
})


export class FinishingHeadComponent  implements AfterViewInit , OnInit{

  work="option2";
  Name="option1";
  Design="option3";
  Quality="option4"  


  displayedColumns: string[] = ['SrNo', 'Work','Name','ToDate','FromDate','Quality','Design','Rate','Action'];
dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);

@ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

ngOnInit(): void {
    throw new Error('Method not implemented.');
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

  }
  
}
