/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,h=b&&!y.call({1:2},1);g.f=h?function(t){var n=b(this,t);return!!n&&n.enumerable}:y;var d,m,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},w=f,S=Function.prototype,j=S.call,O=w&&S.bind.bind(j,j),P=function(t){return w?O(t):function(){return j.apply(t,arguments)}},T=P,x=T({}.toString),A=T("".slice),E=function(t){return A(x(t),8,-1)},C=E,F=P,M=function(t){if("Function"===C(t))return F(t)},R=c,D=E,z=Object,I=M("".split),L=R((function(){return!z("z").propertyIsEnumerable(0)}))?function(t){return"String"==D(t)?I(t,""):z(t)}:z,k=function(t){return null==t},H=k,N=TypeError,_=function(t){if(H(t))throw N("Can't call method on "+t);return t},G=L,U=_,B=function(t){return G(U(t))},q="object"==typeof document&&document.all,W={all:q,IS_HTMLDDA:void 0===q&&void 0!==q},K=W.all,J=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===K}:function(t){return"function"==typeof t},Q=J,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,$=J,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=M({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(m=(d=ct.split("."))[0]>0&&d[0]<4?1:+(d[0]+d[1])),!m&&et&&(!(d=et.match(/Edge\/(\d+)/))||d[1]>=74)&&(d=et.match(/Chrome\/(\d+)/))&&(m=+d[1]);var at=m,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=J,bt=nt,ht=Object,dt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&bt(n.prototype,ht(t))},mt=String,vt=J,wt=function(t){try{return mt(t)}catch(t){return"Object"}},St=TypeError,jt=function(t){if(vt(t))return t;throw St(wt(t)+" is not a function")},Ot=k,Pt=p,Tt=J,xt=X,At=TypeError,Et={exports:{}},Ct=i,Ft=Object.defineProperty,Mt=function(t,n){try{Ft(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Rt=Mt,Dt="__core-js_shared__",zt=i[Dt]||Rt(Dt,{}),It=zt;(Et.exports=function(t,n){return It[t]||(It[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Lt=_,kt=Object,Ht=function(t){return kt(Lt(t))},Nt=Ht,_t=M({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return _t(Nt(t),n)},Ut=M,Bt=0,qt=Math.random(),Wt=Ut(1..toString),Kt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Bt+qt,36)},Jt=i,Qt=Et.exports,Vt=Gt,Xt=Kt,Yt=st,$t=pt,Zt=Qt("wks"),tn=Jt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,cn=dt,an=function(t,n){var r=t[n];return Ot(r)?void 0:jt(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!xt(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!xt(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!xt(e=Pt(r,t)))return e;throw At("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=dt,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},bn=X,hn=i.document,dn=bn(hn)&&bn(hn.createElement),mn=function(t){return dn?hn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(mn("div"),"a",{get:function(){return 7}}).a})),wn=a,Sn=p,jn=g,On=v,Pn=B,Tn=yn,xn=Gt,An=vn,En=Object.getOwnPropertyDescriptor;u.f=wn?En:function(t,n){if(t=Pn(t),n=Tn(n),An)try{return En(t,n)}catch(t){}if(xn(t,n))return On(!Sn(jn.f,t,n),t[n])};var Cn={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mn=X,Rn=String,Dn=TypeError,zn=function(t){if(Mn(t))return t;throw Dn(Rn(t)+" is not an object")},In=a,Ln=vn,kn=Fn,Hn=zn,Nn=yn,_n=TypeError,Gn=Object.defineProperty,Un=Object.getOwnPropertyDescriptor,Bn="enumerable",qn="configurable",Wn="writable";Cn.f=In?kn?function(t,n,r){if(Hn(t),n=Nn(n),Hn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=Un(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:qn in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(Hn(t),n=Nn(n),Hn(r),Ln)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw _n("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Kn=Cn,Jn=v,Qn=a?function(t,n,r){return Kn.f(t,n,Jn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=a,Yn=Gt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=J,er=zt,or=M(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=J,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,gr=Kt,yr=pr("keys"),br={},hr=sr,dr=i,mr=X,vr=Qn,wr=Gt,Sr=zt,jr=function(t){return yr[t]||(yr[t]=gr(t))},Or=br,Pr="Object already initialized",Tr=dr.TypeError,xr=dr.WeakMap;if(hr||Sr.state){var Ar=Sr.state||(Sr.state=new xr);Ar.get=Ar.get,Ar.has=Ar.has,Ar.set=Ar.set,ir=function(t,n){if(Ar.has(t))throw Tr(Pr);return n.facade=t,Ar.set(t,n),n},ur=function(t){return Ar.get(t)||{}},cr=function(t){return Ar.has(t)}}else{var Er=jr("state");Or[Er]=!0,ir=function(t,n){if(wr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return wr(t,Er)?t[Er]:{}},cr=function(t){return wr(t,Er)}}var Cr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!mr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=c,Mr=J,Rr=Gt,Dr=a,zr=nr.CONFIGURABLE,Ir=ar,Lr=Cr.enforce,kr=Cr.get,Hr=Object.defineProperty,Nr=Dr&&!Fr((function(){return 8!==Hr((function(){}),"length",{value:8}).length})),_r=String(String).split("String"),Gr=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Rr(t,"name")||zr&&t.name!==n)&&(Dr?Hr(t,"name",{value:n,configurable:!0}):t.name=n),Nr&&r&&Rr(r,"arity")&&t.length!==r.arity&&Hr(t,"length",{value:r.arity});try{r&&Rr(r,"constructor")&&r.constructor?Dr&&Hr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Lr(t);return Rr(e,"source")||(e.source=_r.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Mr(this)&&kr(this).source||Ir(this)}),"toString");var Ur=J,Br=Cn,qr=Vn.exports,Wr=Mt,Kr={},Jr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Jr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=Gt,le=B,se=ae.indexOf,pe=br,ge=M([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Kr.f=Object.getOwnPropertyNames||function(t){return ye(t,be)};var he={};he.f=Object.getOwnPropertySymbols;var de=tt,me=Kr,ve=he,we=zn,Se=M([].concat),je=de("Reflect","ownKeys")||function(t){var n=me.f(we(t)),r=ve.f;return r?Se(n,r(t)):n},Oe=Gt,Pe=je,Te=u,xe=Cn,Ae=c,Ee=J,Ce=/#|\.prototype\./,Fe=function(t,n){var r=Re[Me(t)];return r==ze||r!=De&&(Ee(n)?Ae(n):!!n)},Me=Fe.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Re=Fe.data={},De=Fe.NATIVE="N",ze=Fe.POLYFILL="P",Ie=Fe,Le=i,ke=u.f,He=Qn,Ne=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Ur(r)&&qr(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},_e=Mt,Ge=function(t,n,r){for(var e=Pe(n),o=xe.f,i=Te.f,u=0;u<e.length;u++){var c=e[u];Oe(t,c)||r&&Oe(r,c)||o(t,c,i(n,c))}},Ue=Ie,Be=E,qe=Array.isArray||function(t){return"Array"==Be(t)},We=TypeError,Ke=yn,Je=Cn,Qe=v,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=J,$e=E,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=M,eo=c,oo=J,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,ho=qe,mo=bo,vo=X,wo=en("species"),So=Array,jo=function(t){var n;return ho(t)&&(n=t.constructor,(mo(n)&&(n===So||ho(n.prototype))||vo(n)&&null===(n=n[wo]))&&(n=void 0)),void 0===n?So:n},Oo=c,Po=at,To=en("species"),xo=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Le:f?Le[c]||_e(c,{}):(Le[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=ke(r,e))&&u.value:r[e],!Ue(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&He(i,"sham",!0),Ne(r,e,i,t)}},Ao=c,Eo=qe,Co=X,Fo=Ht,Mo=ee,Ro=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Do=function(t,n,r){var e=Ke(n);e in t?Je.f(t,e,Qe(0,r)):t[e]=r},zo=function(t,n){return new(jo(t))(0===n?0:n)},Io=function(t){return Po>=51||!Oo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=at,ko=en("isConcatSpreadable"),Ho=Lo>=51||!Ao((function(){var t=[];return t[ko]=!1,t.concat()[0]!==t})),No=Io("concat"),_o=function(t){if(!Co(t))return!1;var n=t[ko];return void 0!==n?!!n:Eo(t)};xo({target:"Array",proto:!0,arity:1,forced:!Ho||!No},{concat:function(t){var n,r,e,o,i,u=Fo(this),c=zo(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(_o(i=-1===n?u:arguments[n]))for(o=Mo(i),Ro(a+o),r=0;r<o;r++,a++)r in i&&Do(c,a,i[r]);else Ro(a+1),Do(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["hu-HU"]=r.default.fn.bootstrapTable.locales.hu={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Betöltés, kérem várjon"},formatRecordsPerPage:function(t){return"".concat(t," rekord per oldal")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Megjelenítve ".concat(t," - ").concat(n," / ").concat(r," összesen (filtered from ").concat(e," total rows)"):"Megjelenítve ".concat(t," - ").concat(n," / ").concat(r," összesen")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Keresés"},formatNoMatches:function(){return"Nincs találat"},formatPaginationSwitch:function(){return"Lapozó elrejtése/megjelenítése"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Frissítés"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Oszlopok"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Összes"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["hu-HU"])}));
