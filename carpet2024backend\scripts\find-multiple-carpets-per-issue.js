// Script to find if any issue number had multiple carpets issued
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function findMultipleCarpetsPerIssue() {
  console.log('🔍 SEARCHING FOR ISSUES WITH MULTIPLE CARPETS');
  console.log('='.repeat(80));
  
  try {
    const db = mongoose.connection.db;
    
    // Get actual issues from carpetorderissues collection
    const issuesCollection = db.collection('carpetorderissues');
    const actualIssues = await issuesCollection.find({}).sort({ Br_issueNo: 1 }).toArray();
    
    console.log(`📊 Total actual issues found: ${actualIssues.length}`);
    
    // Check each issue for PCS (pieces) information
    console.log('\n📋 ISSUE DETAILS WITH PCS INFORMATION:');
    console.log('='.repeat(100));
    console.log('Issue No | PCS Ordered | PCS Received | PCS Waiting | Weaver | Design | Quality');
    console.log('='.repeat(100));
    
    const issuesWithMultiplePCS = [];
    const issuesWithMismatchedPCS = [];
    
    actualIssues.forEach((issue, index) => {
      const issueNo = issue.Br_issueNo;
      const pcsOrdered = issue.pcs || 0;
      const pcsReceived = issue.PCSReceived || 0;
      const pcsWaiting = issue.PCSWaitingToBeReceived || 0;
      const weaver = issue.weaver?.name || issue.weaver || 'N/A';
      const design = issue.design?.design || issue.design || 'N/A';
      const quality = issue.quality?.quality || issue.quality || 'N/A';
      
      console.log(`${issueNo.padEnd(12)} | ${pcsOrdered.toString().padEnd(11)} | ${pcsReceived.toString().padEnd(12)} | ${pcsWaiting.toString().padEnd(11)} | ${weaver.toString().substring(0, 10).padEnd(10)} | ${design.toString().substring(0, 8).padEnd(8)} | ${quality.toString().substring(0, 7).padEnd(7)}`);
      
      // Check for issues with multiple PCS ordered
      if (pcsOrdered > 1) {
        issuesWithMultiplePCS.push({
          issueNo: issueNo,
          pcsOrdered: pcsOrdered,
          pcsReceived: pcsReceived,
          pcsWaiting: pcsWaiting,
          weaver: weaver,
          design: design,
          quality: quality,
          area: issue.area,
          rate: issue.rate,
          amount: issue.amount
        });
      }
      
      // Check for issues where received != ordered
      if (pcsReceived !== pcsOrdered) {
        issuesWithMismatchedPCS.push({
          issueNo: issueNo,
          pcsOrdered: pcsOrdered,
          pcsReceived: pcsReceived,
          pcsWaiting: pcsWaiting,
          weaver: weaver,
          design: design,
          quality: quality
        });
      }
    });
    
    // Show issues with multiple PCS ordered
    console.log('\n🔍 ISSUES WITH MULTIPLE PCS ORDERED:');
    console.log('='.repeat(80));
    
    if (issuesWithMultiplePCS.length > 0) {
      console.log(`✅ Found ${issuesWithMultiplePCS.length} issues with multiple PCS ordered:`);
      
      issuesWithMultiplePCS.forEach((issue, index) => {
        console.log(`\n${index + 1}. ${issue.issueNo}:`);
        console.log(`   📦 PCS Ordered: ${issue.pcsOrdered}`);
        console.log(`   📥 PCS Received: ${issue.pcsReceived}`);
        console.log(`   ⏳ PCS Waiting: ${issue.pcsWaiting}`);
        console.log(`   👤 Weaver: ${issue.weaver}`);
        console.log(`   🎨 Design: ${issue.design}`);
        console.log(`   ⭐ Quality: ${issue.quality}`);
        console.log(`   📐 Area: ${issue.area || 'N/A'} Sq.Ft`);
        console.log(`   💰 Rate: ₹${issue.rate || 'N/A'}/Sq.Ft`);
        console.log(`   💵 Amount: ₹${issue.amount || 'N/A'}`);
        console.log('   ' + '-'.repeat(60));
      });
    } else {
      console.log('❌ No issues found with multiple PCS ordered');
    }
    
    // Show issues with mismatched PCS
    console.log('\n🔍 ISSUES WITH MISMATCHED PCS (Received ≠ Ordered):');
    console.log('='.repeat(80));
    
    if (issuesWithMismatchedPCS.length > 0) {
      console.log(`⚠️ Found ${issuesWithMismatchedPCS.length} issues with mismatched PCS:`);
      
      issuesWithMismatchedPCS.forEach((issue, index) => {
        console.log(`\n${index + 1}. ${issue.issueNo}:`);
        console.log(`   📦 Ordered: ${issue.pcsOrdered} | 📥 Received: ${issue.pcsReceived} | ⏳ Waiting: ${issue.pcsWaiting}`);
        console.log(`   👤 Weaver: ${issue.weaver} | 🎨 Design: ${issue.design} | ⭐ Quality: ${issue.quality}`);
        
        if (issue.pcsReceived > issue.pcsOrdered) {
          console.log(`   ⚠️ OVER-RECEIVED: ${issue.pcsReceived - issue.pcsOrdered} extra pieces`);
        } else if (issue.pcsReceived < issue.pcsOrdered) {
          console.log(`   ⚠️ UNDER-RECEIVED: ${issue.pcsOrdered - issue.pcsReceived} pieces pending`);
        }
      });
    } else {
      console.log('✅ All issues have matching PCS (Received = Ordered)');
    }
    
    // Now check actual carpet records to see if any issue has multiple carpets
    const carpetsCollection = db.collection('carpetreceiveds');
    const allCarpets = await carpetsCollection.find({ receiveNo: { $regex: /^K-/ } }).toArray();
    
    // Group carpets by their issue numbers
    const carpetsByIssue = {};
    allCarpets.forEach(carpet => {
      const issueNo = carpet.issueNo?.Br_issueNo || carpet.receiveNo;
      if (!carpetsByIssue[issueNo]) {
        carpetsByIssue[issueNo] = [];
      }
      carpetsByIssue[issueNo].push({
        carpetNo: carpet.receiveNo,
        weaver: carpet.weaverName,
        design: carpet.design,
        quality: carpet.quality,
        area: carpet.area,
        amount: carpet.amount,
        receivingDate: carpet.receivingDate
      });
    });
    
    // Find issues with multiple carpet records
    console.log('\n🔍 CARPET RECORDS GROUPED BY ISSUE:');
    console.log('='.repeat(80));
    
    const issuesWithMultipleCarpetRecords = [];
    Object.keys(carpetsByIssue).forEach(issueNo => {
      const carpets = carpetsByIssue[issueNo];
      if (carpets.length > 1) {
        issuesWithMultipleCarpetRecords.push({
          issueNo: issueNo,
          carpetCount: carpets.length,
          carpets: carpets
        });
      }
    });
    
    if (issuesWithMultipleCarpetRecords.length > 0) {
      console.log(`✅ Found ${issuesWithMultipleCarpetRecords.length} issues with multiple carpet records:`);
      
      issuesWithMultipleCarpetRecords.forEach((issue, index) => {
        console.log(`\n${index + 1}. ${issue.issueNo} has ${issue.carpetCount} carpet records:`);
        issue.carpets.forEach((carpet, carpetIndex) => {
          console.log(`   ${carpetIndex + 1}. ${carpet.carpetNo}`);
          console.log(`      Weaver: ${carpet.weaver || 'N/A'}`);
          console.log(`      Design: ${carpet.design || 'N/A'} | Quality: ${carpet.quality || 'N/A'}`);
          console.log(`      Area: ${carpet.area || 'N/A'} | Amount: ₹${carpet.amount || 'N/A'}`);
          console.log(`      Received: ${carpet.receivingDate ? new Date(carpet.receivingDate).toISOString().split('T')[0] : 'N/A'}`);
        });
        console.log('   ' + '-'.repeat(60));
      });
    } else {
      console.log('❌ No issues found with multiple carpet records');
      console.log('✅ Each issue has exactly 1 carpet record');
    }
    
    return {
      issuesWithMultiplePCS,
      issuesWithMismatchedPCS,
      issuesWithMultipleCarpetRecords,
      totalIssues: actualIssues.length,
      totalCarpets: allCarpets.length
    };
    
  } catch (error) {
    console.error('❌ Error finding multiple carpets per issue:', error);
    return null;
  }
}

async function main() {
  console.log('🔍 SEARCHING FOR ISSUES WITH MULTIPLE CARPETS');
  console.log('(Finding which issue numbers had multiple carpets issued)');
  console.log('='.repeat(80));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Find multiple carpets per issue
    const result = await findMultipleCarpetsPerIssue();

    if (result) {
      console.log('\n🎯 FINAL ANALYSIS:');
      console.log('='.repeat(50));
      console.log(`📊 Total issues analyzed: ${result.totalIssues}`);
      console.log(`📊 Total carpet records: ${result.totalCarpets}`);
      console.log(`📊 Issues with multiple PCS ordered: ${result.issuesWithMultiplePCS.length}`);
      console.log(`📊 Issues with mismatched PCS: ${result.issuesWithMismatchedPCS.length}`);
      console.log(`📊 Issues with multiple carpet records: ${result.issuesWithMultipleCarpetRecords.length}`);
      
      if (result.issuesWithMultiplePCS.length > 0) {
        console.log('\n✅ FOUND ISSUES THAT SHOULD HAVE MULTIPLE CARPETS!');
        console.log('   These issues were ordered for multiple pieces but only have 1 carpet record');
        console.log('   The missing carpet records (K-2400030 to K-2400053) should be mapped to these issues');
      } else {
        console.log('\n❌ NO ISSUES WITH MULTIPLE PCS FOUND');
        console.log('   All issues were ordered for single pieces only');
      }
    }

  } catch (error) {
    console.error('❌ Analysis failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 MULTIPLE CARPETS SEARCH COMPLETE!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
