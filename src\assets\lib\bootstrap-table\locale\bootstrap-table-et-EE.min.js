/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!y.call({1:2},1);g.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:y;var m,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},w=f,S=Function.prototype,O=S.call,j=w&&S.bind.bind(O,O),P=function(t){return w?j(t):function(){return O.apply(t,arguments)}},T=P,E=T({}.toString),x=T("".slice),A=function(t){return x(E(t),8,-1)},C=A,k=P,F=function(t){if("Function"===C(t))return k(t)},M=c,R=A,D=Object,I=F("".split),L=M((function(){return!D("z").propertyIsEnumerable(0)}))?function(t){return"String"==R(t)?I(t,""):D(t)}:D,N=function(t){return null==t},_=N,z=TypeError,H=function(t){if(_(t))throw z("Can't call method on "+t);return t},G=L,q=H,B=function(t){return G(q(t))},U="object"==typeof document&&document.all,V={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},W=V.all,K=V.IS_HTMLDDA?function(t){return"function"==typeof t||t===W}:function(t){return"function"==typeof t},J=K,Q=V.all,X=V.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:J(t)||t===Q}:function(t){return"object"==typeof t?null!==t:J(t)},Y=i,$=K,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=F({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(h=(m=ct.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!h&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(h=+m[1]);var at=h,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=K,bt=nt,dt=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&bt(n.prototype,dt(t))},ht=String,vt=K,wt=function(t){try{return ht(t)}catch(t){return"Object"}},St=TypeError,Ot=function(t){if(vt(t))return t;throw St(wt(t)+" is not a function")},jt=N,Pt=p,Tt=K,Et=X,xt=TypeError,At={exports:{}},Ct=i,kt=Object.defineProperty,Ft=function(t,n){try{kt(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Mt=Ft,Rt="__core-js_shared__",Dt=i[Rt]||Mt(Rt,{}),It=Dt;(At.exports=function(t,n){return It[t]||(It[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Lt=H,Nt=Object,_t=function(t){return Nt(Lt(t))},zt=_t,Ht=F({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return Ht(zt(t),n)},qt=F,Bt=0,Ut=Math.random(),Vt=qt(1..toString),Wt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Vt(++Bt+Ut,36)},Kt=i,Jt=At.exports,Qt=Gt,Xt=Wt,Yt=st,$t=pt,Zt=Jt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Qt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Qt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,cn=mt,an=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!Et(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!Et(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!Et(e=Pt(r,t)))return e;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=mt,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},bn=X,dn=i.document,mn=bn(dn)&&bn(dn.createElement),hn=function(t){return mn?dn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),wn=a,Sn=p,On=g,jn=v,Pn=B,Tn=yn,En=Gt,xn=vn,An=Object.getOwnPropertyDescriptor;u.f=wn?An:function(t,n){if(t=Pn(t),n=Tn(n),xn)try{return An(t,n)}catch(t){}if(En(t,n))return jn(!Sn(On.f,t,n),t[n])};var Cn={},kn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Fn=X,Mn=String,Rn=TypeError,Dn=function(t){if(Fn(t))return t;throw Rn(Mn(t)+" is not an object")},In=a,Ln=vn,Nn=kn,_n=Dn,zn=yn,Hn=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Un="configurable",Vn="writable";Cn.f=In?Nn?function(t,n,r){if(_n(t),n=zn(n),_n(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Vn in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(_n(t),n=zn(n),_n(r),Ln)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Hn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Wn=Cn,Kn=v,Jn=a?function(t,n,r){return Wn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Qn={exports:{}},Xn=a,Yn=Gt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=K,er=Dt,or=F(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=At.exports,gr=Wt,yr=pr("keys"),br={},dr=sr,mr=i,hr=X,vr=Jn,wr=Gt,Sr=Dt,Or=function(t){return yr[t]||(yr[t]=gr(t))},jr=br,Pr="Object already initialized",Tr=mr.TypeError,Er=mr.WeakMap;if(dr||Sr.state){var xr=Sr.state||(Sr.state=new Er);xr.get=xr.get,xr.has=xr.has,xr.set=xr.set,ir=function(t,n){if(xr.has(t))throw Tr(Pr);return n.facade=t,xr.set(t,n),n},ur=function(t){return xr.get(t)||{}},cr=function(t){return xr.has(t)}}else{var Ar=Or("state");jr[Ar]=!0,ir=function(t,n){if(wr(t,Ar))throw Tr(Pr);return n.facade=t,vr(t,Ar,n),n},ur=function(t){return wr(t,Ar)?t[Ar]:{}},cr=function(t){return wr(t,Ar)}}var Cr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},kr=c,Fr=K,Mr=Gt,Rr=a,Dr=nr.CONFIGURABLE,Ir=ar,Lr=Cr.enforce,Nr=Cr.get,_r=Object.defineProperty,zr=Rr&&!kr((function(){return 8!==_r((function(){}),"length",{value:8}).length})),Hr=String(String).split("String"),Gr=Qn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Mr(t,"name")||Dr&&t.name!==n)&&(Rr?_r(t,"name",{value:n,configurable:!0}):t.name=n),zr&&r&&Mr(r,"arity")&&t.length!==r.arity&&_r(t,"length",{value:r.arity});try{r&&Mr(r,"constructor")&&r.constructor?Rr&&_r(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Lr(t);return Mr(e,"source")||(e.source=Hr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Fr(this)&&Nr(this).source||Ir(this)}),"toString");var qr=K,Br=Cn,Ur=Qn.exports,Vr=Ft,Wr={},Kr=Math.ceil,Jr=Math.floor,Qr=Math.trunc||function(t){var n=+t;return(n>0?Jr:Kr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Qr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=Gt,le=B,se=ae.indexOf,pe=br,ge=F([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Wr.f=Object.getOwnPropertyNames||function(t){return ye(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var me=tt,he=Wr,ve=de,we=Dn,Se=F([].concat),Oe=me("Reflect","ownKeys")||function(t){var n=he.f(we(t)),r=ve.f;return r?Se(n,r(t)):n},je=Gt,Pe=Oe,Te=u,Ee=Cn,xe=c,Ae=K,Ce=/#|\.prototype\./,ke=function(t,n){var r=Me[Fe(t)];return r==De||r!=Re&&(Ae(n)?xe(n):!!n)},Fe=ke.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Me=ke.data={},Re=ke.NATIVE="N",De=ke.POLYFILL="P",Ie=ke,Le=i,Ne=u.f,_e=Jn,ze=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Vr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},He=Ft,Ge=function(t,n,r){for(var e=Pe(n),o=Ee.f,i=Te.f,u=0;u<e.length;u++){var c=e[u];je(t,c)||r&&je(r,c)||o(t,c,i(n,c))}},qe=Ie,Be=A,Ue=Array.isArray||function(t){return"Array"==Be(t)},Ve=TypeError,We=yn,Ke=Cn,Je=v,Qe={};Qe[en("toStringTag")]="z";var Xe="[object z]"===String(Qe),Ye=K,$e=A,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=F,eo=c,oo=K,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,mo=Ue,ho=bo,vo=X,wo=en("species"),So=Array,Oo=function(t){var n;return mo(t)&&(n=t.constructor,(ho(n)&&(n===So||mo(n.prototype))||vo(n)&&null===(n=n[wo]))&&(n=void 0)),void 0===n?So:n},jo=c,Po=at,To=en("species"),Eo=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Le:f?Le[c]||He(c,{}):(Le[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Ne(r,e))&&u.value:r[e],!qe(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&_e(i,"sham",!0),ze(r,e,i,t)}},xo=c,Ao=Ue,Co=X,ko=_t,Fo=ee,Mo=function(t){if(t>9007199254740991)throw Ve("Maximum allowed index exceeded");return t},Ro=function(t,n,r){var e=We(n);e in t?Ke.f(t,e,Je(0,r)):t[e]=r},Do=function(t,n){return new(Oo(t))(0===n?0:n)},Io=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=at,No=en("isConcatSpreadable"),_o=Lo>=51||!xo((function(){var t=[];return t[No]=!1,t.concat()[0]!==t})),zo=Io("concat"),Ho=function(t){if(!Co(t))return!1;var n=t[No];return void 0!==n?!!n:Ao(t)};Eo({target:"Array",proto:!0,arity:1,forced:!_o||!zo},{concat:function(t){var n,r,e,o,i,u=ko(this),c=Do(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(Ho(i=-1===n?u:arguments[n]))for(o=Fo(i),Mo(a+o),r=0;r<o;r++,a++)r in i&&Ro(c,a,i[r]);else Mo(a+1),Ro(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["et-EE"]=r.default.fn.bootstrapTable.locales.et={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Päring käib, palun oota"},formatRecordsPerPage:function(t){return"".concat(t," rida lehe kohta")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Näitan tulemusi ".concat(t," kuni ").concat(n," - kokku ").concat(r," tulemust (filtered from ").concat(e," total rows)"):"Näitan tulemusi ".concat(t," kuni ").concat(n," - kokku ").concat(r," tulemust")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Otsi"},formatNoMatches:function(){return"Päringu tingimustele ei vastanud ühtegi tulemust"},formatPaginationSwitch:function(){return"Näita/Peida lehtedeks jagamine"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Värskenda"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Veerud"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Kõik"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["et-EE"])}));
