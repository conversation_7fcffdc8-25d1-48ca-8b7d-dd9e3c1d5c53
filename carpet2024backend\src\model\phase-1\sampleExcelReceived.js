const mongoose = require('mongoose');

const sampleExcelReceivedSchema = new mongoose.Schema({
  baleNo: {
    type: Number,
    required: true,
  },
  pieceNo: {
    type: Number,
    required: true,
  },
  quality: {
    type: String,
    required: true,
  },
  design: {
    type: String,
    required: true,
  },
  colour: {
    type: String,
    required: true,
  },
  width: {
    type: Number,
    required: true,
  },
  length: {
    type: Number,
    required: true,
  },
  qty: {
    type: Number,
    required: true,
  },
  sqMts: {
    type: Number,
    required: true,
  },
  invoiceNo: {
    type: Number,
    required: true,
  },
  supplierName: {
    type: Number,
    required: true,
  },
  excelFile: {
    type: Number,
    required: true,
  },
});
 
const SampleExcelReceived = mongoose.model('SampleExcelReceived', sampleExcelReceivedSchema);

module.exports = SampleExcelReceived;
