<div class="container mt-4">
  <section>
    <fieldset style="min-height: 200px">
      <legend>
        <b>{{ _isUpdatedChallan ? "Update Challan" : "Create Challan" }}</b>
      </legend>

      <form [formGroup]="frmChallan" (ngSubmit)="createChallan()">
        <div class="row">
          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Group</mat-label>
              <mat-select
                (valueChange)="selectedGroup($event)"
                formControlName="group"
              >
                @for (group of groupList; track group) {
                <mat-option [value]="group">{{ group }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3" *ngIf="!_isSelectedGroup">
            <mat-form-field appearance="outline">
              <mat-label>Wholesaler</mat-label>
              <mat-select formControlName="wholeseller">
                @for (wholesaler of wholesalerList; track wholesaler) {
                <mat-option [value]="wholesaler.wholesalerName">{{
                  wholesaler.wholesalerName
                }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-md-3" *ngIf="!_isSelectedGroup">
            <mat-form-field appearance="outline">
              <mat-label>Retailer Outlet</mat-label>
              <input
                matInput
                placeholder="Retailer Outlet"
                formControlName="retailerOutlet"
              />
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3" *ngIf="_isSelectedGroup">
            <mat-form-field appearance="outline">
              <mat-label>Retailer</mat-label>
              <mat-select
                formControlName="RetailerName"
                (valueChange)="selectedRetailer($event)"
              >
                @for (retailser of retailerName; track retailser) {
                <mat-option value="{{ retailser.name }}">{{
                  retailser.name
                }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3">
            <mat-form-field class="full-width" appearance="outline">
              <mat-label>Choose a date</mat-label>
              <input
                matInput
                [matDatepicker]="picker"
                formControlName="chooseAdate"
              />
              <mat-datepicker-toggle
                matIconSuffix
                [for]="picker"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="mb-2 col-md-3">
            <!-- <mat-form-field appearance="outline">
        <mat-label>Challan No.</mat-label>
        <mat-select [(value)]="newChallanNo">

          @for (challanNo of challanList; track challanNo) {
          <mat-option [value]="challanNo">{{challanNo}}</mat-option>
          }
        </mat-select>
      </mat-form-field> -->
            <mat-form-field appearance="outline">
              <mat-label>Challan No</mat-label>
              <input
                matInput
                placeholder="Placeholder"
                [value]="a"
                formControlName="challanNo"
              />
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Sale Retailer</mat-label>
              <mat-select
                formControlName="saleRetailer"
                (valueChange)="getChallanType($event)"
              >
                @for (item of itemTypes; track item) {
                <mat-option [value]="item">{{ item }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3">

            <mat-form-field class="example-full-width" appearance="outline">
              <mat-label>Barcode No. ( GER. )</mat-label>
              <input
                type="text"
                placeholder="Barcode No. ( GER. )"
                aria-label="Number"
                matInput
                [formControl]="myControl"
                [matAutocomplete]="auto"
              />
              <mat-autocomplete
                autoActiveFirstOption
                #auto="matAutocomplete"
                (optionSelected)="onOptionSelected($event)"
              >
                <mat-option
                  *ngFor="let option of filteredOptions | async"
                  [value]="option"
                >
                  {{ option }}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
          <div class="mt-2 col-md-1" *ngIf="!_isUpdatedChallan">
            <button mat-flat-button color="primary">Save</button>
          </div>
          <div class="mt-2 col-md-1" *ngIf="_isUpdatedChallan">
            <button
              type="button"
              (click)="updateChallan()"
              mat-flat-button
              color="primary"
            >
              Update
            </button>
          </div>
          <!-- &nbsp;&nbsp;&nbsp; -->
          <!-- <div class="mt-2 col-md-2">
            <button
              type="button"
              (click)="reset()"
              mat-flat-button
              color="primary"
            >
             Add New
            </button>
          </div> -->
        </div>
      </form>
    </fieldset>

    <div class="container mt-5">
      <section>
        <fieldset style="min-height: 200px">
          <legend><b>List</b></legend>

          <div class="row">
            <div class="mb-2 col-3">
              <mat-form-field appearance="outline">
                <mat-label>Search</mat-label>
                <input
                  matInput
                  (keyup)="applyFilter($event)"
                  placeholder="Ex. Jack"
                  #input
                />
              </mat-form-field>


            </div>
            <div class="col-md-12">
              <div class="mat-elevation-z8">
                <table mat-table [dataSource]="dataSource" matSort style="width: max-content;">
                  <!-- Sr. No. Column -->
                  <ng-container matColumnDef="exid">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Sr. No.
                    </th>
                    <td mat-cell *matCellDef="let row">{{ row.index }}</td>
                  </ng-container>

                  <!-- Group Column -->
                  <ng-container matColumnDef="carpetDetails">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Carpet Details
                    </th>

                    <td mat-cell *matCellDef="let row">
                      {{ row.QualityDesign }} <br />
                      {{ row.QCode }}, {{ row.colourCode }}{{ row.Color }}
                      {{ row.CCode }}, <br />
                      {{ row.Size }} cm
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="carpetno">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Carpet No.
                    </th>
                    <td mat-cell *matCellDef="let row">
                      {{ row.GerCarpetNo }}
                    </td>
                  </ng-container>

                  <!-- Customer Name Column -->
                  <ng-container matColumnDef="area">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Area
                    </th>
                    <td mat-cell *matCellDef="let row">
                      {{ row.Area | number : "1.2-2" }} QM
                    </td>
                  </ng-container>

                  <!--Challan No Column -->
                  <ng-container matColumnDef="evkPrice">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Evk Price
                    </th>
                    <td mat-cell *matCellDef="let row">
                      <div *ngIf="!isEditAmount || editRowIdx !== row.index">
                        {{ row.EvKPrice | number : "1.2-2" }}
                      </div>
                      <div *ngIf="isEditAmount && editRowIdx === row.index">
                        <input
                          class="inputEvkPrice formControl"
                          style="width: 74px"
                          #myEvkPriceField
                          type="text"
                          [value]="row.EvKPrice"
                        />
                      </div>

                    </td>
                  </ng-container>

                  <ng-container matColumnDef="size">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Size
                    </th>
                    <td mat-cell *matCellDef="let row">
                      <div *ngIf="!isEditAmount || editRowIdx !== row.index">
                        {{ row.Size }}
                      </div>
                      <div *ngIf="isEditAmount && editRowIdx === row.index">
                        <input
                          class="inputSize formControl"
                          style="width: 74px"
                          #mySizeField
                          type="text"
                          [value]="row.Size"
                        />
                      </div>
                    </td>
                  </ng-container>

                  <!-- Colour Picker Column -->
                  <ng-container matColumnDef="amount">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Amount
                    </th>
                    <td mat-cell *matCellDef="let row">
                      <div *ngIf="!isEditAmount || editRowIdx !== row.index">
                        {{ row.Amount | number : "1.2-2" }}
                      </div>
                      <div *ngIf="isEditAmount && editRowIdx === row.index">
                        <input
                          class="inputAmount formControl"
                          #myTextField
                          type="text"
                          [value]="row.Amount"
                        />
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="action">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>
                      Action
                    </th>
                    <td mat-cell *matCellDef="let row">
                      <div *ngIf="!isEditAmount || editRowIdx !== row.index">
                        <a (click)="editAmount(row.index)"
                          ><i
                            class="fa fa-pencil-square-o fa-lg fa-edit"
                            title="Edit Amount "
                            aria-hidden="true"
                          ></i
                        ></a>
                        &nbsp;
                        <a (click)="deleteItem(row.exid)"
                          ><i
                            title="Remove Item"
                            class="fa fa-times"
                            aria-hidden="true"
                          ></i
                        ></a>
                      </div>

                      <div *ngIf="isEditAmount && editRowIdx === row.index">
                        <a (click)="updateAmount(row.exid)"
                          ><i
                            title="Update Amount"
                            class="fa fa-lg fa-check-square-o"
                            aria-hidden="true"
                          ></i
                        ></a>
                        &nbsp;
                        <a (click)="cancel()"
                          ><i
                            title="Cancel"
                            class="fa fa-lg fa-times-circle-o"
                            aria-hidden="true"
                          ></i
                        ></a>
                      </div>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                  <tr
                    mat-row
                    *matRowDef="let row; columns: displayedColumns"
                  ></tr>

                  <!-- Row shown when there is no matching data. -->
                  <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" colspan="4">
                      No data matching the filter "{{ input.value }}"
                    </td>
                  </tr>
                </table>

                <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>
              </div>
            </div>
          </div>
        </fieldset>
        <fieldset
          style="min-height: 90px; width: 300px; float: right"
          class="mt-2"
        >
          <table style="width: 100%">
            <tr>
              <td>Total</td>
              <td class="text-end">
                {{ financialData.amount | number : "1.2-2" }}
              </td>
            </tr>
            <tr>
              <td>Profit</td>
              <td class="text-end">
                {{ financialData.profit | number : "1.2-2" }}
              </td>
            </tr>
            <tr>
              <td>Discount</td>
              <td class="text-end">
                <input
                  style="width: 63px"
                  class="inputDic"
                  placeholder="0.00"
                  #setDiscountAmt
                  type="text"
                  (blur)="setDiscount()"
                />
              </td>
            </tr>
            <tr>
              <td>Gst 19%</td>
              <td class="text-end">
                {{ financialData.gst | number : "1.2-2" }}
              </td>
            </tr>



            <tr>
              <td>Gross</td>
              <td class="text-end">
                {{ financialData.grossAmount | number : "1.2-2" }}
              </td>
            </tr>
          </table>
        </fieldset>
      </section>
    </div>
  </section>
</div>
