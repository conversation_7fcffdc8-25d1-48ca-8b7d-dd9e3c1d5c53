.table tr th td {border: 1px  solid #000!important; padding: 5px;}


body{ color: #818078;}

.hr-text {
    line-height: 1em;
    position: relative;
    outline: 0;
    border: 0;
    color: black;
    text-align: center;
    height: 1.5em;
    opacity: .5;
    &:before {
      content: '';
  
      background: linear-gradient(to right, transparent, #818078, transparent);
      position: absolute;
      left: 0;
      top: 50%;
      width: 100%;
      height: 1px;
    }
    &:after {
      content: attr(data-content);
      position: relative;
      display: inline-block;
      color: black;
  
      padding: 0 .5em;
      line-height: 1.5em;
      
      color: #818078;
      background-color: #fcfcfa;
    }
  }


  .example-full-width {
    width: 100%;
  }

  table, th, td {
    border: 1px solid black;
    
  }
 
  