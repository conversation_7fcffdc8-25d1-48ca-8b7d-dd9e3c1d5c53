// routes/impoterRoutes.js
const express = require('express');
const ImpoterController = require('../../controller/impoter-controller');

const router = express.Router();

const impoterController = new ImpoterController();

router.post('/create-impoter', impoterController.createImpoter.bind(impoterController));
router.get('/getAll-impoter', impoterController.getAllImpoters.bind(impoterController));
router.get('/get-impoter/:id', impoterController.getImpoterById.bind(impoterController));
router.put('/update-addOnPrice/:importerId/:addOnPriceId', impoterController.updateAddOnPrice.bind(impoterController));
router.get('/get-addOnPriceHistory/:addOnPriceId', impoterController.getAddOnPriceHistory.bind(impoterController));
router.delete('/delete-impoter/:id', impoterController.deleteImpoter.bind(impoterController));
router.patch('/update-price-data/:id',impoterController.updatePrice.bind(impoterController));
module.exports = router;
