const sizeRepository = require('../repositories/size-repository');

class SizeService {
  async createSize(sizeData) {
    try {
      const size = await sizeRepository.createSize(sizeData);
      return size;
    } catch (error) {
      throw error;
    }
  }

  async getSizeById(sizeId) {
    try {
      const size = await sizeRepository.getSizeById(sizeId);
      return size;
    } catch (error) {
      throw error;
    }
  }

  async getAllSizes() {
    try {
      const sizes = await sizeRepository.getAllSizes();
      return sizes;
    } catch (error) {
      throw error;
    }
  }

  async updateSize(sizeId, sizeData) {
    try {
      const size = await sizeRepository.updateSize(sizeId, sizeData);
      return size;
    } catch (error) {
      throw error;
    }
  }

  async deleteSize(sizeId) {
    try {
      const size = await sizeRepository.deleteSize(sizeId);
      return size;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new SizeService();
