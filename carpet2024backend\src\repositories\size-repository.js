const Size = require('../model/phase-1/size');

class SizeRepository {
  async createSize(sizeData) {
    try {
      const size = await Size.create(sizeData);
      return size;
    } catch (error) {
      throw error;
    }
  }

  async getSizeById(sizeId) {
    try {
      const size = await Size.findById(sizeId);
      return size;
    } catch (error) {
      throw error;
    }
  }

  async getAllSizes() {
    try {
      const sizes = await Size.find();
      return sizes;
    } catch (error) {
      throw error;
    }
  }

  async updateSize(sizeId, sizeData) {
    try {
      const size = await Size.findByIdAndUpdate(sizeId, sizeData, { new: true });
      return size;
    } catch (error) {
      throw error;
    }
  }

  async deleteSize(sizeId) {
    try {
      const size = await Size.findByIdAndDelete(sizeId);
      return size;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new SizeRepository();
