import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ImporterService } from '../../../../services/importer.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { CustomeServiceService } from '../../../../services/custome-service.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import {ExportDataServiceService} from '../../../../services/export-data-service.service';
import { SweetalertService } from '../../../../services/sweetalert.service';
export interface PeriodicElement {
  index: number;
  challanNo: string;
  challanDate: string;
  challanCustomer: string;
  challanStatus: string;
  retailerOutlet: string;
  gerCarpetNo: string;
  qualityDesign: string;
  color: string;
  cCode: string;
  qCode: string;
  size: string;
  sCode: string;
  area: string;
  evkPrice: string;
  amount: string;
  invoiceNo: string;
  status: string;
  billNo: string;
  billDate: string;
  billCustomer: string;
}

const ELEMENT_DATA: PeriodicElement[] = [];
@Component({
  selector: 'app-bar-code-details',
  templateUrl: './bar-code-details.component.html',
  styleUrl: './bar-code-details.component.css',
})
export class BarCodeDetailsComponent implements OnInit {
  constructor(
    private _service: ImporterService,
    private customeService: CustomeServiceService,
    private fb: FormBuilder,
    private ngxLoader: NgxUiLoaderService,
    private  alert:SweetalertService,
    private excelService:ExportDataServiceService
  ) {}

  selectedOption: string = 'single';
  displayedColumns: string[] = [
    'index',
    'gerCarpetNo',
    'qualityDesign',
    'color',
    'cCode',
    'qCode',
    'size',
    'sCode',
    'area',
    'evkPrice',
    'amount',
    'challanNo',
    'challanDate',
    'challanCustomer',
    'challanStatus',
    'billNo',
    'billCustomer',
    'billDate',
  ];

  frmCrapetDetails!: FormGroup;

  dataSource = new MatTableDataSource(ELEMENT_DATA);
  ngOnInit(): void {
    this.frmCrapetDetails = this.fb.group({
      inputBcr: [],
      toDate: [],
      fromDate: [],
    });
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  selectOption() {
    console.log(this.selectedOption);
  }

  sendData() {


    let bcr = { carpetNo: '', toDate: '', fromDate: '' };

    console.log(this.frmCrapetDetails.value);
    let formData = this.frmCrapetDetails.value;
    if (this.selectedOption === 'single') {
      bcr.carpetNo = formData.inputBcr;
    } else {
      bcr.toDate =this.customeService.convertDate(formData.toDate);
      bcr.fromDate = this.customeService.convertDate(formData.fromDate);
    }


    this.ngxLoader.start();
    this._service.getBarCodeDetails(bcr).subscribe({
      next:(value:any) =>{

          console.log(value);
          if (value) {
            ELEMENT_DATA.length = 0;
            value.map((x: any, ind: number) => {
              ;
              ELEMENT_DATA.push({
                index: ind + 1,
                gerCarpetNo: x.gerCarpetNo,
                qualityDesign: x.qualityDesign,
                color: x.color,
                cCode: x.cCode,
                qCode: x.qCode,
                size: x.size,
                sCode: x.sCode,
                area: x.challanStatus!== 'return' ? x.area : -x.area,
                evkPrice:  x.challanStatus !== 'return' ? x.evkPrice : -x.evkPrice,
                amount: x.challanStatus !== 'return' ? x.amount : -x.amount,
                invoiceNo: x.invoiceNo,
                status: x.status,
                challanNo: x.challanNo || '',
                challanDate: x.challanDate
                  ? this.customeService.convertDate(x.challanDate)
                  : '',
                challanCustomer: x.challanCustomer || '',
                challanStatus: x.challanStatus || '',
                retailerOutlet: x.retailerOutlet || '',

                billNo: x.billNo || '',
                billDate: x.billDate
                  ? this.customeService.convertDate(x.billDate)
                  : '',
                billCustomer: x.billCustomer || '',
              });
            });
          }

          this.dataSource = new MatTableDataSource(ELEMENT_DATA);
          this.ngAfterViewInit();
          this.ngxLoader.stop();
          return;

      },error:(err) =>{

this.alert.error('warning', err.error.message);
this.ngxLoader.stop();
      },
    });
  }


  exportAsXLSX():void {
    this.excelService.exportAsExcelFile(this.dataSource.filteredData, 'export-to-excel');
  }
}
