/**
 * lucide v0.259.0 - ISC
 */

import replaceElement from './replaceElement.js';
import * as index from './icons/index.js';
export { index as icons };
export { default as createElement } from './createElement.js';
export { default as Accessibility } from './icons/accessibility.js';
export { default as ActivitySquare } from './icons/activity-square.js';
export { default as Activity } from './icons/activity.js';
export { default as AirVent } from './icons/air-vent.js';
export { default as Airplay } from './icons/airplay.js';
export { default as AlarmCheck } from './icons/alarm-check.js';
export { default as AlarmClockOff } from './icons/alarm-clock-off.js';
export { default as AlarmClock } from './icons/alarm-clock.js';
export { default as AlarmMinus } from './icons/alarm-minus.js';
export { default as AlarmPlus } from './icons/alarm-plus.js';
export { default as Album } from './icons/album.js';
export { default as AlertCircle } from './icons/alert-circle.js';
export { default as AlertOctagon } from './icons/alert-octagon.js';
export { default as AlertTriangle } from './icons/alert-triangle.js';
export { default as AlignCenterHorizontal } from './icons/align-center-horizontal.js';
export { default as AlignCenterVertical } from './icons/align-center-vertical.js';
export { default as AlignCenter } from './icons/align-center.js';
export { default as AlignEndHorizontal } from './icons/align-end-horizontal.js';
export { default as AlignEndVertical } from './icons/align-end-vertical.js';
export { default as AlignHorizontalDistributeCenter } from './icons/align-horizontal-distribute-center.js';
export { default as AlignHorizontalDistributeEnd } from './icons/align-horizontal-distribute-end.js';
export { default as AlignHorizontalDistributeStart } from './icons/align-horizontal-distribute-start.js';
export { default as AlignHorizontalJustifyCenter } from './icons/align-horizontal-justify-center.js';
export { default as AlignHorizontalJustifyEnd } from './icons/align-horizontal-justify-end.js';
export { default as AlignHorizontalJustifyStart } from './icons/align-horizontal-justify-start.js';
export { default as AlignHorizontalSpaceAround } from './icons/align-horizontal-space-around.js';
export { default as AlignHorizontalSpaceBetween } from './icons/align-horizontal-space-between.js';
export { default as AlignJustify } from './icons/align-justify.js';
export { default as AlignLeft } from './icons/align-left.js';
export { default as AlignRight } from './icons/align-right.js';
export { default as AlignStartHorizontal } from './icons/align-start-horizontal.js';
export { default as AlignStartVertical } from './icons/align-start-vertical.js';
export { default as AlignVerticalDistributeCenter } from './icons/align-vertical-distribute-center.js';
export { default as AlignVerticalDistributeEnd } from './icons/align-vertical-distribute-end.js';
export { default as AlignVerticalDistributeStart } from './icons/align-vertical-distribute-start.js';
export { default as AlignVerticalJustifyCenter } from './icons/align-vertical-justify-center.js';
export { default as AlignVerticalJustifyEnd } from './icons/align-vertical-justify-end.js';
export { default as AlignVerticalJustifyStart } from './icons/align-vertical-justify-start.js';
export { default as AlignVerticalSpaceAround } from './icons/align-vertical-space-around.js';
export { default as AlignVerticalSpaceBetween } from './icons/align-vertical-space-between.js';
export { default as Ampersand } from './icons/ampersand.js';
export { default as Ampersands } from './icons/ampersands.js';
export { default as Anchor } from './icons/anchor.js';
export { default as Angry } from './icons/angry.js';
export { default as Annoyed } from './icons/annoyed.js';
export { default as Aperture } from './icons/aperture.js';
export { default as AppWindow } from './icons/app-window.js';
export { default as Apple } from './icons/apple.js';
export { default as ArchiveRestore } from './icons/archive-restore.js';
export { default as Archive } from './icons/archive.js';
export { default as AreaChart } from './icons/area-chart.js';
export { default as Armchair } from './icons/armchair.js';
export { default as ArrowBigDownDash } from './icons/arrow-big-down-dash.js';
export { default as ArrowBigDown } from './icons/arrow-big-down.js';
export { default as ArrowBigLeftDash } from './icons/arrow-big-left-dash.js';
export { default as ArrowBigLeft } from './icons/arrow-big-left.js';
export { default as ArrowBigRightDash } from './icons/arrow-big-right-dash.js';
export { default as ArrowBigRight } from './icons/arrow-big-right.js';
export { default as ArrowBigUpDash } from './icons/arrow-big-up-dash.js';
export { default as ArrowBigUp } from './icons/arrow-big-up.js';
export { default as ArrowDown01 } from './icons/arrow-down-0-1.js';
export { default as ArrowDown10 } from './icons/arrow-down-1-0.js';
export { default as ArrowDownAZ } from './icons/arrow-down-a-z.js';
export { default as ArrowDownCircle } from './icons/arrow-down-circle.js';
export { default as ArrowDownFromLine } from './icons/arrow-down-from-line.js';
export { default as ArrowDownLeftFromCircle } from './icons/arrow-down-left-from-circle.js';
export { default as ArrowDownLeftSquare } from './icons/arrow-down-left-square.js';
export { default as ArrowDownLeft } from './icons/arrow-down-left.js';
export { default as ArrowDownNarrowWide } from './icons/arrow-down-narrow-wide.js';
export { default as ArrowDownRightFromCircle } from './icons/arrow-down-right-from-circle.js';
export { default as ArrowDownRightSquare } from './icons/arrow-down-right-square.js';
export { default as ArrowDownRight } from './icons/arrow-down-right.js';
export { default as ArrowDownSquare } from './icons/arrow-down-square.js';
export { default as ArrowDownToDot } from './icons/arrow-down-to-dot.js';
export { default as ArrowDownToLine } from './icons/arrow-down-to-line.js';
export { default as ArrowDownUp } from './icons/arrow-down-up.js';
export { default as ArrowDownWideNarrow } from './icons/arrow-down-wide-narrow.js';
export { default as ArrowDownZA } from './icons/arrow-down-z-a.js';
export { default as ArrowDown } from './icons/arrow-down.js';
export { default as ArrowLeftCircle } from './icons/arrow-left-circle.js';
export { default as ArrowLeftFromLine } from './icons/arrow-left-from-line.js';
export { default as ArrowLeftRight } from './icons/arrow-left-right.js';
export { default as ArrowLeftSquare } from './icons/arrow-left-square.js';
export { default as ArrowLeftToLine } from './icons/arrow-left-to-line.js';
export { default as ArrowLeft } from './icons/arrow-left.js';
export { default as ArrowRightCircle } from './icons/arrow-right-circle.js';
export { default as ArrowRightFromLine } from './icons/arrow-right-from-line.js';
export { default as ArrowRightLeft } from './icons/arrow-right-left.js';
export { default as ArrowRightSquare } from './icons/arrow-right-square.js';
export { default as ArrowRightToLine } from './icons/arrow-right-to-line.js';
export { default as ArrowRight } from './icons/arrow-right.js';
export { default as ArrowUp01 } from './icons/arrow-up-0-1.js';
export { default as ArrowUp10 } from './icons/arrow-up-1-0.js';
export { default as ArrowUpAZ } from './icons/arrow-up-a-z.js';
export { default as ArrowUpCircle } from './icons/arrow-up-circle.js';
export { default as ArrowUpDown } from './icons/arrow-up-down.js';
export { default as ArrowUpFromDot } from './icons/arrow-up-from-dot.js';
export { default as ArrowUpFromLine } from './icons/arrow-up-from-line.js';
export { default as ArrowUpLeftFromCircle } from './icons/arrow-up-left-from-circle.js';
export { default as ArrowUpLeftSquare } from './icons/arrow-up-left-square.js';
export { default as ArrowUpLeft } from './icons/arrow-up-left.js';
export { default as ArrowUpNarrowWide } from './icons/arrow-up-narrow-wide.js';
export { default as ArrowUpRightFromCircle } from './icons/arrow-up-right-from-circle.js';
export { default as ArrowUpRightSquare } from './icons/arrow-up-right-square.js';
export { default as ArrowUpRight } from './icons/arrow-up-right.js';
export { default as ArrowUpSquare } from './icons/arrow-up-square.js';
export { default as ArrowUpToLine } from './icons/arrow-up-to-line.js';
export { default as ArrowUpWideNarrow } from './icons/arrow-up-wide-narrow.js';
export { default as ArrowUpZA } from './icons/arrow-up-z-a.js';
export { default as ArrowUp } from './icons/arrow-up.js';
export { default as ArrowsUpFromLine } from './icons/arrows-up-from-line.js';
export { default as Asterisk } from './icons/asterisk.js';
export { default as AtSign } from './icons/at-sign.js';
export { default as Atom } from './icons/atom.js';
export { default as Award } from './icons/award.js';
export { default as Axe } from './icons/axe.js';
export { default as Axis3d } from './icons/axis-3d.js';
export { default as Baby } from './icons/baby.js';
export { default as Backpack } from './icons/backpack.js';
export { default as BadgeAlert } from './icons/badge-alert.js';
export { default as BadgeCheck } from './icons/badge-check.js';
export { default as BadgeDollarSign } from './icons/badge-dollar-sign.js';
export { default as BadgeHelp } from './icons/badge-help.js';
export { default as BadgeInfo } from './icons/badge-info.js';
export { default as BadgeMinus } from './icons/badge-minus.js';
export { default as BadgePercent } from './icons/badge-percent.js';
export { default as BadgePlus } from './icons/badge-plus.js';
export { default as BadgeX } from './icons/badge-x.js';
export { default as Badge } from './icons/badge.js';
export { default as BaggageClaim } from './icons/baggage-claim.js';
export { default as Ban } from './icons/ban.js';
export { default as Banana } from './icons/banana.js';
export { default as Banknote } from './icons/banknote.js';
export { default as BarChart2 } from './icons/bar-chart-2.js';
export { default as BarChart3 } from './icons/bar-chart-3.js';
export { default as BarChart4 } from './icons/bar-chart-4.js';
export { default as BarChartBig } from './icons/bar-chart-big.js';
export { default as BarChartHorizontalBig } from './icons/bar-chart-horizontal-big.js';
export { default as BarChartHorizontal } from './icons/bar-chart-horizontal.js';
export { default as BarChart } from './icons/bar-chart.js';
export { default as Baseline } from './icons/baseline.js';
export { default as Bath } from './icons/bath.js';
export { default as BatteryCharging } from './icons/battery-charging.js';
export { default as BatteryFull } from './icons/battery-full.js';
export { default as BatteryLow } from './icons/battery-low.js';
export { default as BatteryMedium } from './icons/battery-medium.js';
export { default as BatteryWarning } from './icons/battery-warning.js';
export { default as Battery } from './icons/battery.js';
export { default as Beaker } from './icons/beaker.js';
export { default as BeanOff } from './icons/bean-off.js';
export { default as Bean } from './icons/bean.js';
export { default as BedDouble } from './icons/bed-double.js';
export { default as BedSingle } from './icons/bed-single.js';
export { default as Bed } from './icons/bed.js';
export { default as Beef } from './icons/beef.js';
export { default as Beer } from './icons/beer.js';
export { default as BellDot } from './icons/bell-dot.js';
export { default as BellMinus } from './icons/bell-minus.js';
export { default as BellOff } from './icons/bell-off.js';
export { default as BellPlus } from './icons/bell-plus.js';
export { default as BellRing } from './icons/bell-ring.js';
export { default as Bell } from './icons/bell.js';
export { default as Bike } from './icons/bike.js';
export { default as Binary } from './icons/binary.js';
export { default as Biohazard } from './icons/biohazard.js';
export { default as Bird } from './icons/bird.js';
export { default as Bitcoin } from './icons/bitcoin.js';
export { default as Blinds } from './icons/blinds.js';
export { default as BluetoothConnected } from './icons/bluetooth-connected.js';
export { default as BluetoothOff } from './icons/bluetooth-off.js';
export { default as BluetoothSearching } from './icons/bluetooth-searching.js';
export { default as Bluetooth } from './icons/bluetooth.js';
export { default as Bold } from './icons/bold.js';
export { default as Bomb } from './icons/bomb.js';
export { default as Bone } from './icons/bone.js';
export { default as BookCopy } from './icons/book-copy.js';
export { default as BookDown } from './icons/book-down.js';
export { default as BookKey } from './icons/book-key.js';
export { default as BookLock } from './icons/book-lock.js';
export { default as BookMarked } from './icons/book-marked.js';
export { default as BookMinus } from './icons/book-minus.js';
export { default as BookOpenCheck } from './icons/book-open-check.js';
export { default as BookOpen } from './icons/book-open.js';
export { default as BookPlus } from './icons/book-plus.js';
export { default as BookTemplate } from './icons/book-template.js';
export { default as BookUp2 } from './icons/book-up-2.js';
export { default as BookUp } from './icons/book-up.js';
export { default as BookX } from './icons/book-x.js';
export { default as Book } from './icons/book.js';
export { default as BookmarkMinus } from './icons/bookmark-minus.js';
export { default as BookmarkPlus } from './icons/bookmark-plus.js';
export { default as Bookmark } from './icons/bookmark.js';
export { default as Bot } from './icons/bot.js';
export { default as BoxSelect } from './icons/box-select.js';
export { default as Box } from './icons/box.js';
export { default as Boxes } from './icons/boxes.js';
export { default as Braces } from './icons/braces.js';
export { default as Brackets } from './icons/brackets.js';
export { default as BrainCircuit } from './icons/brain-circuit.js';
export { default as BrainCog } from './icons/brain-cog.js';
export { default as Brain } from './icons/brain.js';
export { default as Briefcase } from './icons/briefcase.js';
export { default as Brush } from './icons/brush.js';
export { default as Bug } from './icons/bug.js';
export { default as Building2 } from './icons/building-2.js';
export { default as Building } from './icons/building.js';
export { default as Bus } from './icons/bus.js';
export { default as CakeSlice } from './icons/cake-slice.js';
export { default as Cake } from './icons/cake.js';
export { default as Calculator } from './icons/calculator.js';
export { default as CalendarCheck2 } from './icons/calendar-check-2.js';
export { default as CalendarCheck } from './icons/calendar-check.js';
export { default as CalendarClock } from './icons/calendar-clock.js';
export { default as CalendarDays } from './icons/calendar-days.js';
export { default as CalendarHeart } from './icons/calendar-heart.js';
export { default as CalendarMinus } from './icons/calendar-minus.js';
export { default as CalendarOff } from './icons/calendar-off.js';
export { default as CalendarPlus } from './icons/calendar-plus.js';
export { default as CalendarRange } from './icons/calendar-range.js';
export { default as CalendarSearch } from './icons/calendar-search.js';
export { default as CalendarX2 } from './icons/calendar-x-2.js';
export { default as CalendarX } from './icons/calendar-x.js';
export { default as Calendar } from './icons/calendar.js';
export { default as CameraOff } from './icons/camera-off.js';
export { default as Camera } from './icons/camera.js';
export { default as CandlestickChart } from './icons/candlestick-chart.js';
export { default as CandyCane } from './icons/candy-cane.js';
export { default as CandyOff } from './icons/candy-off.js';
export { default as Candy } from './icons/candy.js';
export { default as Car } from './icons/car.js';
export { default as Carrot } from './icons/carrot.js';
export { default as CaseLower } from './icons/case-lower.js';
export { default as CaseSensitive } from './icons/case-sensitive.js';
export { default as CaseUpper } from './icons/case-upper.js';
export { default as CassetteTape } from './icons/cassette-tape.js';
export { default as Cast } from './icons/cast.js';
export { default as Castle } from './icons/castle.js';
export { default as Cat } from './icons/cat.js';
export { default as CheckCheck } from './icons/check-check.js';
export { default as CheckCircle2 } from './icons/check-circle-2.js';
export { default as CheckCircle } from './icons/check-circle.js';
export { default as CheckSquare } from './icons/check-square.js';
export { default as Check } from './icons/check.js';
export { default as ChefHat } from './icons/chef-hat.js';
export { default as Cherry } from './icons/cherry.js';
export { default as ChevronDownCircle } from './icons/chevron-down-circle.js';
export { default as ChevronDownSquare } from './icons/chevron-down-square.js';
export { default as ChevronDown } from './icons/chevron-down.js';
export { default as ChevronFirst } from './icons/chevron-first.js';
export { default as ChevronLast } from './icons/chevron-last.js';
export { default as ChevronLeftCircle } from './icons/chevron-left-circle.js';
export { default as ChevronLeftSquare } from './icons/chevron-left-square.js';
export { default as ChevronLeft } from './icons/chevron-left.js';
export { default as ChevronRightCircle } from './icons/chevron-right-circle.js';
export { default as ChevronRightSquare } from './icons/chevron-right-square.js';
export { default as ChevronRight } from './icons/chevron-right.js';
export { default as ChevronUpCircle } from './icons/chevron-up-circle.js';
export { default as ChevronUpSquare } from './icons/chevron-up-square.js';
export { default as ChevronUp } from './icons/chevron-up.js';
export { default as ChevronsDownUp } from './icons/chevrons-down-up.js';
export { default as ChevronsDown } from './icons/chevrons-down.js';
export { default as ChevronsLeftRight } from './icons/chevrons-left-right.js';
export { default as ChevronsLeft } from './icons/chevrons-left.js';
export { default as ChevronsRightLeft } from './icons/chevrons-right-left.js';
export { default as ChevronsRight } from './icons/chevrons-right.js';
export { default as ChevronsUpDown } from './icons/chevrons-up-down.js';
export { default as ChevronsUp } from './icons/chevrons-up.js';
export { default as Chrome } from './icons/chrome.js';
export { default as Church } from './icons/church.js';
export { default as CigaretteOff } from './icons/cigarette-off.js';
export { default as Cigarette } from './icons/cigarette.js';
export { default as CircleDashed } from './icons/circle-dashed.js';
export { default as CircleDollarSign } from './icons/circle-dollar-sign.js';
export { default as CircleDotDashed } from './icons/circle-dot-dashed.js';
export { default as CircleDot } from './icons/circle-dot.js';
export { default as CircleEllipsis } from './icons/circle-ellipsis.js';
export { default as CircleEqual } from './icons/circle-equal.js';
export { default as CircleOff } from './icons/circle-off.js';
export { default as CircleSlash2 } from './icons/circle-slash-2.js';
export { default as CircleSlash } from './icons/circle-slash.js';
export { default as Circle } from './icons/circle.js';
export { default as CircuitBoard } from './icons/circuit-board.js';
export { default as Citrus } from './icons/citrus.js';
export { default as Clapperboard } from './icons/clapperboard.js';
export { default as ClipboardCheck } from './icons/clipboard-check.js';
export { default as ClipboardCopy } from './icons/clipboard-copy.js';
export { default as ClipboardEdit } from './icons/clipboard-edit.js';
export { default as ClipboardList } from './icons/clipboard-list.js';
export { default as ClipboardPaste } from './icons/clipboard-paste.js';
export { default as ClipboardSignature } from './icons/clipboard-signature.js';
export { default as ClipboardType } from './icons/clipboard-type.js';
export { default as ClipboardX } from './icons/clipboard-x.js';
export { default as Clipboard } from './icons/clipboard.js';
export { default as Clock1 } from './icons/clock-1.js';
export { default as Clock10 } from './icons/clock-10.js';
export { default as Clock11 } from './icons/clock-11.js';
export { default as Clock12 } from './icons/clock-12.js';
export { default as Clock2 } from './icons/clock-2.js';
export { default as Clock3 } from './icons/clock-3.js';
export { default as Clock4 } from './icons/clock-4.js';
export { default as Clock5 } from './icons/clock-5.js';
export { default as Clock6 } from './icons/clock-6.js';
export { default as Clock7 } from './icons/clock-7.js';
export { default as Clock8 } from './icons/clock-8.js';
export { default as Clock9 } from './icons/clock-9.js';
export { default as Clock } from './icons/clock.js';
export { default as CloudCog } from './icons/cloud-cog.js';
export { default as CloudDrizzle } from './icons/cloud-drizzle.js';
export { default as CloudFog } from './icons/cloud-fog.js';
export { default as CloudHail } from './icons/cloud-hail.js';
export { default as CloudLightning } from './icons/cloud-lightning.js';
export { default as CloudMoonRain } from './icons/cloud-moon-rain.js';
export { default as CloudMoon } from './icons/cloud-moon.js';
export { default as CloudOff } from './icons/cloud-off.js';
export { default as CloudRainWind } from './icons/cloud-rain-wind.js';
export { default as CloudRain } from './icons/cloud-rain.js';
export { default as CloudSnow } from './icons/cloud-snow.js';
export { default as CloudSunRain } from './icons/cloud-sun-rain.js';
export { default as CloudSun } from './icons/cloud-sun.js';
export { default as Cloud } from './icons/cloud.js';
export { default as Cloudy } from './icons/cloudy.js';
export { default as Clover } from './icons/clover.js';
export { default as Club } from './icons/club.js';
export { default as Code2 } from './icons/code-2.js';
export { default as Code } from './icons/code.js';
export { default as Codepen } from './icons/codepen.js';
export { default as Codesandbox } from './icons/codesandbox.js';
export { default as Coffee } from './icons/coffee.js';
export { default as Cog } from './icons/cog.js';
export { default as Coins } from './icons/coins.js';
export { default as Columns } from './icons/columns.js';
export { default as Combine } from './icons/combine.js';
export { default as Command } from './icons/command.js';
export { default as Compass } from './icons/compass.js';
export { default as Component } from './icons/component.js';
export { default as ConciergeBell } from './icons/concierge-bell.js';
export { default as Construction } from './icons/construction.js';
export { default as Contact2 } from './icons/contact-2.js';
export { default as Contact } from './icons/contact.js';
export { default as Container } from './icons/container.js';
export { default as Contrast } from './icons/contrast.js';
export { default as Cookie } from './icons/cookie.js';
export { default as CopyCheck } from './icons/copy-check.js';
export { default as CopyMinus } from './icons/copy-minus.js';
export { default as CopyPlus } from './icons/copy-plus.js';
export { default as CopySlash } from './icons/copy-slash.js';
export { default as CopyX } from './icons/copy-x.js';
export { default as Copy } from './icons/copy.js';
export { default as Copyleft } from './icons/copyleft.js';
export { default as Copyright } from './icons/copyright.js';
export { default as CornerDownLeft } from './icons/corner-down-left.js';
export { default as CornerDownRight } from './icons/corner-down-right.js';
export { default as CornerLeftDown } from './icons/corner-left-down.js';
export { default as CornerLeftUp } from './icons/corner-left-up.js';
export { default as CornerRightDown } from './icons/corner-right-down.js';
export { default as CornerRightUp } from './icons/corner-right-up.js';
export { default as CornerUpLeft } from './icons/corner-up-left.js';
export { default as CornerUpRight } from './icons/corner-up-right.js';
export { default as Cpu } from './icons/cpu.js';
export { default as CreativeCommons } from './icons/creative-commons.js';
export { default as CreditCard } from './icons/credit-card.js';
export { default as Croissant } from './icons/croissant.js';
export { default as Crop } from './icons/crop.js';
export { default as Cross } from './icons/cross.js';
export { default as Crosshair } from './icons/crosshair.js';
export { default as Crown } from './icons/crown.js';
export { default as CupSoda } from './icons/cup-soda.js';
export { default as Currency } from './icons/currency.js';
export { default as DatabaseBackup } from './icons/database-backup.js';
export { default as Database } from './icons/database.js';
export { default as Delete } from './icons/delete.js';
export { default as Dessert } from './icons/dessert.js';
export { default as Diamond } from './icons/diamond.js';
export { default as Dice1 } from './icons/dice-1.js';
export { default as Dice2 } from './icons/dice-2.js';
export { default as Dice3 } from './icons/dice-3.js';
export { default as Dice4 } from './icons/dice-4.js';
export { default as Dice5 } from './icons/dice-5.js';
export { default as Dice6 } from './icons/dice-6.js';
export { default as Dices } from './icons/dices.js';
export { default as Diff } from './icons/diff.js';
export { default as Disc2 } from './icons/disc-2.js';
export { default as Disc3 } from './icons/disc-3.js';
export { default as Disc } from './icons/disc.js';
export { default as DivideCircle } from './icons/divide-circle.js';
export { default as DivideSquare } from './icons/divide-square.js';
export { default as Divide } from './icons/divide.js';
export { default as DnaOff } from './icons/dna-off.js';
export { default as Dna } from './icons/dna.js';
export { default as Dog } from './icons/dog.js';
export { default as DollarSign } from './icons/dollar-sign.js';
export { default as Donut } from './icons/donut.js';
export { default as DoorClosed } from './icons/door-closed.js';
export { default as DoorOpen } from './icons/door-open.js';
export { default as Dot } from './icons/dot.js';
export { default as DownloadCloud } from './icons/download-cloud.js';
export { default as Download } from './icons/download.js';
export { default as Dribbble } from './icons/dribbble.js';
export { default as Droplet } from './icons/droplet.js';
export { default as Droplets } from './icons/droplets.js';
export { default as Drumstick } from './icons/drumstick.js';
export { default as Dumbbell } from './icons/dumbbell.js';
export { default as EarOff } from './icons/ear-off.js';
export { default as Ear } from './icons/ear.js';
export { default as Edit2 } from './icons/edit-2.js';
export { default as Edit3 } from './icons/edit-3.js';
export { default as Edit } from './icons/edit.js';
export { default as EggFried } from './icons/egg-fried.js';
export { default as EggOff } from './icons/egg-off.js';
export { default as Egg } from './icons/egg.js';
export { default as EqualNot } from './icons/equal-not.js';
export { default as Equal } from './icons/equal.js';
export { default as Eraser } from './icons/eraser.js';
export { default as Euro } from './icons/euro.js';
export { default as Expand } from './icons/expand.js';
export { default as ExternalLink } from './icons/external-link.js';
export { default as EyeOff } from './icons/eye-off.js';
export { default as Eye } from './icons/eye.js';
export { default as Facebook } from './icons/facebook.js';
export { default as Factory } from './icons/factory.js';
export { default as Fan } from './icons/fan.js';
export { default as FastForward } from './icons/fast-forward.js';
export { default as Feather } from './icons/feather.js';
export { default as FerrisWheel } from './icons/ferris-wheel.js';
export { default as Figma } from './icons/figma.js';
export { default as FileArchive } from './icons/file-archive.js';
export { default as FileAudio2 } from './icons/file-audio-2.js';
export { default as FileAudio } from './icons/file-audio.js';
export { default as FileAxis3d } from './icons/file-axis-3d.js';
export { default as FileBadge2 } from './icons/file-badge-2.js';
export { default as FileBadge } from './icons/file-badge.js';
export { default as FileBarChart2 } from './icons/file-bar-chart-2.js';
export { default as FileBarChart } from './icons/file-bar-chart.js';
export { default as FileBox } from './icons/file-box.js';
export { default as FileCheck2 } from './icons/file-check-2.js';
export { default as FileCheck } from './icons/file-check.js';
export { default as FileClock } from './icons/file-clock.js';
export { default as FileCode2 } from './icons/file-code-2.js';
export { default as FileCode } from './icons/file-code.js';
export { default as FileCog2 } from './icons/file-cog-2.js';
export { default as FileCog } from './icons/file-cog.js';
export { default as FileDiff } from './icons/file-diff.js';
export { default as FileDigit } from './icons/file-digit.js';
export { default as FileDown } from './icons/file-down.js';
export { default as FileEdit } from './icons/file-edit.js';
export { default as FileHeart } from './icons/file-heart.js';
export { default as FileImage } from './icons/file-image.js';
export { default as FileInput } from './icons/file-input.js';
export { default as FileJson2 } from './icons/file-json-2.js';
export { default as FileJson } from './icons/file-json.js';
export { default as FileKey2 } from './icons/file-key-2.js';
export { default as FileKey } from './icons/file-key.js';
export { default as FileLineChart } from './icons/file-line-chart.js';
export { default as FileLock2 } from './icons/file-lock-2.js';
export { default as FileLock } from './icons/file-lock.js';
export { default as FileMinus2 } from './icons/file-minus-2.js';
export { default as FileMinus } from './icons/file-minus.js';
export { default as FileOutput } from './icons/file-output.js';
export { default as FilePieChart } from './icons/file-pie-chart.js';
export { default as FilePlus2 } from './icons/file-plus-2.js';
export { default as FilePlus } from './icons/file-plus.js';
export { default as FileQuestion } from './icons/file-question.js';
export { default as FileScan } from './icons/file-scan.js';
export { default as FileSearch2 } from './icons/file-search-2.js';
export { default as FileSearch } from './icons/file-search.js';
export { default as FileSignature } from './icons/file-signature.js';
export { default as FileSpreadsheet } from './icons/file-spreadsheet.js';
export { default as FileStack } from './icons/file-stack.js';
export { default as FileSymlink } from './icons/file-symlink.js';
export { default as FileTerminal } from './icons/file-terminal.js';
export { default as FileText } from './icons/file-text.js';
export { default as FileType2 } from './icons/file-type-2.js';
export { default as FileType } from './icons/file-type.js';
export { default as FileUp } from './icons/file-up.js';
export { default as FileVideo2 } from './icons/file-video-2.js';
export { default as FileVideo } from './icons/file-video.js';
export { default as FileVolume2 } from './icons/file-volume-2.js';
export { default as FileVolume } from './icons/file-volume.js';
export { default as FileWarning } from './icons/file-warning.js';
export { default as FileX2 } from './icons/file-x-2.js';
export { default as FileX } from './icons/file-x.js';
export { default as File } from './icons/file.js';
export { default as Files } from './icons/files.js';
export { default as Film } from './icons/film.js';
export { default as FilterX } from './icons/filter-x.js';
export { default as Filter } from './icons/filter.js';
export { default as Fingerprint } from './icons/fingerprint.js';
export { default as FishOff } from './icons/fish-off.js';
export { default as Fish } from './icons/fish.js';
export { default as FlagOff } from './icons/flag-off.js';
export { default as FlagTriangleLeft } from './icons/flag-triangle-left.js';
export { default as FlagTriangleRight } from './icons/flag-triangle-right.js';
export { default as Flag } from './icons/flag.js';
export { default as Flame } from './icons/flame.js';
export { default as FlashlightOff } from './icons/flashlight-off.js';
export { default as Flashlight } from './icons/flashlight.js';
export { default as FlaskConicalOff } from './icons/flask-conical-off.js';
export { default as FlaskConical } from './icons/flask-conical.js';
export { default as FlaskRound } from './icons/flask-round.js';
export { default as FlipHorizontal2 } from './icons/flip-horizontal-2.js';
export { default as FlipHorizontal } from './icons/flip-horizontal.js';
export { default as FlipVertical2 } from './icons/flip-vertical-2.js';
export { default as FlipVertical } from './icons/flip-vertical.js';
export { default as Flower2 } from './icons/flower-2.js';
export { default as Flower } from './icons/flower.js';
export { default as Focus } from './icons/focus.js';
export { default as FoldHorizontal } from './icons/fold-horizontal.js';
export { default as FoldVertical } from './icons/fold-vertical.js';
export { default as FolderArchive } from './icons/folder-archive.js';
export { default as FolderCheck } from './icons/folder-check.js';
export { default as FolderClock } from './icons/folder-clock.js';
export { default as FolderClosed } from './icons/folder-closed.js';
export { default as FolderCog2 } from './icons/folder-cog-2.js';
export { default as FolderCog } from './icons/folder-cog.js';
export { default as FolderDot } from './icons/folder-dot.js';
export { default as FolderDown } from './icons/folder-down.js';
export { default as FolderEdit } from './icons/folder-edit.js';
export { default as FolderGit2 } from './icons/folder-git-2.js';
export { default as FolderGit } from './icons/folder-git.js';
export { default as FolderHeart } from './icons/folder-heart.js';
export { default as FolderInput } from './icons/folder-input.js';
export { default as FolderKanban } from './icons/folder-kanban.js';
export { default as FolderKey } from './icons/folder-key.js';
export { default as FolderLock } from './icons/folder-lock.js';
export { default as FolderMinus } from './icons/folder-minus.js';
export { default as FolderOpenDot } from './icons/folder-open-dot.js';
export { default as FolderOpen } from './icons/folder-open.js';
export { default as FolderOutput } from './icons/folder-output.js';
export { default as FolderPlus } from './icons/folder-plus.js';
export { default as FolderRoot } from './icons/folder-root.js';
export { default as FolderSearch2 } from './icons/folder-search-2.js';
export { default as FolderSearch } from './icons/folder-search.js';
export { default as FolderSymlink } from './icons/folder-symlink.js';
export { default as FolderSync } from './icons/folder-sync.js';
export { default as FolderTree } from './icons/folder-tree.js';
export { default as FolderUp } from './icons/folder-up.js';
export { default as FolderX } from './icons/folder-x.js';
export { default as Folder } from './icons/folder.js';
export { default as Folders } from './icons/folders.js';
export { default as Footprints } from './icons/footprints.js';
export { default as Forklift } from './icons/forklift.js';
export { default as FormInput } from './icons/form-input.js';
export { default as Forward } from './icons/forward.js';
export { default as Frame } from './icons/frame.js';
export { default as Framer } from './icons/framer.js';
export { default as Frown } from './icons/frown.js';
export { default as Fuel } from './icons/fuel.js';
export { default as FunctionSquare } from './icons/function-square.js';
export { default as GalleryHorizontalEnd } from './icons/gallery-horizontal-end.js';
export { default as GalleryHorizontal } from './icons/gallery-horizontal.js';
export { default as GalleryThumbnails } from './icons/gallery-thumbnails.js';
export { default as GalleryVerticalEnd } from './icons/gallery-vertical-end.js';
export { default as GalleryVertical } from './icons/gallery-vertical.js';
export { default as Gamepad2 } from './icons/gamepad-2.js';
export { default as Gamepad } from './icons/gamepad.js';
export { default as GanttChartSquare } from './icons/gantt-chart-square.js';
export { default as GanttChart } from './icons/gantt-chart.js';
export { default as Gauge } from './icons/gauge.js';
export { default as Gavel } from './icons/gavel.js';
export { default as Gem } from './icons/gem.js';
export { default as Ghost } from './icons/ghost.js';
export { default as Gift } from './icons/gift.js';
export { default as GitBranchPlus } from './icons/git-branch-plus.js';
export { default as GitBranch } from './icons/git-branch.js';
export { default as GitCommit } from './icons/git-commit.js';
export { default as GitCompare } from './icons/git-compare.js';
export { default as GitFork } from './icons/git-fork.js';
export { default as GitMerge } from './icons/git-merge.js';
export { default as GitPullRequestClosed } from './icons/git-pull-request-closed.js';
export { default as GitPullRequestDraft } from './icons/git-pull-request-draft.js';
export { default as GitPullRequest } from './icons/git-pull-request.js';
export { default as Github } from './icons/github.js';
export { default as Gitlab } from './icons/gitlab.js';
export { default as GlassWater } from './icons/glass-water.js';
export { default as Glasses } from './icons/glasses.js';
export { default as Globe2 } from './icons/globe-2.js';
export { default as Globe } from './icons/globe.js';
export { default as Goal } from './icons/goal.js';
export { default as Grab } from './icons/grab.js';
export { default as GraduationCap } from './icons/graduation-cap.js';
export { default as Grape } from './icons/grape.js';
export { default as Grid } from './icons/grid.js';
export { default as GripHorizontal } from './icons/grip-horizontal.js';
export { default as GripVertical } from './icons/grip-vertical.js';
export { default as Grip } from './icons/grip.js';
export { default as Group } from './icons/group.js';
export { default as Hammer } from './icons/hammer.js';
export { default as HandMetal } from './icons/hand-metal.js';
export { default as Hand } from './icons/hand.js';
export { default as HardDrive } from './icons/hard-drive.js';
export { default as HardHat } from './icons/hard-hat.js';
export { default as Hash } from './icons/hash.js';
export { default as Haze } from './icons/haze.js';
export { default as Heading1 } from './icons/heading-1.js';
export { default as Heading2 } from './icons/heading-2.js';
export { default as Heading3 } from './icons/heading-3.js';
export { default as Heading4 } from './icons/heading-4.js';
export { default as Heading5 } from './icons/heading-5.js';
export { default as Heading6 } from './icons/heading-6.js';
export { default as Heading } from './icons/heading.js';
export { default as Headphones } from './icons/headphones.js';
export { default as HeartCrack } from './icons/heart-crack.js';
export { default as HeartHandshake } from './icons/heart-handshake.js';
export { default as HeartOff } from './icons/heart-off.js';
export { default as HeartPulse } from './icons/heart-pulse.js';
export { default as Heart } from './icons/heart.js';
export { default as HelpCircle } from './icons/help-circle.js';
export { default as HelpingHand } from './icons/helping-hand.js';
export { default as Hexagon } from './icons/hexagon.js';
export { default as Highlighter } from './icons/highlighter.js';
export { default as History } from './icons/history.js';
export { default as Home } from './icons/home.js';
export { default as HopOff } from './icons/hop-off.js';
export { default as Hop } from './icons/hop.js';
export { default as Hotel } from './icons/hotel.js';
export { default as Hourglass } from './icons/hourglass.js';
export { default as IceCream2 } from './icons/ice-cream-2.js';
export { default as IceCream } from './icons/ice-cream.js';
export { default as ImageMinus } from './icons/image-minus.js';
export { default as ImageOff } from './icons/image-off.js';
export { default as ImagePlus } from './icons/image-plus.js';
export { default as Image } from './icons/image.js';
export { default as Import } from './icons/import.js';
export { default as Inbox } from './icons/inbox.js';
export { default as Indent } from './icons/indent.js';
export { default as IndianRupee } from './icons/indian-rupee.js';
export { default as Infinity } from './icons/infinity.js';
export { default as Info } from './icons/info.js';
export { default as Inspect } from './icons/inspect.js';
export { default as Instagram } from './icons/instagram.js';
export { default as Italic } from './icons/italic.js';
export { default as IterationCcw } from './icons/iteration-ccw.js';
export { default as IterationCw } from './icons/iteration-cw.js';
export { default as JapaneseYen } from './icons/japanese-yen.js';
export { default as Joystick } from './icons/joystick.js';
export { default as KanbanSquareDashed } from './icons/kanban-square-dashed.js';
export { default as KanbanSquare } from './icons/kanban-square.js';
export { default as Kanban } from './icons/kanban.js';
export { default as KeyRound } from './icons/key-round.js';
export { default as KeySquare } from './icons/key-square.js';
export { default as Key } from './icons/key.js';
export { default as Keyboard } from './icons/keyboard.js';
export { default as LampCeiling } from './icons/lamp-ceiling.js';
export { default as LampDesk } from './icons/lamp-desk.js';
export { default as LampFloor } from './icons/lamp-floor.js';
export { default as LampWallDown } from './icons/lamp-wall-down.js';
export { default as LampWallUp } from './icons/lamp-wall-up.js';
export { default as Lamp } from './icons/lamp.js';
export { default as Landmark } from './icons/landmark.js';
export { default as Languages } from './icons/languages.js';
export { default as Laptop2 } from './icons/laptop-2.js';
export { default as Laptop } from './icons/laptop.js';
export { default as LassoSelect } from './icons/lasso-select.js';
export { default as Lasso } from './icons/lasso.js';
export { default as Laugh } from './icons/laugh.js';
export { default as Layers } from './icons/layers.js';
export { default as LayoutDashboard } from './icons/layout-dashboard.js';
export { default as LayoutGrid } from './icons/layout-grid.js';
export { default as LayoutList } from './icons/layout-list.js';
export { default as LayoutPanelLeft } from './icons/layout-panel-left.js';
export { default as LayoutPanelTop } from './icons/layout-panel-top.js';
export { default as LayoutTemplate } from './icons/layout-template.js';
export { default as Layout } from './icons/layout.js';
export { default as Leaf } from './icons/leaf.js';
export { default as LeafyGreen } from './icons/leafy-green.js';
export { default as Library } from './icons/library.js';
export { default as LifeBuoy } from './icons/life-buoy.js';
export { default as Ligature } from './icons/ligature.js';
export { default as LightbulbOff } from './icons/lightbulb-off.js';
export { default as Lightbulb } from './icons/lightbulb.js';
export { default as LineChart } from './icons/line-chart.js';
export { default as Link2Off } from './icons/link-2-off.js';
export { default as Link2 } from './icons/link-2.js';
export { default as Link } from './icons/link.js';
export { default as Linkedin } from './icons/linkedin.js';
export { default as ListChecks } from './icons/list-checks.js';
export { default as ListEnd } from './icons/list-end.js';
export { default as ListFilter } from './icons/list-filter.js';
export { default as ListMinus } from './icons/list-minus.js';
export { default as ListMusic } from './icons/list-music.js';
export { default as ListOrdered } from './icons/list-ordered.js';
export { default as ListPlus } from './icons/list-plus.js';
export { default as ListRestart } from './icons/list-restart.js';
export { default as ListStart } from './icons/list-start.js';
export { default as ListTodo } from './icons/list-todo.js';
export { default as ListTree } from './icons/list-tree.js';
export { default as ListVideo } from './icons/list-video.js';
export { default as ListX } from './icons/list-x.js';
export { default as List } from './icons/list.js';
export { default as Loader2 } from './icons/loader-2.js';
export { default as Loader } from './icons/loader.js';
export { default as LocateFixed } from './icons/locate-fixed.js';
export { default as LocateOff } from './icons/locate-off.js';
export { default as Locate } from './icons/locate.js';
export { default as Lock } from './icons/lock.js';
export { default as LogIn } from './icons/log-in.js';
export { default as LogOut } from './icons/log-out.js';
export { default as Lollipop } from './icons/lollipop.js';
export { default as Luggage } from './icons/luggage.js';
export { default as Magnet } from './icons/magnet.js';
export { default as MailCheck } from './icons/mail-check.js';
export { default as MailMinus } from './icons/mail-minus.js';
export { default as MailOpen } from './icons/mail-open.js';
export { default as MailPlus } from './icons/mail-plus.js';
export { default as MailQuestion } from './icons/mail-question.js';
export { default as MailSearch } from './icons/mail-search.js';
export { default as MailWarning } from './icons/mail-warning.js';
export { default as MailX } from './icons/mail-x.js';
export { default as Mail } from './icons/mail.js';
export { default as Mailbox } from './icons/mailbox.js';
export { default as Mails } from './icons/mails.js';
export { default as MapPinOff } from './icons/map-pin-off.js';
export { default as MapPin } from './icons/map-pin.js';
export { default as Map } from './icons/map.js';
export { default as Martini } from './icons/martini.js';
export { default as Maximize2 } from './icons/maximize-2.js';
export { default as Maximize } from './icons/maximize.js';
export { default as Medal } from './icons/medal.js';
export { default as MegaphoneOff } from './icons/megaphone-off.js';
export { default as Megaphone } from './icons/megaphone.js';
export { default as Meh } from './icons/meh.js';
export { default as MemoryStick } from './icons/memory-stick.js';
export { default as MenuSquare } from './icons/menu-square.js';
export { default as Menu } from './icons/menu.js';
export { default as Merge } from './icons/merge.js';
export { default as MessageCircle } from './icons/message-circle.js';
export { default as MessageSquareDashed } from './icons/message-square-dashed.js';
export { default as MessageSquarePlus } from './icons/message-square-plus.js';
export { default as MessageSquare } from './icons/message-square.js';
export { default as MessagesSquare } from './icons/messages-square.js';
export { default as Mic2 } from './icons/mic-2.js';
export { default as MicOff } from './icons/mic-off.js';
export { default as Mic } from './icons/mic.js';
export { default as Microscope } from './icons/microscope.js';
export { default as Microwave } from './icons/microwave.js';
export { default as Milestone } from './icons/milestone.js';
export { default as MilkOff } from './icons/milk-off.js';
export { default as Milk } from './icons/milk.js';
export { default as Minimize2 } from './icons/minimize-2.js';
export { default as Minimize } from './icons/minimize.js';
export { default as MinusCircle } from './icons/minus-circle.js';
export { default as MinusSquare } from './icons/minus-square.js';
export { default as Minus } from './icons/minus.js';
export { default as MonitorCheck } from './icons/monitor-check.js';
export { default as MonitorDot } from './icons/monitor-dot.js';
export { default as MonitorDown } from './icons/monitor-down.js';
export { default as MonitorOff } from './icons/monitor-off.js';
export { default as MonitorPause } from './icons/monitor-pause.js';
export { default as MonitorPlay } from './icons/monitor-play.js';
export { default as MonitorSmartphone } from './icons/monitor-smartphone.js';
export { default as MonitorSpeaker } from './icons/monitor-speaker.js';
export { default as MonitorStop } from './icons/monitor-stop.js';
export { default as MonitorUp } from './icons/monitor-up.js';
export { default as MonitorX } from './icons/monitor-x.js';
export { default as Monitor } from './icons/monitor.js';
export { default as MoonStar } from './icons/moon-star.js';
export { default as Moon } from './icons/moon.js';
export { default as MoreHorizontal } from './icons/more-horizontal.js';
export { default as MoreVertical } from './icons/more-vertical.js';
export { default as MountainSnow } from './icons/mountain-snow.js';
export { default as Mountain } from './icons/mountain.js';
export { default as MousePointer2 } from './icons/mouse-pointer-2.js';
export { default as MousePointerClick } from './icons/mouse-pointer-click.js';
export { default as MousePointer } from './icons/mouse-pointer.js';
export { default as Mouse } from './icons/mouse.js';
export { default as Move3d } from './icons/move-3d.js';
export { default as MoveDiagonal2 } from './icons/move-diagonal-2.js';
export { default as MoveDiagonal } from './icons/move-diagonal.js';
export { default as MoveDownLeft } from './icons/move-down-left.js';
export { default as MoveDownRight } from './icons/move-down-right.js';
export { default as MoveDown } from './icons/move-down.js';
export { default as MoveHorizontal } from './icons/move-horizontal.js';
export { default as MoveLeft } from './icons/move-left.js';
export { default as MoveRight } from './icons/move-right.js';
export { default as MoveUpLeft } from './icons/move-up-left.js';
export { default as MoveUpRight } from './icons/move-up-right.js';
export { default as MoveUp } from './icons/move-up.js';
export { default as MoveVertical } from './icons/move-vertical.js';
export { default as Move } from './icons/move.js';
export { default as Music2 } from './icons/music-2.js';
export { default as Music3 } from './icons/music-3.js';
export { default as Music4 } from './icons/music-4.js';
export { default as Music } from './icons/music.js';
export { default as Navigation2Off } from './icons/navigation-2-off.js';
export { default as Navigation2 } from './icons/navigation-2.js';
export { default as NavigationOff } from './icons/navigation-off.js';
export { default as Navigation } from './icons/navigation.js';
export { default as Network } from './icons/network.js';
export { default as Newspaper } from './icons/newspaper.js';
export { default as Nfc } from './icons/nfc.js';
export { default as NutOff } from './icons/nut-off.js';
export { default as Nut } from './icons/nut.js';
export { default as Octagon } from './icons/octagon.js';
export { default as Option } from './icons/option.js';
export { default as Orbit } from './icons/orbit.js';
export { default as Outdent } from './icons/outdent.js';
export { default as Package2 } from './icons/package-2.js';
export { default as PackageCheck } from './icons/package-check.js';
export { default as PackageMinus } from './icons/package-minus.js';
export { default as PackageOpen } from './icons/package-open.js';
export { default as PackagePlus } from './icons/package-plus.js';
export { default as PackageSearch } from './icons/package-search.js';
export { default as PackageX } from './icons/package-x.js';
export { default as Package } from './icons/package.js';
export { default as PaintBucket } from './icons/paint-bucket.js';
export { default as Paintbrush2 } from './icons/paintbrush-2.js';
export { default as Paintbrush } from './icons/paintbrush.js';
export { default as Palette } from './icons/palette.js';
export { default as Palmtree } from './icons/palmtree.js';
export { default as PanelBottomClose } from './icons/panel-bottom-close.js';
export { default as PanelBottomInactive } from './icons/panel-bottom-inactive.js';
export { default as PanelBottomOpen } from './icons/panel-bottom-open.js';
export { default as PanelBottom } from './icons/panel-bottom.js';
export { default as PanelLeftClose } from './icons/panel-left-close.js';
export { default as PanelLeftInactive } from './icons/panel-left-inactive.js';
export { default as PanelLeftOpen } from './icons/panel-left-open.js';
export { default as PanelLeft } from './icons/panel-left.js';
export { default as PanelRightClose } from './icons/panel-right-close.js';
export { default as PanelRightInactive } from './icons/panel-right-inactive.js';
export { default as PanelRightOpen } from './icons/panel-right-open.js';
export { default as PanelRight } from './icons/panel-right.js';
export { default as PanelTopClose } from './icons/panel-top-close.js';
export { default as PanelTopInactive } from './icons/panel-top-inactive.js';
export { default as PanelTopOpen } from './icons/panel-top-open.js';
export { default as PanelTop } from './icons/panel-top.js';
export { default as Paperclip } from './icons/paperclip.js';
export { default as Parentheses } from './icons/parentheses.js';
export { default as ParkingCircleOff } from './icons/parking-circle-off.js';
export { default as ParkingCircle } from './icons/parking-circle.js';
export { default as ParkingSquareOff } from './icons/parking-square-off.js';
export { default as ParkingSquare } from './icons/parking-square.js';
export { default as PartyPopper } from './icons/party-popper.js';
export { default as PauseCircle } from './icons/pause-circle.js';
export { default as PauseOctagon } from './icons/pause-octagon.js';
export { default as Pause } from './icons/pause.js';
export { default as PcCase } from './icons/pc-case.js';
export { default as PenTool } from './icons/pen-tool.js';
export { default as Pencil } from './icons/pencil.js';
export { default as Percent } from './icons/percent.js';
export { default as PersonStanding } from './icons/person-standing.js';
export { default as PhoneCall } from './icons/phone-call.js';
export { default as PhoneForwarded } from './icons/phone-forwarded.js';
export { default as PhoneIncoming } from './icons/phone-incoming.js';
export { default as PhoneMissed } from './icons/phone-missed.js';
export { default as PhoneOff } from './icons/phone-off.js';
export { default as PhoneOutgoing } from './icons/phone-outgoing.js';
export { default as Phone } from './icons/phone.js';
export { default as PiSquare } from './icons/pi-square.js';
export { default as Pi } from './icons/pi.js';
export { default as PictureInPicture2 } from './icons/picture-in-picture-2.js';
export { default as PictureInPicture } from './icons/picture-in-picture.js';
export { default as PieChart } from './icons/pie-chart.js';
export { default as PiggyBank } from './icons/piggy-bank.js';
export { default as PilcrowSquare } from './icons/pilcrow-square.js';
export { default as Pilcrow } from './icons/pilcrow.js';
export { default as Pill } from './icons/pill.js';
export { default as PinOff } from './icons/pin-off.js';
export { default as Pin } from './icons/pin.js';
export { default as Pipette } from './icons/pipette.js';
export { default as Pizza } from './icons/pizza.js';
export { default as PlaneLanding } from './icons/plane-landing.js';
export { default as PlaneTakeoff } from './icons/plane-takeoff.js';
export { default as Plane } from './icons/plane.js';
export { default as PlayCircle } from './icons/play-circle.js';
export { default as PlaySquare } from './icons/play-square.js';
export { default as Play } from './icons/play.js';
export { default as Plug2 } from './icons/plug-2.js';
export { default as PlugZap2 } from './icons/plug-zap-2.js';
export { default as PlugZap } from './icons/plug-zap.js';
export { default as Plug } from './icons/plug.js';
export { default as PlusCircle } from './icons/plus-circle.js';
export { default as PlusSquare } from './icons/plus-square.js';
export { default as Plus } from './icons/plus.js';
export { default as PocketKnife } from './icons/pocket-knife.js';
export { default as Pocket } from './icons/pocket.js';
export { default as Podcast } from './icons/podcast.js';
export { default as Pointer } from './icons/pointer.js';
export { default as Popcorn } from './icons/popcorn.js';
export { default as Popsicle } from './icons/popsicle.js';
export { default as PoundSterling } from './icons/pound-sterling.js';
export { default as PowerOff } from './icons/power-off.js';
export { default as Power } from './icons/power.js';
export { default as Presentation } from './icons/presentation.js';
export { default as Printer } from './icons/printer.js';
export { default as Projector } from './icons/projector.js';
export { default as Puzzle } from './icons/puzzle.js';
export { default as QrCode } from './icons/qr-code.js';
export { default as Quote } from './icons/quote.js';
export { default as Radar } from './icons/radar.js';
export { default as Radiation } from './icons/radiation.js';
export { default as RadioReceiver } from './icons/radio-receiver.js';
export { default as RadioTower } from './icons/radio-tower.js';
export { default as Radio } from './icons/radio.js';
export { default as Rainbow } from './icons/rainbow.js';
export { default as Rat } from './icons/rat.js';
export { default as Receipt } from './icons/receipt.js';
export { default as RectangleHorizontal } from './icons/rectangle-horizontal.js';
export { default as RectangleVertical } from './icons/rectangle-vertical.js';
export { default as Recycle } from './icons/recycle.js';
export { default as Redo2 } from './icons/redo-2.js';
export { default as RedoDot } from './icons/redo-dot.js';
export { default as Redo } from './icons/redo.js';
export { default as RefreshCcwDot } from './icons/refresh-ccw-dot.js';
export { default as RefreshCcw } from './icons/refresh-ccw.js';
export { default as RefreshCwOff } from './icons/refresh-cw-off.js';
export { default as RefreshCw } from './icons/refresh-cw.js';
export { default as Refrigerator } from './icons/refrigerator.js';
export { default as Regex } from './icons/regex.js';
export { default as RemoveFormatting } from './icons/remove-formatting.js';
export { default as Repeat1 } from './icons/repeat-1.js';
export { default as Repeat2 } from './icons/repeat-2.js';
export { default as Repeat } from './icons/repeat.js';
export { default as ReplaceAll } from './icons/replace-all.js';
export { default as Replace } from './icons/replace.js';
export { default as ReplyAll } from './icons/reply-all.js';
export { default as Reply } from './icons/reply.js';
export { default as Rewind } from './icons/rewind.js';
export { default as Rocket } from './icons/rocket.js';
export { default as RockingChair } from './icons/rocking-chair.js';
export { default as RollerCoaster } from './icons/roller-coaster.js';
export { default as Rotate3d } from './icons/rotate-3d.js';
export { default as RotateCcw } from './icons/rotate-ccw.js';
export { default as RotateCw } from './icons/rotate-cw.js';
export { default as Router } from './icons/router.js';
export { default as Rows } from './icons/rows.js';
export { default as Rss } from './icons/rss.js';
export { default as Ruler } from './icons/ruler.js';
export { default as RussianRuble } from './icons/russian-ruble.js';
export { default as Sailboat } from './icons/sailboat.js';
export { default as Salad } from './icons/salad.js';
export { default as Sandwich } from './icons/sandwich.js';
export { default as SatelliteDish } from './icons/satellite-dish.js';
export { default as Satellite } from './icons/satellite.js';
export { default as SaveAll } from './icons/save-all.js';
export { default as Save } from './icons/save.js';
export { default as Scale3d } from './icons/scale-3d.js';
export { default as Scale } from './icons/scale.js';
export { default as Scaling } from './icons/scaling.js';
export { default as ScanFace } from './icons/scan-face.js';
export { default as ScanLine } from './icons/scan-line.js';
export { default as Scan } from './icons/scan.js';
export { default as ScatterChart } from './icons/scatter-chart.js';
export { default as School2 } from './icons/school-2.js';
export { default as School } from './icons/school.js';
export { default as Scissors } from './icons/scissors.js';
export { default as ScreenShareOff } from './icons/screen-share-off.js';
export { default as ScreenShare } from './icons/screen-share.js';
export { default as ScrollText } from './icons/scroll-text.js';
export { default as Scroll } from './icons/scroll.js';
export { default as SearchCheck } from './icons/search-check.js';
export { default as SearchCode } from './icons/search-code.js';
export { default as SearchSlash } from './icons/search-slash.js';
export { default as SearchX } from './icons/search-x.js';
export { default as Search } from './icons/search.js';
export { default as Send } from './icons/send.js';
export { default as SeparatorHorizontal } from './icons/separator-horizontal.js';
export { default as SeparatorVertical } from './icons/separator-vertical.js';
export { default as ServerCog } from './icons/server-cog.js';
export { default as ServerCrash } from './icons/server-crash.js';
export { default as ServerOff } from './icons/server-off.js';
export { default as Server } from './icons/server.js';
export { default as Settings2 } from './icons/settings-2.js';
export { default as Settings } from './icons/settings.js';
export { default as Shapes } from './icons/shapes.js';
export { default as Share2 } from './icons/share-2.js';
export { default as Share } from './icons/share.js';
export { default as Sheet } from './icons/sheet.js';
export { default as ShieldAlert } from './icons/shield-alert.js';
export { default as ShieldCheck } from './icons/shield-check.js';
export { default as ShieldClose } from './icons/shield-close.js';
export { default as ShieldOff } from './icons/shield-off.js';
export { default as ShieldQuestion } from './icons/shield-question.js';
export { default as Shield } from './icons/shield.js';
export { default as Ship } from './icons/ship.js';
export { default as Shirt } from './icons/shirt.js';
export { default as ShoppingBag } from './icons/shopping-bag.js';
export { default as ShoppingCart } from './icons/shopping-cart.js';
export { default as Shovel } from './icons/shovel.js';
export { default as ShowerHead } from './icons/shower-head.js';
export { default as Shrink } from './icons/shrink.js';
export { default as Shrub } from './icons/shrub.js';
export { default as Shuffle } from './icons/shuffle.js';
export { default as SigmaSquare } from './icons/sigma-square.js';
export { default as Sigma } from './icons/sigma.js';
export { default as SignalHigh } from './icons/signal-high.js';
export { default as SignalLow } from './icons/signal-low.js';
export { default as SignalMedium } from './icons/signal-medium.js';
export { default as SignalZero } from './icons/signal-zero.js';
export { default as Signal } from './icons/signal.js';
export { default as Siren } from './icons/siren.js';
export { default as SkipBack } from './icons/skip-back.js';
export { default as SkipForward } from './icons/skip-forward.js';
export { default as Skull } from './icons/skull.js';
export { default as Slack } from './icons/slack.js';
export { default as Slice } from './icons/slice.js';
export { default as SlidersHorizontal } from './icons/sliders-horizontal.js';
export { default as Sliders } from './icons/sliders.js';
export { default as SmartphoneCharging } from './icons/smartphone-charging.js';
export { default as SmartphoneNfc } from './icons/smartphone-nfc.js';
export { default as Smartphone } from './icons/smartphone.js';
export { default as SmilePlus } from './icons/smile-plus.js';
export { default as Smile } from './icons/smile.js';
export { default as Snowflake } from './icons/snowflake.js';
export { default as Sofa } from './icons/sofa.js';
export { default as Soup } from './icons/soup.js';
export { default as Space } from './icons/space.js';
export { default as Spade } from './icons/spade.js';
export { default as Sparkle } from './icons/sparkle.js';
export { default as Sparkles } from './icons/sparkles.js';
export { default as Speaker } from './icons/speaker.js';
export { default as SpellCheck2 } from './icons/spell-check-2.js';
export { default as SpellCheck } from './icons/spell-check.js';
export { default as Spline } from './icons/spline.js';
export { default as SplitSquareHorizontal } from './icons/split-square-horizontal.js';
export { default as SplitSquareVertical } from './icons/split-square-vertical.js';
export { default as Split } from './icons/split.js';
export { default as SprayCan } from './icons/spray-can.js';
export { default as Sprout } from './icons/sprout.js';
export { default as SquareAsterisk } from './icons/square-asterisk.js';
export { default as SquareCode } from './icons/square-code.js';
export { default as SquareDashedBottomCode } from './icons/square-dashed-bottom-code.js';
export { default as SquareDashedBottom } from './icons/square-dashed-bottom.js';
export { default as SquareDot } from './icons/square-dot.js';
export { default as SquareEqual } from './icons/square-equal.js';
export { default as SquareSlash } from './icons/square-slash.js';
export { default as SquareStack } from './icons/square-stack.js';
export { default as Square } from './icons/square.js';
export { default as Squirrel } from './icons/squirrel.js';
export { default as Stamp } from './icons/stamp.js';
export { default as StarHalf } from './icons/star-half.js';
export { default as StarOff } from './icons/star-off.js';
export { default as Star } from './icons/star.js';
export { default as StepBack } from './icons/step-back.js';
export { default as StepForward } from './icons/step-forward.js';
export { default as Stethoscope } from './icons/stethoscope.js';
export { default as Sticker } from './icons/sticker.js';
export { default as StickyNote } from './icons/sticky-note.js';
export { default as StopCircle } from './icons/stop-circle.js';
export { default as Store } from './icons/store.js';
export { default as StretchHorizontal } from './icons/stretch-horizontal.js';
export { default as StretchVertical } from './icons/stretch-vertical.js';
export { default as Strikethrough } from './icons/strikethrough.js';
export { default as Subscript } from './icons/subscript.js';
export { default as Subtitles } from './icons/subtitles.js';
export { default as SunDim } from './icons/sun-dim.js';
export { default as SunMedium } from './icons/sun-medium.js';
export { default as SunMoon } from './icons/sun-moon.js';
export { default as SunSnow } from './icons/sun-snow.js';
export { default as Sun } from './icons/sun.js';
export { default as Sunrise } from './icons/sunrise.js';
export { default as Sunset } from './icons/sunset.js';
export { default as Superscript } from './icons/superscript.js';
export { default as SwissFranc } from './icons/swiss-franc.js';
export { default as SwitchCamera } from './icons/switch-camera.js';
export { default as Sword } from './icons/sword.js';
export { default as Swords } from './icons/swords.js';
export { default as Syringe } from './icons/syringe.js';
export { default as Table2 } from './icons/table-2.js';
export { default as TableProperties } from './icons/table-properties.js';
export { default as Table } from './icons/table.js';
export { default as Tablet } from './icons/tablet.js';
export { default as Tablets } from './icons/tablets.js';
export { default as Tag } from './icons/tag.js';
export { default as Tags } from './icons/tags.js';
export { default as Tally1 } from './icons/tally-1.js';
export { default as Tally2 } from './icons/tally-2.js';
export { default as Tally3 } from './icons/tally-3.js';
export { default as Tally4 } from './icons/tally-4.js';
export { default as Tally5 } from './icons/tally-5.js';
export { default as Target } from './icons/target.js';
export { default as Tent } from './icons/tent.js';
export { default as TerminalSquare } from './icons/terminal-square.js';
export { default as Terminal } from './icons/terminal.js';
export { default as TestTube2 } from './icons/test-tube-2.js';
export { default as TestTube } from './icons/test-tube.js';
export { default as TestTubes } from './icons/test-tubes.js';
export { default as TextCursorInput } from './icons/text-cursor-input.js';
export { default as TextCursor } from './icons/text-cursor.js';
export { default as TextQuote } from './icons/text-quote.js';
export { default as TextSelect } from './icons/text-select.js';
export { default as Text } from './icons/text.js';
export { default as ThermometerSnowflake } from './icons/thermometer-snowflake.js';
export { default as ThermometerSun } from './icons/thermometer-sun.js';
export { default as Thermometer } from './icons/thermometer.js';
export { default as ThumbsDown } from './icons/thumbs-down.js';
export { default as ThumbsUp } from './icons/thumbs-up.js';
export { default as Ticket } from './icons/ticket.js';
export { default as TimerOff } from './icons/timer-off.js';
export { default as TimerReset } from './icons/timer-reset.js';
export { default as Timer } from './icons/timer.js';
export { default as ToggleLeft } from './icons/toggle-left.js';
export { default as ToggleRight } from './icons/toggle-right.js';
export { default as Tornado } from './icons/tornado.js';
export { default as TouchpadOff } from './icons/touchpad-off.js';
export { default as Touchpad } from './icons/touchpad.js';
export { default as TowerControl } from './icons/tower-control.js';
export { default as ToyBrick } from './icons/toy-brick.js';
export { default as Train } from './icons/train.js';
export { default as Trash2 } from './icons/trash-2.js';
export { default as Trash } from './icons/trash.js';
export { default as TreeDeciduous } from './icons/tree-deciduous.js';
export { default as TreePine } from './icons/tree-pine.js';
export { default as Trees } from './icons/trees.js';
export { default as Trello } from './icons/trello.js';
export { default as TrendingDown } from './icons/trending-down.js';
export { default as TrendingUp } from './icons/trending-up.js';
export { default as Triangle } from './icons/triangle.js';
export { default as Trophy } from './icons/trophy.js';
export { default as Truck } from './icons/truck.js';
export { default as Tv2 } from './icons/tv-2.js';
export { default as Tv } from './icons/tv.js';
export { default as Twitch } from './icons/twitch.js';
export { default as Twitter } from './icons/twitter.js';
export { default as Type } from './icons/type.js';
export { default as Umbrella } from './icons/umbrella.js';
export { default as Underline } from './icons/underline.js';
export { default as Undo2 } from './icons/undo-2.js';
export { default as UndoDot } from './icons/undo-dot.js';
export { default as Undo } from './icons/undo.js';
export { default as UnfoldHorizontal } from './icons/unfold-horizontal.js';
export { default as UnfoldVertical } from './icons/unfold-vertical.js';
export { default as Ungroup } from './icons/ungroup.js';
export { default as Unlink2 } from './icons/unlink-2.js';
export { default as Unlink } from './icons/unlink.js';
export { default as Unlock } from './icons/unlock.js';
export { default as Unplug } from './icons/unplug.js';
export { default as UploadCloud } from './icons/upload-cloud.js';
export { default as Upload } from './icons/upload.js';
export { default as Usb } from './icons/usb.js';
export { default as User2 } from './icons/user-2.js';
export { default as UserCheck2 } from './icons/user-check-2.js';
export { default as UserCheck } from './icons/user-check.js';
export { default as UserCircle2 } from './icons/user-circle-2.js';
export { default as UserCircle } from './icons/user-circle.js';
export { default as UserCog2 } from './icons/user-cog-2.js';
export { default as UserCog } from './icons/user-cog.js';
export { default as UserMinus2 } from './icons/user-minus-2.js';
export { default as UserMinus } from './icons/user-minus.js';
export { default as UserPlus2 } from './icons/user-plus-2.js';
export { default as UserPlus } from './icons/user-plus.js';
export { default as UserSquare2 } from './icons/user-square-2.js';
export { default as UserSquare } from './icons/user-square.js';
export { default as UserX2 } from './icons/user-x-2.js';
export { default as UserX } from './icons/user-x.js';
export { default as User } from './icons/user.js';
export { default as Users2 } from './icons/users-2.js';
export { default as Users } from './icons/users.js';
export { default as UtensilsCrossed } from './icons/utensils-crossed.js';
export { default as Utensils } from './icons/utensils.js';
export { default as UtilityPole } from './icons/utility-pole.js';
export { default as Variable } from './icons/variable.js';
export { default as Vegan } from './icons/vegan.js';
export { default as VenetianMask } from './icons/venetian-mask.js';
export { default as VibrateOff } from './icons/vibrate-off.js';
export { default as Vibrate } from './icons/vibrate.js';
export { default as VideoOff } from './icons/video-off.js';
export { default as Video } from './icons/video.js';
export { default as Videotape } from './icons/videotape.js';
export { default as View } from './icons/view.js';
export { default as Voicemail } from './icons/voicemail.js';
export { default as Volume1 } from './icons/volume-1.js';
export { default as Volume2 } from './icons/volume-2.js';
export { default as VolumeX } from './icons/volume-x.js';
export { default as Volume } from './icons/volume.js';
export { default as Vote } from './icons/vote.js';
export { default as Wallet2 } from './icons/wallet-2.js';
export { default as WalletCards } from './icons/wallet-cards.js';
export { default as Wallet } from './icons/wallet.js';
export { default as Wallpaper } from './icons/wallpaper.js';
export { default as Wand2 } from './icons/wand-2.js';
export { default as Wand } from './icons/wand.js';
export { default as Warehouse } from './icons/warehouse.js';
export { default as Watch } from './icons/watch.js';
export { default as Waves } from './icons/waves.js';
export { default as Webcam } from './icons/webcam.js';
export { default as Webhook } from './icons/webhook.js';
export { default as WheatOff } from './icons/wheat-off.js';
export { default as Wheat } from './icons/wheat.js';
export { default as WholeWord } from './icons/whole-word.js';
export { default as WifiOff } from './icons/wifi-off.js';
export { default as Wifi } from './icons/wifi.js';
export { default as Wind } from './icons/wind.js';
export { default as WineOff } from './icons/wine-off.js';
export { default as Wine } from './icons/wine.js';
export { default as Workflow } from './icons/workflow.js';
export { default as WrapText } from './icons/wrap-text.js';
export { default as Wrench } from './icons/wrench.js';
export { default as XCircle } from './icons/x-circle.js';
export { default as XOctagon } from './icons/x-octagon.js';
export { default as XSquare } from './icons/x-square.js';
export { default as X } from './icons/x.js';
export { default as Youtube } from './icons/youtube.js';
export { default as ZapOff } from './icons/zap-off.js';
export { default as Zap } from './icons/zap.js';
export { default as ZoomIn } from './icons/zoom-in.js';
export { default as ZoomOut } from './icons/zoom-out.js';

const createIcons = ({ icons = {}, nameAttr = "data-lucide", attrs = {} } = {}) => {
  if (!Object.values(icons).length) {
    throw new Error(
      "Please provide an icons object.\nIf you want to use all the icons you can import it like:\n `import { createIcons, icons } from 'lucide';\nlucide.createIcons({icons});`"
    );
  }
  if (typeof document === "undefined") {
    throw new Error("`createIcons()` only works in a browser environment.");
  }
  const elementsToReplace = document.querySelectorAll(`[${nameAttr}]`);
  Array.from(elementsToReplace).forEach(
    (element) => replaceElement(element, { nameAttr, icons, attrs })
  );
  if (nameAttr === "data-lucide") {
    const deprecatedElements = document.querySelectorAll("[icon-name]");
    if (deprecatedElements.length > 0) {
      console.warn("[Lucide] Some icons were found with the now deprecated icon-name attribute. These will still be replaced for backwards compatibility, but will no longer be supported in v1.0 and you should switch to data-lucide");
      Array.from(deprecatedElements).forEach(
        (element) => replaceElement(element, { nameAttr: "icon-name", icons, attrs })
      );
    }
  }
};

export { createIcons };
//# sourceMappingURL=lucide.js.map
