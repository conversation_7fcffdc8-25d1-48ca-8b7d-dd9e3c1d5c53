const mongoose = require('mongoose');

const rawMaterialGroupSchema = new mongoose.Schema({
  Group: { type: String, required: true },
  Item: { type: String, required: true },
  Quality:{
  type:String,
  required:false,
},
  Description: { type: String, required: false },
  Count: { type: String, required: false },
  Color:{ type: String, required: false },
  
  Details: { type: String, required: false },
  uploadedFile: {
    type: String, // or you can use Buffer for binary data, but String is typical for file paths/URLs
    //required: true
},

});

module.exports = mongoose.model('RawMaterialGroup', rawMaterialGroupSchema);
