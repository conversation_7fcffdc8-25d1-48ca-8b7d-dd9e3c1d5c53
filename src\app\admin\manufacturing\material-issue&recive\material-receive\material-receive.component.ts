import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';
import Swal from 'sweetalert2';
import { ManufactureService } from '../../../../services/manufacture.service';
import { MasterService } from '../../../../services/master.service';
import { KatiReceiveModalComponent } from './modal/kati-receive-modal/kati-receive-modal.component';


@Component({
  selector: 'app-material-receive',
  templateUrl: './material-receive.component.html',
  styleUrl: './material-receive.component.css'
})
export class MaterialReceiveComponent implements OnInit {
  materialReceiveForm!: FormGroup;
  list: any[] = []; // weavers list
  weavers: any[] = []; // weavers list (alias for compatibility)
  availableIssues: any[] = []; // carpet order issues
  carpetOrderIssues: any[] = []; // all carpet order issues
  materialIssueList: any[] = []; // material issues for challan details
  materialLagatList: any[] = []; // material lagats for descriptions and lagats
  selectedIssueData: any = null;

  displayedColumns: string[] = ['issueNo', 'quality', 'design', 'colour', 'size', 'pcs', 'area'];
  dataSource = new MatTableDataSource<any>([]);
  isEditMode: any = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private manufactureService: ManufactureService,
    private masterService: MasterService,
    private dialog: MatDialog
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    // Load data in proper sequence
    this.loadCarpetOrderIssues();
    this.loadMaterialIssuesAndWeavers();
    this.loadMaterialLagats();

    // Check if we're in edit mode
    this.route.queryParams.subscribe(params => {
      if (params['editMode'] === 'true' && params['editId']) {
        console.log('🔍 Edit mode detected, ID:', params['editId']);
        this.isEditMode = true;
        this.loadEditData(params['editId']);
      } else {
        this.isEditMode = false;
        this.generateChallanNo();
      }
    });
  }

  initializeForm(): void {
    this.materialReceiveForm = this.fb.group({
      challanNo: ['', Validators.required],
      date: [new Date().toISOString().split('T')[0], Validators.required],
      weaver: ['', Validators.required],
      issueNo: ['', Validators.required],

      // Material descriptions (readonly - from issue)
      katiDescription: [''],
      tanaDescription: [''],
      sootDescription: [''],
      thariDescription: [''],
      silkDescription: [''],
      otherDescription: [''],

      // Material lagats (readonly - from issue)
      katiLagat: [''],
      tanaLagat: [''],
      sootLagat: [''],
      thariLagat: [''],
      silkLagat: [''],
      otherLagat: [''],

      // Material issues (readonly - from issue)
      katiIssue: [''],
      tanaIssue: [''],
      sootIssue: [''],
      thariIssue: [''],
      silkIssue: [''],
      otherIssue: [''],

      // Material receives (user input)
      katiReceive: [''],
      tanaReceive: [''],
      sootReceive: [''],
      thariReceive: [''],
      silkReceive: [''],
      otherReceive: [''],

      // Material remaining (calculated)
      katiRemaining: [''],
      tanaRemaining: [''],
      sootRemaining: [''],
      thariRemaining: [''],
      silkRemaining: [''],
      otherRemaining: ['']
    });

    // Add value change listeners for receive fields to calculate remaining
    this.setupRemainingCalculations();
  }

  setupRemainingCalculations(): void {
    const materialTypes = ['kati', 'tana', 'soot', 'thari', 'silk', 'other'];

    materialTypes.forEach(type => {
      this.materialReceiveForm.get(`${type}Receive`)?.valueChanges.subscribe(receiveValue => {
        const issueValue = this.materialReceiveForm.get(`${type}Issue`)?.value || 0;
        const remaining = Math.max(0, issueValue - (receiveValue || 0));
        this.materialReceiveForm.patchValue({
          [`${type}Remaining`]: remaining
        }, { emitEvent: false });
      });
    });
  }

  setDigitReceive(val: any, fieldName: string) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      val.target.value = _val.toFixed(3);
      this.materialReceiveForm.get(fieldName)?.patchValue(_val.toFixed(3));

      // Calculate remaining for the corresponding material type
      this.calculateRemaining(fieldName);
    }
  }

  calculateRemaining(receiveFieldName: string) {
    // Map receive field names to their corresponding issue and remaining field names
    const fieldMapping: { [key: string]: { issue: string, remaining: string } } = {
      'katiReceive': { issue: 'katiIssue', remaining: 'katiRemaining' },
      'tanaReceive': { issue: 'tanaIssue', remaining: 'tanaRemaining' },
      'sootReceive': { issue: 'sootIssue', remaining: 'sootRemaining' },
      'thariReceive': { issue: 'thariIssue', remaining: 'thariRemaining' },
      'silkReceive': { issue: 'silkIssue', remaining: 'silkRemaining' },
      'otherReceive': { issue: 'otherIssue', remaining: 'otherRemaining' }
    };

    const mapping = fieldMapping[receiveFieldName];
    if (mapping) {
      const receiveValue = parseFloat(this.materialReceiveForm.get(receiveFieldName)?.value || '0');

      // Only calculate remaining if receive value is entered (greater than 0)
      if (receiveValue > 0) {
        const issueValue = parseFloat(this.materialReceiveForm.get(mapping.issue)?.value || '0');
        const remaining = Math.max(0, issueValue - receiveValue); // Ensure remaining is not negative
        this.materialReceiveForm.get(mapping.remaining)?.patchValue(this.formatToThreeDecimals(remaining));

        console.log(`✅ Calculated remaining for ${receiveFieldName}:`, {
          issue: issueValue,
          receive: receiveValue,
          remaining: remaining
        });
      } else {
        // If no receive value, clear the remaining field
        this.materialReceiveForm.get(mapping.remaining)?.patchValue('');
      }
    }
  }

  openKatiReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('kati', 'Kati');
  }

  openTanaReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('tana', 'Tana');
  }

  openSootReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('soot', 'Soot');
  }

  openThariReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('thari', 'Thari');
  }

  openSilkReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('silk', 'Silk');
  }

  openOtherReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('other', 'Other');
  }

  private openMaterialReceiveModal(materialType: string, materialName: string): void {
    console.log(`🔍 Opening ${materialName} Receive Modal...`);

    // Check if weaver and issue are selected
    const selectedWeaver = this.materialReceiveForm.get('weaver')?.value;
    const selectedIssue = this.materialReceiveForm.get('issueNo')?.value;

    if (!selectedWeaver || !selectedIssue) {
      Swal.fire('Warning', 'Please select weaver and issue number first', 'warning');
      return;
    }

    // Find the selected weaver and issue objects
    const weaverObj = this.weavers.find(w => w._id === selectedWeaver);
    const issueObj = this.availableIssues.find(i => i._id === selectedIssue);

    if (!weaverObj || !issueObj) {
      Swal.fire('Error', 'Selected weaver or issue not found', 'error');
      return;
    }

    const dialogRef = this.dialog.open(KatiReceiveModalComponent, {
      width: '90%',
      maxWidth: '1200px',
      height: '80%',
      data: {
        weaver: weaverObj,
        issueNo: issueObj,
        materialType: materialType,
        materialName: materialName
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.totalReceive) {
        console.log(`✅ ${materialName} receive data received:`, result);

        // Update the corresponding receive field with total
        const receiveFieldName = `${materialType}Receive`;
        this.materialReceiveForm.patchValue({
          [receiveFieldName]: result.totalReceive.toFixed(3)
        });

        // Trigger remaining calculation
        this.calculateRemaining(receiveFieldName);

        Swal.fire('Success', `${materialName} receive data updated successfully`, 'success');
      }
    });
  }

  generateChallanNo(): void {
    // Generate challan number with MR- prefix starting from MR-2400001
    let nextChallanNo = 'MR-2400001'; // Default starting number
    console.log('✅ Generated challan number:', nextChallanNo);
    this.materialReceiveForm.patchValue({ challanNo: nextChallanNo });
  }

  loadMaterialIssuesAndWeavers(): void {
    console.log('🚀 Starting loadMaterialIssuesAndWeavers...');

    // First load material issues
    this.manufactureService.getAllMaterialIssues().subscribe({
      next: (materialIssues: any) => {
        console.log('🔍 Material issues loaded:', materialIssues?.length || 0);
        this.materialIssueList = materialIssues || [];

        // Then load all weavers and filter them
        this.masterService.getAllWeaverEmployee().subscribe({
          next: (allWeavers: any) => {
            console.log('🔍 All weavers loaded:', allWeavers?.length || 0);
            this.filterWeaversBasedOnMaterialIssues(allWeavers || []);
          },
          error: (error: any) => {
            console.error('❌ Error loading weavers:', error);
          }
        });
      },
      error: (error: any) => {
        console.error('❌ Error loading material issues:', error);
      }
    });
  }

  navigateToMaterialIssue(): void {
    console.log('Navigating to Material Issue Component');
    this.router.navigate(['/admin/material-issue']);
  }

  loadWeavers(): void {
    // First load all weavers, then filter based on material issues
    this.masterService.getAllWeaverEmployee().subscribe({
      next: (response: any) => {
        console.log('🔍 All weavers loaded:', response);
        const allWeavers = response || [];

        // Filter weavers that exist in materialIssueList
        this.filterWeaversBasedOnMaterialIssues(allWeavers);
      },
      error: (error: any) => {
        console.error('❌ Error loading weavers:', error);
      }
    });
  }

  filterWeaversBasedOnMaterialIssues(allWeavers: any[]): void {
    console.log('🔍 Starting weaver filtering...');
    console.log('🔍 All weavers count:', allWeavers.length);
    console.log('🔍 Material issues count:', this.materialIssueList.length);
    console.log('🔍 Carpet order issues count:', this.carpetOrderIssues.length);

    // Get unique weaver IDs from all three collections

    // 1. From material issues
    const weaverIdsInMaterialIssues = [...new Set(
      this.materialIssueList.map(issue => issue.weaver?._id || issue.weaver)
    )];

    // 2. From carpet order issues
    const weaverIdsInCarpetOrderIssues = [...new Set(
      this.carpetOrderIssues.map(issue => issue.weaver?._id || issue.weaver)
    )];

    // 3. Find intersection - weavers that exist in both collections
    const commonWeaverIds = weaverIdsInMaterialIssues.filter(id =>
      weaverIdsInCarpetOrderIssues.includes(id)
    );

    console.log('🔍 Weaver IDs in material issues:', weaverIdsInMaterialIssues);
    console.log('🔍 Weaver IDs in carpet order issues:', weaverIdsInCarpetOrderIssues);
    console.log('🔍 Common weaver IDs:', commonWeaverIds);

    // Filter weavers that exist in both collections
    const filteredWeavers = allWeavers.filter(weaver =>
      commonWeaverIds.includes(weaver._id)
    );

    this.list = filteredWeavers.map((weaver: any) => ({
      name: weaver.name,
      displayName: `${weaver.branch?.branchCode || 'K'} - ${weaver.name}`,
      weaver: weaver
    }));

    // Also populate weavers array for modal compatibility
    this.weavers = filteredWeavers;

    console.log('🔍 Filtered weavers for dropdown:', this.list.length);
  }

  loadCarpetOrderIssues(): void {
    this.manufactureService.getsOrderIssueList().subscribe({
      next: (response: any) => {
        console.log('🔍 Carpet order issues loaded:', response);
        this.carpetOrderIssues = response || [];
      },
      error: (error: any) => {
        console.error('❌ Error loading carpet order issues:', error);
      }
    });
  }



  loadEditData(editId: string): void {
    console.log('🔍 Loading edit data for ID:', editId);
    // Implementation for edit mode
  }

  populateFormWithEditData(data: any): void {
    console.log('🔍 Populating form with edit data:', data);
    // Implementation for edit mode
  }

  onWeaverChange(): void {
    const selectedWeaverName = this.materialReceiveForm.get('weaver')?.value;
    console.log('🔍 Weaver selected:', selectedWeaverName);

    if (selectedWeaverName) {
      // Get selected weaver object
      const selectedWeaver = this.list.find(w => w.name === selectedWeaverName)?.weaver;
      const selectedWeaverId = selectedWeaver?._id;

      console.log('🔍 Selected weaver ID:', selectedWeaverId);

      // Filter material issues for this weaver
      const weaverMaterialIssues = this.materialIssueList.filter((materialIssue: any) => {
        const materialWeaverId = typeof materialIssue.weaver === 'object' ?
          materialIssue.weaver._id : materialIssue.weaver;
        return materialWeaverId === selectedWeaverId;
      });

      console.log('🔍 Weaver material issues found:', weaverMaterialIssues);

      // Get unique Br_issueNo from material issues for this weaver
      const weaverIssueNumbers = [...new Set(
        weaverMaterialIssues
          .filter(mi => mi.issueNo && mi.issueNo.Br_issueNo)
          .map(mi => mi.issueNo.Br_issueNo)
      )];

      console.log('🔍 Weaver issue numbers from material issues:', weaverIssueNumbers);

      // Filter carpet order issues that match both weaver and have material issued
      this.availableIssues = this.carpetOrderIssues.filter((issue: any) => {
        const issueWeaverId = typeof issue.weaver === 'object' ? issue.weaver._id : issue.weaver;
        const matchesWeaver = issueWeaverId === selectedWeaverId;
        const hasMaterialIssued = weaverIssueNumbers.includes(issue.Br_issueNo);

        return matchesWeaver && hasMaterialIssued;
      }).map((issue: any) => {
        // Add challan count and other details
        const challanDetails = weaverMaterialIssues.filter(mi =>
          mi.issueNo && mi.issueNo.Br_issueNo === issue.Br_issueNo
        );

        return {
          ...issue,
          issueNoKey: issue._id,
          issueNoDisplay: issue.Br_issueNo,
          challanCount: challanDetails.length,
          challanDetails: challanDetails,
          carpetOrderIssue: issue
        };
      });

      console.log('✅ Available issues for weaver:', this.availableIssues);

      // Clear issue selection
      this.materialReceiveForm.patchValue({ issueNo: '' });
      this.selectedIssueData = null;
      this.dataSource.data = [];
    }
  }

  onIssueChange(): void {
    const selectedIssueNoKey = this.materialReceiveForm.get('issueNo')?.value;
    console.log('🔍 Issue selected:', selectedIssueNoKey);

    if (selectedIssueNoKey) {
      // Find the selected issue data using the key
      this.selectedIssueData = this.availableIssues.find((issue: any) =>
        issue.issueNoKey === selectedIssueNoKey
      );

      if (this.selectedIssueData) {
        console.log('✅ Selected issue data:', this.selectedIssueData);
        this.populateIssueData();
      }
    }
  }

  populateIssueData(): void {
    if (!this.selectedIssueData) return;

    const carpetOrderIssue = this.selectedIssueData.carpetOrderIssue;

    // Calculate area = areaInYard * pcs (from size object)
    let calculatedArea = 0;
    if (carpetOrderIssue.size && carpetOrderIssue.size.areaInYard && carpetOrderIssue.pcs) {
      calculatedArea = parseFloat(carpetOrderIssue.size.areaInYard) * parseInt(carpetOrderIssue.pcs);
    }

    // Format area display: "5.00 Yd "
   
    const areaDisplay = `${calculatedArea.toFixed(2)} Yd`;

    // Populate issue details table
    const issueDetails = [{
      issueNo: this.selectedIssueData.issueNoDisplay,
      quality: this.getDisplayText(carpetOrderIssue?.quality?.quality),
      design: this.getDisplayText(carpetOrderIssue?.design?.design),
      colour: this.getDisplayText(carpetOrderIssue?.borderColour),
      size: this.getDisplayText(carpetOrderIssue?.size?.sizeInYard),
      pcs: carpetOrderIssue?.pcs || 0,
      area: areaDisplay,
      calculatedArea: calculatedArea // Store for lagat calculations
    }];

    this.dataSource.data = issueDetails;

    // Populate material descriptions and lagats from materialLagat collection
    this.populateMaterialDescriptionsAndLagats(carpetOrderIssue, calculatedArea);

    // Populate material data from challan details
    if (this.selectedIssueData.challanDetails && this.selectedIssueData.challanDetails.length > 0) {
      // Aggregate material data from all challans for this issue
      const aggregatedMaterials = {
        kati: { description: '', lagat: 0, issue: 0 },
        tana: { description: '', lagat: 0, issue: 0 },
        soot: { description: '', lagat: 0, issue: 0 },
        thari: { description: '', lagat: 0, issue: 0 },
        silk: { description: '', lagat: 0, issue: 0 },
        other: { description: '', lagat: 0, issue: 0 }
      };

      this.selectedIssueData.challanDetails.forEach((challan: any) => {
        if (challan.materials) {
          const materialTypes = ['kati', 'tana', 'soot', 'thari', 'silk', 'other'];
          materialTypes.forEach(type => {
            if (challan.materials[type]) {
              const issueValue = parseFloat(challan.materials[type].issue || '0');
              (aggregatedMaterials as any)[type].issue += issueValue;

              if (!(aggregatedMaterials as any)[type].description && challan.materials[type].description) {
                (aggregatedMaterials as any)[type].description = this.getDisplayText(challan.materials[type].description);
              }
              if (!(aggregatedMaterials as any)[type].lagat && challan.materials[type].lagat) {
                (aggregatedMaterials as any)[type].lagat = parseFloat(challan.materials[type].lagat || '0');
              }
            }
          });
        }
      });

      // Populate form with aggregated material data (only issues and remaining, not descriptions/lagats)
      this.materialReceiveForm.patchValue({

        // Issues (with .000 format)
        katiIssue: this.formatToThreeDecimals(aggregatedMaterials.kati.issue),
        tanaIssue: this.formatToThreeDecimals(aggregatedMaterials.tana.issue),
        sootIssue: this.formatToThreeDecimals(aggregatedMaterials.soot.issue),
        thariIssue: this.formatToThreeDecimals(aggregatedMaterials.thari.issue),
        silkIssue: this.formatToThreeDecimals(aggregatedMaterials.silk.issue),
        otherIssue: this.formatToThreeDecimals(aggregatedMaterials.other.issue),

        // Remaining (initially empty, calculated when receive is entered)
        katiRemaining: '',
        tanaRemaining: '',
        sootRemaining: '',
        thariRemaining: '',
        silkRemaining: '',
        otherRemaining: ''
      });
    }
  }

  formatToThreeDecimals(value: any): string {
    if (!value || value === '' || value === null || value === undefined) {
      return '0.000';
    }
    const numValue = parseFloat(value.toString());
    if (isNaN(numValue)) {
      return '0.000';
    }
    return numValue.toFixed(3);
  }

  calculateLagat(lagat: number, area: number): string {
    if (!lagat || !area) return '0.000';
    const calculatedLagat = lagat * area;
    return calculatedLagat.toFixed(3);
  }

  getDisplayText(field: any): string {
    if (!field) return '';
    if (typeof field === 'string') return field;
    if (typeof field === 'object') {
      // For size object, show sizeInYard
      if (field.sizeInYard) {
        return field.sizeInYard;
      }
      return field.name || field.title || field.Description || field.description || '';
    }
    return '';
  }

  onSave(): void {
    if (this.materialReceiveForm.valid) {
      const formData = this.materialReceiveForm.value;
      console.log('💾 Saving material receive data:', formData);

      // Validate receive quantities don't exceed issue quantities
      const materialTypes = ['kati', 'tana', 'soot', 'thari', 'silk', 'other'];
      let validationError = false;

      for (const type of materialTypes) {
        const issueValue = formData[`${type}Issue`] || 0;
        const receiveValue = formData[`${type}Receive`] || 0;

        if (receiveValue > issueValue) {
          Swal.fire({
            title: 'Validation Error!',
            text: `${type.charAt(0).toUpperCase() + type.slice(1)} receive quantity cannot exceed issue quantity.`,
            icon: 'warning',
            confirmButtonText: 'OK'
          });
          validationError = true;
          break;
        }
      }

      if (!validationError) {
        // Prepare data for API
        const saveData = {
          ...formData,
          issueId: this.selectedIssueData?.carpetOrderIssue?._id
        };

        // For now, show success message
        // In production, call material receive API
        Swal.fire({
          title: 'Success!',
          text: this.isEditMode ? 'Material receive updated successfully!' : 'Material receive saved successfully!',
          icon: 'success',
          confirmButtonText: 'OK'
        });
      }
    } else {
      this.showValidationErrors();
    }
  }

  onClear(): void {
    this.materialReceiveForm.reset();
    this.initializeForm();
    this.selectedIssueData = null;
    this.dataSource.data = [];
    this.generateChallanNo();
  }

  showValidationErrors(): void {
    Swal.fire({
      title: 'Validation Error!',
      text: 'Please fill all required fields correctly.',
      icon: 'warning',
      confirmButtonText: 'OK'
    });
  }



 
  openChallanDetailsModal(): void {
    if (!this.selectedIssueData || !this.selectedIssueData.challanDetails) {
      console.warn('No challan details available');
      return;
    }

    // Import the ChallanModalComponent
    import('./modal/challan-modal/challan-modal.component').then(({ ChallanModalComponent }) => {
      const dialogRef = this.dialog.open(ChallanModalComponent, {
        width: '1200px',
        data: {
          issueNo: this.selectedIssueData.issueNoDisplay,
          challanDetails: this.selectedIssueData.challanDetails
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        console.log('Challan modal closed');
      });
    });
  }





  loadMaterialLagats(): void {
    console.log('🚀 Loading Material Lagats...');
    this.masterService.getsMaterialLagat().subscribe({
      next: (data: any) => {
        console.log('✅ Material Lagat Data:', data);
        this.materialLagatList = data;
      },
      error: (error: any) => {
        console.error('❌ Error loading material lagats:', error);
      }
    });
  }

  findMaterialLagatByQualityDesignColor(quality: any, design: any, color: any): any {
    if (!this.materialLagatList || this.materialLagatList.length === 0) {
      return null;
    }

    return this.materialLagatList.find((lagat: any) => {
      const qualityMatch = lagat.quality?._id === quality?._id || lagat.quality?._id === quality;
      const designMatch = lagat.AddDesign?._id === design?._id || lagat.AddDesign?._id === design;
      const colorMatch = lagat.Color?._id === color?._id || lagat.Color?._id === color;

      return qualityMatch && designMatch && colorMatch;
    });
  }

  populateMaterialDescriptionsAndLagats(carpetOrderIssue: any, calculatedArea: number): void {
    console.log('🔍 Populating material descriptions and lagats...');

    // Find matching material lagat record
    const materialLagat = this.findMaterialLagatByQualityDesignColor(
      carpetOrderIssue.quality,
      carpetOrderIssue.design,
      carpetOrderIssue.borderColour
    );

    if (materialLagat) {
      console.log('✅ Found matching material lagat:', materialLagat);

      // Populate descriptions and lagats
      this.materialReceiveForm.patchValue({
        // Descriptions (from materialLagat collection)
        katiDescription: this.getDisplayText(materialLagat.katiDescription),
        tanaDescription: this.getDisplayText(materialLagat.tanaDescription),
        sootDescription: this.getDisplayText(materialLagat.sootDescription),
        thariDescription: this.getDisplayText(materialLagat.tharriDescription),
        silkDescription: this.getDisplayText(materialLagat.silkDescription),
        otherDescription: materialLagat.item && materialLagat.item.length > 0 ?
          this.getDisplayText(materialLagat.item[0].description) : '',

        // Lagats (calculated as lagat * area)
        katiLagat: this.calculateLagat(parseFloat(materialLagat.katiLagat || '0'), calculatedArea),
        tanaLagat: this.calculateLagat(parseFloat(materialLagat.tanaLagat || '0'), calculatedArea),
        sootLagat: this.calculateLagat(parseFloat(materialLagat.sootLagat || '0'), calculatedArea),
        thariLagat: this.calculateLagat(parseFloat(materialLagat.tharriLagat || '0'), calculatedArea),
        silkLagat: this.calculateLagat(parseFloat(materialLagat.silkLagat || '0'), calculatedArea),
        otherLagat: materialLagat.item && materialLagat.item.length > 0 ?
          this.calculateLagat(parseFloat(materialLagat.item[0].lagat || '0'), calculatedArea) : '0.000'
      });
    } else {
      console.log('⚠️ No matching material lagat found for quality/design/color combination');
    }
  }

}
