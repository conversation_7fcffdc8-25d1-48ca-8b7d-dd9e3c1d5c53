/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},y={},g={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!g.call({1:2},1);y.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:g;var v,m,h=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,j=w.call,O=S&&w.bind.bind(j,j),P=function(t){return S?O(t):function(){return j.apply(t,arguments)}},T=P,k=T({}.toString),A=T("".slice),x=function(t){return A(k(t),8,-1)},E=x,C=P,F=function(t){if("Function"===E(t))return C(t)},M=a,R=x,I=Object,D=F("".split),L=M((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"==R(t)?D(t,""):I(t)}:I,z=function(t){return null==t},N=z,_=TypeError,H=function(t){if(N(t))throw _("Can't call method on "+t);return t},G=L,q=H,B=function(t){return G(q(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},K=W.all,J=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===K}:function(t){return"function"==typeof t},Q=J,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,Z=J,$=function(t){return Z(t)?t:void 0},tt=function(t,n){return arguments.length<2?$(Y[t]):Y[t]&&Y[t][n]},nt=F({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(m=(v=at.split("."))[0]>0&&v[0]<4?1:+(v[0]+v[1])),!m&&et&&(!(v=et.match(/Edge\/(\d+)/))||v[1]>=74)&&(v=et.match(/Chrome\/(\d+)/))&&(m=+v[1]);var ct=m,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,yt=tt,gt=J,bt=nt,dt=Object,vt=pt?function(t){return"symbol"==typeof t}:function(t){var n=yt("Symbol");return gt(n)&&bt(n.prototype,dt(t))},mt=String,ht=J,St=function(t){try{return mt(t)}catch(t){return"Object"}},wt=TypeError,jt=function(t){if(ht(t))return t;throw wt(St(t)+" is not a function")},Ot=z,Pt=p,Tt=J,kt=X,At=TypeError,xt={exports:{}},Et=i,Ct=Object.defineProperty,Ft=function(t,n){try{Ct(Et,t,{value:n,configurable:!0,writable:!0})}catch(r){Et[t]=n}return n},Mt=Ft,Rt="__core-js_shared__",It=i[Rt]||Mt(Rt,{}),Dt=It;(xt.exports=function(t,n){return Dt[t]||(Dt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Lt=H,zt=Object,Nt=function(t){return zt(Lt(t))},_t=Nt,Ht=F({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return Ht(_t(t),n)},qt=F,Bt=0,Ut=Math.random(),Wt=qt(1..toString),Kt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Bt+Ut,36)},Jt=i,Qt=xt.exports,Vt=Gt,Xt=Kt,Yt=st,Zt=pt,$t=Qt("wks"),tn=Jt.Symbol,nn=tn&&tn.for,rn=Zt?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt($t,t)||!Yt&&"string"!=typeof $t[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?$t[t]=tn[t]:$t[t]=Zt&&nn?nn(n):rn(n)}return $t[t]},on=p,un=X,an=vt,cn=function(t,n){var r=t[n];return Ot(r)?void 0:jt(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!kt(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!kt(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!kt(e=Pt(r,t)))return e;throw At("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},yn=vt,gn=function(t){var n=pn(t,"string");return yn(n)?n:n+""},bn=X,dn=i.document,vn=bn(dn)&&bn(dn.createElement),mn=function(t){return vn?dn.createElement(t):{}},hn=!c&&!a((function(){return 7!=Object.defineProperty(mn("div"),"a",{get:function(){return 7}}).a})),Sn=c,wn=p,jn=y,On=h,Pn=B,Tn=gn,kn=Gt,An=hn,xn=Object.getOwnPropertyDescriptor;u.f=Sn?xn:function(t,n){if(t=Pn(t),n=Tn(n),An)try{return xn(t,n)}catch(t){}if(kn(t,n))return On(!wn(jn.f,t,n),t[n])};var En={},Cn=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Fn=X,Mn=String,Rn=TypeError,In=function(t){if(Fn(t))return t;throw Rn(Mn(t)+" is not an object")},Dn=c,Ln=hn,zn=Cn,Nn=In,_n=gn,Hn=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Un="configurable",Wn="writable";En.f=Dn?zn?function(t,n,r){if(Nn(t),n=_n(n),Nn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(Nn(t),n=_n(n),Nn(r),Ln)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Hn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Kn=En,Jn=h,Qn=c?function(t,n,r){return Kn.f(t,n,Jn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=c,Yn=Gt,Zn=Function.prototype,$n=Xn&&Object.getOwnPropertyDescriptor,tr=Yn(Zn,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&$n(Zn,"name").configurable)},rr=J,er=It,or=F(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=J,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=xt.exports,yr=Kt,gr=pr("keys"),br={},dr=sr,vr=i,mr=X,hr=Qn,Sr=Gt,wr=It,jr=function(t){return gr[t]||(gr[t]=yr(t))},Or=br,Pr="Object already initialized",Tr=vr.TypeError,kr=vr.WeakMap;if(dr||wr.state){var Ar=wr.state||(wr.state=new kr);Ar.get=Ar.get,Ar.has=Ar.has,Ar.set=Ar.set,ir=function(t,n){if(Ar.has(t))throw Tr(Pr);return n.facade=t,Ar.set(t,n),n},ur=function(t){return Ar.get(t)||{}},ar=function(t){return Ar.has(t)}}else{var xr=jr("state");Or[xr]=!0,ir=function(t,n){if(Sr(t,xr))throw Tr(Pr);return n.facade=t,hr(t,xr,n),n},ur=function(t){return Sr(t,xr)?t[xr]:{}},ar=function(t){return Sr(t,xr)}}var Er={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!mr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Cr=a,Fr=J,Mr=Gt,Rr=c,Ir=nr.CONFIGURABLE,Dr=cr,Lr=Er.enforce,zr=Er.get,Nr=Object.defineProperty,_r=Rr&&!Cr((function(){return 8!==Nr((function(){}),"length",{value:8}).length})),Hr=String(String).split("String"),Gr=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Mr(t,"name")||Ir&&t.name!==n)&&(Rr?Nr(t,"name",{value:n,configurable:!0}):t.name=n),_r&&r&&Mr(r,"arity")&&t.length!==r.arity&&Nr(t,"length",{value:r.arity});try{r&&Mr(r,"constructor")&&r.constructor?Rr&&Nr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Lr(t);return Mr(e,"source")||(e.source=Hr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Fr(this)&&zr(this).source||Dr(this)}),"toString");var qr=J,Br=En,Ur=Vn.exports,Wr=Ft,Kr={},Jr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Jr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,Zr=Math.max,$r=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?Zr(r+n,0):$r(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=Gt,le=B,se=ce.indexOf,pe=br,ye=F([].push),ge=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ye(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ye(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Kr.f=Object.getOwnPropertyNames||function(t){return ge(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var ve=tt,me=Kr,he=de,Se=In,we=F([].concat),je=ve("Reflect","ownKeys")||function(t){var n=me.f(Se(t)),r=he.f;return r?we(n,r(t)):n},Oe=Gt,Pe=je,Te=u,ke=En,Ae=a,xe=J,Ee=/#|\.prototype\./,Ce=function(t,n){var r=Me[Fe(t)];return r==Ie||r!=Re&&(xe(n)?Ae(n):!!n)},Fe=Ce.normalize=function(t){return String(t).replace(Ee,".").toLowerCase()},Me=Ce.data={},Re=Ce.NATIVE="N",Ie=Ce.POLYFILL="P",De=Ce,Le=i,ze=u.f,Ne=Qn,_e=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},He=Ft,Ge=function(t,n,r){for(var e=Pe(n),o=ke.f,i=Te.f,u=0;u<e.length;u++){var a=e[u];Oe(t,a)||r&&Oe(r,a)||o(t,a,i(n,a))}},qe=De,Be=x,Ue=Array.isArray||function(t){return"Array"==Be(t)},We=TypeError,Ke=gn,Je=En,Qe=h,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=J,Ze=x,$e=en("toStringTag"),to=Object,no="Arguments"==Ze(function(){return arguments}()),ro=F,eo=a,oo=J,io=Xe?Ze:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),$e))?r:no?Ze(n):"Object"==(e=Ze(n))&&Ye(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),yo=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},go=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};go.sham=!0;var bo=!fo||eo((function(){var t;return yo(yo.call)||!yo(Object)||!yo((function(){t=!0}))||t}))?go:yo,vo=Ue,mo=bo,ho=X,So=en("species"),wo=Array,jo=function(t){var n;return vo(t)&&(n=t.constructor,(mo(n)&&(n===wo||vo(n.prototype))||ho(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},Oo=a,Po=ct,To=en("species"),ko=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?Le:f?Le[a]||He(a,{}):(Le[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=ze(r,e))&&u.value:r[e],!qe(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&Ne(i,"sham",!0),_e(r,e,i,t)}},Ao=a,xo=Ue,Eo=X,Co=Nt,Fo=ee,Mo=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Ro=function(t,n,r){var e=Ke(n);e in t?Je.f(t,e,Qe(0,r)):t[e]=r},Io=function(t,n){return new(jo(t))(0===n?0:n)},Do=function(t){return Po>=51||!Oo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=ct,zo=en("isConcatSpreadable"),No=Lo>=51||!Ao((function(){var t=[];return t[zo]=!1,t.concat()[0]!==t})),_o=Do("concat"),Ho=function(t){if(!Eo(t))return!1;var n=t[zo];return void 0!==n?!!n:xo(t)};ko({target:"Array",proto:!0,arity:1,forced:!No||!_o},{concat:function(t){var n,r,e,o,i,u=Co(this),a=Io(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(Ho(i=-1===n?u:arguments[n]))for(o=Fo(i),Mo(c+o),r=0;r<o;r++,c++)r in i&&Ro(a,c,i[r]);else Mo(c+1),Ro(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["sr-Latn-RS"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Molim sačekaj"},formatRecordsPerPage:function(t){return"".concat(t," redova po strani")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Prikazano ".concat(t,". - ").concat(n,". od ukupnog broja redova ").concat(r," (filtrirano od ").concat(e,")"):"Prikazano ".concat(t,". - ").concat(n,". od ukupnog broja redova ").concat(r)},formatSRPaginationPreText:function(){return"prethodna strana"},formatSRPaginationPageText:function(t){return"na stranu ".concat(t)},formatSRPaginationNextText:function(){return"sledeća strana"},formatDetailPagination:function(t){return"Prikazano ".concat(t," redova")},formatClearSearch:function(){return"Obriši pretragu"},formatSearch:function(){return"Pronađi"},formatNoMatches:function(){return"Nije pronađen ni jedan podatak"},formatPaginationSwitch:function(){return"Prikaži/sakrij paginaciju"},formatPaginationSwitchDown:function(){return"Prikaži paginaciju"},formatPaginationSwitchUp:function(){return"Sakrij paginaciju"},formatRefresh:function(){return"Osveži"},formatToggleOn:function(){return"Prikaži kartice"},formatToggleOff:function(){return"Sakrij kartice"},formatColumns:function(){return"Kolone"},formatColumnsToggleAll:function(){return"Prikaži/sakrij sve"},formatFullscreen:function(){return"Ceo ekran"},formatAllRows:function(){return"Sve"},formatAutoRefresh:function(){return"Automatsko osvežavanje"},formatExport:function(){return"Izvezi podatke"},formatJumpTo:function(){return"Idi"},formatAdvancedSearch:function(){return"Napredna pretraga"},formatAdvancedCloseButton:function(){return"Zatvori"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["sr-Latn-RS"])}));
