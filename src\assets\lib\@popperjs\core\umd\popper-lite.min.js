/**
 * @popperjs/core v2.11.6 - MIT License
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).Popper={})}(this,(function(e){"use strict";function t(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function n(e){return e instanceof t(e).Element||e instanceof Element}function o(e){return e instanceof t(e).HTMLElement||e instanceof HTMLElement}function r(e){return"undefined"!=typeof ShadowRoot&&(e instanceof t(e).ShadowRoot||e instanceof ShadowRoot)}var i=Math.max,a=Math.min,s=Math.round;function f(){var e=navigator.userAgentData;return null!=e&&e.brands?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function c(){return!/^((?!chrome|android).)*safari/i.test(f())}function u(e,r,i){void 0===r&&(r=!1),void 0===i&&(i=!1);var a=e.getBoundingClientRect(),f=1,u=1;r&&o(e)&&(f=e.offsetWidth>0&&s(a.width)/e.offsetWidth||1,u=e.offsetHeight>0&&s(a.height)/e.offsetHeight||1);var l=(n(e)?t(e):window).visualViewport,p=!c()&&i,d=(a.left+(p&&l?l.offsetLeft:0))/f,h=(a.top+(p&&l?l.offsetTop:0))/u,v=a.width/f,m=a.height/u;return{width:v,height:m,top:h,right:d+v,bottom:h+m,left:d,x:d,y:h}}function l(e){var n=t(e);return{scrollLeft:n.pageXOffset,scrollTop:n.pageYOffset}}function p(e){return e?(e.nodeName||"").toLowerCase():null}function d(e){return((n(e)?e.ownerDocument:e.document)||window.document).documentElement}function h(e){return u(d(e)).left+l(e).scrollLeft}function v(e){return t(e).getComputedStyle(e)}function m(e){var t=v(e),n=t.overflow,o=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+o)}function g(e,n,r){void 0===r&&(r=!1);var i,a,f=o(n),c=o(n)&&function(e){var t=e.getBoundingClientRect(),n=s(t.width)/e.offsetWidth||1,o=s(t.height)/e.offsetHeight||1;return 1!==n||1!==o}(n),v=d(n),g=u(e,c,r),y={scrollLeft:0,scrollTop:0},b={x:0,y:0};return(f||!f&&!r)&&(("body"!==p(n)||m(v))&&(y=(i=n)!==t(i)&&o(i)?{scrollLeft:(a=i).scrollLeft,scrollTop:a.scrollTop}:l(i)),o(n)?((b=u(n,!0)).x+=n.clientLeft,b.y+=n.clientTop):v&&(b.x=h(v))),{x:g.left+y.scrollLeft-b.x,y:g.top+y.scrollTop-b.y,width:g.width,height:g.height}}function y(e){return"html"===p(e)?e:e.assignedSlot||e.parentNode||(r(e)?e.host:null)||d(e)}function b(e){return["html","body","#document"].indexOf(p(e))>=0?e.ownerDocument.body:o(e)&&m(e)?e:b(y(e))}function w(e,n){var o;void 0===n&&(n=[]);var r=b(e),i=r===(null==(o=e.ownerDocument)?void 0:o.body),a=t(r),s=i?[a].concat(a.visualViewport||[],m(r)?r:[]):r,f=n.concat(s);return i?f:f.concat(w(y(s)))}function x(e){return["table","td","th"].indexOf(p(e))>=0}function O(e){return o(e)&&"fixed"!==v(e).position?e.offsetParent:null}function j(e){for(var n=t(e),i=O(e);i&&x(i)&&"static"===v(i).position;)i=O(i);return i&&("html"===p(i)||"body"===p(i)&&"static"===v(i).position)?n:i||function(e){var t=/firefox/i.test(f());if(/Trident/i.test(f())&&o(e)&&"fixed"===v(e).position)return null;var n=y(e);for(r(n)&&(n=n.host);o(n)&&["html","body"].indexOf(p(n))<0;){var i=v(n);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||t&&"filter"===i.willChange||t&&i.filter&&"none"!==i.filter)return n;n=n.parentNode}return null}(e)||n}var E="top",L="bottom",M="right",W="left",P=[E,L,M,W],T="end",H="viewport",D="popper",R=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function k(e){var t=new Map,n=new Set,o=[];function r(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var o=t.get(e);o&&r(o)}})),o.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||r(e)})),o}function S(e){return e.split("-")[0]}function A(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function C(e,o,r){return o===H?A(function(e,n){var o=t(e),r=d(e),i=o.visualViewport,a=r.clientWidth,s=r.clientHeight,f=0,u=0;if(i){a=i.width,s=i.height;var l=c();(l||!l&&"fixed"===n)&&(f=i.offsetLeft,u=i.offsetTop)}return{width:a,height:s,x:f+h(e),y:u}}(e,r)):n(o)?function(e,t){var n=u(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(o,r):A(function(e){var t,n=d(e),o=l(e),r=null==(t=e.ownerDocument)?void 0:t.body,a=i(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),s=i(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),f=-o.scrollLeft+h(e),c=-o.scrollTop;return"rtl"===v(r||n).direction&&(f+=i(n.clientWidth,r?r.clientWidth:0)-a),{width:a,height:s,x:f,y:c}}(d(e)))}function V(e){var t=w(y(e)),i=["absolute","fixed"].indexOf(v(e).position)>=0&&o(e)?j(e):e;return n(i)?t.filter((function(e){return n(e)&&function(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&r(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}(e,i)&&"body"!==p(e)})):[]}function N(e){return e.split("-")[1]}function B(e){var t,n=e.reference,o=e.element,r=e.placement,i=r?S(r):null,a=r?N(r):null,s=n.x+n.width/2-o.width/2,f=n.y+n.height/2-o.height/2;switch(i){case E:t={x:s,y:n.y-o.height};break;case L:t={x:s,y:n.y+n.height};break;case M:t={x:n.x+n.width,y:f};break;case W:t={x:n.x-o.width,y:f};break;default:t={x:n.x,y:n.y}}var c=i?function(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}(i):null;if(null!=c){var u="y"===c?"height":"width";switch(a){case"start":t[c]=t[c]-(n[u]/2-o[u]/2);break;case T:t[c]=t[c]+(n[u]/2-o[u]/2)}}return t}var F={placement:"bottom",modifiers:[],strategy:"absolute"};function U(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function q(e){void 0===e&&(e={});var t=e,o=t.defaultModifiers,r=void 0===o?[]:o,i=t.defaultOptions,a=void 0===i?F:i;return function(e,t,o){void 0===o&&(o=a);var i,s,f={placement:"bottom",orderedModifiers:[],options:Object.assign({},F,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},c=[],l=!1,p={state:f,setOptions:function(o){var i="function"==typeof o?o(f.options):o;d(),f.options=Object.assign({},a,f.options,i),f.scrollParents={reference:n(e)?w(e):e.contextElement?w(e.contextElement):[],popper:w(t)};var s,u,l=function(e){var t=k(e);return R.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((s=[].concat(r,f.options.modifiers),u=s.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(u).map((function(e){return u[e]}))));return f.orderedModifiers=l.filter((function(e){return e.enabled})),f.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,o=void 0===n?{}:n,r=e.effect;if("function"==typeof r){var i=r({state:f,name:t,instance:p,options:o}),a=function(){};c.push(i||a)}})),p.update()},forceUpdate:function(){if(!l){var e=f.elements,t=e.reference,n=e.popper;if(U(t,n)){var o,r,i,a;f.rects={reference:g(t,j(n),"fixed"===f.options.strategy),popper:(o=n,r=u(o),i=o.offsetWidth,a=o.offsetHeight,Math.abs(r.width-i)<=1&&(i=r.width),Math.abs(r.height-a)<=1&&(a=r.height),{x:o.offsetLeft,y:o.offsetTop,width:i,height:a})},f.reset=!1,f.placement=f.options.placement,f.orderedModifiers.forEach((function(e){return f.modifiersData[e.name]=Object.assign({},e.data)}));for(var s=0;s<f.orderedModifiers.length;s++)if(!0!==f.reset){var c=f.orderedModifiers[s],d=c.fn,h=c.options,v=void 0===h?{}:h,m=c.name;"function"==typeof d&&(f=d({state:f,options:v,name:m,instance:p})||f)}else f.reset=!1,s=-1}}},update:(i=function(){return new Promise((function(e){p.forceUpdate(),e(f)}))},function(){return s||(s=new Promise((function(e){Promise.resolve().then((function(){s=void 0,e(i())}))}))),s}),destroy:function(){d(),l=!0}};if(!U(e,t))return p;function d(){c.forEach((function(e){return e()})),c=[]}return p.setOptions(o).then((function(e){!l&&o.onFirstUpdate&&o.onFirstUpdate(e)})),p}}var z={passive:!0};var X={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Y(e){var n,o=e.popper,r=e.popperRect,i=e.placement,a=e.variation,f=e.offsets,c=e.position,u=e.gpuAcceleration,l=e.adaptive,p=e.roundOffsets,h=e.isFixed,m=f.x,g=void 0===m?0:m,y=f.y,b=void 0===y?0:y,w="function"==typeof p?p({x:g,y:b}):{x:g,y:b};g=w.x,b=w.y;var x=f.hasOwnProperty("x"),O=f.hasOwnProperty("y"),P=W,H=E,D=window;if(l){var R=j(o),k="clientHeight",S="clientWidth";if(R===t(o)&&"static"!==v(R=d(o)).position&&"absolute"===c&&(k="scrollHeight",S="scrollWidth"),R=R,i===E||(i===W||i===M)&&a===T)H=L,b-=(h&&R===D&&D.visualViewport?D.visualViewport.height:R[k])-r.height,b*=u?1:-1;if(i===W||(i===E||i===L)&&a===T)P=M,g-=(h&&R===D&&D.visualViewport?D.visualViewport.width:R[S])-r.width,g*=u?1:-1}var A,C=Object.assign({position:c},l&&X),V=!0===p?function(e){var t=e.x,n=e.y,o=window.devicePixelRatio||1;return{x:s(t*o)/o||0,y:s(n*o)/o||0}}({x:g,y:b}):{x:g,y:b};return g=V.x,b=V.y,u?Object.assign({},C,((A={})[H]=O?"0":"",A[P]=x?"0":"",A.transform=(D.devicePixelRatio||1)<=1?"translate("+g+"px, "+b+"px)":"translate3d("+g+"px, "+b+"px, 0)",A)):Object.assign({},C,((n={})[H]=O?b+"px":"",n[P]=x?g+"px":"",n.transform="",n))}var _=[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var n=e.state,o=e.instance,r=e.options,i=r.scroll,a=void 0===i||i,s=r.resize,f=void 0===s||s,c=t(n.elements.popper),u=[].concat(n.scrollParents.reference,n.scrollParents.popper);return a&&u.forEach((function(e){e.addEventListener("scroll",o.update,z)})),f&&c.addEventListener("resize",o.update,z),function(){a&&u.forEach((function(e){e.removeEventListener("scroll",o.update,z)})),f&&c.removeEventListener("resize",o.update,z)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=B({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,r=void 0===o||o,i=n.adaptive,a=void 0===i||i,s=n.roundOffsets,f=void 0===s||s,c={placement:S(t.placement),variation:N(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Y(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:f})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Y(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},i=t.elements[e];o(i)&&p(i)&&(Object.assign(i.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],i=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});o(r)&&p(r)&&(Object.assign(r.style,a),Object.keys(i).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]}],G=q({defaultModifiers:_});e.createPopper=G,e.defaultModifiers=_,e.detectOverflow=function(e,t){void 0===t&&(t={});var o,r=t,s=r.placement,f=void 0===s?e.placement:s,c=r.strategy,l=void 0===c?e.strategy:c,p=r.boundary,h=void 0===p?"clippingParents":p,v=r.rootBoundary,m=void 0===v?H:v,g=r.elementContext,y=void 0===g?D:g,b=r.altBoundary,w=void 0!==b&&b,x=r.padding,O=void 0===x?0:x,j=function(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}("number"!=typeof O?O:(o=O,P.reduce((function(e,t){return e[t]=o,e}),{}))),W=y===D?"reference":D,T=e.rects.popper,R=e.elements[w?W:y],k=function(e,t,n,o){var r="clippingParents"===t?V(e):[].concat(t),s=[].concat(r,[n]),f=s[0],c=s.reduce((function(t,n){var r=C(e,n,o);return t.top=i(r.top,t.top),t.right=a(r.right,t.right),t.bottom=a(r.bottom,t.bottom),t.left=i(r.left,t.left),t}),C(e,f,o));return c.width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c}(n(R)?R:R.contextElement||d(e.elements.popper),h,m,l),S=u(e.elements.reference),N=B({reference:S,element:T,strategy:"absolute",placement:f}),F=A(Object.assign({},T,N)),U=y===D?F:S,q={top:k.top-U.top+j.top,bottom:U.bottom-k.bottom+j.bottom,left:k.left-U.left+j.left,right:U.right-k.right+j.right},z=e.modifiersData.offset;if(y===D&&z){var X=z[f];Object.keys(q).forEach((function(e){var t=[M,L].indexOf(e)>=0?1:-1,n=[E,L].indexOf(e)>=0?"y":"x";q[e]+=X[n]*t}))}return q},e.popperGenerator=q,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=popper-lite.min.js.map
