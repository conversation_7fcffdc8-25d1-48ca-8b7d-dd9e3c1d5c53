const Addquality = require('../model/phase-2/quality');

class AddqualityRepository {
    async createQuality(qualityData) {
        try {
            return await Addquality.create(qualityData);
        } catch (error) {
            throw error;
        }
    }

    async getAllQualities() {
        try {
            return await Addquality.find();
        } catch (error) {
            throw error;
        }
    }

    async getQualityById(qualityId) {
        try {
            return await Addquality.findById(qualityId);
        } catch (error) {
            throw error;
        }
    }

    async updateQuality(qualityId, qualityData) {
        try {
            return await Addquality.findByIdAndUpdate(qualityId, qualityData, { new: true });
        } catch (error) {
            throw error;
        }
    }

    async deleteQuality(qualityId) {
        try {
            return await Addquality.findByIdAndDelete(qualityId);
        } catch (error) {
            throw error;
        }
    }
}

module.exports = AddqualityRepository;
