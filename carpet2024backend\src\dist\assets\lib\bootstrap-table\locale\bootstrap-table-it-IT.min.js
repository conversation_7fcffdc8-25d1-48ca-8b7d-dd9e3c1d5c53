/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!y.call({1:2},1);g.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:y;var m,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),C=T("".slice),E=function(t){return C(A(t),8,-1)},x=E,M=P,F=function(t){if("Function"===x(t))return M(t)},z=a,I=E,R=Object,D=F("".split),N=z((function(){return!R("z").propertyIsEnumerable(0)}))?function(t){return"String"==I(t)?D(t,""):R(t)}:R,L=function(t){return null==t},_=L,k=TypeError,H=function(t){if(_(t))throw k("Can't call method on "+t);return t},G=N,q=H,B=function(t){return G(q(t))},U="object"==typeof document&&document.all,V={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},W=V.all,J=V.IS_HTMLDDA?function(t){return"function"==typeof t||t===W}:function(t){return"function"==typeof t},K=J,Q=V.all,X=V.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:K(t)||t===Q}:function(t){return"object"==typeof t?null!==t:K(t)},Y=i,$=J,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=F({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(h=(m=at.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!h&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(h=+m[1]);var ct=h,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=J,bt=nt,dt=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&bt(n.prototype,dt(t))},ht=String,vt=J,St=function(t){try{return ht(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=L,Pt=p,Tt=J,At=X,Ct=TypeError,Et={exports:{}},xt=i,Mt=Object.defineProperty,Ft=function(t,n){try{Mt(xt,t,{value:n,configurable:!0,writable:!0})}catch(r){xt[t]=n}return n},zt=Ft,It="__core-js_shared__",Rt=i[It]||zt(It,{}),Dt=Rt;(Et.exports=function(t,n){return Dt[t]||(Dt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Nt=H,Lt=Object,_t=function(t){return Lt(Nt(t))},kt=_t,Ht=F({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return Ht(kt(t),n)},qt=F,Bt=0,Ut=Math.random(),Vt=qt(1..toString),Wt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Vt(++Bt+Ut,36)},Jt=i,Kt=Et.exports,Qt=Gt,Xt=Wt,Yt=st,$t=pt,Zt=Kt("wks"),tn=Jt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Qt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Qt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,an=mt,cn=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw Ct("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=mt,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},bn=X,dn=i.document,mn=bn(dn)&&bn(dn.createElement),hn=function(t){return mn?dn.createElement(t):{}},vn=!c&&!a((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),Sn=c,wn=p,On=g,jn=v,Pn=B,Tn=yn,An=Gt,Cn=vn,En=Object.getOwnPropertyDescriptor;u.f=Sn?En:function(t,n){if(t=Pn(t),n=Tn(n),Cn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var xn={},Mn=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Fn=X,zn=String,In=TypeError,Rn=function(t){if(Fn(t))return t;throw In(zn(t)+" is not an object")},Dn=c,Nn=vn,Ln=Mn,_n=Rn,kn=yn,Hn=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Un="configurable",Vn="writable";xn.f=Dn?Ln?function(t,n,r){if(_n(t),n=kn(n),_n(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Vn in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(_n(t),n=kn(n),_n(r),Nn)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Hn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Wn=xn,Jn=v,Kn=c?function(t,n,r){return Wn.f(t,n,Jn(1,r))}:function(t,n,r){return t[n]=r,t},Qn={exports:{}},Xn=c,Yn=Gt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=J,er=Rt,or=F(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=J,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,gr=Wt,yr=pr("keys"),br={},dr=sr,mr=i,hr=X,vr=Kn,Sr=Gt,wr=Rt,Or=function(t){return yr[t]||(yr[t]=gr(t))},jr=br,Pr="Object already initialized",Tr=mr.TypeError,Ar=mr.WeakMap;if(dr||wr.state){var Cr=wr.state||(wr.state=new Ar);Cr.get=Cr.get,Cr.has=Cr.has,Cr.set=Cr.set,ir=function(t,n){if(Cr.has(t))throw Tr(Pr);return n.facade=t,Cr.set(t,n),n},ur=function(t){return Cr.get(t)||{}},ar=function(t){return Cr.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(Sr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return Sr(t,Er)?t[Er]:{}},ar=function(t){return Sr(t,Er)}}var xr={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Mr=a,Fr=J,zr=Gt,Ir=c,Rr=nr.CONFIGURABLE,Dr=cr,Nr=xr.enforce,Lr=xr.get,_r=Object.defineProperty,kr=Ir&&!Mr((function(){return 8!==_r((function(){}),"length",{value:8}).length})),Hr=String(String).split("String"),Gr=Qn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!zr(t,"name")||Rr&&t.name!==n)&&(Ir?_r(t,"name",{value:n,configurable:!0}):t.name=n),kr&&r&&zr(r,"arity")&&t.length!==r.arity&&_r(t,"length",{value:r.arity});try{r&&zr(r,"constructor")&&r.constructor?Ir&&_r(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Nr(t);return zr(e,"source")||(e.source=Hr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Fr(this)&&Lr(this).source||Dr(this)}),"toString");var qr=J,Br=xn,Ur=Qn.exports,Vr=Ft,Wr={},Jr=Math.ceil,Kr=Math.floor,Qr=Math.trunc||function(t){var n=+t;return(n>0?Kr:Jr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Qr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=Gt,le=B,se=ce.indexOf,pe=br,ge=F([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Wr.f=Object.getOwnPropertyNames||function(t){return ye(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var me=tt,he=Wr,ve=de,Se=Rn,we=F([].concat),Oe=me("Reflect","ownKeys")||function(t){var n=he.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},je=Gt,Pe=Oe,Te=u,Ae=xn,Ce=a,Ee=J,xe=/#|\.prototype\./,Me=function(t,n){var r=ze[Fe(t)];return r==Re||r!=Ie&&(Ee(n)?Ce(n):!!n)},Fe=Me.normalize=function(t){return String(t).replace(xe,".").toLowerCase()},ze=Me.data={},Ie=Me.NATIVE="N",Re=Me.POLYFILL="P",De=Me,Ne=i,Le=u.f,_e=Kn,ke=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Vr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},He=Ft,Ge=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var a=e[u];je(t,a)||r&&je(r,a)||o(t,a,i(n,a))}},qe=De,Be=E,Ue=Array.isArray||function(t){return"Array"==Be(t)},Ve=TypeError,We=yn,Je=xn,Ke=v,Qe={};Qe[en("toStringTag")]="z";var Xe="[object z]"===String(Qe),Ye=J,$e=E,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=F,eo=a,oo=J,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),go=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,mo=Ue,ho=bo,vo=X,So=en("species"),wo=Array,Oo=function(t){var n;return mo(t)&&(n=t.constructor,(ho(n)&&(n===wo||mo(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=a,Po=ct,To=en("species"),Ao=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?Ne:f?Ne[a]||He(a,{}):(Ne[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Le(r,e))&&u.value:r[e],!qe(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&_e(i,"sham",!0),ke(r,e,i,t)}},Co=a,Eo=Ue,xo=X,Mo=_t,Fo=ee,zo=function(t){if(t>9007199254740991)throw Ve("Maximum allowed index exceeded");return t},Io=function(t,n,r){var e=We(n);e in t?Je.f(t,e,Ke(0,r)):t[e]=r},Ro=function(t,n){return new(Oo(t))(0===n?0:n)},Do=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},No=ct,Lo=en("isConcatSpreadable"),_o=No>=51||!Co((function(){var t=[];return t[Lo]=!1,t.concat()[0]!==t})),ko=Do("concat"),Ho=function(t){if(!xo(t))return!1;var n=t[Lo];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!_o||!ko},{concat:function(t){var n,r,e,o,i,u=Mo(this),a=Ro(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(Ho(i=-1===n?u:arguments[n]))for(o=Fo(i),zo(c+o),r=0;r<o;r++,c++)r in i&&Io(a,c,i[r]);else zo(c+1),Io(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["it-IT"]=r.default.fn.bootstrapTable.locales.it={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Caricamento in corso"},formatRecordsPerPage:function(t){return"".concat(t," elementi per pagina")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Visualizzazione da ".concat(t," a ").concat(n," di ").concat(r," elementi (filtrati da ").concat(e," elementi totali)"):"Visualizzazione da ".concat(t," a ").concat(n," di ").concat(r," elementi")},formatSRPaginationPreText:function(){return"pagina precedente"},formatSRPaginationPageText:function(t){return"alla pagina ".concat(t)},formatSRPaginationNextText:function(){return"pagina successiva"},formatDetailPagination:function(t){return"Mostrando ".concat(t," elementi")},formatClearSearch:function(){return"Pulisci filtri"},formatSearch:function(){return"Cerca"},formatNoMatches:function(){return"Nessun elemento trovato"},formatPaginationSwitch:function(){return"Nascondi/Mostra paginazione"},formatPaginationSwitchDown:function(){return"Mostra paginazione"},formatPaginationSwitchUp:function(){return"Nascondi paginazione"},formatRefresh:function(){return"Aggiorna"},formatToggleOn:function(){return"Mostra visuale a scheda"},formatToggleOff:function(){return"Nascondi visuale a scheda"},formatColumns:function(){return"Colonne"},formatColumnsToggleAll:function(){return"Mostra tutte"},formatFullscreen:function(){return"Schermo intero"},formatAllRows:function(){return"Tutto"},formatAutoRefresh:function(){return"Auto Aggiornamento"},formatExport:function(){return"Esporta dati"},formatJumpTo:function(){return"VAI"},formatAdvancedSearch:function(){return"Filtri avanzati"},formatAdvancedCloseButton:function(){return"Chiudi"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["it-IT"])}));
