const BuyerOrder = require("../../model/phase-4/buyerOrder");
const { ObjectId } = require("mongodb");

const CarpetOrderIsuue = require("../../model/phase-4/carpetOrderIssue");
class BuyerOrderRepository {
  async create(buyerOrder) {
    // Ensure fields are set properly for each item
    if (buyerOrder.items && buyerOrder.items.length > 0) {
      buyerOrder.items.forEach(item => {
        // pcs now represents total ordered pieces
        // PcsesPending initially equals pcs (all pieces are pending)
        if (!item.PcsesPending) {
          item.PcsesPending = item.pcs; // Initially all pieces are pending
        }
        if (!item.PcsesAssigned) {
          item.PcsesAssigned = 0; // Initially no pieces are assigned
        }
      });
    }
    return await BuyerOrder.create(buyerOrder);
  }

  async findById(id) {
    return await BuyerOrder.findById(id);
  }

  async findAll() {
    return await BuyerOrder.find()
      .populate("buyerName")
      .populate("items.quality")
      .populate("items.design")
      .populate("items.sizeId");
  }

  // async update(id, buyerOrder) {

  //   let flag =0;

  //   for (let i of buyerOrder.items) {

  //       // Use findOne to check for existence more efficiently
  //       let existItem = await BuyerOrder.findOne(
  //         { "items._id": i.itemId },
  //         {
  //           items: { $elemMatch: { _id: i.itemId } }
  //         }
  //       );

  //       // If existItem is found, set flag to 1
  //       if (existItem && existItem.items.length > 0) {
  //         console.log(existItem.items[0].pcs)
  //         if(existItem.items[0].pcs>i.pcs){
  //           flag = 1;
  //         break;
  //         }
  //         // flag = 1;
  //         // break; // Optionally break out of the loop if you only need to find one match
  //       }
  //     }

  //     if(flag!==1){
  //       return await BuyerOrder.findByIdAndUpdate(id, buyerOrder, { new: true });
  //     }else{
  //       return null
  //     }
  // }

  async update2(id, buyerOrder) {
    let flag = 0; // Initialize flag

    for (let i of buyerOrder.items) {
      try {
        // Check if the item exists in the database
        let existItem = await BuyerOrder.findOne(
          { _id: new ObjectId(id), "items._id": new ObjectId(i.itemId) },
          {
            items: { $elemMatch: { _id: i.itemId } },
          }
        );

        // If existItem is found and pcs is greater than the provided pcs in buyerOrder, set flag
        if (existItem && existItem.items.length > 0) {
          console.log(existItem.items[0].pcs);
          if (existItem.items[0].pcs > i.pcs) {
            flag = 1;
            break; // Exit loop if condition is met
          }
        }
      } catch (error) {
        console.error("Error fetching item:", error);
        throw error; // Consider throwing the error or handling it based on your application's needs
      }
    }

    // If flag is not set to 1, proceed with the update for each item
    if (flag !== 1) {
      try {
        for (let i of buyerOrder.items) {
          // Update each item in the array where conditions match
          const result = await BuyerOrder.updateOne(
            {
              _id: ObjectId(id), // Match by parent document _id
              "items._id": new ObjectId(i.itemId), // Match by item _id
            },
            {
              $set: {
                // Update fields in the specific item in the array
                "items.$.quality": i.quality, // Use the value from buyerOrder.items array
                "items.$.design": i.design,
                "items.$.groundColour": i.groundColour,
                "items.$.khapSize": i.khapSize,
                "items.$.pcs": i.pcs,
                "items.$.PcsesPending": i.PcsesPending ,
                "items.$.PcsesAssigned": i.PcsesAssigned || 0,
                "items.$.totalArea": i.totalArea,

                // Optionally update fields in the parent document (these are set once per document, not per item)
                buyerName: new ObjectId(buyerOrder.buyerName),
                orderNo: buyerOrder.orderNo,
                companyOrderNo: buyerOrder.companyOrderNo,
                orderDate: buyerOrder.orderDate,
                shippingDate: buyerOrder.shippingDate,
                orderType: buyerOrder.orderType,
              },
            }
          );

          console.log(`Item ${i.itemId} updated successfully`, result);
        }

        return "All applicable items updated successfully.";
      } catch (error) {
        console.error("Error updating document:", error);
        throw error; // Consider throwing the error or handling it
      }
    } else {
      // Return null or handle the case where the condition was not met
      return "No updates were made due to the pcs condition.";
    }
  }
  async delete(id) {
    let exist = await CarpetOrderIsuue.find({ buyerOrder: id });
    if (exist.length > 0) {
      return null;
    } else {
      return await BuyerOrder.findByIdAndDelete(id);
    }
  }

  async deleteItem(id) {
    let exist = await CarpetOrderIsuue.find({ itemId: id });
    if (exist.length > 0) {
      return null;
    } else {
      return await BuyerOrder.updateOne(
        { "items._id": id },
        { $pull: { items: { _id: id } } }
      );
    }
  }

  async updateField(id, field, value) {
    const update = {};
    update[field] = value;
    return await BuyerOrder.findByIdAndUpdate(id, update, { new: true });
  }

  async deleteField(id, field) {
    const update = { $unset: {} };
    update.$unset[field] = "";
    return await BuyerOrder.findByIdAndUpdate(id, update, { new: true });
  }

  async update(id, buyerOrder) {
    let flag = 0; // Initialize flag

    // Iterate over each item in the buyerOrder.items array
    for (let i of buyerOrder.items) {
      try {
        let isuueExist = await CarpetOrderIsuue.find({ itemId: i.itemId });
        let issuePcs = isuueExist.reduce(
          (cuu, acc) => cuu + parseInt(acc.pcs),
          0
        );
        console.log(issuePcs);

        if (issuePcs > parseInt(i.pcs) && i.isUpdatedItem === "updated") {
          console.log(`Existing pcs for item ${i.itemId}:`);
          flag = 1;
        }
      } catch (error) {
        console.error("Error fetching item:", error);
        throw error; // Consider throwing the error or handling it based on your application's needs
      }
    }

    // If flag is not set to 1, proceed with the update for each item
    if (flag !== 1) {
      try {
        // First, validate all items before making any updates
        for (let item of buyerOrder.items) {
          if (item.isUpdatedItem === "updated") {
            // Get current item data from database to check PcsesAssigned
            let currentItem = await BuyerOrder.findOne(
              { "items._id": new ObjectId(item.itemId) },
              { "items.$": 1 }
            );

            let currentPcsesAssigned = 0;
            if (currentItem && currentItem.items && currentItem.items.length > 0) {
              currentPcsesAssigned = currentItem.items[0].PcsesAssigned || 0;
            }

            // Check if new total pieces is less than already assigned pieces
            if (item.pcs < currentPcsesAssigned) {
              return {
                success: false,
                warning: true,
                message: `Cannot reduce total pieces to ${item.pcs}. Already ${currentPcsesAssigned} pieces have been assigned/issued. Please increase the total pieces or cancel some issued pieces first.`,
                currentPcsesAssigned: currentPcsesAssigned,
                requestedPcs: item.pcs
              };
            }
          }
        }

        const updatePromises = buyerOrder.items.map(async (item) => {
          let isuueExist = await CarpetOrderIsuue.find({ itemId: item.itemId });
          let issuePcs = isuueExist.reduce(
            (cuu, acc) => cuu + parseInt(acc.pcs),
            0
          );
          console.log(issuePcs);

          // Get current item data from database to preserve PcsesAssigned
          let currentItem = await BuyerOrder.findOne(
            { "items._id": new ObjectId(item.itemId) },
            { "items.$": 1 }
          );

          let currentPcsesAssigned = 0;
          if (currentItem && currentItem.items && currentItem.items.length > 0) {
            currentPcsesAssigned = currentItem.items[0].PcsesAssigned || 0;
          }

          // Calculate new PcsesPending: new total pcs - already assigned pcs
          let newPcsesPending = Math.max(0, item.pcs - currentPcsesAssigned);

          // Perform the update for each item in the items array
          if (item.isUpdatedItem === "new") {
            await BuyerOrder.updateOne(
              { _id: new ObjectId(id) }, // Match by parent document _id
              {
                $push: {
                  items: {
                    // _id: new ObjectId(item.itemId), // Provide a new ObjectId for the item
                    quality: new ObjectId(item.quality),
                    design: new ObjectId(item.design),
                    groundColour: item.groundColour,
                    khapSize: item.khapSize,
                    pcs: item.pcs,
                    PcsesPending: item.pcs, // For new items, all pieces are pending
                    PcsesAssigned: 0, // For new items, no pieces are assigned
                    sizeId: item.sizeId,
                    totalArea: item.totalArea,
                    // Add any other fields needed for a new item
                  },
                },
              }
            );
          }
          return await BuyerOrder.updateOne(
            {
              _id: new ObjectId(id), // Match by parent document _id
              "items._id": new ObjectId(item.itemId), // Match by item _id
            },
            {
              $set: {
                // Update fields in the specific item in the array
                "items.$.quality": new ObjectId(item.quality), // Use the value from buyerOrder.items array
                "items.$.design": new ObjectId(item.design),
                "items.$.groundColour": item.groundColour,
                "items.$.khapSize": item.khapSize,
                "items.$.sizeId": item.sizeId,
                "items.$.pcs": item.pcs,
                "items.$.PcsesPending": newPcsesPending, // Calculated pending pieces
                "items.$.PcsesAssigned": currentPcsesAssigned, // Preserve assigned pieces
                "items.$.totalArea": item.totalArea,
              },
            }
          );
        });

        // Execute all update operations in parallel
        await Promise.all(updatePromises);

        // Additionally update the parent document fields if needed
        const parentUpdateResult = await BuyerOrder.updateOne(
          { _id: new ObjectId(id) },
          {
            $set: {
              buyerName: new ObjectId(buyerOrder.buyerName), // Update buyerName
              orderNo: buyerOrder.orderNo, // Update orderNo
              companyOrderNo: buyerOrder.companyOrderNo, // Update companyOrderNo
              orderDate: buyerOrder.orderDate, // Update orderDate
              shippingDate: buyerOrder.shippingDate, // Update shippingDate
              orderType: buyerOrder.orderType,
              customerOrder: buyerOrder.customerOrder,
              weavingUnit: buyerOrder.weavingUnit,
              priority: buyerOrder.priority,
              area: buyerOrder.area, // Update orderType
            },
          }
        );

        return {
          success: true,
          parentUpdateResult,
          message: "All applicable items updated successfully.",
        };
      } catch (error) {
        console.error("Error updating document:", error);
        throw error; // Consider throwing the error or handling it
      }
    } else {
      // Return a message or handle the case where no updates were made due to the condition
      return {
        success: false,
        message: "No updates were made due to the pcs condition.",
      };
    }
  }
}

module.exports = new BuyerOrderRepository();
