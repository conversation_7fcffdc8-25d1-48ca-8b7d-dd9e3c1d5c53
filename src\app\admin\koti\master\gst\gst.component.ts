import { Component, OnInit, ViewChild } from '@angular/core';
import { ImporterService } from '../../../../services/importer.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import Swal from 'sweetalert2';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { CustomeServiceService } from '../../../../services/custome-service.service';
export interface Gst {
  id: string;
  index: number;
  toDate: string;
  fromDate: string;
  gstRate: string;
}

const ELEMENT_DATA: Gst[] = [];
@Component({
  selector: 'app-gst',
  templateUrl: './gst.component.html',
  styleUrl: './gst.component.css',
})
export class GstComponent implements OnInit {
  constructor(
    private _service: ImporterService,
    private _fb: FormBuilder,
    private customeService: CustomeServiceService
  ) {}
  dataSource = new MatTableDataSource(ELEMENT_DATA);
  displayedColumns = ['id', 'toDate', 'fromDate', 'gstRate', 'action'];
  frmGst!: FormGroup;
isUpdated:boolean=false;
isId!:string;
  ngOnInit(): void {
    this.frmGst = this._fb.group({
      toDate: [Validators.required],
      fromDate: [Validators.required],
      gstRate: ['', Validators.required],
    });
    this.getAllGst();
  }
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }
  addGst() {
    let frmData = this.frmGst.value;
    if (!this.frmGst.valid) {
      Swal.fire({
        title: 'Error!',
        text: 'Please fill all field!',
        icon: 'warning',
      });
      return;
    }
    this._service.createGst(frmData).subscribe(
      (resp: any) => {
        debugger;
        Swal.fire({
          title: 'Success!',
          text: 'Gst saved successfully',
          icon: 'success',
        });
        this.getAllGst();
      },
      (error: any) => {
        debugger;
        Swal.fire({
          title: 'Warning!',
          text: 'Failed, something went wrong!',
          icon: 'warning',
        });
      }
    );
  }

  getAllGst() {
    this._service.getAllGst().subscribe((resp: any) => {
      if (resp) {
        ELEMENT_DATA.length=0;
        resp.map((x: any, i: number) => {
          ELEMENT_DATA.push({
            id: x._id,
            index: i + 1,
            toDate: this.customeService.convertDate(x.toDate),
            fromDate: this.customeService.convertDate(x.fromDate),
            gstRate: x.gstRate,
          });
        });

        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.ngAfterViewInit();
        return;
      }
    });
  }

  editGst(id: string) {
    this._service.getGst(id).subscribe(
      (resp: any) => {
        this.isId=id;
        this.isUpdated=true;
        this.frmGst.patchValue(resp);
      },
      (err) => {
        Swal.fire({
          title: 'Warning!',
          text: 'Failed, something went wrong!',
          icon: 'warning',
        });
      }
    );
  }
  deleteGst(id: string) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        debugger
        this._service.deleteGst(id).subscribe(
          (resp: any) => {
            Swal.fire({
              title: 'Deleted!',
              text: 'Your gst has been deleted.',
              icon: 'success',
            });
            this.getAllGst();
          },
          (err) => {
            Swal.fire({
              title: 'Warning!',
              text: 'Failed, something went wrong!',
              icon: 'warning',
            });
          }
        );
      }
    });
  }

  updateGst(){
    let frmData = this.frmGst.value;
    if (!this.frmGst.valid ) {
      Swal.fire({
        title: 'Error!',
        text: 'Please fill all field!',
        icon: 'warning',
      });
      return;
    }
  if(this.isId!=null){
    this._service.updateGst(this.isId,frmData).subscribe(
      (resp: any) => {
        debugger;
        Swal.fire({
          title: 'Success!',
          text: 'Gst update successfully',
          icon: 'success',
        });
        this.getAllGst();
      },
      (error: any) => {
        debugger;
        Swal.fire({
          title: 'Warning!',
          text: 'Failed, something went wrong!',
          icon: 'warning',
        });
      }
    );
  }
  }
}
