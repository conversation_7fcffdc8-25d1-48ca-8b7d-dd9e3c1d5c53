const express = require('express');
const router = express.Router();

const billOfLadingRoute=require('./billOfLading-routes');
const sizeRoute=require('./size-routes');
const colorRoute=require('./color-routes');
const roleRoute=require('./role-routes');
const userRoute=require('./user-routes');
const paymentTermRoute=require('./paymentTerm-routes');
const invoiceRoute=require('./invoice-routes');
const impoterRoute=require('./impoter-invoice-routes');
const exlRoute = require("./exlRoute");
const exldata = require("./exlRoute");
const getAllData = require('./exlRoute');
const containerRoute = require('./container-routes');
const containerReceivedRoute =require('./container-rev-routes');

router.use('/billofladings',billOfLadingRoute);//done
router.use('/invoice',invoiceRoute);//done
router.use('/impoter',impoterRoute)
router.use('/size',sizeRoute); //done
router.use('/color',colorRoute); //done
router.use('/role',roleRoute); //done
router.use('/user',userRoute); //done
router.use('/paymentTerm',paymentTermRoute); //done
router.use("/exlupload",exlRoute);//done
router.use("/exldata",exldata);//done
router.use('/getAllData',getAllData);//done
router.use('/containerRcv',containerReceivedRoute);//done
router.use('/container',containerRoute);//done



module.exports = router;