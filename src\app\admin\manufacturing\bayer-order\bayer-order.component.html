<div class="container order mt-4">
  <section>
    <div class="row">
      <fieldset style="min-height: 200px">
        <legend><b>Buyer Order</b></legend>
        <div class="container">
          <form [formGroup]="frmOrder" (ngSubmit)="createOrder()">
          <div class="row">
            <div class="mb-2 col-md-4">
              <mat-form-field appearance="outline">
                <mat-label>Customer Name</mat-label>
                <mat-select (valueChange)="selectCustomer($event)" formControlName="buyerName">
                  @for (customer of customerList; track customer) {
                  <mat-option [value]="customer.id">{{
                    customer.name
                  }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
              <div class="errorDiv" *ngIf="frmOrder.get('buyerName')?.invalid && (frmOrder.get('buyerName')?.dirty || frmOrder.get('buyerName')?.touched)">
                <div *ngIf="frmOrder.get('buyerName')?.errors?.['required']">
                  <span class="errText">Customer Name is required.</span>
                </div>
                <div *ngIf="frmOrder.get('buyerName')?.errors?.['pattern']">

                  <span class="errText"> Something went wrong </span>
                </div>

              </div>
            </div>
            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Order No. </mat-label>
                <input matInput placeholder="Order No.  " formControlName="orderNo" />
              </mat-form-field>
              <div class="erroDiv" *ngIf="frmOrder.get('orderNo')?.invalid && (frmOrder.get('orderNo')?.dirty || frmOrder.get('orderNo')?.touched)">
                <div *ngIf="frmOrder.get('orderNo')?.errors?.['required']">
                 <span class="errText"> Order No is required.</span>
                </div>
              </div>
            </div>
            <div class="mb-2 col-sm-2">
              <mat-form-field class="full-width" appearance="outline" >
                <mat-label>Order Date</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="orderDate"/>

                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="picker"
                ></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
              </mat-form-field>
            </div>
            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Company Order No.</mat-label>

                <input matInput placeholder=" readonly Company Order No." formControlName="companyOrderNo" />
              </mat-form-field>
              <div class="erroDiv" *ngIf="frmOrder.get('companyOrderNo')?.invalid && (frmOrder.get('companyOrderNo')?.dirty || frmOrder.get('companyOrderNo')?.touched)">
                <div *ngIf="frmOrder.get('companyOrderNo')?.errors?.['required']">
                 <span class="errText"> Company Order No is required.</span>
                </div>
              </div>
            </div>


            <div class="mb-2 col-md-2">
              <mat-form-field class="full-width" appearance="outline">
                <mat-label>Shipping Date</mat-label>
                <input matInput [matDatepicker]="picker1"  formControlName="shippingDate"/>

                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="picker1"
                ></mat-datepicker-toggle>
                <mat-datepicker #picker1></mat-datepicker>
              </mat-form-field>
            </div>

            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline">
                <mat-label>Order Type</mat-label>
                <mat-select formControlName="orderType">
                  @for (order of orderTypeList; track order) {
                  <mat-option [value]="order">{{ order }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
              <div class="erroDiv" *ngIf="frmOrder.get('orderType')?.invalid && (frmOrder.get('orderType')?.dirty || frmOrder.get('orderType')?.touched)">
                <div *ngIf="frmOrder.get('orderType')?.errors?.['required']">
                 <span class="errText"> Order Type is required.</span>
                </div>
              </div>
            </div>

            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline">
                <mat-label>Customer Order Unit</mat-label>
                <mat-select formControlName="customerOrder">
                  @for (custUnit of customerOrderUnitList; track custUnit) {
                  <mat-option [value]="custUnit">{{ custUnit }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
              <div class="erroDiv" *ngIf="frmOrder.get('customerOrder')?.invalid && (frmOrder.get('customerOrder')?.dirty || frmOrder.get('customerOrder')?.touched)">
                <div *ngIf="frmOrder.get('customerOrder')?.errors?.['required']">
                 <span class="errText">Customer Order No is required.</span>
                </div>
              </div>
            </div>

            <!-- <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Weaving Unit</mat-label>
                <mat-select formControlName="weavingUnit">
                  @for (weaving of weavingUnitList; track weaving) {
                  <mat-option [value]="weaving">{{ weaving }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
              <div class="erroDiv" *ngIf="frmOrder.get('weavingUnit')?.invalid && (frmOrder.get('weavingUnit')?.dirty || frmOrder.get('weavingUnit')?.touched)">
                <div *ngIf="frmOrder.get('weavingUnit')?.errors?.['required']">
                 <span class="errText"> Weaving Unit is required.</span>
                </div>
              </div>
            </div> -->

            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline">
                <mat-label>Priority</mat-label>
                <mat-select formControlName="priority">
                  @for (pririty of priorityList; track pririty) {
                  <mat-option [value]="pririty">{{ pririty }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
              <div class="erroDiv" *ngIf="frmOrder.get('priority')?.invalid && (frmOrder.get('priority')?.dirty || frmOrder.get('priority')?.touched)">
                <div *ngIf="frmOrder.get('priority')?.errors?.['required']">
                 <span class="errText"> Priority is required.</span>
                </div>
              </div>
            </div>

            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Area</mat-label>
                <input matInput placeholder="  Total Area" readonly formControlName="area" />
              </mat-form-field>
              <div class="erroDiv" *ngIf="frmOrder.get('area')?.invalid && (frmOrder.get('area')?.dirty || frmOrder.get('area')?.touched)">
                <div *ngIf="frmOrder.get('area')?.errors?.['required']">
                 <span class="errText"> Area is required.</span>
                </div>
              </div>
            </div>

            <div class="mt-2 col-md-2">
              <div class="example-button-row">
                <button mat-flat-button color="primary" *ngIf="!isUpdated">Save</button>
                 &nbsp; &nbsp;
                 <button mat-flat-button color="primary"  type="button" (click)="updateBuyerOrder()" *ngIf="isUpdated" >Update</button>
                 &nbsp; &nbsp;
                <!-- <button mat-flat-button color="primary">Map Transfar</button> -->
              </div>
            </div>

          </div>
          </form>
        </div>
      </fieldset>
    </div>
  </section>
</div>


<div class="container mt-4">


  <div class="row">
    <fieldset >
      <legend><b>Add New Order</b></legend>
      <div class="container">
        <form [formGroup]="frmOrderItem" (ngSubmit)="addItems()">
          <div class="row">
            <div class="mb-2 col-md-3">


              <mat-form-field appearance="outline">
                <mat-label>Quality</mat-label>
                <mat-select
                  (valueChange)="selectQuality($event)"
                  formControlName="quality"
                  [disabled]="isUpdatedOrderList && isItemFieldsReadonly(orderList[idUpdatedOrderList])"
                >
                  @for (quality of qualityList; track quality) {
                  <mat-option [value]="quality.id">{{
                    quality.quality
                  }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
              <div class="erroDiv" *ngIf="frmOrderItem.get('quality')?.invalid && (frmOrderItem.get('quality')?.dirty || frmOrderItem.get('quality')?.touched)">
                <div *ngIf="frmOrderItem.get('quality')?.errors?.['required']">
                 <span class="errText"> Quality is required.</span>
                </div>
              </div>
            </div>

            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Design </mat-label>
                <input
                  matInput
                  placeholder=" Design"
                  readonly
                  formControlName="design"
                  [disabled]="isUpdatedOrderList && isItemFieldsReadonly(orderList[idUpdatedOrderList])"
                />
              </mat-form-field>
            </div>
            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Ground Color</mat-label>
                <input
                  matInput
                  placeholder="Ground "
                  readonly
                  formControlName="ground"
                  [disabled]="isUpdatedOrderList && isItemFieldsReadonly(orderList[idUpdatedOrderList])"
                />
              </mat-form-field>
            </div>

            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Size</mat-label>
                <mat-select
                  formControlName="size"
                  (valueChange)="selectSize($event)"
                  [disabled]="isUpdatedOrderList && isItemFieldsReadonly(orderList[idUpdatedOrderList])"
                >
                  @for (size of sizeList; track size) {
                  <mat-option [value]="size.id">{{ size.size }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
              <div class="erroDiv" *ngIf="frmOrderItem.get('size')?.invalid && (frmOrderItem.get('size')?.dirty || frmOrderItem.get('size')?.touched)">
                <div *ngIf="frmOrderItem.get('size')?.errors?.['required']">
                 <span class="errText"> Size is required.</span>
                </div>
              </div>
            </div>

            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Khap Size</mat-label>
                <input
                  matInput
                  readonly
                  placeholder="Khap Size "
                  formControlName="khapSize"
                  [disabled]="isUpdatedOrderList && isItemFieldsReadonly(orderList[idUpdatedOrderList])"
                />
              </mat-form-field>
            </div>

            <div class="mb-2 col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Type</mat-label>
                <mat-select  formControlName="type" [disabled]="isUpdatedOrderList && isItemFieldsReadonly(orderList[idUpdatedOrderList])">
                  <mat-option value="Normal ( NR )">Normal ( NR )</mat-option>
                  <mat-option value="Round(RD)">Round(RD)</mat-option>
                  <mat-option value="Ovel(OV)">Ovel(OV)</mat-option>
                  <mat-option value="Octagent(OT)">Octagent(OT)</mat-option>
                </mat-select>
              </mat-form-field>
              <div class="erroDiv" *ngIf="frmOrderItem.get('type')?.invalid && (frmOrderItem.get('type')?.dirty || frmOrderItem.get('type')?.touched)">
                <div *ngIf="frmOrderItem.get('type')?.errors?.['required']">
                 <span class="errText"> Type is required.</span>
                </div>
              </div>
            </div>
            <div class="mb-2 col-md-1">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Pcs</mat-label>
                <input
                  matInput
                  formControlName="pcs"
(blur)="calcArea()"
                />
              </mat-form-field>
            </div>
            <div class="mb-2 col-md-2">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label> Area</mat-label>
                <input
                  matInput
                  placeholder=" Area "
                  formControlName="totalArea" readonly
                />
              </mat-form-field>
            </div>

            <div class="col-lg-3" *ngIf="!isUpdatedOrderList">
              <button mat-raised-button color="primary" type="submit">Save & Next</button>
            </div>
            <div class="col-lg-3"*ngIf="isUpdatedOrderList">
              <button mat-raised-button color="primary" type="button" (click)="updateOrderList()">Update & Next</button>
            </div>
          </div>
        </form>
      </div>
    </fieldset>
  </div>
</div>

<section class="mt-5" style="margin: 10px ">
  <fieldset style="min-height: 100px">
    <legend><b> Order List</b></legend>


<!-- editable-table.component.html -->
<div class="row">
  <div class="col-md-12">
    <table class="table">
      <thead>
        <tr class="">
          <th>Sr No.</th>
          <th class="">Quality</th>
          <th class="">Design</th>
          <th class="">Groun/Border</th>
          <th class="">Size</th>
          <th class=" ">Khap Size</th>
          <!-- <th class="col-md-2">Type</th> -->
          <th class=" ">Pcs.</th>
          <th class=" ">Area</th>
          <th class=" ">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr class="" *ngFor="let order of orderList; let i = index">
          <td> {{i+1}}</td>
          <td class=" "  *ngIf="editMode && editedRowIndex === i">

            <!-- <mat-form-field appearance="outline">
              <mat-label>Quality</mat-label>
              <input matInput [(ngModel)]="order.quality" />
            </mat-form-field> -->
             {{   order.quality }}

          </td>
          <td class=" " *ngIf="!editMode || (editMode && editedRowIndex !== i)">  &nbsp;{{order.quality }}</td>
          <td class=" " *ngIf="editMode && editedRowIndex === i">
            <!-- <input type="text" [(ngModel)]="order.design" /> -->
            <!-- <mat-form-field appearance="outline">
              <mat-label>Design</mat-label>
              <input matInput [(ngModel)]="order.design" />
            </mat-form-field> -->
            {{ order.design}}

          </td>
          <td class=" " *ngIf="!editMode || (editMode && editedRowIndex !== i)">{{ order.design }}</td>

          <td class="col-md-3" *ngIf="editMode && editedRowIndex === i">
            <!-- <input type="text" [(ngModel)]="order.colour" /> -->
            <!-- <mat-form-field appearance="outline">
              <mat-label>Colour</mat-label>
              <input matInput [(ngModel)]="order.ground" />
            </mat-form-field> -->
            {{ order.ground }}
          </td>
          <td class=" " *ngIf="!editMode || (editMode && editedRowIndex !== i)">{{ order.ground }}</td>
          
          <td class=" " *ngIf="editMode && editedRowIndex === i">
            <!-- <input type="text" [(ngModel)]="order.size" /> -->
            <!-- <mat-form-field appearance="outline">
              <mat-label>Size</mat-label>
              <input matInput [(ngModel)]="order.size" />
            </mat-form-field> -->
            {{ order.size }}
          </td>
          <td class=" " *ngIf="!editMode || (editMode && editedRowIndex !== i)">{{ order.size }}</td>

          <td class="  " *ngIf="editMode && editedRowIndex === i">

            <!-- <input type="text" [(ngModel)]="order.khapSize" /> -->
            <!-- <mat-form-field appearance="outline">
              <mat-label>Khap Size</mat-label>
              <input matInput [(ngModel)]="order.khapSize" />
            </mat-form-field> -->
            {{ order.khapSize }}
          </td>
          <td class="" *ngIf="!editMode || (editMode && editedRowIndex !== i)">{{ order.khapSize }}</td>
          <!-- <td class="col-md-2 " *ngIf="editMode && editedRowIndex === i">

            {{ order.type }}
          </td>
          <td class="col-md-2 " *ngIf="!editMode || (editMode && editedRowIndex !== i)">{{ order.type }}</td> -->

          <td class="" *ngIf="editMode && editedRowIndex === i">
            <!-- <mat-form-field appearance="outline">
              <mat-label>Pcs</mat-label>
              <input matInput [(ngModel)]="order.pcs" />
            </mat-form-field> -->
            <!-- <input type="text" [(ngModel)]="order.psc" /> -->

            {{ order.pcs }}
          </td>
          <td class="" *ngIf="!editMode || (editMode && editedRowIndex !== i)">{{ order.pcs }}</td>

          <td class="" *ngIf="editMode && editedRowIndex === i">
            <!-- <mat-form-field appearance="outline">
              <mat-label>Area</mat-label>
              <input matInput [(ngModel)]="order.area" />
            </mat-form-field> -->
             <!-- <input type="text" [(ngModel)]="order.area" /> -->

             {{ order.totalArea }}
            </td>
          <td class="" *ngIf="!editMode || (editMode && editedRowIndex !== i)">{{ order.totalArea }}</td>

          <td class="">


           <a (click)="saveChanges(i)" class="primary "><i class="fa fa-edit"></i></a>&nbsp;
           <a (click)="delete(i)"><i class="fa fa-times" aria-hidden="true"></i></a>&nbsp;
           <a (click)="deleteBuyerOrder(i)" >
            <i class="fa fa-trash-o  fa-trash" aria-hidden="true"></i></a>
    <!-- <a class="primary" (click)="toggleEditMode(i)">
      <i class="fa" [ngClass]="{'fa-edit': !editMode || editedRowIndex !== i, 'fa-times': editMode && editedRowIndex === i}"></i>
      {{ editMode && editedRowIndex === i ? 'Cancel' : 'Edit' }}
    </a>


            &nbsp;

            <a  *ngIf="editMode && editedRowIndex === i" (click)="saveChanges(i)">
              <i class="fa fa-floppy-o" aria-hidden="true"></i> Save
            </a> -->
          </td>
        </tr>
      </tbody>
    </table>
    </div>
</div>

</fieldset>
</section>
<!-- <div class="container mt-4">
  <section>
    <fieldset>
      <legend><b> Order List</b></legend>
      <div class="row">
        <div class="col-md-12">
          <mat-form-field appearance="outline" class="exwidth">
            <mat-label>Search</mat-label>
            <input
              matInput
              (keyup)="applyFilter($event)"
              placeholder="Ex. Jack"
              #input
            />
          </mat-form-field>

          <div class="mat-elevation-z8">
            <table mat-table [dataSource]="dataSource" matSort>

              <ng-container matColumnDef="SrNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Sr. No.
                </th>
                <td mat-cell *matCellDef="let row">{{ row.SrNo }}</td>
              </ng-container>

              <ng-container matColumnDef="Quality">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Quality
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Quality }}</td>
              </ng-container>

              <ng-container matColumnDef="Design">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Design
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Design }}</td>
              </ng-container>


              <ng-container matColumnDef="GroundBorder">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Ground/Border
                </th>
                <td mat-cell *matCellDef="let row">{{ row.GroundBorder }}</td>
              </ng-container>

              <ng-container matColumnDef="size">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>size</th>
                <td mat-cell *matCellDef="let row">{{ row.size }}</td>
              </ng-container>


              <ng-container matColumnDef="KhapSize">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Khap Size
                </th>
                <td mat-cell *matCellDef="let row">{{ row.KhapSize }}</td>
              </ng-container>

              <ng-container matColumnDef="Pcs">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>pcs.</th>
                <td mat-cell *matCellDef="let row">{{ row.Pcs }}</td>
              </ng-container>

              <ng-container matColumnDef="Area">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Area</th>
                <td mat-cell *matCellDef="let row">{{ row.Area }}</td>
              </ng-container>

              <ng-container matColumnDef="Action">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Action
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Action }}</td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>


              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="4">
                  No data matching the filter "{{ input.value }}"
                </td>
              </tr>
            </table>

            <mat-paginator
              [pageSizeOptions]="[5, 10, 25, 100]"
              aria-label="Select page of users"
            ></mat-paginator>
          </div>
        </div>
      </div>
    </fieldset>
  </section>
</div> -->
