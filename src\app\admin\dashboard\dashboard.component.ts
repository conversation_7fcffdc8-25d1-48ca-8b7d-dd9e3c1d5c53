import { Component, OnInit, ViewChild } from '@angular/core';
import { Chart } from 'chart.js'
declare var google: any;
@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css',
})
export class DashboardComponent implements OnInit {
  constructor() {}
  // canvas: any;
  // ctx: any;
  // @ViewChild('mychart') mychart: any;
  ngOnInit(): void {
    // this.loadGoogleCharts();
  }

  // loadGoogleCharts() {
  //   google.charts.load('current', { packages: ['corechart'] });
  //   google.charts.setOnLoadCallback(this.drawChart);
  // }

  // drawChart() {
  //  var data = google.visualization.arrayToDataTable([
  //        ['Element', 'Density', { role: 'style' }],
  //        ['Copper', 8.94, '#b87333'],            // RGB value
  //        ['Silver', 10.49, 'silver'],            // English color name
  //        ['Gold', 19.30, 'gold'],

  //      ['Platinum', 21.45, 'color: #e5e4e2' ], // CSS-style declaration
  //     ]);
  //     var options = {
  //       title: 'My Daily Activities',
  //       legend: { position: 'top' },  // Set the legend position to top
  //     };
  //    var chart = new google.visualization.PieChart(
  //     document.getElementById('piechart')
  //   );

  //   chart.draw(data,options);
  // }

  designations = [
    { name: 'Web Developer', salary: 895000 },
    { name: 'Web Designer', salary: 80000 },
    { name: 'Graphic Designer', salary: 5000 },
    { name: 'O level Teacher', salary: 7000 },
    { name: 'B Level Teacher', salary: 8800 },
    { name: 'Java Developer', salary: 58000 }
  ];


  // chart js
  ngAfterViewInit() {
    // this.canvas = this.mychart.nativeElement;
    // this.ctx = this.canvas.getContext('2d');

    // let myChart = new Chart(this.ctx, {
    //   type: 'line',

    //   data: {
    //     datasets: [{
    //       label: 'Höhenlinie',
    //       backgroundColor: "rgba(255, 99, 132,0.4)",
    //       borderColor: "rgb(255, 99, 132)",
    //       fill: true,
    //       data: [
    //         { x: 1, y: 2 },
    //         { x: 2500, y: 2.5 },
    //         { x: 3000, y: 5 },
    //         { x: 3400, y: 4.75 },
    //         { x: 3600, y: 4.75 },
    //         { x: 5200, y: 6 },
    //         { x: 6000, y: 9 },
    //         { x: 7100, y: 6 },
    //       ],
    //     }]
    //   },
    //   options: {
    //     responsive: true,
    //     title: {
    //       display: true,
    //       text: 'Höhenlinie'
    //     },
    //     scales: {
    //       xAxes: [{
    //         type: 'linear',
    //         position: 'bottom',
    //         ticks: {
    //           userCallback: function (tick: any) {
    //             if (tick >= 1000) {
    //               return (tick / 1000).toString() + 'km';
    //             }
    //             return tick.toString() + 'm';
    //           }
    //         },
    //         scaleLabel: {
    //           labelString: 'Länge',
    //           display: true,
    //         }
    //       }],
    //       yAxes: [{
    //         type: 'linear',
    //         ticks: {
    //           userCallback: function (tick:any) {
    //             return tick.toString() + 'm';
    //           }
    //         },
    //         scaleLabel: {
    //           labelString: 'Höhe',
    //           display: true
    //         }
    //       }]
    //     }
    //   }
    // });
  }
}
