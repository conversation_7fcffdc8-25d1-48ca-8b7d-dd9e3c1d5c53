const Branch = require('../model/phase-3/manageBranch');

class BranchRepository {
  async create(branchData) {
    const branch = new Branch(branchData);
    return await branch.save();
  }

  async findAll() {
    return await Branch.find();
  }

  async findById(id) {
    return await Branch.findById(id);
  }

  async update(id, branchData) {
    return await Branch.findByIdAndUpdate(id, branchData, { new: true });
  }

  async delete(id) {
    return await Branch.findByIdAndDelete(id);
  }
}

module.exports = new BranchRepository();
