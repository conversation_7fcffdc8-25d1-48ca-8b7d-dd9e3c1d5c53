/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,d=Object.getOwnPropertyDescriptor,b=d&&!y.call({1:2},1);g.f=b?function(t){var n=d(this,t);return!!n&&n.enumerable}:y;var m,v,h=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),M=T("".slice),C=function(t){return M(A(t),8,-1)},x=C,E=P,F=function(t){if("Function"===x(t))return E(t)},R=a,I=C,D=Object,L=F("".split),N=R((function(){return!D("z").propertyIsEnumerable(0)}))?function(t){return"String"==I(t)?L(t,""):D(t)}:D,z=function(t){return null==t},_=z,k=TypeError,q=function(t){if(_(t))throw k("Can't call method on "+t);return t},B=N,G=q,H=function(t){return B(G(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},J=W.all,K=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===J}:function(t){return"function"==typeof t},Q=K,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,$=K,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=F({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(v=(m=at.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!v&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(v=+m[1]);var ct=v,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=K,dt=nt,bt=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&dt(n.prototype,bt(t))},vt=String,ht=K,St=function(t){try{return vt(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(ht(t))return t;throw wt(St(t)+" is not a function")},jt=z,Pt=p,Tt=K,At=X,Mt=TypeError,Ct={exports:{}},xt=i,Et=Object.defineProperty,Ft=function(t,n){try{Et(xt,t,{value:n,configurable:!0,writable:!0})}catch(r){xt[t]=n}return n},Rt=Ft,It="__core-js_shared__",Dt=i[It]||Rt(It,{}),Lt=Dt;(Ct.exports=function(t,n){return Lt[t]||(Lt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Nt=q,zt=Object,_t=function(t){return zt(Nt(t))},kt=_t,qt=F({}.hasOwnProperty),Bt=Object.hasOwn||function(t,n){return qt(kt(t),n)},Gt=F,Ht=0,Ut=Math.random(),Wt=Gt(1..toString),Jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Ht+Ut,36)},Kt=i,Qt=Ct.exports,Vt=Bt,Xt=Jt,Yt=st,$t=pt,Zt=Qt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,an=mt,cn=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw Mt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=mt,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},dn=X,bn=i.document,mn=dn(bn)&&dn(bn.createElement),vn=function(t){return mn?bn.createElement(t):{}},hn=!c&&!a((function(){return 7!=Object.defineProperty(vn("div"),"a",{get:function(){return 7}}).a})),Sn=c,wn=p,On=g,jn=h,Pn=H,Tn=yn,An=Bt,Mn=hn,Cn=Object.getOwnPropertyDescriptor;u.f=Sn?Cn:function(t,n){if(t=Pn(t),n=Tn(n),Mn)try{return Cn(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var xn={},En=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Fn=X,Rn=String,In=TypeError,Dn=function(t){if(Fn(t))return t;throw In(Rn(t)+" is not an object")},Ln=c,Nn=hn,zn=En,_n=Dn,kn=yn,qn=TypeError,Bn=Object.defineProperty,Gn=Object.getOwnPropertyDescriptor,Hn="enumerable",Un="configurable",Wn="writable";xn.f=Ln?zn?function(t,n,r){if(_n(t),n=kn(n),_n(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=Gn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Hn in r?r.enumerable:e.enumerable,writable:!1})}return Bn(t,n,r)}:Bn:function(t,n,r){if(_n(t),n=kn(n),_n(r),Nn)try{return Bn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw qn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Jn=xn,Kn=h,Qn=c?function(t,n,r){return Jn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=c,Yn=Bt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=K,er=Dt,or=F(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Ct.exports,gr=Jt,yr=pr("keys"),dr={},br=sr,mr=i,vr=X,hr=Qn,Sr=Bt,wr=Dt,Or=function(t){return yr[t]||(yr[t]=gr(t))},jr=dr,Pr="Object already initialized",Tr=mr.TypeError,Ar=mr.WeakMap;if(br||wr.state){var Mr=wr.state||(wr.state=new Ar);Mr.get=Mr.get,Mr.has=Mr.has,Mr.set=Mr.set,ir=function(t,n){if(Mr.has(t))throw Tr(Pr);return n.facade=t,Mr.set(t,n),n},ur=function(t){return Mr.get(t)||{}},ar=function(t){return Mr.has(t)}}else{var Cr=Or("state");jr[Cr]=!0,ir=function(t,n){if(Sr(t,Cr))throw Tr(Pr);return n.facade=t,hr(t,Cr,n),n},ur=function(t){return Sr(t,Cr)?t[Cr]:{}},ar=function(t){return Sr(t,Cr)}}var xr={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!vr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Er=a,Fr=K,Rr=Bt,Ir=c,Dr=nr.CONFIGURABLE,Lr=cr,Nr=xr.enforce,zr=xr.get,_r=Object.defineProperty,kr=Ir&&!Er((function(){return 8!==_r((function(){}),"length",{value:8}).length})),qr=String(String).split("String"),Br=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Rr(t,"name")||Dr&&t.name!==n)&&(Ir?_r(t,"name",{value:n,configurable:!0}):t.name=n),kr&&r&&Rr(r,"arity")&&t.length!==r.arity&&_r(t,"length",{value:r.arity});try{r&&Rr(r,"constructor")&&r.constructor?Ir&&_r(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Nr(t);return Rr(e,"source")||(e.source=qr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Br((function(){return Fr(this)&&zr(this).source||Lr(this)}),"toString");var Gr=K,Hr=xn,Ur=Vn.exports,Wr=Ft,Jr={},Kr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Kr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=H,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=Bt,le=H,se=ce.indexOf,pe=dr,ge=F([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},de=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Jr.f=Object.getOwnPropertyNames||function(t){return ye(t,de)};var be={};be.f=Object.getOwnPropertySymbols;var me=tt,ve=Jr,he=be,Se=Dn,we=F([].concat),Oe=me("Reflect","ownKeys")||function(t){var n=ve.f(Se(t)),r=he.f;return r?we(n,r(t)):n},je=Bt,Pe=Oe,Te=u,Ae=xn,Me=a,Ce=K,xe=/#|\.prototype\./,Ee=function(t,n){var r=Re[Fe(t)];return r==De||r!=Ie&&(Ce(n)?Me(n):!!n)},Fe=Ee.normalize=function(t){return String(t).replace(xe,".").toLowerCase()},Re=Ee.data={},Ie=Ee.NATIVE="N",De=Ee.POLYFILL="P",Le=Ee,Ne=i,ze=u.f,_e=Qn,ke=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Gr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Hr.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},qe=Ft,Be=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var a=e[u];je(t,a)||r&&je(r,a)||o(t,a,i(n,a))}},Ge=Le,He=C,Ue=Array.isArray||function(t){return"Array"==He(t)},We=TypeError,Je=yn,Ke=xn,Qe=h,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=K,$e=C,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=F,eo=a,oo=K,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),go=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,mo=Ue,vo=bo,ho=X,So=en("species"),wo=Array,Oo=function(t){var n;return mo(t)&&(n=t.constructor,(vo(n)&&(n===wo||mo(n.prototype))||ho(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=a,Po=ct,To=en("species"),Ao=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?Ne:f?Ne[a]||qe(a,{}):(Ne[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=ze(r,e))&&u.value:r[e],!Ge(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Be(i,o)}(t.sham||o&&o.sham)&&_e(i,"sham",!0),ke(r,e,i,t)}},Mo=a,Co=Ue,xo=X,Eo=_t,Fo=ee,Ro=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Io=function(t,n,r){var e=Je(n);e in t?Ke.f(t,e,Qe(0,r)):t[e]=r},Do=function(t,n){return new(Oo(t))(0===n?0:n)},Lo=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},No=ct,zo=en("isConcatSpreadable"),_o=No>=51||!Mo((function(){var t=[];return t[zo]=!1,t.concat()[0]!==t})),ko=Lo("concat"),qo=function(t){if(!xo(t))return!1;var n=t[zo];return void 0!==n?!!n:Co(t)};Ao({target:"Array",proto:!0,arity:1,forced:!_o||!ko},{concat:function(t){var n,r,e,o,i,u=Eo(this),a=Do(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(qo(i=-1===n?u:arguments[n]))for(o=Fo(i),Ro(c+o),r=0;r<o;r++,c++)r in i&&Io(a,c,i[r]);else Ro(c+1),Io(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["es-AR"]={formatCopyRows:function(){return"Copiar Filas"},formatPrint:function(){return"Imprimir"},formatLoadingMessage:function(){return"Cargando, espere por favor"},formatRecordsPerPage:function(t){return"".concat(t," registros por página")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Mostrando desde ".concat(t," a ").concat(n," de ").concat(r," filas (filtrado de ").concat(e," columnas totales)"):"Mostrando desde ".concat(t," a ").concat(n," de ").concat(r," filas")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"a la página ".concat(t)},formatSRPaginationNextText:function(){return"siguiente página"},formatDetailPagination:function(t){return"Mostrando ".concat(t," columnas")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatPaginationSwitch:function(){return"Ocultar/Mostrar paginación"},formatPaginationSwitchDown:function(){return"Mostrar paginación"},formatPaginationSwitchUp:function(){return"Ocultar paginación"},formatRefresh:function(){return"Recargar"},formatToggleOn:function(){return"Mostrar vista de carta"},formatToggleOff:function(){return"Ocultar vista de carta"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Cambiar todo"},formatFullscreen:function(){return"Pantalla completa"},formatAllRows:function(){return"Todo"},formatAutoRefresh:function(){return"Auto Recargar"},formatExport:function(){return"Exportar datos"},formatJumpTo:function(){return"Ir"},formatAdvancedSearch:function(){return"Búsqueda avanzada"},formatAdvancedCloseButton:function(){return"Cerrar"},formatFilterControlSwitch:function(){return"Ocultar/Mostrar controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Mostrar controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["es-AR"])}));
