/* fieldset {
    font-family: sans-serif;
    border: 2px solid #1F497D;
    background: #ffffff;
    border-radius: 5px;
    padding: 15px;
  }

  fieldset legend {
    background: #ffffff;
    color: #000000;
    padding: 5px 10px ;
    font-size: 20px;
    border-radius: 5px;
    /* box-shadow: 0 0 0 5px #ddd; */
    /* margin-left: 20px;
  }

  legend {
    float: left;
    width: auto;
    padding: 0;
    margin-top: -32px;

    margin-bottom: 0.5rem;
    font-size: calc(1.275rem + .3vw);
    line-height: inherit;
  }
  .full-width{
    width: 100%;
  } */ 

  .mat-mdc-form-field {
    font-size: 14px;
    width: 100%;
  }

  .mat-column-PcsesAssigned {
    padding: 0 16px !important;
  }

  td.mat-cell {
    padding: 16px !important;
  }

  .mat-icon.info-icon {
    width: 20px;
    height: 20px;
    font-size: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    background-color: #3f51b5;
    color: white;
    padding: 4px;
    margin-left: 8px;
  }

  .mat-elevation-z8 {
    box-shadow: 0 5px 5px -3px rgba(0,0,0,.2), 0 8px 10px 1px rgba(0,0,0,.14), 0 3px 14px 2px rgba(0,0,0,.12);
    border-radius: 4px;
    overflow: hidden;
  }

  table {
    width: 100%;
  }

  .mat-header-cell {
    color: rgba(0, 0, 0, 0.87);
    font-size: 14px;
    font-weight: 500;
  }

  .mat-cell {
    color: rgba(0, 0, 0, 0.87);
    font-size: 14px;
  }

  td.mat-cell, th.mat-header-cell {
    padding: 0 16px;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: rgba(0, 0, 0, 0.12);
  }

  .info-circle-icon {
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 16px;
    background-color: #3f51b5;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
  }

  tr.mat-row {
    height: 48px;
  }

  tr.mat-header-row {
    height: 56px;
  }

  .mat-paginator {
    background: transparent;
  }




  fieldset {
  font-family: sans-serif;
  border: 2px solid #1F497D;
  background: #ffffff;
  border-radius: 5px;
  padding: 15px;
}

fieldset legend {
  background: #ffffff;
  color: #000000;
  padding: 5px 10px ;
  font-size: 20px;
  border-radius: 5px;
  /* box-shadow: 0 0 0 5px #ddd; */
  margin-left: 20px;
}

legend {
  float: left;
  width: auto;
  padding: 0;
  margin-top: -32px;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + .3vw);
  line-height: inherit;
}
.ex-width{
  width:100%;
}
.space{
  display: flex;
  justify-content:space-between;

}
.mat-mdc-header-cell {
  background-color: #f5f5f5;
  color: #1F497D;
  font-weight: bold;
  font-size: 14px;
}

.mat-mdc-row:nth-child(even) {
  background-color: #f9f9f9;
}

.mat-mdc-row:hover {
  background-color: #f0f0f0;
}

/* Styling for amount and rate columns */
.mat-column-amount,
.mat-column-rate {
 
  text-align: right !important;
  padding-right: 24px !important;
}

/* Add some padding to all cells for better spacing */
.mat-mdc-cell,
.mat-mdc-header-cell {
  padding: 8px 16px !important;
}

/* Add a subtle border between rows */
.mat-mdc-row {
  border-bottom: 1px solid #e0e0e0;
}

/* Style for the paginator */
.mat-mdc-paginator {
  background-color: #f5f5f5;

}

.scroll-container{
    scroll-behavior: smooth !important;
    overflow-x: scroll !important;
    overflow-y: scroll !important;
  }
 
.scroll-container::-webkit-scrollbar {
    height: 9px !important;
    width: 9px !important;
  }
.scroll-container::-webkit-scrollbar-thumb {
    background: #808080;
  }

    .mat-column-size {
    text-align: center !important;
  }
  .mat-column-Quality{
    text-align: center !important;
  }