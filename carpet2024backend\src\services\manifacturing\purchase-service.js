// services/purchaseService.js
const purchaseRepository = require('../../repositories/manifacturing/purchase-repository');

class PurchaseService {
  async createPurchase(purchaseData) {
    return await purchaseRepository.create(purchaseData);
  }

  async getAllPurchases() {
    return await purchaseRepository.findAll();
  }

  async getPurchaseById(id) {
    return await purchaseRepository.findById(id);
  }

  async updatePurchase(id, purchaseData) {
    return await purchaseRepository.update(id, purchaseData);
  }

  async deletePurchase(id) {
    return await purchaseRepository.delete(id);
  }
}

module.exports = new PurchaseService();
