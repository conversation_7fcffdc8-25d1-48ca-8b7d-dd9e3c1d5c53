const express = require('express');
const branchController = require('../../controller/branch-controller');

const router = express.Router();

router.post('/branches', branchController.createBranch);
router.get('/branches', branchController.getAllBranches);
router.get('/branches/:id', branchController.getBranchById);
router.put('/branches/:id', branchController.updateBranch);
router.delete('/branches/:id', branchController.deleteBranch);

module.exports = router;
