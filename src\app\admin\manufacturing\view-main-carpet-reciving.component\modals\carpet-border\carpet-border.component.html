<div class="carpet-border-dialog ">
  <h2 mat-dialog-title class="title mt-3">Carpet No.{{data.carpetNo}} Size</h2>
  <mat-dialog-content>

        <!-- First row - Carpet Details -->
 <form [formGroup]="borderForm">
  <div class="row g-2">
      <div class="col-md-2">
        <mat-form-field appearance="outline">
          <mat-label>Quality</mat-label>
          <input matInput [value]="data.quality" readonly tabindex="-1">
        </mat-form-field>
      </div>
      <div class="col-md-2">
        <mat-form-field appearance="outline">
          <mat-label>Design</mat-label>
          <input matInput [value]="data.design" readonly tabindex="-1">
        </mat-form-field>
      </div>
      <div class="col-md-2">
        <mat-form-field appearance="outline">
          <mat-label>Colour</mat-label>
          <input matInput [value]="data.color" readonly tabindex="-1">
        </mat-form-field>
      </div>
       <div class="size-w">
        <mat-form-field appearance="outline">
          <mat-label>Size</mat-label>
          <input matInput [value]="data.size" readonly tabindex="-1">
        </mat-form-field>
      </div>





 
       <div class="col-md-2">
                  <mat-form-field appearance="outline" class="ex-width ">
                      <mat-label>Weight</mat-label>
                      <input matInput placeholder=".000" formControlName="weight" (blur)="setDigit($event)" (keydown.enter)="onWeightEnter()" #weightInput tabindex="1">
                      <mat-error *ngIf="borderForm.get('weight')?.hasError('required')">Weight is required</mat-error>
                  </mat-form-field>
        </div>
        <div class="col-md-2">
                <div class="map-w" style="border: 1px solid #4e4b4b; padding: 8px 12px; border-radius: 4px;">
                  <mat-radio-group color="primary" aria-label="Select Yes or No" style="display: flex; gap: 12px; align-items: center;" tabindex="-1">
                    <mat-radio-button value="1" tabindex="-1">Yes</mat-radio-button>
                    <mat-radio-button value="2" tabindex="-1">No</mat-radio-button>
                  </mat-radio-group>
                </div>
              </div>   

            </div>
   
      <div class="input-section">

        <div class="row g-3">
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>⬇️ नधनी कि.</mat-label>
              <input matInput formControlName="BottomBorder1" (keydown.enter)="onBottomBorder1Enter()" #bottomBorder1Input>
              <mat-error *ngIf="borderForm.get('BottomBorder1')?.hasError('required')">Bottom Border 1 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>⬇️ नधनी कि.</mat-label>
              <input matInput formControlName="BottomBorder2" (keydown.enter)="onBottomBorder2Enter()" #bottomBorder2Input>
              <mat-error *ngIf="borderForm.get('BottomBorder2')?.hasError('required')">Bottom Border 2 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>⬇️ नधनी कि.</mat-label>
              <input matInput formControlName="BottomBorder3">
            </mat-form-field>
          </div>
          <!-- </div>
           <div class="d-flex justify-content-evenly"> -->
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>⬆️ उतरनी कि.</mat-label>
              <input matInput formControlName="TopBorder1" (keydown.enter)="onTopBorder1Enter()" #topBorder1Input>
              <mat-error *ngIf="borderForm.get('TopBorder1')?.hasError('required')">Top Border 1 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>⬆️ उतरनी कि.</mat-label>
              <input matInput formControlName="TopBorder2" (keydown.enter)="onTopBorder2Enter()" #topBorder2Input>
              <mat-error *ngIf="borderForm.get('TopBorder2')?.hasError('required')">Top Border 2 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>⬆️ उतरनी कि.</mat-label>
              <input matInput formControlName="TopBorder3">
            </mat-form-field>
          </div>
          </div>
           <div class="row g-3">
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>➡️ दाया कि.</mat-label>
              <input matInput formControlName="rightBorder1" (keydown.enter)="onRightBorder1Enter()" #rightBorder1Input>
              <mat-error *ngIf="borderForm.get('rightBorder1')?.hasError('required')">Right Border 1 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>➡️ दाया कि.</mat-label>
              <input matInput formControlName="rightBorder2" (keydown.enter)="onRightBorder2Enter()" #rightBorder2Input>
              <mat-error *ngIf="borderForm.get('rightBorder2')?.hasError('required')">Right Border 2 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>➡️ दाया कि.</mat-label>
              <input matInput formControlName="rightBorder3">
            </mat-form-field>
          </div>
         
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>⬅️ बाया कि.</mat-label>
              <input matInput formControlName="leftBorder1" (keydown.enter)="onLeftBorder1Enter()" #leftBorder1Input>
              <mat-error *ngIf="borderForm.get('leftBorder1')?.hasError('required')">Left Border 1 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>⬅️ बाया कि.</mat-label>
              <input matInput formControlName="leftBorder2" (keydown.enter)="onLeftBorder2Enter()" #leftBorder2Input>
              <mat-error *ngIf="borderForm.get('leftBorder2')?.hasError('required')">Left Border 2 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>⬅️ बाया कि.</mat-label>
              <input matInput formControlName="leftBorder3">
            </mat-form-field>
          </div>
          </div>
           <div class="row g-3">
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>आधा</mat-label>
              <input matInput formControlName="halfBorder1" (keydown.enter)="onHalfBorder1Enter()" #halfBorder1Input>
              <mat-error *ngIf="borderForm.get('halfBorder1')?.hasError('required')">Half Border 1 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>आधा</mat-label>
              <input matInput formControlName="halfBorder2" (keydown.enter)="onHalfBorder2Enter()" #halfBorder2Input>
              <mat-error *ngIf="borderForm.get('halfBorder2')?.hasError('required')">Half Border 2 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>आधा</mat-label>
              <input matInput formControlName="halfBorder3">
            </mat-form-field>
          </div>

          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>जमीन</mat-label>
              <input matInput formControlName="groundBorder1" (keydown.enter)="onGroundBorder1Enter()" #groundBorder1Input>
              <mat-error *ngIf="borderForm.get('groundBorder1')?.hasError('required')">Ground Border 1 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>जमीन</mat-label>
              <input matInput formControlName="groundBorder2" (keydown.enter)="onGroundBorder2Enter()" #groundBorder2Input>
              <mat-error *ngIf="borderForm.get('groundBorder2')?.hasError('required')">Ground Border 2 is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width ">
              <mat-label>जमीन</mat-label>
              <input matInput formControlName="groundBorder3">
            </mat-form-field>
          </div>
        </div>
        <div class="d-flex justify-content-end mb-1">
        <div *ngIf="boxArray.length < 20">
            <button type="button" class="btn btn-warning box-w" (click)="addBoxInput()">
              + 
            </button> 
            
          </div>

          </div>
        <!-- Dynamic डिब्बा inputs -->
        <div class="row g-3">
          
          <ng-container formArrayName="boxArray">
            <ng-container *ngFor="let ctrl of boxArray.controls; let i = index">
              <div class="col-md-2">
                <mat-form-field appearance="outline" class="ex-width w-100">
                  <mat-label>डिब्बा</mat-label>
                  <input matInput [formControlName]="i">
                </mat-form-field>
              </div>
            </ng-container>
          </ng-container>
          
        </div>
<div class="row">
          <div class=" ">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Remarks</mat-label>
                  <textarea matInput formControlName="Remarks" placeholder="Add remarks" rows="2"></textarea>
                </mat-form-field>
              </div>
      </div>

       <!-- Image upload section above Remarks -->
      <div class="d-flex justify-content-between align-items-center mb-1">    
        <h4 class="upload-color">Upload Picture</h4> 

        <div class="d-flex align-items-center">
         
        <button type="button" class="btn btn-warning image-w" (click)="addImageInput()">
              +
            </button> 
             <h4 class="more-picture">More Picture</h4>
            </div>  
            </div>
          <div class="co-md-12 d-flex align-items-center flex-wrap" style="gap: 12px;">
            <ng-container *ngFor="let img of images; let i = index">
              <div class="me-2 mb-2 d-flex flex-column align-items-center">
                <button type="button" class="btn btn-outline-secondary mb-1" (click)="triggerFileInput(i)">
                  <span *ngIf="!img.preview">Upload Picture {{i+1}}</span>
                </button>
                <input type="file" accept="image/*" (change)="onImageSelected($event, i)" style="display: none;" [id]="'imgInput' + i">
                <ng-container *ngIf="img.preview">
                  <img [src]="img.preview" alt="Preview" width="40" height="40" style="object-fit:cover; border-radius:4px; cursor:pointer; border:2px solid #007bff;" (click)="openImagePreview(img.preview)">
                </ng-container>
              </div>
            </ng-container>
            
          </div>
      

      <!-- Image preview modal -->
      <div *ngIf="previewImage" class="image-preview-modal" (click)="closeImagePreview()" style="position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.7); display:flex; align-items:center; justify-content:center; z-index:9999;">
        <img [src]="previewImage" style="max-width:90vw; max-height:90vh; border-radius:8px; box-shadow:0 2px 16px #000;">
      </div>

      


      </div>

      
     
    </form>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()">Cancel</button>
    <button mat-raised-button color="primary" (click)="onSave()" (keydown.enter)="onSaveEnter()" #saveButton tabindex="0">Save</button>
  </mat-dialog-actions>
</div>




