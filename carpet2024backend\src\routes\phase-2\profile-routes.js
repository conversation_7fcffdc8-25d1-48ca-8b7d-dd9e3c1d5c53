const express = require('express');
const profileController = require('../../controller/profile-controller');

const router = express.Router();

router.post('/create-profile', profileController.createProfile);
router.get('/get-profile/:id', profileController.getProfileById);
router.get('/get-all-profile', profileController.getAllProfiles);
router.put('/update-profile/:id', profileController.updateProfile);
router.delete('/delete-profile/:id', profileController.deleteProfile);

module.exports = router;
