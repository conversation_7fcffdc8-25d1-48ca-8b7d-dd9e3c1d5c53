const express = require('express');
const router = express.Router();
const exportPackingListController = require('../../controller/manifacturing/exportPacking-controller');

// Define routes
router.post('/exportPacking', exportPackingListController.createExportPacking);
router.get('/exportPacking', exportPackingListController.getAllExportPackings);
router.get('/exportPacking/invoice/:invoiceNo', exportPackingListController.getExportPackingsByInvoiceNo);
router.get('/exportPacking/:id', exportPackingListController.getExportPackingById);
router.put('/exportPacking/:id', exportPackingListController.updateExportPacking);
router.delete('/exportPacking/:id', exportPackingListController.deleteExportPacking);

module.exports = router;