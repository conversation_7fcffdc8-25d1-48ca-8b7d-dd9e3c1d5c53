(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ro = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Romanian = {
      weekdays: {
          shorthand: ["Dum", "Lun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>â<PERSON>"],
          longhand: [
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "Sâmbătă",
          ],
      },
      months: {
          shorthand: [
              "<PERSON>",
              "Feb",
              "Mar",
              "Apr",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "Aug",
              "Sep",
              "Oct",
              "<PERSON><PERSON>",
              "<PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "August",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>iembrie",
              "Decembrie",
          ],
      },
      firstDayOfWeek: 1,
      time_24hr: true,
      ordinal: function () {
          return "";
      },
  };
  fp.l10ns.ro = Romanian;
  var ro = fp.l10ns;

  exports.Romanian = Romanian;
  exports.default = ro;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
