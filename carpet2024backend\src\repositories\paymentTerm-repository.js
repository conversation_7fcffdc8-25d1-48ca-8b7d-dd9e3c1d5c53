const PaymentTerms = require('../model/phase-1/paymentTerm');

class PaymentTermsRepository {
  async createPaymentTerm(paymentTermData) {
    try {
      const paymentTerm = new PaymentTerms(paymentTermData);
      await paymentTerm.save();
      return paymentTerm;
    } catch (error) {
      throw error;
    }
  }

  async getPaymentTermById(paymentTermId) {
    try {
      const paymentTerm = await PaymentTerms.findById(paymentTermId);
      return paymentTerm;
    } catch (error) {
      throw error;
    }
  }

  async getAllPaymentTerms() {
    try {
      const paymentTerms = await PaymentTerms.find();
      return paymentTerms;
    } catch (error) {
      throw error;
    }
  }

  async updatePaymentTerm(paymentTermId, paymentTermData) {
    try {
      const paymentTerm = await PaymentTerms.findByIdAndUpdate(paymentTermId, paymentTermData, { new: true });
      return paymentTerm;
    } catch (error) {
      throw error;
    }
  }

  async deletePaymentTerm(paymentTermId) {
    try {
      const deletedPaymentTerm = await PaymentTerms.findByIdAndDelete(paymentTermId);
      return deletedPaymentTerm;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new PaymentTermsRepository();
