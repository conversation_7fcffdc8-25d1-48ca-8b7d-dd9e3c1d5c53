 <div class="container mt-3">
    <section >
        <fieldset  style="min-height: 200px;">
            <legend><b>{{isUpdated? 'Update Size Master':'Add Size Master'}}   </b></legend>
            <form [formGroup]="sizeForm" (ngSubmit)="sizeFormSubmit()">
            <div class="row">
                <div class="mb-2 col-md-4">
                    <mat-form-field appearance="outline" class="ex-width">
                        <mat-label>Size In Yard</mat-label>
                        <input formControlName="sizeInYard" matInput placeholder="Size In Yard" maxlength="14" (blur)="setSizeYard()">
                    </mat-form-field>
                </div>
                <div class="mb-2 col-md-4">
                    <mat-form-field appearance="outline" class="ex-width">
                        <mat-label> Area Sq. Yard</mat-label>
                        <input formControlName="areaInYard"  matInput placeholder="Sq. In Total">
                    </mat-form-field>
                </div>

                <div class="mb-2 col-md-4">
                    <mat-form-field appearance="outline" class="ex-width">
                        <mat-label>Area In Feet</mat-label>
                        <input formControlName="areaInfeet" matInput placeholder="Area In Feet">
                    </mat-form-field>
                </div>
                <div class="mb-2 col-md-4">
                    <mat-form-field appearance="outline" class="ex-width">
                        <mat-label>Khap Size</mat-label>
                        <input formControlName="khapSize" matInput placeholder="Khap Size">
                    </mat-form-field>
                </div>
                <div class="mb-2 col-md-4">
                    <mat-form-field appearance="outline" class="ex-width">
                        <mat-label>Size In Meter</mat-label>
                        <input formControlName="sizeinMeter" matInput placeholder="Size In Meter" (blur)="setSizeMeter()">
                    </mat-form-field>
                </div>
                <div class="mb-2 col-md-4">
                    <mat-form-field appearance="outline" class="ex-width">
                        <mat-label>Sq. Meter</mat-label>
                        <input formControlName="sqMeter" matInput placeholder="Sq.   Meter">
                    </mat-form-field>
                </div>
                <div class="mb-2 col-md-4">
                    <mat-form-field appearance="outline" class="ex-width">
                        <mat-label>Sq.No.</mat-label>
                        <input formControlName="srNo" matInput placeholder="Sq.No.">
                    </mat-form-field>
                </div>

                <div class="mb-2 mt-2 col-md-4">
                  <label for="">Upload Pdf.</label>
                  <input type="file" name="" id="" (change)="upload($event)" class="form-control">
                </div>
                <div class="mb-2 mt-2 col-md-4">

                    <button mat-flat-button color="primary" type="submit">{{isUpdated ? 'Update' : 'Add' }}</button>
                </div>


            </div>
          </form>
        </fieldset>
    </section>
 </div>
 <div class="conatiner mt-5">
    <section>
        <fieldset>
            <legend><b>List</b></legend>

         <div class="row">
            <div class=" col-12">
                <mat-form-field appearance="outline">
                    <mat-label>Search</mat-label>
                    <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Jack" #input>
                  </mat-form-field>

                  <div class="mat-elevation-z8" style="overflow: auto;">
                    <table mat-table [dataSource]="dataSource" matSort style="width: max-content;" >

                      <!-- No Column -->
                      <ng-container matColumnDef="id">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Sr.No.</th>
                        <td mat-cell *matCellDef="let row">{{row.index}}</td>
                      </ng-container>

                      <!-- SizeInYard Column -->
                      <ng-container matColumnDef="sizeInYard">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Size In Yard</th>
                        <td mat-cell *matCellDef="let row"> {{row.sizeInYard}}</td>
                      </ng-container>


                      <!-- AreaSqYard Column -->
                      <ng-container matColumnDef="areaInYard">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Area Sq. Yard</th>
                        <td mat-cell *matCellDef="let row"> {{row.areaInYard}} </td>
                      </ng-container>
                       <!-- KhapSize Column -->
                       <ng-container matColumnDef="khapSize">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Khap Size</th>
                        <td mat-cell *matCellDef="let row"> {{row.khapSize}} </td>
                      </ng-container>


                     <!-- Colour Column -->
                     <ng-container matColumnDef="areaInfeet">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Area In Feet</th>
                        <td mat-cell *matCellDef="let row"> {{row.areaInfeet}} </td>
                      </ng-container>

                         <!-- Description Column -->
                         <ng-container matColumnDef="sizeinMeter">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Size In Meter</th>
                            <td mat-cell *matCellDef="let row"> {{row.sizeinMeter}} </td>
                          </ng-container>

                            <!-- Details Column -->
                         <ng-container matColumnDef="sqMeter">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Sq. Meter</th>
                            <td mat-cell *matCellDef="let row"> {{row.sqMeter}} </td>
                          </ng-container>
                             <!-- Image Column -->
                          <ng-container matColumnDef="srNo">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Sq. No</th>
                            <td mat-cell *matCellDef="let row"> {{row.srNo}} </td>
                          </ng-container>

                          <ng-container matColumnDef="uploadedFile">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>File</th>
                            <td mat-cell *matCellDef="let row">                               <a (click)="readPdf(row.uploadedFile)"><img  src="../../../../assets/images/pdf-icon-64.png" width="15px" alt="Image"></a>
                            </td>
                          </ng-container>
                            <!-- Action Column -->
                          <ng-container matColumnDef="action">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                            <td mat-cell *matCellDef="let row">
                              <button mat-icon-button color="primary" (click)="edit(row)" ><mat-icon>edit</mat-icon></button>
                              <button mat-icon-button color="warn" (click)="deleteSize(row.id)"><mat-icon>delete</mat-icon></button>
                            </td>
                          </ng-container>
                      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                      <!-- Row shown when there is no matching data. -->
                      <tr class="mat-row" *matNoDataRow>
                        <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>
                      </tr>
                    </table>

                    <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>
            </div>
        </div>
         </div>
    </fieldset>
    </section>
 </div>
