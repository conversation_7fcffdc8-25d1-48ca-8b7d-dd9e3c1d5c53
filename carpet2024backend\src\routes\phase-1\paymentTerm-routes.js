const express = require('express');
const paymentTermsController = require('../../controller/paymentTerm-controller');
const { validatePaymentTermData } = require('../../middleware/paymentTerm-middleware');

const router = express.Router();

router.post('/create-paymentTerm', validatePaymentTermData, paymentTermsController.createPaymentTerm);
router.get('/get-paymentTerm/:id', paymentTermsController.getPaymentTermById);
router.get('/get-all-paymentTerm', paymentTermsController.getAllPaymentTerms);
router.put('/update-paymentTerm/:id', validatePaymentTermData, paymentTermsController.updatePaymentTerm);
router.delete('/delete-paymentTerm/:id', paymentTermsController.deletePaymentTerm);

module.exports = router;
