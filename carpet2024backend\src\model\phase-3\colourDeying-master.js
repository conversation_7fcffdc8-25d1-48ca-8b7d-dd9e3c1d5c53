const mongoose = require('mongoose');

const colourcodeDeyingSchema = new mongoose.Schema({
    quality: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'AddQuality'
    },
    Color: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Color'
    },
    colourcode: {
        type: String,
        required: true
    },
    companyColorCode:{
        type: String,
        required:true
    },
    material: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'RawMaterialGroup'
    },
    item: {
        type: String,
        required: true
    },
    count: {
        type: String, // Changed to array of strings
        required: true
    },
    chemicals: [{
        chemical: String,
        weight: String,
        vendorName: String,
        dyesName: String,
        weight2: String,
        vendorName2: String
    }]
});

const colourcodeDeying = mongoose.model('colourcodeDeying', colourcodeDeyingSchema);

module.exports = colourcodeDeying;
 
