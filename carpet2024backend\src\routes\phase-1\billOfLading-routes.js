const express = require('express');
const { uploadPdf } = require('../../middleware/fileUpload-middleware');
const pdfDetailesController=require('../../controller/pdfDetails-controller');

const router = express.Router();

router.post('/upload-billoflading-pdf', uploadPdf.single('billOfLading'),pdfDetailesController.createPdf);
router.get('/get-billoflading-pdf/:id', pdfDetailesController.getPdfById);
router.get('/get-all-billoflading', pdfDetailesController.getAllPdf);
router.put('/update-billoflading-pdf/:id', pdfDetailesController.updatePdf);
router.delete('/delete-billoflading-pdf/:id', pdfDetailesController.deletePdf);


module.exports = router;



