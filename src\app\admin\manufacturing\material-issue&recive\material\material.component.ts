import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-material',
  templateUrl: './material.component.html',
  styleUrl: './material.component.css'
})
export class MaterialComponent implements OnInit {

  constructor(private router: Router) {}

  ngOnInit(): void {
    // Component loads with prompt visible
  }

    navigateToMaterialIssue (): void {
 // Navigate to material-Issue component
    console.log('Navigating to Material Issue Component');
    this.router.navigate(['/admin/material-issue']);
 
  }

 navigateToMaterialReceive(): void {
    // Navigate to material-receive component
    console.log('Navigating to Material Issue Component');
    this.router.navigate(['/admin/material-receive']);
  }
}
