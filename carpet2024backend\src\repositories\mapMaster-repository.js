// repositories/mapMasterRepository.js
const mapMaster = require('../model/phase-3/map-master');

class MapMasterRepository {
    async create(data) {
        return await mapMaster.create(data);
    }

    async findAll() {
        return await mapMaster.find().populate('quality').populate('addDesign').populate('groundColour').populate('borderColour').populate('sizeMaster');
    }

    async findById(id) {
        return await mapMaster.findById(id);
    }

    async update(id, data) {
        return await mapMaster.findByIdAndUpdate(id, data, { new: true });
    }

    async delete(id) {
        return await mapMaster.findByIdAndDelete(id);
    }
}

module.exports = new MapMasterRepository();
