// Test script to verify weaver display logic for H- format records
const mongoose = require('mongoose');

const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function testWeaverDisplay() {
  try {
    // Connect to MongoDB
    await mongoose.connect(DB_URL);
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get H- records with populated data (similar to API)
    const CarpetReceived = mongoose.model('CarpetReceived', new mongoose.Schema({}, { strict: false }));
    const hRecords = await CarpetReceived.find({ receiveNo: { $regex: /^H-/ } })
      .populate('K', 'branchCode branchName')
      .populate('weaverNumber', 'name weaverName')
      .lean();
    
    console.log('\n📋 Testing Weaver Display Logic for H- Records:');
    console.log('='.repeat(80));
    console.log('Receive No | K.branchCode | weaverNumber.name | weaverName | Expected Display');
    console.log('='.repeat(80));
    
    hRecords.forEach((record, index) => {
      const branchCode = record.K?.branchCode || 'null';
      const weaverNumberName = record.weaverNumber?.name || 'null';
      const weaverName = record.weaverName || 'null';
      
      // Apply the frontend logic
      const weaverDisplay = record.receiveNo?.startsWith('H-') ? 
        (record.weaverName || 'N/A') : 
        (record.K?.branchCode+' - '+(record.weaverNumber?.name || record.weaverName) || record.weaverName || 'N/A');
      
      console.log(`${record.receiveNo.padEnd(10)} | ${branchCode.padEnd(12)} | ${weaverNumberName.padEnd(17)} | ${weaverName.padEnd(18)} | ${weaverDisplay}`);
    });
    
    console.log('\n✅ Test Results:');
    console.log(`   - Found ${hRecords.length} H- records`);
    console.log(`   - All should display proper "Stock March YYYY" weaver names`);
    console.log(`   - No "H - Undefined" should appear`);
    
    // Test the weaver list generation logic
    console.log('\n📋 Testing Weaver List Generation:');
    const weaverSet = new Set();
    hRecords.forEach(item => {
      if (item.receiveNo?.startsWith('H-')) {
        // For H- format records, use weaverName directly
        if (item.weaverName) {
          weaverSet.add(item.weaverName);
        }
      } else {
        // For K- format records, use branch code + weaver name
        if (item.K?.branchCode && item.weaverNumber?.name) {
          weaverSet.add(item.K.branchCode + ' - ' + item.weaverNumber.name);
        }
      }
    });
    
    const uniqueWeavers = Array.from(weaverSet).sort();
    console.log(`   - Unique H- weavers for dropdown: ${uniqueWeavers.length}`);
    uniqueWeavers.forEach((weaver, index) => {
      console.log(`     ${index + 1}. ${weaver}`);
    });
    
  } catch (error) {
    console.error('❌ Error testing weaver display:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the test
testWeaverDisplay().catch(console.error);
