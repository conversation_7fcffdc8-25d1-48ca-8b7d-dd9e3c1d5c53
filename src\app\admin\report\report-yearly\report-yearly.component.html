<div class="row">
  <div class="col-md-2">
    <mat-form-field appearance="outline">
      <mat-label>Select Year</mat-label>
      <mat-select (valueChange)="getMonth($event)">
        <mat-option value="All">All</mat-option>
        <mat-option *ngFor="let year of years" [value]="year.year">{{
          year.year
        }}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div class="col-md-2">
    <mat-form-field appearance="outline">
      <mat-label>Select Month</mat-label>

      <mat-select (valueChange)="getMonthName($event)">
        <mat-option value="All">All</mat-option>
        <mat-option *ngFor="let month of months" [value]="month.id">{{
          month.month
        }}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div class="col-md-3"> <mat-form-field appearance="outline">
    <mat-label>Search</mat-label>
    <input
      matInput
      (keyup)="applyFilter($event)"
      placeholder="Ex. Jack"
      #input
    />
  </mat-form-field></div>
  <div class="col-md-3" style="padding-top: 10px;">
    <button type="button" mat-flat-button color="primary" (click)="logAndExportCurrentPageData()" >
      <i class="fa fa-file-excel-o" aria-hidden="true"></i> Export To Excel
    </button>
  </div>
  <div class="col-md-12"></div>

  <div class="col-md-12" style="overflow: auto;">
    <table mat-table [dataSource]="dataSource" class="mat-elevation-z8" matSort style="width: max-content;">
      <!--- Note that these columns can be defined in any order.
            The actual rendered columns are set as a property on the row definition" -->

      <!-- Position Column -->
      <ng-container matColumnDef="id">
        <th mat-header-cell mat-sort-header *matHeaderCellDef>No.</th>
        <td mat-cell *matCellDef="let element">{{ element.index }}</td>
      </ng-container>

      <!-- Name Column -->
      <ng-container matColumnDef="billNo">
        <th mat-header-cell mat-sort-header *matHeaderCellDef>Bill No</th>
        <td mat-cell *matCellDef="let element">{{ element.billNo }}</td>
      </ng-container>

      <!-- Weight Column -->
      <ng-container matColumnDef="date">
        <th mat-header-cell mat-sort-header *matHeaderCellDef>Date</th>
        <td mat-cell *matCellDef="let element">{{ element.date }}</td>
      </ng-container>

      <!-- Symbol Column -->
      <ng-container matColumnDef="customer">
        <th mat-header-cell *matHeaderCellDef>Customer</th>
        <td mat-cell *matCellDef="let element">{{ element.customer }}</td>
      </ng-container>

      <ng-container matColumnDef="pcs">
        <th mat-header-cell *matHeaderCellDef>Pcs</th>
        <td mat-cell *matCellDef="let element" class="text-end">{{ element.pcs }}</td>
      </ng-container>

      <ng-container matColumnDef="area">
        <th mat-header-cell *matHeaderCellDef>Area</th>
        <td mat-cell *matCellDef="let element" class="text-end">
          {{ element.area | number : "1.2-2" }}
        </td>
      </ng-container>

      <ng-container matColumnDef="gst">
        <th mat-header-cell *matHeaderCellDef>Gst</th>
        <td mat-cell *matCellDef="let element" class="text-end">
          {{ element.gst | number : "1.2-2" }}
        </td>
        <!-- <td mat-footer-cell *matFooterCellDef>{{ calculations.grossTotalAmount | number: "1.2-2" }}</td> -->
      </ng-container>
      <ng-container matColumnDef="profit">
        <th mat-header-cell *matHeaderCellDef>Profit</th>
        <td mat-cell *matCellDef="let element" class="text-end">
          {{ element.profit | number : "1.2-2" }}
        </td>
        <!-- <td mat-footer-cell *matFooterCellDef>{{ calculations.grossTotalAmount | number: "1.2-2" }}</td> -->
      </ng-container>

      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef>Amount</th>
        <td mat-cell *matCellDef="let element" class="text-end">
          {{ element.amount | number : "1.2-2" }}
        </td>
      </ng-container>
      <ng-container matColumnDef="totalAmount">
        <th mat-header-cell *matHeaderCellDef>Total Amount</th>
        <td mat-cell *matCellDef="let element" class="text-end">
          {{ element.totalAmount | number : "1.2-2" }}
        </td>
        <!-- <td mat-footer-cell *matFooterCellDef>--</td> -->
      </ng-container>
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef>Action</th>
        <td mat-cell *matCellDef="let element">
          <a routerLink="../report-sale/{{ element.id }} bill"
            ><i class="fa fa-eye" aria-hidden="true"></i
          ></a>
          &nbsp;
          <a routerLink="../bills-for-wholeseller/{{ element.id }}"
            ><i
              class="fa fa-pencil-square-o fa-edit"
              title="Edit Bill "
              aria-hidden="true"
            ></i
          ></a>
          &nbsp;
          <a (click)="deleteChallan(element.id + ' ')"
            ><i class="fa fa-trash-o" aria-hidden="true"></i
          ></a>

          &nbsp;
          <a routerLink="/my/{{ element.id }} print" target="_blank" > <i class="fa fa-print"  title="Print"></i></a>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>

      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      <!-- <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr> -->
    </table>

  </div>
  <div class="row">
    <div class="col-md-5"></div>
    <div class="col-md-7">
      <table style="width: 100%">
        <tr class="bg-success">
          <th>Pcs</th>
          <th>Area</th>
          <th>Gst</th>
          <th>Amount</th>
          <th>Gross Amount</th>

        </tr>
        <tr>
          <td>
            {{ calculations.totalPcs }}
          </td>
          <td>{{ calculations.totalArea | number : "1.2-2" }}</td>
          <td>{{ calculations.totalGst | number : "1.2-2" }}</td>
          <td>{{ calculations.totalAmount | number : "1.2-2" }}</td>
          <td>{{ calculations.grossTotalAmount | number : "1.2-2" }}</td>
          <td></td>
        </tr>
      </table>
    </div>
  </div>
  <mat-paginator
    [length]="100"
    [pageSize]="10"
    [pageSizeOptions]="[5, 10, 25, 100]"
    aria-label="Select page"
  >
  </mat-paginator>
</div>


