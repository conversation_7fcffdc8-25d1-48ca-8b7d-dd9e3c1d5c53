
<div style="width: 90%" class="conatiner mt-4">
  <section>
      <fieldset>
          <legend><b>{{isEditMode?'Update':'Add'}} Buyer Master</b></legend>
          <form [formGroup]="buyerForm"(ngSubmit)="buyerFormSubmit()" >
          <div class="row">
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width " appearance="outline">
                  <mat-label>Customer Code</mat-label>
                  <input matInput formControlName="customerCode" placeholder="Customer Code">
                </mat-form-field>
                <div class="erroDiv" *ngIf="buyerForm.get('customerCode')?.invalid && (buyerForm.get('customerCode')?.dirty || buyerForm.get('customerCode')?.touched)">
                  <div *ngIf="buyerForm.get('customerCode')?.errors?.['required']">
                   <span class="errText"> Customer code is required.</span>
                  </div>
                </div>
              </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Customer Name</mat-label>
                  <input matInput formControlName="customerName" placeholder="Customer Name">
                </mat-form-field>
                <div class="erroDiv" *ngIf="buyerForm.get('customerName')?.invalid && (buyerForm.get('customerName')?.dirty || buyerForm.get('customerName')?.touched)">
                  <div *ngIf="buyerForm.get('customerName')?.errors?.['required']">
                   <span class="errText"> Name is required.</span>
                  </div>

                </div>
              </div>

              <!-- <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Customer Name</mat-label>
                  <input matInput formControlName="customerName" placeholder="Customer Name">
                </mat-form-field>
                <div *ngIf="buyerForm.get('customerName')?.invalid && (buyerForm.get('customerName')?.dirty || buyerForm.get('customerName')?.touched)">
                  <div *ngIf="buyerForm.get('customerName')?.errors?.required">
                    Name is required.
                  </div>
                  <div *ngIf="buyerForm.get('customerName')?.errors?.pattern">
                    something weny
                  </div>
                </div>
              </div> -->

              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Contact No</mat-label>
                  <input matInput formControlName="customerNo" placeholder="Contact No">
                </mat-form-field>

                <!-- <div class="erroDiv" *ngIf="buyerForm.get('customerNo')?.invalid && (buyerForm.get('customerNo')?.dirty || buyerForm.get('customerNo')?.touched)">
                  <div *ngIf="buyerForm.get('customerNo')?.errors?.['required']">
                   <span class="text-danger"> Contact No. is required.</span>
                  </div>
                  <div *ngIf="buyerForm.get('customerNo')?.errors?.['pattern']">

                    <span class="text-danger"> Something went wrong </span>
                  </div>
                </div> -->
              </div>

              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Customer Address</mat-label>
                  <textarea formControlName="customerAddress" matInput ></textarea>
                </mat-form-field>

                <div class="erroDiv" *ngIf="buyerForm.get('customerAddress')?.invalid && (buyerForm.get('customerAddress')?.dirty || buyerForm.get('customerAddress')?.touched)">
                  <div *ngIf="buyerForm.get('customerAddress')?.errors?.['required']">
                   <span class="text-danger"> Address is required.</span>
                  </div>
                  <div *ngIf="buyerForm.get('customerAddress')?.errors?.['pattern']">

                    <span class="text-danger"> Something went wrong </span>
                  </div>
                </div>
              </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Select Country</mat-label>
                  <mat-select formControlName="country">
                    <mat-option *ngFor="let val of country"[value]="val">{{val}}</mat-option>
                  </mat-select>
                </mat-form-field>
                <div class="erroDiv" *ngIf="buyerForm.get('country')?.invalid && (buyerForm.get('country')?.dirty || buyerForm.get('country')?.touched)">
                  <div *ngIf="buyerForm.get('country')?.errors?.['required']">
                   <span class="text-danger"> Please select a country </span>
                  </div>

                </div>
              </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Zip Code</mat-label>
                  <input formControlName="zipCode" matInput placeholder="Zip Code">

                </mat-form-field>
                <!-- <div class="erroDiv" *ngIf="buyerForm.get('zipCode')?.invalid && (buyerForm.get('zipCode')?.dirty || buyerForm.get('zipCode')?.touched)">
                  <div *ngIf="buyerForm.get('zipCode')?.errors?.['required']">
                   <span class="text-danger"> Address is required.</span>
                  </div>
                  <div *ngIf="buyerForm.get('zipCode')?.errors?.['pattern']">

                    <span class="text-danger"> Something went wrong </span>
                  </div>
                </div> -->
              </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Customer Email</mat-label>
                  <input formControlName="customerEmail" type="email" matInput placeholder="Customer Email">
                </mat-form-field>
                <div class="erroDiv" *ngIf="buyerForm.get('customerEmail')?.invalid && (buyerForm.get('customerEmail')?.dirty || buyerForm.get('customerEmail')?.touched)">
                  <div *ngIf="buyerForm.get('customerEmail')?.errors?.['required']">
                   <span class="text-danger"> Address is required.</span>
                  </div>
                  <div *ngIf="buyerForm.get('customerEmail')?.errors?.['pattern']">

                    <span class="text-danger"> Something went wrong </span>
                  </div>
                </div>
              </div>

              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>No. of Stores</mat-label>
                  <input formControlName="numberOfStores" matInput placeholder="No. of Stores">
                </mat-form-field>
              </div><hr><br><br><legend><b>Bank Details</b></legend><hr>

              <div class="row" >

                <div class="mb-2 col-sm-3">
                 <mat-form-field class="ex-width" appearance="outline">
                   <mat-label>Bank Name</mat-label>
                    <input formControlName="bankName" matInput placeholder="Bank Name">
                  </mat-form-field>
                </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Bank Branch</mat-label>
                  <input formControlName="branch" matInput placeholder="Bank Branch">

                </mat-form-field>
              </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Bank Contact No.</mat-label>
                  <input  matInput placeholder="Bank Contact No." formControlName="bankContactNo">
                </mat-form-field>
              </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Bank Address</mat-label>
                  <textarea  formControlName="bankAddress" matInput></textarea>
                </mat-form-field>
              </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Select Bank Country</mat-label>
                  <mat-select formControlName="bankCountry">
                    <mat-option *ngFor="let val of bankCountry"[value]="val">{{val}}</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Bank Zip Code</mat-label>
                  <input  matInput placeholder="Bank Zip Code" formControlName="bankZipCode">

                </mat-form-field>
              </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Bank Email</mat-label>
                  <input formControlName="bankEmail" type="email"  matInput placeholder="Bank Email">
                </mat-form-field>
              </div>
              <div class="mb-2 col-sm-3">
                <mat-form-field class="ex-width" appearance="outline">
                  <mat-label>Swift Code</mat-label>
                  <input formControlName="swiftCode" matInput placeholder="Swift Code">
                </mat-form-field>
              </div>
            </div>
              <div class="mb-2 mt-2 col-sm-3"><button mat-raised-button color="primary"type="submit"  [disabled]="!buyerForm.valid" >{{isEditMode ? 'Update' : 'Add' }}</button>
              </div>
          </div>
        </form>
      </fieldset>
  </section>
</div>

<div  style="width: 90%" class="conatiner mt-4">
  <section>
    <fieldset>
      <legend><b>List</b></legend>
      <div class="container">

          <div class="row mt-3">
             <div class="col-sm-3">
              <mat-form-field  appearance="outline"><mat-label>Seacrh</mat-label>
                <input matInput (keyup)="applyFilter($event)"placeholder="Ex. Jia" #input/>
              </mat-form-field>
            </div>
            <div class="mat-elevation-z8 scroll-container">
              <table mat-table [dataSource]="dataSource" class="mat-elevation-z8" matSort style="width: max-content;">
                <!-- ID Column -->
                <ng-container matColumnDef="id">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Sr.No.</th>
                  <td mat-cell *matCellDef="let row,let i = index">{{getSerialNumber(i)}}</td>
                </ng-container>
                <!-- customerCode Column -->
                <ng-container matColumnDef="customerCode">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Buyer Code</th>
                  <td mat-cell *matCellDef="let row">{{row.customerCode}}</td>
                </ng-container>

                <!--customerName Column -->
                <ng-container matColumnDef="customerName">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                  <td mat-cell *matCellDef="let row">{{row.customerName}}</td>
                </ng-container>

                <!-- customerNo Column -->
                <ng-container matColumnDef="customerNo">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Contact No.</th>
                  <td mat-cell *matCellDef="let row">{{row.customerNo}}</td>
                </ng-container>
                <!-- customerAddress Column -->
                <ng-container matColumnDef="customerAddress">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Address</th>
                  <td mat-cell *matCellDef="let row">{{row.customerAddress}}</td>
                </ng-container>
                <!-- country Column -->
                <ng-container matColumnDef="country">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Country</th>
                  <td mat-cell *matCellDef="let row">{{row.country}}</td>
                </ng-container>
                <!-- zipCode Column -->
                <ng-container matColumnDef="zipCode">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Zip Code</th>
                  <td mat-cell *matCellDef="let row">{{row.zipCode}}</td>
                </ng-container>
                <!-- customerEmail Column -->
                <ng-container matColumnDef="customerEmail">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
                  <td mat-cell *matCellDef="let row" class="no-capitalize">{{row.customerEmail}}</td>
                </ng-container>
                <!-- numberOfStores Column -->
                <ng-container matColumnDef="numberOfStores">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>No.Of Stores</th>
                  <td mat-cell *matCellDef="let row">{{row.numberOfStores}}</td>
                </ng-container>
                <!-- bankName Column -->
                <ng-container matColumnDef="bankName">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Name</th>
                  <td mat-cell *matCellDef="let row">{{row.bankDetails.bankName}}</td>
                </ng-container>
                <!-- branch Column -->
                <ng-container matColumnDef="branch">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Branch</th>
                  <td mat-cell *matCellDef="let row">{{row.bankDetails.branch}}</td>
                </ng-container>
                <!-- bankAddress Column -->
                <ng-container matColumnDef="bankAddress">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Address</th>
                  <td mat-cell *matCellDef="let row">{{row.bankDetails.bankAddress}}</td>
                </ng-container>
                <!-- bankCountry Column -->
                <ng-container matColumnDef="bankCountry">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Country</th>
                  <td mat-cell *matCellDef="let row">{{row.bankDetails.bankCountry}}</td>
                </ng-container>
                <!-- bankEmail Column -->
                <ng-container matColumnDef="bankEmail">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Bank Email</th>
                  <td mat-cell *matCellDef="let row" class="no-capitalize">{{row.bankDetails.bankEmail}}</td>
                </ng-container>

                <!-- swiftCode Column -->
                <ng-container matColumnDef="swiftCode">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Swift Code</th>
                  <td mat-cell *matCellDef="let row">{{row.bankDetails.swiftCode}}</td>
                </ng-container>


                <!-- action Column -->
                <ng-container matColumnDef="action">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                  <td mat-cell *matCellDef="let row">
                    <button mat-icon-button color="primary"(click)="editBuyer(row)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn"(click)="deleteConformation(row._id)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </td>

                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr
                  mat-row
                  *matRowDef="let row; columns: displayedColumns"
                ></tr>

                <!-- Row shown when there is no matching data. -->
                <tr class="mat-row" *matNoDataRow>
                  <td class="mat-cell" colspan="4">
                    No data matching the filter "{{ input.value }}"
                  </td>
                </tr>
              </table>
            </div>
            <mat-paginator
              [pageSizeOptions]="[5, 10, 25, 100]"aria-label="Select page of users">
            </mat-paginator>
          </div>
        </div>

    </fieldset>
  </section>
</div>
