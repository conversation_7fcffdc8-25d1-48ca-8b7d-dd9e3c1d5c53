import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-admin-sidebar-new',
  templateUrl: './admin-sidebar-new.component.html',
  styleUrl: './admin-sidebar-new.component.css'
})
export class AdminSidebarNewComponent implements OnInit {
[x: string]: any;
  @Input()isOpen:boolean=true;
  // activeMenu: string | null = null;

  // toggleMenu(menuId: string) {
  //   this.activeMenu = this.activeMenu === menuId ? null : menuId;
  // }

  ngOnInit(): void {
    
  }
 

  }
