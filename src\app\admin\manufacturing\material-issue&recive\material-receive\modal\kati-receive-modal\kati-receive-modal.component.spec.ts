import { ComponentFixture, TestBed } from '@angular/core/testing';

import { KatiReceiveModalComponent } from './kati-receive-modal.component';

describe('KatiReceiveModalComponent', () => {
  let component: KatiReceiveModalComponent;
  let fixture: ComponentFixture<KatiReceiveModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [KatiReceiveModalComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(KatiReceiveModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
