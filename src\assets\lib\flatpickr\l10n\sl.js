(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.sl = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Slovenian = {
      weekdays: {
          shorthand: ["<PERSON>", "Pon", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sob"],
          longhand: [
              "Nedelja",
              "Ponedeljek",
              "Torek",
              "Sr<PERSON>",
              "Č<PERSON>rte<PERSON>",
              "Pete<PERSON>",
              "Sobota",
          ],
      },
      months: {
          shorthand: [
              "Jan",
              "Feb",
              "Mar",
              "Apr",
              "Maj",
              "Jun",
              "Jul",
              "Avg",
              "Sep",
              "Okt",
              "Nov",
              "Dec",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "April",
              "<PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>v<PERSON><PERSON>",
              "September",
              "<PERSON><PERSON><PERSON>",
              "November",
              "December",
          ],
      },
      firstDayOfWeek: 1,
      rangeSeparator: " do ",
      time_24hr: true,
      ordinal: function () {
          return ".";
      },
  };
  fp.l10ns.sl = Slovenian;
  var sl = fp.l10ns;

  exports.Slovenian = Slovenian;
  exports.default = sl;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
