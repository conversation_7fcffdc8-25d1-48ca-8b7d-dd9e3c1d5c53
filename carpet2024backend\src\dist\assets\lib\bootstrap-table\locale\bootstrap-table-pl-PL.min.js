/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},y={},b={}.propertyIsEnumerable,d=Object.getOwnPropertyDescriptor,g=d&&!b.call({1:2},1);y.f=g?function(t){var n=d(this,t);return!!n&&n.enumerable}:b;var m,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},w=f,S=Function.prototype,j=S.call,O=w&&S.bind.bind(j,j),P=function(t){return w?O(t):function(){return j.apply(t,arguments)}},k=P,T=k({}.toString),z=k("".slice),A=function(t){return z(T(t),8,-1)},E=A,x=P,C=function(t){if("Function"===E(t))return x(t)},F=a,M=A,R=Object,D=C("".split),L=F((function(){return!R("z").propertyIsEnumerable(0)}))?function(t){return"String"==M(t)?D(t,""):R(t)}:R,I=function(t){return null==t},N=I,W=TypeError,_=function(t){if(N(t))throw W("Can't call method on "+t);return t},U=L,G=_,q=function(t){return U(G(t))},B="object"==typeof document&&document.all,H={all:B,IS_HTMLDDA:void 0===B&&void 0!==B},K=H.all,Z=H.IS_HTMLDDA?function(t){return"function"==typeof t||t===K}:function(t){return"function"==typeof t},J=Z,Q=H.all,V=H.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:J(t)||t===Q}:function(t){return"object"==typeof t?null!==t:J(t)},X=i,Y=Z,$=function(t){return Y(t)?t:void 0},tt=function(t,n){return arguments.length<2?$(X[t]):X[t]&&X[t][n]},nt=C({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(h=(m=at.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!h&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(h=+m[1]);var ct=h,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,yt=tt,bt=Z,dt=nt,gt=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=yt("Symbol");return bt(n)&&dt(n.prototype,gt(t))},ht=String,vt=Z,wt=function(t){try{return ht(t)}catch(t){return"Object"}},St=TypeError,jt=function(t){if(vt(t))return t;throw St(wt(t)+" is not a function")},Ot=I,Pt=p,kt=Z,Tt=V,zt=TypeError,At={exports:{}},Et=i,xt=Object.defineProperty,Ct=function(t,n){try{xt(Et,t,{value:n,configurable:!0,writable:!0})}catch(r){Et[t]=n}return n},Ft=Ct,Mt="__core-js_shared__",Rt=i[Mt]||Ft(Mt,{}),Dt=Rt;(At.exports=function(t,n){return Dt[t]||(Dt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Lt=_,It=Object,Nt=function(t){return It(Lt(t))},Wt=Nt,_t=C({}.hasOwnProperty),Ut=Object.hasOwn||function(t,n){return _t(Wt(t),n)},Gt=C,qt=0,Bt=Math.random(),Ht=Gt(1..toString),Kt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Ht(++qt+Bt,36)},Zt=i,Jt=At.exports,Qt=Ut,Vt=Kt,Xt=st,Yt=pt,$t=Jt("wks"),tn=Zt.Symbol,nn=tn&&tn.for,rn=Yt?tn:tn&&tn.withoutSetter||Vt,en=function(t){if(!Qt($t,t)||!Xt&&"string"!=typeof $t[t]){var n="Symbol."+t;Xt&&Qt(tn,t)?$t[t]=tn[t]:$t[t]=Yt&&nn?nn(n):rn(n)}return $t[t]},on=p,un=V,an=mt,cn=function(t,n){var r=t[n];return Ot(r)?void 0:jt(r)},fn=function(t,n){var r,e;if("string"===n&&kt(r=t.toString)&&!Tt(e=Pt(r,t)))return e;if(kt(r=t.valueOf)&&!Tt(e=Pt(r,t)))return e;if("string"!==n&&kt(r=t.toString)&&!Tt(e=Pt(r,t)))return e;throw zt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},yn=mt,bn=function(t){var n=pn(t,"string");return yn(n)?n:n+""},dn=V,gn=i.document,mn=dn(gn)&&dn(gn.createElement),hn=function(t){return mn?gn.createElement(t):{}},vn=!c&&!a((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),wn=c,Sn=p,jn=y,On=v,Pn=q,kn=bn,Tn=Ut,zn=vn,An=Object.getOwnPropertyDescriptor;u.f=wn?An:function(t,n){if(t=Pn(t),n=kn(n),zn)try{return An(t,n)}catch(t){}if(Tn(t,n))return On(!Sn(jn.f,t,n),t[n])};var En={},xn=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Cn=V,Fn=String,Mn=TypeError,Rn=function(t){if(Cn(t))return t;throw Mn(Fn(t)+" is not an object")},Dn=c,Ln=vn,In=xn,Nn=Rn,Wn=bn,_n=TypeError,Un=Object.defineProperty,Gn=Object.getOwnPropertyDescriptor,qn="enumerable",Bn="configurable",Hn="writable";En.f=Dn?In?function(t,n,r){if(Nn(t),n=Wn(n),Nn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Hn in r&&!r.writable){var e=Gn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Bn in r?r.configurable:e.configurable,enumerable:qn in r?r.enumerable:e.enumerable,writable:!1})}return Un(t,n,r)}:Un:function(t,n,r){if(Nn(t),n=Wn(n),Nn(r),Ln)try{return Un(t,n,r)}catch(t){}if("get"in r||"set"in r)throw _n("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Kn=En,Zn=v,Jn=c?function(t,n,r){return Kn.f(t,n,Zn(1,r))}:function(t,n,r){return t[n]=r,t},Qn={exports:{}},Vn=c,Xn=Ut,Yn=Function.prototype,$n=Vn&&Object.getOwnPropertyDescriptor,tr=Xn(Yn,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Vn||Vn&&$n(Yn,"name").configurable)},rr=Z,er=Rt,or=C(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=Z,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=At.exports,yr=Kt,br=pr("keys"),dr={},gr=sr,mr=i,hr=V,vr=Jn,wr=Ut,Sr=Rt,jr=function(t){return br[t]||(br[t]=yr(t))},Or=dr,Pr="Object already initialized",kr=mr.TypeError,Tr=mr.WeakMap;if(gr||Sr.state){var zr=Sr.state||(Sr.state=new Tr);zr.get=zr.get,zr.has=zr.has,zr.set=zr.set,ir=function(t,n){if(zr.has(t))throw kr(Pr);return n.facade=t,zr.set(t,n),n},ur=function(t){return zr.get(t)||{}},ar=function(t){return zr.has(t)}}else{var Ar=jr("state");Or[Ar]=!0,ir=function(t,n){if(wr(t,Ar))throw kr(Pr);return n.facade=t,vr(t,Ar,n),n},ur=function(t){return wr(t,Ar)?t[Ar]:{}},ar=function(t){return wr(t,Ar)}}var Er={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw kr("Incompatible receiver, "+t+" required");return r}}},xr=a,Cr=Z,Fr=Ut,Mr=c,Rr=nr.CONFIGURABLE,Dr=cr,Lr=Er.enforce,Ir=Er.get,Nr=Object.defineProperty,Wr=Mr&&!xr((function(){return 8!==Nr((function(){}),"length",{value:8}).length})),_r=String(String).split("String"),Ur=Qn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Fr(t,"name")||Rr&&t.name!==n)&&(Mr?Nr(t,"name",{value:n,configurable:!0}):t.name=n),Wr&&r&&Fr(r,"arity")&&t.length!==r.arity&&Nr(t,"length",{value:r.arity});try{r&&Fr(r,"constructor")&&r.constructor?Mr&&Nr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Lr(t);return Fr(e,"source")||(e.source=_r.join("string"==typeof n?n:"")),t};Function.prototype.toString=Ur((function(){return Cr(this)&&Ir(this).source||Dr(this)}),"toString");var Gr=Z,qr=En,Br=Qn.exports,Hr=Ct,Kr={},Zr=Math.ceil,Jr=Math.floor,Qr=Math.trunc||function(t){var n=+t;return(n>0?Jr:Zr)(n)},Vr=function(t){var n=+t;return n!=n||0===n?0:Qr(n)},Xr=Vr,Yr=Math.max,$r=Math.min,te=Vr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=q,ie=function(t,n){var r=Xr(t);return r<0?Yr(r+n,0):$r(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=Ut,le=q,se=ce.indexOf,pe=dr,ye=C([].push),be=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ye(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ye(i,r));return i},de=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Kr.f=Object.getOwnPropertyNames||function(t){return be(t,de)};var ge={};ge.f=Object.getOwnPropertySymbols;var me=tt,he=Kr,ve=ge,we=Rn,Se=C([].concat),je=me("Reflect","ownKeys")||function(t){var n=he.f(we(t)),r=ve.f;return r?Se(n,r(t)):n},Oe=Ut,Pe=je,ke=u,Te=En,ze=a,Ae=Z,Ee=/#|\.prototype\./,xe=function(t,n){var r=Fe[Ce(t)];return r==Re||r!=Me&&(Ae(n)?ze(n):!!n)},Ce=xe.normalize=function(t){return String(t).replace(Ee,".").toLowerCase()},Fe=xe.data={},Me=xe.NATIVE="N",Re=xe.POLYFILL="P",De=xe,Le=i,Ie=u.f,Ne=Jn,We=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Gr(r)&&Br(r,i,e),e.global)o?t[n]=r:Hr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:qr.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},_e=Ct,Ue=function(t,n,r){for(var e=Pe(n),o=Te.f,i=ke.f,u=0;u<e.length;u++){var a=e[u];Oe(t,a)||r&&Oe(r,a)||o(t,a,i(n,a))}},Ge=De,qe=A,Be=Array.isArray||function(t){return"Array"==qe(t)},He=TypeError,Ke=bn,Ze=En,Je=v,Qe={};Qe[en("toStringTag")]="z";var Ve="[object z]"===String(Qe),Xe=Z,Ye=A,$e=en("toStringTag"),to=Object,no="Arguments"==Ye(function(){return arguments}()),ro=C,eo=a,oo=Z,io=Ve?Ye:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),$e))?r:no?Ye(n):"Object"==(e=Ye(n))&&Xe(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),yo=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},bo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};bo.sham=!0;var go=!fo||eo((function(){var t;return yo(yo.call)||!yo(Object)||!yo((function(){t=!0}))||t}))?bo:yo,mo=Be,ho=go,vo=V,wo=en("species"),So=Array,jo=function(t){var n;return mo(t)&&(n=t.constructor,(ho(n)&&(n===So||mo(n.prototype))||vo(n)&&null===(n=n[wo]))&&(n=void 0)),void 0===n?So:n},Oo=a,Po=ct,ko=en("species"),To=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?Le:f?Le[a]||_e(a,{}):(Le[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Ie(r,e))&&u.value:r[e],!Ge(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ue(i,o)}(t.sham||o&&o.sham)&&Ne(i,"sham",!0),We(r,e,i,t)}},zo=a,Ao=Be,Eo=V,xo=Nt,Co=ee,Fo=function(t){if(t>9007199254740991)throw He("Maximum allowed index exceeded");return t},Mo=function(t,n,r){var e=Ke(n);e in t?Ze.f(t,e,Je(0,r)):t[e]=r},Ro=function(t,n){return new(jo(t))(0===n?0:n)},Do=function(t){return Po>=51||!Oo((function(){var n=[];return(n.constructor={})[ko]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=ct,Io=en("isConcatSpreadable"),No=Lo>=51||!zo((function(){var t=[];return t[Io]=!1,t.concat()[0]!==t})),Wo=Do("concat"),_o=function(t){if(!Eo(t))return!1;var n=t[Io];return void 0!==n?!!n:Ao(t)};To({target:"Array",proto:!0,arity:1,forced:!No||!Wo},{concat:function(t){var n,r,e,o,i,u=xo(this),a=Ro(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(_o(i=-1===n?u:arguments[n]))for(o=Co(i),Fo(c+o),r=0;r<o;r++,c++)r in i&&Mo(a,c,i[r]);else Fo(c+1),Mo(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["pl-PL"]=r.default.fn.bootstrapTable.locales.pl={formatCopyRows:function(){return"Kopiuj wiersze"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Ładowanie, proszę czekać"},formatRecordsPerPage:function(t){return"".concat(t," rekordów na stronę")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Wyświetlanie rekordów od ".concat(t," do ").concat(n," z ").concat(r," (filtered from ").concat(e," total rows)"):"Wyświetlanie rekordów od ".concat(t," do ").concat(n," z ").concat(r)},formatSRPaginationPreText:function(){return"poprzednia strona"},formatSRPaginationPageText:function(t){return"z ".concat(t)},formatSRPaginationNextText:function(){return"następna strona"},formatDetailPagination:function(t){return"Wyświetla ".concat(t," wierszy")},formatClearSearch:function(){return"Wyczyść wyszukiwanie"},formatSearch:function(){return"Szukaj"},formatNoMatches:function(){return"Niestety, nic nie znaleziono"},formatPaginationSwitch:function(){return"Pokaż/ukryj stronicowanie"},formatPaginationSwitchDown:function(){return"Pokaż stronicowanie"},formatPaginationSwitchUp:function(){return"Ukryj stronicowanie"},formatRefresh:function(){return"Odśwież"},formatToggleOn:function(){return"Pokaż układ karty"},formatToggleOff:function(){return"Ukryj układ karty"},formatColumns:function(){return"Kolumny"},formatColumnsToggleAll:function(){return"Zaznacz wszystko"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Wszystkie"},formatAutoRefresh:function(){return"Auto odświeżanie"},formatExport:function(){return"Eksport danych"},formatJumpTo:function(){return"Przejdź"},formatAdvancedSearch:function(){return"Wyszukiwanie zaawansowane"},formatAdvancedCloseButton:function(){return"Zamknij"},formatFilterControlSwitch:function(){return"Pokaż/Ukryj"},formatFilterControlSwitchHide:function(){return"Pokaż"},formatFilterControlSwitchShow:function(){return"Ukryj"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["pl-PL"])}));
