import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ImporterService } from '../../../../services/importer.service';

export interface UserData {
  id: string;
  srNo: number;
  importerNo: string;
  importerName: string;
  invoiceNo: string;
  // chooseDate: string;
  areaUnit: string;
  currency: string;
  quality: string;
  totalArea: string;
  // area: string;
  amount: string;


}
const ELEMENT_DATA2: UserData[] = []

@Component({
  selector: 'app-import-invoice-report',
  templateUrl: './import-invoice-report.component.html',
  styleUrl: './import-invoice-report.component.css'
})
export class ImportInvoiceReportComponent implements OnInit {

  displayedColumns: string[] = ['srNo', 'importerNo', 'importerName', 'invoiceNo', 'areaUnit', 'currency', 'quality', 'totalArea', 'amount', 'action'];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA2);

  constructor(private _service: ImporterService) { }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;


  ngOnInit(): void {
    this.getInvoiceList();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

  }

  getInvoiceList() {
    this._service.getAllImporterInvoice().subscribe((resp: any) => {
      console.log(resp);
      if (resp) {

        ELEMENT_DATA2.length = 0;
        resp.map((x: any, i: number) => {
          let impoterno = x.impotererNo.split('/');
          ELEMENT_DATA2.push({
            id: x._id,
            srNo: i + 1,
            importerNo: impoterno[0],
            importerName: x.impotererName,
            invoiceNo: x.invoiceNo,
            
            areaUnit: x.areaOfUnit,
            currency: x.currency,
            quality: x.quantity,
            totalArea: x.totalArea,
            amount: x.amount
          })
        })
        this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
        this.ngAfterViewInit();
        return;
      }
    })
  }

}
