/*! Bootstrap 5 integration for DataTables' Responsive
 * © SpryMedia Ltd - datatables.net/license
 */
import $ from"jquery";import DataTable from"datatables.net-bs5";import"datatables.net-responsive";var modal,_display=DataTable.Responsive.display,_original=_display.modal,_modal=$('<div class="modal fade dtr-bs-modal" role="dialog"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"/></div></div></div>'),_bs=window.bootstrap;DataTable.Responsive.bootstrap=function(a){_bs=a},_display.modal=function(t){return modal=modal||new _bs.Modal(_modal[0]),function(a,d,o){var l,e;$.fn.modal?d||(t&&t.header&&(e=(l=_modal.find("div.modal-header")).find("button").detach(),l.empty().append('<h4 class="modal-title">'+t.header(a)+"</h4>").append(e)),_modal.find("div.modal-body").empty().append(o()),_modal.appendTo("body").modal(),modal.show()):_original(a,d,o)}};export default DataTable;