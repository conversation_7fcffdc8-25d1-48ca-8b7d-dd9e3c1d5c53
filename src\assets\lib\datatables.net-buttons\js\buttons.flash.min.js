/*!
 Flash export buttons for Buttons and DataTables.
 2015 SpryMedia Ltd - datatables.net/license

 ZeroClipbaord - MIT license
 Copyright (c) 2012 <PERSON>
*/
(function(f){"function"===typeof define&&define.amd?define(["jquery","datatables.net","datatables.net-buttons"],function(q){return f(q,window,document)}):"object"===typeof exports?module.exports=function(q,r){q||(q=window);r&&r.fn.dataTable||(r=require("datatables.net")(q,r).$);r.fn.dataTable.Buttons||require("datatables.net-buttons")(q,r);return f(r,q,q.document)}:f(jQuery,window,document)})(function(f,q,r,y){function t(a,b,c){var d=a.createElement(b);c&&(c.attr&&f(d).attr(c.attr),c.children&&f.each(c.children,
function(g,e){d.appendChild(e)}),c.text&&d.appendChild(a.createTextNode(c.text)));return d}function O(a,b){var c=a.header[b].length;a.footer&&a.footer[b].length>c&&(c=a.footer[b].length);for(var d=0,g=a.body.length;d<g;d++){var e=a.body[d][b];e=null!==e&&e!==y?e.toString():"";-1!==e.indexOf("\n")?(e=e.split("\n"),e.sort(function(n,m){return m.length-n.length}),e=e[0].length):e=e.length;e>c&&(c=e);if(40<c)return 52}c*=1.3;return 6<c?c:6}function K(a){E===y&&(E=-1===I.serializeToString(f.parseXML(A["xl/worksheets/sheet1.xml"])).indexOf("xmlns:r"));
f.each(a,function(b,c){if(f.isPlainObject(c))K(c);else{if(E){var d=c.childNodes[0],g,e=[];for(g=d.attributes.length-1;0<=g;g--){var n=d.attributes[g].nodeName;var m=d.attributes[g].nodeValue;-1!==n.indexOf(":")&&(e.push({name:n,value:m}),d.removeAttribute(n))}g=0;for(n=e.length;g<n;g++)m=c.createAttribute(e[g].name.replace(":","_dt_b_namespace_token_")),m.value=e[g].value,d.setAttributeNode(m)}c=I.serializeToString(c);E&&(-1===c.indexOf("<?xml")&&(c='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+
c),c=c.replace(/_dt_b_namespace_token_/g,":"));c=c.replace(/<([^<>]*?) xmlns=""([^<>]*?)>/g,"<$1 $2>");a[b]=c}})}var z=f.fn.dataTable,l={version:"1.0.4-TableTools2",clients:{},moviePath:"",nextId:1,$:function(a){"string"==typeof a&&(a=r.getElementById(a));a.addClass||(a.hide=function(){this.style.display="none"},a.show=function(){this.style.display=""},a.addClass=function(b){this.removeClass(b);this.className+=" "+b},a.removeClass=function(b){this.className=this.className.replace(new RegExp("\\s*"+
b+"\\s*")," ").replace(/^\s+/,"").replace(/\s+$/,"")},a.hasClass=function(b){return!!this.className.match(new RegExp("\\s*"+b+"\\s*"))});return a},setMoviePath:function(a){this.moviePath=a},dispatch:function(a,b,c){(a=this.clients[a])&&a.receiveEvent(b,c)},log:function(a){console.log("Flash: "+a)},register:function(a,b){this.clients[a]=b},getDOMObjectPosition:function(a){var b={left:0,top:0,width:a.width?a.width:a.offsetWidth,height:a.height?a.height:a.offsetHeight};""!==a.style.width&&(b.width=a.style.width.replace("px",
""));""!==a.style.height&&(b.height=a.style.height.replace("px",""));for(;a;)b.left+=a.offsetLeft,b.top+=a.offsetTop,a=a.offsetParent;return b},Client:function(a){this.handlers={};this.id=l.nextId++;this.movieId="ZeroClipboard_TableToolsMovie_"+this.id;l.register(this.id,this);a&&this.glue(a)}};l.Client.prototype={id:0,ready:!1,movie:null,clipText:"",fileName:"",action:"copy",handCursorEnabled:!0,cssEffects:!0,handlers:null,sized:!1,sheetName:"",glue:function(a,b){this.domElement=l.$(a);a=99;this.domElement.style.zIndex&&
(a=parseInt(this.domElement.style.zIndex,10)+1);var c=l.getDOMObjectPosition(this.domElement);this.div=r.createElement("div");var d=this.div.style;d.position="absolute";d.left="0px";d.top="0px";d.width=c.width+"px";d.height=c.height+"px";d.zIndex=a;"undefined"!=typeof b&&""!==b&&(this.div.title=b);0!==c.width&&0!==c.height&&(this.sized=!0);this.domElement&&(this.domElement.appendChild(this.div),this.div.innerHTML=this.getHTML(c.width,c.height).replace(/&/g,"&amp;"))},positionElement:function(){var a=
l.getDOMObjectPosition(this.domElement),b=this.div.style;b.position="absolute";b.width=a.width+"px";b.height=a.height+"px";0!==a.width&&0!==a.height&&(this.sized=!0,b=this.div.childNodes[0],b.width=a.width,b.height=a.height)},getHTML:function(a,b){var c="",d="id="+this.id+"&width="+a+"&height="+b;if(navigator.userAgent.match(/MSIE/)){var g=location.href.match(/^https/i)?"https://":"http://";c+='<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="'+g+'download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=10,0,0,0" width="'+
a+'" height="'+b+'" id="'+this.movieId+'" align="middle"><param name="allowScriptAccess" value="always" /><param name="allowFullScreen" value="false" /><param name="movie" value="'+l.moviePath+'" /><param name="loop" value="false" /><param name="menu" value="false" /><param name="quality" value="best" /><param name="bgcolor" value="#ffffff" /><param name="flashvars" value="'+d+'"/><param name="wmode" value="transparent"/></object>'}else c+='<embed id="'+this.movieId+'" src="'+l.moviePath+'" loop="false" menu="false" quality="best" bgcolor="#ffffff" width="'+
a+'" height="'+b+'" name="'+this.movieId+'" align="middle" allowScriptAccess="always" allowFullScreen="false" type="application/x-shockwave-flash" pluginspage="http://www.macromedia.com/go/getflashplayer" flashvars="'+d+'" wmode="transparent" />';return c},hide:function(){this.div&&(this.div.style.left="-2000px")},show:function(){this.reposition()},destroy:function(){var a=this;this.domElement&&this.div&&(f(this.div).remove(),this.div=this.domElement=null,f.each(l.clients,function(b,c){c===a&&delete l.clients[b]}))},
reposition:function(a){a&&((this.domElement=l.$(a))||this.hide());if(this.domElement&&this.div){a=l.getDOMObjectPosition(this.domElement);var b=this.div.style;b.left=""+a.left+"px";b.top=""+a.top+"px"}},clearText:function(){this.clipText="";this.ready&&this.movie.clearText()},appendText:function(a){this.clipText+=a;this.ready&&this.movie.appendText(a)},setText:function(a){this.clipText=a;this.ready&&this.movie.setText(a)},setFileName:function(a){this.fileName=a;this.ready&&this.movie.setFileName(a)},
setSheetData:function(a){this.ready&&this.movie.setSheetData(JSON.stringify(a))},setAction:function(a){this.action=a;this.ready&&this.movie.setAction(a)},addEventListener:function(a,b){a=a.toString().toLowerCase().replace(/^on/,"");this.handlers[a]||(this.handlers[a]=[]);this.handlers[a].push(b)},setHandCursor:function(a){this.handCursorEnabled=a;this.ready&&this.movie.setHandCursor(a)},setCSSEffects:function(a){this.cssEffects=!!a},receiveEvent:function(a,b){a=a.toString().toLowerCase().replace(/^on/,
"");switch(a){case "load":this.movie=r.getElementById(this.movieId);if(!this.movie){var c=this;setTimeout(function(){c.receiveEvent("load",null)},1);return}if(!this.ready&&navigator.userAgent.match(/Firefox/)&&navigator.userAgent.match(/Windows/)){c=this;setTimeout(function(){c.receiveEvent("load",null)},100);this.ready=!0;return}this.ready=!0;this.movie.clearText();this.movie.appendText(this.clipText);this.movie.setFileName(this.fileName);this.movie.setAction(this.action);this.movie.setHandCursor(this.handCursorEnabled);
break;case "mouseover":this.domElement&&this.cssEffects&&this.recoverActive&&this.domElement.addClass("active");break;case "mouseout":this.domElement&&this.cssEffects&&(this.recoverActive=!1,this.domElement.hasClass("active")&&(this.domElement.removeClass("active"),this.recoverActive=!0));break;case "mousedown":this.domElement&&this.cssEffects&&this.domElement.addClass("active");break;case "mouseup":this.domElement&&this.cssEffects&&(this.domElement.removeClass("active"),this.recoverActive=!1)}if(this.handlers[a])for(var d=
0,g=this.handlers[a].length;d<g;d++){var e=this.handlers[a][d];if("function"==typeof e)e(this,b);else if("object"==typeof e&&2==e.length)e[0][e[1]](this,b);else if("string"==typeof e)q[e](this,b)}}};l.hasFlash=function(){try{return new ActiveXObject("ShockwaveFlash.ShockwaveFlash"),!0}catch(a){if(navigator.mimeTypes&&navigator.mimeTypes["application/x-shockwave-flash"]!==y&&navigator.mimeTypes["application/x-shockwave-flash"].enabledPlugin)return!0}return!1};q.ZeroClipboard_TableTools=l;var L=function(a,
b){b.attr("id");b.parents("html").length?a.glue(b[0],""):setTimeout(function(){L(a,b)},500)},F=function(a,b){var c="*"===a.filename&&"*"!==a.title&&a.title!==y?a.title:a.filename;"function"===typeof c&&(c=c());-1!==c.indexOf("*")&&(c=f.trim(c.replace("*",f("title").text())));c=c.replace(/[^a-zA-Z0-9_\u00A1-\uFFFF\.,\-_ !\(\)]/g,"");return b===y||!0===b?c+a.extension:c},P=function(a){var b="Sheet1";a.sheetName&&(b=a.sheetName.replace(/[\[\]\*\/\\\?:]/g,""));return b},G=function(a,b){b=b.match(/[\s\S]{1,8192}/g)||
[];a.clearText();for(var c=0,d=b.length;c<d;c++)a.appendText(b[c])},M=function(a,b){var c=b.newline?b.newline:navigator.userAgent.match(/Windows/)?"\r\n":"\n";a=a.buttons.exportData(b.exportOptions);var d=b.fieldBoundary,g=b.fieldSeparator,e=new RegExp(d,"g"),n=b.escapeChar!==y?b.escapeChar:"\\",m=function(k){for(var h="",B=0,u=k.length;B<u;B++)0<B&&(h+=g),h+=d?d+(""+k[B]).replace(e,n+d)+d:k[B];return h},C=b.header?m(a.header)+c:"";b=b.footer&&a.footer?c+m(a.footer):"";for(var v=[],p=0,D=a.body.length;p<
D;p++)v.push(m(a.body[p]));return{str:C+v.join(c)+b,rows:v.length}},H={available:function(){return l.hasFlash()},init:function(a,b,c){l.moviePath=z.Buttons.swfPath;var d=new l.Client;d.setHandCursor(!0);d.addEventListener("mouseDown",function(g){c._fromFlash=!0;a.button(b[0]).trigger();c._fromFlash=!1});L(d,b);c._flash=d},destroy:function(a,b,c){c._flash.destroy()},fieldSeparator:",",fieldBoundary:'"',exportOptions:{},title:"*",filename:"*",extension:".csv",header:!0,footer:!1},I="";I="undefined"===
typeof q.XMLSerializer?new function(){this.serializeToString=function(a){return a.xml}}:new XMLSerializer;var E,A={"_rels/.rels":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/></Relationships>',"xl/_rels/workbook.xml.rels":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml"/></Relationships>',
"[Content_Types].xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension="xml" ContentType="application/xml" /><Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml" /><Default Extension="jpeg" ContentType="image/jpeg" /><Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml" /><Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml" /><Override PartName="/xl/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml" /></Types>',
"xl/workbook.xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"><fileVersion appName="xl" lastEdited="5" lowestEdited="5" rupBuild="24816"/><workbookPr showInkAnnotation="0" autoCompressPictures="0"/><bookViews><workbookView xWindow="0" yWindow="0" windowWidth="25600" windowHeight="19020" tabRatio="500"/></bookViews><sheets><sheet name="" sheetId="1" r:id="rId1"/></sheets></workbook>',
"xl/worksheets/sheet1.xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac"><sheetData/></worksheet>',"xl/styles.xml":'<?xml version="1.0" encoding="UTF-8"?><styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac"><numFmts count="6"><numFmt numFmtId="164" formatCode="#,##0.00_- [$$-45C]"/><numFmt numFmtId="165" formatCode="&quot;£&quot;#,##0.00"/><numFmt numFmtId="166" formatCode="[$€-2] #,##0.00"/><numFmt numFmtId="167" formatCode="0.0%"/><numFmt numFmtId="168" formatCode="#,##0;(#,##0)"/><numFmt numFmtId="169" formatCode="#,##0.00;(#,##0.00)"/></numFmts><fonts count="5" x14ac:knownFonts="1"><font><sz val="11" /><name val="Calibri" /></font><font><sz val="11" /><name val="Calibri" /><color rgb="FFFFFFFF" /></font><font><sz val="11" /><name val="Calibri" /><b /></font><font><sz val="11" /><name val="Calibri" /><i /></font><font><sz val="11" /><name val="Calibri" /><u /></font></fonts><fills count="6"><fill><patternFill patternType="none" /></fill><fill/><fill><patternFill patternType="solid"><fgColor rgb="FFD9D9D9" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="FFD99795" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="ffc6efce" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="ffc6cfef" /><bgColor indexed="64" /></patternFill></fill></fills><borders count="2"><border><left /><right /><top /><bottom /><diagonal /></border><border diagonalUp="false" diagonalDown="false"><left style="thin"><color auto="1" /></left><right style="thin"><color auto="1" /></right><top style="thin"><color auto="1" /></top><bottom style="thin"><color auto="1" /></bottom><diagonal /></border></borders><cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0" /></cellStyleXfs><cellXfs count="61"><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="left"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="center"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="right"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="fill"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment textRotation="90"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment wrapText="1"/></xf><xf numFmtId="9"   fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="164" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="165" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="166" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="167" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="168" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="169" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="3" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="4" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/></cellXfs><cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0" /></cellStyles><dxfs count="0" /><tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4" /></styleSheet>'},
N=[{match:/^\-?\d+\.\d%$/,style:60,fmt:function(a){return a/100}},{match:/^\-?\d+\.?\d*%$/,style:56,fmt:function(a){return a/100}},{match:/^\-?\$[\d,]+.?\d*$/,style:57},{match:/^\-?£[\d,]+.?\d*$/,style:58},{match:/^\-?€[\d,]+.?\d*$/,style:59},{match:/^\([\d,]+\)$/,style:61,fmt:function(a){return-1*a.replace(/[\(\)]/g,"")}},{match:/^\([\d,]+\.\d{2}\)$/,style:62,fmt:function(a){return-1*a.replace(/[\(\)]/g,"")}},{match:/^[\d,]+$/,style:63},{match:/^[\d,]+\.\d{2}$/,style:64}];z.Buttons.swfPath="//cdn.datatables.net/buttons/1.2.4/swf/flashExport.swf";
z.Api.register("buttons.resize()",function(){f.each(l.clients,function(a,b){b.domElement!==y&&b.domElement.parentNode&&b.positionElement()})});z.ext.buttons.copyFlash=f.extend({},H,{className:"buttons-copy buttons-flash",text:function(a){return a.i18n("buttons.copy","Copy")},action:function(a,b,c,d){d._fromFlash&&(this.processing(!0),a=d._flash,c=M(b,d),d=d.customize?d.customize(c.str,d):c.str,a.setAction("copy"),G(a,d),this.processing(!1),b.buttons.info(b.i18n("buttons.copyTitle","Copy to clipboard"),
b.i18n("buttons.copySuccess",{_:"Copied %d rows to clipboard",1:"Copied 1 row to clipboard"},c.rows),3E3))},fieldSeparator:"\t",fieldBoundary:""});z.ext.buttons.csvFlash=f.extend({},H,{className:"buttons-csv buttons-flash",text:function(a){return a.i18n("buttons.csv","CSV")},action:function(a,b,c,d){a=d._flash;b=M(b,d);b=d.customize?d.customize(b.str,d):b.str;a.setAction("csv");a.setFileName(F(d));G(a,b)},escapeChar:'"'});z.ext.buttons.excelFlash=f.extend({},H,{className:"buttons-excel buttons-flash",
text:function(a){return a.i18n("buttons.excel","Excel")},action:function(a,b,c,d){this.processing(!0);a=d._flash;var g=0,e=f.parseXML(A["xl/worksheets/sheet1.xml"]),n=e.getElementsByTagName("sheetData")[0];c={_rels:{".rels":f.parseXML(A["_rels/.rels"])},xl:{_rels:{"workbook.xml.rels":f.parseXML(A["xl/_rels/workbook.xml.rels"])},"workbook.xml":f.parseXML(A["xl/workbook.xml"]),"styles.xml":f.parseXML(A["xl/styles.xml"]),worksheets:{"sheet1.xml":e}},"[Content_Types].xml":f.parseXML(A["[Content_Types].xml"])};
b=b.buttons.exportData(d.exportOptions);var m,C,v=function(k){m=g+1;C=t(e,"row",{attr:{r:m}});for(var h=0,B=k.length;h<B;h++){for(var u=h,w="";0<=u;)w=String.fromCharCode(u%26+65)+w,u=Math.floor(u/26)-1;u=w+""+m;var x=null;if(null!==k[h]&&k[h]!==y&&""!==k[h]){k[h]=f.trim(k[h]);for(var J=0,Q=N.length;J<Q;J++)if(w=N[J],k[h].match&&!k[h].match(/^0\d+/)&&k[h].match(w.match)){x=k[h].replace(/[^\d\.\-]/g,"");w.fmt&&(x=w.fmt(x));x=t(e,"c",{attr:{r:u,s:w.style},children:[t(e,"v",{text:x})]});break}x||("number"===
typeof k[h]||k[h].match&&k[h].match(/^-?\d+(\.\d+)?$/)&&!k[h].match(/^0\d+/)?x=t(e,"c",{attr:{t:"n",r:u},children:[t(e,"v",{text:k[h]})]}):(w=k[h].replace?k[h].replace(/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F-\x9F]/g,""):k[h],x=t(e,"c",{attr:{t:"inlineStr",r:u},children:{row:t(e,"is",{children:{row:t(e,"t",{text:w})}})}})));C.appendChild(x)}}n.appendChild(C);g++};f("sheets sheet",c.xl["workbook.xml"]).attr("name",P(d));d.customizeData&&d.customizeData(b);d.header&&(v(b.header,g),f("row c",e).attr("s","2"));
for(var p=0,D=b.body.length;p<D;p++)v(b.body[p],g);d.footer&&b.footer&&(v(b.footer,g),f("row:last c",e).attr("s","2"));v=t(e,"cols");f("worksheet",e).prepend(v);p=0;for(D=b.header.length;p<D;p++)v.appendChild(t(e,"col",{attr:{min:p+1,max:p+1,width:O(b,p),customWidth:1}}));d.customize&&d.customize(c);K(c);a.setAction("excel");a.setFileName(F(d));a.setSheetData(c);G(a,"");this.processing(!1)},extension:".xlsx"});z.ext.buttons.pdfFlash=f.extend({},H,{className:"buttons-pdf buttons-flash",text:function(a){return a.i18n("buttons.pdf",
"PDF")},action:function(a,b,c,d){this.processing(!0);a=d._flash;var g=b.buttons.exportData(d.exportOptions),e=b.table().node().offsetWidth,n=b.columns(d.columns).indexes().map(function(m){return b.column(m).header().offsetWidth/e});a.setAction("pdf");a.setFileName(F(d));G(a,JSON.stringify({title:F(d,!1),message:"function"==typeof d.message?d.message(b,c,d):d.message,colWidth:n.toArray(),orientation:d.orientation,size:d.pageSize,header:d.header?g.header:null,footer:d.footer?g.footer:null,body:g.body}));
this.processing(!1)},extension:".pdf",orientation:"portrait",pageSize:"A4",message:"",newline:"\n"});return z.Buttons});
