<div class="container mt-4">
  <section >
      <fieldset>
          <legend><b> View Carpet Order Issue</b></legend>
<div class="row">
  <div class="col-md-3">
    <mat-form-field appearance="outline">
      <mat-label>Filter</mat-label>
      <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Mia" #input>
    </mat-form-field>
  </div>

  <!-- <PERSON> Filter -->
<div class="col-md-3">
  <mat-form-field appearance="outline">
    <mat-label>Weaver</mat-label>
    <mat-select [(ngModel)]="selectedWeaver" (selectionChange)="applyAllFilters()">
      <mat-option value="">All</mat-option>
      <mat-option *ngFor="let weaver of weaverList" [value]="weaver">{{weaver}}</mat-option>
    </mat-select>
  </mat-form-field>
</div>


 <!-- Year Filter -->
<div class="col-md-2">
  <mat-form-field appearance="outline">
    <mat-label>Year</mat-label>
    <mat-select [(ngModel)]="selectedYear" (selectionChange)="applyAllFilters()">
      <mat-option value="">All</mat-option>
      <mat-option *ngFor="let year of availableYears" [value]="year">{{ year }}</mat-option>
    </mat-select>
  </mat-form-field>
</div>

<!-- Month Filter -->
<div class="col-md-2">
  <mat-form-field appearance="outline">
    <mat-label>Month</mat-label>
    <mat-select [(ngModel)]="selectedMonth" (selectionChange)="applyAllFilters()">
      <mat-option *ngFor="let month of availableMonths" [value]="month.value">{{ month.label }}</mat-option>
    </mat-select>
  </mat-form-field>
</div>

<!-- Area Unit Filter -->
<div class="col-md-2">
  <mat-form-field appearance="outline">
    <mat-label>Area Unit</mat-label>
    <mat-select [(ngModel)]="selectedAreaUnit" (selectionChange)="applyAllFilters()">
      <mat-option value="">All</mat-option>
      <mat-option value="Ft">Sq.Feet (Ft)</mat-option>
      <mat-option value="Yd">Sq.Yard (Yd)</mat-option>
    </mat-select>
  </mat-form-field>
</div>



  <div class="col-md-12" style="overflow: auto;">
    <div class="mat-elevation-z8 table-responsive scroll-container">
    <table mat-table [dataSource]="dataSource" matSort style="width: max-content;">

      <ng-container matColumnDef="Br_issueNo">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Issue No. </th>
        <td mat-cell *matCellDef="let element">  <a routerLink="../carpet-order-issue/{{element.id}}" >{{element.Br_issueNo}}</a> </td>
      </ng-container>





      <ng-container matColumnDef="quality">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Quality </th>
        <td mat-cell *matCellDef="let element"> {{element.quality}} </td>
      </ng-container>
      <ng-container matColumnDef="design">
        <th mat-header-cell *matHeaderCellDef  mat-sort-header> Design </th>
        <td mat-cell *matCellDef="let element"> {{element.design}} </td>
      </ng-container>
      <ng-container matColumnDef="borderColour">
        <th mat-header-cell *matHeaderCellDef>  Colour </th>
        <td mat-cell *matCellDef="let element"> {{element.borderColour}} </td>
      </ng-container>
      <ng-container matColumnDef="size">
        <th mat-header-cell *matHeaderCellDef  mat-sort-header> Size </th>
        <td mat-cell *matCellDef="let element"> {{element.size}} </td>
      </ng-container>
      <!-- <ng-container matColumnDef="khapSize">
        <th mat-header-cell *matHeaderCellDef> Khap Size </th>
        <td mat-cell *matCellDef="let element"> {{element.khapSize}} </td>
      </ng-container>
 -->
      <ng-container matColumnDef="pcs">
        <th mat-header-cell *matHeaderCellDef  mat-sort-header> Pcs </th>
        <td mat-cell *matCellDef="let element"> {{element.pcs}} </td>
      </ng-container>
      <ng-container matColumnDef="area">
        <th mat-header-cell *matHeaderCellDef>Area</th>
        <td mat-cell *matCellDef="let element"> {{ element.area }}</td>
      </ng-container>
      <ng-container matColumnDef="rate">
        <th mat-header-cell *matHeaderCellDef  mat-sort-header> Rate </th>
        <td mat-cell *matCellDef="let element"> {{element.rate | number:'1.2-3'}} </td>
      </ng-container>
      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef  mat-sort-header>Amount</th>
        <td mat-cell *matCellDef="let element">{{ element.amount | number:'1.0-0' }}.00 </td>
      </ng-container>
      <ng-container matColumnDef="MapOrderNo">
        <th mat-header-cell *matHeaderCellDef> Map Order No </th>
        <td mat-cell *matCellDef="let element"> {{element.MapOrderNo}} </td>
      </ng-container>
      <!-- <ng-container matColumnDef="uploadFile">
        <th mat-header-cell *matHeaderCellDef> UploadFile </th>
        <td mat-cell *matCellDef="let element"><img   crossorigin="anonymous"  (click)="openModal(element.uploadFile)"
          id="myImg" [src]="element.uploadFile" alt="" width="50px" style="border-radius: 5px;"> </td>
      </ng-container> -->
      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef  mat-sort-header> Sr No. </th>
        <td mat-cell *matCellDef="let element ; let i=index"> {{i+1}} </td>
      </ng-container>
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef> Action </th>
        <td mat-cell *matCellDef="let element">
          <a routerLink="../carpet-order-issue/{{element.id}}" ><i class="fa-sharp fa-solid fa-pen-to-square" aria-hidden="true"></i></a>
          &nbsp;
          <a (click)="print(element.id)">
            <i class="fa-solid fa-print"></i>
          </a>&nbsp;

          <a  (click)="delete(element)"><i class="fa-solid fa-trash"></i></a>
         </td>
      </ng-container>



      <ng-container matColumnDef="date">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> date </th>
        <td mat-cell *matCellDef="let element"> {{element.date}} </td>
      </ng-container>


      <ng-container matColumnDef="buyerOrder">
        <th mat-header-cell *matHeaderCellDef> buyerOrder </th>
        <td mat-cell *matCellDef="let element"> {{element.buyerOrder}} </td>
      </ng-container>


      <ng-container matColumnDef="weaver">
        <th mat-header-cell *matHeaderCellDef> weaver </th>
        <td mat-cell *matCellDef="let element"> {{element.weaver}} </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
  <mat-paginator [pageSizeOptions]="[50, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>
  
</div>
</div>

</fieldset>
  </section>
  </div>





<div
class="modal fade"
id="myModal"
tabindex="-1"
aria-labelledby="exampleModalLabel"
aria-hidden="true"
>
<div class="modal-dialog modal-dialog-centered">
  <div class="modal-content">
    <div class="modal-header">
      <h5 class="modal-title" id="exampleModalLabel">Image Preview</h5>
      <button
        type="button"
        class="btn-close"
        data-bs-dismiss="modal"
        aria-label="Close"
      ></button>
    </div>
    <div class="modal-body">
      <img
        [src]="selectedImageUrl"
        crossorigin="anonymous"
        class="img-fluid"
        id="img01"
        alt="Image"
      />
      <div id="caption">{{ selectedImageUrl }}</div>
    </div>
  </div>
</div>
</div>
