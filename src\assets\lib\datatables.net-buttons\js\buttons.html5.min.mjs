/*!
 * HTML5 export buttons for Buttons and DataTables.
 * 2016 SpryMedia Ltd - datatables.net/license
 *
 * FileSaver.js (1.3.3) - MIT license
 * Copyright © 2016 Eli Grey - http://eligrey.com
 */
import $ from"jquery";import DataTable from"datatables.net";import Buttons from"datatables.net-buttons";var useJszip,usePdfmake;function _jsZip(){return useJszip||window.JSZip}function _pdfMake(){return usePdfmake||window.pdfMake}DataTable.Buttons.pdfMake=function(e){if(!e)return _pdfMake();usePdfmake=e},DataTable.Buttons.jszip=function(e){if(!e)return _jsZip();useJszip=e};var _saveAs=function(d){"use strict";var p,i,s,f,m,c,t,y,u,l,e;if(!(void 0===d||"undefined"!=typeof navigator&&/MSIE [1-9]\./.test(navigator.userAgent)))return e=d.document,p=function(){return d.URL||d.webkitURL||d},i=e.createElementNS("http://www.w3.org/1999/xhtml","a"),s="download"in i,f=/constructor/i.test(d.HTMLElement)||d.safari,m=/CriOS\/[\d]+/.test(navigator.userAgent),c=function(e){(d.setImmediate||d.setTimeout)(function(){throw e},0)},t=4e4,y=function(e){setTimeout(function(){"string"==typeof e?p().revokeObjectURL(e):e.remove()},t)},u=function(e){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e},e=(l=function(e,o,t){t||(e=u(e));var l,r,n=this,t="application/octet-stream"===e.type,a=function(){for(var e=n,t="writestart progress write writeend".split(" "),o=void 0,l=(t=[].concat(t)).length;l--;){var r=e["on"+t[l]];if("function"==typeof r)try{r.call(e,o||e)}catch(e){c(e)}}};n.readyState=n.INIT,s?(l=p().createObjectURL(e),setTimeout(function(){var e,t;i.href=l,i.download=o,e=i,t=new MouseEvent("click"),e.dispatchEvent(t),a(),y(l),n.readyState=n.DONE})):(m||t&&f)&&d.FileReader?((r=new FileReader).onloadend=function(){var e=m?r.result:r.result.replace(/^data:[^;]*;/,"data:attachment/file;");d.open(e,"_blank")||(d.location.href=e),e=void 0,n.readyState=n.DONE,a()},r.readAsDataURL(e),n.readyState=n.INIT):(l=l||p().createObjectURL(e),!t&&d.open(l,"_blank")||(d.location.href=l),n.readyState=n.DONE,a(),y(l))}).prototype,"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(e,t,o){return t=t||e.name||"download",o||(e=u(e)),navigator.msSaveOrOpenBlob(e,t)}:(e.abort=function(){},e.readyState=e.INIT=0,e.WRITING=1,e.DONE=2,e.error=e.onwritestart=e.onprogress=e.onwrite=e.onabort=e.onerror=e.onwriteend=null,function(e,t,o){return new l(e,t||e.name||"download",o)})}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||this.content),_sheetname=(DataTable.fileSave=_saveAs,function(e){var t="Sheet1";return t=e.sheetName?e.sheetName.replace(/[\[\]\*\/\\\?\:]/g,""):t}),_newLine=function(e){return e.newline||(navigator.userAgent.match(/Windows/)?"\r\n":"\n")},_exportData=function(e,t){function o(e){for(var t="",o=0,l=e.length;o<l;o++)0<o&&(t+=a),t+=n?n+(""+e[o]).replace(d,p+n)+n:e[o];return t}for(var l=_newLine(t),r=e.buttons.exportData(t.exportOptions),n=t.fieldBoundary,a=t.fieldSeparator,d=new RegExp(n,"g"),p=void 0!==t.escapeChar?t.escapeChar:"\\",e=t.header?o(r.header)+l:"",t=t.footer&&r.footer?l+o(r.footer):"",i=[],s=0,f=r.body.length;s<f;s++)i.push(o(r.body[s]));return{str:e+i.join(l)+t,rows:i.length}},_isDuffSafari=function(){var e;return-1!==navigator.userAgent.indexOf("Safari")&&-1===navigator.userAgent.indexOf("Chrome")&&-1===navigator.userAgent.indexOf("Opera")&&!!((e=navigator.userAgent.match(/AppleWebKit\/(\d+\.\d+)/))&&1<e.length&&+e[1]<603.1)};function createCellPos(e){for(var t="A".charCodeAt(0),o="Z".charCodeAt(0)-t+1,l="";0<=e;)l=String.fromCharCode(e%o+t)+l,e=Math.floor(e/o)-1;return l}try{var _ieExcel,_serialiser=new XMLSerializer}catch(e){}function _addToZip(s,e){void 0===_ieExcel&&(_ieExcel=-1===_serialiser.serializeToString((new window.DOMParser).parseFromString(excelStrings["xl/worksheets/sheet1.xml"],"text/xml")).indexOf("xmlns:r")),$.each(e,function(e,t){if($.isPlainObject(t))_addToZip(s.folder(e),t);else{if(_ieExcel){for(var o,l=t.childNodes[0],r=[],n=l.attributes.length-1;0<=n;n--){var a=l.attributes[n].nodeName,d=l.attributes[n].nodeValue;-1!==a.indexOf(":")&&(r.push({name:a,value:d}),l.removeAttribute(a))}for(n=0,o=r.length;n<o;n++){var p=t.createAttribute(r[n].name.replace(":","_dt_b_namespace_token_"));p.value=r[n].value,l.setAttributeNode(p)}}var i=_serialiser.serializeToString(t),i=(i=_ieExcel?(i=(i=-1===i.indexOf("<?xml")?'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+i:i).replace(/_dt_b_namespace_token_/g,":")).replace(/xmlns:NS[\d]+="" NS[\d]+:/g,""):i).replace(/<([^<>]*?) xmlns=""([^<>]*?)>/g,"<$1 $2>");s.file(e,i)}})}function _createNode(e,t,o){var l=e.createElement(t);return o&&(o.attr&&$(l).attr(o.attr),o.children&&$.each(o.children,function(e,t){l.appendChild(t)}),null!==o.text&&void 0!==o.text&&l.appendChild(e.createTextNode(o.text))),l}function _excelColWidth(e,t){var o=e.header[t].length;e.footer&&e.footer[t].length>o&&(o=e.footer[t].length);for(var l=0,r=e.body.length;l<r;l++){var n,a=e.body[l][t];if(40<(o=o<(n=(-1!==(a=null!=a?a.toString():"").indexOf("\n")?((n=a.split("\n")).sort(function(e,t){return t.length-e.length}),n[0]):a).length)?n:o))return 54}return 6<(o*=1.35)?o:6}var excelStrings={"_rels/.rels":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/></Relationships>',"xl/_rels/workbook.xml.rels":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml"/></Relationships>',"[Content_Types].xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension="xml" ContentType="application/xml" /><Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml" /><Default Extension="jpeg" ContentType="image/jpeg" /><Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml" /><Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml" /><Override PartName="/xl/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml" /></Types>',"xl/workbook.xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"><fileVersion appName="xl" lastEdited="5" lowestEdited="5" rupBuild="24816"/><workbookPr showInkAnnotation="0" autoCompressPictures="0"/><bookViews><workbookView xWindow="0" yWindow="0" windowWidth="25600" windowHeight="19020" tabRatio="500"/></bookViews><sheets><sheet name="Sheet1" sheetId="1" r:id="rId1"/></sheets><definedNames/></workbook>',"xl/worksheets/sheet1.xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac"><sheetData/><mergeCells count="0"/></worksheet>',"xl/styles.xml":'<?xml version="1.0" encoding="UTF-8"?><styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac"><numFmts count="6"><numFmt numFmtId="164" formatCode="#,##0.00_- [$$-45C]"/><numFmt numFmtId="165" formatCode="&quot;£&quot;#,##0.00"/><numFmt numFmtId="166" formatCode="[$€-2] #,##0.00"/><numFmt numFmtId="167" formatCode="0.0%"/><numFmt numFmtId="168" formatCode="#,##0;(#,##0)"/><numFmt numFmtId="169" formatCode="#,##0.00;(#,##0.00)"/></numFmts><fonts count="5" x14ac:knownFonts="1"><font><sz val="11" /><name val="Calibri" /></font><font><sz val="11" /><name val="Calibri" /><color rgb="FFFFFFFF" /></font><font><sz val="11" /><name val="Calibri" /><b /></font><font><sz val="11" /><name val="Calibri" /><i /></font><font><sz val="11" /><name val="Calibri" /><u /></font></fonts><fills count="6"><fill><patternFill patternType="none" /></fill><fill><patternFill patternType="none" /></fill><fill><patternFill patternType="solid"><fgColor rgb="FFD9D9D9" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="FFD99795" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="ffc6efce" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="ffc6cfef" /><bgColor indexed="64" /></patternFill></fill></fills><borders count="2"><border><left /><right /><top /><bottom /><diagonal /></border><border diagonalUp="false" diagonalDown="false"><left style="thin"><color auto="1" /></left><right style="thin"><color auto="1" /></right><top style="thin"><color auto="1" /></top><bottom style="thin"><color auto="1" /></bottom><diagonal /></border></borders><cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0" /></cellStyleXfs><cellXfs count="68"><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="left"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="center"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="right"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="fill"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment textRotation="90"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment wrapText="1"/></xf><xf numFmtId="9"   fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="164" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="165" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="166" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="167" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="168" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="169" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="3" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="4" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="1" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="2" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="14" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/></cellXfs><cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0" /></cellStyles><dxfs count="0" /><tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4" /></styleSheet>'},_excelSpecials=[{match:/^\-?\d+\.\d%$/,style:60,fmt:function(e){return e/100}},{match:/^\-?\d+\.?\d*%$/,style:56,fmt:function(e){return e/100}},{match:/^\-?\$[\d,]+.?\d*$/,style:57},{match:/^\-?£[\d,]+.?\d*$/,style:58},{match:/^\-?€[\d,]+.?\d*$/,style:59},{match:/^\-?\d+$/,style:65},{match:/^\-?\d+\.\d{2}$/,style:66},{match:/^\([\d,]+\)$/,style:61,fmt:function(e){return-1*e.replace(/[\(\)]/g,"")}},{match:/^\([\d,]+\.\d{2}\)$/,style:62,fmt:function(e){return-1*e.replace(/[\(\)]/g,"")}},{match:/^\-?[\d,]+$/,style:63},{match:/^\-?[\d,]+\.\d{2}$/,style:64},{match:/^[\d]{4}\-[01][\d]\-[0123][\d]$/,style:67,fmt:function(e){return Math.round(25569+Date.parse(e)/864e5)}}];DataTable.ext.buttons.copyHtml5={className:"buttons-copy buttons-html5",text:function(e){return e.i18n("buttons.copy","Copy")},action:function(e,t,o,l){this.processing(!0);var r=this,n=_exportData(t,l),a=t.buttons.exportInfo(l),d=_newLine(l),p=n.str,i=$("<div/>").css({height:1,width:1,overflow:"hidden",position:"fixed",top:0,left:0}),d=(a.title&&(p=a.title+d+d+p),a.messageTop&&(p=a.messageTop+d+d+p),a.messageBottom&&(p=p+d+d+a.messageBottom),l.customize&&(p=l.customize(p,l,t)),$("<textarea readonly/>").val(p).appendTo(i));if(document.queryCommandSupported("copy")){i.appendTo(t.table().container()),d[0].focus(),d[0].select();try{var s=document.execCommand("copy");if(i.remove(),s)return t.buttons.info(t.i18n("buttons.copyTitle","Copy to clipboard"),t.i18n("buttons.copySuccess",{1:"Copied one row to clipboard",_:"Copied %d rows to clipboard"},n.rows),2e3),void this.processing(!1)}catch(e){}}function f(){m.off("click.buttons-copy"),$(document).off(".buttons-copy"),t.buttons.info(!1)}var a=$("<span>"+t.i18n("buttons.copyKeys","Press <i>ctrl</i> or <i>⌘</i> + <i>C</i> to copy the table data<br>to your system clipboard.<br><br>To cancel, click this message or press escape.")+"</span>").append(i),m=(t.buttons.info(t.i18n("buttons.copyTitle","Copy to clipboard"),a,0),d[0].focus(),d[0].select(),$(a).closest(".dt-button-info"));m.on("click.buttons-copy",f),$(document).on("keydown.buttons-copy",function(e){27===e.keyCode&&(f(),r.processing(!1))}).on("copy.buttons-copy cut.buttons-copy",function(){f(),r.processing(!1)})},exportOptions:{},fieldSeparator:"\t",fieldBoundary:"",header:!0,footer:!1,title:"*",messageTop:"*",messageBottom:"*"},DataTable.ext.buttons.csvHtml5={bom:!1,className:"buttons-csv buttons-html5",available:function(){return void 0!==window.FileReader&&window.Blob},text:function(e){return e.i18n("buttons.csv","CSV")},action:function(e,t,o,l){this.processing(!0);var r=_exportData(t,l).str,n=t.buttons.exportInfo(l),a=l.charset;l.customize&&(r=l.customize(r,l,t)),a=!1!==a?(a=a||document.characterSet||document.charset)&&";charset="+a:"",l.bom&&(r=String.fromCharCode(65279)+r),_saveAs(new Blob([r],{type:"text/csv"+a}),n.filename,!0),this.processing(!1)},filename:"*",extension:".csv",exportOptions:{},fieldSeparator:",",fieldBoundary:'"',escapeChar:'"',charset:null,header:!0,footer:!1},DataTable.ext.buttons.excelHtml5={className:"buttons-excel buttons-html5",available:function(){return void 0!==window.FileReader&&void 0!==_jsZip()&&!_isDuffSafari()&&_serialiser},text:function(e){return e.i18n("buttons.excel","Excel")},action:function(e,t,o,s){this.processing(!0);function l(e){return e=excelStrings[e],$.parseXML(e)}function r(e){m=_createNode(y,"row",{attr:{r:f=c+1}});for(var t=0,o=e.length;t<o;t++){var l=createCellPos(t)+""+f,r=null;if(null===e[t]||void 0===e[t]||""===e[t]){if(!0!==s.createEmptyCells)continue;e[t]=""}var n=e[t];e[t]="function"==typeof e[t].trim?e[t].trim():e[t];for(var a=0,d=_excelSpecials.length;a<d;a++){var p=_excelSpecials[a];if(e[t].match&&!e[t].match(/^0\d+/)&&e[t].match(p.match)){var i=e[t].replace(/[^\d\.\-]/g,"");p.fmt&&(i=p.fmt(i)),r=_createNode(y,"c",{attr:{r:l,s:p.style},children:[_createNode(y,"v",{text:i})]});break}}r=r||("number"==typeof e[t]||e[t].match&&e[t].match(/^-?\d+(\.\d+)?([eE]\-?\d+)?$/)&&!e[t].match(/^0\d+/)?_createNode(y,"c",{attr:{t:"n",r:l},children:[_createNode(y,"v",{text:e[t]})]}):(n=n.replace?n.replace(/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F-\x9F]/g,""):n,_createNode(y,"c",{attr:{t:"inlineStr",r:l},children:{row:_createNode(y,"is",{children:{row:_createNode(y,"t",{text:n,attr:{"xml:space":"preserve"}})}})}}))),m.appendChild(r)}u.appendChild(m),c++}function n(e,t){var o=$("mergeCells",y);o[0].appendChild(_createNode(y,"mergeCell",{attr:{ref:"A"+e+":"+createCellPos(t)+e}})),o.attr("count",parseFloat(o.attr("count"))+1),$("row:eq("+(e-1)+") c",y).attr("s","51")}var a,f,m,d=this,c=0,y=l("xl/worksheets/sheet1.xml"),u=y.getElementsByTagName("sheetData")[0],p={_rels:{".rels":l("_rels/.rels")},xl:{_rels:{"workbook.xml.rels":l("xl/_rels/workbook.xml.rels")},"workbook.xml":l("xl/workbook.xml"),"styles.xml":l("xl/styles.xml"),worksheets:{"sheet1.xml":y}},"[Content_Types].xml":l("[Content_Types].xml")},i=t.buttons.exportData(s.exportOptions),I=(s.customizeData&&s.customizeData(i),t.buttons.exportInfo(s));I.title&&(r([I.title]),n(c,i.header.length-1)),I.messageTop&&(r([I.messageTop]),n(c,i.header.length-1)),s.header&&(r(i.header),$("row:last c",y).attr("s","2"));for(var x=c,F=0,h=i.body.length;F<h;F++)r(i.body[F]);a=c,s.footer&&i.footer&&(r(i.footer),$("row:last c",y).attr("s","2")),I.messageBottom&&(r([I.messageBottom]),n(c,i.header.length-1));var b=_createNode(y,"cols");$("worksheet",y).prepend(b);for(var g=0,v=i.header.length;g<v;g++)b.appendChild(_createNode(y,"col",{attr:{min:g+1,max:g+1,width:_excelColWidth(i,g),customWidth:1}}));var w=p.xl["workbook.xml"];$("sheets sheet",w).attr("name",_sheetname(s)),s.autoFilter&&($("mergeCells",y).before(_createNode(y,"autoFilter",{attr:{ref:"A"+x+":"+createCellPos(i.header.length-1)+a}})),$("definedNames",w).append(_createNode(w,"definedName",{attr:{name:"_xlnm._FilterDatabase",localSheetId:"0",hidden:1},text:_sheetname(s)+"!$A$"+x+":"+createCellPos(i.header.length-1)+a}))),s.customize&&s.customize(p,s,t),0===$("mergeCells",y).children().length&&$("mergeCells",y).remove();var w=new(_jsZip()),x={compression:"DEFLATE",type:"blob",mimeType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},B=(_addToZip(w,p),I.filename);175<B&&(B=B.substr(0,175)),w.generateAsync?w.generateAsync(x).then(function(e){_saveAs(e,B),d.processing(!1)}):(_saveAs(w.generate(x),B),this.processing(!1))},filename:"*",extension:".xlsx",exportOptions:{},header:!0,footer:!1,title:"*",messageTop:"*",messageBottom:"*",createEmptyCells:!1,autoFilter:!1,sheetName:""},DataTable.ext.buttons.pdfHtml5={className:"buttons-pdf buttons-html5",available:function(){return void 0!==window.FileReader&&_pdfMake()},text:function(e){return e.i18n("buttons.pdf","PDF")},action:function(e,t,o,l){this.processing(!0);var r=t.buttons.exportData(l.exportOptions),n=t.buttons.exportInfo(l),a=[];l.header&&a.push($.map(r.header,function(e){return{text:"string"==typeof e?e:e+"",style:"tableHeader"}}));for(var d=0,p=r.body.length;d<p;d++)a.push($.map(r.body[d],function(e){return{text:"string"==typeof(e=null==e?"":e)?e:e+"",style:d%2?"tableBodyEven":"tableBodyOdd"}}));l.footer&&r.footer&&a.push($.map(r.footer,function(e){return{text:"string"==typeof e?e:e+"",style:"tableFooter"}}));var i={pageSize:l.pageSize,pageOrientation:l.orientation,content:[{table:{headerRows:1,body:a},layout:"noBorders"}],styles:{tableHeader:{bold:!0,fontSize:11,color:"white",fillColor:"#2d4154",alignment:"center"},tableBodyEven:{},tableBodyOdd:{fillColor:"#f3f3f3"},tableFooter:{bold:!0,fontSize:11,color:"white",fillColor:"#2d4154"},title:{alignment:"center",fontSize:15},message:{}},defaultStyle:{fontSize:10}},t=(n.messageTop&&i.content.unshift({text:n.messageTop,style:"message",margin:[0,0,0,12]}),n.messageBottom&&i.content.push({text:n.messageBottom,style:"message",margin:[0,0,0,12]}),n.title&&i.content.unshift({text:n.title,style:"title",margin:[0,0,0,12]}),l.customize&&l.customize(i,l,t),_pdfMake().createPdf(i));"open"!==l.download||_isDuffSafari()?t.download(n.filename):t.open(),this.processing(!1)},title:"*",filename:"*",extension:".pdf",exportOptions:{},orientation:"portrait",pageSize:"A4",header:!0,footer:!1,messageTop:"*",messageBottom:"*",customize:null,download:"download"};export default DataTable;