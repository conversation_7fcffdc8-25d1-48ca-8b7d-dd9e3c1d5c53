// pdfService.js
const PdfRepository = require('../repositories/pdfDetailes-repository');
class PdfService {
  async createPdf(pdfData) {
    try {
      return await PdfRepository.createPdf(pdfData);
    } catch (error) {
      throw error;
    }
  }

  async getAllPdf() {
    try {
      return await PdfRepository.getAllPdf();
    } catch (error) {
      throw error;
    }
  }

  async getPdfById(pdfId) {
    try {
      return await PdfRepository.getPdfById(pdfId);
    } catch (error) {
      throw error;
    }
  }

  async updatePdf(pdfId, newData) {
    try {
      return await PdfRepository.updatePdf(pdfId, newData);
    } catch (error) {
      throw error;
    }
  }

  async deletePdf(pdfId) {
    try {
      return await PdfRepository.deletePdf(pdfId);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new PdfService();
