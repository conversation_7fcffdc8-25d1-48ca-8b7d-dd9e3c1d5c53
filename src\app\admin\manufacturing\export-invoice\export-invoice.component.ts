import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
// Import specific Angular Material modules instead of MaterialModules
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { HttpClientModule, HttpClient } from '@angular/common/http';
import { ExportInvoiceService, InvoiceData } from './export-invoice.service';
import { Observable, of, catchError, map } from 'rxjs';
import { MasterService } from '../../../services/master.service';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { QualityModalComponent } from './modals/quality-modal/quality-modal.component';

interface InvoiceListItem {
  id: string;
  number: string;
  date: string;
}

@Component({
  selector: 'app-invoice-form',
  templateUrl: './export-invoice.component.html',
  styleUrls: ['./export-invoice.component.css'],
  // Added imports for FormsModule to support ngModel and CommonModule for *ngIf
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    HttpClientModule,
    MatInputModule,
    MatTableModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MatAutocompleteModule,
    MatDialogModule
  ],
  standalone: true
})
export class ExportInvoiceComponent implements OnInit {
  invoiceForm: FormGroup;
  totalPieces: number = 0;
  totalQuantity: number = 0;
  totalCifEuro: string = '0.00';
  totalFOB: string = '0.00';
  grandTotal: string = '0.00';
  signatureImageUrl: string | null = null;
  signatureBase64: string | null = null; // Store base64 for database

  // Invoice selection properties
  showInvoiceForm: boolean = false;
  showOldInvoices: boolean = false;
  selectedOldInvoice: string = '';
  oldInvoices: InvoiceListItem[] = [];
  isLoading: boolean = false;
  isSubmitting: boolean = false;

  // Buyer order and consignee properties
  buyerOrders: any[] = [];
  filteredBuyerOrders: any[] = [];
  buyersOrderNoRefs: string[] = [];
  consignees: any[] = [];
  selectedBuyerOrderId: string = '';
  selectedConsigneeId: string = '';
  buyers: any[] = [];
  selectedBuyer: any = null;
  buyersListText: string = '';

  // Packing list properties
  packingLists: any[] = [];

  // Country and port options
  countries = [
    { value: 'GERMANY', viewValue: 'Germany' },
    { value: 'FRANCE', viewValue: 'France' },
    { value: 'NETHERLANDS', viewValue: 'Netherlands' },
    { value: 'UNITED KINGDOM', viewValue: 'United Kingdom' },
    { value: 'ITALY', viewValue: 'Italy' },
    { value: 'SPAIN', viewValue: 'Spain' },
    { value: 'BELGIUM', viewValue: 'Belgium' },
    { value: 'SWITZERLAND', viewValue: 'Switzerland' },
    { value: 'AUSTRIA', viewValue: 'Austria' },
    { value: 'DENMARK', viewValue: 'Denmark' },
    { value: 'SWEDEN', viewValue: 'Sweden' },
    { value: 'NORWAY', viewValue: 'Norway' },
    { value: 'FINLAND', viewValue: 'Finland' },
    { value: 'PORTUGAL', viewValue: 'Portugal' },
    { value: 'GREECE', viewValue: 'Greece' },
    { value: 'IRELAND', viewValue: 'Ireland' },
    { value: 'POLAND', viewValue: 'Poland' },
    { value: 'CZECH REPUBLIC', viewValue: 'Czech Republic' },
    { value: 'HUNGARY', viewValue: 'Hungary' },
    { value: 'UNITED STATES', viewValue: 'United States' },
    { value: 'CANADA', viewValue: 'Canada' },
    { value: 'AUSTRALIA', viewValue: 'Australia' },
    { value: 'NEW ZEALAND', viewValue: 'New Zealand' },
    { value: 'JAPAN', viewValue: 'Japan' },
    { value: 'CHINA', viewValue: 'China' },
    { value: 'SOUTH KOREA', viewValue: 'South Korea' },
    { value: 'SINGAPORE', viewValue: 'Singapore' },
    { value: 'UNITED ARAB EMIRATES', viewValue: 'United Arab Emirates' },
    { value: 'SAUDI ARABIA', viewValue: 'Saudi Arabia' },
    { value: 'QATAR', viewValue: 'Qatar' },
    { value: 'KUWAIT', viewValue: 'Kuwait' },
    { value: 'BAHRAIN', viewValue: 'Bahrain' },
    { value: 'OMAN', viewValue: 'Oman' }
  ];

  indianPorts = [
    { value: 'MUMBAI', viewValue: 'Mumbai' },
    { value: 'NHAVA SHEVA', viewValue: 'Nhava Sheva' },
    { value: 'CHENNAI', viewValue: 'Chennai' },
    { value: 'KOLKATA', viewValue: 'Kolkata' },
    { value: 'COCHIN', viewValue: 'Cochin' },
    { value: 'TUTICORIN', viewValue: 'Tuticorin' },
    { value: 'VIZAG', viewValue: 'Visakhapatnam' },
    { value: 'KANDLA', viewValue: 'Kandla' },
    { value: 'MUNDRA', viewValue: 'Mundra' },
    { value: 'PIPAVAV', viewValue: 'Pipavav' },
    { value: 'HAZIRA', viewValue: 'Hazira' },
    { value: 'MANGALORE', viewValue: 'Mangalore' },
    { value: 'PARADIP', viewValue: 'Paradip' },
    { value: 'ENNORE', viewValue: 'Ennore' },
    { value: 'KRISHNAPATNAM', viewValue: 'Krishnapatnam' },
    { value: 'KAKINADA', viewValue: 'Kakinada' },
    { value: 'DAHEJ', viewValue: 'Dahej' },
    { value: 'DHAMRA', viewValue: 'Dhamra' },
    { value: 'HALDIA', viewValue: 'Haldia' },
    { value: 'JNPT', viewValue: 'Jawaharlal Nehru Port' }
  ];
  packingListApiUrl: any;

  exporterRefs: string[] = [];
  consigneeRefs: string[] = [];
  otherReferencesRefs: string[] = [];
  buyerIfOtherRefs: string[] = [];
  preCarriageByRefs: string[] = [];
  placeOfReceiptRefs: string[] = [];
  vesselNoRefs: string[] = [];
  portOfDischargeRefs: string[] = [];
  descriptionOfGoodsRefs: string[] = [];
  marksAndNosRefs: string[] = [];
  noOfKindOfPackageRefs: string[] = [];

  isUpdateMode: boolean = false;
  loadedInvoiceId: string | null = null;

  // Track if coming from packing list
  isFromPackingList: boolean = false;

  exporterText: string = '';

  // For signature upload
  onSignatureSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        this.snackBar.open('Please select an image file', 'Close', {
          duration: 3000
        });
        return;
      }

      // Check file size (10KB to 100KB)
      const minSize = 10 * 1024; // 10KB in bytes
      const maxSize = 100 * 1024; // 100KB in bytes

      if (file.size < minSize) {
        this.snackBar.open('Signature image is too small. Minimum size is 10KB', 'Close', {
          duration: 3000
        });
        return;
      }

      if (file.size > maxSize) {
        this.snackBar.open('Signature image is too large. Maximum size is 100KB', 'Close', {
          duration: 3000
        });
        return;
      }

      // Create a URL for preview
      this.signatureImageUrl = URL.createObjectURL(file);

      // Convert to base64 for database storage
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.signatureBase64 = e.target.result; // This includes data:image/jpeg;base64,
        console.log('✅ Signature converted to base64 for database storage');
      };
      reader.readAsDataURL(file);
    }
  }

  // Remove signature
  removeSignature(): void {
    this.signatureImageUrl = null;
    this.signatureBase64 = null;
  }

  // Helper method to load buyers (Promise version)
  private loadBuyersPromise(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.masterService.getAllBuyerList().subscribe({
        next: (buyers: any[]) => {
          this.buyers = buyers;
          console.log('✅ Buyers loaded:', buyers.length);
          resolve();
        },
        error: (error: any) => {
          console.error('❌ Error loading buyers:', error);
          reject(error);
        }
      });
    });
  }

  // Helper method to match and set consignee
  private matchAndSetConsignee(consigneeString: string): void {
    console.log('🔍 Matching consignee:', consigneeString);

    // Parse consignee string to find matching buyer
    const consigneeParts = consigneeString.split(',');
    const consigneeName = consigneeParts[0]?.trim();

    console.log('🔍 Looking for buyer matching:', consigneeName);

    // Find matching buyer by name or customer code
    const matchingBuyer = this.buyers.find(buyer => {
      const nameMatch = buyer.customerName?.toLowerCase().includes(consigneeName?.toLowerCase()) ||
                       consigneeName?.toLowerCase().includes(buyer.customerName?.toLowerCase());
      const codeMatch = buyer.customerCode?.toLowerCase().includes(consigneeName?.toLowerCase()) ||
                       consigneeName?.toLowerCase().includes(buyer.customerCode?.toLowerCase());

      return nameMatch || codeMatch;
    });

    if (matchingBuyer) {
      this.selectedBuyer = matchingBuyer;
      this.selectedConsigneeId = matchingBuyer._id;
      console.log('✅ Found matching buyer:', matchingBuyer.customerName);

      // Update the consignee field with proper format (include customer code)
      const consigneeText = `${matchingBuyer.customerCode} - ${matchingBuyer.customerName},\n${matchingBuyer.customerAddress},\n${matchingBuyer.country} ${matchingBuyer.zipCode}`;
      this.invoiceForm.patchValue({
        consignee: consigneeText
      });
    } else {
      console.log('❌ No matching buyer found for:', consigneeName);
    }
  }

  /**
   * Force uppercase for all text input fields as user types.
   * Usage: (input)="toUppercaseInput($event, 'formControlName')"
   * @param event Input event
   * @param controlName Name of the FormControl
   */
  toUppercaseInput(event: any, controlName: string): void {
    const value = event.target.value?.toUpperCase() || '';
    this.invoiceForm.get(controlName)?.setValue(value, { emitEvent: false });
    // Also update the input's value to avoid cursor jump
    event.target.value = value;
  }

  /**
   * Force uppercase for mat-select fields when an option is selected.
   * Usage: (selectionChange)="toUppercaseSelect($event.value, 'formControlName')"
   * @param value The selected value
   * @param controlName Name of the FormControl
   */
  toUppercaseSelect(value: string, controlName: string): void {
    if (typeof value === 'string') {
      const upperValue = value.toUpperCase();
      this.invoiceForm.get(controlName)?.setValue(upperValue);
    } else {
      this.invoiceForm.get(controlName)?.setValue(value);
    }
  }

  constructor(
    private fb: FormBuilder,
    private invoiceService: ExportInvoiceService,
    private snackBar: MatSnackBar,
    private http: HttpClient,
    private masterService: MasterService, // Inject MasterService
    private router: Router,
    private dialog: MatDialog
  ) {
    this.invoiceForm = this.fb.group({
      invoiceNumber: ['RE-755', Validators.required],
      invoiceDate: [new Date(), Validators.required],
      exporterRef: ['DEFAULT-REF'], // Made optional with default
      exporter: ['M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA', Validators.required],
      buyersOrderNo: ['DEFAULT-ORDER'], // Made optional with default
      buyersOrderDate: [new Date()], // Made optional with default date
      otherReferences: [''],
      consignee: ['DEFAULT CONSIGNEE\nDefault Address\nGermany'], // Made optional with default
      buyerIfOther: ['SAME'],
      preCarriageBy: [''],
      placeOfReceipt: [''],
      originCountry: ['INDIA'],
      destinationCountry: ['Germany'], // Added default
      vesselNo: [''],
      portOfLoading: [''],
      portOfDischarge: ['Hamburg'], // Added default
      finalDestination: ['Germany'], // Added default
      marksAndNos: [''],
      noOfKindOfPackage: [''],
      descriptionOfGoods: ['Indian Hand-Knotted Woolen Carpets'],
      area: ['507f1f77bcf86cd799439011'], // Default ObjectId for area
      goods: this.fb.array([
        this.createGoodsItem('Default Quality', 'Default Design', 1, 1, 100), // Start with default values
      ]),
      addedFreight: ['0.00'],
      insurance: ['0.00'],
      igstPercentage: ['0.00'],
      igst: ['0.00'],
      amountChargeableWords: ['One Hundred Only'], // Added default

      signature: [''] // Use FormControl for signature, not an object
    });

    this.goods.valueChanges.subscribe(() => this.calculateTotals());
    this.invoiceForm.get('addedFreight')?.valueChanges.subscribe(() => this.calculateTotals());
    this.calculateTotals(); // Initial calculation
  }

  createGoodsItem(
    quality: string = '',
    design: string = '',
    pieces: number = 0,
    quantitySqMeter: number = 0,
    rateFOB: number = 0,
    extra?: {
      srNo?: number,
      baleNo?: string,
      pcsNo?: string,
      carpetNo?: string,
      colour?: string,
      size?: string,
      area?: number,
      tArea?: number,
      remarks?: string
    }
  ): FormGroup {
    return this.fb.group({
      srNo: [extra?.srNo || null],
      baleNo: [extra?.baleNo || ''],
      pcsNo: [extra?.pcsNo || ''],
      carpetNo: [extra?.carpetNo || ''],
      quality: [quality],
      design: [design],
      colour: [extra?.colour || ''],
      size: [extra?.size || ''],
      area: [extra?.area ?? quantitySqMeter],
      tArea: [extra?.tArea || null],
      pieces: [pieces],
      quantitySqMeter: [quantitySqMeter],
      rateFOB: [rateFOB],
      amountFOB: [{ value: ((quantitySqMeter || 1) * (rateFOB || 0)).toFixed(2), disabled: true }],
      remarks: [extra?.remarks || '']
    });
  }

  // Calculate area based on length, width and unit
  calculateArea(length: number, width: number, unit: string): number {
    const area = length * width;
    // If unit is sqyd, convert from sqft to sqyd (1 sqyd = 9 sqft)
    return unit === 'sqyd' ? area / 9 : area;
  }

  // Debug form validation
  debugFormValidation(): void {
    console.log('=== FORM VALIDATION DEBUG ===');
    console.log('Form valid:', this.invoiceForm.valid);
    console.log('Form errors:', this.invoiceForm.errors);

    Object.keys(this.invoiceForm.controls).forEach(key => {
      const control = this.invoiceForm.get(key);
      if (control && control.invalid) {
        console.log(`Field '${key}' is invalid:`, control.errors);
        console.log(`Field '${key}' value:`, control.value);
      }
    });

    // Check goods array
    const goodsArray = this.invoiceForm.get('goods') as FormArray;
    if (goodsArray && goodsArray.invalid) {
      console.log('Goods array is invalid');
      goodsArray.controls.forEach((control, index) => {
        if (control.invalid) {
          console.log(`Goods item ${index} is invalid:`, control.errors);
          console.log(`Goods item ${index} value:`, control.value);
        }
      });
    }
  }

  ngOnInit(): void {
    console.log('🚀 Invoice component ngOnInit started');
    console.log('🚀 Navigation history state:', history.state);

    // If navigated from packing list with a selected invoice, load it directly
    if (history.state && history.state.invoiceId) {
      console.log('✅ Found invoice ID in navigation state:', history.state.invoiceId);
      console.log('✅ From packing list:', history.state.fromPackingList);
      console.log('✅ Update existing invoice:', history.state.updateExistingInvoice);

      this.showInvoiceForm = true; // Ensure form is visible immediately when coming from packing list
      this.isFromPackingList = history.state.fromPackingList || false; // Track if coming from packing list
      const invoiceId = history.state.invoiceId;
      this.isLoading = true;

      console.log('🔄 Loading invoice with ID:', invoiceId);
      this.invoiceService.getInvoiceById(invoiceId).subscribe({
        next: (invoice) => {
          console.log('✅ Successfully loaded invoice data:', invoice);

          // Patch the form with all invoice data (reuse loadOldInvoice logic)
          const invoiceDate = invoice.invoiceDate ? new Date(invoice.invoiceDate) : new Date();
          const buyersOrderDate = invoice.buyersOrderDate ? this.parseDate(invoice.buyersOrderDate) : null;

          console.log('📝 Preparing patch data for form...');
          const patchData = {
            invoiceNumber: invoice.invoiceNumber ,
            invoiceDate: invoiceDate,
            exporterRef: invoice.exporterRef || '',
            exporter: 'M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA',
            buyersOrderNo: invoice.buyersOrderNo || '',
            buyersOrderDate: buyersOrderDate,
            otherReferences: invoice.otherReferences || '',
            consignee: invoice.consignee || '',
            buyerIfOther: invoice.buyerIfOther || 'SAME',
            preCarriageBy: invoice.preCarriageBy || '',
            placeOfReceipt: invoice.placeOfReceipt || '',
            originCountry: invoice.originCountry || 'INDIA',
            destinationCountry: invoice.destinationCountry || '',
            vesselNo: invoice.vesselNo || '',
            portOfLoading: invoice.portOfLoading || '',
            portOfDischarge: invoice.portOfDischarge || '',
            finalDestination: invoice.finalDestination || '',
            marksAndNos: invoice.marksAndNos || '',
            noOfKindOfPackage: invoice.noOfKindOfPackage || '',
            descriptionOfGoods: invoice.descriptionOfGoods || 'Indian Hand-Knotted Woolen Carpets',
            area: invoice.area || '',
            addedFreight: invoice.addedFreight || '0.00',
            amountChargeableWords: invoice.amountChargeableWords || '',
            insurance: '0.00',
            igstPercentage: '0.00',
            igst: '0.00'
          };

          console.log('📝 Patch data to be applied:', patchData);
          this.invoiceForm.patchValue(patchData);
          console.log('📝 Form values after patching:', this.invoiceForm.value);
          // Signature
          if (invoice.signature) {
            this.signatureBase64 = invoice.signature;
            this.signatureImageUrl = invoice.signature;
          } else {
            this.signatureBase64 = null;
            this.signatureImageUrl = null;
          }
          // If coming from packing list, load goods data from localStorage
          if (history.state.fromPackingList && history.state.loadPackingListGoods) {
            console.log('🔄 Loading goods from localStorage for existing invoice update');
            this.loadGoodsFromLocalStorage();
          } else {
            // Load existing goods from invoice
            while (this.goods.length) { this.goods.removeAt(0); }
            if (invoice.goods && invoice.goods.length > 0) {
              invoice.goods.forEach((item: any) => {
                this.goods.push(this.createGoodsItem(
                  item.quality || '',
                  item.design || '',
                  item.pieces || 0,
                  item.quantitySqMeter || 0,
                  item.rateFOB || 0
                ));
              });
            } else {
              this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
            }
          }
          this.finishInvoiceLoad();
        },
        error: (error) => {
          console.error('❌ Error loading invoice:', error);
          console.error('❌ Invoice ID that failed:', history.state.invoiceId);
          this.isLoading = false;
          this.showInvoiceForm = false;

          // Show error message to user
          this.snackBar.open('Failed to load invoice data. Please try again.', 'Close', {
            duration: 5000
          });
        }
      });
      return;
    }

    console.log('📋 Normal initialization - no invoice ID in navigation state');
    console.log('📋 showInvoiceForm:', this.showInvoiceForm);
    console.log('📋 showOldInvoices:', this.showOldInvoices);

    // Load Exporter's Ref options from localStorage if available
    const savedRefs = localStorage.getItem('exporterRefs');
    if (savedRefs) {
      this.exporterRefs = JSON.parse(savedRefs);
    } else {
      this.exporterRefs = [];
    }

    // Load Consignee options
    const savedConsigneeRefs = localStorage.getItem('consigneeRefs');
    this.consigneeRefs = savedConsigneeRefs ? JSON.parse(savedConsigneeRefs) : [];
    // Load Other Reference(s) options
    const savedOtherReferencesRefs = localStorage.getItem('otherReferencesRefs');
    this.otherReferencesRefs = savedOtherReferencesRefs ? JSON.parse(savedOtherReferencesRefs) : [];
    // Load Buyer (if other than consignee) options
    const savedBuyerIfOtherRefs = localStorage.getItem('buyerIfOtherRefs');
    this.buyerIfOtherRefs = savedBuyerIfOtherRefs ? JSON.parse(savedBuyerIfOtherRefs) : [];
    // Load Pre-Carriage By options
    const savedPreCarriageByRefs = localStorage.getItem('preCarriageByRefs');
    this.preCarriageByRefs = savedPreCarriageByRefs ? JSON.parse(savedPreCarriageByRefs) : [];
    // Load Place of Receipt options
    const savedPlaceOfReceiptRefs = localStorage.getItem('placeOfReceiptRefs');
    this.placeOfReceiptRefs = savedPlaceOfReceiptRefs ? JSON.parse(savedPlaceOfReceiptRefs) : [];
    // Load Vessel No options
    const savedVesselNoRefs = localStorage.getItem('vesselNoRefs');
    this.vesselNoRefs = savedVesselNoRefs ? JSON.parse(savedVesselNoRefs) : [];
    // Load Port of Discharge options
    const savedPortOfDischargeRefs = localStorage.getItem('portOfDischargeRefs');
    this.portOfDischargeRefs = savedPortOfDischargeRefs ? JSON.parse(savedPortOfDischargeRefs) : [];
    // Load Description of Goods options
    const savedDescriptionOfGoodsRefs = localStorage.getItem('descriptionOfGoodsRefs');
    this.descriptionOfGoodsRefs = savedDescriptionOfGoodsRefs ? JSON.parse(savedDescriptionOfGoodsRefs) : [];
    // Load Marks & Cont No. options
    const savedMarksAndNosRefs = localStorage.getItem('marksAndNosRefs');
    this.marksAndNosRefs = savedMarksAndNosRefs ? JSON.parse(savedMarksAndNosRefs) : [];
    // Load No & Kind of Package options
    const savedNoOfKindOfPackageRefs = localStorage.getItem('noOfKindOfPackageRefs');
    this.noOfKindOfPackageRefs = savedNoOfKindOfPackageRefs ? JSON.parse(savedNoOfKindOfPackageRefs) : [];
    // Load Buyer's Order No. options
    const savedBuyersOrderNoRefs = localStorage.getItem('buyersOrderNoRefs');
    this.buyersOrderNoRefs = savedBuyersOrderNoRefs ? JSON.parse(savedBuyersOrderNoRefs) : [];

    // Load existing invoices when component initializes
    this.loadInvoices();

    // Load buyer orders
    this.loadBuyerOrders();

    // Load consignees and buyers together (they're the same data)
    this.loadBuyers();

    // Fetch areas (SizeMaster) for dropdown
    this.masterService.getAllSizesList().subscribe({
      next: (sizes: any) => {
        if (Array.isArray(sizes)) {
          this.areas = sizes;
        } else if (sizes && Array.isArray(sizes.data)) {
          this.areas = sizes.data;
        } else {
          this.areas = [];
        }
      },
      error: (err) => {
        this.areas = [];
      }
    });
  }

  private saveRefs(key: string, arr: string[]) {
    localStorage.setItem(key, JSON.stringify(arr));
  }

  onExporterRefBlur() {
    const value = this.invoiceForm.get('exporterRef')?.value;
    if (value && !this.exporterRefs.includes(value)) {
      this.exporterRefs.push(value);
      this.saveRefs('exporterRefs', this.exporterRefs);
    }
  }

  removeExporterRef(ref: string) {
    const index = this.exporterRefs.indexOf(ref);
    if (index > -1) {
      this.exporterRefs.splice(index, 1);
      this.saveRefs('exporterRefs', this.exporterRefs);
      // If the removed ref is currently selected, clear the field
      if (this.invoiceForm.get('exporterRef')?.value === ref) {
        this.invoiceForm.get('exporterRef')?.setValue('');
      }
    }
  }

  onConsigneeBlur() {
    const value = this.invoiceForm.get('consignee')?.value;
    if (value && !this.consigneeRefs.includes(value)) {
      this.consigneeRefs.push(value);
      this.saveRefs('consigneeRefs', this.consigneeRefs);
    }
  }

  removeConsigneeRef(ref: string) {
    const index = this.consigneeRefs.indexOf(ref);
    if (index > -1) {
      this.consigneeRefs.splice(index, 1);
      this.saveRefs('consigneeRefs', this.consigneeRefs);
      // If the removed ref is currently selected, clear the field
      if (this.invoiceForm.get('consignee')?.value === ref) {
        this.invoiceForm.get('consignee')?.setValue('');
      }
    }
  }

  onConsigneeOptionSelected(event: any) {
    // This method handles when an option is selected from autocomplete
    // The value is already set by the autocomplete, so we just need to save it
    this.onConsigneeBlur();
  }

  onBuyerSelected(buyer: any) {
    // When a buyer is selected from the buyer master dropdown
    this.selectedBuyer = buyer;
    console.log('🔄 Buyer selected:', buyer);

    // Format consignee text with all required details
    const consigneeText = `${buyer.customerCode || ''} - ${buyer.customerName || ''},
${buyer.customerAddress || ''},
${buyer.country || ''} ${buyer.zipCode || ''}`.trim();

    console.log('📝 Formatted consignee text:', consigneeText);

    this.invoiceForm.patchValue({
      consignee: consigneeText
    });

    // Save to consignee refs if not already present
    if (!this.consigneeRefs.includes(consigneeText)) {
      this.consigneeRefs.push(consigneeText);
      this.saveRefs('consigneeRefs', this.consigneeRefs);
    }
  }



  onOtherReferencesBlur() {
    const value = this.invoiceForm.get('otherReferences')?.value;
    if (value && !this.otherReferencesRefs.includes(value)) {
      this.otherReferencesRefs.push(value);
      this.saveRefs('otherReferencesRefs', this.otherReferencesRefs);
    }
  }

  removeOtherReferencesRef(ref: string) {
    const idx = this.otherReferencesRefs.indexOf(ref);
    if (idx > -1) {
      this.otherReferencesRefs.splice(idx, 1);
      this.saveRefs('otherReferencesRefs', this.otherReferencesRefs);
      if (this.invoiceForm.get('otherReferences')?.value === ref) {
        this.invoiceForm.get('otherReferences')?.setValue('');
      }
    }
  }

  onBuyerIfOtherBlur() {
    const value = this.invoiceForm.get('buyerIfOther')?.value;
    if (value && !this.buyerIfOtherRefs.includes(value)) {
      this.buyerIfOtherRefs.push(value);
      this.saveRefs('buyerIfOtherRefs', this.buyerIfOtherRefs);
    }
  }

  removeBuyerIfOtherRef(ref: string) {
    const idx = this.buyerIfOtherRefs.indexOf(ref);
    if (idx > -1) {
      this.buyerIfOtherRefs.splice(idx, 1);
      this.saveRefs('buyerIfOtherRefs', this.buyerIfOtherRefs);
      if (this.invoiceForm.get('buyerIfOther')?.value === ref) {
        this.invoiceForm.get('buyerIfOther')?.setValue('');
      }
    }
  }

  onPreCarriageByBlur() {
    const value = this.invoiceForm.get('preCarriageBy')?.value;
    if (value && !this.preCarriageByRefs.includes(value)) {
      this.preCarriageByRefs.push(value);
      this.saveRefs('preCarriageByRefs', this.preCarriageByRefs);
    }
  }

  removePreCarriageByRef(ref: string) {
    const idx = this.preCarriageByRefs.indexOf(ref);
    if (idx > -1) {
      this.preCarriageByRefs.splice(idx, 1);
      this.saveRefs('preCarriageByRefs', this.preCarriageByRefs);
      if (this.invoiceForm.get('preCarriageBy')?.value === ref) {
        this.invoiceForm.get('preCarriageBy')?.setValue('');
      }
    }
  }

  onPlaceOfReceiptBlur() {
    const value = this.invoiceForm.get('placeOfReceipt')?.value;
    if (value && !this.placeOfReceiptRefs.includes(value)) {
      this.placeOfReceiptRefs.push(value);
      this.saveRefs('placeOfReceiptRefs', this.placeOfReceiptRefs);
    }
  }

  removePlaceOfReceiptRef(ref: string) {
    const idx = this.placeOfReceiptRefs.indexOf(ref);
    if (idx > -1) {
      this.placeOfReceiptRefs.splice(idx, 1);
      this.saveRefs('placeOfReceiptRefs', this.placeOfReceiptRefs);
      if (this.invoiceForm.get('placeOfReceipt')?.value === ref) {
        this.invoiceForm.get('placeOfReceipt')?.setValue('');
      }
    }
  }

  onVesselNoBlur() {
    const value = this.invoiceForm.get('vesselNo')?.value;
    if (value && !this.vesselNoRefs.includes(value)) {
      this.vesselNoRefs.push(value);
      this.saveRefs('vesselNoRefs', this.vesselNoRefs);
    }
  }

  removeVesselNoRef(ref: string) {
    const idx = this.vesselNoRefs.indexOf(ref);
    if (idx > -1) {
      this.vesselNoRefs.splice(idx, 1);
      this.saveRefs('vesselNoRefs', this.vesselNoRefs);
      if (this.invoiceForm.get('vesselNo')?.value === ref) {
        this.invoiceForm.get('vesselNo')?.setValue('');
      }
    }
  }

  onPortOfDischargeBlur() {
    const value = this.invoiceForm.get('portOfDischarge')?.value;
    if (value && !this.portOfDischargeRefs.includes(value)) {
      this.portOfDischargeRefs.push(value);
      this.saveRefs('portOfDischargeRefs', this.portOfDischargeRefs);
    }
  }

  removePortOfDischargeRef(ref: string) {
    const idx = this.portOfDischargeRefs.indexOf(ref);
    if (idx > -1) {
      this.portOfDischargeRefs.splice(idx, 1);
      this.saveRefs('portOfDischargeRefs', this.portOfDischargeRefs);
      if (this.invoiceForm.get('portOfDischarge')?.value === ref) {
        this.invoiceForm.get('portOfDischarge')?.setValue('');
      }
    }
  }

  onDescriptionOfGoodsBlur() {
    const value = this.invoiceForm.get('descriptionOfGoods')?.value;
    if (value && !this.descriptionOfGoodsRefs.includes(value)) {
      this.descriptionOfGoodsRefs.push(value);
      this.saveRefs('descriptionOfGoodsRefs', this.descriptionOfGoodsRefs);
    }
  }

  removeDescriptionOfGoodsRef(ref: string) {
    const idx = this.descriptionOfGoodsRefs.indexOf(ref);
    if (idx > -1) {
      this.descriptionOfGoodsRefs.splice(idx, 1);
      this.saveRefs('descriptionOfGoodsRefs', this.descriptionOfGoodsRefs);
      if (this.invoiceForm.get('descriptionOfGoods')?.value === ref) {
        this.invoiceForm.get('descriptionOfGoods')?.setValue('');
      }
    }
  }

  onMarksAndNosBlur() {
    const value = this.invoiceForm.get('marksAndNos')?.value;
    if (value && !this.marksAndNosRefs.includes(value)) {
      this.marksAndNosRefs.push(value);
      this.saveRefs('marksAndNosRefs', this.marksAndNosRefs);
    }
  }

  removeMarksAndNosRef(ref: string) {
    const idx = this.marksAndNosRefs.indexOf(ref);
    if (idx > -1) {
      this.marksAndNosRefs.splice(idx, 1);
      this.saveRefs('marksAndNosRefs', this.marksAndNosRefs);
      if (this.invoiceForm.get('marksAndNos')?.value === ref) {
        this.invoiceForm.get('marksAndNos')?.setValue('');
      }
    }
  }

  onNoOfKindOfPackageBlur() {
    const value = this.invoiceForm.get('noOfKindOfPackage')?.value;
    if (value && !this.noOfKindOfPackageRefs.includes(value)) {
      this.noOfKindOfPackageRefs.push(value);
      this.saveRefs('noOfKindOfPackageRefs', this.noOfKindOfPackageRefs);
    }
  }

  removeNoOfKindOfPackageRef(ref: string) {
    const idx = this.noOfKindOfPackageRefs.indexOf(ref);
    if (idx > -1) {
      this.noOfKindOfPackageRefs.splice(idx, 1);
      this.saveRefs('noOfKindOfPackageRefs', this.noOfKindOfPackageRefs);
      if (this.invoiceForm.get('noOfKindOfPackage')?.value === ref) {
        this.invoiceForm.get('noOfKindOfPackage')?.setValue('');
      }
    }
  }

  // Add property to hold areas
  public areas: any[] = [];

  // Removed loadConsignees - now handled by loadBuyers for better performance

  // Load buyer orders from API or service
  loadBuyerOrders(): void {
    console.log('🔄 Loading buyer orders...');
    this.invoiceService.getAllBuyerOrders().subscribe({
      next: (orders: any[]) => {
        console.log('✅ Buyer orders loaded:', orders);
        this.buyerOrders = orders;
        this.filteredBuyerOrders = [...orders]; // Initialize filtered list
        console.log('📋 Available orders for dropdown:', this.buyerOrders.map(o => ({ id: o.id, orderNo: o.orderNo, buyerName: o.buyerName })));
      },
      error: (error: any) => {
        console.error('❌ Error loading buyer orders:', error);
        this.snackBar.open('Failed to load buyer orders', 'Close', {
          duration: 3000
        });
      }
    });
  }

  // Load buyers from MasterService (also populates consignees)
  loadBuyers(): void {
    console.log('🔄 Loading buyers/consignees...');
    this.masterService.getAllBuyerList().subscribe({
      next: (buyers: any[]) => {
        console.log('✅ Buyers loaded successfully:', buyers.length);
        this.buyers = buyers;
        this.consignees = buyers; // Same data for consignees
        this.updateBuyersListText();
      },
      error: (error: any) => {
        console.error('❌ Error loading buyers:', error);
        this.snackBar.open('Failed to load buyers', 'Close', { duration: 3000 });
      }
    });
  }

  updateBuyersListText(): void {
    this.buyersListText = this.buyers.map(buyer =>
      `${buyer.customerName}\n${buyer.customerAddress}\n${buyer.country} ${buyer.zipCode}`
    ).join('\n\n');
  }

  // When buyer order selection changes
  onBuyerOrderChange(orderId: string): void {
    this.selectedBuyerOrderId = orderId;
    const selectedOrder = this.buyerOrders.find(order => order.id === orderId);
    if (selectedOrder) {
      this.invoiceForm.patchValue({
        buyersOrderNo: selectedOrder.orderNo, // Use actual order number, not ObjectId
        buyersOrderDate: selectedOrder.orderDate
      });
    }
  }

  // New methods for autocomplete functionality
  onBuyerOrderInputChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    console.log('🔍 Buyer order input changed:', value);

    // Simple implementation - just add to refs if not exists
    if (value && value.trim()) {
      const upperValue = value.toUpperCase();
      const existsInOrders = this.buyerOrders.some(order =>
        order.orderNo.toLowerCase() === value.toLowerCase()
      );
      const existsInRefs = this.buyersOrderNoRefs.includes(upperValue);

      if (!existsInOrders && !existsInRefs) {
        this.buyersOrderNoRefs.unshift(upperValue);
        console.log('➕ Added new buyer order ref:', upperValue);
      }
    }
  }

  onBuyerOrderSelected(orderNo: string): void {
    console.log('🔍 Buyer order selected:', orderNo);
    console.log('🔍 Available buyer orders:', this.buyerOrders);

    // Find the selected order
    const selectedOrder = this.buyerOrders.find(order => order.orderNo === orderNo);
    if (selectedOrder) {
      console.log('✅ Found matching order:', selectedOrder);
      this.selectedBuyerOrderId = selectedOrder.id;
      this.invoiceForm.patchValue({
        buyersOrderNo: orderNo,
        buyersOrderDate: selectedOrder.orderDate
      });
      console.log('✅ Form updated with order details');
    } else {
      console.log('⚠️ Order not found in list, using custom entry');
      // Custom entry
      this.invoiceForm.patchValue({
        buyersOrderNo: orderNo
      });
    }
  }

  onBuyersOrderNoBlur(): void {
    const value = this.invoiceForm.get('buyersOrderNo')?.value;
    if (value && !this.buyersOrderNoRefs.includes(value)) {
      this.buyersOrderNoRefs.push(value);
      this.saveRefs('buyersOrderNoRefs', this.buyersOrderNoRefs);
    }
  }

  removeBuyersOrderNoRef(ref: string): void {
    const index = this.buyersOrderNoRefs.indexOf(ref);
    if (index > -1) {
      this.buyersOrderNoRefs.splice(index, 1);
      this.saveRefs('buyersOrderNoRefs', this.buyersOrderNoRefs);
    }
  }

  // When consignee selection changes (legacy method - now handled by onBuyerSelected)
  onConsigneeChange(buyerId: string): void {
    this.selectedConsigneeId = buyerId;
    this.selectedBuyer = this.buyers.find(b => b._id === buyerId);
    if (this.selectedBuyer) {
      const consigneeText = `${this.selectedBuyer.customerCode} - ${this.selectedBuyer.customerName},\n${this.selectedBuyer.customerAddress},\n${this.selectedBuyer.country} ${this.selectedBuyer.zipCode}`;
      this.invoiceForm.patchValue({
        consignee: consigneeText
      });
    }
  }

  // Load packing lists from the API
  loadPackingLists(): void {
    this.isLoading = true;
    this.invoiceService.getAllPackingLists().subscribe({
      next: (packingLists: any[]) => {
        this.packingLists = packingLists;
        console.log('Loaded packing lists:', packingLists);

        if (packingLists.length > 0) {
          // Show dialog or prompt to select a packing list
          this.selectPackingList();
        } else {
          this.snackBar.open('No packing lists found', 'Close', {
            duration: 3000
          });
          this.isLoading = false;
        }
      },
      error: (error: any) => {
        console.error('Error loading packing lists:', error);
        this.snackBar.open('Failed to load packing lists', 'Close', {
          duration: 3000
        });
        this.isLoading = false;
      }
    });
  }

  // Simple method to select a packing list
  selectPackingList(): void {
    // For simplicity, just show a snackbar with options
    const packingListOptions = this.packingLists.map(pl => pl._id).join(', ');
    this.snackBar.open(`Select a packing list ID: ${packingListOptions}`, 'Close', {
      duration: 10000
    });

    // In a real implementation, you would show a dialog or dropdown
    // For now, just use the first packing list
    if (this.packingLists.length > 0) {
      this.loadGoodsFromPackingList(this.packingLists[0]._id);
    } else {
      this.isLoading = false;
    }
  }

  // Load goods from packing list
  loadGoodsFromPackingList(packingListId: string): void {
    if (!packingListId) {
      this.isLoading = false;
      return;
    }

    this.invoiceService.getPackingListById(packingListId).subscribe({
      next: (packingList: any) => {
        if (packingList && packingList.items && packingList.items.length > 0) {
          // Clear existing goods
          while (this.goods.length) {
            this.goods.removeAt(0);
          }

          // Add goods from packing list
          packingList.items.forEach((item: any) => {
            this.goods.push(this.createGoodsItem(
              item.quality || '',
              item.design || '',
              item.pieces || 0,
              item.area || 0,
              0 // Default rate
            ));
          });

          // Update form with packing list data if available
          if (packingList.buyerOrder) {
            this.invoiceForm.patchValue({
              buyersOrderNo: packingList.buyerOrder.orderNo || '', // Use actual order number
              buyersOrderDate: this.parseDate(packingList.buyerOrder.orderDate || '')
            });
          }

          if (packingList.buyer) {
            this.invoiceForm.patchValue({
              consignee: packingList.buyer._id || '' // Use ObjectId
            });
          }

          // Calculate totals
          this.calculateTotals();

          this.snackBar.open('Goods loaded from packing list', 'Close', {
            duration: 3000
          });
        } else {
          this.snackBar.open('No items found in packing list', 'Close', {
            duration: 3000
          });
        }


        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading packing list:', error);
        this.snackBar.open('Failed to load packing list', 'Close', {
          duration: 3000
        });
        this.isLoading = false;
      }
    });
  }

  get goods(): FormArray {
    return this.invoiceForm.get('goods') as FormArray;
  }

  addGoodsItem(): void {
    this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
  }

  removeGoodsItem(index: number): void {
    if (this.goods.length > 1) {
      this.goods.removeAt(index);
      this.calculateTotals();
    }
  }

  calculateTotals(): void {
    let totalPieces = 0;
    let totalQuantity = 0;
    let totalFOB = 0;

    this.goods.controls.forEach(group => {
      const pieces = parseInt(group.get('pieces')?.value) || 0;
      let quantitySqMeter = parseFloat(group.get('quantitySqMeter')?.value) || 0;
      const rateFOB = parseFloat(group.get('rateFOB')?.value) || 0;

      // Round quantity to 2 decimal places to prevent floating point errors
      quantitySqMeter = Math.round(quantitySqMeter * 100) / 100;

      // Update the form control with the rounded value to ensure consistency
      group.get('quantitySqMeter')?.setValue(quantitySqMeter, { emitEvent: false });

      // Calculate amount FOB
      const amountFOB = quantitySqMeter * rateFOB;
      group.get('amountFOB')?.setValue(amountFOB.toFixed(2), { emitEvent: false });

      totalPieces += pieces;
      totalQuantity += quantitySqMeter;
      totalFOB += amountFOB;
    });

    this.totalPieces = totalPieces;
    // Round total quantity to prevent floating point errors like 18.119999999999997
    this.totalQuantity = Math.round(totalQuantity * 100) / 100;
    this.totalFOB = totalFOB.toFixed(2);
    this.totalCifEuro = totalFOB.toFixed(2); // For backward compatibility

    // Calculate additional charges
    const addedFreight = parseFloat(this.invoiceForm.get('addedFreight')?.value || '0');
    const insurance = parseFloat(this.invoiceForm.get('insurance')?.value || '0');

    // Calculate IGST
    const igstPercentage = parseFloat(this.invoiceForm.get('igstPercentage')?.value || '0');
    const igstAmount = (totalFOB * igstPercentage) / 100;
    this.invoiceForm.get('igst')?.setValue(igstAmount.toFixed(2), { emitEvent: false });

    // Calculate grand total
    const grandTotal = totalFOB + addedFreight + insurance + igstAmount;
    this.grandTotal = grandTotal.toFixed(2);

    // Update amount in words
    const amountInWords = this.convertNumberToWords(Number(grandTotal));
    this.invoiceForm.get('amountChargeableWords')?.setValue(`EURO ${amountInWords} ONLY`, { emitEvent: false });

    // Note: "No & Kind of Package" is populated from packing list data in loadGoodsFromLocalStorage()
    // Do not override it here in calculateTotals() to preserve the correct bale count
  }

  submitForm(): void {
    // Patch consignee to string before submit
    const consigneeValue = this.invoiceForm.get('consignee')?.value;
    if (consigneeValue && typeof consigneeValue === 'object') {
      // If it's an object, build the string
      const c: any = consigneeValue;
      const consigneeString = [
        c.customerName || c.name || '',
        c.customerAddress || c.address || '',
        c.country || '',
        c.zipCode || ''
      ].filter(Boolean).join(', ');
      this.invoiceForm.get('consignee')?.setValue(consigneeString, { emitEvent: false });
    }
    if (this.isUpdateMode) {
      this.updateInvoice();
    } else {
      this.createInvoice();
    }
  }

  createInvoice(): void {
    if (this.invoiceForm.valid) {
      this.isSubmitting = true;

      // Prepare the invoice data
      const formData = this.invoiceForm.getRawValue();
      // Send Date objects directly to service (service will handle ISO conversion)
      const invoiceDate = formData.invoiceDate ? new Date(formData.invoiceDate) : new Date();
      const buyersOrderDate = formData.buyersOrderDate ? new Date(formData.buyersOrderDate) : null;
      delete formData.invoiceDate;

      const invoiceData: InvoiceData = {
        ...formData,
        invoiceDate: invoiceDate instanceof Date && !isNaN(invoiceDate.getTime()) ? invoiceDate.toISOString() : invoiceDate, // Always send ISO string
        area: formData.area, // Add area ObjectId
        buyersOrderNo: formData.buyersOrderNo, // Should be ObjectId string
        buyersOrderDate: buyersOrderDate instanceof Date && !isNaN(buyersOrderDate.getTime()) ? buyersOrderDate.toISOString() : buyersOrderDate,
        totalPieces: this.totalPieces,
        totalQuantity: this.totalQuantity,
        totalCifEuro: this.totalCifEuro.toString(),
        grandTotal: this.grandTotal.toString(),
        signature: this.signatureBase64 || '' // Use base64 for database storage
      } as any;

      // Use the service to create the invoice (handles backend structure)
      this.invoiceService.createInvoice(invoiceData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.snackBar.open('Invoice saved successfully!', 'Close', {
            duration: 3000
          });
          // this.createNewInvoice(); // Removed to prevent generating a new invoice after save
          this.loadInvoices();
        },
        error: (error) => {
          console.error('Error saving invoice:', error);
          this.isSubmitting = false;
          this.snackBar.open('Failed to save invoice. Please try again.', 'Close', {
            duration: 3000
          });
        }
      });
    } else {
      this.invoiceForm.markAllAsTouched();
      this.snackBar.open('Please fill all required fields.', 'Close', {
        duration: 3000
      });
    }
  }

  updateInvoice(): void {
    if (this.invoiceForm.valid && this.loadedInvoiceId) {
      this.isSubmitting = true;
      const formData = this.invoiceForm.getRawValue();

      // Do NOT append date to invoice number, keep as RE-XXX only
      // Send Date objects directly to service (service will handle ISO conversion)
      const invoiceDate = formData.invoiceDate ? new Date(formData.invoiceDate) : new Date();
      const buyersOrderDate = formData.buyersOrderDate ? new Date(formData.buyersOrderDate) : null;
      delete formData.invoiceDate;

      // Compose InvoiceData
      const invoiceData: InvoiceData = {
        ...formData,
        invoiceDate: invoiceDate instanceof Date && !isNaN(invoiceDate.getTime()) ? invoiceDate.toISOString() : invoiceDate, // Always send ISO string
        area: formData.area, // Add area ObjectId
        buyersOrderNo: formData.buyersOrderNo, // Should be ObjectId string
        buyersOrderDate: buyersOrderDate instanceof Date && !isNaN(buyersOrderDate.getTime()) ? buyersOrderDate.toISOString() : buyersOrderDate,
        totalPieces: this.totalPieces,
        totalQuantity: this.totalQuantity,
        totalCifEuro: this.totalCifEuro.toString(),
        grandTotal: this.grandTotal.toString(),
        signature: this.signatureBase64 || '' // Use base64 for database storage
      } as any;

      console.log('🔄 Updating invoice with ID:', this.loadedInvoiceId);
      console.log('🔄 Invoice data for update:', invoiceData);

      this.invoiceService.updateInvoice(this.loadedInvoiceId, invoiceData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.snackBar.open('Invoice updated successfully!', 'Close', { duration: 3000 });
          // this.createNewInvoice(); // Removed to keep user on the same invoice after update
          this.loadInvoices();
          this.isUpdateMode = false;
          this.loadedInvoiceId = null;
        },
        error: (error) => {
          this.isSubmitting = false;
          this.snackBar.open('Failed to update invoice. Please try again.', 'Close', { duration: 3000 });
        }
      });
    } else {
      this.invoiceForm.markAllAsTouched();
      this.snackBar.open('Please fill all required fields.', 'Close', { duration: 3000 });
    }
  }

  // Save & Print functionality for new invoices
  saveAndPrint(): void {
    // Patch consignee to string before submit
    const consigneeValue = this.invoiceForm.get('consignee')?.value;
    if (consigneeValue && typeof consigneeValue === 'object') {
      const c: any = consigneeValue;
      const consigneeString = [
        c.customerName || c.name || '',
        c.customerAddress || c.address || '',
        c.country || '',
        c.zipCode || ''
      ].filter(Boolean).join(', ');
      this.invoiceForm.get('consignee')?.setValue(consigneeString, { emitEvent: false });
    }

    if (this.invoiceForm.valid) {
      this.isSubmitting = true;

      // Prepare the invoice data (same as createInvoice)
      const formData = this.invoiceForm.getRawValue();
      const invoiceDate = formData.invoiceDate ? new Date(formData.invoiceDate) : new Date();
      const buyersOrderDate = formData.buyersOrderDate ? new Date(formData.buyersOrderDate) : null;
      delete formData.invoiceDate;

      const invoiceData: InvoiceData = {
        ...formData,
        invoiceDate: invoiceDate instanceof Date && !isNaN(invoiceDate.getTime()) ? invoiceDate.toISOString() : invoiceDate,
        area: formData.area,
        buyersOrderNo: formData.buyersOrderNo,
        buyersOrderDate: buyersOrderDate instanceof Date && !isNaN(buyersOrderDate.getTime()) ? buyersOrderDate.toISOString() : buyersOrderDate,
        totalPieces: this.totalPieces,
        totalQuantity: this.totalQuantity,
        totalCifEuro: this.totalCifEuro.toString(),
        grandTotal: this.grandTotal.toString(),
        signature: this.signatureBase64 || ''
      } as any;

      this.invoiceService.createInvoice(invoiceData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.snackBar.open('Invoice saved successfully! Opening print preview...', 'Close', { duration: 3000 });

          // Print the invoice after saving
          setTimeout(() => {
            this.printInvoice();
          }, 500);

          this.loadInvoices();
        },
        error: (error) => {
          console.error('Error saving invoice:', error);
          this.isSubmitting = false;
          this.snackBar.open('Failed to save invoice. Please try again.', 'Close', { duration: 3000 });
        }
      });
    } else {
      this.invoiceForm.markAllAsTouched();
      this.snackBar.open('Please fill all required fields.', 'Close', { duration: 3000 });
    }
  }

  // Update & Print functionality for existing invoices
  updateAndPrint(): void {
    if (this.invoiceForm.valid && this.loadedInvoiceId) {
      this.isSubmitting = true;
      const formData = this.invoiceForm.getRawValue();

      // Same logic as updateInvoice method
      const invoiceDate = formData.invoiceDate ? new Date(formData.invoiceDate) : new Date();
      const buyersOrderDate = formData.buyersOrderDate ? new Date(formData.buyersOrderDate) : null;
      delete formData.invoiceDate;

      const invoiceData: InvoiceData = {
        ...formData,
        invoiceDate: invoiceDate instanceof Date && !isNaN(invoiceDate.getTime()) ? invoiceDate.toISOString() : invoiceDate,
        area: formData.area,
        buyersOrderNo: formData.buyersOrderNo,
        buyersOrderDate: buyersOrderDate instanceof Date && !isNaN(buyersOrderDate.getTime()) ? buyersOrderDate.toISOString() : buyersOrderDate,
        totalPieces: this.totalPieces,
        totalQuantity: this.totalQuantity,
        totalCifEuro: this.totalCifEuro.toString(),
        grandTotal: this.grandTotal.toString(),
        signature: this.signatureBase64 || ''
      } as any;

      this.invoiceService.updateInvoice(this.loadedInvoiceId, invoiceData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.snackBar.open('Invoice updated successfully! Opening print preview...', 'Close', { duration: 3000 });

          // Print the invoice after updating
          setTimeout(() => {
            this.printInvoice();
          }, 500);

          this.loadInvoices();
        },
        error: (error) => {
          this.isSubmitting = false;
          this.snackBar.open('Failed to update invoice. Please try again.', 'Close', { duration: 3000 });
        }
      });
    } else {
      this.invoiceForm.markAllAsTouched();
      this.snackBar.open('Please fill all required fields.', 'Close', { duration: 3000 });
    }
  }

  // Format quantity input to 2 decimal places
  formatQuantityInput(event: any): void {
    const input = event.target;
    let value = input.value;

    // Allow typing but format immediately to prevent floating point errors
    if (value && !isNaN(value)) {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        // Round to 2 decimal places to prevent floating point errors
        const roundedValue = Math.round(numValue * 100) / 100;

        // Update the form control with the rounded value
        const control = this.invoiceForm.get('goods') as FormArray;
        const currentIndex = this.getRowIndex(input);
        if (control && control.at(currentIndex)) {
          control.at(currentIndex).get('quantitySqMeter')?.setValue(roundedValue, { emitEvent: false });
        }
      }
    }
  }

  // Format quantity on blur to show exactly 2 decimal places
  formatQuantityOnBlur(event: any): void {
    const input = event.target;
    let value = input.value;

    if (value && !isNaN(value)) {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        // Round to 2 decimal places to prevent floating point errors like 13.999999998
        const roundedValue = Math.round(numValue * 100) / 100;
        const formattedValue = roundedValue.toFixed(2);

        // Update the input display
        input.value = formattedValue;

        // Update the form control with the rounded value
        const control = this.invoiceForm.get('goods') as FormArray;
        const currentIndex = this.getRowIndex(input);
        if (control && control.at(currentIndex)) {
          control.at(currentIndex).get('quantitySqMeter')?.setValue(roundedValue, { emitEvent: false });
        }
      }
    }
  }

  // Helper method to get row index for form array
  private getRowIndex(input: HTMLElement): number {
    const row = input.closest('tr');
    if (row && row.parentElement) {
      return Array.from(row.parentElement.children).indexOf(row);
    }
    return 0;
  }

  // Print invoice functionality
  printInvoice(): void {
    try {
      // Create a new window for printing
      const printWindow = window.open('', '_blank', 'width=800,height=600');

      if (!printWindow) {
        this.snackBar.open('Please allow popups to print the invoice.', 'Close', { duration: 3000 });
        return;
      }

      // Get form data for the invoice
      const formData = this.invoiceForm.value;

      // Generate the invoice HTML content
      const invoiceHTML = this.generateInvoiceHTML(formData);

      // Write the document to the new window
      printWindow.document.write(invoiceHTML);
      printWindow.document.close();

      // Wait for content to load, then print
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

    } catch (error) {
      console.error('Error printing invoice:', error);
      this.snackBar.open('Error printing invoice. Please try again.', 'Close', { duration: 3000 });
    }
  }

  // Generate proper invoice HTML format
  private generateInvoiceHTML(formData: any): string {
    const goods = formData.goods || [];

    // Generate goods table rows
    let goodsRows = '';
    goods.forEach((item: any) => {
      goodsRows += `
        <tr>
          <td>${item.quality || ''}</td>
          <td>${item.design || ''}</td>
          <td style="text-align: center;">${item.pieces || 0}</td>
          <td style="text-align: right;">${(item.quantitySqMeter || 0).toFixed(2)}</td>
          <td style="text-align: right;">${(item.rateFOB || 0).toFixed(2)}</td>
          <td style="text-align: right;">${(item.amountFOB || 0).toFixed(2)}</td>
        </tr>
      `;
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Invoice Print</title>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.1;
            color: #000;
            background: white;
            padding: 10px;
            margin: 0;
          }
          .invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
          }
          .invoice-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
            text-decoration: underline;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 2px;
          }
          th, td {
            border: 1px solid #000;
            padding: 3px;
            vertical-align: top;
            font-size: 9px;
          }
          .header-table td {
            padding: 4px;
            font-size: 10px;
          }
          .goods-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
            font-size: 9px;
            padding: 6px 4px;
          }
          .goods-table td {
            font-size: 10px;
            padding: 4px;
          }
          .total-row {
            font-weight: bold;
            background-color: #f8f8f8;
          }
          .text-center {
            text-align: center;
          }
          .text-right {
            text-align: right;
          }
          .small-text {
            font-size: 9px;
          }
          @media print {
            body { margin: 0; padding: 10px; }
            .invoice-container { margin: 0; }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <!-- Invoice Title -->
          <div class="invoice-title">INVOICE</div>

          <!-- Header Information Table -->
          <table class="header-table">
            <tr>
              <td rowspan="4" style="width: 40%; vertical-align: top;">
                <strong>Exporter:</strong><br>
                <strong>M/S. RACHIN EXPORTS</strong><br>
                SUREKA ESTATE, MAHANTH SHIWALA,<br>
                MIRZAPUR, U.P. INDIA.
              </td>
              <td style="width: 30%;"><strong>Invoice No & Date:</strong></td>
              <td style="width: 30%;"><strong>Exporter's Ref:</strong></td>
            </tr>
            <tr>
              <td>${formData.invoiceNumber || 'RE-869'} ${formData.invoiceDate ? new Date(formData.invoiceDate).toLocaleDateString('en-GB') : '11/03/2014'}</td>
              <td rowspan="3" style="vertical-align: top;">${formData.exporterRef || ''}</td>
            </tr>
            <tr>
              <td><strong>Buyer's Order No. & Date:</strong></td>
            </tr>
            <tr>
              <td>${formData.buyersOrderNo || '100135'} Dt. ${formData.buyersOrderDate ? new Date(formData.buyersOrderDate).toLocaleDateString('en-GB') : '05/03/2014'}</td>
            </tr>
          </table>

          <!-- Second Header Table -->
          <table class="header-table">
            <tr>
              <td rowspan="2" style="width: 40%; vertical-align: top;">
                <strong>Consignee:</strong><br>
                M/S KESHARI ORIENT TEPPICHE IMPORT GmbH<br><br>
                STOCKUM 2A<br>
                D- 48653 COESFELD<br>
                GERMANY
              </td>
              <td style="width: 30%;"><strong>Other Reference(s):</strong></td>
              <td rowspan="2" style="width: 30%; vertical-align: top;">
                <strong>Buyer (if other than consignee):</strong><br>
                ${formData.buyerIfOther || ''}
              </td>
            </tr>
            <tr>
              <td style="vertical-align: top;">${formData.otherReferences || ''}</td>
            </tr>
          </table>

          <!-- Shipping Information Table -->
          <table class="header-table">
            <tr>
              <td style="width: 25%;"><strong>Pre-Carriage by</strong><br>${formData.preCarriageBy || 'LORRY'}</td>
              <td style="width: 25%;"><strong>Place of Receipt by Pre-Carrier</strong><br>${formData.placeOfReceipt || 'MADHOSINGH'}</td>
              <td style="width: 25%;"><strong>Country of Origin of Goods</strong><br>${formData.originCountry || 'INDIA'}</td>
              <td style="width: 25%;"><strong>Country of Final Destination</strong><br>${formData.destinationCountry || 'GERMANY'}</td>
            </tr>
            <tr>
              <td><strong>Vessel/Flight No.</strong><br>${formData.vesselNo || 'SEA'}</td>
              <td><strong>Port of Loading</strong><br>${formData.portOfLoading || 'MUMBAI'}</td>
              <td><strong>Terms of Delivery and Payment</strong><br>C.A.D</td>
              <td rowspan="2" style="vertical-align: top;">
                <strong>Description of goods</strong><br>
                Indian Hand-Knotted Woolen Carpets
              </td>
            </tr>
            <tr>
              <td><strong>Port of discharge</strong><br>${formData.portOfDischarge || 'ROTTERDAM'}</td>
              <td><strong>Final Destination</strong><br>${formData.finalDestination || 'GERMANY'}</td>
              <td><strong>Marks & Cont No.</strong><br>REMOTI<br>ROTTERDAM</td>
            </tr>
            <tr>
              <td colspan="2">
                <strong>Roll No.:</strong> ${formData.rollNo || '338-405-485-491-402-525-540'}
              </td>
              <td><strong>No & Kind of Package</strong><br>${formData.noOfKindOfPackage || '52 Roll(s)'}</td>
              <td><strong>RATE PER<br>Sq. Meter</strong></td>
            </tr>
          </table>

          <!-- Goods Table -->
          <table class="goods-table">
            <thead>
              <tr>
                <th style="width: 18%;">QUALITY</th>
                <th style="width: 18%;">DESIGN</th>
                <th style="width: 12%;">PIECES</th>
                <th style="width: 15%;">QUANTITY<br>Sq. Meter</th>
                <th style="width: 15%;">RATE PER<br>Sq. Meter</th>
                <th style="width: 22%;">CIF<br>IN EURO</th>
              </tr>
            </thead>
            <tbody>
              ${goodsRows}

              <!-- Empty rows for spacing -->
              <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
              <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
              <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
              <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
              <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
              <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
              <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
              <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>

              <tr class="total-row">
                <td colspan="2" style="text-align: center;"><strong>TOTAL:</strong></td>
                <td style="text-align: center;"><strong>${this.totalPieces || 0}</strong></td>
                <td style="text-align: right;"><strong>${(this.totalQuantity || 0).toFixed(2)}</strong></td>
                <td style="text-align: center;"><strong>CIF&nbsp;&nbsp;&nbsp;&nbsp;EURO</strong></td>
                <td style="text-align: right;"><strong>${(parseFloat(this.totalCifEuro?.toString() || '0')).toFixed(2)}</strong></td>
              </tr>
              <tr>
                <td colspan="5" style="text-align: right;"><strong>Added Freight</strong></td>
                <td style="text-align: right;"><strong>${(parseFloat(formData.addedFreight?.toString() || '0')).toFixed(2)}</strong></td>
              </tr>
              <tr>
                <td colspan="5" style="text-align: right;"><strong>GRAND TOTAL</strong></td>
                <td style="text-align: right;"><strong>${(parseFloat(this.grandTotal?.toString() || '0')).toFixed(2)}</strong></td>
              </tr>
            </tbody>
          </table>

          <!-- Additional Information -->
          <table class="header-table">
            <tr>
              <td style="width: 50%;"><strong>WOOL 50% COTTON 50%</strong></td>
              <td style="width: 25%;"><strong>Gross Wt.</strong> ${formData.grossWeight || '1040.000 KGS'}</td>
              <td style="width: 25%;"><strong>Net Wt.</strong> ${formData.netWeight || '990.000 KGS'}</td>
            </tr>
            <tr>
              <td><strong>QUALITY:</strong></td>
              <td colspan="2"></td>
            </tr>
          </table>

          <!-- Amount in Words and Declaration -->
          <table class="header-table">
            <tr>
              <td style="width: 70%; vertical-align: top;">
                <strong>Amount chargeable in words</strong><br>
                <span style="text-decoration: underline;">${formData.amountChargeableWords || 'EURO TWENTY SEVEN THOUSAND SEVEN HUNDRED SIXTY THREE AND POINT TWENTY SIX ONLY.'}</span>
                <br><br>
                <strong>Declaration</strong><br>
                <span style="font-size: 9px;">
                We declare that this Invoice shows the actual price of the goods<br>
                described and that all particulars are true and correct.
                </span>
              </td>
              <td style="width: 30%; text-align: center; vertical-align: bottom;">
                <strong>Signature & Date</strong><br><br><br><br>
                ${this.signatureBase64 ? `<img src="${this.signatureBase64}" style="max-width: 150px; max-height: 60px;"><br>` : '<br><br><br>'}
                <div style="border-top: 1px solid #000; margin-top: 20px; padding-top: 5px; font-size: 10px;">
                  Signature
                </div>
              </td>
            </tr>
          </table>
        </div>
      </body>
      </html>
    `;
  }

  // Invoice selection methods
  createNewInvoice(): void {
    this.isLoading = true;

    // Get a new invoice number from the API
    this.invoiceService.generateInvoiceNumber().subscribe({
      next: (response) => {
        console.log('✅ Received invoice number from backend:', response);
        let invoiceNo = 'RE-750';
        if (response && response.invoiceNumber) {
          // Use the full invoice number from backend (includes date)
          invoiceNo = response.invoiceNumber;
        }
        console.log('🔢 Using invoice number:', invoiceNo);
        // Reset the form with only the exporter data pre-filled
        this.invoiceForm.reset({
          invoiceNumber: invoiceNo, // Always just RE-XXX
          invoiceDate: new Date(), // Set to current date
          exporterRef: '',
          exporter: 'M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA',
          // All other fields are left empty for user input
          buyersOrderNo: '',
          buyersOrderDate: null,
          otherReferences: '',
          consignee: '',
          buyerIfOther: '',
          preCarriageBy: '',
          placeOfReceipt: '',
          originCountry: 'INDIA', // Default country of origin
          destinationCountry: '',
          vesselNo: '',
          portOfLoading: '',
          portOfDischarge: '',
          finalDestination: '',
          marksAndNos: '',
          noOfKindOfPackage: '',
          descriptionOfGoods: 'Indian Hand-Knotted Woolen Carpets',
          area: '', // Initialize area field
          addedFreight: '0.00',
          amountChargeableWords: '',
          insurance: '0.00',
          igstPercentage: '0.00',
          igst: '0.00'
        });

        // Clear the goods array - keep it completely empty for new invoice
        while (this.goods.length) {
          this.goods.removeAt(0);
        }
        // No goods rows added - goods section will be empty

        // Clear signature
        this.signatureImageUrl = null;
        this.signatureBase64 = null;

        // Show the invoice form
        this.showInvoiceForm = true;
        this.showOldInvoices = false;
        this.selectedOldInvoice = '';

        // Set to create mode for new invoice
        this.isUpdateMode = false;
        this.loadedInvoiceId = null;
        this.isFromPackingList = false; // Reset packing list flag for new invoice

        // Notify user about the new invoice number
        this.snackBar.open(`New invoice created with number: ${invoiceNo}`, 'Close', {
          duration: 3000
        });
      },
      error: (error) => {
        // Fallback: always start at RE-755 if API fails
        this.invoiceForm.reset({
          invoiceNumber: 'RE-755',
          invoiceDate: new Date(),
          exporterRef: '',
          exporter: 'M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA',
          buyersOrderNo: '',
          buyersOrderDate: null,
          otherReferences: '',
          consignee: '',
          buyerIfOther: '',
          preCarriageBy: '',
          placeOfReceipt: '',
          originCountry: 'INDIA',
          destinationCountry: '',
          vesselNo: '',
          portOfLoading: '',
          portOfDischarge: '',
          finalDestination: '',
          marksAndNos: '',
          noOfKindOfPackage: '',
          descriptionOfGoods: '',
          area: '', // Initialize area field
          addedFreight: '0.00',
          amountChargeableWords: '',
        });
        while (this.goods.length) {
          this.goods.removeAt(0);
        }
        // Keep goods section empty for new invoice

        // Clear signature
        this.signatureImageUrl = null;
        this.signatureBase64 = null;

        this.showInvoiceForm = true;
        this.showOldInvoices = false;
        this.selectedOldInvoice = '';
        this.isLoading = false;

        // Set to create mode for new invoice
        this.isUpdateMode = false;
        this.loadedInvoiceId = null;
        this.isFromPackingList = false; // Reset packing list flag for new invoice

        this.calculateTotals();
        this.snackBar.open('New invoice created with number: RE-755 (fallback)', 'Close', {
          duration: 3000
        });
      }
    });
  }

  loadInvoices(): void {
    console.log('📡 Loading old invoices from exportinvoices collection...');
    this.isLoading = true;
    this.invoiceService.getAllInvoices().subscribe({
      next: (invoices) => {
        console.log('✅ Raw invoices received:', invoices);
        console.log('✅ Number of invoices:', invoices.length);

        this.oldInvoices = invoices.map(invoice => ({
          id: invoice.id || '',
          number: invoice.invoiceNumber,
          date: this.extractDateFromInvoiceNumber(invoice.invoiceNumber)
        }));

        console.log('✅ Processed old invoices:', this.oldInvoices);
        this.isLoading = false;

        if (invoices.length === 0) {
          console.log('⚠️ No invoices found in database');
          this.snackBar.open('No invoices found in database', 'Close', {
            duration: 3000
          });
        }
      },
      error: (error) => {
        console.error('❌ Error loading invoices:', error);
        this.snackBar.open('Failed to load invoices. Please try again.', 'Close', {
          duration: 3000
        });
        this.isLoading = false;
      }
    });
  }

  extractDateFromInvoiceNumber(invoiceNumber: string): string {
    // Extract date from invoice number format like "RE-659 11/03/2014"
    const parts = invoiceNumber.split(' ');
    return parts.length > 1 ? parts[1] : '';
  }

  toggleOldInvoices(): void {
    this.showOldInvoices = !this.showOldInvoices;
    if (this.showOldInvoices && this.oldInvoices.length === 0) {
      this.loadInvoices();
    }
  }

  loadOldInvoice(): void {
    if (!this.selectedOldInvoice) {
      return;
    }

    this.isLoading = true;
    console.log('📡 Loading old invoice:', this.selectedOldInvoice);

    this.invoiceService.getInvoiceById(this.selectedOldInvoice).subscribe({
      next: (invoice) => {
        console.log('✅ Received invoice data:', invoice);

        // Log specific field values from database
        console.log('🔍 Database field values:');
        console.log('  exporterRef:', invoice.exporterRef);
        console.log('  buyersOrderNo:', invoice.buyersOrderNo);
        console.log('  buyersOrderDate:', invoice.buyersOrderDate);
        console.log('  otherReferences:', invoice.otherReferences);
        console.log('  preCarriageBy:', invoice.preCarriageBy);
        console.log('  placeOfReceipt:', invoice.placeOfReceipt);
        console.log('  vesselNo:', invoice.vesselNo);
        console.log('  portOfLoading:', invoice.portOfLoading);
        console.log('  portOfDischarge:', invoice.portOfDischarge);
        console.log('  finalDestination:', invoice.finalDestination);
        console.log('  marksAndNos:', invoice.marksAndNos);
        console.log('  noOfKindOfPackage:', invoice.noOfKindOfPackage);
        console.log('  descriptionOfGoods:', invoice.descriptionOfGoods);
        console.log('  goods:', invoice.goods);

        // Clear the goods array
        while (this.goods.length) {
          this.goods.removeAt(0);
        }

        // Handle signature if present
        if (invoice.signature) {
          this.signatureBase64 = invoice.signature;
          this.signatureImageUrl = invoice.signature; // Use base64 directly for preview
          console.log('✅ Signature loaded from database');
        } else {
          this.signatureBase64 = null;
          this.signatureImageUrl = null;
        }

        // Parse dates properly
        const invoiceDate = invoice.invoiceDate ? new Date(invoice.invoiceDate) : new Date();
        const buyersOrderDate = invoice.buyersOrderDate ? this.parseDate(invoice.buyersOrderDate) : null;

        console.log('📅 Date parsing:');
        console.log('  Raw invoice date:', invoice.invoiceDate);
        console.log('  Parsed invoice date:', invoiceDate);
        console.log('  Raw buyer order date:', invoice.buyersOrderDate);
        console.log('  Parsed buyer order date:', buyersOrderDate);

        // Find and set the consignee/buyer after ensuring buyers are loaded
        if (invoice.consignee) {
          console.log('🔍 Looking for consignee match:', invoice.consignee);
          console.log('🔍 Available buyers:', this.buyers.length);

          // If buyers not loaded, load them first
          if (this.buyers.length === 0) {
            console.log('⏳ Loading buyers first...');
            this.loadBuyersPromise().then(() => {
              this.matchAndSetConsignee(invoice.consignee);
            });
          } else {
            this.matchAndSetConsignee(invoice.consignee);
          }
        }

        // Load the invoice data into the form with proper structure
        console.log('📝 Patching form with invoice data...');
        console.log('📝 Invoice number:', invoice.invoiceNumber);
        console.log('📝 Invoice date:', invoiceDate);
        console.log('📝 Buyer order no:', invoice.buyersOrderNo);
        console.log('📝 Buyer order date:', buyersOrderDate);
        console.log('📝 Consignee:', invoice.consignee);

        const patchData = {
          invoiceNumber: invoice.invoiceNumber || 'RE-755',
          invoiceDate: invoiceDate,
          exporterRef: invoice.exporterRef || '',
          exporter: 'M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA',
          buyersOrderNo: invoice.buyersOrderNo || '',
          buyersOrderDate: buyersOrderDate,
          otherReferences: invoice.otherReferences || '',
          consignee: invoice.consignee || '',
          buyerIfOther: invoice.buyerIfOther || 'SAME',
          preCarriageBy: invoice.preCarriageBy || '',
          placeOfReceipt: invoice.placeOfReceipt || '',
          originCountry: invoice.originCountry || 'INDIA',
          destinationCountry: invoice.destinationCountry || '',
          vesselNo: invoice.vesselNo || '',
          portOfLoading: invoice.portOfLoading || '',
          portOfDischarge: invoice.portOfDischarge || '',
          finalDestination: invoice.finalDestination || '',
          marksAndNos: invoice.marksAndNos || '',
          noOfKindOfPackage: invoice.noOfKindOfPackage || '',
          descriptionOfGoods: invoice.descriptionOfGoods || 'Indian Hand-Knotted Woolen Carpets',
          area: invoice.area || '',
          addedFreight: invoice.addedFreight || '0.00',
          amountChargeableWords: invoice.amountChargeableWords || '',
          insurance: '0.00',
          igstPercentage: '0.00',
          igst: '0.00'
        };

        console.log('📝 Patch data object:', patchData);
        console.log('📝 Form before patch:', this.invoiceForm.value);

        this.invoiceForm.patchValue(patchData);

        console.log('📝 Form after patch:', this.invoiceForm.value);

        // Check specific field values
        console.log('📝 Invoice Number field:', this.invoiceForm.get('invoiceNumber')?.value);
        console.log('📝 Consignee field:', this.invoiceForm.get('consignee')?.value);
        console.log('📝 Exporter Ref field:', this.invoiceForm.get('exporterRef')?.value);
        console.log('📝 Buyer Order No field:', this.invoiceForm.get('buyersOrderNo')?.value);
        console.log('📝 Buyer Order Date field:', this.invoiceForm.get('buyersOrderDate')?.value);
        console.log('📝 Other References field:', this.invoiceForm.get('otherReferences')?.value);
        console.log('✅ Form patched successfully');

        // ADDITIONAL DEBUG: Check if buyer order fields have actual values
        console.log('🔍 BUYER ORDER DEBUG:');
        console.log('  Raw DB buyersOrderNo:', invoice.buyersOrderNo);
        console.log('  Raw DB buyersOrderDate:', invoice.buyersOrderDate);
        console.log('  Raw DB otherReferences:', invoice.otherReferences);
        console.log('  Form buyersOrderNo after patch:', this.invoiceForm.get('buyersOrderNo')?.value);
        console.log('  Form buyersOrderDate after patch:', this.invoiceForm.get('buyersOrderDate')?.value);
        console.log('  Form otherReferences after patch:', this.invoiceForm.get('otherReferences')?.value);

        // Handle goods loading based on source
        console.log('📦 Loading goods data:', invoice.goods);
        console.log('📦 History state:', history.state);

        // Clear existing goods first
        while (this.goods.length) {
          this.goods.removeAt(0);
        }

        // Check if we're coming from packing list
        const isFromPackingList = history.state && history.state.fromPackingList;

        if (isFromPackingList) {
          console.log('📦 Coming from packing list - goods will be loaded from packing list data');
          // Add a temporary empty item that will be replaced by packing list goods
          this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
        } else {
          // Normal invoice loading - load goods from invoice
          if (invoice.goods && invoice.goods.length > 0) {
            console.log('📦 Found', invoice.goods.length, 'goods items from invoice');
            invoice.goods.forEach((item, index) => {
              console.log(`📦 Adding goods item ${index + 1}:`, item);
              this.goods.push(this.createGoodsItem(
                item.quality || '',
                item.design || '',
                item.pieces || 0,
                item.quantity || 0,
                item.rate || 0
              ));
            });
            console.log('📦 Invoice goods loaded successfully');
          } else {
            console.log('📦 No goods data found in invoice, adding default empty item');
            // Add at least one empty item
            this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
          }
        }

        // Show the invoice form
        console.log('🎯 Setting form visibility and calculating totals...');
        this.showInvoiceForm = true;
        this.showOldInvoices = false;
        this.isLoading = false;

        // Calculate totals
        this.calculateTotals();

        // Always enable update mode when loading existing invoice
        console.log('🔄 Loading existing invoice - enabling update mode');
        this.isUpdateMode = true;
        this.loadedInvoiceId = invoice.id || null;

        // If coming from packing list, load packing list goods immediately
        if (isFromPackingList) {
          console.log('🔄 Coming from packing list - loading packing list goods immediately');
          console.log('🔄 Invoice ID for packing list:', invoice.id);

          this.invoiceService.getPackingListsByInvoiceId(invoice.id || this.selectedOldInvoice).subscribe({
            next: (packingLists: any[]) => {
              console.log('📦 Received packing lists from service:', packingLists);
              console.log('📦 Number of packing lists:', packingLists ? packingLists.length : 0);

              if (packingLists && packingLists.length > 0) {
                console.log('📦 Loading goods from', packingLists.length, 'packing lists');
                if (packingLists.length > 0) {
                  this.loadGoodsFromPackingList(packingLists[0]._id);
                }
              } else {
                console.log('📦 No packing lists found for this invoice');
              }
            },
            error: (error) => {
              console.error('❌ Error loading packing lists for invoice:', error);
            }
          });
        }

        // Force change detection to ensure form is visible
        setTimeout(() => {
          console.log('🔄 Forcing change detection for form visibility');
          this.showInvoiceForm = true;
          this.showOldInvoices = false;
        }, 100);

        console.log('🎯 Form state - showInvoiceForm:', this.showInvoiceForm);
        console.log('🎯 Form state - showOldInvoices:', this.showOldInvoices);
        console.log('🎯 Form state - isLoading:', this.isLoading);
        console.log('🎯 Update mode enabled:', this.isUpdateMode);

        // Force change detection and check DOM
        setTimeout(() => {
          console.log('🔄 Triggering change detection...');
          console.log('🔄 Final form values:', this.invoiceForm.value);

          // Check if form element exists in DOM
          const formElement = document.querySelector('.invoice-container');
          console.log('🔄 Form element in DOM:', formElement ? 'EXISTS' : 'NOT FOUND');

          if (formElement) {
            console.log('🔄 Form element styles:', window.getComputedStyle(formElement).display);
            console.log('🔄 Form element visibility:', window.getComputedStyle(formElement).visibility);
          }

          // Double check component state
          console.log('🔄 Component showInvoiceForm:', this.showInvoiceForm);
          console.log('🔄 Component showOldInvoices:', this.showOldInvoices);

          // Check if selectedBuyer is set properly
          console.log('🔄 Selected Buyer:', this.selectedBuyer);
          console.log('🔄 Selected Buyer ID:', this.selectedConsigneeId);

          // Check form control states
          console.log('🔄 Form controls status:');
          Object.keys(this.invoiceForm.controls).forEach(key => {
            const control = this.invoiceForm.get(key);
            if (control) {
              console.log(`  ${key}: value="${control.value}", disabled=${control.disabled}, valid=${control.valid}`);
            }
          });
        }, 500);

        console.log('✅ Invoice loaded successfully in edit mode');
      },
      error: (error) => {
        console.error('❌ Error loading invoice:', error);
        this.snackBar.open('Failed to load invoice. Please try again.', 'Close', {
          duration: 3000
        });
        this.isLoading = false;
      }
    });
  }

  // Helper methods
  private generateRandomNumber(): string {
    // Generate a random 3-digit number
    return (Math.floor(Math.random() * 900) + 100).toString();
  }

  // Get all packing lists
  getAllPackingLists(): Observable<any[]> {
    return this.http.get<any[]>(`${this.packingListApiUrl}/exportPacking`).pipe(
      map(packingLists => {


        return Array.isArray(packingLists) ? packingLists : [];
      }),
      catchError(error => {
        console.error('Error fetching packing lists:', error);
        return of([]);
      })
    );
  }

  // Get packing list by ID
  getPackingListById(id: string): Observable<any> {
    return this.http.get<any>(`${this.packingListApiUrl}/exportPacking/${id}`).pipe(
      map(response => {
        if (response && response.data) {
          return response.data;
        } else if (response) {
          return response;
        } else {
          return null;
           }
      }),
      catchError(error => {
        console.error('Error fetching packing list:', error);
        // Return sample data based on ID
                  return of(null);
      })
    );
  }

  // Generate invoice number
  generateInvoiceNumber(): Observable<any> {
    // Always start at RE-750 and increment from there
    return this.http.get<any>('http://localhost:2000/api/invoices/latest').pipe(
      map((response: any) => {
        let nextNumber = 750; // Start at 750
        if (response && response.document && response.document.invoiceNumber) {
          // Extract the number part from the latest invoice number (format: RE-XXX DD/MM/YYYY)
          const invoiceNumberParts = response.document.invoiceNumber.split(' ');
          const invoiceNo = invoiceNumberParts[0]; // RE-XXX
          const match = invoiceNo.match(/RE-(\d+)/);
          if (match && match[1]) {
            const lastNum = parseInt(match[1], 10);
            nextNumber = lastNum >= 750 ? lastNum + 1 : 750;
          }
        }
        const invoiceNumber = `RE-${nextNumber}`;
        const today = new Date();
        const day = String(today.getDate()).padStart(2, '0');
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const year = today.getFullYear();
        const dateStr = `${day}/${month}/${year}`;
        return { invoiceNumber: `${invoiceNumber} ${dateStr}` };
      }),
      catchError((error: any) => {
        // Fallback: always start at RE-750 if no previous invoice
        const today = new Date();
        const day = String(today.getDate()).padStart(2, '0');
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const year = today.getFullYear();
        const dateStr = `${day}/${month}/${year}`;
        return of({ invoiceNumber: `RE-750 ${dateStr}` });
      })
    );
  }

  private formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}/${month}/${year}`;
  }

  private parseDate(dateStr: string): Date | null {
    if (!dateStr) return null;

    // Parse date in format DD/MM/YYYY
    const parts = dateStr.split('/');
    if (parts.length !== 3) return null;

    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed in Date
    const year = parseInt(parts[2], 10);

    if (isNaN(day) || isNaN(month) || isNaN(year)) return null;

    return new Date(year, month, day);
  }

  // Convert number to words
  private convertNumberToWords(num: number): string {
    const ones = ['', 'ONE', 'TWO', 'THREE', 'FOUR', 'FIVE', 'SIX', 'SEVEN', 'EIGHT', 'NINE', 'TEN', 'ELEVEN', 'TWELVE', 'THIRTEEN', 'FOURTEEN', 'FIFTEEN', 'SIXTEEN', 'SEVENTEEN', 'EIGHTEEN', 'NINETEEN'];
    const tens = ['', '', 'TWENTY', 'THIRTY', 'FORTY', 'FIFTY', 'SIXTY', 'SEVENTY', 'EIGHTY', 'NINETY'];

    const numStr = num.toString();
    const decimalParts = numStr.split('.');
    const wholePart = parseInt(decimalParts[0]);
    const decimalPart = decimalParts.length > 1 ? parseInt(decimalParts[1]) : 0;

    const convertLessThanOneThousand = (n: number): string => {
      if (n === 0) {
        return '';
      }

      if (n < 20) {
        return ones[n];
      }

      if (n < 100) {
        return tens[Math.floor(n / 10)] + (n % 10 !== 0 ? ' ' + ones[n % 10] : '');
      }

      return ones[Math.floor(n / 100)] + ' HUNDRED' + (n % 100 !== 0 ? ' AND ' + convertLessThanOneThousand(n % 100) : '');
    };

    const convertNumber = (n: number): string => {
      if (n === 0) {
        return 'ZERO';
      }

      let result = '';

      // Handle millions
      if (n >= 1000000) {
        result += convertLessThanOneThousand(Math.floor(n / 1000000)) + ' MILLION ';
        n %= 1000000;
      }

      // Handle thousands
      if (n >= 1000) {
        result += convertLessThanOneThousand(Math.floor(n / 1000)) + ' THOUSAND ';
        n %= 1000;
      }

      // Handle hundreds and tens
      if (n > 0) {
        result += convertLessThanOneThousand(n);
      }

      return result.trim();
    };

    let result = convertNumber(wholePart);

    // Handle decimal part
    if (decimalPart > 0) {
      // Format decimal part to always have 2 digits
      const decimalStr = decimalParts[1].padEnd(2, '0').substring(0, 2);
      result += ' POINT ' + convertNumber(parseInt(decimalStr));
    }

    return result;
  }

  // Format Exporter address so that 'MADHOSINGH, P.O. AURAI' is always on the same line
  formatExporterText(raw: string): string {
    return raw;
  }

  private finishInvoiceLoad(): void {
    console.log('🏁 Finishing invoice load...');
    console.log('🏁 Setting showInvoiceForm to true');
    console.log('🏁 Current form values:', this.invoiceForm.value);

    // Set update mode when loading existing invoice (especially from packing list)
    if (history.state && history.state.invoiceId) {
      console.log('🔄 Setting update mode for existing invoice from navigation state');
      this.isUpdateMode = true;
      this.loadedInvoiceId = history.state.invoiceId;
      console.log('✅ Update mode enabled with invoice ID:', this.loadedInvoiceId);
    }

    this.isLoading = false;
    this.showInvoiceForm = true;
    this.showOldInvoices = false;
    this.calculateTotals();

    console.log('✅ Invoice load completed successfully');
    console.log('✅ Update mode:', this.isUpdateMode);
    console.log('✅ Loaded invoice ID:', this.loadedInvoiceId);
  }

  goToPackingList(): void {
    // Show SweetAlert confirmation
    Swal.fire({
      title: 'Save Shipping Details?',
      text: 'Do you want to save the shipping details and go to Packing List?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes, proceed',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'mat-raised-button mat-primary',
        cancelButton: 'mat-raised-button mat-warn'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        // Save shipping details and navigate
        this.saveShippingDetailsAndNavigate();
      }
    });
  }

  private saveShippingDetailsAndNavigate(): void {
    // Save the complete invoice data first
    if (this.isUpdateMode && this.loadedInvoiceId) {
      // Update existing invoice
      this.updateInvoicePromise().then(() => {
        this.navigateToPackingList();
      }).catch((error: any) => {
        console.error('Error saving invoice:', error);
        // Still navigate even if save fails
        this.navigateToPackingList();
      });
    } else {
      // Save new invoice
      this.saveInvoicePromise().then(() => {
        this.navigateToPackingList();
      }).catch((error: any) => {
        console.error('Error saving invoice:', error);
        // Still navigate even if save fails
        this.navigateToPackingList();
      });
    }
  }

  private navigateToPackingList(): void {
    // Extract shipping details from form
    const shippingData = {
      preCarriageBy: this.invoiceForm.get('preCarriageBy')?.value || '',
      placeOfReceipt: this.invoiceForm.get('placeOfReceipt')?.value || '',
      originCountry: this.invoiceForm.get('originCountry')?.value || '',
      destinationCountry: this.invoiceForm.get('destinationCountry')?.value || '',
      vesselNo: this.invoiceForm.get('vesselNo')?.value || '',
      portOfLoading: this.invoiceForm.get('portOfLoading')?.value || '',
      portOfDischarge: this.invoiceForm.get('portOfDischarge')?.value || '',
      finalDestination: this.invoiceForm.get('finalDestination')?.value || ''
    };

    // Extract buyer order details from form
    const buyerOrderData = {
      buyersOrderNo: this.invoiceForm.get('buyersOrderNo')?.value || '',
      buyersOrderDate: this.invoiceForm.get('buyersOrderDate')?.value || null,
      otherReferences: this.invoiceForm.get('otherReferences')?.value || '',
      buyerIfOther: this.invoiceForm.get('buyerIfOther')?.value || '',
      consignee: this.invoiceForm.get('consignee')?.value || '',
      exporterRef: this.invoiceForm.get('exporterRef')?.value || ''
    };

    console.log('💾 Saving buyer order data to localStorage:', buyerOrderData);

    // Save both shipping and buyer order details to localStorage
    localStorage.setItem('invoiceShippingDetails', JSON.stringify(shippingData));
    localStorage.setItem('invoiceBuyerOrderDetails', JSON.stringify(buyerOrderData));

    // Navigate to packing list page with state indicating coming from invoice
    // Always use the loadedInvoiceId (MongoDB _id) for proper matching
    const invoiceId = this.loadedInvoiceId;
    console.log('🚀 Navigating to packing list with invoice ID:', invoiceId);
    console.log('🚀 Update mode:', this.isUpdateMode);
    console.log('🚀 Invoice Number:', this.invoiceForm.get('invoiceNumber')?.value);

    if (!invoiceId) {
      console.error('❌ No invoice ID available for navigation');
      this.snackBar.open('Please save the invoice first before going to packing list', 'Close', {
        duration: 3000
      });
      return;
    }

    this.router.navigate(['/admin/export-packing-list'], {
      state: {
        fromInvoice: true,
        invoiceId: invoiceId
      }
    });
  }

  private saveInvoicePromise(): Promise<any> {
    return new Promise((resolve, reject) => {
      // Use existing saveInvoice method but capture the result
      const originalSubmitting = this.isSubmitting;
      this.isSubmitting = true;

      if (this.invoiceForm.valid) {
        const formData = this.invoiceForm.getRawValue();

        // Prepare the invoice data (same as createInvoice method)
        const invoiceDate = formData.invoiceDate ? new Date(formData.invoiceDate) : new Date();
        const buyersOrderDate = formData.buyersOrderDate ? new Date(formData.buyersOrderDate) : null;
        delete formData.invoiceDate;

        const invoiceData: InvoiceData = {
          ...formData,
          invoiceDate: invoiceDate instanceof Date && !isNaN(invoiceDate.getTime()) ? invoiceDate.toISOString() : invoiceDate,
          area: formData.area,
          buyersOrderNo: formData.buyersOrderNo,
          buyersOrderDate: buyersOrderDate instanceof Date && !isNaN(buyersOrderDate.getTime()) ? buyersOrderDate.toISOString() : buyersOrderDate,
          totalPieces: this.totalPieces,
          totalQuantity: this.totalQuantity,
          totalCifEuro: this.totalCifEuro.toString(),
          grandTotal: this.grandTotal.toString(),
          signature: this.signatureBase64 || ''
        } as any;

        this.invoiceService.createInvoice(invoiceData).subscribe({
          next: (response: any) => {
            this.loadedInvoiceId = response.id || response._id || response.invoiceId;
            this.isUpdateMode = true;
            this.isSubmitting = originalSubmitting;
            resolve(response);
          },
          error: (error) => {
            this.isSubmitting = originalSubmitting;
            reject(error);
          }
        });
      } else {
        this.isSubmitting = originalSubmitting;
        resolve(null); // Continue even if form is invalid
      }
    });
  }

  private updateInvoicePromise(): Promise<any> {
    return new Promise((resolve, reject) => {
      if (this.loadedInvoiceId) {
        const originalSubmitting = this.isSubmitting;
        this.isSubmitting = true;

        const formData = this.invoiceForm.getRawValue();

        // Prepare the invoice data (same as updateInvoice method)
        const invoiceDate = formData.invoiceDate ? new Date(formData.invoiceDate) : new Date();
        const buyersOrderDate = formData.buyersOrderDate ? new Date(formData.buyersOrderDate) : null;
        delete formData.invoiceDate;

        const invoiceData: InvoiceData = {
          ...formData,
          invoiceDate: invoiceDate instanceof Date && !isNaN(invoiceDate.getTime()) ? invoiceDate.toISOString() : invoiceDate,
          area: formData.area,
          buyersOrderNo: formData.buyersOrderNo,
          buyersOrderDate: buyersOrderDate instanceof Date && !isNaN(buyersOrderDate.getTime()) ? buyersOrderDate.toISOString() : buyersOrderDate,
          totalPieces: this.totalPieces,
          totalQuantity: this.totalQuantity,
          totalCifEuro: this.totalCifEuro.toString(),
          grandTotal: this.grandTotal.toString(),
          signature: this.signatureBase64 || ''
        } as any;

        this.invoiceService.updateInvoice(this.loadedInvoiceId, invoiceData).subscribe({
          next: (response) => {
            this.isSubmitting = originalSubmitting;
            resolve(response);
          },
          error: (error) => {
            this.isSubmitting = originalSubmitting;
            reject(error);
          }
        });
      } else {
        resolve(null);
      }
    });
  }

  private loadInvoiceById(invoiceId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      console.log('🔄 Loading invoice by ID:', invoiceId);
      // Set the selected invoice ID and use existing loadOldInvoice method
      this.selectedOldInvoice = invoiceId;
      this.loadOldInvoice();

      // Wait a bit for the loading to complete, then resolve
      setTimeout(() => {
        resolve(invoiceId);
      }, 1000);
    });
  }

  private loadGoodsFromLocalStorage(): void {
    // PRESERVE BUYER ORDER FIELDS - Store current values before loading goods (outside try-catch for scope)
    const currentBuyersOrderNo = this.invoiceForm.get('buyersOrderNo')?.value;
    const currentBuyersOrderDate = this.invoiceForm.get('buyersOrderDate')?.value;
    const currentOtherReferences = this.invoiceForm.get('otherReferences')?.value;

    console.log('🔄 PRESERVING buyer order fields before loading goods:');
    console.log('  buyersOrderNo:', currentBuyersOrderNo);
    console.log('  buyersOrderDate:', currentBuyersOrderDate);
    console.log('  otherReferences:', currentOtherReferences);

    try {
      const packingListDataStr = localStorage.getItem('packingListGoodsData');
      const packageInfoStr = localStorage.getItem('packingListPackageInfo');

      if (packingListDataStr) {
        const packingListGoods = JSON.parse(packingListDataStr);

        // Clear existing goods
        while (this.goods.length) {
          this.goods.removeAt(0);
        }

        // Add goods row by row from packing list data
        packingListGoods.forEach((goodsItem: any) => {
          this.goods.push(this.createGoodsItem(
            goodsItem.quality || '',
            goodsItem.design || '',
            goodsItem.pieces || 0,
            goodsItem.quantitySqMeter || 0,
            goodsItem.rateFOB || 0
          ));
        });

        // If no goods data, add one empty row
        if (packingListGoods.length === 0) {
          this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
        }

        // Load package information if available
        if (packageInfoStr) {
          const packageInfo = JSON.parse(packageInfoStr);
          console.log('📦 Package info from localStorage:', packageInfo);
          console.log('📦 Number of bales:', packageInfo.noOfBales);
          console.log('📦 Package description:', packageInfo.packageDescription);

          // Set the package description in the form
          this.invoiceForm.get('noOfKindOfPackage')?.setValue(packageInfo.packageDescription, { emitEvent: false });
          console.log('✅ Successfully updated No & Kind of Package field with:', packageInfo.packageDescription);
        } else {
          console.log('❌ No package info found in localStorage');
        }

        // RESTORE BUYER ORDER FIELDS from localStorage if available
        const buyerOrderDataStr = localStorage.getItem('invoiceBuyerOrderDetails');
        if (buyerOrderDataStr) {
          try {
            const buyerOrderData = JSON.parse(buyerOrderDataStr);
            console.log('🔄 RESTORING buyer order fields from localStorage:', buyerOrderData);

            this.invoiceForm.patchValue({
              buyersOrderNo: buyerOrderData.buyersOrderNo || currentBuyersOrderNo,
              buyersOrderDate: buyerOrderData.buyersOrderDate || currentBuyersOrderDate,
              otherReferences: buyerOrderData.otherReferences || currentOtherReferences,
              buyerIfOther: buyerOrderData.buyerIfOther || this.invoiceForm.get('buyerIfOther')?.value,
              consignee: buyerOrderData.consignee || this.invoiceForm.get('consignee')?.value,
              exporterRef: buyerOrderData.exporterRef || this.invoiceForm.get('exporterRef')?.value
            }, { emitEvent: false });

            console.log('✅ Buyer order fields restored from localStorage');

            // Clear the localStorage data after using it
            localStorage.removeItem('invoiceBuyerOrderDetails');
          } catch (error) {
            console.error('❌ Error parsing buyer order data from localStorage:', error);
            // Fallback to current values
            this.invoiceForm.patchValue({
              buyersOrderNo: currentBuyersOrderNo,
              buyersOrderDate: currentBuyersOrderDate,
              otherReferences: currentOtherReferences
            }, { emitEvent: false });
          }
        } else {
          // Fallback: restore current values if no localStorage data
          console.log('🔄 RESTORING buyer order fields from current values (no localStorage data):');
          this.invoiceForm.patchValue({
            buyersOrderNo: currentBuyersOrderNo,
            buyersOrderDate: currentBuyersOrderDate,
            otherReferences: currentOtherReferences
          }, { emitEvent: false });
        }

        // Clear the localStorage data after loading
        localStorage.removeItem('packingListGoodsData');
        localStorage.removeItem('packingListPackageInfo');

        // Calculate totals after loading goods
        this.calculateTotals();
      } else {
        // Fallback: add empty goods row
        while (this.goods.length) {
          this.goods.removeAt(0);
        }
        this.goods.push(this.createGoodsItem('', '', 0, 0, 0));

        // RESTORE BUYER ORDER FIELDS even in fallback case
        const buyerOrderDataStr = localStorage.getItem('invoiceBuyerOrderDetails');
        if (buyerOrderDataStr) {
          try {
            const buyerOrderData = JSON.parse(buyerOrderDataStr);
            console.log('🔄 RESTORING buyer order fields from localStorage (fallback case):', buyerOrderData);

            this.invoiceForm.patchValue({
              buyersOrderNo: buyerOrderData.buyersOrderNo || currentBuyersOrderNo,
              buyersOrderDate: buyerOrderData.buyersOrderDate || currentBuyersOrderDate,
              otherReferences: buyerOrderData.otherReferences || currentOtherReferences,
              buyerIfOther: buyerOrderData.buyerIfOther || this.invoiceForm.get('buyerIfOther')?.value,
              consignee: buyerOrderData.consignee || this.invoiceForm.get('consignee')?.value,
              exporterRef: buyerOrderData.exporterRef || this.invoiceForm.get('exporterRef')?.value
            }, { emitEvent: false });

            // Clear the localStorage data after using it
            localStorage.removeItem('invoiceBuyerOrderDetails');
          } catch (error) {
            console.error('❌ Error parsing buyer order data in fallback case:', error);
            // Fallback to current values
            this.invoiceForm.patchValue({
              buyersOrderNo: currentBuyersOrderNo,
              buyersOrderDate: currentBuyersOrderDate,
              otherReferences: currentOtherReferences
            }, { emitEvent: false });
          }
        } else {
          console.log('🔄 RESTORING buyer order fields from current values (fallback case):');
          this.invoiceForm.patchValue({
            buyersOrderNo: currentBuyersOrderNo,
            buyersOrderDate: currentBuyersOrderDate,
            otherReferences: currentOtherReferences
          }, { emitEvent: false });
        }
      }
    } catch (error) {
      console.error('Error loading goods from packing list:', error);
      // Fallback: add empty goods row
      while (this.goods.length) {
        this.goods.removeAt(0);
      }
      this.goods.push(this.createGoodsItem('', '', 0, 0, 0));

      // RESTORE BUYER ORDER FIELDS even in error case
      const buyerOrderDataStr = localStorage.getItem('invoiceBuyerOrderDetails');
      if (buyerOrderDataStr) {
        try {
          const buyerOrderData = JSON.parse(buyerOrderDataStr);
          console.log('🔄 RESTORING buyer order fields from localStorage (error case):', buyerOrderData);

          this.invoiceForm.patchValue({
            buyersOrderNo: buyerOrderData.buyersOrderNo || currentBuyersOrderNo,
            buyersOrderDate: buyerOrderData.buyersOrderDate || currentBuyersOrderDate,
            otherReferences: buyerOrderData.otherReferences || currentOtherReferences,
            buyerIfOther: buyerOrderData.buyerIfOther || this.invoiceForm.get('buyerIfOther')?.value,
            consignee: buyerOrderData.consignee || this.invoiceForm.get('consignee')?.value,
            exporterRef: buyerOrderData.exporterRef || this.invoiceForm.get('exporterRef')?.value
          }, { emitEvent: false });

          // Clear the localStorage data after using it
          localStorage.removeItem('invoiceBuyerOrderDetails');
        } catch (parseError) {
          console.error('❌ Error parsing buyer order data in error case:', parseError);
          // Fallback to current values
          this.invoiceForm.patchValue({
            buyersOrderNo: currentBuyersOrderNo,
            buyersOrderDate: currentBuyersOrderDate,
            otherReferences: currentOtherReferences
          }, { emitEvent: false });
        }
      } else {
        console.log('🔄 RESTORING buyer order fields from current values (error case):');
        this.invoiceForm.patchValue({
          buyersOrderNo: currentBuyersOrderNo,
          buyersOrderDate: currentBuyersOrderDate,
          otherReferences: currentOtherReferences
        }, { emitEvent: false });
      }
    }
  }

  openQualityModal(): void {
    // Get current packing list data to show as "old quality"
    const currentPackingData = this.packingLists || [];

    const dialogRef = this.dialog.open(QualityModalComponent, {
      width: '1000px',
      data: {
        currentPackingData: currentPackingData,
        currentGoods: this.goods.value
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Quality modal result:', result);
        this.updateQualityAndDesignData(result);
      }
    });
  }

  updateQualityAndDesignData(result: any): void {
    const { newQualities } = result;

    // Update goods array with new quality and design values
    const goodsArray = this.goods;

    newQualities.forEach((newItem: any, index: number) => {
      if (index < goodsArray.length) {
        const goodControl = goodsArray.at(index);

        // Update quality if provided
        if (newItem.quality && newItem.quality.trim() !== '') {
          goodControl.get('quality')?.setValue(newItem.quality.trim().toUpperCase());
        }

        // Update design if provided
        if (newItem.design && newItem.design.trim() !== '') {
          goodControl.get('design')?.setValue(newItem.design.trim().toUpperCase());
        }
      }
    });

    // Show success message
    Swal.fire({
      title: 'Updated!',
      text: 'Quality and Design data has been updated successfully',
      icon: 'success',
      confirmButtonText: 'OK'
    });
  }

  saveShippingAndGoToPackingList(): void {
    // Save only the shipping-related fields
    const shippingData = {
      preCarriageBy: this.invoiceForm.get('preCarriageBy')?.value,
      placeOfReceipt: this.invoiceForm.get('placeOfReceipt')?.value,
      originCountry: this.invoiceForm.get('originCountry')?.value,
      destinationCountry: this.invoiceForm.get('destinationCountry')?.value,
      vesselNo: this.invoiceForm.get('vesselNo')?.value,
      portOfLoading: this.invoiceForm.get('portOfLoading')?.value,
      portOfDischarge: this.invoiceForm.get('portOfDischarge')?.value,
      finalDestination: this.invoiceForm.get('finalDestination')?.value
    };
    // Optionally, store in localStorage or pass via navigation state
    localStorage.setItem('invoiceShippingDraft', JSON.stringify(shippingData));
    // Navigate to packing list page
    this.router.navigate(['/admin/export-packing-list']);
  }
}