const mongoose = require('mongoose');

async function dropECollectionIndex() {
  try {
    // Connect to MongoDB
    await mongoose.connect("mongodb+srv://infosarthaktech:<EMAIL>/test", {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('Connected to MongoDB Atlas');

    // Get the collection
    const collection = mongoose.connection.db.collection('e');
    
    // Get current indexes
    const indexes = await collection.indexes();
    console.log('Current indexes for collection e:', JSON.stringify(indexes, null, 2));
    
    // Find and drop the unique index
    for (const index of indexes) {
      if (index.key && index.key.invoiceNo === 1 && index.key.baleNo === 1) {
        console.log(`Found invoiceNo_baleNo index: ${index.name}, unique: ${index.unique === true}`);
        if (index.unique === true) {
          console.log(`Dropping unique index: ${index.name}`);
          await collection.dropIndex(index.name);
          console.log(`Dropped unique index: ${index.name}`);
        }
      }
    }
    
    // Create non-unique index
    console.log('Creating non-unique index on invoiceNo and baleNo...');
    await collection.createIndex({ invoiceNo: 1, baleNo: 1 }, { unique: false, background: true });
    
    // Verify indexes after changes
    const updatedIndexes = await collection.indexes();
    console.log('Updated indexes:', JSON.stringify(updatedIndexes, null, 2));
    
    console.log('Collection e index updated successfully');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the function
dropECollectionIndex();
