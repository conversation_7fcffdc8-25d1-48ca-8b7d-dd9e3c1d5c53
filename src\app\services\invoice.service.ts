import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class InvoiceService {
  private apiUrl = 'http://localhost:2000/api/phase-four';

  constructor(private http: HttpClient) {}

  // Get packing lists by invoice ID
  getPackingListsByInvoiceId(invoiceId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/exportPackingList/exportPacking/byInvoice/${invoiceId}`).pipe(
      map((response: any) => {
        // Handle different response formats
        if (Array.isArray(response)) {
          return response;
        } else if (response && response.data && Array.isArray(response.data)) {
          return response.data;
        } else {
          return [];
        }
      }),
      catchError(error => {
        console.error('Error fetching packing lists by invoice ID:', error);
        return of([]);
      })
    );
  }

  // Save shipping details
  saveShippingDetails(invoiceId: string, shippingDetails: any): Observable<any> {
    return this.http.patch(`${this.apiUrl}/exportInvoice/${invoiceId}/shipping`, shippingDetails);
  }
}