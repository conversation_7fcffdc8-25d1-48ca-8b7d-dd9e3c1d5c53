<div class="container mt-5">
  <section>
    <fieldset>
      <legend>
        <b>{{ isUpdated ? "Update" : "Add" }} Map Master</b>
      </legend>
      <form [formGroup]="frmMapMaster" (ngSubmit)="addMapMaster()">
        <div class="row">
          <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline">
              <mat-label>Quality</mat-label>
              <mat-select
                formControlName="quality"
                (valueChange)="selectQuality($event)"
              >
                <mat-option
                  *ngFor="let quality of qualityList"
                  value="{{ quality.id }}"
                >
                  {{ quality.quality }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-md-2 d-flex">
            <mat-form-field appearance="outline">
              <mat-label>Design</mat-label>
              <input matInput formControlName="addDesign" />
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3 d-flex">
            <mat-form-field appearance="outline">
              <mat-label>Ground Border</mat-label>
              <input matInput formControlName="groundColour" />
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline">
              <mat-label>Size</mat-label>
              <mat-select
                formControlName="sizeMaster"
                (valueChange)="selectSize($event)"
              >
                <mat-option *ngFor="let size of sizeList" value="{{ size.id }}">
                  {{ size.size }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-md-3">
            <label class="form-label"
              ><b>&nbsp;&nbsp;Upload File (.Bmp)</b> </label
            ><input
              type="file"
              class="form-control"
              (change)="chooseFile($event)"
            />
          </div>

          <div class="mb-2 col-md-3">
            <button
              *ngIf="!isUpdated; else updateTemplate"
              mat-flat-button
              color="primary"
            >
              Save
            </button>
            <ng-template #updateTemplate>
              <button
                mat-flat-button
                color="primary"
                (click)="update()"
                type="button"
              >
                Update
              </button>
            </ng-template>
          </div>
        </div>
      </form>
    </fieldset>
  </section>
</div>

<div class="container mt-4">
  <section>
    <fieldset>
      <legend><b> List</b></legend>
      <div class="row">
        <div class="col-3">
          <mat-form-field appearance="outline">
            <mat-label>Search</mat-label>
            <input
              matInput
              (keyup)="applyFilter($event)"
              placeholder="Ex. Jack"
              #input
            />
          </mat-form-field>
        </div>
        <div class="col-md-12">
          <div class="mat-elevation-z8">
            <table mat-table [dataSource]="dataSource" matSort>
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Sr.No</th>
                <td mat-cell *matCellDef="let row">{{ row.index }}</td>
              </ng-container>

              <ng-container matColumnDef="quality">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Quality
                </th>
                <td mat-cell *matCellDef="let row">{{ row.quality }}</td>
              </ng-container>

              <ng-container matColumnDef="addDesign">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Design
                </th>
                <td mat-cell *matCellDef="let row">{{ row.addDesign }}</td>
              </ng-container>

              <ng-container matColumnDef="borderColour">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Border
                </th>
                <td mat-cell *matCellDef="let row">
                  {{ row.groundColour }} / {{ row.borderColour }}
                </td>
              </ng-container>
              <!-- <ng-container matColumnDef="color">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Color</th>
                <td mat-cell *matCellDef="let row">{{ row.color }}</td>
              </ng-container> -->
              <ng-container matColumnDef="uploadFile">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>File</th>
                <td mat-cell *matCellDef="let row">
                  <img
                    crossorigin="anonymous"
                    (click)="openModal(row.uploadFile)"
                    id="myImg"
                    width="50px"
                    [src]="row.uploadFile"
                    alt=""
                  />
                </td>
              </ng-container>

              <ng-container matColumnDef="sizeMaster">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Size</th>
                <td mat-cell *matCellDef="let row">{{ row.sizeMaster }}</td>
              </ng-container>

              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Action
                </th>
                <td mat-cell *matCellDef="let row">
                  <a (click)="edit(row.id)"
                    ><i
                      class="fa fa-pencil-square-o fa-edit"
                      title="Edit purchase details "
                      aria-hidden="true"
                    ></i
                  ></a>
                  &nbsp;
                  <a (click)="delete(row.id)"
                    ><i class="fa fa-trash-o" aria-hidden="true"></i
                  ></a>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>

              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="4">
                  No data matching the filter "{{ input.value }}"
                </td>
              </tr>
            </table>

            <mat-paginator
              [pageSizeOptions]="[5, 10, 25, 100]"
              aria-label="Select page of users"
            ></mat-paginator>
          </div>
        </div>
      </div>
    </fieldset>
  </section>
</div>

<div
  class="modal fade"
  id="myModal"
  tabindex="-1"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Image Preview</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <img
          [src]="selectedImageUrl"
          crossorigin="anonymous"
          class="img-fluid"
          id="img01"
          alt="Image"
        />
        <div id="caption">{{ selectedImageUrl }}</div>
      </div>
    </div>
  </div>
</div>
