const colorRepository = require('../repositories/color-repository');

class ColorService {
  async createColor(colorData) {
    return await colorRepository.createColor(colorData);
  }

  async getColorById(colorId) {
    return await colorRepository.getColorById(colorId);
  }

  async getAllColors() {
    return await colorRepository.getAllColors();
  }

  async updateColor(colorId, colorData) {
    return await colorRepository.updateColor(colorId, colorData);
  }

  async deleteColor(colorId) {
    return await colorRepository.deleteColor(colorId);
  }
}

module.exports = new ColorService();
