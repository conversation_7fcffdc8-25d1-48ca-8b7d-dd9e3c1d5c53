/* Invoice Selection Styles */
.invoice-selection-container {
  max-width: 400px;
  margin: 20px auto;
  font-family: Arial, sans-serif;
}

.invoice-selection-header {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.invoice-selection-header h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  text-align: center;
}

.invoice-selection-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 15px;
}

.invoice-button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.new-button {
  background-color: #4CAF50;
  color: white;
}

.new-button:hover {
  background-color: #45a049;
}

.old-button {
  background-color: #2196F3;
  color: white;
}

.old-button:hover {
  background-color: #0b7dda;
}

/* Date Selection Styles */
.date-selection {
  margin: 15px auto;
  max-width: 300px;
}

.date-selection mat-form-field {
  width: 100%;
}

.old-invoices-dropdown {
  margin-top: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.old-invoice-selection {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
  align-items: center;
}

.old-invoice-select {
  flex: 1;
  min-width: 80px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

/* .date-field {
  flex: 1;
  min-width: 200px;
} */

.load-invoice-button {
  padding: 8px 15px;
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.load-invoice-button:hover {
  background-color: #e68a00;
}

.load-invoice-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.loading-message, .no-invoices-message {
  text-align: center;
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
}

.loading-message {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.no-invoices-message {
  background-color: #ffebee;
  color: #c62828;
}

/* Invoice Container Styles */
.invoice-container {
  border: 1px solid black;
  padding: 20px;
  margin: 20px auto;
  font-family: Arial, sans-serif;
  max-width: auto;
  box-sizing: border-box;
}

/* Title Section */
.title-section {
  margin-bottom: 10px;
}

.invoice-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10px;
}

/* Exporter and Consignee Section */
.exporter-consignee-section {
  display: flex;
  margin-bottom: 5px;
}

.exporter-box {
  border: 1px solid black;
  width: 60%;
  margin-right: 5px;
}

.section-label {
  font-size: 12px;
  font-weight: bold;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  display: inline-block;
  margin-bottom: 5px;
}

.section-content {
  padding: 5px;
  min-height: 60px;
}

.exporter-textarea, .consignee-textarea, .buyer-textarea {
  width: 100%;
  height: 40px;
  border: none;
  resize: none;
  font-size: 12px;
  background: transparent;
}

.invoice-details-right {
  width: 40%;
  display: flex;
  flex-direction: column;
}

.invoice-row {
  display: flex;
  margin-bottom: 5px;
}

.invoice-number-box {
  border: 1px solid black;
  padding: 5px;
  width: 60%;
  margin-right: 5px;
}

.invoice-number-label {
  font-size: 12px;
  font-weight: bold;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  display: inline-block;
  margin-bottom: 5px;
}

.invoice-date-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.invoice-number-field {
  flex: 1;
  min-width: 120px;
}

.invoice-date-field {
  flex: 1;
  min-width: 150px;
}

.invoice-dropdown {
  width: 100%;
  padding: 3px;
  border: 1px solid #ccc;
  font-size: 12px;
}

.invoice-number-input {
  width: 100%;
  padding: 3px;
  border: 1px solid #ccc;
  font-size: 12px;
  background-color: #f9f9f9;
  font-weight: bold;
  color: #333;
}

.exporter-ref-box {
  border: 1px solid black;
  padding: 5px;
  width: 40%;
}

.exporter-ref-label {
  font-size: 12px;
  font-weight: bold;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  display: inline-block;
  margin-bottom: 5px;
}

.buyers-order-box {
  border: 1px solid black;
  padding: 5px;
}

.buyers-order-label {
  font-size: 12px;
  font-weight: bold;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  display: inline-block;
  margin-bottom: 5px;
}

.buyers-order-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 5px;
}

.buyers-order-field {
  flex: 1;
  min-width: 120px;
}

.buyers-order-date-field {
  flex: 1;
  min-width: 120px;
}

.buyers-order-input {
  width: 100%;
  padding: 3px;
  border: 1px solid #ccc;
  font-size: 12px;
}

.invoice-detail-row {
  display: flex;
  border-bottom: 1px solid black;
}

.invoice-detail-row:last-child {
  border-bottom: none;
}

.invoice-detail-label {
  width: 50%;
  font-size: 12px;
  font-weight: bold;
  padding: 5px;
  border-right: 1px solid black;
}

.invoice-detail-value {
  width: 50%;
  padding: 5px;
}

.detail-input {
  width: 100%;
  border: none;
  font-size: 12px;
  background: transparent;
}

.consignee-section {
  display: flex;
  margin-bottom: 5px;
}

.consignee-box {
  border: 1px solid black;
  width: 60%;
  margin-right: 5px;
}

.buyer-details-right {
  width: 40%;
  display: flex;
  flex-direction: column;
}

.other-ref-box {
  border: 1px solid black;
  padding: 5px;
  margin-bottom: 5px;
}

.other-ref-label {
  font-size: 12px;
  font-weight: bold;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  display: inline-block;
  margin-bottom: 5px;
}

.other-ref-input {
  width: 100%;
  padding: 3px;
  border: 1px solid #ccc;
  font-size: 12px;
}

.buyer-box {
  border: 1px solid black;
  flex-grow: 1;
}

/* Shipping Details Section */
.shipping-details-section {
  border: 1px solid black;
  margin-bottom: 5px;
}

.shipping-row {
  display: flex;
  border-bottom: 1px solid black;
}

.shipping-row:last-child {
  border-bottom: none;
}

.shipping-cell {
  flex: 1;
  border-right: 1px solid black;
}

.shipping-cell:last-child {
  border-right: none;
}

.shipping-label {
  font-size: 12px;
  font-weight: bold;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  text-align: center;
  margin: 5px;
}

.shipping-value {
  padding: 5px;
  height: auto;
}

.shipping-input {
  width: 100%;
  border: none;
  font-size: 12px;
  background: transparent;
}

/* Marks and Package Section */
.marks-package-section {
  display: flex;
  margin-bottom: 5px;
}

.marks-box {
  border: 1px solid black;
  width: 33.33%;
  margin-right: 5px;
}

.marks-label {
  font-size: 12px;
  font-weight: bold;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  text-align: center;
  margin: 5px;
}

.marks-value {
  padding: 5px;
  height: auto;
}

.marks-textarea {
  width: 100%;
  height: 60px;
  border: none;
  resize: none;
  font-size: 12px;
  background: transparent;
}

.package-box {
  border: 1px solid black;
  width: 33.33%;
  margin-right: 5px;
}

.package-label {
  font-size: 12px;
  font-weight: bold;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  text-align: center;
  margin: 5px;
}

.package-value {
  padding: 5px;
  height: 60px;
}

.package-input {
  width: 100%;
  border: none;
  font-size: 12px;
  background: transparent;
}

.description-box {
  border: 1px solid black;
  width: 33.33%;
}

.description-label {
  font-size: 12px;
  font-weight: bold;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  text-align: center;
  margin: 5px;
}

.description-value {
  padding: 5px;
  height: 60px;
}

.description-input {
  width: 100%;
  border: none;
  font-size: 12px;
  background: transparent;
}

/* Goods Table Section */
.goods-table-container {
  margin: 5px 0;
}

.goods-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid black;
}

.goods-table th, .goods-table td {
  border: 1px solid black;
  padding: 5px;
  text-align: center;
  font-size: 12px;
}

.goods-table th {
  font-weight: bold;
  background-color: #f5f5f5;
}

.quality-column {
  width: 10%;
}

.design-column {
  width: 15%;
}

.pieces-column {
  width: 10%;
  text-align: center;
}

.quantity-column {
  width: 15%;
  text-align: center;
}

.cif-column {
  width: 10%;
}

.rate-column {
  width: 15%;
}

.euro-column {
  width: 25%;
  text-align: center;
}

.table-input {
  width: 95%;
  border: none;
  text-align: center;
  font-size: 12px;
  background: transparent;
}

.add-button {
  margin: 10px 0;
  padding: 5px 10px;
  background-color: #0d56b6b9;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* Summary Section */
.summary-section {
  border: 1px solid black;
  margin: 5px 0;
}

.summary-table {
  width: 100%;
  border-collapse: collapse;
}

.summary-table td {
  border: 1px solid black;
  padding: 5px;
  font-size: 12px;
}

.total-row td {
  border-bottom: 1px solid black;
}

.total-label {
  width: 10%;
  font-weight: bold;
}

.empty-cell {
  width: 15%;
}

.total-pieces {
  width: 10%;
  text-align: center;
}

.total-quantity {
  width: 15%;
  text-align: center;
}

.total-cif {
  width: 10%;
  text-align: center;
  font-weight: bold;
}

.total-euro {
  width: 15%;
  text-align: center;
  font-weight: bold;
}

.total-amount {
  width: 25%;
  text-align: center;
}

.freight-row td {
  border-bottom: 1px solid black;
}

.freight-label {
  font-weight: bold;
}

.freight-value {
  text-align: center;
}

.freight-input {
  width: 95%;
  border: none;
  text-align: center;
  font-size: 12px;
  background: transparent;
}

.grand-total-row td {
  border-bottom: 1px solid black;
}

.grand-total-label {
  font-weight: bold;
}

.grand-total-value {
  text-align: center;
  font-weight: bold;
}

.composition-row {
  display: flex;
  font-size: 12px;
  padding: 5px;
}

.wool-section, .cotton-section {
  display: flex;
  margin-right: 20px;
}

.wool-label, .cotton-label, .weight-label {
  font-weight: bold;
  margin-right: 5px;
}

.weight-section {
  display: flex;
  margin-left: auto;
}

.gross-weight {
  margin-right: 20px;
}

/* Amount in Words Section */
.amount-words-section {
  border: 1px solid black;
  margin: 5px 0;
}

.amount-words-label {
  font-weight: bold;
  font-size: 12px;
  padding: 5px;
  /* border: 1px solid black; */
  /* background-color: #f5f5f5; */
  display: inline-block;
  margin: 5px;
}

.amount-words-value {
  padding: 5px;
}

.amount-words-textarea {
  width: 100%;
  border: none;
  font-size: 12px;
  resize: none;
  background: transparent;
  height: 30px;
}

/* Declaration Section */
.declaration-section {
  display: flex;
  margin: 5px 0;
}

.declaration-box {
  flex: 2;
  border: 1px solid black;
  padding: 5px;
  margin-right: 5px;
}

.declaration-label {
  font-weight: bold;
  font-size: 12px;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  display: inline-block;
  margin-bottom: 5px;
}

.declaration-text {
  font-size: 12px;
}

.signature-box {
  flex: 1;
  border: 1px solid black;
  padding: 5px;
}

.signature-label {
  font-weight: bold;
  font-size: 12px;
  /* border: 1px solid black; */
  padding: 5px;
  /* background-color: #f5f5f5; */
  display: inline-block;
  margin-bottom: 5px;
}

/* .signature-value {
  height: 50px;
} */

.signature-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 5px;
}

.signature-preview {
  position: relative;
  margin-bottom: 10px;
  border: 1px dashed #ccc;
  padding: 5px;
  max-width: 200px;
}

.signature-image {
  max-width: 100%;
  max-height: 100px;
}

.remove-signature-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: white;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.signature-input {
  width: 100%;
  border: none;
  font-size: 12px;
  background: transparent;
}

.signature-size-hint {
  margin-top: 5px;
  color: #666;
  font-style: italic;
  text-align: center;
}

/* Submit Button */
.text-center {
  text-align: center;
  margin-top: 20px;
}

button[type="submit"] {
  padding: 8px 16px;
  background-color: #1a55d3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button[type="submit"]:hover {
  background-color: #1a58dd;
}

/* Material Form Field Styles */
.full-width {
  width: 100%;
}

::ng-deep .mat-form-field-wrapper {
  margin-bottom: -1.25em;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.5em 0;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
  padding: 0 0.75em 0 0.75em;
  margin-top: -0.25em;
  position: relative;
}

/* Cascading Dropdowns Styles */
.cascading-dropdowns {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.dropdown-field {
  width: 100%;
}

@media (min-width: 768px) {
  .cascading-dropdowns {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .dropdown-field {
    flex: 1;
    min-width: 120px;
    margin-right: 10px;
  }

  .dropdown-field:last-child {
    margin-right: 0;
  }
}
