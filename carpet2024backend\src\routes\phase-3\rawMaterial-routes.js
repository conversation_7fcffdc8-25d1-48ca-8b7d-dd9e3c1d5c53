const express = require('express');
const router = express.Router();
const rawMaterialGroupController = require('../../controller/rawMaterial-controller');
const uploadFiles = require('../../middleware/allfile-middleware');

// Create a raw material group
router.post('/raw-material-groups', uploadFiles, (req, res) => {
  rawMaterialGroupController.createRawMaterialGroup(req, res);
});

// Get all raw material groups
router.get('/raw-material-groups', (req, res) => {
  rawMaterialGroupController.getAllRawMaterialGroups(req, res);
});

// Get a raw material group by ID
router.get('/raw-material-groups/:id', (req, res) => {
  rawMaterialGroupController.getRawMaterialGroupById(req, res);
});

// Update a raw material group by ID
router.put('/raw-material-groups/:id', uploadFiles, (req, res) => {
  rawMaterialGroupController.updateRawMaterialGroup(req, res);
});

// Delete a raw material group by ID
router.delete('/raw-material-groups/:id', (req, res) => {
  rawMaterialGroupController.deleteRawMaterialGroup(req, res);
});

module.exports = router;
