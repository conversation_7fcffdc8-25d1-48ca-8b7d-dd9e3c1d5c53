import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';

export interface ColourLagat {
  colour: {
    newColor: string;
    companyColorCode: string;
    remark: string;
  };
  lagat: number;
  issueValue?: string;
  toIssueValue?: string;
}

@Component({
  selector: 'app-kati-modal',
  templateUrl: './kati-modal.component.html',
  styleUrls: ['./kati-modal.component.css']
})
export class KatiModalComponent implements OnInit {
  colourLagats: ColourLagat[] = [];
  totalArea: number = 0;
  katiLagat: number = 0;
  isEditMode: boolean = false;

  // Angular Material Table properties
  displayedColumns: string[] = ['srNo', 'colour', 'lagat', 'carpetLagat', 'issue', 'toIssue'];
  dataSource = new MatTableDataSource<ColourLagat>([]);

  constructor(
    public dialogRef: MatDialogRef<KatiModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    console.log('🔍 Kati Modal Data:', this.data);
    this.isEditMode = this.data.isEditMode || false;
    this.initializeData();

    // Add debugging for table display
    console.log('🔍 Final colourLagats:', this.colourLagats);
    console.log('🔍 DataSource data:', this.dataSource.data);
    console.log('🔍 DisplayedColumns:', this.displayedColumns);
    console.log('🔍 Is Edit Mode:', this.isEditMode);
  }

  setDigitIssue(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      val.target.value = _val.toFixed(3);
    }
  }

  setDigitToIssue(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      val.target.value = _val.toFixed(3);
    }
  }

  initializeData(): void {
    // Get total area from the passed data
    this.totalArea = this.data.totalArea || 5.00; // Default value for testing
    this.katiLagat = this.data.katiLagat || 1.500; // Default value for testing

    console.log('🔍 Total Area:', this.totalArea);
    console.log('🔍 Kati Lagat:', this.katiLagat);

    // Get colourLagats from the materialLagatList
    // Find the matching material lagat based on quality and design from filterData
    if (this.data.materialLagatList && this.data.materialLagatList.length > 0 &&
        this.data.filterData && this.data.filterData.length > 0) {

      const issueData = this.data.filterData[0]; // Get first item's data
      console.log('🔍 Issue Data for matching:', issueData);

      // Get quality and design IDs for matching
      const qualityId = typeof issueData.quality === 'object' ? issueData.quality._id : issueData.quality;
      const designId = typeof issueData.design === 'object' ? issueData.design._id : issueData.design;

      console.log('🔍 Looking for match with qualityId:', qualityId, 'designId:', designId);

      // Find matching material lagat entry
      const matchingLagat = this.data.materialLagatList.find((lagat: any) => {
        const lagatQualityId = typeof lagat.quality === 'object' ? lagat.quality._id : lagat.quality;
        const lagatDesignId = typeof lagat.AddDesign === 'object' ? lagat.AddDesign._id : lagat.AddDesign;

        const qualityMatch = lagatQualityId === qualityId;
        const designMatch = lagatDesignId === designId;

        console.log('🔍 Checking lagat:', {
          lagatId: lagat._id,
          lagatQualityId,
          lagatDesignId,
          qualityMatch,
          designMatch
        });

        return qualityMatch && designMatch;
      });

      if (matchingLagat && matchingLagat.AddDesign && matchingLagat.AddDesign.colourLagats) {
        console.log('✅ Found matching lagat with colourLagats:', matchingLagat.AddDesign.colourLagats);

        // First, create array from design colors
        const designColors = matchingLagat.AddDesign.colourLagats.map((item: any) => ({
          colour: item.colour || {},
          lagat: parseFloat(item.lagat) || 0,
          issueValue: '',
          toIssueValue: '',
        }));

        // If we have existing kati data, we need to merge it properly
        if (this.data.existingKatiData && this.data.existingKatiData.length > 0) {
          console.log('🔍 Merging existing kati data:', this.data.existingKatiData);

          // Create a map to track which colors we've processed
          const processedColors = new Map();
          const finalColors: any[] = [];

          // First, process design colors and match with existing data
          designColors.forEach((designColor: any) => {
            const colorId = designColor.colour._id || designColor.colour.id;
            if (colorId) {
              // Find matching existing data by color ID
              const existingItem = this.data.existingKatiData.find((existing: any) =>
                existing.colour && (existing.colour._id === colorId || existing.colour.id === colorId)
              );

              if (existingItem) {
                designColor.issueValue = existingItem.issueValue || '';
                designColor.toIssueValue = existingItem.toIssueValue || '';
              }

              processedColors.set(colorId, true);
              finalColors.push(designColor);
            }
          });

          // Then, add any existing colors that weren't in the design
          this.data.existingKatiData.forEach((existingItem: any) => {
            const colorId = existingItem.colour && (existingItem.colour._id || existingItem.colour.id);
            if (colorId && !processedColors.has(colorId)) {
              finalColors.push({
                colour: existingItem.colour || {},
                lagat: parseFloat(existingItem.lagat) || 0,
                issueValue: existingItem.issueValue || '',
                toIssueValue: existingItem.toIssueValue || '',
              });
            }
          });

          this.colourLagats = finalColors;
        } else {
          // No existing data, just use design colors
          this.colourLagats = designColors;
        }
      } else {
        console.log('⚠️ No matching lagat found, using sample data');
        
      }
    } else {
      console.log('⚠️ No material lagat data available, using sample data');
    }

    console.log('✅ Initialized colourLagats:', this.colourLagats);

    // Update the dataSource for Angular Material Table
    this.dataSource.data = this.colourLagats;
  }

  getColourDisplay(item: ColourLagat): string {
    if (item.colour) {
      return `${item.colour.newColor || ''} - ${item.colour.companyColorCode || ''} - ${item.colour.remark || ''}`;
    }
    return '';
  }

  calculateCarpetLagat(lagat: number): string {
    // Carpet Lagat = lagat * totalArea
    const carpetLagat = lagat * this.totalArea;
    return carpetLagat.toFixed(3);
  }

  onInputChange(element: ColourLagat, field: string, event: any): void {
    const value = event.target.value;

    if (field === 'issue') {
      element.issueValue = value;
    } else if (field === 'toIssue') {
      element.toIssueValue = (value) ;
    }

    console.log(`Updated ${field} for colour ${element.colour?.newColor}:`, value);
  }

  onSave(): void {
    console.log('Saving Kati data:', this.colourLagats);
    // Implement save logic here
    this.dialogRef.close(this.colourLagats);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
