.text-hd-n{
  display: flex;
  gap: 20px;
  width: 100%;
  font-size: 15px;
}
.hd-text h4{
  width: 100%;
}
.card1{
  border: none;
}
.line1 ul li {
  display: inline;
  font-size: 15px;
}
.line1 ul {
  display: flex;
  gap: 40px;
}
span{
  border: 1px solid black;
}

.bill ul {
  display: flex;
  gap: 60px;
}
.mdle-hd {

  display: flex;
  justify-content: space-between;
}

.md-hd1 h6 {
  margin-left: 24px;
}

/* .md-hd{
  padding-left: 70vh;
} */

.space1{
  height: 9vh;
}

.footer ul li {
  display: inline;
}
.footer ul{
 display: flex;
 gap: 215px;
}

.footer11{
  display: flex;
}
.footer12 ul{
 list-style: none;
}
.footer12 ul li{
  font-size: 10px;
}

@media (max-width: 767px) {
  .footer11{
      display: flex;
  }

}


/* second-page */

.second-page11 {
  display: flex;
  justify-content: space-between;
}

.second-page{
  margin-top: 180px;
}

.card-text{
  display: flex;
  justify-content: space-between;
}
.second-text22{
  display: flex;
  gap: 20px;
}


.footer{
  display: flex;
  gap: 18%;
}
/* second-third-heading  */

.counter-heading-text{
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.counter-heading-text1{
  height: 120px;
  width: 24%;

}
.counter-heading-text2{
  height: 120px;
  width: 100%;
  line-height: 10px;

}
.counter-heading-text3{
  height: 120px;
  width: 25%;

}
.counter-heading-text3 p{
 font-size: 15px;
}
.counter-heading-text4{
  height: 120px;
  width: 25%;

}
.counter-heading-text4 p{
 font-size: 15px;

}
.counter-heading-text5{
  height: 120px;
  width: 25%;
}

.counter-heading-text5 p{
  font-size: 15px;

}
/* last-counter */
.last-counter1{
  display: flex;
  justify-content: space-between;
}
.mdle-hd{
  display: flex;
  gap: 20px;
}
.count{
  border: none;

}
.mat-h5, .mat-typography .mat-h5, .mat-typography h5 {
  font: 400 calc(20px* .83) / 20px Roboto, sans-serif;
  margin: 0 0 12px;
}
