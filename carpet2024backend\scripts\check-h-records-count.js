// Script to check H- records count and identify any issues
const mongoose = require('mongoose');

const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function checkRecords() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 15000,
      connectTimeoutMS: 10000
    });
    console.log('✅ Connected successfully');
    
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get all H- records
    const hRecords = await collection.find(
      { receiveNo: { $regex: /^H-/ } },
      { projection: { receiveNo: 1, weaverName: 1, design: 1 } }
    ).sort({ receiveNo: 1 }).toArray();
    
    console.log(`📊 Total H- records found: ${hRecords.length}`);
    console.log('\n📋 First 10 H- records:');
    hRecords.slice(0, 10).forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo} - ${record.weaverName} - ${record.design}`);
    });
    
    console.log('\n📋 Last 10 H- records:');
    hRecords.slice(-10).forEach((record, index) => {
      console.log(`${hRecords.length - 9 + index}. ${record.receiveNo} - ${record.weaverName} - ${record.design}`);
    });
    
    // Check for any patterns in the data
    const designCounts = {};
    const weaverCounts = {};
    
    hRecords.forEach(record => {
      designCounts[record.design] = (designCounts[record.design] || 0) + 1;
      weaverCounts[record.weaverName] = (weaverCounts[record.weaverName] || 0) + 1;
    });
    
    console.log('\n📊 Design distribution:');
    Object.entries(designCounts).sort((a, b) => b[1] - a[1]).slice(0, 10).forEach(([design, count]) => {
      console.log(`  ${design}: ${count} records`);
    });
    
    console.log('\n📊 Weaver distribution (first 10):');
    Object.entries(weaverCounts).sort((a, b) => b[1] - a[1]).slice(0, 10).forEach(([weaver, count]) => {
      console.log(`  ${weaver}: ${count} records`);
    });
    
    // Check if we have the expected 393 records from our import data
    console.log('\n🔍 Expected vs Actual:');
    console.log(`Expected: 393 H- records`);
    console.log(`Actual: ${hRecords.length} H- records`);
    console.log(`Difference: ${393 - hRecords.length} records`);
    
    if (hRecords.length === 393) {
      console.log('🎉 ALL H- FORMAT RECORDS ARE IMPORTED!');
    } else if (hRecords.length < 393) {
      console.log('⚠️ Some records are missing');
    } else {
      console.log('⚠️ More records than expected');
    }
    
    await mongoose.connection.close();
    console.log('\n🔌 Connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkRecords();
