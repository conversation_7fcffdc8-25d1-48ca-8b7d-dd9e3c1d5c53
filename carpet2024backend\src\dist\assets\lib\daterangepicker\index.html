<!DOCTYPE html>
<html lang="en">
    <head>

        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <title>Date Range Picker &mdash; JavaScript Date &amp; Time Picker Library</title>

        <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.0/umd/popper.min.js" integrity="sha384-cs/chFZiN24E4KMATLdqdvsezGxaGsi4hLGOzlXwp5UZB1LY//20VyM2taTB4QvJ" crossorigin="anonymous"></script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/js/bootstrap.min.js" integrity="sha384-uefMccjFJAIv6A+rW+L4AHf99KvxDjWSu1z9VI8SKNVmz4sk7buKt/6v9KI65qnm" crossorigin="anonymous"></script>

        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.22.1/moment.min.js"></script>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/trianglify/0.2.1/trianglify.min.js"></script>

        <script type="text/javascript" src="daterangepicker.js"></script>
        <link rel="stylesheet" type="text/css" href="daterangepicker.css" />

        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css" integrity="sha384-9gVQ4dYFwwWSjIDZnLEWnxCjeSWFphJiwGPXr1jddIhOegiu1FwO5qRGvFXOdJZ4" crossorigin="anonymous">

        <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.0.10/css/all.css" integrity="sha384-+d0P83n9kaQMCwj8F4RJB66tzIwOKmrdb46+porD/OvrJ+37WqIM7UoBtwHO6Nlg" crossorigin="anonymous">

        <script src="website.js"></script>
        <link rel="stylesheet" type="text/css" href="website.css" />

        <meta name="google-site-verification" content="1fP-Eo9i1ozV4MUlqZv2vsLv1r7tvYutUb6i38v0_vg" />
    </head>
    <body>

        <nav class="navbar navbar-expand-sm navbar-dark bg-dark" style="padding: 0; justify-content: space-between">
            <div class="container">
                <ul class="navbar-nav">
                    <li class="nav-item" style="padding-left: 0"><a class="nav-link" href="/" style="color: #eeeeee"><i class="fa fa-calendar"></i>&nbsp; Date Range Picker</a></li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item"><a class="nav-link" href="https://www.improvely.com" style="color: #33beff"><i class="fa fa-chart-bar"></i>&nbsp; Improvely</a></li>
                    <li class="nav-item"><a class="nav-link" href="https://www.w3counter.com" style="color: #ff9999"><i class="fa fa-chart-pie"></i>&nbsp; W3Counter</a></li>
                    <li class="nav-item"><a class="nav-link" href="https://www.websitegoodies.com" style="color: #f49a16"><i class="fa fa-wrench"></i>&nbsp; Website Goodies</a></li>
                </ul>
            </div>
        </nav>

        <div id="jumbo">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 col-sm-12">

                        <h1 style="margin: 0 0 10px 0">Date Range Picker</h1>
                        <p style="font-size: 18px; margin-bottom: 0">
                            A JavaScript component for choosing date ranges,
                            dates and times.
                        </p>

                    </div>
                    <div class="col-md-6 col-sm-12" style="text-align: right; padding-right: 0">

                        <div style="margin-bottom: 10px; margin-right: 15px">
                            <a href="https://github.com/dangrossman/daterangepicker" class="btn btn-light"><i class="fab fa-github-alt"></i>&nbsp; View on GitHub</a>

                            &nbsp;

                            <a href="https://github.com/dangrossman/daterangepicker/archive/master.zip" class="btn btn-success"><i class="fas fa-download"></i>&nbsp; Download ZIP</a>
                        </div>

                        <div style="margin-right: 0px">
                            <iframe src="https://ghbtns.com/github-btn.html?user=dangrossman&repo=daterangepicker&type=star&count=true&size=large" frameborder="0" scrolling="0" width="160px" height="30px"></iframe>

                            <iframe src="https://ghbtns.com/github-btn.html?user=dangrossman&repo=daterangepicker&type=watch&count=true&size=large&v=2" frameborder="0" scrolling="0" width="160px" height="30px"></iframe>

                            <iframe src="https://ghbtns.com/github-btn.html?user=dangrossman&repo=daterangepicker&type=fork&count=true&size=large" frameborder="0" scrolling="0" width="160px" height="30px"></iframe>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div class="container" style="padding: 0 30px" id="main">
            <div class="row">
                <div class="leftcol d-none d-lg-block d-xl-block">
                    <div id="menu" class="list-group" style="margin-bottom: 10px">
                        <a class="list-group-item" href="#usage">Getting Started</a>
                        <a class="list-group-item" href="#examples">Examples</a>
                        <a class="list-group-item" href="#options">Options, Methods &amp; Events</a>
                        <a class="list-group-item" href="#config">Configuration Generator</a>
                        <a class="list-group-item" href="#license">License &amp; Comments</a>
                    </div>

                    <div style="width: 300px; min-width: 300px">
                        <script async src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
                        <!-- DRP Responsive -->
                        <ins class="adsbygoogle"
                             style="display:block"
                             data-ad-client="ca-pub-9095657276960731"
                             data-ad-slot="**********"
                             data-ad-format="auto"></ins>
                        <script>
                        (adsbygoogle = window.adsbygoogle || []).push({});
                        </script>
                    </div>

                </div>
                <div class="rightcol">

                    <p>Originally created for reports at <a href="https://www.improvely.com">Improvely</a>, the Date Range Picker can be attached to any webpage element to pop up two calendars for selecting dates, times, or predefined ranges like "Last 30 Days".</p>

                    <a style="display: block; height: 300px; background: url('drp.png') top right no-repeat; background-size: cover; border: 1px solid #ccc; margin-bottom: 30px" href="https://awio.iljmp.com/5/drpdemo" title="Click for a Live Demo"></a>

                    <h1><a id="usage" href="#usage">Getting Started</a></h1>

                    <p>
                        To get started, include jQuery, Moment.js and Date Range Picker's files in your webpage:
                    </p>

                    <script src="https://gist.github.com/dangrossman/4879503153c6a7a0b3b6ebd64e0383b7.js"></script>

                    <p style="margin-bottom: 10px">
                        Then attach a date range picker to whatever you want to trigger it:
                    </p>

                    <div class="row">
                        <div class="col-8">
                            <label>Code:</label>
                            <script src="https://gist.github.com/dangrossman/f460cf1243d8ffb08c749730e89c2f3d.js"></script>
                        </div>
                        <div class="col-4">
                            <label>Produces:</label>
                            <input type="text" name="dates" class="form-control pull-right" />
                            <script>
                                $(function() {
                                    $('input[name="dates"]').daterangepicker({ startDate: moment(), endDate: moment().add(2, 'day')});
                                })
                            </script>
                        </div>
                    </div>

                    <p>
                        You can customize Date Range Picker with options, and get notified when the user chooses new dates by providing a callback function.
                    </p>

                    <h1><a id="examples" href="#examples">Examples</a></h1>

                    <!-- Example 1 -->
                    <h2><a data-toggle="nothing" href="#example1">Simple Date Range Picker With a Callback</a></h2>
                    <div class="collapsable" id="example1">
                        <div class="row">
                            <div class="col-8">
                                <label>Code:</label>
                                <script src="https://gist.github.com/dangrossman/e118af4dbadc5177d7494dba9d3295d1.js"></script>
                            </div>
                            <div class="col-4">
                                <label>Produces:</label>
                                <input type="text" name="daterange" class="form-control" value="1/01/2018 - 01/15/2018" />

                                <script>
                                $(function() {
                                  $('input[name="daterange"]').daterangepicker({
                                    opens: 'left'
                                  }, function(start, end, label) {
                                    console.log("A new date selection was made: " + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD'));
                                  });
                                });
                                </script>
                            </div>
                        </div>
                    </div>

                    <!-- Example 2 -->
                    <h2><a data-toggle="nothing" href="#example2">Date Range Picker With Times</a></h2>
                    <div class="collapsable" id="example2">
                        <div class="row">
                            <div class="col-8">
                                <label>Code:</label>
                                <script src="https://gist.github.com/dangrossman/14db17599e24652db7401ed2448eb91a.js"></script>
                            </div>
                            <div class="col-4">
                                <label>Produces:</label>
                                <input type="text" name="datetimes" class="form-control pull-right" />

                                <script>
                                $(function() {
                                  $('input[name="datetimes"]').daterangepicker({
                                    timePicker: true,
                                    startDate: moment().startOf('hour'),
                                    endDate: moment().startOf('hour').add(32, 'hour'),
                                    locale: {
                                      format: 'M/DD hh:mm A'
                                    }
                                  });
                                });
                                </script>
                            </div>
                        </div>
                    </div>

                    <!-- Example 3 -->
                    <h2><a data-toggle="nothing" href="#example3">Single Date Picker</a></h2>
                    <div class="collapsable" id="example3">
                        <div class="row">
                            <div class="col-8">
                                <label>Code:</label>
                                <script src="https://gist.github.com/dangrossman/98d8f1c304328c191b1ad33ac21354fd.js"></script>
                            </div>
                            <div class="col-4">
                                <label>Produces:</label>
                                <input type="text" name="birthday" class="form-control pull-right" value="10/24/1984" />

                                <script>
                                $(function() {
                                  $('input[name="birthday"]').daterangepicker({
                                    singleDatePicker: true,
                                    showDropdowns: true,
                                    minYear: 1901,
                                    maxYear: parseInt(moment().format('YYYY'),10)
                                  }, function(start, end, label) {
                                    var years = moment().diff(start, 'years');
                                    alert("You are " + years + " years old!");
                                  });
                                });
                                </script>
                            </div>
                        </div>
                    </div>

                    <!-- Example 4 -->
                    <h2><a data-toggle="nothing" href="#example4">Predefined Date Ranges</a></h2>
                    <div class="collapsable" id="example4">
                        <div class="row">
                            <div class="col-8">
                                <label>Code:</label>
                                <script src="https://gist.github.com/dangrossman/8c6747b82572bc860364f17258004dbb.js"></script>
                            </div>
                            <div class="col-4">
                                <label>Produces:</label>

                                <div id="reportrange" class="pull-right" style="background: #fff; cursor: pointer; padding: 5px 10px; border: 1px solid #ccc; width: 100%">
                                    <i class="fa fa-calendar"></i>&nbsp;
                                    <span></span> <i class="fa fa-caret-down"></i>
                                </div>

                                <script type="text/javascript">
                                $(function() {

                                    var start = moment().subtract(29, 'days');
                                    var end = moment();

                                    function cb(start, end) {
                                        $('#reportrange span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
                                    }

                                    $('#reportrange').daterangepicker({
                                        startDate: start,
                                        endDate: end,
                                        ranges: {
                                           'Today': [moment(), moment()],
                                           'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                                           'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                                           'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                                           'This Month': [moment().startOf('month'), moment().endOf('month')],
                                           'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                                        }
                                    }, cb);

                                    cb(start, end);
                                    
                                });
                                </script>

                            </div>
                        </div>
                    </div>

                    <!-- Example 5 -->
                    <h2><a data-toggle="nothing" href="#example5">Input Initially Empty</a></h2>
                    <div class="collapsable" id="example5">
                        <div class="row">
                            <div class="col-8">
                                <label>Code:</label>
                                <script src="https://gist.github.com/dangrossman/d50376f3467f69e7fb5570afd07dc921.js"></script>
                            </div>
                            <div class="col-4">
                                <label>Produces:</label>
                                <input type="text" name="datefilter" class="form-control pull-right" value="" />
                                 
                                <script type="text/javascript">
                                $(function() {

                                  $('input[name="datefilter"]').daterangepicker({
                                      autoUpdateInput: false,
                                      locale: {
                                          cancelLabel: 'Clear'
                                      }
                                  });

                                  $('input[name="datefilter"]').on('apply.daterangepicker', function(ev, picker) {
                                      $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
                                  });

                                  $('input[name="datefilter"]').on('cancel.daterangepicker', function(ev, picker) {
                                      $(this).val('');
                                  });

                                });
                                </script>
                            </div>
                        </div>
                    </div>

                    <h1 style="margin-top: 30px"><a id="options" href="#options">Options</a></h1>

                    <ul class="nobullets">
                        <li>
                            <code>startDate</code> (Date or string) The beginning date of the initially selected date range. If you provide a string, it must match the date format string set in your <code>locale</code> setting.
                        </li>
                        <li>
                            <code>endDate</code>: (Date or string) The end date of the initially selected date range.
                        </li>
                        <li>
                            <code>minDate</code>: (Date or string) The earliest date a user may select.
                        </li>
                        <li>
                            <code>maxDate</code>: (Date or string) The latest date a user may select.
                        </li>
                        <li>
                            <code>maxSpan</code>: (object) The maximum span between the selected start and end dates. Check off <code>maxSpan</code> in the configuration generator for an example of how to use this. You can provide any object the <code>moment</code> library would let you add to a date.
                        </li>
                        <li>
                            <code>showDropdowns</code>: (true/false) Show year and month select boxes above calendars to jump to a specific month and year.
                        </li>
                        <li>
                            <code>minYear</code>: (number) The minimum year shown in the dropdowns when <code>showDropdowns</code> is set to true.
                        </li>
                        <li>
                            <code>maxYear</code>: (number) The maximum year shown in the dropdowns when <code>showDropdowns</code> is set to true.
                        </li>
                        <li>
                            <code>showWeekNumbers</code>: (true/false) Show localized week numbers at the start of each week on the calendars.
                        </li>
                        <li>
                            <code>showISOWeekNumbers</code>: (true/false) Show ISO week numbers at the start of each week on the calendars.
                        </li>
                        <li>
                            <code>timePicker</code>: (true/false) Adds select boxes to choose times in addition to dates.
                        </li>
                        <li>
                            <code>timePickerIncrement</code>: (number) Increment of the minutes selection list for times (i.e. 30 to allow only selection of times ending in 0 or 30).
                        </li>
                        <li>
                            <code>timePicker24Hour</code>: (true/false) Use 24-hour instead of 12-hour times, removing the AM/PM selection.
                        </li>
                        <li>
                            <code>timePickerSeconds</code>: (true/false) Show seconds in the timePicker.
                        </li>
                        <li>
                            <code>ranges</code>: (object) Set predefined date ranges the user can select from. Each key is the label for the range, and its value an array with two dates representing the bounds of the range. Click <code>ranges</code> in the configuration generator for examples.
                        </li>
                        <li>
                            <code>showCustomRangeLabel</code>: (true/false) Displays "Custom Range" at
                            the end of the list of predefined ranges, when the <code>ranges</code> option is used.
                            This option will be highlighted whenever the current date range selection does not match one of the predefined ranges. Clicking it will display the calendars to select a new range.
                        </li>
                        <li>
                            <code>alwaysShowCalendars</code>: (true/false) Normally, if you use the <code>ranges</code> option to specify pre-defined date ranges, calendars for choosing a custom date range are not shown until the user clicks "Custom Range". When this option is set to true, the calendars for choosing a custom date range are always shown instead.
                        </li>
                        <li>
                            <code>opens</code>: ('left'/'right'/'center') Whether the picker appears aligned to the left, to the right, or centered under the HTML element it's attached to.
                        </li>
                        <li>
                            <code>drops</code>: ('down'/'up') Whether the picker appears below (default) or above the HTML element it's attached to.
                        </li>
                        <li>
                            <code>buttonClasses</code>: (string) CSS class names that will be added to both the apply and cancel buttons.
                        </li>
                        <li>
                            <code>applyButtonClasses</code>: (string) CSS class names that will be added only to the apply button.
                        </li>
                        <li>
                            <code>cancelButtonClasses</code>: (string) CSS class names that will be added only to the cancel button.
                        </li>
                        <li>
                            <code>locale</code>: (object) Allows you to provide localized strings for buttons and labels, customize the date format, and change the first day of week for the calendars.
                            Check off <code>locale</code> in the configuration generator to see how
                            to customize these options.
                        </li>
                        <li>
                            <code>singleDatePicker</code>: (true/false) Show only a single calendar to choose one date, instead of a range picker with two calendars. The start and end dates provided to your callback will be the same single date chosen.
                        </li>
                        <li>
                            <code>autoApply</code>: (true/false) Hide the apply and cancel buttons, and automatically apply a new date range as soon as two dates are clicked.
                        </li>
                        <li>
                            <code>linkedCalendars</code>: (true/false) When enabled, the two calendars displayed will always be for two sequential months (i.e. January and February), and both will be advanced when clicking the left or right arrows above the calendars. When disabled, the two calendars can be individually advanced and display any month/year.
                        </li>
                        <li>
                            <code>isInvalidDate</code>: (function) A function that is passed each date in the two
                            calendars before they are displayed, and may return true or false to indicate whether
                            that date should be available for selection or not.
                        </li>
                        <li>
                            <code>isCustomDate</code>: (function) A function that is passed each date in the two
                            calendars before they are displayed, and may return a string or array of CSS class names
                            to apply to that date's calendar cell.
                        </li>
                        <li>
                            <code>autoUpdateInput</code>: (true/false) Indicates whether the date range picker should
                            automatically update the value of the <code>&lt;input&gt;</code> element it's attached to at initialization and when the selected dates change.
                        </li>
                        <li>
                            <code>parentEl</code>: (string) jQuery selector of the parent element that the date range picker will be added to, if not provided this will be 'body'
                        </li>
                    </ul>

                    <h1 style="margin-top: 30px"><a id="methods" href="#methods">Methods</a></h1>

                    <p>
                        You can programmatically update the <code>startDate</code> and <code>endDate</code>
                        in the picker using the <code>setStartDate</code> and <code>setEndDate</code> methods.
                        You can access the Date Range Picker object and its functions and properties through 
                        data properties of the element you attached it to.
                    </p>

                    <script src="https://gist.github.com/dangrossman/8ff9b1220c9b5682e8bd.js"></script>

                    <br/>
                    
                    <ul class="nobullets">
                        <li>
                            <code>setStartDate(Date or string)</code>: Sets the date range picker's currently selected start date to the provided date
                        </li>
                        <li>
                            <code>setEndDate(Date or string)</code>: Sets the date range picker's currently selected end date to the provided date
                        </li>
                    </ul>

                    <p style="margin: 0"><b>Example usage:</b></p>

                    <script src="https://gist.github.com/dangrossman/e1a8effbaeacb50a1e31.js"></script>

                    <h1 style="margin-top: 30px"><a id="events" href="#events">Events</a></h1>

                    <p>
                        Several events are triggered on the element you attach the picker to, which you can listen for.
                    </p>

                    <ul class="nobullets">
                        <li>
                            <code>show.daterangepicker</code>: Triggered when the picker is shown
                        </li>
                        <li>
                            <code>hide.daterangepicker</code>: Triggered when the picker is hidden
                        </li>
                        <li>
                            <code>showCalendar.daterangepicker</code>: Triggered when the calendar(s) are shown
                        </li>
                        <li>
                            <code>hideCalendar.daterangepicker</code>: Triggered when the calendar(s) are hidden
                        </li>
                        <li>
                            <code>apply.daterangepicker</code>: Triggered when the apply button is clicked,
                            or when a predefined range is clicked
                        </li>
                        <li>
                            <code>cancel.daterangepicker</code>: Triggered when the cancel button is clicked
                        </li>
                    </ul>

                    <p>
                        Some applications need a "clear" instead of a "cancel" functionality, which can be achieved by changing the button label and watching for the cancel event:
                    </p>

                    <script src="https://gist.github.com/dangrossman/1bea78da703f2896564d.js"></script>

                    <br/>

                    <p>
                        While passing in a callback to the constructor is the easiest way to listen for changes in the selected date range, you can also do something every time the apply button is clicked even if the selection hasn't changed:
                    </p>

                    <script src="https://gist.github.com/dangrossman/0c6c911fea1459b5fd13.js"></script>

                    <h1 style="margin-top: 30px"><a id="config" href="#config">Configuration Generator</a></h1>

                    <div class="well configurator">
                                       
                      <form>
                          <div class="row">

                            <div class="col-md-4">

                              <div class="form-group">
                                <label for="parentEl">parentEl</label>
                                <input type="text" class="form-control" id="parentEl" value="" placeholder="body">
                              </div>

                              <div class="form-group">
                                <label for="startDate">startDate</label>
                                <input type="text" class="form-control" id="startDate" value="07/01/2017">
                              </div>

                              <div class="form-group">
                                <label for="endDate">endDate</label>
                                <input type="text" class="form-control" id="endDate" value="07/15/2017">
                              </div>

                              <div class="form-group">
                                <label for="minDate">minDate</label>
                                <input type="text" class="form-control" id="minDate" value="" placeholder="MM/DD/YYYY">
                              </div>

                              <div class="form-group">
                                <label for="maxDate">maxDate</label>
                                <input type="text" class="form-control" id="maxDate" value="" placeholder="MM/DD/YYYY">
                              </div>

                              <div class="form-group">
                                <label for="opens">opens</label>
                                <select id="opens" class="form-control">
                                  <option value="right" selected>right</option>
                                  <option value="left">left</option>
                                  <option value="center">center</option>
                                </select>
                              </div>

                              <div class="form-group">
                                <label for="drops">drops</label>
                                <select id="drops" class="form-control">
                                  <option value="down" selected>down</option>
                                  <option value="up">up</option>
                                </select>
                              </div>

                            </div>
                            <div class="col-md-4">

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="showDropdowns"> showDropdowns
                                </label>
                              </div>

                              <div class="form-group">
                                <label for="minYear">minYear</label>
                                <input type="text" class="form-control" id="minYear" value="">
                              </div>

                              <div class="form-group">
                                <label for="maxYear">maxYear</label>
                                <input type="text" class="form-control" id="maxYear" value="">
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="showWeekNumbers"> showWeekNumbers
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="showISOWeekNumbers"> showISOWeekNumbers
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="singleDatePicker"> singleDatePicker
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="timePicker"> timePicker
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="timePicker24Hour"> timePicker24Hour
                                </label>
                              </div>

                              <div class="form-group">
                                <label for="timePickerIncrement">timePickerIncrement (in minutes)</label>
                                <input type="text" class="form-control" id="timePickerIncrement" value="1">
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="timePickerSeconds"> timePickerSeconds
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="maxSpan"> maxSpan (example value)
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="locale"> locale (example settings)
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="autoApply"> autoApply
                                </label>
                              </div>

                            </div>
                            <div class="col-md-4">

                              <div class="form-group">
                                <label for="buttonClasses">buttonClasses</label>
                                <input type="text" class="form-control" id="buttonClasses" value="btn btn-sm">
                              </div>

                              <div class="form-group">
                                <label for="applyButtonClasses">applyButtonClasses</label>
                                <input type="text" class="form-control" id="applyButtonClasses" value="btn-primary">
                              </div>

                              <div class="form-group">
                                <label for="cancelButtonClasses">cancelButtonClasses</label>
                                <input type="text" class="form-control" id="cancelButtonClasses" value="btn-default">
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="ranges"> ranges (with example predefined ranges)
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="alwaysShowCalendars"> alwaysShowCalendars
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="showCustomRangeLabel" checked="checked"> showCustomRangeLabel
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="linkedCalendars" checked="checked"> linkedCalendars
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="autoUpdateInput" checked="checked"> autoUpdateInput
                                </label>
                              </div>

                            </div>

                          </div>
                      </form>

                    </div>

                    <div class="row">

                      <div class="col-4 demo">
                        <div style="position: relative">
                            <h2 style="margin-bottom: 15px">Your Date Range Picker</h2>
                            <input type="text" id="config-demo" class="form-control">
                        </div>
                      </div>

                      <div class="col-8">
                        <h2 style="margin-bottom: 15px">Your Configuration to Copy</h2>

                        <div class="well">
                          <textarea id="config-text" style="height: 300px; width: 100%; padding: 10px"></textarea>
                        </div>
                      </div>

                    </div>

                    <!-- License -->

                    <h1 style="margin-top: 30px"><a id="license" href="#license">License</a></h1>

                    <p>The MIT License (MIT)</p>

                    <p>Copyright (c) 2012-2018 <a href="http://www.dangrossman.info">Dan Grossman</a></p>

                    <p>
                        Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
                    </p>

                    <p>
                        The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
                    </p>

                    <p>
                        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
                    </p>

                    <h1 style="margin-top: 30px"><a id="config" href="#comments">Comments</a></h1>

                    <div id="disqus_thread"></div>
                    <script type="text/javascript">
                        /* * * CONFIGURATION VARIABLES: EDIT BEFORE PASTING INTO YOUR WEBPAGE * * */
                        var disqus_url = 'http://www.dangrossman.info/2012/08/20/a-date-range-picker-for-twitter-bootstrap/';
                        var disqus_identifier = '1045 http://www.dangrossman.info/?p=1045';
                        var disqus_container_id = 'disqus_thread';
                        var disqus_shortname = 'dangrossman';
                        var disqus_title = "A Date Range Picker for Bootstrap";

                        /* * * DON'T EDIT BELOW THIS LINE * * */
                        (function() {
                            var dsq = document.createElement('script'); dsq.type = 'text/javascript'; dsq.async = true;
                            dsq.src = 'https://' + disqus_shortname + '.disqus.com/embed.js';
                            (document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(dsq);
                        })();
                    </script>
                    <noscript>Please enable JavaScript to view the <a href="https://disqus.com/?ref_noscript">comments powered by Disqus.</a></noscript>

                </div>
            </div>
        </div>

        <!-- Begin W3Counter Tracking Code -->
        <script type="text/javascript" src="https://www.w3counter.com/tracker.js?id=90840"></script>
        <!-- End W3Counter Tracking Code -->

        <script type="text/javascript">
        var im_domain = 'awio';
        var im_project_id = 48;
        (function(e,t){window._improvely=[];var n=e.getElementsByTagName("script")[0];var r=e.createElement("script");r.type="text/javascript";r.src="https://"+im_domain+".iljmp.com/improvely.js";r.async=true;n.parentNode.insertBefore(r,n);if(typeof t.init=="undefined"){t.init=function(e,t){window._improvely.push(["init",e,t])};t.goal=function(e){window._improvely.push(["goal",e])};t.conversion=function(e){window._improvely.push(["conversion",e])};t.label=function(e){window._improvely.push(["label",e])}}window.improvely=t;t.init(im_domain,im_project_id)})(document,window.improvely||[])
        </script>

        <div id="footer">
            Copyright &copy; 2012-2019 <a href="http://www.dangrossman.info/">Dan Grossman</a>. 
        </div>

    </body>
</html>