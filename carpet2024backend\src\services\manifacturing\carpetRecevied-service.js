const carpetReceivedRepository = require('../../repositories/manifacturing/carpetReceived-repository');

const createCarpetReceived = async (data) => {
  return await carpetReceivedRepository.createCarpetReceived(data);
};

const getAllCarpetReceiveds = async () => {
  return await carpetReceivedRepository.getAllCarpetReceiveds();
};

const getCarpetReceivedById = async (id) => {
  return await carpetReceivedRepository.getCarpetReceivedById(id);
};

const updateCarpetReceived = async (id, data) => {
  return await carpetReceivedRepository.updateCarpetReceived(id, data);
};


const deleteCarpetReceived = async (id) => {
  return await carpetReceivedRepository.deleteCarpetReceived(id);
};

const getLastCarpetNo = async () => {
  return await carpetReceivedRepository.getLastCarpetReceived();
};

const getReceivedCarpetsByIssueIds = async (issueIds) => {
  return await carpetReceivedRepository.getReceivedCarpetsByIssueIds(issueIds);
};

module.exports = {
  createCarpetReceived,
  getAllCarpetReceiveds,
  getCarpetReceivedById,
  updateCarpetReceived,
  deleteCarpetReceived,
  getLastCarpetNo,
  getReceivedCarpetsByIssueIds
};
