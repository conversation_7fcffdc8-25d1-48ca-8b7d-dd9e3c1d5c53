const mongoose = require('mongoose');

const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function testApiResponse() {
  try {
    // Connect to MongoDB
    await mongoose.connect(DB_URL);
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get H- records with populated data (similar to API)
    const CarpetReceived = mongoose.model('CarpetReceived', new mongoose.Schema({}, { strict: false }));
    const hRecords = await CarpetReceived.find({ receiveNo: { $regex: /^H-/ } })
      .populate('K', 'branchCode branchName')
      .populate('weaverNumber', 'name weaverName')
      .lean();
    
    console.log(`\n📊 Found ${hRecords.length} H- records`);
    console.log('\n🔍 API Response Structure:');
    console.log('='.repeat(80));
    
    hRecords.slice(0, 3).forEach((record, index) => {
      console.log(`${index + 1}. Record: ${record.receiveNo}`);
      console.log(`   weaverName: "${record.weaverName}"`);
      console.log(`   weaverNumber:`, record.weaverNumber);
      console.log(`   K (branch):`, record.K);
      console.log(`   K.branchCode: ${record.K?.branchCode || 'N/A'}`);
      console.log(`   Expected Display: ${record.K?.branchCode || ''} - ${record.weaverName}`);
      console.log('   ---');
    });
    
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testApiResponse();
