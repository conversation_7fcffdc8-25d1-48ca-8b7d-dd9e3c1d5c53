const mongoose = require('mongoose');

const purchaseSchema = new mongoose.Schema({
materialGroup:{
    type:mongoose.Schema.Types.ObjectId,
    ref:"RawMaterialGroup",
    required: true,
},
description:{
    type:String,
    required: true, 
},
partyName:{
    type:mongoose.Schema.Types.ObjectId,
    ref:"WeaverEmployee",
    required: true,
},

purchase:{
    type:mongoose.Schema.Types.ObjectId,
    ref:'purchaserDetails',
    required: true,
},
date:{
    type:String,
    required: true,
},
woolQuality:{
    type:mongoose.Schema.Types.ObjectId,
    ref:"RawMaterialGroup"
},
count:{
    type:String,
    required: true,
},
colour:{
    type:String,
    required: true,
},
ply:{
    type:String,
    required: true,
},
lotNo:{
    type:String,
    required: true,
},
weight:{
    type:String,
    required: true,
},
rate:{
    type:String,
    required: true,
},
amount:{
type:String,
    required: true,
},
deliveryDate:{
    type:String,
    required: true,
}
})

const purchaseDetails = mongoose.model('Purchase', purchaseSchema);

module.exports = purchaseDetails;
