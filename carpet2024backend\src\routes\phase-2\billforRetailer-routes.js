const express = require('express');
const BillForRetailerController = require('../../controller/billforretailer-controller');

const router = express.Router();
const billForRetailerController = new BillForRetailerController();

router.post('/billforretailer', billForRetailerController.createBill.bind(billForRetailerController));
router.get('/billforretailer', billForRetailerController.getAllBills.bind(billForRetailerController));
router.get('/billforretailer/:id', billForRetailerController.getBillById.bind(billForRetailerController));
router.put('/billforretailer/:id', billForRetailerController.updateBill.bind(billForRetailerController));
router.delete('/billforretailer/:id', billForRetailerController.deleteBill.bind(billForRetailerController));

module.exports = router;
