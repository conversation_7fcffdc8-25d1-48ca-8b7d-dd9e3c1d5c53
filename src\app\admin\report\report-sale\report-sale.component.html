<div class="container mt-5">
  <div class="row text-center">
    <div class="col-md-12">
      <h4>{{ _isChallan ? "Challan Details" : "Bill Details" }}</h4>
    </div>
  </div>
  <section>
    <div class="row text-center" *ngIf="!_isChallan">
      <div class="col-md-2">Bill No - {{ _bill.billNo }}</div>
      <div class="col-md-2">Date -{{ _bill.date }}</div>
      <div class="col-md-3">Wholeseller - {{ _bill.customer }}</div>
    </div>
    <div class="row text-center" *ngIf="_isChallan">
      <div class="col-md-2">Challan No - {{ _bill.challanNo }}</div>
      <div class="col-md-2">Date -{{ _bill.chooseAdate }}</div>
      <div class="col-md-4">Wholeseller - {{ _bill.wholeseller }}</div>
      <div class="col-md-4">Retailer - {{ _bill.RetailerName }}</div>
    </div>
    <fieldset class="mt-5">
      <legend>
        <b>{{ _isChallan ? "Carpet List" : "Challan Details" }}</b>
      </legend>

      <div class="row mt-2">
        <div class="table-container" style="height: 372px; overflow: auto" #scrollContainer>
          <table class="table ">
            <thead>
              <tr class="tt">
                <th>Bar Code</th>
                <th>Carpet Details</th>
                <th>Area</th>
                <!-- <th>Evk Price</th> -->
                <th>Amount</th>
                <!-- <th>Action</th> -->
              </tr>
            </thead>

            <tr *ngFor="let item of billDetails; let i = index">
              <td>{{ item.carpetNo }}</td>
              <td>
                <span
                  style="
                    color: black;
                    font-weight: bold;
                    text-decoration: underline;
                  "
                  *ngIf="item.challanNo"
                  >RG: {{ item.challanNo }} vom {{ item.challanDate }} an
                  {{ item.customer }} <br />
                </span>

                <!-- RG:&nbsp;{{item.challanNo}} &nbsp;vom&nbsp; {{item.challanDate}} an {{item.customer}} <br> -->
                {{ item.qualityDesign }} <br />
                {{ item.qualityCode }}, {{ item.colourCode }} {{ item.colour }}
                <br />
                {{ item.size }} cm
              </td>
              <td>{{ item.area | number : "1.2-2" }} QM</td>
              <!-- <td class="text-center">
                {{ item.evkPrice | number : "1.2-2" }}
              </td> -->
              <td class="text-center">
                {{ item.amount | number : "1.2-2" }}
              </td>
             <!--  <td>
                <a (click)="removeChallan(item.challanNo)"><i class="fa fa-times" aria-hidden="true"></i></a>

              <a *ngIf="isUpdate" (click)="deleteBill(item.challanNo)"><i class="fa fa-trash-o fa-lg fa-trash"
                  aria-hidden="true"></i></a>

              </td> -->
            </tr>
            <tfoot>
            <tr >

              <td colspan="2" class="text-end">Total</td>
              <td class="text-center">
                {{ this.totalCalculation.totalArea | number : "1.2-2" }}
              </td>
              <!-- <td class="text-center">
                {{ this.totalCalculation.totalEvkPrice | number : "1.2-2" }}
              </td> -->
              <td class="text-center">
                {{ this.totalCalculation.totalAmount | number : "1.2-2" }}
              </td>

            </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </fieldset>
  </section>
</div>
