<div class="modal-header">
  <h4 class="modal-title">{{ materialName }} Material Receive Details</h4>
  <button type="button" class="btn-close" (click)="onCancel()">
    <mat-icon>close</mat-icon>
  </button>
</div>

<div class="modal-body">
  <form [formGroup]="katiReceiveForm">
    <div class="table-container">
      <table mat-table [dataSource]="materialData" class="mat-elevation-2 custom-table">
        
        <!-- Sr.No Column -->
        <ng-container matColumnDef="srNo">
          <th mat-header-cell *matHeaderCellDef class="text-center">Sr.No</th>
          <td mat-cell *matCellDef="let element; let i = index" class="text-center">
            {{ element.srNo }}
          </td>
        </ng-container>

        <!-- Colour Column -->
        <ng-container matColumnDef="colour">
          <th mat-header-cell *matHeaderCellDef class="text-center">Colour</th>
          <td mat-cell *matCellDef="let element" class="text-center">
            {{ element.colour }}
          </td>
        </ng-container>

        <!-- Lagat Column -->
        <ng-container matColumnDef="lagat">
          <th mat-header-cell *matHeaderCellDef class="text-center">Lagat</th>
          <td mat-cell *matCellDef="let element" class="text-center">
            {{ element.lagat | number:'1.3-3' }}
          </td>
        </ng-container>

        <!-- Carpet Lagat Column -->
        <ng-container matColumnDef="carpetLagat">
          <th mat-header-cell *matHeaderCellDef class="text-center">Carpet Lagat</th>
          <td mat-cell *matCellDef="let element" class="text-center">
            {{ element.carpetLagat | number:'1.3-3' }}
          </td>
        </ng-container>

        <!-- T. Issued Column -->
        <ng-container matColumnDef="tIssued">
          <th mat-header-cell *matHeaderCellDef class="text-center">T. Issued</th>
          <td mat-cell *matCellDef="let element" class="text-center">
            {{ element.tIssued | number:'1.3-3' }}
          </td>
        </ng-container>

        <!-- Receive Column -->
        <ng-container matColumnDef="receive">
          <th mat-header-cell *matHeaderCellDef class="text-center">Receive</th>
          <td mat-cell *matCellDef="let element; let i = index" class="text-center">
            <div formArrayName="katiItems">
              <div [formGroupName]="i">
                <mat-form-field appearance="outline" class="receive-input">
                  <input 
                    matInput 
                    type="text" 
                    formControlName="receive"
                    placeholder="0.000"
                    (blur)="setDigitReceive($event)"
                    (input)="onReceiveChange(i, $event)"
                  >
                </mat-form-field>
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Total Lagat Column (Previously Remaining) -->
        <ng-container matColumnDef="totalLagat">
          <th mat-header-cell *matHeaderCellDef class="text-center">Total Lagat</th>
          <td mat-cell *matCellDef="let element" class="text-center">
            {{ element.totalLagat | number:'1.3-3' }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <!-- Total Receive Display -->
    <div class="total-section" *ngIf="materialData.length > 0">
      <div class="total-row">
        <strong>Total Receive: {{ totalReceive | number:'1.3-3' }}</strong>
      </div>
    </div>
  </form>
</div>

<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="onCancel()">Cancel</button>
  <button type="button" class="btn btn-primary" (click)="onSave()">Save</button>
</div>
