!function(t,i){"object"==typeof exports?module.exports=i():"function"==typeof define&&define.amd?define(i):t.Spinner=i()}(this,function(){"use strict";var t,i=["webkit","Moz","ms","O"],e={};function o(t,i){var e,o=document.createElement(t||"div");for(e in i)o[e]=i[e];return o}function n(t){for(var i=1,e=arguments.length;i<e;i++)t.appendChild(arguments[i]);return t}var r,s=(r=o("style",{type:"text/css"}),n(document.getElementsByTagName("head")[0],r),r.sheet||r.styleSheet);function a(t,e){var o,n,r=t.style;for(e=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<i.length;n++)if(void 0!==r[o=i[n]+e])return o;if(void 0!==r[e])return e}function l(t,i){for(var e in i)t.style[a(t,e)||e]=i[e];return t}function d(t){for(var i=1;i<arguments.length;i++){var e=arguments[i];for(var o in e)void 0===t[o]&&(t[o]=e[o])}return t}function c(t,i){return"string"==typeof t?t:t[i%t.length]}var p={lines:12,length:7,width:5,radius:10,rotate:0,corners:1,color:"#000",direction:1,speed:1,trail:100,opacity:.25,fps:20,zIndex:2e9,className:"spinner",top:"50%",left:"50%",position:"absolute"};function u(t){this.opts=d(t||{},u.defaults,p)}u.defaults={},d(u.prototype,{spin:function(i){this.stop();var e=this,n=e.opts,r=e.el=l(o(0,{className:n.className}),{position:n.position,width:0,zIndex:n.zIndex});n.radius,n.length,n.width;if(l(r,{left:n.left,top:n.top}),i&&i.insertBefore(r,i.firstChild||null),r.setAttribute("role","progressbar"),e.lines(r,e.opts),!t){var s,a=0,d=(n.lines-1)*(1-n.direction)/2,c=n.fps,p=c/n.speed,u=(1-n.opacity)/(p*n.trail/100),f=p/n.lines;!function t(){a++;for(var i=0;i<n.lines;i++)s=Math.max(1-(a+(n.lines-i)*f)%p*u,n.opacity),e.opacity(r,i*n.direction+d,s,n);e.timeout=e.el&&setTimeout(t,~~(1e3/c))}()}return e},stop:function(){var t=this.el;return t&&(clearTimeout(this.timeout),t.parentNode&&t.parentNode.removeChild(t),this.el=void 0),this},lines:function(i,r){var a,d,p,u,f,h,m,v,y,g,w=0,x=(r.lines-1)*(1-r.direction)/2;function b(t,i){return l(o(),{position:"absolute",width:r.length+r.width+"px",height:r.width+"px",background:t,boxShadow:i,transformOrigin:"left",transform:"rotate("+~~(360/r.lines*w+r.rotate)+"deg) translate("+r.radius+"px,0)",borderRadius:(r.corners*r.width>>1)+"px"})}for(;w<r.lines;w++)a=l(o(),{position:"absolute",top:1+~(r.width/2)+"px",transform:r.hwaccel?"translate3d(0,0,0)":"",opacity:r.opacity,animation:t&&(d=r.opacity,p=r.trail,u=x+w*r.direction,f=r.lines,void 0,void 0,void 0,void 0,void 0,h=["opacity",p,~~(100*d),u,f].join("-"),m=.01+u/f*100,v=Math.max(1-(1-d)/p*(100-m),d),y=t.substring(0,t.indexOf("Animation")).toLowerCase(),g=y&&"-"+y+"-"||"",e[h]||(s.insertRule("@"+g+"keyframes "+h+"{0%{opacity:"+v+"}"+m+"%{opacity:"+d+"}"+(m+.01)+"%{opacity:1}"+(m+p)%100+"%{opacity:"+d+"}100%{opacity:"+v+"}}",s.cssRules.length),e[h]=1),h+" "+1/r.speed+"s linear infinite")}),r.shadow&&n(a,l(b("#000","0 0 4px #000"),{top:"2px"})),n(i,n(a,b(c(r.color,w),"0 0 1px rgba(0,0,0,.1)")));return i},opacity:function(t,i,e){i<t.childNodes.length&&(t.childNodes[i].style.opacity=e)}});var f=l(o("group"),{behavior:"url(#default#VML)"});return!a(f,"transform")&&f.adj?function(){function t(t,i){return o("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="spin-vml">',i)}s.addRule(".spin-vml","behavior:url(#default#VML)"),u.prototype.lines=function(i,e){var o=e.length+e.width,r=2*o;function s(){return l(t("group",{coordsize:r+" "+r,coordorigin:-o+" "+-o}),{width:r,height:r})}var a,d=2*-(e.width+e.length)+"px",p=l(s(),{position:"absolute",top:d,left:d});function u(i,r,a){n(p,n(l(s(),{rotation:360/e.lines*i+"deg",left:~~r}),n(l(t("roundrect",{arcsize:e.corners}),{width:o,height:e.width,left:e.radius,top:-e.width>>1,filter:a}),t("fill",{color:c(e.color,i),opacity:e.opacity}),t("stroke",{opacity:0}))))}if(e.shadow)for(a=1;a<=e.lines;a++)u(a,-2,"progid:DXImageTransform.Microsoft.Blur(pixelradius=2,makeshadow=1,shadowopacity=.3)");for(a=1;a<=e.lines;a++)u(a);return n(i,p)},u.prototype.opacity=function(t,i,e,o){var n=t.firstChild;o=o.shadow&&o.lines||0,n&&i+o<n.childNodes.length&&(n=(n=(n=n.childNodes[i+o])&&n.firstChild)&&n.firstChild)&&(n.opacity=e)}}():t=a(f,"animation"),u});