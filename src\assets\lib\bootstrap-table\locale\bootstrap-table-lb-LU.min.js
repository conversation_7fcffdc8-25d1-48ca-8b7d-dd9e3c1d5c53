/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var e=n(t),r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof r&&r)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},b={}.propertyIsEnumerable,y=Object.getOwnPropertyDescriptor,h=y&&!b.call({1:2},1);g.f=h?function(t){var n=y(this,t);return!!n&&n.enumerable}:b;var m,v,d=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),x=T("".slice),E=function(t){return x(A(t),8,-1)},C=E,F=P,M=function(t){if("Function"===C(t))return F(t)},D=c,R=E,I=Object,L=M("".split),k=D((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"==R(t)?L(t,""):I(t)}:I,N=function(t){return null==t},Z=N,_=TypeError,z=function(t){if(Z(t))throw _("Can't call method on "+t);return t},G=k,U=z,W=function(t){return G(U(t))},K="object"==typeof document&&document.all,q={all:K,IS_HTMLDDA:void 0===K&&void 0!==K},B=q.all,H=q.IS_HTMLDDA?function(t){return"function"==typeof t||t===B}:function(t){return"function"==typeof t},V=H,J=q.all,Q=q.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:V(t)||t===J}:function(t){return"object"==typeof t?null!==t:V(t)},X=i,Y=H,$=function(t){return Y(t)?t:void 0},tt=function(t,n){return arguments.length<2?$(X[t]):X[t]&&X[t][n]},nt=M({}.isPrototypeOf),et=i,rt=tt("navigator","userAgent")||"",ot=et.process,it=et.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(v=(m=ct.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!v&&rt&&(!(m=rt.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=rt.match(/Chrome\/(\d+)/))&&(v=+m[1]);var at=v,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,bt=H,yt=nt,ht=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return bt(n)&&yt(n.prototype,ht(t))},vt=String,dt=H,St=function(t){try{return vt(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(dt(t))return t;throw wt(St(t)+" is not a function")},jt=N,Pt=p,Tt=H,At=Q,xt=TypeError,Et={exports:{}},Ct=i,Ft=Object.defineProperty,Mt=function(t,n){try{Ft(Ct,t,{value:n,configurable:!0,writable:!0})}catch(e){Ct[t]=n}return n},Dt=Mt,Rt="__core-js_shared__",It=i[Rt]||Dt(Rt,{}),Lt=It;(Et.exports=function(t,n){return Lt[t]||(Lt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var kt=z,Nt=Object,Zt=function(t){return Nt(kt(t))},_t=Zt,zt=M({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return zt(_t(t),n)},Ut=M,Wt=0,Kt=Math.random(),qt=Ut(1..toString),Bt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+qt(++Wt+Kt,36)},Ht=i,Vt=Et.exports,Jt=Gt,Qt=Bt,Xt=st,Yt=pt,$t=Vt("wks"),tn=Ht.Symbol,nn=tn&&tn.for,en=Yt?tn:tn&&tn.withoutSetter||Qt,rn=function(t){if(!Jt($t,t)||!Xt&&"string"!=typeof $t[t]){var n="Symbol."+t;Xt&&Jt(tn,t)?$t[t]=tn[t]:$t[t]=Yt&&nn?nn(n):en(n)}return $t[t]},on=p,un=Q,cn=mt,an=function(t,n){var e=t[n];return jt(e)?void 0:Ot(e)},fn=function(t,n){var e,r;if("string"===n&&Tt(e=t.toString)&&!At(r=Pt(e,t)))return r;if(Tt(e=t.valueOf)&&!At(r=Pt(e,t)))return r;if("string"!==n&&Tt(e=t.toString)&&!At(r=Pt(e,t)))return r;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=rn("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var e,r=an(t,sn);if(r){if(void 0===n&&(n="default"),e=on(r,t,n),!un(e)||cn(e))return e;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=mt,bn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},yn=Q,hn=i.document,mn=yn(hn)&&yn(hn.createElement),vn=function(t){return mn?hn.createElement(t):{}},dn=!a&&!c((function(){return 7!=Object.defineProperty(vn("div"),"a",{get:function(){return 7}}).a})),Sn=a,wn=p,On=g,jn=d,Pn=W,Tn=bn,An=Gt,xn=dn,En=Object.getOwnPropertyDescriptor;u.f=Sn?En:function(t,n){if(t=Pn(t),n=Tn(n),xn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var Cn={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mn=Q,Dn=String,Rn=TypeError,In=function(t){if(Mn(t))return t;throw Rn(Dn(t)+" is not an object")},Ln=a,kn=dn,Nn=Fn,Zn=In,_n=bn,zn=TypeError,Gn=Object.defineProperty,Un=Object.getOwnPropertyDescriptor,Wn="enumerable",Kn="configurable",qn="writable";Cn.f=Ln?Nn?function(t,n,e){if(Zn(t),n=_n(n),Zn(e),"function"==typeof t&&"prototype"===n&&"value"in e&&qn in e&&!e.writable){var r=Un(t,n);r&&r.writable&&(t[n]=e.value,e={configurable:Kn in e?e.configurable:r.configurable,enumerable:Wn in e?e.enumerable:r.enumerable,writable:!1})}return Gn(t,n,e)}:Gn:function(t,n,e){if(Zn(t),n=_n(n),Zn(e),kn)try{return Gn(t,n,e)}catch(t){}if("get"in e||"set"in e)throw zn("Accessors not supported");return"value"in e&&(t[n]=e.value),t};var Bn=Cn,Hn=d,Vn=a?function(t,n,e){return Bn.f(t,n,Hn(1,e))}:function(t,n,e){return t[n]=e,t},Jn={exports:{}},Qn=a,Xn=Gt,Yn=Function.prototype,$n=Qn&&Object.getOwnPropertyDescriptor,te=Xn(Yn,"name"),ne={EXISTS:te,PROPER:te&&"something"===function(){}.name,CONFIGURABLE:te&&(!Qn||Qn&&$n(Yn,"name").configurable)},ee=H,re=It,oe=M(Function.toString);ee(re.inspectSource)||(re.inspectSource=function(t){return oe(t)});var ie,ue,ce,ae=re.inspectSource,fe=H,le=i.WeakMap,se=fe(le)&&/native code/.test(String(le)),pe=Et.exports,ge=Bt,be=pe("keys"),ye={},he=se,me=i,ve=Q,de=Vn,Se=Gt,we=It,Oe=function(t){return be[t]||(be[t]=ge(t))},je=ye,Pe="Object already initialized",Te=me.TypeError,Ae=me.WeakMap;if(he||we.state){var xe=we.state||(we.state=new Ae);xe.get=xe.get,xe.has=xe.has,xe.set=xe.set,ie=function(t,n){if(xe.has(t))throw Te(Pe);return n.facade=t,xe.set(t,n),n},ue=function(t){return xe.get(t)||{}},ce=function(t){return xe.has(t)}}else{var Ee=Oe("state");je[Ee]=!0,ie=function(t,n){if(Se(t,Ee))throw Te(Pe);return n.facade=t,de(t,Ee,n),n},ue=function(t){return Se(t,Ee)?t[Ee]:{}},ce=function(t){return Se(t,Ee)}}var Ce={set:ie,get:ue,has:ce,enforce:function(t){return ce(t)?ue(t):ie(t,{})},getterFor:function(t){return function(n){var e;if(!ve(n)||(e=ue(n)).type!==t)throw Te("Incompatible receiver, "+t+" required");return e}}},Fe=c,Me=H,De=Gt,Re=a,Ie=ne.CONFIGURABLE,Le=ae,ke=Ce.enforce,Ne=Ce.get,Ze=Object.defineProperty,_e=Re&&!Fe((function(){return 8!==Ze((function(){}),"length",{value:8}).length})),ze=String(String).split("String"),Ge=Jn.exports=function(t,n,e){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),e&&e.getter&&(n="get "+n),e&&e.setter&&(n="set "+n),(!De(t,"name")||Ie&&t.name!==n)&&(Re?Ze(t,"name",{value:n,configurable:!0}):t.name=n),_e&&e&&De(e,"arity")&&t.length!==e.arity&&Ze(t,"length",{value:e.arity});try{e&&De(e,"constructor")&&e.constructor?Re&&Ze(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=ke(t);return De(r,"source")||(r.source=ze.join("string"==typeof n?n:"")),t};Function.prototype.toString=Ge((function(){return Me(this)&&Ne(this).source||Le(this)}),"toString");var Ue=H,We=Cn,Ke=Jn.exports,qe=Mt,Be={},He=Math.ceil,Ve=Math.floor,Je=Math.trunc||function(t){var n=+t;return(n>0?Ve:He)(n)},Qe=function(t){var n=+t;return n!=n||0===n?0:Je(n)},Xe=Qe,Ye=Math.max,$e=Math.min,tr=Qe,nr=Math.min,er=function(t){return t>0?nr(tr(t),9007199254740991):0},rr=function(t){return er(t.length)},or=W,ir=function(t,n){var e=Xe(t);return e<0?Ye(e+n,0):$e(e,n)},ur=rr,cr=function(t){return function(n,e,r){var o,i=or(n),u=ur(i),c=ir(r,u);if(t&&e!=e){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===e)return t||c||0;return!t&&-1}},ar={includes:cr(!0),indexOf:cr(!1)},fr=Gt,lr=W,sr=ar.indexOf,pr=ye,gr=M([].push),br=function(t,n){var e,r=lr(t),o=0,i=[];for(e in r)!fr(pr,e)&&fr(r,e)&&gr(i,e);for(;n.length>o;)fr(r,e=n[o++])&&(~sr(i,e)||gr(i,e));return i},yr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Be.f=Object.getOwnPropertyNames||function(t){return br(t,yr)};var hr={};hr.f=Object.getOwnPropertySymbols;var mr=tt,vr=Be,dr=hr,Sr=In,wr=M([].concat),Or=mr("Reflect","ownKeys")||function(t){var n=vr.f(Sr(t)),e=dr.f;return e?wr(n,e(t)):n},jr=Gt,Pr=Or,Tr=u,Ar=Cn,xr=c,Er=H,Cr=/#|\.prototype\./,Fr=function(t,n){var e=Dr[Mr(t)];return e==Ir||e!=Rr&&(Er(n)?xr(n):!!n)},Mr=Fr.normalize=function(t){return String(t).replace(Cr,".").toLowerCase()},Dr=Fr.data={},Rr=Fr.NATIVE="N",Ir=Fr.POLYFILL="P",Lr=Fr,kr=i,Nr=u.f,Zr=Vn,_r=function(t,n,e,r){r||(r={});var o=r.enumerable,i=void 0!==r.name?r.name:n;if(Ue(e)&&Ke(e,i,r),r.global)o?t[n]=e:qe(n,e);else{try{r.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=e:We.f(t,n,{value:e,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return t},zr=Mt,Gr=function(t,n,e){for(var r=Pr(n),o=Ar.f,i=Tr.f,u=0;u<r.length;u++){var c=r[u];jr(t,c)||e&&jr(e,c)||o(t,c,i(n,c))}},Ur=Lr,Wr=E,Kr=Array.isArray||function(t){return"Array"==Wr(t)},qr=TypeError,Br=bn,Hr=Cn,Vr=d,Jr={};Jr[rn("toStringTag")]="z";var Qr="[object z]"===String(Jr),Xr=H,Yr=E,$r=rn("toStringTag"),to=Object,no="Arguments"==Yr(function(){return arguments}()),eo=M,ro=c,oo=H,io=Qr?Yr:function(t){var n,e,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,n){try{return t[n]}catch(t){}}(n=to(t),$r))?e:no?Yr(n):"Object"==(r=Yr(n))&&Xr(n.callee)?"Arguments":r},uo=ae,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=eo(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},bo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};bo.sham=!0;var yo=!fo||ro((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?bo:go,ho=Kr,mo=yo,vo=Q,So=rn("species"),wo=Array,Oo=function(t){var n;return ho(t)&&(n=t.constructor,(mo(n)&&(n===wo||ho(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=c,Po=at,To=rn("species"),Ao=function(t,n){var e,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(e=a?kr:f?kr[c]||zr(c,{}):(kr[c]||{}).prototype)for(r in n){if(i=n[r],o=t.dontCallGetSet?(u=Nr(e,r))&&u.value:e[r],!Ur(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Gr(i,o)}(t.sham||o&&o.sham)&&Zr(i,"sham",!0),_r(e,r,i,t)}},xo=c,Eo=Kr,Co=Q,Fo=Zt,Mo=rr,Do=function(t){if(t>9007199254740991)throw qr("Maximum allowed index exceeded");return t},Ro=function(t,n,e){var r=Br(n);r in t?Hr.f(t,r,Vr(0,e)):t[r]=e},Io=function(t,n){return new(Oo(t))(0===n?0:n)},Lo=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},ko=at,No=rn("isConcatSpreadable"),Zo=ko>=51||!xo((function(){var t=[];return t[No]=!1,t.concat()[0]!==t})),_o=Lo("concat"),zo=function(t){if(!Co(t))return!1;var n=t[No];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!Zo||!_o},{concat:function(t){var n,e,r,o,i,u=Fo(this),c=Io(u,0),a=0;for(n=-1,r=arguments.length;n<r;n++)if(zo(i=-1===n?u:arguments[n]))for(o=Mo(i),Do(a+o),e=0;e<o;e++,a++)e in i&&Ro(c,a,i[e]);else Do(a+1),Ro(c,a++,i);return c.length=a,c}}),e.default.fn.bootstrapTable.locales["lb-LU"]=e.default.fn.bootstrapTable.locales.lb={formatCopyRows:function(){return"Zeilen kopéieren"},formatPrint:function(){return"Drécken"},formatLoadingMessage:function(){return"Gëtt gelueden, gedellëgt Iech wannechgelift ee Moment"},formatRecordsPerPage:function(t){return"".concat(t," Zeilen per Säit")},formatShowingRows:function(t,n,e,r){return void 0!==r&&r>0&&r>e?"Weist Zeil ".concat(t," bis ").concat(n," vun ").concat(e," Zeil").concat(e>1?"en":""," (gefiltert vun insgesamt ").concat(r," Zeil").concat(e>1?"en":"",")"):"Weist Zeil ".concat(t," bis ").concat(n," vun ").concat(e," Zeil").concat(e>1?"en":"")},formatSRPaginationPreText:function(){return"viregt Säit"},formatSRPaginationPageText:function(t){return"op Säit ".concat(t)},formatSRPaginationNextText:function(){return"nächst Säit"},formatDetailPagination:function(t){return"Weist ".concat(t," Zeilen")},formatClearSearch:function(){return"Sich réckgängeg maachen"},formatSearch:function(){return"Sich"},formatNoMatches:function(){return"Keng passend Anträg fonnt"},formatPaginationSwitch:function(){return"Paginatioun uweisen/verstoppen"},formatPaginationSwitchDown:function(){return"Paginatioun uweisen"},formatPaginationSwitchUp:function(){return"Paginatioun verstoppen"},formatRefresh:function(){return"Nei lueden"},formatToggleOn:function(){return"Kaartenusiicht uweisen"},formatToggleOff:function(){return"Kaartenusiicht verstoppen"},formatColumns:function(){return"Kolonnen"},formatColumnsToggleAll:function(){return"All ëmschalten"},formatFullscreen:function(){return"Vollbild"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Automatescht neilueden"},formatExport:function(){return"Daten exportéieren"},formatJumpTo:function(){return"Sprangen"},formatAdvancedSearch:function(){return"Erweidert Sich"},formatAdvancedCloseButton:function(){return"Zoumaachen"},formatFilterControlSwitch:function(){return"Schaltelementer uweisen/verstoppen"},formatFilterControlSwitchHide:function(){return"Schaltelementer verstoppen"},formatFilterControlSwitchShow:function(){return"Schaltelementer uweisen"}},e.default.extend(e.default.fn.bootstrapTable.defaults,e.default.fn.bootstrapTable.locales["lb-LU"])}));
