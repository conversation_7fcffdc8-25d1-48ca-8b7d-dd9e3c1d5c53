import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { Router } from '@angular/router';
import { ManufactureService } from '../../../../services/manufacture.service';
import Swal from 'sweetalert2';

interface MaterialIssueData {
  _id: string;
  challanNo: string;
  date: string;
  weaver: any;
  issueNo: any;
  materials: {
    kati: { description: any; lagat: string; issue: string; };
    tana: { description: any; lagat: string; issue: string; };
    soot: { description: any; lagat: string; issue: string; };
    thari: { description: any; lagat: string; issue: string; };
    silk: { description: any; lagat: string; issue: string; };
  };
  katiData: any[];
}

interface TableRow {
  srNo: number;
  weaver: string;
  issueNo: string;
  challanNo: string;
  date: string;
  quality: string;
  design: string;
  colour: string;
  size: string;
  pcs: string;
  area: string;
  kati: string;
  tana: string;
  soot: string;
  thari: string;
  silk: string;
  _id: string;
}

@Component({
  selector: 'app-view-weaver-material',
  templateUrl: './view-weaver-material.component.html',
  styleUrl: './view-weaver-material.component.css'
})
export class ViewWeaverMaterialComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  filterForm!: FormGroup;
  dataSource = new MatTableDataSource<TableRow>([]);
  displayedColumns: string[] = [
    'srNo', 'weaver', 'issueNo', 'challanNo', 'date',
    'quality', 'design', 'colour', 'size', 'pcs', 'area',
    'kati', 'tana', 'soot', 'thari', 'silk', 'action'
  ];

  materialIssueList: MaterialIssueData[] = [];
  weaverList: any[] = [];
  issueList: any[] = [];
  filteredData: TableRow[] = [];

  constructor(
    private fb: FormBuilder,
    private manufactureService: ManufactureService,
    private router: Router
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadData();
    this.setupDataSource();
  }

  initializeForm(): void {
    this.filterForm = this.fb.group({
      weaver: [''],
      issueNo: [''],
      fromDate: [''],
      toDate: ['']
    });

    // Subscribe to form changes for real-time filtering
    this.filterForm.valueChanges.subscribe(() => {
      this.applyFilters();
    });
  }

  setupDataSource(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadData(): void {
    console.log('🔄 Loading Material Issue data...');

    this.manufactureService.getAllMaterialIssues().subscribe({
      next: (data: any) => {
        console.log('✅ Material Issues loaded:', data);
        console.log('🔍 First item structure:', data[0]);
        if (data[0] && data[0].materials) {
          console.log('🔍 Materials structure:', data[0].materials);
        }
        this.materialIssueList = data;
        this.extractFiltersData();
        this.processTableData();
      },
      error: (error) => {
        console.error('❌ Error loading Material Issues:', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load material issues data.',
          icon: 'error',
          confirmButtonText: 'OK'
        });
      }
    });
  }

  extractFiltersData(): void {
    // Extract unique weavers
    const weavers = new Set();
    const issues = new Set();

    this.materialIssueList.forEach(item => {
      if (item.weaver) {
        if (typeof item.weaver === 'object' && item.weaver.name) {
          weavers.add(item.weaver.name);
        } else if (typeof item.weaver === 'string') {
          weavers.add(item.weaver);
        }
      }

      if (item.issueNo) {
        if (typeof item.issueNo === 'object' && item.issueNo.Br_issueNo) {
          issues.add(item.issueNo.Br_issueNo);
        } else if (typeof item.issueNo === 'string') {
          issues.add(item.issueNo);
        }
      }
    });

    this.weaverList = Array.from(weavers).map(name => ({ name }));
    this.issueList = Array.from(issues).map(issueNo => ({ Br_issueNo: issueNo }));

    console.log('✅ Extracted filters:', {
      weavers: this.weaverList,
      issues: this.issueList
    });
  }

  processTableData(): void {
    this.filteredData = [];
    let srNo = 1;

    // Sort materialIssueList by challanNo in descending order (2400003, 2400002, 2400001...)
    const sortedMaterialIssues = [...this.materialIssueList].sort((a, b) => {
      const extractNumber = (challanNo: string) => {
        if (!challanNo) return 0;
        // Extract number after dash (MI-2400001 -> 2400001)
        if (challanNo.includes('-')) {
          const parts = challanNo.split('-');
          const num = parseInt(parts[1]);
          return isNaN(num) ? 0 : num;
        }
        // Fallback: extract all digits
        const num = parseInt(challanNo.replace(/\D/g, ''));
        return isNaN(num) ? 0 : num;
      };

      const numA = extractNumber(a.challanNo || '');
      const numB = extractNumber(b.challanNo || '');

      // Sort in descending order (latest/highest number first)
      return numB - numA;
    });

    console.log('🔍 Sorted challan numbers:', sortedMaterialIssues.map(item => item.challanNo));

    sortedMaterialIssues.forEach(materialIssue => {
      // Get basic info
      const weaverName = this.getWeaverName(materialIssue.weaver);
      const issueNumber = this.getIssueNumber(materialIssue.issueNo);
      const formattedDate = this.formatDate(materialIssue.date);

      // Get material values
      const materials = materialIssue.materials || {};
      console.log('🔍 Material data for issue:', materialIssue.challanNo, materials);

      const row: TableRow = {
        srNo: srNo++,
        weaver: weaverName,
        issueNo: issueNumber,
        challanNo: materialIssue.challanNo,
        date: formattedDate,
        quality: this.getPopulatedValue(materialIssue.issueNo?.quality, 'quality'),
        design: this.getPopulatedValue(materialIssue.issueNo?.design, 'design'),
        colour: this.getPopulatedValue(materialIssue.issueNo, 'borderColour'),
        size: this.getPopulatedValue(materialIssue.issueNo?.size, 'sizeInYard'),
        pcs: this.getPopulatedValue(materialIssue.issueNo, 'pcs'),
        area: this.getPopulatedValue(materialIssue.issueNo, 'area'),
        kati: this.getMaterialValue(materials.kati),
        tana: this.getMaterialValue(materials.tana),
        soot: this.getMaterialValue(materials.soot),
        thari: this.getMaterialValue(materials.thari),
        silk: this.getMaterialValue(materials.silk),
        _id: materialIssue._id
      };

      this.filteredData.push(row);
    });

    this.dataSource.data = this.filteredData;
    console.log('✅ Processed table data:', this.filteredData);
  }

  getWeaverName(weaver: any): string {
    if (typeof weaver === 'object' && weaver?.name) {
      // Add branch code prefix like "K - Shabana"
      const branchCode = weaver.branchCode || 'K'; // Default to K if no branch code
      return `${branchCode} - ${weaver.name}`;
    } else if (typeof weaver === 'string') {
      // If it's already a string, check if it has branch code
      if (weaver.includes(' - ')) {
        return weaver; // Already formatted
      }
      return `K - ${weaver}`; // Add default branch code
    }
    return 'N/A';
  }

  getIssueNumber(issueNo: any): string {
    if (typeof issueNo === 'object' && issueNo?.Br_issueNo) {
      return issueNo.Br_issueNo;
    } else if (typeof issueNo === 'string') {
      return issueNo;
    }
    return 'N/A';
  }

  formatDate(date: any): string {
    if (!date) return 'N/A';
    try {
      const dateObj = new Date(date);
      return dateObj.toLocaleDateString('en-GB');
    } catch {
      return date.toString();
    }
  }

  getPopulatedValue(issueNo: any, field: string): string {
    if (typeof issueNo === 'object' && issueNo?.[field]) {
      const value = issueNo[field];
      if (typeof value === 'object' && value?.name) {
        return value.name;
      } else if (typeof value === 'string') {
        return value;
      }
    }
    return 'N/A';
  }

  getMaterialValue(material: any): string {
    console.log('🔍 Processing material:', material);

    if (!material) {
      console.log('⚠️ Material is null/undefined');
      return 'N/A';
    }

    const parts = [];

    // Check for lagat value
    if (material.lagat && material.lagat !== '0' && material.lagat !== '') {
      parts.push(`L: ${material.lagat}`);
    }

    // Check for issue value
    if (material.issue && material.issue !== '0' && material.issue !== '') {
      parts.push(`I: ${material.issue}`);
    }

    const result = parts.length > 0 ? parts.join(', ') : 'N/A';
    console.log('🔍 Material value result:', result);
    return result;
  }

  applyFilters(): void {
    const filters = this.filterForm.value;
    let filtered = [...this.filteredData];

    // Filter by weaver
    if (filters.weaver) {
      filtered = filtered.filter(item =>
        item.weaver.toLowerCase().includes(filters.weaver.toLowerCase())
      );
    }

    // Filter by issue number
    if (filters.issueNo) {
      filtered = filtered.filter(item =>
        item.issueNo.toLowerCase().includes(filters.issueNo.toLowerCase())
      );
    }

    // Filter by date range
    if (filters.fromDate) {
      const fromDate = new Date(filters.fromDate);
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.date.split('/').reverse().join('-'));
        return itemDate >= fromDate;
      });
    }

    if (filters.toDate) {
      const toDate = new Date(filters.toDate);
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.date.split('/').reverse().join('-'));
        return itemDate <= toDate;
      });
    }

    this.dataSource.data = filtered;
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.dataSource.data = this.filteredData;
  }

  editMaterialIssue(id: string): void {
    console.log('Edit material issue:', id);

    // Find the material issue data
    const materialIssue = this.materialIssueList.find(item => item._id === id);

    if (materialIssue) {
      console.log('🔍 Material issue data for edit:', materialIssue);

      // Navigate to material-issue component with edit data
      this.router.navigate(['/admin/material-issue'], {
        queryParams: {
          editId: id,
          editMode: 'true'
        },
        state: {
          editData: materialIssue
        }
      });
    } else {
      console.error('❌ Material issue not found for ID:', id);
      Swal.fire({
        title: 'Error!',
        text: 'Material issue data not found.',
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }
  }

  deleteMaterialIssue(id: string): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to delete this material issue?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        this.manufactureService.deleteMaterialIssue(id).subscribe({
          next: () => {
            Swal.fire('Deleted!', 'Material issue has been deleted.', 'success');
            this.loadData(); // Reload data
          },
          error: (error) => {
            console.error('Error deleting material issue:', error);
            Swal.fire('Error!', 'Failed to delete material issue.', 'error');
          }
        });
      }
    });
  }
}
