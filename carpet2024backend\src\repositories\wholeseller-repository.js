// repositories/wholeSellerRepository.js

const WholeSeller = require('../model/phase-2/wholeseller');

async function createWholeSeller(wholeSellerData) {
    return WholeSeller.create(wholeSellerData);
}

async function getWholeSellerById(wholeSellerId) {
    return WholeSeller.findById(wholeSellerId);
}

async function getAllWholeSellers() {
    return WholeSeller.find();
}

async function updateWholeSeller(wholeSellerId, wholeSellerData) {
    return WholeSeller.findByIdAndUpdate(wholeSellerId, wholeSellerData, { new: true });
}

async function deleteWholeSeller(wholeSellerId) {
    return WholeSeller.findByIdAndDelete(wholeSellerId);
}

module.exports = {
    createWholeSeller,
    getWholeSellerById,
    getAllWholeSellers,
    updateWholeSeller,
    deleteWholeSeller
};
