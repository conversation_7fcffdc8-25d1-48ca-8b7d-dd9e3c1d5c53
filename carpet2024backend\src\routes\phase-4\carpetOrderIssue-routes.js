const express = require('express');
const router = express.Router();
const carpetOrderissueController = require('../../controller/manifacturing/carpetOrder-controller');

router.post('/carpetOrderissues', carpetOrderissueController.createCarpetOrderissue);
router.get('/carpetOrderissues', carpetOrderissueController.getAllCarpetOrderissues);
router.get('/getpopulate', carpetOrderissueController.getPopulate);
router.get('/carpetOrderissues/initializePCSFields', carpetOrderissueController.initializePCSFields);
router.get('/carpetOrderissues/fixPCSFields', carpetOrderissueController.fixPCSFields);
router.get('/carpetOrderissues/fixBuyerOrderNaNValues', carpetOrderissueController.fixBuyerOrderNaNValues);
router.get('/carpetOrderissues/:id', carpetOrderissueController.getCarpetOrderissueById);
router.patch('/carpetOrderissues/:id', carpetOrderissueController.updateCarpetOrderissue);
router.patch('/carpetOrderissues/:id/updatePcs', carpetOrderissueController.updateCarpetOrderissueField);
router.patch('/carpetOrderissues/:issueId/updatePCSFields', carpetOrderissueController.updatePCSFields);
router.delete('/delete-carpetOrderissues/:id', carpetOrderissueController.deleteCarpetOrderissueField);
router.delete('/carpetOrderissues/:id', carpetOrderissueController.deleteCarpetOrderissue);

module.exports = router;
