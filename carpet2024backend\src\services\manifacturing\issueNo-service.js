const issueNoRepository = require('../../repositories/manifacturing/issueNo-repository');

class IssueNoService {
  async createIssueNo(issueNoData) {
    return await issueNoRepository.create(issueNoData);
  }

  async getAllIssueNos() {
    return await issueNoRepository.findAll();
  }

  async getIssueNoById(id) {
    return await issueNoRepository.findById(id);
  }

  async updateIssueNo(id, issueNoData) {
    return await issueNoRepository.update(id, issueNoData);
  }

  async deleteIssueNo(id) {
    return await issueNoRepository.delete(id);
  }
}

module.exports = new IssueNoService();
