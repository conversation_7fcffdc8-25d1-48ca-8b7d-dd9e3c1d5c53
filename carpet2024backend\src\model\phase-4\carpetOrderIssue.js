const mongoose = require("mongoose");

const carpetOrderissueSchema = new mongoose.Schema(
  {
    Br_issueNo: {
      type: String,
      required: true,
      // Removed unique constraint to allow multiple documents with the same Br_issueNo
    },
    branch:{type:mongoose.Schema.Types.ObjectId, ref:'branch', required:true},
    date: {
      type: String,
      required: true,
    },
    buyerOrder: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "BuyerOrder",
      required: true,
    },
    weaver: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "WeaverEmployee",
      required: true,
    },
    itemId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BuyerOrder.items',
    },
    areaIn: {
      type: String,
      enum: ["Sq.Feet", "Sq.Yard"],
      default: "Sq.Feet",
    },

    quality: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'AddQuality',
    },
    design: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'AddDesign',
    },
    borderColour: {
      type: String,
      required: true,
    },
    size: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SizeMaster',
    },
    khapSize: {
      type: String,
      required: true,
    },
    pcs: {
      type: String,
      required: true,
    },
    PCSWaitingToBeReceived: {
      type: Number,
      default: function() {
        return parseInt(this.pcs) || 0;
      }
    },
    PCSReceived: {
      type: Number,
      default: 0
    },
    area: {
      type: String,
      required: true,
    },
    rate: {
      type: String,
      required: true,
    },
    amount: {
      type: String,
      required: true
      // default:0,
    },

    MapOrderNo: {
      type: String,
      // required: true,
      // unique: true
    },


    uploadFile: {
      type: String,

    }
  }
);

module.exports = mongoose.model("CarpetOrderissue", carpetOrderissueSchema);
