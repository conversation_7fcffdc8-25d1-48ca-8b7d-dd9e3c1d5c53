import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, FormArray } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-quality-modal',
  templateUrl: './quality-modal.component.html',
  styleUrls: ['./quality-modal.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTableModule,
    MatCardModule,
    MatIconModule
  ]
})
export class QualityModalComponent implements OnInit {
  qualityForm: FormGroup;
  oldQualityData: any[] = [];

  constructor(
    public dialogRef: MatDialogRef<QualityModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder
  ) {
    this.qualityForm = this.fb.group({
      newQualities: this.fb.array([])
    });
  }

  ngOnInit() {
    this.loadOldQualityData();
    this.initializeNewQualityInputs();
  }

  get newQualities() {
    return this.qualityForm.get('newQualities') as FormArray;
  }

  loadOldQualityData() {
    // Load old quality data from packing list
    if (this.data.currentPackingData && this.data.currentPackingData.length > 0) {
      this.oldQualityData = this.data.currentPackingData.flatMap((packingList: any) =>
        packingList.items?.map((item: any) => ({
          quality: item.quality || '',
          design: item.design || '',
          color: item.color || '',
          size: item.size || '',
          pieces: item.pcs || 0
        })) || []
      );
    }

    // If no packing data, use current goods data
    if (this.oldQualityData.length === 0 && this.data.currentGoods) {
      this.oldQualityData = this.data.currentGoods.map((good: any) => ({
        quality: good.quality || '',
        design: good.design || '',
        color: '',
        size: '',
        pieces: good.pieces || 0
      }));
    }
  }

  initializeNewQualityInputs() {
    // Create input fields for new qualities and designs based on old data
    this.oldQualityData.forEach(() => {
      this.newQualities.push(this.fb.group({
        quality: [''],
        design: ['']
      }));
    });
  }

  addNewQualityRow() {
    this.newQualities.push(this.fb.group({
      quality: [''],
      design: ['']
    }));
    this.oldQualityData.push({
      quality: '',
      design: '',
      color: '',
      size: '',
      pieces: 0
    });
  }

  removeQualityRow(index: number) {
    this.newQualities.removeAt(index);
    this.oldQualityData.splice(index, 1);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    const newQualityValues = this.newQualities.value;
    const hasNewData = newQualityValues.some((item: any) =>
      (item.quality && item.quality.trim() !== '') || (item.design && item.design.trim() !== '')
    );

    if (!hasNewData) {
      Swal.fire({
        title: 'No Changes',
        text: 'Please enter at least one new quality or design value',
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return;
    }

    // Show success message
    Swal.fire({
      title: 'Success!',
      text: 'Quality and Design data updated successfully',
      icon: 'success',
      confirmButtonText: 'OK'
    }).then(() => {
      this.dialogRef.close({
        newQualities: newQualityValues,
        oldQualities: this.oldQualityData
      });
    });
  }
}
