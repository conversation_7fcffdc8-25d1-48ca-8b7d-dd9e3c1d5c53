import { Component, OnInit, ViewChild } from '@angular/core';
import { ImporterService } from '../../../../services/importer.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { CustomeServiceService } from '../../../../services/custome-service.service';
export interface DespatchedData {
  index: number;
  id: string;
  impoterNo: string;
  chooseAdate: string;
  country: string;
  currency: string;
  containerNumber: string;
  linerDetails: string;
  noOfInvoice: string;
  totalNumberOfPcs: string;
  totalQuantity: string;
  totalAmount: string;
}
const ELEMENT_DATA2: DespatchedData[] = [];
@Component({
  selector: 'app-container-despatch-report',
  templateUrl: './container-despatch-report.component.html',
  styleUrl: './container-despatch-report.component.css',
})
export class ContainerDespatchReportComponent implements OnInit {
  displayedColumns: string[] = [
    'id',
    'impoterNo',
    'chooseAdate',
    'containerNumber',
    'noOfInvoice',
    'totalNumberOfPcs',
    'totalQuantity',
    'totalAmount',
    'country',
    'currency',
  ];
  dataSource = new MatTableDataSource<DespatchedData>();
  constructor(private _service: ImporterService, private customeService : CustomeServiceService) {}
  ngOnInit(): void {
    this.getAllContainer();
  }

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }
  getAllContainer() {
    this._service.getAllContainerDespatche().subscribe((resp: any) => {
      console.log(resp);
      if (resp) {
        ELEMENT_DATA2.length = 0;
        resp.forEach((element: any, ind: number) => {
          let importerNo = element.impoterNo.split('/');
          let date = this.customeService.convertDate(element.chooseAdate);
          ELEMENT_DATA2.push({
            index: ind + 1,
            id: element._id,
            impoterNo: importerNo[0],
            chooseAdate: date,
            country: element.country,
            currency: element.currency,
            containerNumber: element.containerNumber,
            linerDetails: element.linerDetails,
            noOfInvoice: element.noOfInvoice,
            totalNumberOfPcs: element.totalNumberOfPcs,
            totalQuantity: element.totalQuantity,
            totalAmount: element.totalAmount,
          });
        });
        this.dataSource = new MatTableDataSource(ELEMENT_DATA2);
        this.ngAfterViewInit();
        return
      }
    });
  }
}
