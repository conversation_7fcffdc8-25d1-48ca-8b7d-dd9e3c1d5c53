/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!y.call({1:2},1);g.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:y;var m,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,x=T({}.toString),E=T("".slice),A=function(t){return E(x(t),8,-1)},C=A,R=P,F=function(t){if("Function"===C(t))return R(t)},M=a,D=A,I=Object,L=F("".split),N=M((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"==D(t)?L(t,""):I(t)}:I,z=function(t){return null==t},_=z,k=TypeError,q=function(t){if(_(t))throw k("Can't call method on "+t);return t},B=N,G=q,H=function(t){return B(G(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},J=W.all,K=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===J}:function(t){return"function"==typeof t},Q=K,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,$=K,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=F({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(h=(m=at.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!h&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(h=+m[1]);var ct=h,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=K,bt=nt,dt=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&bt(n.prototype,dt(t))},ht=String,vt=K,St=function(t){try{return ht(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=z,Pt=p,Tt=K,xt=X,Et=TypeError,At={exports:{}},Ct=i,Rt=Object.defineProperty,Ft=function(t,n){try{Rt(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Mt=Ft,Dt="__core-js_shared__",It=i[Dt]||Mt(Dt,{}),Lt=It;(At.exports=function(t,n){return Lt[t]||(Lt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Nt=q,zt=Object,_t=function(t){return zt(Nt(t))},kt=_t,qt=F({}.hasOwnProperty),Bt=Object.hasOwn||function(t,n){return qt(kt(t),n)},Gt=F,Ht=0,Ut=Math.random(),Wt=Gt(1..toString),Jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Ht+Ut,36)},Kt=i,Qt=At.exports,Vt=Bt,Xt=Jt,Yt=st,$t=pt,Zt=Qt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,an=mt,cn=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!xt(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!xt(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!xt(e=Pt(r,t)))return e;throw Et("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=mt,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},bn=X,dn=i.document,mn=bn(dn)&&bn(dn.createElement),hn=function(t){return mn?dn.createElement(t):{}},vn=!c&&!a((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),Sn=c,wn=p,On=g,jn=v,Pn=H,Tn=yn,xn=Bt,En=vn,An=Object.getOwnPropertyDescriptor;u.f=Sn?An:function(t,n){if(t=Pn(t),n=Tn(n),En)try{return An(t,n)}catch(t){}if(xn(t,n))return jn(!wn(On.f,t,n),t[n])};var Cn={},Rn=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Fn=X,Mn=String,Dn=TypeError,In=function(t){if(Fn(t))return t;throw Dn(Mn(t)+" is not an object")},Ln=c,Nn=vn,zn=Rn,_n=In,kn=yn,qn=TypeError,Bn=Object.defineProperty,Gn=Object.getOwnPropertyDescriptor,Hn="enumerable",Un="configurable",Wn="writable";Cn.f=Ln?zn?function(t,n,r){if(_n(t),n=kn(n),_n(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=Gn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Hn in r?r.enumerable:e.enumerable,writable:!1})}return Bn(t,n,r)}:Bn:function(t,n,r){if(_n(t),n=kn(n),_n(r),Nn)try{return Bn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw qn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Jn=Cn,Kn=v,Qn=c?function(t,n,r){return Jn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=c,Yn=Bt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=K,er=It,or=F(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=At.exports,gr=Jt,yr=pr("keys"),br={},dr=sr,mr=i,hr=X,vr=Qn,Sr=Bt,wr=It,Or=function(t){return yr[t]||(yr[t]=gr(t))},jr=br,Pr="Object already initialized",Tr=mr.TypeError,xr=mr.WeakMap;if(dr||wr.state){var Er=wr.state||(wr.state=new xr);Er.get=Er.get,Er.has=Er.has,Er.set=Er.set,ir=function(t,n){if(Er.has(t))throw Tr(Pr);return n.facade=t,Er.set(t,n),n},ur=function(t){return Er.get(t)||{}},ar=function(t){return Er.has(t)}}else{var Ar=Or("state");jr[Ar]=!0,ir=function(t,n){if(Sr(t,Ar))throw Tr(Pr);return n.facade=t,vr(t,Ar,n),n},ur=function(t){return Sr(t,Ar)?t[Ar]:{}},ar=function(t){return Sr(t,Ar)}}var Cr={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Rr=a,Fr=K,Mr=Bt,Dr=c,Ir=nr.CONFIGURABLE,Lr=cr,Nr=Cr.enforce,zr=Cr.get,_r=Object.defineProperty,kr=Dr&&!Rr((function(){return 8!==_r((function(){}),"length",{value:8}).length})),qr=String(String).split("String"),Br=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Mr(t,"name")||Ir&&t.name!==n)&&(Dr?_r(t,"name",{value:n,configurable:!0}):t.name=n),kr&&r&&Mr(r,"arity")&&t.length!==r.arity&&_r(t,"length",{value:r.arity});try{r&&Mr(r,"constructor")&&r.constructor?Dr&&_r(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Nr(t);return Mr(e,"source")||(e.source=qr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Br((function(){return Fr(this)&&zr(this).source||Lr(this)}),"toString");var Gr=K,Hr=Cn,Ur=Vn.exports,Wr=Ft,Jr={},Kr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Kr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=H,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=Bt,le=H,se=ce.indexOf,pe=br,ge=F([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Jr.f=Object.getOwnPropertyNames||function(t){return ye(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var me=tt,he=Jr,ve=de,Se=In,we=F([].concat),Oe=me("Reflect","ownKeys")||function(t){var n=he.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},je=Bt,Pe=Oe,Te=u,xe=Cn,Ee=a,Ae=K,Ce=/#|\.prototype\./,Re=function(t,n){var r=Me[Fe(t)];return r==Ie||r!=De&&(Ae(n)?Ee(n):!!n)},Fe=Re.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Me=Re.data={},De=Re.NATIVE="N",Ie=Re.POLYFILL="P",Le=Re,Ne=i,ze=u.f,_e=Qn,ke=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Gr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Hr.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},qe=Ft,Be=function(t,n,r){for(var e=Pe(n),o=xe.f,i=Te.f,u=0;u<e.length;u++){var a=e[u];je(t,a)||r&&je(r,a)||o(t,a,i(n,a))}},Ge=Le,He=A,Ue=Array.isArray||function(t){return"Array"==He(t)},We=TypeError,Je=yn,Ke=Cn,Qe=v,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=K,$e=A,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=F,eo=a,oo=K,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),go=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,mo=Ue,ho=bo,vo=X,So=en("species"),wo=Array,Oo=function(t){var n;return mo(t)&&(n=t.constructor,(ho(n)&&(n===wo||mo(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=a,Po=ct,To=en("species"),xo=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?Ne:f?Ne[a]||qe(a,{}):(Ne[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=ze(r,e))&&u.value:r[e],!Ge(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Be(i,o)}(t.sham||o&&o.sham)&&_e(i,"sham",!0),ke(r,e,i,t)}},Eo=a,Ao=Ue,Co=X,Ro=_t,Fo=ee,Mo=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Do=function(t,n,r){var e=Je(n);e in t?Ke.f(t,e,Qe(0,r)):t[e]=r},Io=function(t,n){return new(Oo(t))(0===n?0:n)},Lo=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},No=ct,zo=en("isConcatSpreadable"),_o=No>=51||!Eo((function(){var t=[];return t[zo]=!1,t.concat()[0]!==t})),ko=Lo("concat"),qo=function(t){if(!Co(t))return!1;var n=t[zo];return void 0!==n?!!n:Ao(t)};xo({target:"Array",proto:!0,arity:1,forced:!_o||!ko},{concat:function(t){var n,r,e,o,i,u=Ro(this),a=Io(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(qo(i=-1===n?u:arguments[n]))for(o=Fo(i),Mo(c+o),r=0;r<o;r++,c++)r in i&&Do(a,c,i[r]);else Mo(c+1),Do(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["pt-BR"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Carregando, aguarde"},formatRecordsPerPage:function(t){return"".concat(t," registros por página")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Exibindo ".concat(t," até ").concat(n," de ").concat(r," linhas (filtradas de um total de ").concat(e," linhas)"):"Exibindo ".concat(t," até ").concat(n," de ").concat(r," linhas")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"Para a página ".concat(t)},formatSRPaginationNextText:function(){return"próxima página"},formatDetailPagination:function(t){return"Mostrando ".concat(t," linhas")},formatClearSearch:function(){return"Limpar Pesquisa"},formatSearch:function(){return"Pesquisar"},formatNoMatches:function(){return"Nenhum registro encontrado"},formatPaginationSwitch:function(){return"Ocultar/Exibir paginação"},formatPaginationSwitchDown:function(){return"Mostrar Paginação"},formatPaginationSwitchUp:function(){return"Esconder Paginação"},formatRefresh:function(){return"Recarregar"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Colunas"},formatColumnsToggleAll:function(){return"Alternar tudo"},formatFullscreen:function(){return"Tela cheia"},formatAllRows:function(){return"Tudo"},formatAutoRefresh:function(){return"Atualização Automática"},formatExport:function(){return"Exportar dados"},formatJumpTo:function(){return"IR"},formatAdvancedSearch:function(){return"Pesquisa Avançada"},formatAdvancedCloseButton:function(){return"Fechar"},formatFilterControlSwitch:function(){return"Ocultar/Exibir controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Exibir controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["pt-BR"])}));
