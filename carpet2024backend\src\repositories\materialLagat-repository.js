const MaterialLagat = require('../model/phase-3/materialLagat');

const createMaterialLagat = async (materialLagatData) => {
  return await MaterialLagat.create(materialLagatData);
};

const getAllMaterialLagats = async () => {
  return await MaterialLagat.find()
    .populate('quality')
    .populate({
      path: 'AddDesign',
      populate: [
        { path: 'productQuality' },
        { path: 'border' },
        { path: 'ground' },
        { path: 'colourLagats.colour' }
      ]
    })
    .populate('Color')
    .populate('tanaDescription')
    .populate('sootDescription')
    .populate('tharriDescription')
    .populate('silkDescription')
    .populate('katiDescription')
    .populate('item.description')
    .exec();
};

const getMaterialLagatById = async (id) => {
  return await MaterialLagat.findById(id)
    .populate('quality')
    .populate({
      path: 'AddDesign',
      populate: [
        { path: 'productQuality' },
        { path: 'border' },
        { path: 'ground' },
        { path: 'colourLagats.colour' }
      ]
    })
    .populate('Color')
    .populate('tanaDescription')
    .populate('sootDescription')
    .populate('tharriDescription')
    .populate('silkDescription')
    .populate('katiDescription')
    .populate('item.description')
    .exec();
};

const updateMaterialLagat = async (id, materialLagatData) => {
  return await MaterialLagat.findByIdAndUpdate(id, materialLagatData, { new: true })
    .populate('quality')
    .populate({
      path: 'AddDesign',
      populate: [
        { path: 'productQuality' },
        { path: 'border' },
        { path: 'ground' },
        { path: 'colourLagats.colour' }
      ]
    })
    .populate('Color')
    .populate('tanaDescription')
    .populate('sootDescription')
    .populate('tharriDescription')
    .populate('silkDescription')
    .populate('katiDescription')
    .populate('item.description')
    .exec();
};

const deleteMaterialLagat = async (id) => {
  return await MaterialLagat.findByIdAndDelete(id).exec();
};

const updateField = async (id, field, value) => {
  const update = { [field]: value };
  return await MaterialLagat.findByIdAndUpdate(id, { $set: update }, { new: true }).exec();
};

const deleteField = async (id, fieldPath) => {
  let updateOperation;
  if (fieldPath.startsWith('item.')) {
    const parts = fieldPath.split('.');
    const index = parseInt(parts[1]);
    const subfield = parts.slice(2).join('.');
    updateOperation = { $unset: { [`item.${index}.${subfield}`]: "" } };
  } else {
    updateOperation = { $unset: { [fieldPath]: "" } };
  }
  return await MaterialLagat.findByIdAndUpdate(id, updateOperation, { new: true }).exec();
};

module.exports = {
  createMaterialLagat,
  getAllMaterialLagats,
  getMaterialLagatById,
  updateMaterialLagat,
  deleteMaterialLagat,
  updateField,
  deleteField,
};
