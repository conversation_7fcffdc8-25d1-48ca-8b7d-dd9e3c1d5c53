// services/invoiceService.js
const invoiceRepository = require('../../repositories/manifacturing/invoice-repository');

class InvoiceService {
    async createInvoice(data) {
        return await invoiceRepository.createInvoice(data);
    }

    async getAllInvoices(sortBy = 'invoiceDate', order = 'asc') {
        return await invoiceRepository.getAllInvoices(sortBy, order);
    }

    async getInvoiceById(id) {
        return await invoiceRepository.getInvoiceById(id);
    }

    async updateInvoice(id, data) {
        return await invoiceRepository.updateInvoice(id, data);
    }

    async deleteInvoice(id) {
        return await invoiceRepository.deleteInvoice(id);
    }
}

module.exports = new InvoiceService();

