const weaverEmployeeRepository = require('../repositories/weaver_employee-repository');

const createEmployee = async (employeeData) => {
  return await weaverEmployeeRepository.createEmployee(employeeData);
};

const getEmployees = async () => {
  return await weaverEmployeeRepository.getEmployees();
};

const getEmployeeById = async (id) => {
  return await weaverEmployeeRepository.getEmployeeById(id);
};

const updateEmployee = async (id, updateData) => {
  return await weaverEmployeeRepository.updateEmployee(id, updateData);
};

const deleteEmployee = async (id) => {
  return await weaverEmployeeRepository.deleteEmployee(id);
};

module.exports = {
  createEmployee,
  getEmployees,
  getEmployeeById,
  updateEmployee,
  deleteEmployee
};
