/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=e(t);function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function f(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=i(t);if(e){var o=i(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return c(this,r)}}function a(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=a(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},p=function(t){return t&&t.Math==Math&&t},y=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof s&&s)||function(){return this}()||Function("return this")(),b={},v=function(t){try{return!!t()}catch(t){return!0}},h=!v((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),d=!v((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),g=d,m=Function.prototype.call,O=g?m.bind(m):function(){return m.apply(m,arguments)},w={},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,P=S&&!j.call({1:2},1);w.f=P?function(t){var e=S(this,t);return!!e&&e.enumerable}:j;var T,E,x=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},A=d,_=Function.prototype,M=_.call,R=A&&_.bind.bind(M,M),D=function(t){return A?R(t):function(){return M.apply(t,arguments)}},F=D,I=F({}.toString),C=F("".slice),L=function(t){return C(I(t),8,-1)},k=L,z=D,B=function(t){if("Function"===k(t))return z(t)},N=v,U=L,G=Object,q=B("".split),H=N((function(){return!G("z").propertyIsEnumerable(0)}))?function(t){return"String"==U(t)?q(t,""):G(t)}:G,W=function(t){return null==t},K=W,Q=TypeError,V=function(t){if(K(t))throw Q("Can't call method on "+t);return t},X=H,Y=V,$=function(t){return X(Y(t))},J="object"==typeof document&&document.all,Z={all:J,IS_HTMLDDA:void 0===J&&void 0!==J},tt=Z.all,et=Z.IS_HTMLDDA?function(t){return"function"==typeof t||t===tt}:function(t){return"function"==typeof t},rt=et,nt=Z.all,ot=Z.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:rt(t)||t===nt}:function(t){return"object"==typeof t?null!==t:rt(t)},it=y,ut=et,ct=function(t){return ut(t)?t:void 0},ft=function(t,e){return arguments.length<2?ct(it[t]):it[t]&&it[t][e]},at=B({}.isPrototypeOf),lt=y,st=ft("navigator","userAgent")||"",pt=lt.process,yt=lt.Deno,bt=pt&&pt.versions||yt&&yt.version,vt=bt&&bt.v8;vt&&(E=(T=vt.split("."))[0]>0&&T[0]<4?1:+(T[0]+T[1])),!E&&st&&(!(T=st.match(/Edge\/(\d+)/))||T[1]>=74)&&(T=st.match(/Chrome\/(\d+)/))&&(E=+T[1]);var ht=E,dt=ht,gt=v,mt=!!Object.getOwnPropertySymbols&&!gt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&dt&&dt<41})),Ot=mt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,wt=ft,jt=et,St=at,Pt=Object,Tt=Ot?function(t){return"symbol"==typeof t}:function(t){var e=wt("Symbol");return jt(e)&&St(e.prototype,Pt(t))},Et=String,xt=et,At=function(t){try{return Et(t)}catch(t){return"Object"}},_t=TypeError,Mt=function(t){if(xt(t))return t;throw _t(At(t)+" is not a function")},Rt=W,Dt=O,Ft=et,It=ot,Ct=TypeError,Lt={exports:{}},kt=y,zt=Object.defineProperty,Bt=function(t,e){try{zt(kt,t,{value:e,configurable:!0,writable:!0})}catch(r){kt[t]=e}return e},Nt=Bt,Ut="__core-js_shared__",Gt=y[Ut]||Nt(Ut,{}),qt=Gt;(Lt.exports=function(t,e){return qt[t]||(qt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Ht=V,Wt=Object,Kt=function(t){return Wt(Ht(t))},Qt=Kt,Vt=B({}.hasOwnProperty),Xt=Object.hasOwn||function(t,e){return Vt(Qt(t),e)},Yt=B,$t=0,Jt=Math.random(),Zt=Yt(1..toString),te=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Zt(++$t+Jt,36)},ee=y,re=Lt.exports,ne=Xt,oe=te,ie=mt,ue=Ot,ce=re("wks"),fe=ee.Symbol,ae=fe&&fe.for,le=ue?fe:fe&&fe.withoutSetter||oe,se=function(t){if(!ne(ce,t)||!ie&&"string"!=typeof ce[t]){var e="Symbol."+t;ie&&ne(fe,t)?ce[t]=fe[t]:ce[t]=ue&&ae?ae(e):le(e)}return ce[t]},pe=O,ye=ot,be=Tt,ve=function(t,e){var r=t[e];return Rt(r)?void 0:Mt(r)},he=function(t,e){var r,n;if("string"===e&&Ft(r=t.toString)&&!It(n=Dt(r,t)))return n;if(Ft(r=t.valueOf)&&!It(n=Dt(r,t)))return n;if("string"!==e&&Ft(r=t.toString)&&!It(n=Dt(r,t)))return n;throw Ct("Can't convert object to primitive value")},de=TypeError,ge=se("toPrimitive"),me=function(t,e){if(!ye(t)||be(t))return t;var r,n=ve(t,ge);if(n){if(void 0===e&&(e="default"),r=pe(n,t,e),!ye(r)||be(r))return r;throw de("Can't convert object to primitive value")}return void 0===e&&(e="number"),he(t,e)},Oe=Tt,we=function(t){var e=me(t,"string");return Oe(e)?e:e+""},je=ot,Se=y.document,Pe=je(Se)&&je(Se.createElement),Te=function(t){return Pe?Se.createElement(t):{}},Ee=!h&&!v((function(){return 7!=Object.defineProperty(Te("div"),"a",{get:function(){return 7}}).a})),xe=h,Ae=O,_e=w,Me=x,Re=$,De=we,Fe=Xt,Ie=Ee,Ce=Object.getOwnPropertyDescriptor;b.f=xe?Ce:function(t,e){if(t=Re(t),e=De(e),Ie)try{return Ce(t,e)}catch(t){}if(Fe(t,e))return Me(!Ae(_e.f,t,e),t[e])};var Le={},ke=h&&v((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),ze=ot,Be=String,Ne=TypeError,Ue=function(t){if(ze(t))return t;throw Ne(Be(t)+" is not an object")},Ge=h,qe=Ee,He=ke,We=Ue,Ke=we,Qe=TypeError,Ve=Object.defineProperty,Xe=Object.getOwnPropertyDescriptor,Ye="enumerable",$e="configurable",Je="writable";Le.f=Ge?He?function(t,e,r){if(We(t),e=Ke(e),We(r),"function"==typeof t&&"prototype"===e&&"value"in r&&Je in r&&!r.writable){var n=Xe(t,e);n&&n.writable&&(t[e]=r.value,r={configurable:$e in r?r.configurable:n.configurable,enumerable:Ye in r?r.enumerable:n.enumerable,writable:!1})}return Ve(t,e,r)}:Ve:function(t,e,r){if(We(t),e=Ke(e),We(r),qe)try{return Ve(t,e,r)}catch(t){}if("get"in r||"set"in r)throw Qe("Accessors not supported");return"value"in r&&(t[e]=r.value),t};var Ze=Le,tr=x,er=h?function(t,e,r){return Ze.f(t,e,tr(1,r))}:function(t,e,r){return t[e]=r,t},rr={exports:{}},nr=h,or=Xt,ir=Function.prototype,ur=nr&&Object.getOwnPropertyDescriptor,cr=or(ir,"name"),fr={EXISTS:cr,PROPER:cr&&"something"===function(){}.name,CONFIGURABLE:cr&&(!nr||nr&&ur(ir,"name").configurable)},ar=et,lr=Gt,sr=B(Function.toString);ar(lr.inspectSource)||(lr.inspectSource=function(t){return sr(t)});var pr,yr,br,vr=lr.inspectSource,hr=et,dr=y.WeakMap,gr=hr(dr)&&/native code/.test(String(dr)),mr=Lt.exports,Or=te,wr=mr("keys"),jr={},Sr=gr,Pr=y,Tr=ot,Er=er,xr=Xt,Ar=Gt,_r=function(t){return wr[t]||(wr[t]=Or(t))},Mr=jr,Rr="Object already initialized",Dr=Pr.TypeError,Fr=Pr.WeakMap;if(Sr||Ar.state){var Ir=Ar.state||(Ar.state=new Fr);Ir.get=Ir.get,Ir.has=Ir.has,Ir.set=Ir.set,pr=function(t,e){if(Ir.has(t))throw Dr(Rr);return e.facade=t,Ir.set(t,e),e},yr=function(t){return Ir.get(t)||{}},br=function(t){return Ir.has(t)}}else{var Cr=_r("state");Mr[Cr]=!0,pr=function(t,e){if(xr(t,Cr))throw Dr(Rr);return e.facade=t,Er(t,Cr,e),e},yr=function(t){return xr(t,Cr)?t[Cr]:{}},br=function(t){return xr(t,Cr)}}var Lr={set:pr,get:yr,has:br,enforce:function(t){return br(t)?yr(t):pr(t,{})},getterFor:function(t){return function(e){var r;if(!Tr(e)||(r=yr(e)).type!==t)throw Dr("Incompatible receiver, "+t+" required");return r}}},kr=v,zr=et,Br=Xt,Nr=h,Ur=fr.CONFIGURABLE,Gr=vr,qr=Lr.enforce,Hr=Lr.get,Wr=Object.defineProperty,Kr=Nr&&!kr((function(){return 8!==Wr((function(){}),"length",{value:8}).length})),Qr=String(String).split("String"),Vr=rr.exports=function(t,e,r){"Symbol("===String(e).slice(0,7)&&(e="["+String(e).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!Br(t,"name")||Ur&&t.name!==e)&&(Nr?Wr(t,"name",{value:e,configurable:!0}):t.name=e),Kr&&r&&Br(r,"arity")&&t.length!==r.arity&&Wr(t,"length",{value:r.arity});try{r&&Br(r,"constructor")&&r.constructor?Nr&&Wr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=qr(t);return Br(n,"source")||(n.source=Qr.join("string"==typeof e?e:"")),t};Function.prototype.toString=Vr((function(){return zr(this)&&Hr(this).source||Gr(this)}),"toString");var Xr=et,Yr=Le,$r=rr.exports,Jr=Bt,Zr={},tn=Math.ceil,en=Math.floor,rn=Math.trunc||function(t){var e=+t;return(e>0?en:tn)(e)},nn=function(t){var e=+t;return e!=e||0===e?0:rn(e)},on=nn,un=Math.max,cn=Math.min,fn=nn,an=Math.min,ln=function(t){return t>0?an(fn(t),9007199254740991):0},sn=function(t){return ln(t.length)},pn=$,yn=function(t,e){var r=on(t);return r<0?un(r+e,0):cn(r,e)},bn=sn,vn=function(t){return function(e,r,n){var o,i=pn(e),u=bn(i),c=yn(n,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},hn={includes:vn(!0),indexOf:vn(!1)},dn=Xt,gn=$,mn=hn.indexOf,On=jr,wn=B([].push),jn=function(t,e){var r,n=gn(t),o=0,i=[];for(r in n)!dn(On,r)&&dn(n,r)&&wn(i,r);for(;e.length>o;)dn(n,r=e[o++])&&(~mn(i,r)||wn(i,r));return i},Sn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Zr.f=Object.getOwnPropertyNames||function(t){return jn(t,Sn)};var Pn={};Pn.f=Object.getOwnPropertySymbols;var Tn=ft,En=Zr,xn=Pn,An=Ue,_n=B([].concat),Mn=Tn("Reflect","ownKeys")||function(t){var e=En.f(An(t)),r=xn.f;return r?_n(e,r(t)):e},Rn=Xt,Dn=Mn,Fn=b,In=Le,Cn=v,Ln=et,kn=/#|\.prototype\./,zn=function(t,e){var r=Nn[Bn(t)];return r==Gn||r!=Un&&(Ln(e)?Cn(e):!!e)},Bn=zn.normalize=function(t){return String(t).replace(kn,".").toLowerCase()},Nn=zn.data={},Un=zn.NATIVE="N",Gn=zn.POLYFILL="P",qn=zn,Hn=y,Wn=b.f,Kn=er,Qn=function(t,e,r,n){n||(n={});var o=n.enumerable,i=void 0!==n.name?n.name:e;if(Xr(r)&&$r(r,i,n),n.global)o?t[e]=r:Jr(e,r);else{try{n.unsafe?t[e]&&(o=!0):delete t[e]}catch(t){}o?t[e]=r:Yr.f(t,e,{value:r,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return t},Vn=Bt,Xn=function(t,e,r){for(var n=Dn(e),o=In.f,i=Fn.f,u=0;u<n.length;u++){var c=n[u];Rn(t,c)||r&&Rn(r,c)||o(t,c,i(e,c))}},Yn=qn,$n=L,Jn=Array.isArray||function(t){return"Array"==$n(t)},Zn=TypeError,to=we,eo=Le,ro=x,no={};no[se("toStringTag")]="z";var oo="[object z]"===String(no),io=et,uo=L,co=se("toStringTag"),fo=Object,ao="Arguments"==uo(function(){return arguments}()),lo=B,so=v,po=et,yo=oo?uo:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=fo(t),co))?r:ao?uo(e):"Object"==(n=uo(e))&&io(e.callee)?"Arguments":n},bo=vr,vo=function(){},ho=[],go=ft("Reflect","construct"),mo=/^\s*(?:class|function)\b/,Oo=lo(mo.exec),wo=!mo.exec(vo),jo=function(t){if(!po(t))return!1;try{return go(vo,ho,t),!0}catch(t){return!1}},So=function(t){if(!po(t))return!1;switch(yo(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return wo||!!Oo(mo,bo(t))}catch(t){return!0}};So.sham=!0;var Po=!go||so((function(){var t;return jo(jo.call)||!jo(Object)||!jo((function(){t=!0}))||t}))?So:jo,To=Jn,Eo=Po,xo=ot,Ao=se("species"),_o=Array,Mo=function(t){var e;return To(t)&&(e=t.constructor,(Eo(e)&&(e===_o||To(e.prototype))||xo(e)&&null===(e=e[Ao]))&&(e=void 0)),void 0===e?_o:e},Ro=v,Do=ht,Fo=se("species"),Io=function(t,e){var r,n,o,i,u,c=t.target,f=t.global,a=t.stat;if(r=f?Hn:a?Hn[c]||Vn(c,{}):(Hn[c]||{}).prototype)for(n in e){if(i=e[n],o=t.dontCallGetSet?(u=Wn(r,n))&&u.value:r[n],!Yn(f?n:c+(a?".":"#")+n,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Xn(i,o)}(t.sham||o&&o.sham)&&Kn(i,"sham",!0),Qn(r,n,i,t)}},Co=v,Lo=Jn,ko=ot,zo=Kt,Bo=sn,No=function(t){if(t>9007199254740991)throw Zn("Maximum allowed index exceeded");return t},Uo=function(t,e,r){var n=to(e);n in t?eo.f(t,n,ro(0,r)):t[n]=r},Go=function(t,e){return new(Mo(t))(0===e?0:e)},qo=function(t){return Do>=51||!Ro((function(){var e=[];return(e.constructor={})[Fo]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Ho=ht,Wo=se("isConcatSpreadable"),Ko=Ho>=51||!Co((function(){var t=[];return t[Wo]=!1,t.concat()[0]!==t})),Qo=qo("concat"),Vo=function(t){if(!ko(t))return!1;var e=t[Wo];return void 0!==e?!!e:Lo(t)};Io({target:"Array",proto:!0,arity:1,forced:!Ko||!Qo},{concat:function(t){var e,r,n,o,i,u=zo(this),c=Go(u,0),f=0;for(e=-1,n=arguments.length;e<n;e++)if(Vo(i=-1===e?u:arguments[e]))for(o=Bo(i),No(f+o),r=0;r<o;r++,f++)r in i&&Uo(c,f,i[r]);else No(f+1),Uo(c,f++,i);return c.length=f,c}}),r.default.extend(r.default.fn.bootstrapTable.defaults,{deferUrl:void 0}),r.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(s,t);var e,r,c,a=f(s);function s(){return n(this,s),a.apply(this,arguments)}return e=s,r=[{key:"init",value:function(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];(t=l(i(s.prototype),"init",this)).call.apply(t,[this].concat(r)),this.options.deferUrl&&(this.options.url=this.options.deferUrl)}}],r&&o(e.prototype,r),c&&o(e,c),Object.defineProperty(e,"prototype",{writable:!1}),s}(r.default.BootstrapTable)}));
