

const multer = require('multer');
const path = require('path');

// Define storage for the images
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.resolve(__dirname, '../../public/all-image'));
  },
  filename: function (req, file, cb) {
    // Extract the file extension
    const ext = path.extname(file.originalname);
    // Generate a unique filename with the original extension
    cb(null, file.originalname + '-' + Date.now() + ext);
  }
});

// Filter for image files only
const fileFilter = (req, file, cb) => {
  // Accept image files only
  if (file.mimetype.startsWith('image')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

// Set up multer instance
const upload = multer({ storage: storage, fileFilter: fileFilter });

module.exports = upload;




// const multer = require('multer');
// const path = require('path');

// // Define storage for the images
// const storage = multer.diskStorage({
//   destination: function (req, file, cb) {
//     cb(null, path.resolve(__dirname, '../../public/all-image'));
//   },
//   filename: function (req, file, cb) {
//     cb(null, file.originalname + '-' + Date.now() + '.image');
//   }
// });

// // Filter for image files only
// const fileFilter = (req, file, cb) => {
//   // Accept image files only
//   if (file.mimetype.startsWith('image')) {
//     cb(null, true);
//   } else {
//     cb(new Error('Only image files are allowed!'), false);
//   }
// };

// // Set up multer instance
// const upload = multer({ storage: storage, fileFilter: fileFilter });

// module.exports = upload;
