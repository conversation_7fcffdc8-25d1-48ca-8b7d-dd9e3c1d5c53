
<div class="container mt-4">
  <section>
    <fieldset>
      <legend>
        <b>Material Issue</b>
        
      </legend>
      <div class="d-flex justify-content-end align-items-center">
        <div class="navigate-text">
        <a href="/admin/material-receive" class="text-end fs-6 fw-bold pe-1 mb-1">Go To Receive </a>
        </div>
        <div>
        <button
          mat-icon-button
          color="primary"
          class="navigate-button mb-2"
          (click)="navigateToMaterialReceive()"
          title="Go to Material Receive">
           <i class="fa-solid fa-arrow-right"></i>
        </button>
        </div>
      </div>

      <form [formGroup]="materialIssueForm" (ngSubmit)="onSubmit()">
        <!-- First Row: Challan No, Date, Weaver Name -->
        <div class="row">
          <div class="col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Challan No.</mat-label>
              <input matInput formControlName="challanNo">
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="date">
              <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Weaver Name</mat-label>
              <mat-select #weaverSelect formControlName="weaver" (selectionChange)="onWeaverChange()">
                <mat-option *ngFor="let weaverName of list" [value]="weaverName.weaver">
                  {{weaverName.displayName}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-3 issueNo">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Issue No.</mat-label>
              <mat-select #issueSelect formControlName="issueNo" (selectionChange)="onIssueNoChange()">
                <mat-option *ngFor="let issueData of availableIssues" [value]="issueData.Br_issueNo">
                  {{ issueData.Br_issueNo }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

    
      </form>

<!-- First Row: Issue Details -->
    <div class="issue-details-header ">
      <h3>Issue Details</h3>
    </div>
       <!-- Issue Details Table -->
    <div class="issue-details-table-section">
      <table class="issue-details-table">
        <thead>
          <tr>
            <th>Issue No</th>
            <th>Quality</th>
            <th>Design</th>
            <th>Colour</th>
            <th>Size</th>
            <th>Pcs</th>
            <th>Area</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of dataSource.data">
            <td>{{item.Br_issueNo}}</td>
            <td>{{item.quality?.quality || item.quality}}</td>
            <td>{{item.design?.design || item.design}}</td>
            <td>{{item.borderColour}}</td>
            <td>{{item.size?.sizeInYard || item.size}}</td>
            <td>{{item.pcs}}</td>
            <td>{{getCalculatedArea(item)}}</td>
          </tr>
          <tr *ngIf="dataSource.data.length === 0">
            <td colspan="8" class="no-data">No data available</td>
          </tr>
        </tbody>
      </table>
    </div>


    </fieldset>
  </section>
</div>


<!-- Issue Details Section -->
<div class="container mt-4" >
   <section>
    <fieldset>
    

   
    <!-- Material Description Section -->
   
      <form [formGroup]="materialIssueForm" >
       
          <div class="card-header bg-primary text-white text-center fw-bolder w-100 ">
            <h4 class="mb-0">Material Description & Lagat</h4>
          </div>
          
          <div class="card-body" style="border-top: 2px solid #1f497d;margin-top: 5px;">
            <!-- Bootstrap Grid Layout -->
            <div class="container-fluid">
             

              <!-- Material Columns with Description and Lagat vertically stacked -->
              <div class="row gx-3 col-md-12">
                <div class="coldes">

                   <div class="mt-2 Des-box-hieght">
                  <div class="text-center fw-bold text-primary fs-5  ">Description →</div>
                </div>

                 <div class="mt-2 Lag-box-hieght">
                  <div class="text-center fw-bold text-primary fs-5 pb-1">Lagat →</div>
                </div>

                <div class="mt-1 Issue-box-hieght">
                  <div class="text-center fw-bold text-primary fs-5 pb-2">Issue →</div>
                </div>

                </div>

               

                <!-- Kati Column -->
                <div class="col21 kati-height">
                <div class="mb-3">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Kati</div>
                </div>

                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Kati Description</mat-label>
                      <input matInput formControlName="katiDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Kati Lagat</mat-label>
                      <input matInput formControlName="katiLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Kati Issue</mat-label>
                      <input type="text" matInput formControlName="katiIssue" placeholder=".000" (blur)="setDigitKatiIssue($event)">
                    </mat-form-field>
                  </div>
                  <div>
                    <button mat-raised-button color="primary"
                            (click)="openKatiModal()"
                            class="kati-details-btn">
                      <mat-icon>visibility</mat-icon>
                      View Details
                    </button>
                  </div>
                </div>

                <!-- Tana Column -->
                <div class="col21">
                   <div class="mb-3 mt-2">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Tana</div>
                </div>
                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tana Description</mat-label>
                      <input matInput formControlName="tanaDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tana Lagat</mat-label>
                      <input matInput formControlName="tanaLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tana Issue</mat-label>
                      <input type="text" matInput formControlName="tanaIssue" placeholder=".000" (blur)="setDigitTanaIssue($event)" >
                    </mat-form-field>
                  </div>
                </div>

                <!-- Soot Column -->
                <div class="col21">
                  <div class="mb-3 mt-2">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Soot</div>
                </div>
                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Soot Description</mat-label>
                      <input matInput formControlName="sootDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Soot Lagat</mat-label>
                      <input matInput formControlName="sootLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Soot Issue</mat-label>
                      <input type="text" matInput formControlName="sootIssue" placeholder=".000" (blur)="setDigitSootIssue($event)" >
                    </mat-form-field>
                  </div>
                </div>

                <!-- Thari Column -->
                <div class="col21">
                  <div class="mb-3 mt-2">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Thari</div>
                </div>
                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tharri Description</mat-label>
                      <input matInput formControlName="thariDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tharri Lagat</mat-label>
                      <input matInput formControlName="thariLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tharri Issue</mat-label>
                      <input type="text" matInput formControlName="thariIssue" placeholder=".000" (blur)="setDigitTharriIssue($event)" >
                    </mat-form-field>
                  </div>
                </div>

                <!-- Silk Column -->
                <div class="col21">
                  <div class="mb-3 mt-2">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Silk</div>
                </div>
                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Silk Description</mat-label>
                      <input matInput formControlName="silkDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Silk Lagat</mat-label>
                      <input matInput formControlName="silkLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Silk Issue</mat-label>
                      <input type="text" matInput formControlName="silkIssue" placeholder=".000" (blur)="setDigitSilkIssue($event)" >
                    </mat-form-field>
                  </div>
                </div>
                 <!-- Other Column -->
                <div class="col21">
                 <div class="mb-3 mt-2">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Other</div>
                </div>
                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Other Description</mat-label>
                      <input matInput formControlName="otherDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Other Lagat</mat-label>
                      <input matInput formControlName="otherLagat" type="text" readonly>
                    </mat-form-field>
                  </div> 
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Other Issue</mat-label>
                      <input type="text" matInput formControlName="otherIssue" placeholder=".000" (blur)="setDigitOtherIssue($event)">
                    </mat-form-field>
                  </div>                  
                </div> 

              </div>
            </div>
          </div>

            <!-- Action Buttons -->
        <div class="d-flex justify-content-end text-center mt-3">
          <button mat-raised-button color="primary" type="submit" (click)="onSubmit()" class="me-2">
            <mat-icon>{{isEditMode ? 'update' : 'save'}}</mat-icon>
            {{isEditMode ? 'Update' : 'Save'}}
          </button>
          <button mat-raised-button color="warn" type="button" (click)="clearForm()" class="me-4">
            <mat-icon>clear</mat-icon>
            Clear
          </button>
        </div>
      </form>
   
    </fieldset>
  </section>

       <div class="d-flex justify-content-center">
        <!-- Kati Data Table -->
        <div *ngIf="katiData.length > 0" class="kati-data-section mt-4 col-md-12">
     <section >
      <fieldset>
          <legend><b>Kati Material Details</b></legend>
          <div class="kati-data-table-container">
            <table mat-table [dataSource]="katiDataSource" class="mat-elevation-z4 kati-data-table">
              <!-- Sr.No Column -->
              <ng-container matColumnDef="srNo">
                <th mat-header-cell *matHeaderCellDef>Sr.No</th>
                <td mat-cell *matCellDef="let element">{{element.srNo}}</td>
              </ng-container>

              <!-- Colour Column -->
              <ng-container matColumnDef="colour">
                <th mat-header-cell *matHeaderCellDef class="">Colour</th>
                <td mat-cell *matCellDef="let element" class="">{{getKatiColourDisplay(element)}}</td>
              </ng-container>

              <!-- Lagat Column -->
              <ng-container matColumnDef="lagat">
                <th mat-header-cell *matHeaderCellDef>Lagat</th>
                <td mat-cell *matCellDef="let element">{{element.lagat | number:'1.3-3'}}</td>
              </ng-container>

              <!-- Carpet Lagat Column -->
              <ng-container matColumnDef="carpetLagat">
                <th mat-header-cell *matHeaderCellDef>Carpet Lagat</th>
                <td mat-cell *matCellDef="let element">{{element.carpetLagat}}</td>
              </ng-container>

              <!-- Issue Column -->
              <ng-container matColumnDef="issue">
                <th mat-header-cell *matHeaderCellDef>Issue</th>
                <td mat-cell *matCellDef="let element">{{element.issueValue || '-'}}</td>
              </ng-container>

              <!-- To Issue Column -->
              <ng-container matColumnDef="toIssue">
                <th mat-header-cell *matHeaderCellDef>To Issue</th>
                <td mat-cell *matCellDef="let element">{{element.toIssueValue | number:'1.2-2'}}</td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="katiDisplayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: katiDisplayedColumns;"></tr>
            </table>
          </div>
          </fieldset>
        </section>
        </div>

      </div>

    
</div>


