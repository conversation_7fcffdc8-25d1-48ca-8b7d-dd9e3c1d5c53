/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return c(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=f(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},p=function(t){return t&&t.Math==Math&&t},y=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof s&&s)||function(){return this}()||Function("return this")(),d={},b=function(t){try{return!!t()}catch(t){return!0}},h=!b((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),v=!b((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),g=v,m=Function.prototype.call,w=g?m.bind(m):function(){return m.apply(m,arguments)},j={},O={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,T=S&&!O.call({1:2},1);j.f=T?function(t){var e=S(this,t);return!!e&&e.enumerable}:O;var P,C,E=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},R=v,x=Function.prototype,A=x.call,F=R&&x.bind.bind(A,A),_=function(t){return R?F(t):function(){return A.apply(t,arguments)}},D=_,k=D({}.toString),I=D("".slice),M=function(t){return I(k(t),8,-1)},B=M,L=_,z=function(t){if("Function"===B(t))return L(t)},N=b,W=M,H=Object,q=z("".split),G=N((function(){return!H("z").propertyIsEnumerable(0)}))?function(t){return"String"==W(t)?q(t,""):H(t)}:H,$=function(t){return null==t},U=$,V=TypeError,X=function(t){if(U(t))throw V("Can't call method on "+t);return t},K=G,Q=X,Y=function(t){return K(Q(t))},J="object"==typeof document&&document.all,Z={all:J,IS_HTMLDDA:void 0===J&&void 0!==J},tt=Z.all,et=Z.IS_HTMLDDA?function(t){return"function"==typeof t||t===tt}:function(t){return"function"==typeof t},nt=et,rt=Z.all,ot=Z.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:nt(t)||t===rt}:function(t){return"object"==typeof t?null!==t:nt(t)},it=y,ut=et,ct=function(t){return ut(t)?t:void 0},at=function(t,e){return arguments.length<2?ct(it[t]):it[t]&&it[t][e]},ft=z({}.isPrototypeOf),lt=y,st=at("navigator","userAgent")||"",pt=lt.process,yt=lt.Deno,dt=pt&&pt.versions||yt&&yt.version,bt=dt&&dt.v8;bt&&(C=(P=bt.split("."))[0]>0&&P[0]<4?1:+(P[0]+P[1])),!C&&st&&(!(P=st.match(/Edge\/(\d+)/))||P[1]>=74)&&(P=st.match(/Chrome\/(\d+)/))&&(C=+P[1]);var ht=C,vt=ht,gt=b,mt=!!Object.getOwnPropertySymbols&&!gt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&vt&&vt<41})),wt=mt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,jt=at,Ot=et,St=ft,Tt=Object,Pt=wt?function(t){return"symbol"==typeof t}:function(t){var e=jt("Symbol");return Ot(e)&&St(e.prototype,Tt(t))},Ct=String,Et=et,Rt=function(t){try{return Ct(t)}catch(t){return"Object"}},xt=TypeError,At=function(t){if(Et(t))return t;throw xt(Rt(t)+" is not a function")},Ft=At,_t=$,Dt=w,kt=et,It=ot,Mt=TypeError,Bt={exports:{}},Lt=y,zt=Object.defineProperty,Nt=function(t,e){try{zt(Lt,t,{value:e,configurable:!0,writable:!0})}catch(n){Lt[t]=e}return e},Wt=Nt,Ht="__core-js_shared__",qt=y[Ht]||Wt(Ht,{}),Gt=qt;(Bt.exports=function(t,e){return Gt[t]||(Gt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var $t=X,Ut=Object,Vt=function(t){return Ut($t(t))},Xt=Vt,Kt=z({}.hasOwnProperty),Qt=Object.hasOwn||function(t,e){return Kt(Xt(t),e)},Yt=z,Jt=0,Zt=Math.random(),te=Yt(1..toString),ee=function(t){return"Symbol("+(void 0===t?"":t)+")_"+te(++Jt+Zt,36)},ne=y,re=Bt.exports,oe=Qt,ie=ee,ue=mt,ce=wt,ae=re("wks"),fe=ne.Symbol,le=fe&&fe.for,se=ce?fe:fe&&fe.withoutSetter||ie,pe=function(t){if(!oe(ae,t)||!ue&&"string"!=typeof ae[t]){var e="Symbol."+t;ue&&oe(fe,t)?ae[t]=fe[t]:ae[t]=ce&&le?le(e):se(e)}return ae[t]},ye=w,de=ot,be=Pt,he=function(t,e){var n=t[e];return _t(n)?void 0:Ft(n)},ve=function(t,e){var n,r;if("string"===e&&kt(n=t.toString)&&!It(r=Dt(n,t)))return r;if(kt(n=t.valueOf)&&!It(r=Dt(n,t)))return r;if("string"!==e&&kt(n=t.toString)&&!It(r=Dt(n,t)))return r;throw Mt("Can't convert object to primitive value")},ge=TypeError,me=pe("toPrimitive"),we=function(t,e){if(!de(t)||be(t))return t;var n,r=he(t,me);if(r){if(void 0===e&&(e="default"),n=ye(r,t,e),!de(n)||be(n))return n;throw ge("Can't convert object to primitive value")}return void 0===e&&(e="number"),ve(t,e)},je=Pt,Oe=function(t){var e=we(t,"string");return je(e)?e:e+""},Se=ot,Te=y.document,Pe=Se(Te)&&Se(Te.createElement),Ce=function(t){return Pe?Te.createElement(t):{}},Ee=Ce,Re=!h&&!b((function(){return 7!=Object.defineProperty(Ee("div"),"a",{get:function(){return 7}}).a})),xe=h,Ae=w,Fe=j,_e=E,De=Y,ke=Oe,Ie=Qt,Me=Re,Be=Object.getOwnPropertyDescriptor;d.f=xe?Be:function(t,e){if(t=De(t),e=ke(e),Me)try{return Be(t,e)}catch(t){}if(Ie(t,e))return _e(!Ae(Fe.f,t,e),t[e])};var Le={},ze=h&&b((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Ne=ot,We=String,He=TypeError,qe=function(t){if(Ne(t))return t;throw He(We(t)+" is not an object")},Ge=h,$e=Re,Ue=ze,Ve=qe,Xe=Oe,Ke=TypeError,Qe=Object.defineProperty,Ye=Object.getOwnPropertyDescriptor,Je="enumerable",Ze="configurable",tn="writable";Le.f=Ge?Ue?function(t,e,n){if(Ve(t),e=Xe(e),Ve(n),"function"==typeof t&&"prototype"===e&&"value"in n&&tn in n&&!n.writable){var r=Ye(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Ze in n?n.configurable:r.configurable,enumerable:Je in n?n.enumerable:r.enumerable,writable:!1})}return Qe(t,e,n)}:Qe:function(t,e,n){if(Ve(t),e=Xe(e),Ve(n),$e)try{return Qe(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Ke("Accessors not supported");return"value"in n&&(t[e]=n.value),t};var en=Le,nn=E,rn=h?function(t,e,n){return en.f(t,e,nn(1,n))}:function(t,e,n){return t[e]=n,t},on={exports:{}},un=h,cn=Qt,an=Function.prototype,fn=un&&Object.getOwnPropertyDescriptor,ln=cn(an,"name"),sn={EXISTS:ln,PROPER:ln&&"something"===function(){}.name,CONFIGURABLE:ln&&(!un||un&&fn(an,"name").configurable)},pn=et,yn=qt,dn=z(Function.toString);pn(yn.inspectSource)||(yn.inspectSource=function(t){return dn(t)});var bn,hn,vn,gn=yn.inspectSource,mn=et,wn=y.WeakMap,jn=mn(wn)&&/native code/.test(String(wn)),On=Bt.exports,Sn=ee,Tn=On("keys"),Pn=function(t){return Tn[t]||(Tn[t]=Sn(t))},Cn={},En=jn,Rn=y,xn=ot,An=rn,Fn=Qt,_n=qt,Dn=Pn,kn=Cn,In="Object already initialized",Mn=Rn.TypeError,Bn=Rn.WeakMap;if(En||_n.state){var Ln=_n.state||(_n.state=new Bn);Ln.get=Ln.get,Ln.has=Ln.has,Ln.set=Ln.set,bn=function(t,e){if(Ln.has(t))throw Mn(In);return e.facade=t,Ln.set(t,e),e},hn=function(t){return Ln.get(t)||{}},vn=function(t){return Ln.has(t)}}else{var zn=Dn("state");kn[zn]=!0,bn=function(t,e){if(Fn(t,zn))throw Mn(In);return e.facade=t,An(t,zn,e),e},hn=function(t){return Fn(t,zn)?t[zn]:{}},vn=function(t){return Fn(t,zn)}}var Nn={set:bn,get:hn,has:vn,enforce:function(t){return vn(t)?hn(t):bn(t,{})},getterFor:function(t){return function(e){var n;if(!xn(e)||(n=hn(e)).type!==t)throw Mn("Incompatible receiver, "+t+" required");return n}}},Wn=b,Hn=et,qn=Qt,Gn=h,$n=sn.CONFIGURABLE,Un=gn,Vn=Nn.enforce,Xn=Nn.get,Kn=Object.defineProperty,Qn=Gn&&!Wn((function(){return 8!==Kn((function(){}),"length",{value:8}).length})),Yn=String(String).split("String"),Jn=on.exports=function(t,e,n){"Symbol("===String(e).slice(0,7)&&(e="["+String(e).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(e="get "+e),n&&n.setter&&(e="set "+e),(!qn(t,"name")||$n&&t.name!==e)&&(Gn?Kn(t,"name",{value:e,configurable:!0}):t.name=e),Qn&&n&&qn(n,"arity")&&t.length!==n.arity&&Kn(t,"length",{value:n.arity});try{n&&qn(n,"constructor")&&n.constructor?Gn&&Kn(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=Vn(t);return qn(r,"source")||(r.source=Yn.join("string"==typeof e?e:"")),t};Function.prototype.toString=Jn((function(){return Hn(this)&&Xn(this).source||Un(this)}),"toString");var Zn=et,tr=Le,er=on.exports,nr=Nt,rr=function(t,e,n,r){r||(r={});var o=r.enumerable,i=void 0!==r.name?r.name:e;if(Zn(n)&&er(n,i,r),r.global)o?t[e]=n:nr(e,n);else{try{r.unsafe?t[e]&&(o=!0):delete t[e]}catch(t){}o?t[e]=n:tr.f(t,e,{value:n,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return t},or={},ir=Math.ceil,ur=Math.floor,cr=Math.trunc||function(t){var e=+t;return(e>0?ur:ir)(e)},ar=function(t){var e=+t;return e!=e||0===e?0:cr(e)},fr=ar,lr=Math.max,sr=Math.min,pr=ar,yr=Math.min,dr=function(t){return t>0?yr(pr(t),9007199254740991):0},br=function(t){return dr(t.length)},hr=Y,vr=function(t,e){var n=fr(t);return n<0?lr(n+e,0):sr(n,e)},gr=br,mr=function(t){return function(e,n,r){var o,i=hr(e),u=gr(i),c=vr(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},wr={includes:mr(!0),indexOf:mr(!1)},jr=Qt,Or=Y,Sr=wr.indexOf,Tr=Cn,Pr=z([].push),Cr=function(t,e){var n,r=Or(t),o=0,i=[];for(n in r)!jr(Tr,n)&&jr(r,n)&&Pr(i,n);for(;e.length>o;)jr(r,n=e[o++])&&(~Sr(i,n)||Pr(i,n));return i},Er=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Rr=Cr,xr=Er.concat("length","prototype");or.f=Object.getOwnPropertyNames||function(t){return Rr(t,xr)};var Ar={};Ar.f=Object.getOwnPropertySymbols;var Fr=at,_r=or,Dr=Ar,kr=qe,Ir=z([].concat),Mr=Fr("Reflect","ownKeys")||function(t){var e=_r.f(kr(t)),n=Dr.f;return n?Ir(e,n(t)):e},Br=Qt,Lr=Mr,zr=d,Nr=Le,Wr=b,Hr=et,qr=/#|\.prototype\./,Gr=function(t,e){var n=Ur[$r(t)];return n==Xr||n!=Vr&&(Hr(e)?Wr(e):!!e)},$r=Gr.normalize=function(t){return String(t).replace(qr,".").toLowerCase()},Ur=Gr.data={},Vr=Gr.NATIVE="N",Xr=Gr.POLYFILL="P",Kr=Gr,Qr=y,Yr=d.f,Jr=rn,Zr=rr,to=Nt,eo=function(t,e,n){for(var r=Lr(e),o=Nr.f,i=zr.f,u=0;u<r.length;u++){var c=r[u];Br(t,c)||n&&Br(n,c)||o(t,c,i(e,c))}},no=Kr,ro=function(t,e){var n,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(n=a?Qr:f?Qr[c]||to(c,{}):(Qr[c]||{}).prototype)for(r in e){if(i=e[r],o=t.dontCallGetSet?(u=Yr(n,r))&&u.value:n[r],!no(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;eo(i,o)}(t.sham||o&&o.sham)&&Jr(i,"sham",!0),Zr(n,r,i,t)}},oo=Cr,io=Er,uo=Object.keys||function(t){return oo(t,io)},co=h,ao=z,fo=w,lo=b,so=uo,po=Ar,yo=j,bo=Vt,ho=G,vo=Object.assign,go=Object.defineProperty,mo=ao([].concat),wo=!vo||lo((function(){if(co&&1!==vo({b:1},vo(go({},"a",{enumerable:!0,get:function(){go(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!=vo({},t)[n]||so(vo({},e)).join("")!=r}))?function(t,e){for(var n=bo(t),r=arguments.length,o=1,i=po.f,u=yo.f;r>o;)for(var c,a=ho(arguments[o++]),f=i?mo(so(a),i(a)):so(a),l=f.length,s=0;l>s;)c=f[s++],co&&!fo(u,a,c)||(n[c]=a[c]);return n}:vo,jo=wo;ro({target:"Object",stat:!0,arity:2,forced:Object.assign!==jo},{assign:jo});var Oo=M,So=Array.isArray||function(t){return"Array"==Oo(t)},To=TypeError,Po=Oe,Co=Le,Eo=E,Ro={};Ro[pe("toStringTag")]="z";var xo="[object z]"===String(Ro),Ao=xo,Fo=et,_o=M,Do=pe("toStringTag"),ko=Object,Io="Arguments"==_o(function(){return arguments}()),Mo=Ao?_o:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=ko(t),Do))?n:Io?_o(e):"Object"==(r=_o(e))&&Fo(e.callee)?"Arguments":r},Bo=z,Lo=b,zo=et,No=Mo,Wo=gn,Ho=function(){},qo=[],Go=at("Reflect","construct"),$o=/^\s*(?:class|function)\b/,Uo=Bo($o.exec),Vo=!$o.exec(Ho),Xo=function(t){if(!zo(t))return!1;try{return Go(Ho,qo,t),!0}catch(t){return!1}},Ko=function(t){if(!zo(t))return!1;switch(No(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Vo||!!Uo($o,Wo(t))}catch(t){return!0}};Ko.sham=!0;var Qo=!Go||Lo((function(){var t;return Xo(Xo.call)||!Xo(Object)||!Xo((function(){t=!0}))||t}))?Ko:Xo,Yo=So,Jo=Qo,Zo=ot,ti=pe("species"),ei=Array,ni=function(t){var e;return Yo(t)&&(e=t.constructor,(Jo(e)&&(e===ei||Yo(e.prototype))||Zo(e)&&null===(e=e[ti]))&&(e=void 0)),void 0===e?ei:e},ri=function(t,e){return new(ni(t))(0===e?0:e)},oi=b,ii=ht,ui=pe("species"),ci=ro,ai=b,fi=So,li=ot,si=Vt,pi=br,yi=function(t){if(t>9007199254740991)throw To("Maximum allowed index exceeded");return t},di=function(t,e,n){var r=Po(e);r in t?Co.f(t,r,Eo(0,n)):t[r]=n},bi=ri,hi=function(t){return ii>=51||!oi((function(){var e=[];return(e.constructor={})[ui]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},vi=ht,gi=pe("isConcatSpreadable"),mi=vi>=51||!ai((function(){var t=[];return t[gi]=!1,t.concat()[0]!==t})),wi=hi("concat"),ji=function(t){if(!li(t))return!1;var e=t[gi];return void 0!==e?!!e:fi(t)};ci({target:"Array",proto:!0,arity:1,forced:!mi||!wi},{concat:function(t){var e,n,r,o,i,u=si(this),c=bi(u,0),a=0;for(e=-1,r=arguments.length;e<r;e++)if(ji(i=-1===e?u:arguments[e]))for(o=pi(i),yi(a+o),n=0;n<o;n++,a++)n in i&&di(c,a,i[n]);else yi(a+1),di(c,a++,i);return c.length=a,c}});var Oi=At,Si=v,Ti=z(z.bind),Pi=function(t,e){return Oi(t),void 0===e?t:Si?Ti(t,e):function(){return t.apply(e,arguments)}},Ci=G,Ei=Vt,Ri=br,xi=ri,Ai=z([].push),Fi=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,l,s){for(var p,y,d=Ei(a),b=Ci(d),h=Pi(f,l),v=Ri(b),g=0,m=s||xi,w=e?m(a,v):n||u?m(a,0):void 0;v>g;g++)if((c||g in b)&&(y=h(p=b[g],g,d),t))if(e)w[g]=y;else if(y)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:Ai(w,p)}else switch(t){case 4:return!1;case 7:Ai(w,p)}return i?-1:r||o?o:w}},_i={forEach:Fi(0),map:Fi(1),filter:Fi(2),some:Fi(3),every:Fi(4),find:Fi(5),findIndex:Fi(6),filterReject:Fi(7)},Di={},ki=h,Ii=ze,Mi=Le,Bi=qe,Li=Y,zi=uo;Di.f=ki&&!Ii?Object.defineProperties:function(t,e){Bi(t);for(var n,r=Li(e),o=zi(e),i=o.length,u=0;i>u;)Mi.f(t,n=o[u++],r[n]);return t};var Ni,Wi=at("document","documentElement"),Hi=qe,qi=Di,Gi=Er,$i=Cn,Ui=Wi,Vi=Ce,Xi=Pn("IE_PROTO"),Ki=function(){},Qi=function(t){return"<script>"+t+"</"+"script>"},Yi=function(t){t.write(Qi("")),t.close();var e=t.parentWindow.Object;return t=null,e},Ji=function(){try{Ni=new ActiveXObject("htmlfile")}catch(t){}var t,e;Ji="undefined"!=typeof document?document.domain&&Ni?Yi(Ni):((e=Vi("iframe")).style.display="none",Ui.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Qi("document.F=Object")),t.close(),t.F):Yi(Ni);for(var n=Gi.length;n--;)delete Ji.prototype[Gi[n]];return Ji()};$i[Xi]=!0;var Zi=pe,tu=Object.create||function(t,e){var n;return null!==t?(Ki.prototype=Hi(t),n=new Ki,Ki.prototype=null,n[Xi]=t):n=Ji(),void 0===e?n:qi.f(n,e)},eu=Le.f,nu=Zi("unscopables"),ru=Array.prototype;null==ru[nu]&&eu(ru,nu,{configurable:!0,value:tu(null)});var ou=ro,iu=_i.find,uu=function(t){ru[nu][t]=!0},cu="find",au=!0;cu in[]&&Array(1).find((function(){au=!1})),ou({target:"Array",proto:!0,forced:au},{find:function(t){return iu(this,t,arguments.length>1?arguments[1]:void 0)}}),uu(cu);var fu=Mo,lu=xo?{}.toString:function(){return"[object "+fu(this)+"]"};xo||rr(Object.prototype,"toString",lu,{unsafe:!0});var su=b,pu=ro,yu=G,du=Y,bu=function(t,e){var n=[][t];return!!n&&su((function(){n.call(null,e||function(){return 1},1)}))},hu=z([].join),vu=yu!=Object,gu=bu("join",",");pu({target:"Array",proto:!0,forced:vu||!gu},{join:function(t){return hu(du(this),void 0===t?",":t)}});var mu=n.default.fn.bootstrapTable.utils;n.default.extend(n.default.fn.bootstrapTable.locales,{formatCopyRows:function(){return"Copy Rows"}}),n.default.extend(n.default.fn.bootstrapTable.defaults,n.default.fn.bootstrapTable.locales),n.default.extend(n.default.fn.bootstrapTable.defaults.icons,{copy:{bootstrap3:"glyphicon-copy icon-pencil",bootstrap5:"bi-clipboard",materialize:"content_copy","bootstrap-table":"icon-copy"}[n.default.fn.bootstrapTable.theme]||"fa-copy"});n.default.extend(n.default.fn.bootstrapTable.defaults,{showCopyRows:!1,copyWithHidden:!1,copyDelimiter:", ",copyNewline:"\n"}),n.default.extend(n.default.fn.bootstrapTable.columnDefaults,{ignoreCopy:!1,rawCopy:!1}),n.default.fn.bootstrapTable.methods.push("copyColumnsToClipboard"),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(p,t);var e,c,f,s=a(p);function p(){return r(this,p),s.apply(this,arguments)}return e=p,c=[{key:"initToolbar",value:function(){var t;this.options.showCopyRows&&this.header.stateField&&(this.buttons=Object.assign(this.buttons,{copyRows:{text:this.options.formatCopyRows(),icon:this.options.icons.copy,event:this.copyColumnsToClipboard,attributes:{"aria-label":this.options.formatCopyRows(),title:this.options.formatCopyRows()}}}));for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=l(i(p.prototype),"initToolbar",this)).call.apply(t,[this].concat(n)),this.$copyButton=this.$toolbar.find('>.columns [name="copyRows"]'),this.options.showCopyRows&&this.header.stateField&&this.updateCopyButton()}},{key:"copyColumnsToClipboard",value:function(){var t=this,e=[];n.default.each(this.getSelections(),(function(r,o){var i=[];n.default.each(t.options.columns[0],(function(e,n){if(n.field!==t.header.stateField&&(!t.options.copyWithHidden||t.options.copyWithHidden&&n.visible)&&!n.ignoreCopy&&null!==o[n.field]){var u=n.rawCopy?o[n.field]:mu.calculateObjectValue(n,t.header.formatters[e],[o[n.field],o,r],o[n.field]);i.push(u)}})),e.push(i.join(t.options.copyDelimiter))})),function(t){var e=document.createElement("textarea");n.default(e).html(t),document.body.appendChild(e),e.select();try{document.execCommand("copy")}catch(t){console.warn("Oops, unable to copy")}n.default(e).remove()}(e.join(this.options.copyNewline))}},{key:"updateSelected",value:function(){l(i(p.prototype),"updateSelected",this).call(this),this.updateCopyButton()}},{key:"updateCopyButton",value:function(){this.options.showCopyRows&&this.header.stateField&&this.$copyButton&&this.$copyButton.prop("disabled",!this.getSelections().length)}}],c&&o(e.prototype,c),f&&o(e,f),Object.defineProperty(e,"prototype",{writable:!1}),p}(n.default.BootstrapTable)}));
