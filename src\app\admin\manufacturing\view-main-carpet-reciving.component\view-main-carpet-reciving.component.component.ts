import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';


import { ManufactureService } from '../../../services/manufacture.service';
import { SweetalertService } from '../../../services/sweetalert.service';
import { MasterService } from '../../../services/master.service';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';
import { CarpetBorderComponent } from './modals/carpet-border/carpet-border.component';

interface CarpetReceiving {
  SrNo: number;
  CarpetNo: string;
  CDate: string;
  IssueNo: string;
  IDate: string;
  Weaver: string;
  Quality: string;
  Design: string;
  Color: string;
  Size: string;
  Pcs: number;
  Area: number;
  Rate: number;
  Amount: number;
  _id: string; // Added for MongoDB ObjectId
}

@Component({
  selector: 'app-view-main-carpet-reciving.component',
  templateUrl: './view-main-carpet-reciving.component.component.html',
  styleUrl: './view-main-carpet-reciving.component.component.css'
})
export class ViewMainCarpetRecivingComponentComponent implements OnInit {
  displayedColumns: string[] = [
    'SrNo', 'CarpetNo', 'CDate', 'IssueNo', 'IDate', 'Weaver',
    'Quality', 'Design', 'Color', 'Size', 'Pcs', 'Area', 'Rate', 'Amount', 'actions'
  ];

  dataSource = new MatTableDataSource<CarpetReceiving>([]);
  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  areaUnit: string = '';

  
    constructor(
      private manufactureService: ManufactureService,
      private alert: SweetalertService,
      private snackBar: MatSnackBar,
      private customeService: CustomeServiceService,
      private router: Router,
      private route: ActivatedRoute,
      private http: HttpClient,
      public dialog: MatDialog
    ) {}

  ngOnInit() {
    // Populate availableYears with last 10 years
    const currentYear = new Date().getFullYear();
    this.availableYears = Array.from({ length: 10 }, (_, i) => currentYear - i);

    this.fetchCarpetData();
  }

  ngAfterViewInit() {
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
    this.dataSource.sortingDataAccessor = (item: any, property: string) => {
      switch (property) {
        case 'CarpetNo':
          const carpetNo = item.CarpetNo || '';
          // Extract number after dash for proper numerical sorting
          if (carpetNo.includes('-')) {
            const parts = carpetNo.split('-');
            return parseInt(parts[1]) || 0;
          }
          // Fallback: extract all digits
          return parseInt(carpetNo.replace(/\D/g, '')) || 0;
        default:
          return item[property];
      }
    };

    // Set default sorting to CarpetNo in descending order
    this.sort.sort({
      id: 'CarpetNo',
      start: 'desc',
      disableClear: false
    });

    // Disable sorting for SrNo column
    this.sort.sortChange.subscribe(() => {
      const data = this.dataSource.data;
      data.forEach((item, index) => {
        item.SrNo = index + 1;
      });
      this.dataSource.data = data;
    });
  }

  // viewCarpetDetails() {
  //   // Navigate to the detailed view with the carpet ID or number
  //   this.router.navigate(['/admin/view-carpet-reciving']);
  // }

  
  selectedYear: number | null = null;
selectedMonth: number | null = null;
availableYears: number[] = [];

availableMonths = [
  { value: null, label: 'All' }, // All option
  { value: 1, label: 'January' },
  { value: 2, label: 'February' },
  { value: 3, label: 'March' },
  { value: 4, label: 'April' },
  { value: 5, label: 'May' },
  { value: 6, label: 'June' },
  { value: 7, label: 'July' },
  { value: 8, label: 'August' },
  { value: 9, label: 'September' },
  { value: 10, label: 'October' },
  { value: 11, label: 'November' },
  { value: 12, label: 'December' },
];

selectedWeaver: string = '';
weaverList: string[] = [];


  applyAllFilters() {
    const year = this.selectedYear;
    const month = this.selectedMonth;
    const Weaver = this.selectedWeaver;

    this.dataSource.filterPredicate = (data: any, _: string) => {
      // Use CDate for year/month filtering (format: dd-mm-yyyy)
      if (!data.CDate) return false;
      const [day, monthStr, yearStr] = data.CDate.split('-');
      const rowMonth = parseInt(monthStr, 10);
      const rowYear = parseInt(yearStr, 10);
      const matchYear = !year || rowYear === year;
      const matchMonth = month === null || rowMonth === month;
      const matchWeaver = !Weaver || data.Weaver === Weaver;
      return matchYear && matchMonth && matchWeaver;
    };

    // This forces the filterPredicate to run
    this.dataSource.filter = Math.random().toString();
}
formatDate(dateString: string): string {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString; // In case the date is already formatted

  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
}

  fetchCarpetData() {
    this.http.get<any[]>('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied')
      .subscribe({
        next: (data) => {
          console.log('Raw data from API:', data);
          if (!Array.isArray(data)) {
            console.error('Expected array from API');
            return;
          }

          // Sort data by sequential number only (ignore branch prefix)
          const sortedData = data.sort((a, b) => {
            const extractNumber = (receiveNo: string) => {
              if (!receiveNo) return 0;
              // Extract number after dash (K-2400001 -> 2400001)
              if (receiveNo.includes('-')) {
                const parts = receiveNo.split('-');
                return parseInt(parts[1]) || 0;
              }
              // Fallback: extract all digits
              return parseInt(receiveNo.replace(/\D/g, '')) || 0;
            };

            const numA = extractNumber(a.receiveNo || '');
            const numB = extractNumber(b.receiveNo || '');

            // Sort in descending order (latest first)
            return numB - numA;
          });

          // Map the sorted data with sequential SrNo
          const mappedData = sortedData.map((item, index) => {
            // Parse both area values if present
            let areaFt = null, areaYd = null;
            if (typeof item.area === 'string') {
              const ftMatch = item.area.match(/([\d.]+)\s*Ft/i);
              const ydMatch = item.area.match(/([\d.]+)\s*Yd/i);
              if (ftMatch) areaFt = parseFloat(ftMatch[1]);
              if (ydMatch) areaYd = parseFloat(ydMatch[1]);
            }
            return {
              SrNo: index + 1,
              CarpetNo: item.receiveNo || '',
              CDate: this.formatDate(item.receivingDate) || '',
              IssueNo: item.issueNo?.Br_issueNo || '',
              IDate: this.customeService.convertDate(item.issueNo?.date) || '',
              Weaver: item.receiveNo?.startsWith('H-') ?
                (item.weaverName || 'N/A') :
                (item.K?.branchCode+' - '+item.weaverNumber?.name || 'N/A'),
              Quality: item.issueNo?.quality?.quality || '',
              Design: item.issueNo?.design?.design || '',
              Color: item.issueNo?.borderColour || '',
              Size: item.issueNo?.size?.sizeInYard || '',
              Pcs: item.pcs || 0,
              Area: item.area || 0,
              Rate: item.issueNo?.rate || 0,
              Amount: item.amount || 0,
              areaFt: areaFt,
              areaYd: areaYd,
              _id: item._id // Add this line to include MongoDB ObjectId
            };
          });

          console.log('Mapped data for table:', mappedData);
          this.dataSource.data = mappedData;

          // Weaver List
          const weaverSet = new Set<string>();
          data.forEach(item => {
            if (item.receiveNo?.startsWith('H-')) {
              // For H- format records, use weaverName directly
              if (item.weaverName) {
                weaverSet.add(item.weaverName);
              }
            } else {
              // For K- format records, use branch code + weaver name
              if (item.K?.branchCode && item.weaverNumber?.name) {
                weaverSet.add(item.K.branchCode + ' - ' + item.weaverNumber.name);
              }
            }
          });
          this.weaverList = Array.from(weaverSet).sort();
        },
        error: (error) => {
          console.error('Error fetching carpet data:', error);
          this.snackBar.open('Error loading carpet data', 'Close', {
            duration: 3000,
            horizontalPosition: 'right',
            verticalPosition: 'top'
          });
        }
      });
  }

  applyFilter(event?: Event) {
    const filterValue = event ? (event.target as HTMLInputElement).value.trim().toLowerCase() : '';
    this.dataSource.filter = filterValue;
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  } 
    deleteCarpet(carpetNo: string) {
    this.alert.confirm({
      title: 'Delete Carpet',
      text: `Are you sure you want to delete carpet no. ${carpetNo}?`,
      icon: 'warning',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it'
    }).then((result: { isConfirmed: boolean }) => {
      if (result.isConfirmed) {
        // First get the carpet details to find its _id
        this.http.get<any[]>('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied')
          .subscribe({
            next: (data) => {
              const carpetToDelete = data.find(carpet => carpet.receiveNo === carpetNo);
              if (!carpetToDelete || !carpetToDelete._id) {
                this.alert.error('Error', 'Carpet not found');
                return;
              }

              // Now delete using the _id
              this.http.delete(`http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied/${carpetToDelete._id}`)
                .subscribe({
                  next: () => {
                    this.dataSource.data = this.dataSource.data.filter(item => item.CarpetNo !== carpetNo);
                    this.alert.success('Success', 'Carpet deleted successfully');
                    // Refresh the data to ensure everything is in sync
                    this.fetchCarpetData();
                  },
                  error: (error) => {
                    console.error('Error deleting carpet:', error);
                    const errorMessage = error.error?.message || 'Failed to delete carpet. Please try again.';
                    this.alert.error('Error', errorMessage);
                  }
                });
            },
            error: (error) => {
              console.error('Error fetching carpet details:', error);
              this.alert.error('Error', 'Failed to get carpet details');
            }
          });
      }
    });
  }


 getAreaHeader() {
    if (this.areaUnit === 'Sq.Feet') return 'AreaInFeet';
    if (this.areaUnit === 'Sq.Yard') return 'AreaInYard';
    return 'Area';
  }

  getAreaValue(element: any) {
    const pcs = element.Pcs || 1;
    // Use the setSizeYard formula for area calculation
    let areaInYard = 0;
    let areaInFeet = 0;
    if (element.Size) {
      // Parse sizeInYard (format: "width x length", e.g. "1.10 x 2.10")
      const calcData = element.Size.split(/[xX]/);
      if (calcData.length === 2) {
        const widthParts = calcData[0].trim().split('.');
        const lengthParts = calcData[1].trim().split('.');
        const width1 = parseInt(widthParts[0]) || 0;
        const width2 = parseInt(widthParts[1]) || 0;
        const length1 = parseInt(lengthParts[0]) || 0;
        const length2 = parseInt(lengthParts[1]) || 0;
        const sizeInYaardCalc1 = width1 * 12 + width2;
        const sizeInYaardCalc2 = length1 * 12 + length2;
        areaInYard = (sizeInYaardCalc1 * sizeInYaardCalc2) / 1296;
        areaInFeet = (sizeInYaardCalc1 * sizeInYaardCalc2) / 144;
      }
    }
    if (this.areaUnit === 'Sq.Feet') {
      return (areaInFeet * pcs).toFixed(2) + ' Ft';
    }
    if (this.areaUnit === 'Sq.Yard') {
      return (areaInYard * pcs).toFixed(2) + ' Yd';
    }
    // Default
    return element.Area;
  }




  // openCarpetBorderDialog(element: CarpetReceiving) {
  //   const dialogRef = this.dialog.open(CarpetBorderComponent, {
  //     width: '800px',
  //     data: {
  //       quality: element.Quality,
  //       design: element.Design,
  //       color: element.Color,
  //       size: element.Size,
  //       carpetReceivedId: element._id // Use the MongoDB _id
  //     }
  //   });

  //   dialogRef.afterClosed().subscribe(result => {
  //     if (result) {
  //       // Save to backend
  //       const carpetReceivedId = element._id; // Use the actual MongoDB _id
  //       this.http.post(`http://localhost:2000/api/phase-four/carpetReceivedUpdates/${carpetReceivedId}/border-size-history`, result)
  //         .subscribe({
  //           next: (res) => {
  //             this.alert.success('Success', 'Border size saved successfully!');
  //             this.snackBar.open('Border size saved!', 'Close', { duration: 2000 });
  //             console.log('Saved border size:', res);
  //           },
  //           error: (err) => {
  //             this.alert.error('Error', 'Failed to save border size!');
  //             this.snackBar.open('Failed to save border size', 'Close', { duration: 3000 });
  //             console.error('Failed to save border size:', err);
  //           }
  //         });
  //     }
  //   });
  // }

  
openCarpetBorderDialog(element: CarpetReceiving) {
  const dialogRef = this.dialog.open(CarpetBorderComponent, {
    width: '1030px',
    data: {
      carpetNo: element.CarpetNo || '',
      quality: element.Quality,
      design: element.Design,
      color: element.Color,
      size: element.Size,
      carpetReceivedId: element._id // Use the MongoDB _id
    }
  });

  dialogRef.afterClosed().subscribe(result => {
    if (result) {
      // Prepare request data like edit-carpet-modal.component
      const requestData = {
        carpetReceivedId: element._id,
        ...result, // All border form fields
        updateType: 'border-size',
        carpetInfo: {
          carpetNo: element.CarpetNo,
          quality: element.Quality,
          design: element.Design,
          weaver: element.Weaver,
          issueNo: element.IssueNo
        }
      };
      this.http.post('http://localhost:2000/api/phase-four/carpetReceivedUpdates', requestData)
        .subscribe({
          next: (res) => {
            this.alert.success('Success', 'Border size saved successfully!');
            this.snackBar.open('Border size saved!', 'Close', { duration: 2000 });
            console.log('Saved border size:', res);
          },
          error: (err) => {
            this.alert.error('Error', 'Failed to save border size!');
            this.snackBar.open('Failed to save border size', 'Close', { duration: 3000 });
            console.error('Failed to save border size:', err);
          }
        });
    }
  });
}
 
}
