const Role = require('../model/phase-1/role');

class RoleRepository {
  async createRole(roleData) {
    try {
      const existingRole = await Role.findOne({ name: roleData.name.toUpperCase() });
      if (existingRole) {
        return existingRole;
      } else {
        const newRole = new Role(roleData);
        await newRole.save();
        return newRole;
      }
    } catch (error) {
      throw error;
    }
  }

  async getRoleById(roleId) {
    try {
      const role = await Role.findById(roleId);
      return role;
    } catch (error) {
      throw error;
    }
  }

  async getRoleByName(roleName) {
    try {
      const role = await Role.findOne({ name: roleName });
      return role;
    } catch (error) {
      throw error;
    }
  }

  async getAllRoles() {
    try {
      const roles = await Role.find();
      return roles;
    } catch (error) {
      throw error;
    }
  }

  async updateRole(roleId, roleData) {
    try {
      const role = await Role.findByIdAndUpdate(roleId, roleData, { new: true });
      return role;
    } catch (error) {
      throw error;
    }
  }

  async deleteRole(roleId) {
    try {
      const role = await Role.findByIdAndDelete(roleId);
      return role;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new RoleRepository();
