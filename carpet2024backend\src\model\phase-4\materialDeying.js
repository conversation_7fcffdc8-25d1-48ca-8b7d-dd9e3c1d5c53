const mongoose = require('mongoose');

const materialDeyingSchema = new mongoose.Schema({
    challanNo:{
        type: String,
        required: true,
    },
    date:{
        type: String,
        required: true,
    },
    name:{
        type: mongoose.Schema.Types.ObjectId,
        ref:'deyingRate'
    },
    group:{
        type: mongoose.Schema.Types.ObjectId,
        ref:'RawMaterialGroup'
    },
    item:{
        type: mongoose.Schema.Types.ObjectId,
        ref:'RawMaterialGroup',
        required: true,
    },
    woolQuality:{
        type: mongoose.Schema.Types.ObjectId,
        ref:'AddQuality'
    },
    count:{
        type: String,
        required: true,
    },
    colour:{
        type:mongoose.Schema.Types.ObjectId,
        ref:'Color',
        required: true,
    },
   partyName:{
    type:mongoose.Schema.Types.ObjectId,
    ref:"purchaserDetails",
    required: true,
   },
   lotNo:{
    type:String,
    required: true,
   },
   weight:{
    type:String,
    required: true,
   },
   rate:{
    type:String,
    required: true,
   },
   amount:{
    type:String,
    required: true,
   }

});
const materialDeying = mongoose.model('MaterialDeying',materialDeyingSchema);

module.exports = materialDeying;