<div class="container mt-4">
  <section>
      <fieldset>
          <legend><b> Carpet Recieved</b></legend>
          <form [formGroup]="frmReceivingCarpet" (ngSubmit)="addReceivingCarpet()">
          <div class="row">
              <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline"  >
                      <mat-label>Weaver Name</mat-label>
                      <mat-select #weaverSelect formControlName="weaver" (selectionChange)="onWeaverSelectionChange($event)">

                         @for(weaverName of list; track weaverName){
                          <mat-option [value]="weaverName.id">{{weaverName.branch}} - {{weaverName.weaver}}</mat-option>
                         }
                      </mat-select>
                  </mat-form-field>
              </div>
              <div class="mb-2 issueNo">
                <mat-form-field appearance="outline" class="ex-width">
                  <mat-label>Issue No.</mat-label>
                  <mat-select #issueSelect formControlName="issueNo" (selectionChange)="onIssueSelectionChange($event)">
                    <mat-option *ngFor="let issueData of getAvailableIssues()" [value]="issueData._id">
                      {{ issueData.Br_issueNo }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="mb-2 col-md-2">
                 <mat-form-field appearance="outline"  class="ex-width">
                 <mat-label>Date</mat-label>
                 <input matInput [matDatepicker]="picker" formControlName="date" #dateInput (keydown.enter)="onDateEnter()">
                 <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                 <mat-datepicker #picker></mat-datepicker>
               </mat-form-field>
              </div>
              
              <!-- <div class="mb-2 col-md-2">
                <div style="border: 1px solid #4e4b4b; padding: 8px 12px; border-radius: 4px;">
                  <mat-radio-group color="primary" aria-label="Select Yes or No" style="display: flex; gap: 12px; align-items: center;">
                    <mat-radio-button value="1">Yes</mat-radio-button>
                    <mat-radio-button value="2">No</mat-radio-button>
                  </mat-radio-group>
                </div>
              </div> -->

              <!-- <div class="mb-2 weight-w">
                  <mat-form-field appearance="outline" class="ex-width ">
                      <mat-label>Weight</mat-label>
                      <input matInput placeholder=".000" formControlName="weight" (blur)="setDigit($event)">
                  </mat-form-field>
              </div> -->
              <div class="mb-2 carpetNo-w">
                <mat-form-field appearance="outline" class="ex-width ">
                  <mat-label>Carpet No.</mat-label>
                  <input matInput placeholder=" Carpet No." formControlName="receiveNo">
                  <!-- <input matInput placeholder=" Carpet No." [value]="issucarpet"> -->
                </mat-form-field>
              </div>

               <div class="mt-2 col-md-2">
                  <button mat-flat-button color="primary" type="button" (click)="saveNext()" #saveNextButton (keydown.enter)="onSaveNextEnter()">Save & Next</button>
              </div>
              <div class="mt-2 col-md-1">
                <button mat-flat-button color="primary" type="button" (click)="exit()">Exit</button>
              </div>

              <!-- 1 Piece Preview Table (matches Order List style) -->
              <ng-container *ngIf="previewRow">
                <div class="mb-2 col-md-12">
                  <table class="piece-preview-table" style="margin-bottom: 0;">
                    <thead>
                      <tr>
                        <th>IssueNo</th>
                        <th>IssueDt</th>
                        <th>Weaver</th>
                        <th>Quality</th>
                        <th>Design</th>
                        <th>Colour</th>
                        <th>Size</th>
                        <th>Pcs.</th>
                        <th>Area</th>
                        <th>Rate</th>
                        <th>Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>{{previewRow.IssueNo}}</td>
                        <td>{{previewRow.IssueDt}}</td>
                        <td>{{previewRow.Weaver}}</td>
                        <td>{{previewRow.Quality}}</td>
                        <td>{{previewRow.Design}}</td>
                        <td>{{previewRow.GroundBorder}}</td>
                        <td>{{previewRow.Size}}</td>
                        <td>{{previewRow.Pcs}}</td>
                        <td>{{formatArea(previewRow.Area)}}</td>
                        <td>{{previewRow.Rate}}</td>
                        <td>{{previewRow.Amount | number:'1.0-0'}}.00</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </ng-container>
<!-- <div class="d-flex justify-content-end">
              <div class="mt-2 col-md-2">
                  <button mat-flat-button color="primary" type="button" (click)="saveNext()">Save & Next</button>
              </div>
              <div class="mt-2 col-md-1">
                <button mat-flat-button color="primary" type="button" (click)="exit()">Exit</button>
              </div>
              </div> -->
            
          </div>
         </form>
      </fieldset>
  </section>
</div>
<div class="container mt-5">
  <section>
      <fieldset>
          <legend><b>Order List</b></legend>
          <div class="row">
            <div class="col-md-3">
              <mat-form-field appearance="outline">
              <mat-label>Search</mat-label>
              <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Jack" #input>
            </mat-form-field>
            </div>
              <div class="col-12">
                    <div class="mat-elevation-z8 table-responsive scroll-container">
    <table mat-table [dataSource]="dataSource" matSort style="width: max-content;">
                    <!-- <div class="mat-elevation-z8" style="overflow: auto;">
                      <table mat-table [dataSource]="dataSource" matSort> -->

                        <!-- SrNo Column (without sorting) -->                        <ng-container matColumnDef="SrNo">
                          <th mat-header-cell *matHeaderCellDef>Sr.No</th>
                          <td mat-cell *matCellDef="let row"> {{row.SrNo}} </td>
                        </ng-container>

                        <!-- CarpetNo Column (with sorting) -->
                        <ng-container matColumnDef="CarpetNo">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header="CarpetNo" disableClear>CarpetNo</th>
                          <td mat-cell *matCellDef="let row"> {{row.CarpetNo}}</td>
                        </ng-container>

                        <!-- CDate Column -->
                        <ng-container matColumnDef="CDate">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>C Date</th>
                          <td mat-cell *matCellDef="let row"> {{row.CDate}}</td>
                        </ng-container>

                        <!-- IssueNo Column -->
                        <ng-container matColumnDef="IssueNo">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>IssueNo</th>
                          <td mat-cell *matCellDef="let row"> {{row.IssueNo}}</td>
                        </ng-container>

                        <!-- IDate Column -->
                        <ng-container matColumnDef="IDate">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>I Date</th>
                          <td mat-cell *matCellDef="let row"> {{row.IDate}}</td>
                        </ng-container>

                        <!-- Weaver Column -->
                        <ng-container matColumnDef="Weaver">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Weaver</th>
                          <td mat-cell *matCellDef="let row"> {{row.Weaver}}</td>
                        </ng-container>

                        <!-- Quality Column -->
                        <ng-container matColumnDef="Quality">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
                          <td mat-cell *matCellDef="let row"> {{row.Quality}}</td>
                        </ng-container>

                        <!-- Design Column -->
                        <ng-container matColumnDef="Design">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Design</th>
                          <td mat-cell *matCellDef="let row"> {{row.Design}} </td>
                        </ng-container>

                        <!-- Border Column -->
                        <ng-container matColumnDef="Border">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Border</th>
                          <td mat-cell *matCellDef="let row"> {{row.Border}} </td>
                        </ng-container>

                        <!-- Size Column -->
                        <ng-container matColumnDef="Size">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Size</th>
                          <td mat-cell *matCellDef="let row"> {{row.Size}} </td>
                        </ng-container>

                        <!-- Pcs Column -->
                        <ng-container matColumnDef="Pcs">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Pcs.</th>
                          <td mat-cell *matCellDef="let row"> {{row.Pcs}} </td>
                        </ng-container>

                        <!-- Area Column -->
                        <ng-container matColumnDef="Area">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Area</th>
                          <td mat-cell *matCellDef="let row"> {{formatArea(row.Area)}}</td>
                        </ng-container>

                        <!-- Rate Column -->
                        <ng-container matColumnDef="Rate">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Rate</th>
                          <td mat-cell *matCellDef="let row"> {{row.Rate | number:'1.2-3'}} </td>
                        </ng-container>

                        <!-- Amount Column -->
                        <ng-container matColumnDef="Amount">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Amount</th>
                          <td mat-cell *matCellDef="let row"> {{row.Amount | number:'1.0-0'}}.00 </td>
                        </ng-container>

                        <!-- Actions Column -->
                        <ng-container matColumnDef="Actions">
                          <th mat-header-cell *matHeaderCellDef>Actions</th>
                          <td mat-cell *matCellDef="let row">
                            <button mat-icon-button color="warn" (click)="deleteCarpetReceived(row)"
                                    matTooltip="Delete Carpet">
                              <mat-icon>delete</mat-icon>
                            </button>
                          </td>
                        </ng-container>

                        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                        <!-- Row shown when there is no matching data. -->
                        <tr class="mat-row" *matNoDataRow>
                          <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>
                        </tr>
                      </table>

              </div>


          </div>

          </div>
          


      </fieldset>
  </section>
</div>
