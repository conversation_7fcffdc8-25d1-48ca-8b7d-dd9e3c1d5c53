const winston = require("winston");
const mongoose = require("mongoose");
const { createActivityLog } = require("../model/phase-1/activitylog"); // Adjust path as necessary

const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: "activity.log" }),
  ],
});

const stringifyJSON = (data) => {
  return JSON.stringify(data, null, 4); // Changed indentation to 4 spaces for better readability
};

const getModel = (url) => {
  try {
    const urlParts = url.split("/");
    const modelName = urlParts[4]; // Assuming the model name is at index 4
    const model = mongoose.models[modelName];
    if (!model) {
      throw new Error(`Model not found: ${modelName}`);
    }
    return model;
  } catch (error) {
    console.error("Error retrieving model:", error);
    return null;
  }
};

const logRequestResponse = (req, res, next) => {
  const start = Date.now();

  res.on("finish", () => {
    const duration = Date.now() - start;
    const logMessage = `${req.method} ${req.originalUrl} ${res.statusCode} ${duration}ms`;
    logger.info(logMessage);
  });

  next();
};

const logDataChanges = async (req, res, next) => {
  const { method, originalUrl, body } = req;
  let activityMessage = "";
  let oldData = null;
  let newData = null;

  try {
    const urlParts = originalUrl.split("/");
    const idIndex = urlParts.findIndex(part => mongoose.Types.ObjectId.isValid(part));
    const id = idIndex !== -1 ? urlParts[idIndex] : null;
    const itemId = idIndex !== -1 && urlParts.length > idIndex + 1 ? urlParts[idIndex + 1] : null;

    if ((method === "PUT" || method === "DELETE") && !id) {
      throw new Error("ID not found in request parameters or URL");
    }

    switch (method) {
      case "POST":
        activityMessage = `New bill created successfully: ${stringifyJSON(body)}`;
        newData = body; // Capture the new data being created
        break;

      case "PUT":
        const updateModel = getModel(originalUrl);
        if (!updateModel) {
          throw new Error(`Unable to retrieve model for URL: ${originalUrl}`);
        }

        oldData = await updateModel.findById(id);
        if (!oldData) {
          throw new Error(`Previous data not found for ID: ${id}`);
        }

        if (itemId) {
          // Find the index of the item within the array
          const itemIndex = oldData.carpetList.findIndex(
            (item) => item._id.toString() === itemId
          );
          if (itemIndex === -1) {
            throw new Error(`Item not found for itemId: ${itemId}`);
          }

          // Update the specific item
          const oldItemData = oldData.carpetList[itemIndex];
          oldData.carpetList[itemIndex] = {
            ...oldData.carpetList[itemIndex],
            ...body,
          };
          newData = await oldData.save();

          activityMessage = `Item updated successfully:
            Old Item Data: ${stringifyJSON(oldItemData)}
            Updated Item Data: ${stringifyJSON(oldData.carpetList[itemIndex])}`;
        } else {
          // If itemId is not provided, update the entire document
          oldData = oldData.toObject();
          newData = await updateModel.findByIdAndUpdate(id, body, { new: true });

          activityMessage = `Data updated successfully:
            Old Data: ${stringifyJSON(oldData)}
            Updated Data: ${stringifyJSON(newData)}`;
        }

        break;

        case "DELETE":
        oldData = await model.findById(id);
        if (!oldData) {
          throw new Error(`Previous data not found for ID: ${id}`);
        }

        if (itemId) {
          // If itemId is provided, find and delete the specific item within a subdocument array
          const itemIndex = oldData.carpetList.findIndex(
            (item) => item._id.toString() === itemId
          );
          if (itemIndex === -1) {
            throw new Error(`Item not found for itemId: ${itemId}`);
          }

          const [deletedItem] = oldData.carpetList.splice(itemIndex, 1);
          newData = await oldData.save();
          activityMessage = `Item deleted successfully:
            Deleted Item Data: ${stringifyJSON(deletedItem)}`;
        } else {
          // If itemId is not provided, delete the entire document
          await model.findByIdAndDelete(id);
          newData = oldData; // Capture the state before deletion
          activityMessage = `Data deleted successfully:
            Deleted Data: ${stringifyJSON(oldData)}`;
        }
        break;

      case "GET":
        activityMessage = `Data retrieved successfully`;
        // You may want to capture the retrieved data if applicable
        break;

      default:
        activityMessage = `Unknown operation`;
    }

    logger.info(activityMessage);

    // Save log to database
    try {
      await createActivityLog({ message: activityMessage, oldData, newData });
    } catch (error) {
      console.error("Error saving activity log:", error);
    }

    next();
  } catch (error) {
    logger.error(`Error in logging data changes: ${error.message}`);
    next(error);
  }
};

module.exports = {
  logRequestResponse,
  logDataChanges,
  createActivityLog,
};
