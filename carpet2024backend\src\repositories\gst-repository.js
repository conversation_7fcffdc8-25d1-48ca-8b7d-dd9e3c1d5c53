const GST = require('../model/phase-2/gst');

async function createGST(toDate, fromDate, gstRate) {
  try {
    const newGST = new GST({ toDate, fromDate, gstRate });
    const savedGST = await newGST.save();
    return savedGST;
  } catch (error) {
    throw new Error('Could not create GST: ' + error.message);
  }
}

async function getGSTById(id) {
  try {
    const gst = await GST.findById(id);
    return gst;
  } catch (error) {
    throw new Error('Could not get GST: ' + error.message);
  }
}

async function getGST() {
    try {
      const gst = await GST.find({});
      return gst;
    } catch (error) {
      throw new Error('Could not get GST: ' + error.message);
    }
  }

async function updateGST(id, updates) {
  try {
    const updatedGST = await GST.findByIdAndUpdate(id, updates, { new: true });
    return updatedGST;
  } catch (error) {
    throw new Error('Could not update GST: ' + error.message);
  }
}

async function deleteGST(id) {
  try {
    await GST.findByIdAndDelete(id);
    return 'GST deleted successfully';
  } catch (error) {
    throw new Error('Could not delete GST: ' + error.message);
  }
}

module.exports = {
  createGST,
  getGSTById,
  updateGST,
  deleteGST,
  getGST
};
