const express = require('express');
const router = express.Router();


const phaseOne=require('./phase-1/index');
const phaseTwo=require('./phase-2/index');
const phaseThree=require('./phase-3/index');
const phasefour = require('./phase-4/index');

const invoiceRoutes = require('./phase-4/invoice-routes');
router.use('/invoices', invoiceRoutes);

router.use('/phase-one',phaseOne);

router.use('/phase-two',phaseTwo);

router.use('/phase-Three',phaseThree);

router.use('/phase-four',phasefour);

module.exports = router;

