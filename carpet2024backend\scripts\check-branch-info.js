const mongoose = require('mongoose');

const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function checkBranchInfo() {
  try {
    // Connect to MongoDB
    await mongoose.connect(DB_URL);
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    
    // Check branches collection
    const branchesCollection = db.collection('branches');
    const branches = await branchesCollection.find({}).toArray();
    
    console.log(`\n📊 Found ${branches.length} branches:`);
    branches.forEach((branch, index) => {
      console.log(`${index + 1}. ${branch.branchCode} - ${branch.branchName} (ID: ${branch._id})`);
    });
    
    // Check K- records to see how branch is set
    const carpetCollection = db.collection('carpetreceiveds');
    const kRecord = await carpetCollection.findOne({ receiveNo: { $regex: /^K-/ } });
    
    if (kRecord) {
      console.log('\n🔍 Sample K- record branch info:');
      console.log(`   K field: ${kRecord.K}`);
      console.log(`   K populated:`, kRecord.K);
    }
    
    // Check H- records
    const hRecord = await carpetCollection.findOne({ receiveNo: { $regex: /^H-/ } });
    
    if (hRecord) {
      console.log('\n🔍 Sample H- record branch info:');
      console.log(`   K field: ${hRecord.K}`);
      console.log(`   K populated:`, hRecord.K);
    }
    
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkBranchInfo();
