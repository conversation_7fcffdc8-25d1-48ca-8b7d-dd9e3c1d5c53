const AddDesign = require('../model/phase-2/design');

class AddDesignRepository {
    async createDesign(designData) {
        try {
            return await AddDesign.create(designData);
        } catch (error) {
            throw error;
        }
    }

    async getAllDesigns() {
        try {
            return await AddDesign.find().populate('border')
            .populate('ground')
            .populate('colourLagats.colour')
            .populate('productQuality').exec();
        } catch (error) {
            throw error;
        }
    }

    async getDesignById(designId) {
        try {
            // .populate('border')
            // .populate('ground')
            // .populate('colourLagats.colour')
            // .populate('productQuality').exec()
            return await AddDesign.findById(designId);
        } catch (error) {
            throw error;
        }
    }

    async updateDesign(designId, designData) {
        try {
            return await AddDesign.findByIdAndUpdate(designId, designData, { new: true });
        } catch (error) {
            throw error;
        }
    }

    async deleteDesign(designId) {
        try {
            return await AddDesign.findByIdAndDelete(designId);
        } catch (error) {
            throw error;
        }
    }
}

module.exports = AddDesignRepository;
