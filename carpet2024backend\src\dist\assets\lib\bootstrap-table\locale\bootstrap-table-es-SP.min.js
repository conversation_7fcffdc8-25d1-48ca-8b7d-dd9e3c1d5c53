/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!y.call({1:2},1);g.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:y;var m,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),P=function(t){return S?j(t):function(){return O.apply(t,arguments)}},T=P,A=T({}.toString),x=T("".slice),C=function(t){return x(A(t),8,-1)},E=C,F=P,M=function(t){if("Function"===E(t))return F(t)},R=c,D=C,I=Object,L=M("".split),N=R((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"==D(t)?L(t,""):I(t)}:I,z=function(t){return null==t},_=z,k=TypeError,H=function(t){if(_(t))throw k("Can't call method on "+t);return t},G=N,q=H,B=function(t){return G(q(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},J=W.all,K=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===J}:function(t){return"function"==typeof t},Q=K,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,$=K,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=M({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(h=(m=ct.split("."))[0]>0&&m[0]<4?1:+(m[0]+m[1])),!h&&et&&(!(m=et.match(/Edge\/(\d+)/))||m[1]>=74)&&(m=et.match(/Chrome\/(\d+)/))&&(h=+m[1]);var at=h,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=K,bt=nt,dt=Object,mt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&bt(n.prototype,dt(t))},ht=String,vt=K,St=function(t){try{return ht(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},jt=z,Pt=p,Tt=K,At=X,xt=TypeError,Ct={exports:{}},Et=i,Ft=Object.defineProperty,Mt=function(t,n){try{Ft(Et,t,{value:n,configurable:!0,writable:!0})}catch(r){Et[t]=n}return n},Rt=Mt,Dt="__core-js_shared__",It=i[Dt]||Rt(Dt,{}),Lt=It;(Ct.exports=function(t,n){return Lt[t]||(Lt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Nt=H,zt=Object,_t=function(t){return zt(Nt(t))},kt=_t,Ht=M({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return Ht(kt(t),n)},qt=M,Bt=0,Ut=Math.random(),Wt=qt(1..toString),Jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Bt+Ut,36)},Kt=i,Qt=Ct.exports,Vt=Gt,Xt=Jt,Yt=st,$t=pt,Zt=Qt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,cn=mt,an=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!At(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!At(e=Pt(r,t)))return e;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=mt,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},bn=X,dn=i.document,mn=bn(dn)&&bn(dn.createElement),hn=function(t){return mn?dn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),Sn=a,wn=p,On=g,jn=v,Pn=B,Tn=yn,An=Gt,xn=vn,Cn=Object.getOwnPropertyDescriptor;u.f=Sn?Cn:function(t,n){if(t=Pn(t),n=Tn(n),xn)try{return Cn(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var En={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mn=X,Rn=String,Dn=TypeError,In=function(t){if(Mn(t))return t;throw Dn(Rn(t)+" is not an object")},Ln=a,Nn=vn,zn=Fn,_n=In,kn=yn,Hn=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Un="configurable",Wn="writable";En.f=Ln?zn?function(t,n,r){if(_n(t),n=kn(n),_n(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(_n(t),n=kn(n),_n(r),Nn)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Hn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Jn=En,Kn=v,Qn=a?function(t,n,r){return Jn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=a,Yn=Gt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=K,er=It,or=M(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=K,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Ct.exports,gr=Jt,yr=pr("keys"),br={},dr=sr,mr=i,hr=X,vr=Qn,Sr=Gt,wr=It,Or=function(t){return yr[t]||(yr[t]=gr(t))},jr=br,Pr="Object already initialized",Tr=mr.TypeError,Ar=mr.WeakMap;if(dr||wr.state){var xr=wr.state||(wr.state=new Ar);xr.get=xr.get,xr.has=xr.has,xr.set=xr.set,ir=function(t,n){if(xr.has(t))throw Tr(Pr);return n.facade=t,xr.set(t,n),n},ur=function(t){return xr.get(t)||{}},cr=function(t){return xr.has(t)}}else{var Cr=Or("state");jr[Cr]=!0,ir=function(t,n){if(Sr(t,Cr))throw Tr(Pr);return n.facade=t,vr(t,Cr,n),n},ur=function(t){return Sr(t,Cr)?t[Cr]:{}},cr=function(t){return Sr(t,Cr)}}var Er={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=c,Mr=K,Rr=Gt,Dr=a,Ir=nr.CONFIGURABLE,Lr=ar,Nr=Er.enforce,zr=Er.get,_r=Object.defineProperty,kr=Dr&&!Fr((function(){return 8!==_r((function(){}),"length",{value:8}).length})),Hr=String(String).split("String"),Gr=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Rr(t,"name")||Ir&&t.name!==n)&&(Dr?_r(t,"name",{value:n,configurable:!0}):t.name=n),kr&&r&&Rr(r,"arity")&&t.length!==r.arity&&_r(t,"length",{value:r.arity});try{r&&Rr(r,"constructor")&&r.constructor?Dr&&_r(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Nr(t);return Rr(e,"source")||(e.source=Hr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Mr(this)&&zr(this).source||Lr(this)}),"toString");var qr=K,Br=En,Ur=Vn.exports,Wr=Mt,Jr={},Kr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Kr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=Gt,le=B,se=ae.indexOf,pe=br,ge=M([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Jr.f=Object.getOwnPropertyNames||function(t){return ye(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var me=tt,he=Jr,ve=de,Se=In,we=M([].concat),Oe=me("Reflect","ownKeys")||function(t){var n=he.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},je=Gt,Pe=Oe,Te=u,Ae=En,xe=c,Ce=K,Ee=/#|\.prototype\./,Fe=function(t,n){var r=Re[Me(t)];return r==Ie||r!=De&&(Ce(n)?xe(n):!!n)},Me=Fe.normalize=function(t){return String(t).replace(Ee,".").toLowerCase()},Re=Fe.data={},De=Fe.NATIVE="N",Ie=Fe.POLYFILL="P",Le=Fe,Ne=i,ze=u.f,_e=Qn,ke=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},He=Mt,Ge=function(t,n,r){for(var e=Pe(n),o=Ae.f,i=Te.f,u=0;u<e.length;u++){var c=e[u];je(t,c)||r&&je(r,c)||o(t,c,i(n,c))}},qe=Le,Be=C,Ue=Array.isArray||function(t){return"Array"==Be(t)},We=TypeError,Je=yn,Ke=En,Qe=v,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=K,$e=C,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=M,eo=c,oo=K,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,mo=Ue,ho=bo,vo=X,So=en("species"),wo=Array,Oo=function(t){var n;return mo(t)&&(n=t.constructor,(ho(n)&&(n===wo||mo(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=c,Po=at,To=en("species"),Ao=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Ne:f?Ne[c]||He(c,{}):(Ne[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=ze(r,e))&&u.value:r[e],!qe(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&_e(i,"sham",!0),ke(r,e,i,t)}},xo=c,Co=Ue,Eo=X,Fo=_t,Mo=ee,Ro=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Do=function(t,n,r){var e=Je(n);e in t?Ke.f(t,e,Qe(0,r)):t[e]=r},Io=function(t,n){return new(Oo(t))(0===n?0:n)},Lo=function(t){return Po>=51||!jo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},No=at,zo=en("isConcatSpreadable"),_o=No>=51||!xo((function(){var t=[];return t[zo]=!1,t.concat()[0]!==t})),ko=Lo("concat"),Ho=function(t){if(!Eo(t))return!1;var n=t[zo];return void 0!==n?!!n:Co(t)};Ao({target:"Array",proto:!0,arity:1,forced:!_o||!ko},{concat:function(t){var n,r,e,o,i,u=Fo(this),c=Io(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(Ho(i=-1===n?u:arguments[n]))for(o=Mo(i),Ro(a+o),r=0;r<o;r++,a++)r in i&&Do(c,a,i[r]);else Ro(a+1),Do(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["es-SP"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Cargando, por favor espera"},formatRecordsPerPage:function(t){return"".concat(t," registros por p&#225;gina.")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"".concat(t," - ").concat(n," de ").concat(r," registros (filtered from ").concat(e," total rows)"):"".concat(t," - ").concat(n," de ").concat(r," registros.")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se han encontrado registros."},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Actualizar"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Todo"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Ocultar/Mostrar controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Mostrar controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["es-SP"])}));
