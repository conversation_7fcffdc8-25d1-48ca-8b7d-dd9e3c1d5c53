<h2 mat-dialog-title class="title mt-2">Carpet No. {{data.CarpetNo}}</h2>
<mat-dialog-content>
  <form>    <div class="row">
      <div class="col-md-4">
        <mat-form-field appearance="outline">
          <mat-label>Quality</mat-label>
          <input matInput [value]="data.Quality" readonly tabindex="-1">
        </mat-form-field>
      </div>
      <div class="col-md-4">
        <mat-form-field appearance="outline">
          <mat-label>Design</mat-label>
          <input matInput [value]="data.Design" readonly tabindex="-1">
        </mat-form-field>
      </div>
      <div class="col-md-4">
        <mat-form-field appearance="outline">
          <mat-label>Colour</mat-label>
          <input matInput [value]="data.BorderColour" readonly tabindex="-1">
        </mat-form-field>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Size</mat-label>
          <input
            matInput
            [(ngModel)]="data.Size"
            (input)="calculateArea()"
            (blur)="calculateArea()"
            (keydown.enter)="onSizeEnter()"
            name="size"
            type="text"
            required
            #sizeInput
          />
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Area</mat-label>
          <input matInput [value]="data.Area" readonly tabindex="-1">
        </mat-form-field>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Rate</mat-label>
          <input matInput [value]="data.Rate | number:'1.2-2'" readonly tabindex="-1">
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Deduction</mat-label>          <input
            matInput
            [(ngModel)]="data.Deduction"
            name="deduction"
            type="number"
            step="0.01"
            (input)="calculateAmounts()"
            (blur)="onDeductionBlur()"
            (keydown.enter)="onDeductionEnter()"
            #deductionInput
            placeholder=".00"
          />
        </mat-form-field>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Amount</mat-label>
          <input matInput [value]="data.Amount" readonly tabindex="-1">
        </mat-form-field>
      </div>
      <div class="col-md-3">
        <mat-form-field appearance="outline">
          <mat-label>TDS</mat-label>
          <input matInput [value]="data.TDS" readonly tabindex="-1">
        </mat-form-field>
      </div>
      <div class="col-md-3">
        <mat-form-field appearance="outline">
          <mat-label>Commission</mat-label>
          <input matInput [value]="data.Commission" readonly tabindex="-1">
        </mat-form-field>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <mat-form-field appearance="outline">
          <mat-label>Net Amount</mat-label>
          <input matInput [value]="data.NetAmount" readonly tabindex="-1">
        </mat-form-field>
      </div>
    </div>
  </form>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">Cancel</button>
  <button mat-raised-button color="primary" (click)="onSave()" (keydown.enter)="onSaveEnter()" [disabled]="!data.Size" #saveButton tabindex="0">Save</button>
</mat-dialog-actions>