import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import Swal from 'sweetalert2';
import { Global } from '../../../Shared/validators';

@Component({
  selector: 'app-buyer-master',
  templateUrl: './buyer-master.component.html',
  styleUrl: './buyer-master.component.css',
})
export class BuyerMasterComponent implements OnInit {
  buyerForm: FormGroup;
  data: any;
  editId: any;
  isEditMode: boolean = false;
  constructor(public _fb: FormBuilder, public masterservice: MasterService) {
    this.buyerForm = this._fb.group({
      customerCode: ['',[Validators.required]],
      customerName: ['' ,[Validators.required,Validators.pattern(Global.nameRegex)]],
      customerNo: [''],
      customerAddress: ['',[Validators.required]],
      country: ['',[Validators.required]],
      zipCode: [],
      customerEmail: ['',[Validators.required,Validators.pattern(Global.emailRegex)]],
      numberOfStores: [''],
      bankName: [''],
      branch: [''],
      bankAddress: [''],
      bankCountry: [''],
      bankEmail: [''],
      swiftCode: [''],
      bankZipCode: [''],
      bankContactNo: [''],
    });
  }
  ngOnInit(): void {
    this.getAllBuyerList();
  }
  country: any = ['India', 'Germany', 'USA', 'Belgium'];
  bankCountry: any = ['India', 'Germany', 'USA', 'Belgium'];

  buyerFormSubmit() {
    // console.log(this.buyerForm.value)
    if (this.buyerForm.valid) {
    
      let frmData = this.buyerForm.value;
     
      let bankDetails = {
        bankName: frmData.bankName,
        bankAddress: frmData.bankAddress,
        bankContactNo: frmData.bankContactNo,
        bankCountry: frmData.bankCountry,
        bankEmail: frmData.bankEmail,
        branch: frmData.branch,
        swiftCode: frmData.swiftCode,
        bankZipCode: frmData.bankZipCode,
      };
      frmData['bankDetails'] = bankDetails;

      if (this.isEditMode && this.editId) {
        // let frmData = this.buyerForm.value;
        this.masterservice.updateBuyer(this.editId, frmData).subscribe({
          next: () => {
            Swal.fire({
              title: 'Success',
              text: 'Successfully Updated !',
              icon: 'success',
            });
            this.getAllBuyerList();
            this.resetForm();
          },
          error: () => {
            Swal.fire({
              icon: 'error',
              title: 'Failed !',
              text: 'Something went wrong !',
            });
          },
        });
      } else {
        this.masterservice.addBuyer(frmData).subscribe({
          next: () => {
            Swal.fire({
              title: 'Success',
              text: 'Successfully Added !',
              icon: 'success',
            });
            this.getAllBuyerList();
            this.resetForm();
          },
          error: () => {
            Swal.fire({
              title: 'Failed !',
              text: 'Something went wrong ?',
              icon: 'error',
            });
          },
        });
      }
    }
  }

  getAllBuyerList() {
    this.masterservice.getAllBuyerList().subscribe({
      next: (res) => {
        debugger;
        this.dataSource = new MatTableDataSource(res);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        console.log(this.dataSource.filteredData);
      },
      error: console.log,
    });
  }
  deleteConformation(id: any) {
    Swal.fire({
      icon: 'warning',
      text: 'Do you want to Delete this Data ?',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        this.deleteBuyer(id);
      } else if (result.isDenied) {
        Swal.fire('Changes are not save', '', 'info');
      }
    });
  }
  deleteBuyer(id: any) {
    this.masterservice.deleteBuyer(id).subscribe({
      next: (res) => {
        Swal.fire('Deleted !', 'Your data has been deleted.', 'success');
        this.getAllBuyerList();
      },
      error: () => {
        // alert('Error !');
        Swal.fire({
          title: 'Failed !',
          text: 'Something went wrong ?',
          icon: 'error',
        });
      },
    });
  }

  editBuyer(data: any) {
    this.isEditMode = true;
    this.editId = data._id;

    debugger;

    this.buyerForm.patchValue({
      customerCode: data.customerCode,
      customerName: data.customerName,
      customerNo: data.customerNo,
      customerAddress: data.customerAddress,
      country: data.country,
      zipCode: data.zipCode,
      customerEmail: data.customerEmail,
      numberOfStores: data.numberOfStores,
      bankAddress: data.bankDetails.bankAddress,
      bankCountry: data.bankDetails.bankCountry,
      bankEmail: data.bankDetails.bankEmail,
      bankName: data.bankDetails.bankName,
      branch: data.bankDetails.branch,
      swiftCode: data.bankDetails.swiftCode,
    });
  }
  resetForm() {
    this.buyerForm.reset();
    this.isEditMode = false;
  }
  getSerialNumber(index: number): number {
    if (!this.paginator) {
      return index + 1;
    }
    return index + 1 + this.paginator.pageIndex * this.paginator.pageSize;
  }
  displayedColumns: string[] = [
    'id',
    'customerCode',
    'customerName',
    'customerNo',
    'customerAddress',
    'country',
    'zipCode',
    'customerEmail',
    'numberOfStores',
    'bankName',
    'branch',
    'bankAddress',
    'bankCountry',
    'bankEmail',
    'swiftCode',
    'action',
  ];
  dataSource = new MatTableDataSource<any>();

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
}
