// Final script to fix weaver names to show only K-format
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function updateWeaverNamesToKFormat() {
  console.log('🔄 UPDATING WEAVER NAMES TO K-FORMAT');
  console.log('='.repeat(60));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get all records
    const allRecords = await collection.find({}).toArray();
    console.log(`📊 Found ${allRecords.length} records to update`);
    
    const results = { success: [], errors: [] };
    
    // Update each record's weaverName to K-format
    for (let i = 0; i < allRecords.length; i++) {
      try {
        const record = allRecords[i];
        let currentWeaverName = record.weaverName || '';
        
        // If weaver name doesn't start with K-, add K- prefix
        let newWeaverName = currentWeaverName;
        if (currentWeaverName && !currentWeaverName.startsWith('K-') && !currentWeaverName.startsWith('K ')) {
          newWeaverName = `K-${currentWeaverName}`;
        }
        
        // Update the record
        await collection.updateOne(
          { _id: record._id },
          { 
            $set: { 
              weaverName: newWeaverName 
            } 
          }
        );
        
        results.success.push({
          receiveNo: record.receiveNo,
          oldWeaver: currentWeaverName,
          newWeaver: newWeaverName
        });
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Updated ${i + 1}/${allRecords.length} records`);
        }
        
      } catch (error) {
        console.error(`❌ Error updating record ${i + 1}:`, error.message);
        results.errors.push({
          receiveNo: allRecords[i]?.receiveNo || `Record ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error updating weaver names:', error);
    return { success: [], errors: [] };
  }
}

async function updateWeaverEmployeeNames() {
  console.log('\n🔄 UPDATING WEAVER EMPLOYEE NAMES TO K-FORMAT');
  console.log('='.repeat(60));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('weaver_employees');
    
    // Get all weaver employees
    const allWeavers = await collection.find({}).toArray();
    console.log(`📊 Found ${allWeavers.length} weaver employees to update`);
    
    const results = { success: [], errors: [] };
    
    // Update each weaver's name to K-format
    for (let i = 0; i < allWeavers.length; i++) {
      try {
        const weaver = allWeavers[i];
        let currentName = weaver.name || '';
        
        // If name doesn't start with K-, add K- prefix
        let newName = currentName;
        if (currentName && !currentName.startsWith('K-') && !currentName.startsWith('K ')) {
          newName = `K-${currentName}`;
        }
        
        // Update the weaver
        await collection.updateOne(
          { _id: weaver._id },
          { 
            $set: { 
              name: newName 
            } 
          }
        );
        
        results.success.push({
          id: weaver._id,
          oldName: currentName,
          newName: newName
        });
        
      } catch (error) {
        console.error(`❌ Error updating weaver ${i + 1}:`, error.message);
        results.errors.push({
          id: allWeavers[i]?._id || `Weaver ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error updating weaver employee names:', error);
    return { success: [], errors: [] };
  }
}

async function verifyKFormatNames() {
  console.log('\n🔍 Verifying K-format names...');
  
  try {
    const db = mongoose.connection.db;
    
    // Get sample carpet received records
    const samples = await db.collection('carpetreceiveds').find({}).limit(10).toArray();
    
    console.log('\n📋 Sample carpet received records:');
    samples.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver Name: ${record.weaverName || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ${record.amount || 'N/A'}`);
      console.log('   ' + '-'.repeat(50));
    });
    
    // Check distinct weaver names
    const distinctWeavers = await db.collection('carpetreceiveds').distinct('weaverName');
    console.log(`\n👥 All weaver names in carpet received:`);
    distinctWeavers.forEach((weaver, index) => {
      console.log(`   ${index + 1}. ${weaver}`);
    });
    
    // Check weaver employees
    const weaverEmployees = await db.collection('weaver_employees').find({}).limit(10).toArray();
    console.log(`\n👥 Sample weaver employees:`);
    weaverEmployees.forEach((weaver, index) => {
      console.log(`   ${index + 1}. ${weaver.name}`);
    });
    
  } catch (error) {
    console.error('❌ Error verifying K-format names:', error);
  }
}

async function main() {
  console.log('🔄 FINAL FIX: K-FORMAT WEAVER NAMES ONLY');
  console.log('(K-Rahila, K-Jeet Narayan, K-Yaseen, etc.)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Update carpet received weaver names
    const carpetResults = await updateWeaverNamesToKFormat();

    // Update weaver employee names
    const weaverResults = await updateWeaverEmployeeNames();

    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 K-FORMAT WEAVER NAMES UPDATE COMPLETE');
    console.log('='.repeat(60));
    console.log(`✅ Carpet Received Records Updated: ${carpetResults.success.length}`);
    console.log(`✅ Weaver Employees Updated: ${weaverResults.success.length}`);
    console.log(`❌ Total Errors: ${carpetResults.errors.length + weaverResults.errors.length}`);
    
    if (carpetResults.success.length > 0) {
      console.log('\n✅ SAMPLE UPDATED CARPET RECORDS:');
      carpetResults.success.slice(0, 10).forEach(record => {
        console.log(`  - ${record.receiveNo}: "${record.oldWeaver}" → "${record.newWeaver}"`);
      });
    }
    
    // Verify K-format names
    await verifyKFormatNames();

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 K-FORMAT WEAVER NAMES FINALIZED!');
    console.log('✅ All weaver names now in K-format');
    console.log('✅ Frontend will show: K-Rahila, K-Jeet Narayan, K-Yaseen, etc.');
    console.log('✅ No MB001 prefix, only K-format names!');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
