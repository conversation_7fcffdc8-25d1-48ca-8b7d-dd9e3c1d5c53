const AddContainerDespatchRepository = require('../repositories/containerD-repository');

class AddContainerDespatchService {
  async createContainerDespatch(containerDespatchData) {
    try {
      return await AddContainerDespatchRepository.createContainerDespatch(containerDespatchData);
    } catch (error) {
      throw error;
    }
  }

  async getContainerDespatchById(containerDespatchId) {
    try {
      return await AddContainerDespatchRepository.getContainerDespatchById(containerDespatchId);
    } catch (error) {
      throw error;
    }
  }

  async getAllContainerDespatches() {
    try {
      return await AddContainerDespatchRepository.getAllContainerDespatches();
    } catch (error) {
      throw error;
    }
  }

  async updateContainerDespatch(containerDespatchId, containerDespatchData) {
    try {
      return await AddContainerDespatchRepository.updateContainerDespatch(containerDespatchId, containerDespatchData);
    } catch (error) {
      throw error;
    }
  }

  async deleteContainerDespatch(containerDespatchId) {
    try {
      return await AddContainerDespatchRepository.deleteContainerDespatch(containerDespatchId);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new AddContainerDespatchService();
