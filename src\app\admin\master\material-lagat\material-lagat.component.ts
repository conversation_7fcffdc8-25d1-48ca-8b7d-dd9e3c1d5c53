import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { DomSanitizer } from '@angular/platform-browser';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { SweetalertService } from '../../../services/sweetalert.service';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MasterService } from '../../../services/master.service';
import { MatTableDataSource } from '@angular/material/table';
export interface MaterialLagat {
  id: string;
  index: number;
  quality: string;
  addDesign: string;
  color: string;
  todate: string;
  fromdate: string;
}

const ELEMENT_DATA: MaterialLagat[] = [];
@Component({
  selector: 'app-material-lagat',
  templateUrl: './material-lagat.component.html',
  styleUrl: './material-lagat.component.css',
})
export class MaterialLagatComponent implements OnInit {
  // saif, diwakar gaurav vishal

  displayedColumns: string[] = [
    'id',
    'todate',
    'fromdate',
    'quality',
    'addDesign',
    'color',
    'action',
  ];
  dataSource = new MatTableDataSource<MaterialLagat>(ELEMENT_DATA);
  constructor(
    private service: MasterService,
    private alert: SweetalertService,
    private fb: FormBuilder,
    private customeService: CustomeServiceService,
    private sanitizer: DomSanitizer
  ) {}
  qualityList: any = [];
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  color!: string;

  design!: string;
  editId!: string;
  isUpdated: boolean = false;

  newControlLabel!: string;
  newControlName!: string;
  viewLagat:any;
  controlsArray: {
    id: number;
    label: string;
    controlNames: string[];
    types: string[];

  }[] = [];
  private controlIdCounter: number = 0;
  frmMaterialLagat!: FormGroup;
  ngOnInit(): void {
    this.frmMaterialLagat = this.fb.group({
      todate: [''],
      fromdate: [''],
      quality: [''],
      AddDesign: [''],
      groundColour: [''],
      borderColour: [''],
      tanaLagat: [],
      tanaDescription: [],
      tanaRatePerkg: [],
      sootLagat: [],
      sootDescription: [],
      sootRatePerkg: [],
      tharriLagat: [],
      tharriDescription: [],
      tharriRatePerkg: [],
      silkLagat: [],
      silkDescription: [],
      silkRatePerkg: [],
      katiLagat: [],
      katiDescription: [],
      katiRatePerkg: [],
      item: this.fb.array([]), // Initialize FormArray
    });
    this.qualitys();
    this.descList();
    this.gets();
  }
  get item(): FormArray {
    return this.frmMaterialLagat.get('item') as FormArray;
  }
  addItem(): void {
    this.item.push(
      this.fb.group({
        lagat: [''],
        description: [''],
        ratePerkg: [''],
      })
    );
  }

  removeItem(index: number): void {
    this.item.removeAt(index);
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }


   setDigitKatiRate(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.frmMaterialLagat.get('katiRatePerkg')?.patchValue(_val.toFixed(2)); 
    }
  }
   setDigitTanaRate(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.frmMaterialLagat.get('tanaRatePerkg')?.patchValue(_val.toFixed(2));
    }
  }
   setDigitSootRate(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.frmMaterialLagat.get('sootRatePerkg')?.patchValue(_val.toFixed(2)); 
    }
  }
   setDigitTharriRate(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.frmMaterialLagat.get('tharriRatePerkg')?.patchValue(_val.toFixed(2));
    }
  }
   setDigitSilkRate(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.frmMaterialLagat.get('silkRatePerkg')?.patchValue(_val.toFixed(2)); 
    }
  }
   setDigitOtherRate(val: any, index: number) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      const controls = this.item.at(index).get('ratePerkg');
      controls?.patchValue(_val.toFixed(2));

    }
  }

  // Lagat fields - 3 decimal places (.000)
  setDigitKatiLagat(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.frmMaterialLagat.get('katiLagat')?.patchValue(_val.toFixed(3));
    }
  }
  setDigitTanaLagat(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.frmMaterialLagat.get('tanaLagat')?.patchValue(_val.toFixed(3));
    }
  }
  setDigitSootLagat(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.frmMaterialLagat.get('sootLagat')?.patchValue(_val.toFixed(3));
    }
  }
  setDigitTharriLagat(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.frmMaterialLagat.get('tharriLagat')?.patchValue(_val.toFixed(3));
    }
  }
  setDigitSilkLagat(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      this.frmMaterialLagat.get('silkLagat')?.patchValue(_val.toFixed(3));
    }
  }

  // Method for Others lagat field in FormArray
  setDigitOtherLagat(val: any, index: number) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      const control = this.item.at(index).get('lagat');
      control?.patchValue(_val.toFixed(3));
    }
  }

  _qualitys: any = [];
  qualitys() {
    this.service.getqualityList().subscribe({
      next: (value: any) => {
        this._qualitys = value;
        value.map((v: any) => {
          this.qualityList.push({
            quality: v.quality,
            id: v._id,
          });
        });
      },
      error: (err) => {},
    });
  }
  qty: any;
  onQuality(data: any) {
    debugger;

    let data2 = this.qualityList.find((x: any) => x.id === data);
    this.qty = data2.id;

    this.service.getAllDesignList().subscribe({
      next: (value: any) => {
        debugger;
        this.designList = value.filter(
          (x: any) => x.productQuality?.quality === data2.quality
        );
        console.log(value);
      },
    });
  }
  designList: any = [];

  onDesign(d: any) {
    debugger;
    this.design = '';
    this.design = d;
    let gb = this.designList.find((x: any) => x._id === d);
    this.frmMaterialLagat.get('borderColour')?.setValue(gb.border?.newColor);
    this.frmMaterialLagat.get('groundColour')?.setValue(gb.ground?.newColor);
  }
  descriptionList: any = [];
  katiDescriptionList: any = [];
  tanaDescriptionList: any = [];
  sootDescriptionList: any = [];
  tharriDescriptionList: any = [];
  silkDescriptionList: any = [];
  othersDescriptionList: any = []; // Add Others list

  descList() {
    this.service.getsRawMaterial().subscribe({
      next: (value: any) => {
        // Clear all lists first
        this.descriptionList = [];
        this.katiDescriptionList = [];
        this.tanaDescriptionList = [];
        this.sootDescriptionList = [];
        this.tharriDescriptionList = [];
        this.silkDescriptionList = [];
        this.othersDescriptionList = []; // Clear Others list

        value.map((x: any, i: number) => {
          const item = {
            id: x._id,
            description: x.Description,
            group: x.Group
          };

          // Add to general list
          this.descriptionList.push(item);

          // Filter by Group and add to specific lists
          if (x.Group === 'Kati') {
            this.katiDescriptionList.push(item);
          } else if (x.Group === 'Tana') {
            this.tanaDescriptionList.push(item);
          } else if (x.Group === 'Soot') {
            this.sootDescriptionList.push(item);
          } else if (x.Group === 'Tharri') {
            this.tharriDescriptionList.push(item);
          } else if (x.Group === 'Silk') {
            this.silkDescriptionList.push(item);
          } else if (x.Group === 'Others') {
            this.othersDescriptionList.push(item); // Add Others items
          }
        });

        console.log('🔍 Filtered Description Lists:', {
          kati: this.katiDescriptionList,
          tana: this.tanaDescriptionList,
          soot: this.sootDescriptionList,
          tharri: this.tharriDescriptionList,
          silk: this.silkDescriptionList,
          others: this.othersDescriptionList
        });
      },
    });
  }
lagatList:any=[];
  gets() {
    this.service.getsMaterialLagat().subscribe({
      next: (value: any) => {
        debugger;
        this.lagatList=value;
        console.log(value);
        ELEMENT_DATA.length=0;
        value.map((v: any, i: number) => {
          console.log('🔍 Material Lagat Item:', v);
          console.log('🎨 AddDesign:', v.AddDesign);
          console.log('🟢 Ground Color:', v.AddDesign?.ground);
          console.log('🔵 Border Color:', v.AddDesign?.border);

          ELEMENT_DATA.push({
            id: v._id,
            index: i + 1,
            todate: this.customeService.convertDate(v.todate),
            fromdate: this.customeService.convertDate(v.fromdate),
            quality: v.quality?.quality || 'Undefined',
            addDesign: v.AddDesign?.design || 'Undefined',
            color: (v.AddDesign?.ground?.newColor || 'Undefined') + ' / ' + (v.AddDesign?.border?.newColor || 'Undefined'),
          });
        });

        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.ngAfterViewInit();
        debugger;
        return;
      },
    });
  }
  addLagat() {
    let frmArray = this.frmMaterialLagat.value;
    this.service.addMaterialLagat(frmArray).subscribe({
      next: (value) => {
        if (value) {
          this.alert.success('success', 'Material lagat has been added');
          this.gets();
          this.clear();
        }
      },
      error: (err) => {
        this.alert.error('warning', 'Something went wrong');
      },
    });
  }
  update() {
    let frmArray = this.frmMaterialLagat.value;
    this.service.updateMaterialLagat(this.editId,frmArray).subscribe({
      next: (value) => {
        if (value) {
          this.alert.success('success', 'Material lagat has been updated');
          this.gets();
          this.clear();
        }
      },
      error: (err) => {
        this.alert.error('warning', 'Something went wrong');
      },
    });
  }
  edit(id: string) {
    debugger
    this.service.getMaterialLagat(id).subscribe({
      next: (value:any) => {
        this.editId = id;
        this.isUpdated = true;
        debugger
        this.onQuality(value.quality?._id || value.quality);
        setTimeout(()=>{
          this.onDesign(value.AddDesign?._id || value.AddDesign);
        // Patch form with proper IDs for populated fields
        const formData = {
          ...value,
          quality: value.quality?._id || value.quality,
          AddDesign: value.AddDesign?._id || value.AddDesign,
          Color: value.Color?._id || value.Color,
          katiDescription: value.katiDescription?._id || value.katiDescription,
          tanaDescription: value.tanaDescription?._id || value.tanaDescription,
          sootDescription: value.sootDescription?._id || value.sootDescription,
          tharriDescription: value.tharriDescription?._id || value.tharriDescription,
          silkDescription: value.silkDescription?._id || value.silkDescription,
          // Format rate values to 2 decimal places (.00)
          katiRatePerkg: value.katiRatePerkg ? parseFloat(value.katiRatePerkg).toFixed(2) : '0.00',
          tanaRatePerkg: value.tanaRatePerkg ? parseFloat(value.tanaRatePerkg).toFixed(2) : '0.00',
          sootRatePerkg: value.sootRatePerkg ? parseFloat(value.sootRatePerkg).toFixed(2) : '0.00',
          tharriRatePerkg: value.tharriRatePerkg ? parseFloat(value.tharriRatePerkg).toFixed(2) : '0.00',
          silkRatePerkg: value.silkRatePerkg ? parseFloat(value.silkRatePerkg).toFixed(2) : '0.00',
          // Format lagat values to 3 decimal places (.000)
          katiLagat: value.katiLagat ? parseFloat(value.katiLagat).toFixed(3) : '0.000',
          tanaLagat: value.tanaLagat ? parseFloat(value.tanaLagat).toFixed(3) : '0.000',
          sootLagat: value.sootLagat ? parseFloat(value.sootLagat).toFixed(3) : '0.000',
          tharriLagat: value.tharriLagat ? parseFloat(value.tharriLagat).toFixed(3) : '0.000',
          silkLagat: value.silkLagat ? parseFloat(value.silkLagat).toFixed(3) : '0.000',
        };
        this.frmMaterialLagat.patchValue(formData);

        this.item.clear(); // Clear the FormArray before patching
        value.item.forEach((v: any) => {
          this.item.push(this.fb.group({
            lagat: v.lagat ? parseFloat(v.lagat).toFixed(3) : '0.000', // Lagat - 3 decimal places
            description: v.description?._id || v.description,
            ratePerkg: v.ratePerkg ? parseFloat(v.ratePerkg).toFixed(2) : '0.00', // Rate - 2 decimal places
          }));
        });
        },1000)
      },
    });
  }
  delete(id: string) {
    this.alert
      .confirm({})
      .then((res: any) => {
        if (res.isConfirmed) {
          this.service.deleteMaterialLagat(id).subscribe({
            next:(value) =>{
              this.alert.success('success', 'Record has been deleted');
              this.gets();
              // this.clear();
            },error:(err) =>{
              this.alert.error('warning', 'Something went wrong');
            },
          })

        }
      })
      .catch((erro) => {
        this.alert.error('warning', 'Something went wrong');
      });
  }
  clear(){
    this.frmMaterialLagat.reset();
  }

  view(id:any){
    this.viewLagat = this.lagatList.find((x:any)=>x._id===id)
    debugger
  }
}
