const BillForWholesellerRepository = require('../repositories/billwholeseller-repository');

class BillForWholesellerService {
    constructor() {
        this.billForWholesellerRepository = new BillForWholesellerRepository();
    }
   
    async createBill(billData) {
        try {
          let challanNo=[];     // If challanNo is not an array, convert it into an array
        if (Array.isArray(billData.challanNo)) {
            billData.challanNo.map(x=>{
                challanNo.push({
                    challanNumber:x,
                    status:'sale'
                })
            })

            billData.challanNo = challanNo;
        }
            return await this.billForWholesellerRepository.create(billData);
        } catch (error) {
            throw error;
        }
    }

    async getAllBills() {
        try {
            return await this.billForWholesellerRepository.findAll();
        } catch (error) {
            throw error;
        }
    }

    async getBillById(id) {
        try {
            return await this.billForWholesellerRepository.findById(id);
        } catch (error) {
            throw error;
        }
    }

    async updateBill(id, billData) {
        try {
            return await this.billForWholesellerRepository.update(id, billData);
        } catch (error) {
            throw error;
        }
    }

    async deleteBill(id) {
        try {
            return await this.billForWholesellerRepository.delete(id);
        } catch (error) {
            throw error;
        }
    }

    async getCarpetDetails(bcr) {
        try {
            return await this.billForWholesellerRepository.getCarpetDetails(bcr);
        } catch (error) {
            throw error;
        }
    }
}

module.exports = BillForWholesellerService;
