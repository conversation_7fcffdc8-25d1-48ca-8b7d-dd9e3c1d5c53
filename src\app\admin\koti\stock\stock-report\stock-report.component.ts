import { Component, OnInit, ViewChild } from '@angular/core';
import { ImporterService } from '../../../../services/importer.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { CustomeServiceService } from '../../../../services/custome-service.service';
export interface PeriodicElement {
  index: number;
  Amount: string;
  Area: string;
  BillChooseAdate: string;
  BillCustomer: string;
  BillNo: string;
  CCode: string;
  ChallanNo: string;
  ChooseAdate: string;
  Color: string;
  ContainerDate: string;
  ContainerStatus: string;
  EvKPrice: string;
  GerCarpetNo: string;
  InvoiceNo: string;
  IsBillDeleted: string;
  MatchedBarcodeNo: string;
  MatchedStatus: string;
  QCode: string;
  QualityDesign: string;
  RetailerOutlet: string;
  SCore: string;
  Size: string;
  Wholeseller: string;
}

const ELEMENT_DATA: PeriodicElement[] = [];
@Component({
  selector: 'app-stock-report',
  templateUrl: './stock-report.component.html',
  styleUrl: './stock-report.component.css',
})
export class StockReportComponent implements OnInit {
  displayedColumns: any[] = [
    'index',
    'ContainerDate',
    'GerCarpetNo',

    'QualityDesign',
    'QCode',
    'Color',
    'CCode',
    'Size',
    'SCore',
    'Area',
    'EvKPrice',
    'Amount',
    'InvoiceNo',
    'ContainerStatus',

    'ChallanNo',
    'ChooseAdate',
    'Wholeseller',
    'RetailerOutlet',
    'MatchedBarcodeNo',
    'MatchedStatus',
    'BillNo',
    'BillChooseAdate',
    'BillCustomer',

    'IsBillDeleted',
  ];
  dataSource = new MatTableDataSource(ELEMENT_DATA);
  constructor(private _service: ImporterService, private customeData : CustomeServiceService) {}
  ngOnInit(): void {
    this.getStocks();
  }
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;
  pagination = {
    totalResults: 0,
    currentPage: 0,
    totalPages: 0,
    pageSize: 0
  };
  ngAfterViewInit() {
    debugger
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }
  getStocks() {
    this._service.getSolededStock( this.pagination.currentPage,this.pagination.pageSize).subscribe({
      next: (value: any) => {

        ELEMENT_DATA.length=0;
        value.data.map((x: any) => {
          ELEMENT_DATA.push({
            index: x.index,
            Amount: x.Amount,
            Area: x.Area,
            BillChooseAdate:this.customeData.convertDate(x.BillChooseAdate),
            BillCustomer: x.BillCustomer,
            BillNo: x.BillNo,
            CCode: x.CCode,
            ChallanNo: x.ChallanNo,
            ChooseAdate: this.customeData.convertDate(x.ChooseAdate),
            Color: x.Color,
            ContainerDate: this.customeData.convertDate(x.ContainerDate),
            ContainerStatus: x.ContainerStatus,
            EvKPrice: x.EvKPrice,
            GerCarpetNo: x.GerCarpetNo,
            InvoiceNo: x.InvoiceNo,
            IsBillDeleted: x.IsBillDeleted,
            MatchedBarcodeNo: x.MatchedBarcodeNo,
            MatchedStatus: x.MatchedStatus,
            QCode: x.QCode,
            QualityDesign: x.QualityDesign,
            RetailerOutlet: x.RetailerOutlet,
            SCore: x.SCore,
            Size: x.Size,
            Wholeseller: x.Wholeseller,
          });
        });

       this.pagination= {
          currentPage: value.pagination.currentPage,
          pageSize: value.pagination.pageSize,
          totalPages: value.pagination.totalPages,
          totalResults: value.pagination.totalResults
        }



        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.paginator.pageSize = this.pagination.pageSize;
        this.paginator.length = this.pagination.totalResults;
        this.paginator.pageIndex = this.pagination.currentPage - 1;
        // this.ngAfterViewInit();
        return;
      },
      error(err) {
        console.log(err.error.message);
      },
    });
  }
  pageChanged(event: any) {
    this.pagination.currentPage = event.pageIndex + 1;
    this.pagination.pageSize = event.pageSize;
    this.getStocks(); // Re-fetch data based on new pagination
  }
}
