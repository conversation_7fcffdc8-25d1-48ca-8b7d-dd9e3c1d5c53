fieldset {
  font-family: sans-serif;
  border: 2px solid #1F497D;
  background: #ffffff;
  border-radius: 5px;
  padding: 10px;
}

fieldset legend {
  background: #ffffff;
  color: #000000;
  padding: 5px 10px ;
  font-size: 20px;
  border-radius: 5px;
  /* box-shadow: 0 0 0 5px #ddd; */
  margin-left: 20px;
}

legend {
  float: left;
  width: auto;
  padding: 0;
  margin-top: -32px;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + .3vw);
  line-height: inherit;
}
.ex-width{
  width: 100%;
}
/* .issue{

} */
::ng-deep .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper {
  padding-right: 0 !important;
  padding-left: 6.25px !important;
}
::ng-deep .mat-mdc-icon-button.mat-mdc-button-base {
  --mdc-icon-button-state-layer-size: 37px !important;
  width: var(--mdc-icon-button-state-layer-size);
  height: var(--mdc-icon-button-state-layer-size);
  padding: 2px !important;
}
.mat-mdc-radio-button ~ .mat-mdc-radio-button {
margin-left: 16px;
}



.modern-toast-center {
box-shadow: none !important;
border-radius:13px !important;
padding: 15px 20px !important;
}



.mat-mdc-header-cell {
  background-color: #f5f5f5;
  color: #1F497D;
  font-weight: bold;
  font-size: 14px;
}

.mat-mdc-row:nth-child(even) {
  background-color: #f9f9f9;
}

.mat-mdc-row:hover {
  background-color: #f0f0f0;
}

/* Add a subtle border between rows */
.mat-mdc-row {
  border-bottom: 1px solid #e0e0e0;
}

/* Style for the paginator */
.mat-mdc-paginator {
  background-color: #f5f5f5;
}



.scroll-container{
    scroll-behavior: smooth !important;
    overflow-x: scroll !important;
    overflow-y: scroll !important;
  }
 
.scroll-container::-webkit-scrollbar {
    height: 9px !important;
    width: 9px !important;
  }
.scroll-container::-webkit-scrollbar-thumb {
    background: #808080;
  }
/* Preview table style to match Order List */
.piece-preview-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
  margin-bottom: 0;
}
.piece-preview-table th {
  background-color: #f5f5f5;
  color: #1F497D;
  font-weight: bold;
  font-size: 14px;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 8px;
}
.piece-preview-table td {
  background: #fff;
  font-size: 14px;
  color: #222;
  padding: 8px 8px;
  border-bottom: 1px solid #e0e0e0;
}
.piece-preview-table tr {
  border-bottom: 1px solid #e0e0e0;
}
.piece-preview-table tbody tr:hover {
  background-color: #f0f0f0;
}




.issueNo{
  width: 180px !important;
}

.weight-w{
  width: 180px !important;
}

.carpetNo-w{
  width: 180px !important;
}

