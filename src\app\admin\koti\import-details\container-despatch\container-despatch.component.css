.example-full-width{
    width: 100%;
   
}
.example-container mat-form-field + mat-form-field {
    margin-left: 8px;
  }
  
  .example-right-align {
    text-align: right;
  }
  
  input.example-right-align::-webkit-outer-spin-button,
  input.example-right-align::-webkit-inner-spin-button {
    display: none;
  }
  
  input.example-right-align {
    -moz-appearance: textfield;
  }
  section {
    display: table;
  }
  
  .example-label {
    display: table-cell;
    font-size: 14px;
    margin-left: 8px;
    min-width: 120px;
  }
  
  .example-button-row {
    display: table-cell;
    max-width: 600px;
  }
  
  .example-button-row .mat-mdc-button-base {
    margin: 8px 8px 8px 0;
  }
  
  .example-flex-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  
  .example-button-container {
    display: flex;
    justify-content: center;
    width: 120px;
  }
  table {
    width: 100%;
  }
  
  .mat-mdc-form-field {
    font-size: 14px;
    width: 100%;
  }
  
  td, th {
    width: 25%;
  }
  
  .fa-trash-o{
    color: red;
    }