const MaterialLagatRepository = require('../repositories/materialLagat-repository');

const createMaterialLagat = async (materialLagatData) => {
  return await MaterialLagatRepository.createMaterialLagat(materialLagatData);
};

const getAllMaterialLagats = async () => {
  return await MaterialLagatRepository.getAllMaterialLagats();
};

const getMaterialLagatById = async (id) => {
  return await MaterialLagatRepository.getMaterialLagatById(id);
};

const updateMaterialLagat = async (id, materialLagatData) => {
  return await MaterialLagatRepository.updateMaterialLagat(id, materialLagatData);
};

const deleteMaterialLagat = async (id) => {
  return await MaterialLagatRepository.deleteMaterialLagat(id);
};

const updateField = async (id, field, value) => {
  return await MaterialLagatRepository.updateField(id, field, value);
};

const deleteField = async (id, fieldPath) => {
  return await MaterialLagatRepository.deleteField(id, fieldPath);
};

module.exports = {
  createMaterialLagat,
  getAllMaterialLagats,
  getMaterialLagatById,
  updateMaterialLagat,
  deleteMaterialLagat,
  updateField,
  deleteField,
};
