const mongoose = require('mongoose');

const addDesignSchema = new mongoose.Schema({
    productQuality: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'AddQuality',
        required: true
    },
    border: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Color'
    },
    ground: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Color'
    },
    design: {
        type: String,
        required: true
    },
    uploadedFile: {
        type: String, // or Buffer if you need to store binary data
        // required: true
    },
    noOfcolour: {
        type: String,
        required: true
    },
    colourLagats: [{
        colour: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Color',
            required: true
        },
        lagat: {
            type: String,
            required: true
        }
    }]
});

const AddDesign = mongoose.model('AddDesign', addDesignSchema);

module.exports = AddDesign;
