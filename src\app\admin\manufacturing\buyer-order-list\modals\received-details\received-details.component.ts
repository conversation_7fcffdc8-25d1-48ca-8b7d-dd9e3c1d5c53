import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

interface ReceivedDetail {
  receivingDate: string | Date;
  weaverNumber?: { name?: string; weaverName?: string } | string;
  weaverName?: string;
  K?: { branchCode?: string };
  issueNo?: any;
  pcs: number;
  area: string;
  receiveNo?: string;
  quality?: any;
  design?: any;
  borderColour?: string;
  size?: any;
  [key: string]: any;
}

@Component({
  selector: 'app-received-details',
  templateUrl: './received-details.component.html',
  styleUrl: './received-details.component.css'
})
export class ReceivedDetailsComponent {
  displayedColumns: string[] = ['weaver', 'carpetNo', 'cDate', 'issueNo', 'iDate', 'quality', 'design', 'colour', 'size', 'pcs', 'area'];

  get receivedDetails() {
    return (this.data.receivedCarpets || []).map((carpet: ReceivedDetail) => ({
      ...carpet,
      carpetNo: carpet.receiveNo || 'N/A',
      cDate: carpet.receivingDate,
      issueNo: carpet.issueNo?.Br_issueNo || 'N/A',
      iDate: carpet.issueNo?.date,
      weaver: this.getWeaverDisplay(carpet),
      quality: carpet.issueNo?.quality?.quality || carpet.quality?.quality || 'N/A',
      design: carpet.issueNo?.design?.design || carpet.design?.design || 'N/A',
      colour: carpet.issueNo?.borderColour || carpet.borderColour || 'N/A',
      size: carpet.issueNo?.size?.sizeInYard || carpet.size?.sizeInYard || carpet.size || 'N/A',
      pcs: carpet.pcs || 0,
      area: carpet.area || 'N/A'
    }));
  }

  constructor(
    public dialogRef: MatDialogRef<ReceivedDetailsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { receivedCarpets: ReceivedDetail[] }
  ) {}

  private getWeaverDisplay(carpet: ReceivedDetail): string {
    const branch = carpet.K?.branchCode || '';
    let weaver = '';

    if (typeof carpet.weaverNumber === 'string') {
      weaver = carpet.weaverNumber;
    } else if (carpet.weaverNumber) {
      weaver = carpet.weaverNumber.name || carpet.weaverNumber.weaverName || '';
    }

    // If no weaver from weaverNumber, try weaverName field
    if (!weaver && carpet.weaverName) {
      weaver = carpet.weaverName;
    }

    return branch && weaver ? `${branch}-${weaver}` : (weaver || 'N/A');
  }
}
