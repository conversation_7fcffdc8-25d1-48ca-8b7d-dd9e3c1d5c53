import { Injectable } from '@angular/core';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import Swal from 'sweetalert2';


@Injectable({
  providedIn: 'root'
})
export class SweetalertService {

  constructor() { }

  success(title: string, text: string): void {
    Swal.fire({
      title: title,
      text: text,
      icon: 'success'
    });
  }

  error(title: string, text: string): void {
    Swal.fire({
      title: title,
      text: text,
      icon: 'error'
    });
  }

  toast(icon: 'success' | 'error' | 'info' | 'warning', message: string) {
    Swal.fire({
      icon: icon,
      html: `<div style="font-size: 16px;"><b>${message}</b></div>`,
      background: '#fff',
      position: 'center',
      showConfirmButton: false,
      timer: 2000,
      width: '300px',
      customClass: {
        popup: 'modern-toast-center'
      }
    });
  }
  
  
  confirm(options: {
    title?: string;
    text?: string;
    icon?: 'warning' | 'error' | 'success' | 'info' | 'question';
    confirmButtonText?: string;
    cancelButtonText?: string;
  }): Promise<any> {
    return Swal.fire({
      title: options.title || 'Are you sure?',
      text: options.text || "You won't be able to revert this!",
      icon: options.icon || 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: options.confirmButtonText || 'Yes, delete it!',
      cancelButtonText: options.cancelButtonText || 'Cancel'
    });
  }

  public generatePDF(elementId: string): void {
    const element = document.getElementById(elementId);
    if (element) {
      html2canvas(element).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'mm', 'a4');
        const imgProps = pdf.getImageProperties(imgData);
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
        pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
        pdf.save('document.pdf');
      });
    }
  }
}
