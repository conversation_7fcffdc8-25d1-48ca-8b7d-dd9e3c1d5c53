/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},m={}.propertyIsEnumerable,d=Object.getOwnPropertyDescriptor,y=d&&!m.call({1:2},1);g.f=y?function(t){var n=d(this,t);return!!n&&n.enumerable}:m;var b,v,h=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,O=w.call,j=S&&w.bind.bind(O,O),T=function(t){return S?j(t):function(){return O.apply(t,arguments)}},P=T,A=P({}.toString),x=P("".slice),E=function(t){return x(A(t),8,-1)},C=E,D=T,M=function(t){if("Function"===C(t))return D(t)},F=c,L=E,R=Object,k=M("".split),I=F((function(){return!R("z").propertyIsEnumerable(0)}))?function(t){return"String"==L(t)?k(t,""):R(t)}:R,N=function(t){return null==t},z=N,V=TypeError,G=function(t){if(z(t))throw V("Can't call method on "+t);return t},_=I,B=G,q=function(t){return _(B(t))},H="object"==typeof document&&document.all,U={all:H,IS_HTMLDDA:void 0===H&&void 0!==H},K=U.all,W=U.IS_HTMLDDA?function(t){return"function"==typeof t||t===K}:function(t){return"function"==typeof t},J=W,Q=U.all,X=U.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:J(t)||t===Q}:function(t){return"object"==typeof t?null!==t:J(t)},Y=i,Z=W,$=function(t){return Z(t)?t:void 0},tt=function(t,n){return arguments.length<2?$(Y[t]):Y[t]&&Y[t][n]},nt=M({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(v=(b=ct.split("."))[0]>0&&b[0]<4?1:+(b[0]+b[1])),!v&&et&&(!(b=et.match(/Edge\/(\d+)/))||b[1]>=74)&&(b=et.match(/Chrome\/(\d+)/))&&(v=+b[1]);var at=v,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,mt=W,dt=nt,yt=Object,bt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return mt(n)&&dt(n.prototype,yt(t))},vt=String,ht=W,St=function(t){try{return vt(t)}catch(t){return"Object"}},wt=TypeError,Ot=function(t){if(ht(t))return t;throw wt(St(t)+" is not a function")},jt=N,Tt=p,Pt=W,At=X,xt=TypeError,Et={exports:{}},Ct=i,Dt=Object.defineProperty,Mt=function(t,n){try{Dt(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Ft=Mt,Lt="__core-js_shared__",Rt=i[Lt]||Ft(Lt,{}),kt=Rt;(Et.exports=function(t,n){return kt[t]||(kt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var It=G,Nt=Object,zt=function(t){return Nt(It(t))},Vt=zt,Gt=M({}.hasOwnProperty),_t=Object.hasOwn||function(t,n){return Gt(Vt(t),n)},Bt=M,qt=0,Ht=Math.random(),Ut=Bt(1..toString),Kt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Ut(++qt+Ht,36)},Wt=i,Jt=Et.exports,Qt=_t,Xt=Kt,Yt=st,Zt=pt,$t=Jt("wks"),tn=Wt.Symbol,nn=tn&&tn.for,rn=Zt?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Qt($t,t)||!Yt&&"string"!=typeof $t[t]){var n="Symbol."+t;Yt&&Qt(tn,t)?$t[t]=tn[t]:$t[t]=Zt&&nn?nn(n):rn(n)}return $t[t]},on=p,un=X,cn=bt,an=function(t,n){var r=t[n];return jt(r)?void 0:Ot(r)},fn=function(t,n){var r,e;if("string"===n&&Pt(r=t.toString)&&!At(e=Tt(r,t)))return e;if(Pt(r=t.valueOf)&&!At(e=Tt(r,t)))return e;if("string"!==n&&Pt(r=t.toString)&&!At(e=Tt(r,t)))return e;throw xt("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=bt,mn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},dn=X,yn=i.document,bn=dn(yn)&&dn(yn.createElement),vn=function(t){return bn?yn.createElement(t):{}},hn=!a&&!c((function(){return 7!=Object.defineProperty(vn("div"),"a",{get:function(){return 7}}).a})),Sn=a,wn=p,On=g,jn=h,Tn=q,Pn=mn,An=_t,xn=hn,En=Object.getOwnPropertyDescriptor;u.f=Sn?En:function(t,n){if(t=Tn(t),n=Pn(n),xn)try{return En(t,n)}catch(t){}if(An(t,n))return jn(!wn(On.f,t,n),t[n])};var Cn={},Dn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mn=X,Fn=String,Ln=TypeError,Rn=function(t){if(Mn(t))return t;throw Ln(Fn(t)+" is not an object")},kn=a,In=hn,Nn=Dn,zn=Rn,Vn=mn,Gn=TypeError,_n=Object.defineProperty,Bn=Object.getOwnPropertyDescriptor,qn="enumerable",Hn="configurable",Un="writable";Cn.f=kn?Nn?function(t,n,r){if(zn(t),n=Vn(n),zn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Un in r&&!r.writable){var e=Bn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Hn in r?r.configurable:e.configurable,enumerable:qn in r?r.enumerable:e.enumerable,writable:!1})}return _n(t,n,r)}:_n:function(t,n,r){if(zn(t),n=Vn(n),zn(r),In)try{return _n(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Gn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Kn=Cn,Wn=h,Jn=a?function(t,n,r){return Kn.f(t,n,Wn(1,r))}:function(t,n,r){return t[n]=r,t},Qn={exports:{}},Xn=a,Yn=_t,Zn=Function.prototype,$n=Xn&&Object.getOwnPropertyDescriptor,tr=Yn(Zn,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&$n(Zn,"name").configurable)},rr=W,er=Rt,or=M(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=W,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,gr=Kt,mr=pr("keys"),dr={},yr=sr,br=i,vr=X,hr=Jn,Sr=_t,wr=Rt,Or=function(t){return mr[t]||(mr[t]=gr(t))},jr=dr,Tr="Object already initialized",Pr=br.TypeError,Ar=br.WeakMap;if(yr||wr.state){var xr=wr.state||(wr.state=new Ar);xr.get=xr.get,xr.has=xr.has,xr.set=xr.set,ir=function(t,n){if(xr.has(t))throw Pr(Tr);return n.facade=t,xr.set(t,n),n},ur=function(t){return xr.get(t)||{}},cr=function(t){return xr.has(t)}}else{var Er=Or("state");jr[Er]=!0,ir=function(t,n){if(Sr(t,Er))throw Pr(Tr);return n.facade=t,hr(t,Er,n),n},ur=function(t){return Sr(t,Er)?t[Er]:{}},cr=function(t){return Sr(t,Er)}}var Cr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!vr(n)||(r=ur(n)).type!==t)throw Pr("Incompatible receiver, "+t+" required");return r}}},Dr=c,Mr=W,Fr=_t,Lr=a,Rr=nr.CONFIGURABLE,kr=ar,Ir=Cr.enforce,Nr=Cr.get,zr=Object.defineProperty,Vr=Lr&&!Dr((function(){return 8!==zr((function(){}),"length",{value:8}).length})),Gr=String(String).split("String"),_r=Qn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Fr(t,"name")||Rr&&t.name!==n)&&(Lr?zr(t,"name",{value:n,configurable:!0}):t.name=n),Vr&&r&&Fr(r,"arity")&&t.length!==r.arity&&zr(t,"length",{value:r.arity});try{r&&Fr(r,"constructor")&&r.constructor?Lr&&zr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Ir(t);return Fr(e,"source")||(e.source=Gr.join("string"==typeof n?n:"")),t};Function.prototype.toString=_r((function(){return Mr(this)&&Nr(this).source||kr(this)}),"toString");var Br=W,qr=Cn,Hr=Qn.exports,Ur=Mt,Kr={},Wr=Math.ceil,Jr=Math.floor,Qr=Math.trunc||function(t){var n=+t;return(n>0?Jr:Wr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Qr(n)},Yr=Xr,Zr=Math.max,$r=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=q,ie=function(t,n){var r=Yr(t);return r<0?Zr(r+n,0):$r(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=_t,le=q,se=ae.indexOf,pe=dr,ge=M([].push),me=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},de=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Kr.f=Object.getOwnPropertyNames||function(t){return me(t,de)};var ye={};ye.f=Object.getOwnPropertySymbols;var be=tt,ve=Kr,he=ye,Se=Rn,we=M([].concat),Oe=be("Reflect","ownKeys")||function(t){var n=ve.f(Se(t)),r=he.f;return r?we(n,r(t)):n},je=_t,Te=Oe,Pe=u,Ae=Cn,xe=c,Ee=W,Ce=/#|\.prototype\./,De=function(t,n){var r=Fe[Me(t)];return r==Re||r!=Le&&(Ee(n)?xe(n):!!n)},Me=De.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Fe=De.data={},Le=De.NATIVE="N",Re=De.POLYFILL="P",ke=De,Ie=i,Ne=u.f,ze=Jn,Ve=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Br(r)&&Hr(r,i,e),e.global)o?t[n]=r:Ur(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:qr.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},Ge=Mt,_e=function(t,n,r){for(var e=Te(n),o=Ae.f,i=Pe.f,u=0;u<e.length;u++){var c=e[u];je(t,c)||r&&je(r,c)||o(t,c,i(n,c))}},Be=ke,qe=E,He=Array.isArray||function(t){return"Array"==qe(t)},Ue=TypeError,Ke=mn,We=Cn,Je=h,Qe={};Qe[en("toStringTag")]="z";var Xe="[object z]"===String(Qe),Ye=W,Ze=E,$e=en("toStringTag"),to=Object,no="Arguments"==Ze(function(){return arguments}()),ro=M,eo=c,oo=W,io=Xe?Ze:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),$e))?r:no?Ze(n):"Object"==(e=Ze(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},mo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};mo.sham=!0;var yo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?mo:go,bo=He,vo=yo,ho=X,So=en("species"),wo=Array,Oo=function(t){var n;return bo(t)&&(n=t.constructor,(vo(n)&&(n===wo||bo(n.prototype))||ho(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},jo=c,To=at,Po=en("species"),Ao=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Ie:f?Ie[c]||Ge(c,{}):(Ie[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Ne(r,e))&&u.value:r[e],!Be(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;_e(i,o)}(t.sham||o&&o.sham)&&ze(i,"sham",!0),Ve(r,e,i,t)}},xo=c,Eo=He,Co=X,Do=zt,Mo=ee,Fo=function(t){if(t>9007199254740991)throw Ue("Maximum allowed index exceeded");return t},Lo=function(t,n,r){var e=Ke(n);e in t?We.f(t,e,Je(0,r)):t[e]=r},Ro=function(t,n){return new(Oo(t))(0===n?0:n)},ko=function(t){return To>=51||!jo((function(){var n=[];return(n.constructor={})[Po]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Io=at,No=en("isConcatSpreadable"),zo=Io>=51||!xo((function(){var t=[];return t[No]=!1,t.concat()[0]!==t})),Vo=ko("concat"),Go=function(t){if(!Co(t))return!1;var n=t[No];return void 0!==n?!!n:Eo(t)};Ao({target:"Array",proto:!0,arity:1,forced:!zo||!Vo},{concat:function(t){var n,r,e,o,i,u=Do(this),c=Ro(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(Go(i=-1===n?u:arguments[n]))for(o=Mo(i),Fo(a+o),r=0;r<o;r++,a++)r in i&&Lo(c,a,i[r]);else Fo(a+1),Lo(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["nl-NL"]=r.default.fn.bootstrapTable.locales.nl={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Laden, even geduld"},formatRecordsPerPage:function(t){return"".concat(t," records per pagina")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Toon ".concat(t," tot ").concat(n," van ").concat(r," record").concat(r>1?"s":""," (gefilterd van ").concat(e," records in totaal)"):"Toon ".concat(t," tot ").concat(n," van ").concat(r," record").concat(r>1?"s":"")},formatSRPaginationPreText:function(){return"vorige pagina"},formatSRPaginationPageText:function(t){return"tot pagina ".concat(t)},formatSRPaginationNextText:function(){return"volgende pagina"},formatDetailPagination:function(t){return"Toon ".concat(t," record").concat(t>1?"s":"")},formatClearSearch:function(){return"Verwijder filters"},formatSearch:function(){return"Zoeken"},formatNoMatches:function(){return"Geen resultaten gevonden"},formatPaginationSwitch:function(){return"Verberg/Toon paginering"},formatPaginationSwitchDown:function(){return"Toon paginering"},formatPaginationSwitchUp:function(){return"Verberg paginering"},formatRefresh:function(){return"Vernieuwen"},formatToggleOn:function(){return"Toon kaartweergave"},formatToggleOff:function(){return"Verberg kaartweergave"},formatColumns:function(){return"Kolommen"},formatColumnsToggleAll:function(){return"Allen omschakelen"},formatFullscreen:function(){return"Volledig scherm"},formatAllRows:function(){return"Alle"},formatAutoRefresh:function(){return"Automatisch vernieuwen"},formatExport:function(){return"Exporteer gegevens"},formatJumpTo:function(){return"GA"},formatAdvancedSearch:function(){return"Geavanceerd zoeken"},formatAdvancedCloseButton:function(){return"Sluiten"},formatFilterControlSwitch:function(){return"Verberg/Toon controls"},formatFilterControlSwitchHide:function(){return"Verberg controls"},formatFilterControlSwitchShow:function(){return"Toon controls"},formatAddLevel:function(){return"Niveau toevoegen"},formatCancel:function(){return"Annuleren"},formatColumn:function(){return"Kolom"},formatDeleteLevel:function(){return"Niveau verwijderen"},formatDuplicateAlertTitle:function(){return"Duplicaten gevonden!"},formatDuplicateAlertDescription:function(){return"Gelieve dubbele kolommen te verwijderen of wijzigen"},formatMultipleSort:function(){return"Meervoudige sortering"},formatOrder:function(){return"Volgorde"},formatSort:function(){return"Sorteren"},formatSortBy:function(){return"Sorteren op"},formatThenBy:function(){return"vervolgens"},formatSortOrders:function(){return{asc:"Oplopend",desc:"Aflopend"}}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["nl-NL"])}));
