const mongoose = require('mongoose');

// Define the schema for packing list items
const packingItemSchema = new mongoose.Schema({
  pcsNo: {
    type: String, // Changed to String to match frontend input
    required: true
  },
  carpetNo: {
    type: String,
    required: true
  },
  baleNo: {
    type: String
  },
  quality: {
    type: String,
    required: true
  },
  design: {
    type: String,
    required: true
  },
  colour: {
    type: String,
    required: true
  },
  size: {
    type: String,
    required: true
  },
  area: {
    type: Number, // Changed to Number for consistency
    required: true
  },
  tArea: {
    type: Number,
    required: true
  },
  remarks: {
    type: String,
    default: '',
    required: false
  },
  areaDisplay: {
    type: String
  },
  areaIn: {
    type: String,
    enum: ['Sq.Feet', 'Sq.Meter'],
    default: 'Sq.Feet'
  },
  serialNo: {
    type: Number
  },
  addedAt: {
    type: Date,
    default: Date.now
  }
});

// Define the schema for export packing list
const exportPackingListSchema = new mongoose.Schema({
  invoiceNo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Invoice',
    required: true
  },
  baleNo: {
    type: String,
    required: true
    // Removed index: true to avoid duplicate index definition
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  items: [packingItemSchema],
  totalArea: {
    type: Number, // Changed to Number for consistency
    required: true
  },
  totalTArea: {
    type: Number,
    required: true
  },
  totalPcses: {
    type: Number,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Remove all indexes first to ensure no unique constraints remain
// This will be executed when the model is first created
exportPackingListSchema.collection && exportPackingListSchema.collection.dropIndexes().catch(err => console.log('Index drop error (can be ignored on first run):', err));

// Add index on invoiceNo to improve query performance
// We're removing the unique constraint to allow multiple bales per invoice
exportPackingListSchema.index({ invoiceNo: 1 }, { unique: false });

// Ensure baleNo is not a unique index
exportPackingListSchema.index({ baleNo: 1 }, { unique: false });

// Update the updatedAt field before saving
exportPackingListSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Update the updatedAt field before findOneAndUpdate
exportPackingListSchema.pre('findOneAndUpdate', function(next) {
  this.set({ updatedAt: Date.now() });
  next();
});

// Create the model
const ExportPackingList = mongoose.model('ExportPackingList', exportPackingListSchema);

module.exports = ExportPackingList;