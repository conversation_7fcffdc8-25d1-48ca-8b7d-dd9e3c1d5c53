const CreateChallanRepository = require('../repositories/challan-repository');

class CreateChallanService {
    constructor() {
        this.createChallanRepository = new CreateChallanRepository();
    }

    async createChallan(challanData) {
        try {
            return await this.createChallanRepository.create(challanData);
        } catch (error) {
            throw error;
        }
    }

    async getAllChallans() {
        try {
            return await this.createChallanRepository.findAll();
        } catch (error) {
            throw error;
        }
    }

    async getChallans(filterValue) {
        try {
            return await this.createChallanRepository.findChallans(filterValue);
        } catch (error) {
            throw error;
        }
    }

    async getChallanById(id) {
        try {
            return await this.createChallanRepository.findById(id);
        } catch (error) {
            throw error;
        }
    }

    async updateChallan(id, challanData) {
        try {
            return await this.createChallanRepository.update(id, challanData);
        } catch (error) {
            throw error;
        }
    }

    async deleteChallan(id) {
        try {
            return await this.createChallanRepository.delete(id);
        } catch (error) {
            throw error;
        }
    }
    async deleteChallanItem(id) {
        try {
            return await this.createChallanRepository.deleteItem(id);
        } catch (error) {
            throw error;
        }
    }

    async processReturn(barcodeNo) {
        try {
            const existingChallan = await this.createChallanRepository.findByBarcode(barcodeNo);
            if (!existingChallan) {
                throw new Error('Challan not found for provided barcode');
            }
            if (existingChallan.returned) {
                throw new Error('This item has already been returned');
            }
            // Update the returned status
            existingChallan.returned = true;
            await existingChallan.save();
            return true;
        } catch (error) {
            throw error;
        }
    }
    async getBarcodeHistory(barcodeNo) {
        try {
            // Find the challans where the barcode number matches
            const challans = await CreateChallan.find({ "carpetList.barcodeNo": barcodeNo });
    
            // Process the history
            const history = challans.map(challan => {
                const historyItem = {
                    challanId: challan._id,
                    challanNo: challan.challanNo,
                    status: "Unknown", // Default status
                    createdAt: challan.createdAt,
                    updatedAt: challan.updatedAt
                };
    
                // Find the status of the barcode in this challan
                const carpetItem = challan.carpetList.find(item => item.barcodeNo === barcodeNo);
                if (carpetItem) {
                    historyItem.status = carpetItem.status;
                }
    
                return historyItem;
            });
    
            return history;
        } catch (error) {
            throw error;
        }
    }
    async CreateChallanHistory(challanData) {
        try {
            return await this.createChallanRepository.CreateChallanHistory(challanData);
        } catch (error) {
            throw error;
        }
    }

    async getDeletedChllanData() {

        return await this.createChallanRepository.getDeletedChallanHistoryData();
    }
}

module.exports = CreateChallanService;
