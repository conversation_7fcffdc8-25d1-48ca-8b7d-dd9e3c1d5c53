/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},m={}.propertyIsEnumerable,y=Object.getOwnPropertyDescriptor,d=y&&!m.call({1:2},1);g.f=d?function(t){var n=y(this,t);return!!n&&n.enumerable}:m;var b,h,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,j=w.call,O=S&&w.bind.bind(j,j),P=function(t){return S?O(t):function(){return j.apply(t,arguments)}},T=P,x=T({}.toString),A=T("".slice),C=function(t){return A(x(t),8,-1)},E=C,k=P,M=function(t){if("Function"===E(t))return k(t)},F=a,R=C,D=Object,I=M("".split),L=F((function(){return!D("z").propertyIsEnumerable(0)}))?function(t){return"String"==R(t)?I(t,""):D(t)}:D,N=function(t){return null==t},_=N,z=TypeError,H=function(t){if(_(t))throw z("Can't call method on "+t);return t},G=L,q=H,B=function(t){return G(q(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},Y=W.all,J=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===Y}:function(t){return"function"==typeof t},K=J,Q=W.all,V=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:K(t)||t===Q}:function(t){return"object"==typeof t?null!==t:K(t)},X=i,$=J,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(X[t]):X[t]&&X[t][n]},nt=M({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,at=ut&&ut.v8;at&&(h=(b=at.split("."))[0]>0&&b[0]<4?1:+(b[0]+b[1])),!h&&et&&(!(b=et.match(/Edge\/(\d+)/))||b[1]>=74)&&(b=et.match(/Chrome\/(\d+)/))&&(h=+b[1]);var ct=h,ft=ct,lt=a,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,mt=J,yt=nt,dt=Object,bt=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return mt(n)&&yt(n.prototype,dt(t))},ht=String,vt=J,St=function(t){try{return ht(t)}catch(t){return"Object"}},wt=TypeError,jt=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},Ot=N,Pt=p,Tt=J,xt=V,At=TypeError,Ct={exports:{}},Et=i,kt=Object.defineProperty,Mt=function(t,n){try{kt(Et,t,{value:n,configurable:!0,writable:!0})}catch(r){Et[t]=n}return n},Ft=Mt,Rt="__core-js_shared__",Dt=i[Rt]||Ft(Rt,{}),It=Dt;(Ct.exports=function(t,n){return It[t]||(It[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var Lt=H,Nt=Object,_t=function(t){return Nt(Lt(t))},zt=_t,Ht=M({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return Ht(zt(t),n)},qt=M,Bt=0,Ut=Math.random(),Wt=qt(1..toString),Yt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Bt+Ut,36)},Jt=i,Kt=Ct.exports,Qt=Gt,Vt=Yt,Xt=st,$t=pt,Zt=Kt("wks"),tn=Jt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Vt,en=function(t){if(!Qt(Zt,t)||!Xt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Xt&&Qt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=V,an=bt,cn=function(t,n){var r=t[n];return Ot(r)?void 0:jt(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!xt(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!xt(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!xt(e=Pt(r,t)))return e;throw At("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||an(t))return t;var r,e=cn(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||an(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=bt,mn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},yn=V,dn=i.document,bn=yn(dn)&&yn(dn.createElement),hn=function(t){return bn?dn.createElement(t):{}},vn=!c&&!a((function(){return 7!=Object.defineProperty(hn("div"),"a",{get:function(){return 7}}).a})),Sn=c,wn=p,jn=g,On=v,Pn=B,Tn=mn,xn=Gt,An=vn,Cn=Object.getOwnPropertyDescriptor;u.f=Sn?Cn:function(t,n){if(t=Pn(t),n=Tn(n),An)try{return Cn(t,n)}catch(t){}if(xn(t,n))return On(!wn(jn.f,t,n),t[n])};var En={},kn=c&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mn=V,Fn=String,Rn=TypeError,Dn=function(t){if(Mn(t))return t;throw Rn(Fn(t)+" is not an object")},In=c,Ln=vn,Nn=kn,_n=Dn,zn=mn,Hn=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Un="configurable",Wn="writable";En.f=In?Nn?function(t,n,r){if(_n(t),n=zn(n),_n(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(_n(t),n=zn(n),_n(r),Ln)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Hn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Yn=En,Jn=v,Kn=c?function(t,n,r){return Yn.f(t,n,Jn(1,r))}:function(t,n,r){return t[n]=r,t},Qn={exports:{}},Vn=c,Xn=Gt,$n=Function.prototype,Zn=Vn&&Object.getOwnPropertyDescriptor,tr=Xn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Vn||Vn&&Zn($n,"name").configurable)},rr=J,er=Dt,or=M(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,ar,cr=er.inspectSource,fr=J,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Ct.exports,gr=Yt,mr=pr("keys"),yr={},dr=sr,br=i,hr=V,vr=Kn,Sr=Gt,wr=Dt,jr=function(t){return mr[t]||(mr[t]=gr(t))},Or=yr,Pr="Object already initialized",Tr=br.TypeError,xr=br.WeakMap;if(dr||wr.state){var Ar=wr.state||(wr.state=new xr);Ar.get=Ar.get,Ar.has=Ar.has,Ar.set=Ar.set,ir=function(t,n){if(Ar.has(t))throw Tr(Pr);return n.facade=t,Ar.set(t,n),n},ur=function(t){return Ar.get(t)||{}},ar=function(t){return Ar.has(t)}}else{var Cr=jr("state");Or[Cr]=!0,ir=function(t,n){if(Sr(t,Cr))throw Tr(Pr);return n.facade=t,vr(t,Cr,n),n},ur=function(t){return Sr(t,Cr)?t[Cr]:{}},ar=function(t){return Sr(t,Cr)}}var Er={set:ir,get:ur,has:ar,enforce:function(t){return ar(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!hr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},kr=a,Mr=J,Fr=Gt,Rr=c,Dr=nr.CONFIGURABLE,Ir=cr,Lr=Er.enforce,Nr=Er.get,_r=Object.defineProperty,zr=Rr&&!kr((function(){return 8!==_r((function(){}),"length",{value:8}).length})),Hr=String(String).split("String"),Gr=Qn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Fr(t,"name")||Dr&&t.name!==n)&&(Rr?_r(t,"name",{value:n,configurable:!0}):t.name=n),zr&&r&&Fr(r,"arity")&&t.length!==r.arity&&_r(t,"length",{value:r.arity});try{r&&Fr(r,"constructor")&&r.constructor?Rr&&_r(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Lr(t);return Fr(e,"source")||(e.source=Hr.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Mr(this)&&Nr(this).source||Ir(this)}),"toString");var qr=J,Br=En,Ur=Qn.exports,Wr=Mt,Yr={},Jr=Math.ceil,Kr=Math.floor,Qr=Math.trunc||function(t){var n=+t;return(n>0?Kr:Jr)(n)},Vr=function(t){var n=+t;return n!=n||0===n?0:Qr(n)},Xr=Vr,$r=Math.max,Zr=Math.min,te=Vr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Xr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ae=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),a=ie(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ce={includes:ae(!0),indexOf:ae(!1)},fe=Gt,le=B,se=ce.indexOf,pe=yr,ge=M([].push),me=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},ye=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Yr.f=Object.getOwnPropertyNames||function(t){return me(t,ye)};var de={};de.f=Object.getOwnPropertySymbols;var be=tt,he=Yr,ve=de,Se=Dn,we=M([].concat),je=be("Reflect","ownKeys")||function(t){var n=he.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},Oe=Gt,Pe=je,Te=u,xe=En,Ae=a,Ce=J,Ee=/#|\.prototype\./,ke=function(t,n){var r=Fe[Me(t)];return r==De||r!=Re&&(Ce(n)?Ae(n):!!n)},Me=ke.normalize=function(t){return String(t).replace(Ee,".").toLowerCase()},Fe=ke.data={},Re=ke.NATIVE="N",De=ke.POLYFILL="P",Ie=ke,Le=i,Ne=u.f,_e=Kn,ze=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},He=Mt,Ge=function(t,n,r){for(var e=Pe(n),o=xe.f,i=Te.f,u=0;u<e.length;u++){var a=e[u];Oe(t,a)||r&&Oe(r,a)||o(t,a,i(n,a))}},qe=Ie,Be=C,Ue=Array.isArray||function(t){return"Array"==Be(t)},We=TypeError,Ye=mn,Je=En,Ke=v,Qe={};Qe[en("toStringTag")]="z";var Ve="[object z]"===String(Qe),Xe=J,$e=C,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=M,eo=a,oo=J,io=Ve?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Xe(n.callee)?"Arguments":e},uo=cr,ao=function(){},co=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(ao),go=function(t){if(!oo(t))return!1;try{return fo(ao,co,t),!0}catch(t){return!1}},mo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};mo.sham=!0;var yo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?mo:go,bo=Ue,ho=yo,vo=V,So=en("species"),wo=Array,jo=function(t){var n;return bo(t)&&(n=t.constructor,(ho(n)&&(n===wo||bo(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},Oo=a,Po=ct,To=en("species"),xo=function(t,n){var r,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?Le:f?Le[a]||He(a,{}):(Le[a]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Ne(r,e))&&u.value:r[e],!qe(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&_e(i,"sham",!0),ze(r,e,i,t)}},Ao=a,Co=Ue,Eo=V,ko=_t,Mo=ee,Fo=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},Ro=function(t,n,r){var e=Ye(n);e in t?Je.f(t,e,Ke(0,r)):t[e]=r},Do=function(t,n){return new(jo(t))(0===n?0:n)},Io=function(t){return Po>=51||!Oo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=ct,No=en("isConcatSpreadable"),_o=Lo>=51||!Ao((function(){var t=[];return t[No]=!1,t.concat()[0]!==t})),zo=Io("concat"),Ho=function(t){if(!Eo(t))return!1;var n=t[No];return void 0!==n?!!n:Co(t)};xo({target:"Array",proto:!0,arity:1,forced:!_o||!zo},{concat:function(t){var n,r,e,o,i,u=ko(this),a=Do(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(Ho(i=-1===n?u:arguments[n]))for(o=Mo(i),Fo(c+o),r=0;r<o;r++,c++)r in i&&Ro(a,c,i[r]);else Fo(c+1),Ro(a,c++,i);return a.length=c,a}}),r.default.fn.bootstrapTable.locales["ms-MY"]=r.default.fn.bootstrapTable.locales.ms={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Permintaan sedang dimuatkan. Sila tunggu sebentar"},formatRecordsPerPage:function(t){return"".concat(t," rekod setiap muka surat")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Sedang memaparkan rekod ".concat(t," hingga ").concat(n," daripada jumlah ").concat(r," rekod (filtered from ").concat(e," total rows)"):"Sedang memaparkan rekod ".concat(t," hingga ").concat(n," daripada jumlah ").concat(r," rekod")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Cari"},formatNoMatches:function(){return"Tiada rekod yang menyamai permintaan"},formatPaginationSwitch:function(){return"Tunjuk/sembunyi muka surat"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Muatsemula"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Lajur"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Semua"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ms-MY"])}));
