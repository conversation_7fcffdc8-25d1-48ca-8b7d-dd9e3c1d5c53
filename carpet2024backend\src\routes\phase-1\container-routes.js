const express = require('express');
const router = express.Router();
const AddContainerDespatchController = require('../../controller/containerD-controller');

router.post('/create-container', AddContainerDespatchController.createContainerDespatch);
router.get('/get-container/:id', AddContainerDespatchController.getContainerDespatchById);
router.get('/getAll-container', AddContainerDespatchController.getAllContainerDespatches);
router.put('/update-container/:id', AddContainerDespatchController.updateContainerDespatch);
router.delete('/delete-container/:id', AddContainerDespatchController.deleteContainerDespatch);

module.exports = router;
