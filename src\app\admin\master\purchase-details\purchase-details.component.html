
<div class="mt-4" style="width: 80%;">
  <section class="margin:10px">
    <fieldset style="min-height: 200px">
      <legend><b> {{isUpdated?'Update':'Add'}} Purchaser Ledger Account</b></legend>
      <form [formGroup]="frmPurchaseDetails" (ngSubmit)="addPurchaseDetails()">
        <div class="row">
          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline">
              <mat-label>Group</mat-label>
              <mat-select formControlName="group">
                @for (group of groupList; track group) {
                <mat-option [value]="group">{{ group }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmPurchaseDetails.get('group')?.invalid && (frmPurchaseDetails.get('group')?.dirty || frmPurchaseDetails.get('group')?.touched)">
              <div *ngIf="frmPurchaseDetails.get('group')?.errors?.['required']">
               <span class="errText"> Please select a group type. group </span>
              </div>

            </div>
          </div>

          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline">
              <mat-label>Sub group</mat-label>
              <mat-select formControlName="subGroup">
                @for (group of subGroupList; track group) {
                <mat-option [value]="group">{{ group }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmPurchaseDetails.get('subGroup')?.invalid && (frmPurchaseDetails.get('subGroup')?.dirty || frmPurchaseDetails.get('subGroup')?.touched)">
              <div *ngIf="frmPurchaseDetails.get('subGroup')?.errors?.['required']">
               <span class="errText"> Please select a sub group type. group </span>
              </div>

            </div>
          </div>
          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline">
              <mat-label>Material</mat-label>
              <mat-select formControlName="Material" (valueChange)="material($event)">
                @for (material of materialList; track material) {
                <mat-option value="{{material.id}}">{{ material.material }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmPurchaseDetails.get('Material')?.invalid && (frmPurchaseDetails.get('Material')?.dirty || frmPurchaseDetails.get('Material')?.touched)">
              <div *ngIf="frmPurchaseDetails.get('Material')?.errors?.['required']">
               <span class="errText"> Please select a material type.</span>
              </div>

            </div>
          </div>

          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline">
              <mat-label>Count</mat-label>
              <mat-select formControlName="count">
                @for (count of countList; track count) {
                <mat-option [value]="count">{{ count }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmPurchaseDetails.get('count')?.invalid && (frmPurchaseDetails.get('count')?.dirty || frmPurchaseDetails.get('count')?.touched)">
              <div *ngIf="frmPurchaseDetails.get('count')?.errors?.['required']">
               <span class="errText"> Please select a count type.</span>
              </div>

            </div>
          </div>
          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Party Name</mat-label>
              <input matInput placeholder="Party Name" formControlName="partyname" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmPurchaseDetails.get('partyname')?.invalid && (frmPurchaseDetails.get('partyname')?.dirty || frmPurchaseDetails.get('partyname')?.touched)">
              <div *ngIf="frmPurchaseDetails.get('partyname')?.errors?.['required']">
               <span class="errText"> Party name is required.</span>
              </div>
              <div *ngIf="frmPurchaseDetails.get('partyname')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>










          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Address </mat-label>
              <textarea matInput formControlName="address"></textarea>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmPurchaseDetails.get('address')?.invalid && (frmPurchaseDetails.get('address')?.dirty || frmPurchaseDetails.get('address')?.touched)">
              <div *ngIf="frmPurchaseDetails.get('address')?.errors?.['required']">
               <span class="errText"> Address is required.</span>
              </div>
              <div *ngIf="frmPurchaseDetails.get('address')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Zipcode</mat-label>
              <input matInput placeholder="Zipcode" formControlName="zipcode" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmPurchaseDetails.get('zipcode')?.invalid && (frmPurchaseDetails.get('zipcode')?.dirty || frmPurchaseDetails.get('zipcode')?.touched)">
              <div *ngIf="frmPurchaseDetails.get('zipcode')?.errors?.['required']">
               <span class="errText"> Zip code is required.</span>
              </div>
              <div *ngIf="frmPurchaseDetails.get('zipcode')?.errors?.['pattern']">

                <span class="errText"> Something went wrong </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Contact no</mat-label>
              <input matInput placeholder="Contact no"  formControlName="contractno"/>
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmPurchaseDetails.get('contractno')?.invalid && (frmPurchaseDetails.get('contractno')?.dirty || frmPurchaseDetails.get('contractno')?.touched)">
              <div *ngIf="frmPurchaseDetails.get('contractno')?.errors?.['required']">
               <span class="text-danger"> Contact number must be 10 digits long.</span>
              </div>
              <div *ngIf="frmPurchaseDetails.get('contractno')?.errors?.['pattern']">

                <span class="text-danger"> Invalid contact number </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Email</mat-label>
              <input type="email" matInput placeholder="Ex. <EMAIL>" formControlName="email" />
            </mat-form-field>
            <div class="erroDiv" *ngIf="frmPurchaseDetails.get('email')?.invalid && (frmPurchaseDetails.get('email')?.dirty || frmPurchaseDetails.get('email')?.touched)">
              <div *ngIf="frmPurchaseDetails.get('email')?.errors?.['required']">
               <span class="text-danger"> Email is required.</span>
              </div>
              <div *ngIf="frmPurchaseDetails.get('email')?.errors?.['pattern']">

                <span class="text-danger"> Invalid email format  </span>
              </div>
            </div>
          </div>
          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Gst no</mat-label>
              <input matInput placeholder="Gst no 15 digit" formControlName="gstno" />
            </mat-form-field>
          </div>
          <div class="mb-2 col-md-4">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Pan no.</mat-label>
              <input matInput placeholder="Pan no." formControlName="panno" />
            </mat-form-field>
          </div>

          <div class="mb-2 mt-2 col-md-6">

            <button *ngIf="isUpdated; else addButton" mat-raised-button type="button"  [disabled]="!frmPurchaseDetails.valid" (click)="update()" color="primary">
              Update
            </button>
            <ng-template #addButton>
              <button mat-raised-button color="primary"  [disabled]="!frmPurchaseDetails.valid" >Add</button>
            </ng-template>
          </div>

        </div>
      </form>
    </fieldset>
  </section>
</div>
<div class="mt-4" style="width: 80%;">
  <section>
    <fieldset>
      <legend><b>List</b></legend>
      <div class="row">
        <div class="col-md-12">
          <mat-form-field>
            <mat-label>Search</mat-label>
            <input
              matInput
              (keyup)="applyFilter($event)"
              placeholder="Ex. Mia"
              #input
            />
          </mat-form-field>

          <div class="mat-elevation-z8" style="overflow: auto;">
            <table mat-table [dataSource]="dataSource" matSort style="width: max-content;">
              <!-- ID Column -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>No.</th>
                <td mat-cell *matCellDef="let row">{{ row.index }}</td>
              </ng-container>

              <!-- Progress Column -->
              <ng-container matColumnDef="group">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Group</th>
                <td mat-cell *matCellDef="let row">{{ row.group }}</td>
              </ng-container>

              <!-- Name Column -->
              <ng-container matColumnDef="Material">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Material
                </th>
                <td mat-cell *matCellDef="let row">{{ row.Material }}</td>
              </ng-container>

              <!-- Fruit Column -->
              <ng-container matColumnDef="count">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Count</th>
                <td mat-cell *matCellDef="let row">{{ row.count }}</td>
              </ng-container>

              <ng-container matColumnDef="partyname">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Party Name
                </th>
                <td mat-cell *matCellDef="let row">{{ row.partyname }}</td>
              </ng-container>

              <ng-container matColumnDef="address">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Address
                </th>
                <td mat-cell *matCellDef="let row">{{ row.address }}</td>
              </ng-container>


              <ng-container matColumnDef="subGroup">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Sub Group
                </th>
                <td mat-cell *matCellDef="let row">{{ row.subGroup }}</td>
              </ng-container>
              <ng-container matColumnDef="zipcode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Zipcode
                </th>
                <td mat-cell *matCellDef="let row">{{ row.zipcode }}</td>
              </ng-container>

              <ng-container matColumnDef="contractno">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Contact no
                </th>
                <td mat-cell *matCellDef="let row">{{ row.contractno }}</td>
              </ng-container>
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
                <td mat-cell *matCellDef="let row" class="no-capitalize">{{ row.email }}</td>
              </ng-container>

              <ng-container matColumnDef="gstno">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  GST No
                </th>
                <td mat-cell *matCellDef="let row">{{ row.gstno }}</td>
              </ng-container>

              <ng-container matColumnDef="panno">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Pan No
                </th>
                <td mat-cell *matCellDef="let row">{{ row.panno }}</td>
              </ng-container>

              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  Action
                </th>
                <td mat-cell *matCellDef="let row">
                  <a (click)="edit(row.id)"
            ><i
              class="fa fa-pencil-square-o fa-edit"
              title="Edit purchase details "
              aria-hidden="true"
            ></i
          ></a>
          &nbsp;
          <a (click)="delete(row.id)"><i class="fa fa-trash-o" aria-hidden="true"></i></a>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>

              <!-- Row shown when there is no matching data. -->
              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="4">
                  No data matching the filter "{{ input.value }}"
                </td>
              </tr>
            </table>

            <mat-paginator
              [pageSizeOptions]="[5, 10, 25, 100]"
              aria-label="Select page of users"
            ></mat-paginator>
          </div>
        </div>
      </div>
    </fieldset>
  </section>
</div>
