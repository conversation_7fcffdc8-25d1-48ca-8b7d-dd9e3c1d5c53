const paymentTermsRepository = require('../repositories/paymentTerm-repository');

class PaymentTermsService {
  async createPaymentTerm(paymentTermData) {
    try {
      const paymentTerm = await paymentTermsRepository.createPaymentTerm(paymentTermData);
      return paymentTerm;
    } catch (error) {
      throw error;
    }
  }

  async getPaymentTermById(paymentTermId) {
    try {
      const paymentTerm = await paymentTermsRepository.getPaymentTermById(paymentTermId);
      return paymentTerm;
    } catch (error) {
      throw error;
    }
  }

  async getAllPaymentTerms() {
    try {
      const paymentTerms = await paymentTermsRepository.getAllPaymentTerms();
      return paymentTerms;
    } catch (error) {
      throw error;
    }
  }

  async updatePaymentTerm(paymentTermId, paymentTermData) {
    try {
      const paymentTerm = await paymentTermsRepository.updatePaymentTerm(paymentTermId, paymentTermData);
      return paymentTerm;
    } catch (error) {
      throw error;
    }
  }

  async deletePaymentTerm(paymentTermId) {
    try {
      const deletedPaymentTerm = await paymentTermsRepository.deletePaymentTerm(paymentTermId);
      return deletedPaymentTerm;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new PaymentTermsService();
