const mapRateRepository = require('../repositories/mapRate-repository');

const createMapRate = async (mapRateData) => {
  return await mapRateRepository.createMapRate(mapRateData);
};

const getAllMapRates = async () => {
  return await mapRateRepository.getAllMapRates();
};

const getMapRateById = async (id) => {
  return await mapRateRepository.getMapRateById(id);
};

const updateMapRate = async (id, mapRateData) => {
  return await mapRateRepository.updateMapRate(id, mapRateData);
};

const deleteMapRate = async (id) => {
  return await mapRateRepository.deleteMapRate(id);
};

module.exports = {
  createMapRate,
  getAllMapRates,
  getMapRateById,
  updateMapRate,
  deleteMapRate
};
