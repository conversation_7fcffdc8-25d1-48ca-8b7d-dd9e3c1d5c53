const mongoose = require('mongoose');

const containerItemSchema = new mongoose.Schema({
    GerCarpetNo: {
        type: String,
        unique:true,
    },
    Date:{type:  Date},
    QualityDesign: {
        type: String
    },
    Color: {
        type: String
    },
    CCode: {
        type: String
    },
    QCode: {
        type: String
    },
    Size: {
        
        type: String
    },
    SCore: {
        type: String
    },
    Area: {
        type: String
    },
    Rate: {
        type: String
    },
    EvKPrice: {
        type: String
    },
    Amount: {
        type: String
    },
    InvoiceNo: {
        type: String
    },
    ImporterCode: {
        type: String
    },
    Remarks: {
        type: String
    },
    status: {
        type: String,
        enum: ['sale', 'return', 'stock'],
        default: 'stock'
    }
});

const containerReceivedSchema = new mongoose.Schema({
    impoterName: {
        type: String,
        required: true
    },
    expensesAmount: {
        type: Number,
        required: true
    },
    totalArea: {
        type: Number,
        required: true
    },
    espPrice: {
        type: Number,
        required: true
    },
    blPdf: {
       type:String
    },
    containerItem: [{
        type: containerItemSchema,
        validate: {
            validator: function (items) {
                // Ensure that only items with status 'sale' or 'return' exist in the array
                return items.every(item => item.status === 'sale' || item.status === 'return');
            },
            message: 'Each item must have status either "sale" or "return".'
        }
    }]
}, { timestamps: true });

const ContainerReceived = mongoose.model('ContainerReceived', containerReceivedSchema);

module.exports = ContainerReceived;
