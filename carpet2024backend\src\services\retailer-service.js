// services/retailerService.js

const retailerRepository = require('../repositories/retailer-repository');

async function createRetailer(data) {
    return await retailerRepository.createRetailer(data);
}

async function getRetailers() {
    return await retailerRepository.getRetailers();
}

async function getRetailerById(id) {
    return await retailerRepository.getRetailerById(id);
}

async function updateRetailer(id, data) {
    return await retailerRepository.updateRetailer(id, data);
}

async function deleteRetailer(id) {
    return await retailerRepository.deleteRetailer(id);
}

//////////
  ////// here code for add on price after add importer
  ////
  async function addRetailerPrice(id,reqData){
    try {
      return await retailerRepository.addRetailerPrice(id,reqData);
    } catch (error) {
      throw error
    }
  }
module.exports = {
    createRetailer,
    getRetailers,
    getRetailerById,
    updateRetailer,
    deleteRetailer,
    addRetailerPrice
};
