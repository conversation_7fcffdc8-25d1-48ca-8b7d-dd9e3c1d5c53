<div class="row">
  <div class="col-md-12" style="overflow: auto;">
    <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">

      <ng-container matColumnDef="select">
        <th mat-header-cell *matHeaderCellDef>
          <mat-checkbox (change)="$event ? masterToggle() : null"
            [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()">
          </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let row">
          <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(row) : null; logSelection(row)"
            [checked]="selection.isSelected(row)">
          </mat-checkbox>
        </td>
      </ng-container>

      <ng-container matColumnDef="GerCarpetNo">
        <th mat-header-cell *matHeaderCellDef> Carpet No </th>
        <td mat-cell *matCellDef="let element"> {{element.GerCarpetNo}} </td>
      </ng-container>

      <ng-container matColumnDef="QualityDesign">
        <th mat-header-cell *matHeaderCellDef> Quality Design </th>
        <td mat-cell *matCellDef="let element"> {{element.QualityDesign}} </td>
      </ng-container>

      <ng-container matColumnDef="Color">
        <th mat-header-cell *matHeaderCellDef> Color </th>
        <td mat-cell *matCellDef="let element"> {{element.Color}} </td>
      </ng-container>

      <ng-container matColumnDef="CCode">
        <th mat-header-cell *matHeaderCellDef> Color Code </th>
        <td mat-cell *matCellDef="let element"> {{element.CCode}} </td>
      </ng-container>

      <ng-container matColumnDef="QCode">
        <th mat-header-cell *matHeaderCellDef> QCode </th>
        <td mat-cell *matCellDef="let element"> {{element.QCode}} </td>
      </ng-container>

      <ng-container matColumnDef="Size">
        <th mat-header-cell *matHeaderCellDef> Size </th>
        <td mat-cell *matCellDef="let element"> {{element.Size}} </td>
      </ng-container>

      <ng-container matColumnDef="SCore">
        <th mat-header-cell *matHeaderCellDef> Size Code </th>
        <td mat-cell *matCellDef="let element"> {{element.SCore}} </td>
      </ng-container>

      <ng-container matColumnDef="Area">
        <th mat-header-cell *matHeaderCellDef> Area </th>
        <td mat-cell *matCellDef="let element"> {{element.Area}} </td>
      </ng-container>

      <ng-container matColumnDef="EvKPrice">
        <th mat-header-cell *matHeaderCellDef> EvK Price </th>
        <td mat-cell *matCellDef="let element"> {{element.EvKPrice}} </td>
      </ng-container>

      <ng-container matColumnDef="Amount">
        <th mat-header-cell *matHeaderCellDef> Amount </th>
        <td mat-cell *matCellDef="let element"> {{element.Amount}} </td>
      </ng-container>

      <ng-container matColumnDef="InvoiceNo">
        <th mat-header-cell *matHeaderCellDef> Invoice No </th>
        <td mat-cell *matCellDef="let element"> {{element.InvoiceNo}} </td>
      </ng-container>

      <ng-container matColumnDef="Date">
        <th mat-header-cell *matHeaderCellDef> Date </th>
        <td mat-cell *matCellDef="let element"> {{element.Date}} </td>
      </ng-container>

      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef> No. </th>
        <td mat-cell *matCellDef="let element"> {{element.index}} </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
  <mat-paginator [length]="100"
              [pageSize]="10"
              [pageSizeOptions]="[5, 10, 25, 100]"
              aria-label="Select page">
  </mat-paginator>
</div>
