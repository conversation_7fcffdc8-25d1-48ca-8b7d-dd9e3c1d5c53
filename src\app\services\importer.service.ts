import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment.development';



@Injectable({
  providedIn: 'root'
})
export class ImporterService {


  constructor(private httpClient: HttpClient) { }

  apiUrl: any = environment.apiUrl;
  phaseOne: any = `${this.apiUrl}/phase-one`;
  phaseTwo: any = `${this.apiUrl}/phase-two`;


  // service for importer-detail
  createImporter(data: any) {
    return this.httpClient.post(`${this.phaseTwo}/impoter/create-impoter`, data)
  }

  /////////////////////////////
  ////////////////// here code for add price of indivisual importer
  //////////
  /////
addImporterPrice(id:string, data:any){
  return this.httpClient.patch(`${this.phaseTwo}/impoter/update-price-data/${id}`,data);
}
  /////////////////////////////////////
  ///// get  importer by id
  //
  getImporter(id: any) {
    return this.httpClient.get(`${this.phaseTwo}/impoter/get-impoter/${id}`)
  }


  //////////////////////////////////////////////////
  ///////////////////////////////
  ////////////////////add on price using update inporter details
  ///////////////
  //////////
  addPrice(data: any, id: any) {
    return this.httpClient.put(`${this.phaseTwo}/impoter/update-impoter/${id}`, data);
  }






  /////////////////////////////////////
  ///// delete importer
  //

  deleteImporter(id: any) {
    return this.httpClient.delete(`${this.phaseTwo}/impoter/delete-impoter/${id}`);
  }


  private data: any

  setData(data: any) {
    this.data = data
  }

  getData() {
    return this.data
  }

  getAllImporter() {
    return this.httpClient.get(`${this.phaseTwo}/impoter/getAll-impoter`)
  }

  // here service for import-invoice
  // this service for upload excel

  UploadExcel(file: File,data:any ) {
    debugger
    let formData = new FormData();
    formData.append('file', file)
    formData.append('impoterNo',data.importNo);
    formData.append('impoterName',data.importerName)
    return this.httpClient.post(`${this.phaseOne}/exlupload/exlupload`, formData)
  }

  ExcelCalculationData(data: any) {
    return this.httpClient.post(`${this.phaseOne}/exldata/exldata`, data)
  }

  // this service for invoice-pdf

  createInvoicePdf(data: File) {
    // Create a new FormData object
    debugger
    let formData = new FormData();
    // Append the File object to the FormData object
    formData.append('invoicePdf', data);
    // formData.append('blPdf',data );
    // Send the FormData object as part of a POST request
    return this.httpClient.post(`${this.phaseOne}/invoice/upload-invoice-pdf`, formData,

    );
  }

  getInvoicePdf(id: any) {
    return this.httpClient.get(`${this.phaseOne}/invoice/get-invoice-pdf/${id}`)
  }

  getAllInvoicePdf() {
    return this.httpClient.get(`${this.phaseOne}/invoice/get-all-invoice-pdf`)
  }



  // here service for importer-invoice-details

  createInvoiceDetails(data: any) {
    return this.httpClient.post(`${this.phaseOne}/impoter/create-impoter-invoice`, data);
  }



  getInvoiceDetails(id: any) {
    return this.httpClient.get(`${this.phaseOne}/impoter/get-impoter-invoice/${id}`)
  }






  /////////////////////////////////////////////
  ////////////////////////////////
  ///////////////////here code for get all inporter invoice usng this route ==> impoter/getAll-impoter-invoice
  //////////

  getAllImporterInvoice() {
    return this.httpClient.get(`${this.phaseOne}/impoter/getAll-impoter-invoice`);

  }

  /////////////////////////////////////////////
  ////////////////////////////////
  ///////////////////here code for get summary of excel data usng this route ==> phase-one/exldata/exldata
  //////////
  getExcelSummary(a: any) {
    debugger
    return this.httpClient.post(`${this.phaseOne}/exldata/exldata`, a);
  }


  /////////////////////////////////////////////
  ////////////////////////////////
  ///////////////////here code for get all excel data usng this route ==> getAllData/getAll-data
  //////////

  getAllExcelList(data:any) {
    return this.httpClient.post(`${this.phaseOne}/getAllData/getAll-data`,data);
  }


  ////////////////////////////////////////////////////
  /////////////////////////////////
  ///////////////////////impoter//delete-impoter-invoice
  ///////////
  deleteImporterInvoice(id: any) {
    return this.httpClient.delete(`${this.phaseOne}/impoter//delete-impoter-invoice/${id}`);
  }

  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// add new container despatched
  ///////
  addContainerDespatched(data: any) {
    return this.httpClient.post(`${this.phaseOne}/container/create-container`, data);
  }
  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// get all container despatched
  ///////
  getAllContainerDespatche() {
    return this.httpClient.get(`${this.phaseOne}/container/getAll-container`);
  }
  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// delete container despatched
  ///////
  deleteContainerDespatched(id: any) {
    return this.httpClient.delete(`${this.phaseOne}/container/delete-container/${id}`);
  }

  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// get container despatched detail
  ///////
  getContainerDespatch(id: any) {
    return this.httpClient.get(`${this.phaseOne}/container/get-container/${id}`);
  }
  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// update container despatched detail
  ///////
  updateContainerDespatched(id: any, data: any) {
    return this.httpClient.put(`${this.phaseOne}/container/update-container/${id}`, data);
  }

  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for post quality design code
  ///////
  addQualityDesign(data: any) {
    return this.httpClient.post(`${this.phaseTwo}/qualityandDesign/craete-qualityanddesign`, data);
  }

  getAllQualityDesign() {
    return this.httpClient.get(`${this.phaseTwo}/qualityandDesign/getAll-qualityanddesign`);
  }
  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for post size code
  ///////

  addSizeCode(data: any) {
    return this.httpClient.post(`${this.phaseOne}/size/create-size`, data);
  }
  getAllSizeList() {
    return this.httpClient.get(`${this.phaseOne}/size/get-all-size`)
  }

  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for upload bill of ladding pdf
  ///////
  uploadBillPdf(file: File) {
    debugger
    let formData = new FormData();

    formData.append('billOfLading', file);
    return this.httpClient.post(`${this.phaseOne}/billOfLadings/upload-billoflading-pdf`, formData);
  }

  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for post containerReceived
  ///////
  addContainerRecieved(data: any) {
    return this.httpClient.post(`${this.phaseOne}/containerRcv/upload-containerReceived`, data);
  }


  getAllContainerRecieved() {
    return this.httpClient.get(`${this.phaseOne}/containerRcv/getAll-containerReceived`);
  }

  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for get all challan list
  ///////
  getAllChallan() {
    return this.httpClient.get(`${this.phaseTwo}/CreateChallan/CreateChallan`);
  }
  getChallans(data:string){
    debugger
    let d={filterValue:data};
    return this.httpClient.post(`${this.phaseTwo}/CreateChallan/getChallans`,d)
  }
  createChallan(data:any){
    return this.httpClient.post(`${this.phaseTwo}/CreateChallan/CreateChallan`,data);
  }
  getChallan(id:any){
    return this.httpClient.get(`${this.phaseTwo}/CreateChallan/CreateChallan/${id}`);
  }

  updateChallan(id:string, data:any){
    return this.httpClient.put(`${this.phaseTwo}/CreateChallan/CreateChallan/${id}`,data);

  }

  deleteChallan(challanId:string, itemId:string ){
    return this.httpClient.delete(`${this.phaseTwo}/CreateChallan/CreateChallan/${challanId}/${itemId}`);
  }
  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for add wholsaler
  ///////
  addWholesaler(data: any) {
    return this.httpClient.post(`${this.phaseTwo}/wholeSeller/create-wholeseller`, data);
  }
  getsWholesalerList() {
    return this.httpClient.get(`${this.phaseTwo}/wholeSeller/getAll-wholeseller`)
  }
  getWholesaller(id:string){
    return this.httpClient.get(`${this.phaseTwo}/wholeSeller/get-wholeseller/${id}`)

  }
  updateWholesaler(data: any,id:string) {
    debugger
    return this.httpClient.put(`${this.phaseTwo}/wholeSeller/update-wholeseller/${id}`, data);
  }
  deletWholesaller(id:string){
    return this.httpClient.delete(`${this.phaseTwo}/wholeSeller/delete-wholeseller/${id}`)
  }
//////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for add retailer
  ///////
  addRetailer(data:any){
    return this.httpClient.post(`${this.phaseTwo}/retailer/create-retailer`,data);
  }

//////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for get Retailer list
  ///////
  getRetailerList(){
    return this.httpClient.get(`${this.phaseTwo}/retailer/getAll-retailer`);
  }


  updateRetailer(data:any,id:string){
    debugger
    return this.httpClient.patch(`${this.phaseTwo}/retailer/add-retailer-price/${id}`,data);
  }
//////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for get wholeSeller/getAll-billwholeseller
  ///////

  getsBill(){
    return this.httpClient.get(`${this.phaseTwo}/wholeSeller/BillForWholeseller`);
  }
//////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for create wholeSeller/create-billwholeseller
  ///////

  createBill(data:any){
    return this.httpClient.post(`${this.phaseTwo}/wholeSeller/BillForWholeseller`,data);
  }

  updateBill(data:any,id:string){
    debugger
    return this.httpClient.put(`${this.phaseTwo}/wholeSeller/BillForWholeseller/${id}`,data);
  }

  deleteWholesellerBill(id:string){
    debugger
    return this.httpClient.delete(`${this.phaseTwo}/wholeSeller/BillForWholeseller/${id}`);
  }

  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for create wholeSeller/gets-billwholeseller
  ///////
  getBills(){
    return this.httpClient.get(`${this.phaseTwo}/wholeSeller/getAll-billwholeseller`)
  }


  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// here service for Gst
  ///////
  createGst(data:any){
    return this.httpClient.post(`${this.phaseTwo}/gst/create-gst`,data);
  }
  getAllGst(){
    return this.httpClient.get(`${this.phaseTwo}/gst/get-all-gst`);
  }
  getGst(id:string){
    return this.httpClient.get(`${this.phaseTwo}/gst/get-gst/${id}`);
  }
  updateGst(id:string, data:any){
    return this.httpClient.put(`${this.phaseTwo}/gst/update-gst/${id}`,data);
  }
  deleteGst(id:string){
    return this.httpClient.delete(`${this.phaseTwo}/gst/delete-gst/${id}`);
  }

  getsretailser(){
    return this.httpClient.get(`${this.phaseTwo}/`)
  }

  getBarCodeDetails(bcr:any){
    return this.httpClient.post(`${this.phaseTwo}/wholeSeller/carpet-details`,bcr);
  }

  getCarpetStock(carpet:any){
    return this.httpClient.post(`${this.phaseOne}/containerRcv/carpet-stock-details`, carpet);

  }




  getSolededStock(page: number, limit: number){
    debugger
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());
    return this.httpClient.get(`${this.phaseOne}/containerRcv/get-all-soleded`,{params});

  }
}

