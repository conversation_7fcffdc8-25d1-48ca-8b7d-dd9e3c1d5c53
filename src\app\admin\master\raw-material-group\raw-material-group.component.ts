import { AfterViewInit, OnInit, Component, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SweetalertService } from '../../../services/sweetalert.service';
import { environment } from '../../../../environments/environment.development';
import { DomSanitizer ,SafeUrl } from '@angular/platform-browser';
declare var bootstrap: any
export interface UserData {
  id: string;
  index: number;
  Group: string;
  Item: string;
  Count: string;
  Colour: string;
  Description: string;
  Details: string;
  Image: SafeUrl;
  Quality: string;
}

const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-raw-material-group',
  templateUrl: './raw-material-group.component.html',
  styleUrl: './raw-material-group.component.css',
})
export class RawMaterialGroupComponent implements OnInit, AfterViewInit {
  Group = 'silk';
  Item = 'option1';
  Count = 'option2';
  Colour = 'option3';
  Group1 = 'silk';

  displayedColumns: string[] = [
    'id',
    'Group',
    'Item',
    'Quality',
    'Count',
    'Colour',
    'Description',
    'Details',
    'Image',
    'Action',
  ];
  dataSource = new MatTableDataSource(ELEMENT_DATA);

  constructor(
    private services: MasterService,
    private fb: FormBuilder,
    private alert: SweetalertService,
    private sanitizer: DomSanitizer
  ) {}
  ngOnInit(): void {
    this.frmRawMaterial = this.fb.group({
      Group: ['',[Validators.required]],
      Item: ['',[Validators.required]],
      Description: ['',[Validators.required]],
      Count: ['',[Validators.required]],
      Color: ['',[Validators.required]],
      Quality: ['',[Validators.required]],
      Details: [''],
      MaterialImage: [''],
    });
    this.qualitys();
    this.gets();
  }
  groupList: any = ['Kati', 'Tana','Soot', 'Tharri', 'Silk', 'Others'];
  qualityList: any = [];
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  frmRawMaterial!: FormGroup;
  MaterialImage: any;
  editId!: string;
  isUpdated: boolean = false;
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }
  applyFilter($event: any) {
    this.dataSource.filter = $event.target.value;
  }
  file(file: any) {
    this.MaterialImage = file.target.files[0];
    debugger;
  }
  imageUrl!: SafeUrl;

  getSafeUrl(url: string): SafeUrl {
    return this.sanitizer.bypassSecurityTrustUrl(url);
  }
  gets() {
    this.services.getsRawMaterial().subscribe({
      next: (value: any) => {
        ELEMENT_DATA.length = 0;
        value.map((v: any, i: number) => {
          debugger

          const imageUrl = `${environment.imageUrl}${v.uploadedFile}`;
let img:any =this.getSafeUrl(imageUrl);
          ELEMENT_DATA.push({
            id: v._id,
            index: i + 1,
            Group: v.Group,
            Item: v.Item,
            Count: v.Count,
            Colour: v.Color,
            Description: v.Description,
            Details: v.Details,
            Image:imageUrl,
            Quality: v.Quality,
            // v.Material ? v.Material.Item : 'N/A',
          });
        });
        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        return
      },
    });
  }
  qualitys() {
    this.services.getqualityList().subscribe({
      next: (value: any) => {
        value.map((v: any) => {
          this.qualityList.push({
            quality: v.quality,
            id: v._id,
          });
        });
      },
      error: (err) => {},
    });
  }
  qty: any;
  onQuality(data: any) {
    this.qty = data;
  }
  addRawMaterial() {
    let frmData = this.frmRawMaterial.value;
    debugger
    let formData = new FormData();
    formData.append('Group', frmData.Group);
    formData.append('Item', frmData.Item);
    formData.append('Description', frmData.Description);
    formData.append('Count', frmData.Count);
    formData.append('Color', frmData.Color);
    formData.append('Quality', frmData.Quality);
    formData.append('Details', frmData.Details);
    formData.append('uploadedFile', this.MaterialImage);

    this.services.addRawMaterial(formData).subscribe({
      next: (value) => {
        this.alert.success('success', 'Material has been saved successfully');
        this.gets()
        this.clear();
      },
      error: (err) => {
        this.alert.error('Failed !', 'Something went wrong');
      },
    });
  }
  update() {
    let frmData = this.frmRawMaterial.value;
    let formData = new FormData();
    formData.append('Group', frmData.Group);
    formData.append('Item', frmData.Item);
    formData.append('Description', frmData.Description);
    formData.append('Count', frmData.Count);
    formData.append('Color', frmData.Color);
    formData.append('Quality', frmData.Quality);
    formData.append('Details', frmData.Details);
    formData.append('uploadedFile', this.MaterialImage);

    this.services.updateRawMaterial(this.editId, formData).subscribe({
      next: (value) => {
        this.alert.success('success', 'Material has been update successfully');
        this.gets();
        this.clear();
      },
      error: (err) => {
        this.alert.error('Failed !', 'Something went wrong');
      },
    });
  }
  edit(id: string) {
    this.services.getRawMaterial(id).subscribe({
      next: (value) => {
        this.editId = id;
        this.isUpdated = true;
        this.frmRawMaterial.patchValue(value);
      },
    });
  }
  delete(id: string) {
    this.alert.confirm({}).then((confi:any)=>{
      if (confi.isConfirmed) {
        this.services.deleteRawMaterial(id).subscribe({
          next: (value) => {
            this.alert.success('success', 'Raw material has been deleted');
            this.gets();
          },
          error: (err) => {
            this.alert.error('Failed !', 'Something went wrong');
          },
        });
      }
    })
  }

  selectedImageUrl:any;
  openModal(imageUrl: string): void {
    this.selectedImageUrl = imageUrl;
    const modal = new bootstrap.Modal(document.getElementById('myModal'), {});
    modal.show();
  }
  clear() {
    this.isUpdated = false;
    this.frmRawMaterial.reset();
  }


}
