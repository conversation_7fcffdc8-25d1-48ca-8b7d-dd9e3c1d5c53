const mongoose = require('mongoose');

// Define a schema for border size history entries (MUST be before main schema)
const carpetBorderSizeEntrySchema = new mongoose.Schema({
  lastCarpetSizedate: { type: Date, required: true },
  weight: { type: String, required: true },
  yes: { type: String },
  no: { type: String },
  BottomBorder1: { type: String, required: true },
  BottomBorder2: { type: String, required: true },
  BottomBorder3: { type: String },
  TopBorder1: { type: String, required: true },
  TopBorder2: { type: String, required: true },
  TopBorder3: { type: String },
  leftBorder1: { type: String, required: true },
  leftBorder2: { type: String, required: true },
  leftBorder3: { type: String },
  rightBorder1: { type: String, required: true },
  rightBorder2: { type: String, required: true },
  rightBorder3: { type: String },
  halfBorder1: { type: String, required: true },
  halfBorder2: { type: String, required: true },
  halfBorder3: { type: String },
  groundBorder1: { type: String, required: true },
  groundBorder2: { type: String, required: true },
  groundBorder3: { type: String },
  boxArray: [{ type: String }],
  imagesArray: [{ type: String }], // store image URLs or base64 strings
  Remarks: { type: String },
  updatedAt: { type: Date, default: Date.now }
});

// Define a schema for history entries
const historyEntrySchema = new mongoose.Schema({
  // Fields for edit updates
  size: {
    type: String,
  },
  deduction: {
    type: Number,
  },
  // Additional calculated fields
  area: {
    type: String,
  },
  amount: {
    type: Number,
  },
  tds: {
    type: Number,
  },
  commission: {
    type: Number,
  },
  netAmount: {
    type: Number,
  },
  // Fields for payment updates
  paymentDt: {
    type: Date,
  },
  remarks: {
    type: String,
  },
  bankName: {
    type: String,
  },
  chequeNoOrRGSno: {
    type: String,
  },
  payedAmount: {
    type: Number,
  },
  // Timestamp for this history entry
  updatedAt: {
    type: Date,
    default: Date.now,
  }
});

const carpetReceivedUpdateSchema = new mongoose.Schema({
  carpetReceivedId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CarpetReceived',
    required: true,
  },
  // Store the complete carpet data
  carpetData: {
    type: mongoose.Schema.Types.Mixed,
    default: null,
  },
  // Track the last update type (for backward compatibility)
  lastUpdateType: {
    type: String,
    enum: ['edit', 'payment', 'border-size'], // Added 'border-size' to allowed values
    default: 'edit',
  },
  // Current values (most recent)
  // Fields for edit updates
  size: {
    type: String,
  },
  deduction: {
    type: Number,
  },
  // Additional calculated fields
  area: {
    type: String,
  },
  amount: {
    type: Number,
  },
  tds: {
    type: Number,
  },
  commission: {
    type: Number,
  },
  netAmount: {
    type: Number,
  },
  // Fields for payment updates
  paymentDt: {
    type: Date,
  },
  remarks: {
    type: String,
  },
  bankName: {
    type: String,
  },
  chequeNoOrRGSno: {
    type: String,
  },
  payedAmount: {
    type: Number,
  },
  // Border size fields (added directly)
  lastCarpetSizedate: { type: Date },
  weight: { type: String },
  yes: { type: String },
  no: { type: String },
  BottomBorder1: { type: String },
  BottomBorder2: { type: String },
  BottomBorder3: { type: String },
  TopBorder1: { type: String },
  TopBorder2: { type: String },
  TopBorder3: { type: String },
  leftBorder1: { type: String },
  leftBorder2: { type: String },
  leftBorder3: { type: String },
  rightBorder1: { type: String },
  rightBorder2: { type: String },
  rightBorder3: { type: String },
  halfBorder1: { type: String },
  halfBorder2: { type: String },
  halfBorder3: { type: String },
  groundBorder1: { type: String },
  groundBorder2: { type: String },
  groundBorder3: { type: String },
  boxArray: [{ type: String }],
  imagesArray: [{ type: String }], // store image URLs or base64 strings
  Remarks: { type: String },
  // Tracking fields
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  // Last edit update timestamp
  lastEditAt: {
    type: Date,
    default: null,
  },
  // Last payment update timestamp
  lastPaymentAt: {
    type: Date,
    default: null,
  },
  // History array to store all previous values
  editHistory: [historyEntrySchema],
  paymentHistory: [historyEntrySchema],
  carpetBorderSizeHistory: [carpetBorderSizeEntrySchema]
});

// Add index for faster queries
carpetReceivedUpdateSchema.index({ carpetReceivedId: 1, lastUpdateType: 1, updatedAt: -1 });

module.exports = mongoose.model('CarpetReceivedUpdate', carpetReceivedUpdateSchema);