// repositories/deyingRate-repository.js
const deyingRate = require('../model/phase-3/deyingRate');

async function createDeyingRate(data) {
  return await deyingRate.create(data);
}

async function getDeyingRateById(id) {

  return await deyingRate.findById(id) 
}


async function getAllDeyingRates() {
  return await deyingRate.find()
    .populate('quality')
    .populate('material')
    .populate('color')
    .populate('yarnColor')
    .populate('name');
}

async function updateDeyingRate(id, data) {
  return await deyingRate.findByIdAndUpdate(id, data, { new: true });
}

async function deleteDeyingRate(id) {
  return await deyingRate.findByIdAndDelete(id);
}

module.exports = {
  createDeyingRate,
  getDeyingRateById,
  getAllDeyingRates,
  updateDeyingRate,
  deleteDeyingRate
};
