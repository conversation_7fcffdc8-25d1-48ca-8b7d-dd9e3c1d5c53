const mongoose = require("mongoose");

const buyerOrderSchema = new mongoose.Schema(
  {

    buyerName: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Buyer',
          required: true
        },
        orderNo: {
          type: String,
          required: true,
        },
        companyOrderNo: {
          type: String,
          required: true,
        },
        orderDate: {
          type: Date,
          required: true,
        },
        shippingDate: {
          type: Date,
          required: true,
        },
        orderType: {
          type: String,
          required: true,
        },
        customerOrder: {
          type: String,
          required: true,
        },
        // weavingUnit: {
        //   type: String,
        //   required: true,
        // },
        priority: {
          type: String,
          required: true,
        },
        area: {
          type: String,
          required: true,
        },
        items: [
          {
            quality: {
              type: mongoose.Schema.Types.ObjectId,
              ref: 'AddQuality',
              required: true,
            },
            design: {
              type: mongoose.Schema.Types.ObjectId,
              ref:'AddDesign',
              required: true,
            },
            groundColour: {
              type: String,
              required: true,
            },
            sizeId: {
              type: mongoose.Schema.Types.ObjectId,
              ref: 'SizeMaster',
              required: true,
            },
            khapSize: {
              type: String,
              required: true,
            },
            pcs: {
              type: Number,
              required: true,
            },
            PcsesPending : {
              type: Number,
              required: true,
              default: 0
            },
             PcsesAssigned  : {
              type: Number,
              required: true,
              default: 0
            },
            totalArea: {
              type: Number,
              required: true,
            },
            type: {
              type: String,
              required: true,
            },
            // amount: {
            //   type: String,

            // },

          }
        ]

  },
  {
    autoCreate: true,
    timestamps: true,
  }
);

module.exports = mongoose.model("BuyerOrder", buyerOrderSchema);
