/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=function(t){return t&&t.Math==Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u={},c=function(t){try{return!!t()}catch(t){return!0}},a=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,p=l?s.bind(s):function(){return s.apply(s,arguments)},g={},y={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,d=b&&!y.call({1:2},1);g.f=d?function(t){var n=b(this,t);return!!n&&n.enumerable}:y;var h,m,v=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},S=f,w=Function.prototype,j=w.call,O=S&&w.bind.bind(j,j),P=function(t){return S?O(t):function(){return j.apply(t,arguments)}},T=P,x=T({}.toString),A=T("".slice),E=function(t){return A(x(t),8,-1)},C=E,F=P,R=function(t){if("Function"===C(t))return F(t)},M=c,k=E,z=Object,D=R("".split),I=M((function(){return!z("z").propertyIsEnumerable(0)}))?function(t){return"String"==k(t)?D(t,""):z(t)}:z,L=function(t){return null==t},H=L,N=TypeError,_=function(t){if(H(t))throw N("Can't call method on "+t);return t},G=I,q=_,B=function(t){return G(q(t))},U="object"==typeof document&&document.all,W={all:U,IS_HTMLDDA:void 0===U&&void 0!==U},K=W.all,J=W.IS_HTMLDDA?function(t){return"function"==typeof t||t===K}:function(t){return"function"==typeof t},Q=J,V=W.all,X=W.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===V}:function(t){return"object"==typeof t?null!==t:Q(t)},Y=i,$=J,Z=function(t){return $(t)?t:void 0},tt=function(t,n){return arguments.length<2?Z(Y[t]):Y[t]&&Y[t][n]},nt=R({}.isPrototypeOf),rt=i,et=tt("navigator","userAgent")||"",ot=rt.process,it=rt.Deno,ut=ot&&ot.versions||it&&it.version,ct=ut&&ut.v8;ct&&(m=(h=ct.split("."))[0]>0&&h[0]<4?1:+(h[0]+h[1])),!m&&et&&(!(h=et.match(/Edge\/(\d+)/))||h[1]>=74)&&(h=et.match(/Chrome\/(\d+)/))&&(m=+h[1]);var at=m,ft=at,lt=c,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,yt=J,bt=nt,dt=Object,ht=pt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return yt(n)&&bt(n.prototype,dt(t))},mt=String,vt=J,St=function(t){try{return mt(t)}catch(t){return"Object"}},wt=TypeError,jt=function(t){if(vt(t))return t;throw wt(St(t)+" is not a function")},Ot=L,Pt=p,Tt=J,xt=X,At=TypeError,Et={exports:{}},Ct=i,Ft=Object.defineProperty,Rt=function(t,n){try{Ft(Ct,t,{value:n,configurable:!0,writable:!0})}catch(r){Ct[t]=n}return n},Mt=Rt,kt="__core-js_shared__",zt=i[kt]||Mt(kt,{}),Dt=zt;(Et.exports=function(t,n){return Dt[t]||(Dt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var It=_,Lt=Object,Ht=function(t){return Lt(It(t))},Nt=Ht,_t=R({}.hasOwnProperty),Gt=Object.hasOwn||function(t,n){return _t(Nt(t),n)},qt=R,Bt=0,Ut=Math.random(),Wt=qt(1..toString),Kt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Wt(++Bt+Ut,36)},Jt=i,Qt=Et.exports,Vt=Gt,Xt=Kt,Yt=st,$t=pt,Zt=Qt("wks"),tn=Jt.Symbol,nn=tn&&tn.for,rn=$t?tn:tn&&tn.withoutSetter||Xt,en=function(t){if(!Vt(Zt,t)||!Yt&&"string"!=typeof Zt[t]){var n="Symbol."+t;Yt&&Vt(tn,t)?Zt[t]=tn[t]:Zt[t]=$t&&nn?nn(n):rn(n)}return Zt[t]},on=p,un=X,cn=ht,an=function(t,n){var r=t[n];return Ot(r)?void 0:jt(r)},fn=function(t,n){var r,e;if("string"===n&&Tt(r=t.toString)&&!xt(e=Pt(r,t)))return e;if(Tt(r=t.valueOf)&&!xt(e=Pt(r,t)))return e;if("string"!==n&&Tt(r=t.toString)&&!xt(e=Pt(r,t)))return e;throw At("Can't convert object to primitive value")},ln=TypeError,sn=en("toPrimitive"),pn=function(t,n){if(!un(t)||cn(t))return t;var r,e=an(t,sn);if(e){if(void 0===n&&(n="default"),r=on(e,t,n),!un(r)||cn(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=ht,yn=function(t){var n=pn(t,"string");return gn(n)?n:n+""},bn=X,dn=i.document,hn=bn(dn)&&bn(dn.createElement),mn=function(t){return hn?dn.createElement(t):{}},vn=!a&&!c((function(){return 7!=Object.defineProperty(mn("div"),"a",{get:function(){return 7}}).a})),Sn=a,wn=p,jn=g,On=v,Pn=B,Tn=yn,xn=Gt,An=vn,En=Object.getOwnPropertyDescriptor;u.f=Sn?En:function(t,n){if(t=Pn(t),n=Tn(n),An)try{return En(t,n)}catch(t){}if(xn(t,n))return On(!wn(jn.f,t,n),t[n])};var Cn={},Fn=a&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Rn=X,Mn=String,kn=TypeError,zn=function(t){if(Rn(t))return t;throw kn(Mn(t)+" is not an object")},Dn=a,In=vn,Ln=Fn,Hn=zn,Nn=yn,_n=TypeError,Gn=Object.defineProperty,qn=Object.getOwnPropertyDescriptor,Bn="enumerable",Un="configurable",Wn="writable";Cn.f=Dn?Ln?function(t,n,r){if(Hn(t),n=Nn(n),Hn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Wn in r&&!r.writable){var e=qn(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Un in r?r.configurable:e.configurable,enumerable:Bn in r?r.enumerable:e.enumerable,writable:!1})}return Gn(t,n,r)}:Gn:function(t,n,r){if(Hn(t),n=Nn(n),Hn(r),In)try{return Gn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw _n("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var Kn=Cn,Jn=v,Qn=a?function(t,n,r){return Kn.f(t,n,Jn(1,r))}:function(t,n,r){return t[n]=r,t},Vn={exports:{}},Xn=a,Yn=Gt,$n=Function.prototype,Zn=Xn&&Object.getOwnPropertyDescriptor,tr=Yn($n,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Xn||Xn&&Zn($n,"name").configurable)},rr=J,er=zt,or=R(Function.toString);rr(er.inspectSource)||(er.inspectSource=function(t){return or(t)});var ir,ur,cr,ar=er.inspectSource,fr=J,lr=i.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),pr=Et.exports,gr=Kt,yr=pr("keys"),br={},dr=sr,hr=i,mr=X,vr=Qn,Sr=Gt,wr=zt,jr=function(t){return yr[t]||(yr[t]=gr(t))},Or=br,Pr="Object already initialized",Tr=hr.TypeError,xr=hr.WeakMap;if(dr||wr.state){var Ar=wr.state||(wr.state=new xr);Ar.get=Ar.get,Ar.has=Ar.has,Ar.set=Ar.set,ir=function(t,n){if(Ar.has(t))throw Tr(Pr);return n.facade=t,Ar.set(t,n),n},ur=function(t){return Ar.get(t)||{}},cr=function(t){return Ar.has(t)}}else{var Er=jr("state");Or[Er]=!0,ir=function(t,n){if(Sr(t,Er))throw Tr(Pr);return n.facade=t,vr(t,Er,n),n},ur=function(t){return Sr(t,Er)?t[Er]:{}},cr=function(t){return Sr(t,Er)}}var Cr={set:ir,get:ur,has:cr,enforce:function(t){return cr(t)?ur(t):ir(t,{})},getterFor:function(t){return function(n){var r;if(!mr(n)||(r=ur(n)).type!==t)throw Tr("Incompatible receiver, "+t+" required");return r}}},Fr=c,Rr=J,Mr=Gt,kr=a,zr=nr.CONFIGURABLE,Dr=ar,Ir=Cr.enforce,Lr=Cr.get,Hr=Object.defineProperty,Nr=kr&&!Fr((function(){return 8!==Hr((function(){}),"length",{value:8}).length})),_r=String(String).split("String"),Gr=Vn.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Mr(t,"name")||zr&&t.name!==n)&&(kr?Hr(t,"name",{value:n,configurable:!0}):t.name=n),Nr&&r&&Mr(r,"arity")&&t.length!==r.arity&&Hr(t,"length",{value:r.arity});try{r&&Mr(r,"constructor")&&r.constructor?kr&&Hr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Ir(t);return Mr(e,"source")||(e.source=_r.join("string"==typeof n?n:"")),t};Function.prototype.toString=Gr((function(){return Rr(this)&&Lr(this).source||Dr(this)}),"toString");var qr=J,Br=Cn,Ur=Vn.exports,Wr=Rt,Kr={},Jr=Math.ceil,Qr=Math.floor,Vr=Math.trunc||function(t){var n=+t;return(n>0?Qr:Jr)(n)},Xr=function(t){var n=+t;return n!=n||0===n?0:Vr(n)},Yr=Xr,$r=Math.max,Zr=Math.min,te=Xr,ne=Math.min,re=function(t){return t>0?ne(te(t),9007199254740991):0},ee=function(t){return re(t.length)},oe=B,ie=function(t,n){var r=Yr(t);return r<0?$r(r+n,0):Zr(r,n)},ue=ee,ce=function(t){return function(n,r,e){var o,i=oe(n),u=ue(i),c=ie(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},ae={includes:ce(!0),indexOf:ce(!1)},fe=Gt,le=B,se=ae.indexOf,pe=br,ge=R([].push),ye=function(t,n){var r,e=le(t),o=0,i=[];for(r in e)!fe(pe,r)&&fe(e,r)&&ge(i,r);for(;n.length>o;)fe(e,r=n[o++])&&(~se(i,r)||ge(i,r));return i},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Kr.f=Object.getOwnPropertyNames||function(t){return ye(t,be)};var de={};de.f=Object.getOwnPropertySymbols;var he=tt,me=Kr,ve=de,Se=zn,we=R([].concat),je=he("Reflect","ownKeys")||function(t){var n=me.f(Se(t)),r=ve.f;return r?we(n,r(t)):n},Oe=Gt,Pe=je,Te=u,xe=Cn,Ae=c,Ee=J,Ce=/#|\.prototype\./,Fe=function(t,n){var r=Me[Re(t)];return r==ze||r!=ke&&(Ee(n)?Ae(n):!!n)},Re=Fe.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Me=Fe.data={},ke=Fe.NATIVE="N",ze=Fe.POLYFILL="P",De=Fe,Ie=i,Le=u.f,He=Qn,Ne=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(qr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},_e=Rt,Ge=function(t,n,r){for(var e=Pe(n),o=xe.f,i=Te.f,u=0;u<e.length;u++){var c=e[u];Oe(t,c)||r&&Oe(r,c)||o(t,c,i(n,c))}},qe=De,Be=E,Ue=Array.isArray||function(t){return"Array"==Be(t)},We=TypeError,Ke=yn,Je=Cn,Qe=v,Ve={};Ve[en("toStringTag")]="z";var Xe="[object z]"===String(Ve),Ye=J,$e=E,Ze=en("toStringTag"),to=Object,no="Arguments"==$e(function(){return arguments}()),ro=R,eo=c,oo=J,io=Xe?$e:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=to(t),Ze))?r:no?$e(n):"Object"==(e=$e(n))&&Ye(n.callee)?"Arguments":e},uo=ar,co=function(){},ao=[],fo=tt("Reflect","construct"),lo=/^\s*(?:class|function)\b/,so=ro(lo.exec),po=!lo.exec(co),go=function(t){if(!oo(t))return!1;try{return fo(co,ao,t),!0}catch(t){return!1}},yo=function(t){if(!oo(t))return!1;switch(io(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return po||!!so(lo,uo(t))}catch(t){return!0}};yo.sham=!0;var bo=!fo||eo((function(){var t;return go(go.call)||!go(Object)||!go((function(){t=!0}))||t}))?yo:go,ho=Ue,mo=bo,vo=X,So=en("species"),wo=Array,jo=function(t){var n;return ho(t)&&(n=t.constructor,(mo(n)&&(n===wo||ho(n.prototype))||vo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?wo:n},Oo=c,Po=at,To=en("species"),xo=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Ie:f?Ie[c]||_e(c,{}):(Ie[c]||{}).prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=Le(r,e))&&u.value:r[e],!qe(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&He(i,"sham",!0),Ne(r,e,i,t)}},Ao=c,Eo=Ue,Co=X,Fo=Ht,Ro=ee,Mo=function(t){if(t>9007199254740991)throw We("Maximum allowed index exceeded");return t},ko=function(t,n,r){var e=Ke(n);e in t?Je.f(t,e,Qe(0,r)):t[e]=r},zo=function(t,n){return new(jo(t))(0===n?0:n)},Do=function(t){return Po>=51||!Oo((function(){var n=[];return(n.constructor={})[To]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Io=at,Lo=en("isConcatSpreadable"),Ho=Io>=51||!Ao((function(){var t=[];return t[Lo]=!1,t.concat()[0]!==t})),No=Do("concat"),_o=function(t){if(!Co(t))return!1;var n=t[Lo];return void 0!==n?!!n:Eo(t)};xo({target:"Array",proto:!0,arity:1,forced:!Ho||!No},{concat:function(t){var n,r,e,o,i,u=Fo(this),c=zo(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(_o(i=-1===n?u:arguments[n]))for(o=Ro(i),Mo(a+o),r=0;r<o;r++,a++)r in i&&ko(c,a,i[r]);else Mo(a+1),ko(c,a++,i);return c.length=a,c}}),r.default.fn.bootstrapTable.locales["hr-HR"]=r.default.fn.bootstrapTable.locales.hr={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Molimo pričekajte"},formatRecordsPerPage:function(t){return"".concat(t," broj zapisa po stranici")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Prikazujem ".concat(t,". - ").concat(n,". od ukupnog broja zapisa ").concat(r," (filtered from ").concat(e," total rows)"):"Prikazujem ".concat(t,". - ").concat(n,". od ukupnog broja zapisa ").concat(r)},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Pretraži"},formatNoMatches:function(){return"Nije pronađen niti jedan zapis"},formatPaginationSwitch:function(){return"Prikaži/sakrij stranice"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Osvježi"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolone"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Sve"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["hr-HR"])}));
