// services/buyerService.js
const buyerRepository = require('../repositories/masterBuyer-repository');

async function getAllBuyers() {
    return await buyerRepository.getAllBuyers();
}

async function getBuyerById(id) {
    return await buyerRepository.getBuyerById(id);
}

async function createBuyer(buyerData) {
    return await buyerRepository.createBuyer(buyerData);
}

async function updateBuyer(id, newData) {
    return await buyerRepository.updateBuyer(id, newData);
}

async function deleteBuyer(id) {
    return await buyerRepository.deleteBuyer(id);
}

module.exports = {
    getAllBuyers,
    getBuyerById,
    createBuyer,
    updateBuyer,
    deleteBuyer
};
