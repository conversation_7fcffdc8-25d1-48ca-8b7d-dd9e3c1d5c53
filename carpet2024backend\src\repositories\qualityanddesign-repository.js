// qualityRepository.js

const QualityAndDesign = require('../model/phase-2/qualityanddesign');

class QualityAndDesignRepository {
  async createQualityAndDesign(qualityAndDesignData) {
    try {
      return await QualityAndDesign.create(qualityAndDesignData);
    } catch (error) {
      throw error;
    }
  }

  async getQualityAndDesignById(qualityAndDesignId) {
    try {
      return await QualityAndDesign.findById(qualityAndDesignId);
    } catch (error) {
      throw error;
    }
  }

  async getAllQualityAndDesigns() {
    try {
      return await QualityAndDesign.find();
    } catch (error) {
      throw error;
    }
  }

  async updateQualityAndDesign(qualityAndDesignId, qualityAndDesignData) {
    try {
      return await QualityAndDesign.findByIdAndUpdate(qualityAndDesignId, qualityAndDesignData, { new: true });
    } catch (error) {
      throw error;
    }
  }

  async deleteQualityAndDesign(qualityAndDesignId) {
    try {
      return await QualityAndDesign.findByIdAndDelete(qualityAndDesignId);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new QualityAndDesignRepository();
