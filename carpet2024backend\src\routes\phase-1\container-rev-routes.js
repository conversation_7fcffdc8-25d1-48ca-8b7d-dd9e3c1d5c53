const express = require('express');
const router = express.Router();
//const loggerMiddleware = require('../../middleware/logger');
const { uploadPdf } = require('../../middleware/fileUpload-middleware');

const containerReceivedController = require('../../controller/container-rev-controller');


// Apply logger middleware to all routes
//router.use(loggerMiddleware);
const { logRequestResponse, logDataChanges } = require('../../middleware/logger'); // Import middleware functions

// Apply logger middleware to log request and response
// router.use(logRequestResponse);

// // Apply data changes logger middleware to log data updates, creations, deletions, and retrievals
// router.use(logDataChanges);
// Create
router.post('/upload-containerReceived',uploadPdf.single('billoflading'), containerReceivedController.createContainerReceived);

// Read
router.get('/get-containerReceived/:id', containerReceivedController.getContainerReceivedById);
router.get('/getAll-containerReceived', containerReceivedController.getAllContainerReceived);

// Update
router.put('/ContainerReceived/:id', containerReceivedController.updateContainerReceived);

// Delete
router.delete('/ContainerReceived/:id', containerReceivedController.deleteContainerReceived);
router.post('/carpet-stock-details', containerReceivedController.stockDetail);
router.post('/carpet-stk',containerReceivedController.getAllCarpetStorck);
let testing = require('../../controller/testing');
router.get('/get-histry',testing.getHistry);
router.get('/stock/complete',  containerReceivedController.getCompleteStockDetails); // New route


const soledController = require('../../controller/stockController');

//// all soled carpet 
router.get('/get-all-soleded', soledController.getsAllStock);
module.exports = router;
