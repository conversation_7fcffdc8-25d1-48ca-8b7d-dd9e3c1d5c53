/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),o="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},e=function(t){return t&&t.Math==Math&&t},a=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof o&&o)||function(){return this}()||Function("return this")(),i={},u=function(t){try{return!!t()}catch(t){return!0}},c=!u((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=!u((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=f,s=Function.prototype.call,m=l?s.bind(s):function(){return s.apply(s,arguments)},g={},d={}.propertyIsEnumerable,h=Object.getOwnPropertyDescriptor,p=h&&!d.call({1:2},1);g.f=p?function(t){var n=h(this,t);return!!n&&n.enumerable}:d;var S,w,P=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},T=f,R=Function.prototype,b=R.call,C=T&&R.bind.bind(b,b),v=function(t){return T?C(t):function(){return b.apply(t,arguments)}},A=v,x=A({}.toString),y=A("".slice),F=function(t){return y(x(t),8,-1)},O=F,k=v,M=function(t){if("Function"===O(t))return k(t)},H=u,N=F,E=Object,D=M("".split),z=H((function(){return!E("z").propertyIsEnumerable(0)}))?function(t){return"String"==N(t)?D(t,""):E(t)}:E,j=function(t){return null==t},L=j,B=TypeError,U=function(t){if(L(t))throw B("Can't call method on "+t);return t},Z=z,J=U,G=function(t){return Z(J(t))},I="object"==typeof document&&document.all,V={all:I,IS_HTMLDDA:void 0===I&&void 0!==I},q=V.all,K=V.IS_HTMLDDA?function(t){return"function"==typeof t||t===q}:function(t){return"function"==typeof t},W=K,_=V.all,Y=V.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:W(t)||t===_}:function(t){return"object"==typeof t?null!==t:W(t)},X=a,Q=K,$=function(t){return Q(t)?t:void 0},tt=function(t,n){return arguments.length<2?$(X[t]):X[t]&&X[t][n]},nt=M({}.isPrototypeOf),rt=a,ot=tt("navigator","userAgent")||"",et=rt.process,at=rt.Deno,it=et&&et.versions||at&&at.version,ut=it&&it.v8;ut&&(w=(S=ut.split("."))[0]>0&&S[0]<4?1:+(S[0]+S[1])),!w&&ot&&(!(S=ot.match(/Edge\/(\d+)/))||S[1]>=74)&&(S=ot.match(/Chrome\/(\d+)/))&&(w=+S[1]);var ct=w,ft=ct,lt=u,st=!!Object.getOwnPropertySymbols&&!lt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),mt=st&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=tt,dt=K,ht=nt,pt=Object,St=mt?function(t){return"symbol"==typeof t}:function(t){var n=gt("Symbol");return dt(n)&&ht(n.prototype,pt(t))},wt=String,Pt=K,Tt=function(t){try{return wt(t)}catch(t){return"Object"}},Rt=TypeError,bt=function(t){if(Pt(t))return t;throw Rt(Tt(t)+" is not a function")},Ct=j,vt=m,At=K,xt=Y,yt=TypeError,Ft={exports:{}},Ot=a,kt=Object.defineProperty,Mt=function(t,n){try{kt(Ot,t,{value:n,configurable:!0,writable:!0})}catch(r){Ot[t]=n}return n},Ht=Mt,Nt="__core-js_shared__",Et=a[Nt]||Ht(Nt,{}),Dt=Et;(Ft.exports=function(t,n){return Dt[t]||(Dt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var zt=U,jt=Object,Lt=function(t){return jt(zt(t))},Bt=Lt,Ut=M({}.hasOwnProperty),Zt=Object.hasOwn||function(t,n){return Ut(Bt(t),n)},Jt=M,Gt=0,It=Math.random(),Vt=Jt(1..toString),qt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Vt(++Gt+It,36)},Kt=a,Wt=Ft.exports,_t=Zt,Yt=qt,Xt=st,Qt=mt,$t=Wt("wks"),tn=Kt.Symbol,nn=tn&&tn.for,rn=Qt?tn:tn&&tn.withoutSetter||Yt,on=function(t){if(!_t($t,t)||!Xt&&"string"!=typeof $t[t]){var n="Symbol."+t;Xt&&_t(tn,t)?$t[t]=tn[t]:$t[t]=Qt&&nn?nn(n):rn(n)}return $t[t]},en=m,an=Y,un=St,cn=function(t,n){var r=t[n];return Ct(r)?void 0:bt(r)},fn=function(t,n){var r,o;if("string"===n&&At(r=t.toString)&&!xt(o=vt(r,t)))return o;if(At(r=t.valueOf)&&!xt(o=vt(r,t)))return o;if("string"!==n&&At(r=t.toString)&&!xt(o=vt(r,t)))return o;throw yt("Can't convert object to primitive value")},ln=TypeError,sn=on("toPrimitive"),mn=function(t,n){if(!an(t)||un(t))return t;var r,o=cn(t,sn);if(o){if(void 0===n&&(n="default"),r=en(o,t,n),!an(r)||un(r))return r;throw ln("Can't convert object to primitive value")}return void 0===n&&(n="number"),fn(t,n)},gn=St,dn=function(t){var n=mn(t,"string");return gn(n)?n:n+""},hn=Y,pn=a.document,Sn=hn(pn)&&hn(pn.createElement),wn=function(t){return Sn?pn.createElement(t):{}},Pn=!c&&!u((function(){return 7!=Object.defineProperty(wn("div"),"a",{get:function(){return 7}}).a})),Tn=c,Rn=m,bn=g,Cn=P,vn=G,An=dn,xn=Zt,yn=Pn,Fn=Object.getOwnPropertyDescriptor;i.f=Tn?Fn:function(t,n){if(t=vn(t),n=An(n),yn)try{return Fn(t,n)}catch(t){}if(xn(t,n))return Cn(!Rn(bn.f,t,n),t[n])};var On={},kn=c&&u((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mn=Y,Hn=String,Nn=TypeError,En=function(t){if(Mn(t))return t;throw Nn(Hn(t)+" is not an object")},Dn=c,zn=Pn,jn=kn,Ln=En,Bn=dn,Un=TypeError,Zn=Object.defineProperty,Jn=Object.getOwnPropertyDescriptor,Gn="enumerable",In="configurable",Vn="writable";On.f=Dn?jn?function(t,n,r){if(Ln(t),n=Bn(n),Ln(r),"function"==typeof t&&"prototype"===n&&"value"in r&&Vn in r&&!r.writable){var o=Jn(t,n);o&&o.writable&&(t[n]=r.value,r={configurable:In in r?r.configurable:o.configurable,enumerable:Gn in r?r.enumerable:o.enumerable,writable:!1})}return Zn(t,n,r)}:Zn:function(t,n,r){if(Ln(t),n=Bn(n),Ln(r),zn)try{return Zn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Un("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var qn=On,Kn=P,Wn=c?function(t,n,r){return qn.f(t,n,Kn(1,r))}:function(t,n,r){return t[n]=r,t},_n={exports:{}},Yn=c,Xn=Zt,Qn=Function.prototype,$n=Yn&&Object.getOwnPropertyDescriptor,tr=Xn(Qn,"name"),nr={EXISTS:tr,PROPER:tr&&"something"===function(){}.name,CONFIGURABLE:tr&&(!Yn||Yn&&$n(Qn,"name").configurable)},rr=K,or=Et,er=M(Function.toString);rr(or.inspectSource)||(or.inspectSource=function(t){return er(t)});var ar,ir,ur,cr=or.inspectSource,fr=K,lr=a.WeakMap,sr=fr(lr)&&/native code/.test(String(lr)),mr=Ft.exports,gr=qt,dr=mr("keys"),hr={},pr=sr,Sr=a,wr=Y,Pr=Wn,Tr=Zt,Rr=Et,br=function(t){return dr[t]||(dr[t]=gr(t))},Cr=hr,vr="Object already initialized",Ar=Sr.TypeError,xr=Sr.WeakMap;if(pr||Rr.state){var yr=Rr.state||(Rr.state=new xr);yr.get=yr.get,yr.has=yr.has,yr.set=yr.set,ar=function(t,n){if(yr.has(t))throw Ar(vr);return n.facade=t,yr.set(t,n),n},ir=function(t){return yr.get(t)||{}},ur=function(t){return yr.has(t)}}else{var Fr=br("state");Cr[Fr]=!0,ar=function(t,n){if(Tr(t,Fr))throw Ar(vr);return n.facade=t,Pr(t,Fr,n),n},ir=function(t){return Tr(t,Fr)?t[Fr]:{}},ur=function(t){return Tr(t,Fr)}}var Or={set:ar,get:ir,has:ur,enforce:function(t){return ur(t)?ir(t):ar(t,{})},getterFor:function(t){return function(n){var r;if(!wr(n)||(r=ir(n)).type!==t)throw Ar("Incompatible receiver, "+t+" required");return r}}},kr=u,Mr=K,Hr=Zt,Nr=c,Er=nr.CONFIGURABLE,Dr=cr,zr=Or.enforce,jr=Or.get,Lr=Object.defineProperty,Br=Nr&&!kr((function(){return 8!==Lr((function(){}),"length",{value:8}).length})),Ur=String(String).split("String"),Zr=_n.exports=function(t,n,r){"Symbol("===String(n).slice(0,7)&&(n="["+String(n).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Hr(t,"name")||Er&&t.name!==n)&&(Nr?Lr(t,"name",{value:n,configurable:!0}):t.name=n),Br&&r&&Hr(r,"arity")&&t.length!==r.arity&&Lr(t,"length",{value:r.arity});try{r&&Hr(r,"constructor")&&r.constructor?Nr&&Lr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var o=zr(t);return Hr(o,"source")||(o.source=Ur.join("string"==typeof n?n:"")),t};Function.prototype.toString=Zr((function(){return Mr(this)&&jr(this).source||Dr(this)}),"toString");var Jr=K,Gr=On,Ir=_n.exports,Vr=Mt,qr={},Kr=Math.ceil,Wr=Math.floor,_r=Math.trunc||function(t){var n=+t;return(n>0?Wr:Kr)(n)},Yr=function(t){var n=+t;return n!=n||0===n?0:_r(n)},Xr=Yr,Qr=Math.max,$r=Math.min,to=Yr,no=Math.min,ro=function(t){return t>0?no(to(t),9007199254740991):0},oo=function(t){return ro(t.length)},eo=G,ao=function(t,n){var r=Xr(t);return r<0?Qr(r+n,0):$r(r,n)},io=oo,uo=function(t){return function(n,r,o){var e,a=eo(n),i=io(a),u=ao(o,i);if(t&&r!=r){for(;i>u;)if((e=a[u++])!=e)return!0}else for(;i>u;u++)if((t||u in a)&&a[u]===r)return t||u||0;return!t&&-1}},co={includes:uo(!0),indexOf:uo(!1)},fo=Zt,lo=G,so=co.indexOf,mo=hr,go=M([].push),ho=function(t,n){var r,o=lo(t),e=0,a=[];for(r in o)!fo(mo,r)&&fo(o,r)&&go(a,r);for(;n.length>e;)fo(o,r=n[e++])&&(~so(a,r)||go(a,r));return a},po=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");qr.f=Object.getOwnPropertyNames||function(t){return ho(t,po)};var So={};So.f=Object.getOwnPropertySymbols;var wo=tt,Po=qr,To=So,Ro=En,bo=M([].concat),Co=wo("Reflect","ownKeys")||function(t){var n=Po.f(Ro(t)),r=To.f;return r?bo(n,r(t)):n},vo=Zt,Ao=Co,xo=i,yo=On,Fo=u,Oo=K,ko=/#|\.prototype\./,Mo=function(t,n){var r=No[Ho(t)];return r==Do||r!=Eo&&(Oo(n)?Fo(n):!!n)},Ho=Mo.normalize=function(t){return String(t).replace(ko,".").toLowerCase()},No=Mo.data={},Eo=Mo.NATIVE="N",Do=Mo.POLYFILL="P",zo=Mo,jo=a,Lo=i.f,Bo=Wn,Uo=function(t,n,r,o){o||(o={});var e=o.enumerable,a=void 0!==o.name?o.name:n;if(Jr(r)&&Ir(r,a,o),o.global)e?t[n]=r:Vr(n,r);else{try{o.unsafe?t[n]&&(e=!0):delete t[n]}catch(t){}e?t[n]=r:Gr.f(t,n,{value:r,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t},Zo=Mt,Jo=function(t,n,r){for(var o=Ao(n),e=yo.f,a=xo.f,i=0;i<o.length;i++){var u=o[i];vo(t,u)||r&&vo(r,u)||e(t,u,a(n,u))}},Go=zo,Io=F,Vo=Array.isArray||function(t){return"Array"==Io(t)},qo=TypeError,Ko=dn,Wo=On,_o=P,Yo={};Yo[on("toStringTag")]="z";var Xo="[object z]"===String(Yo),Qo=K,$o=F,te=on("toStringTag"),ne=Object,re="Arguments"==$o(function(){return arguments}()),oe=M,ee=u,ae=K,ie=Xo?$o:function(t){var n,r,o;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=ne(t),te))?r:re?$o(n):"Object"==(o=$o(n))&&Qo(n.callee)?"Arguments":o},ue=cr,ce=function(){},fe=[],le=tt("Reflect","construct"),se=/^\s*(?:class|function)\b/,me=oe(se.exec),ge=!se.exec(ce),de=function(t){if(!ae(t))return!1;try{return le(ce,fe,t),!0}catch(t){return!1}},he=function(t){if(!ae(t))return!1;switch(ie(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return ge||!!me(se,ue(t))}catch(t){return!0}};he.sham=!0;var pe=!le||ee((function(){var t;return de(de.call)||!de(Object)||!de((function(){t=!0}))||t}))?he:de,Se=Vo,we=pe,Pe=Y,Te=on("species"),Re=Array,be=function(t){var n;return Se(t)&&(n=t.constructor,(we(n)&&(n===Re||Se(n.prototype))||Pe(n)&&null===(n=n[Te]))&&(n=void 0)),void 0===n?Re:n},Ce=u,ve=ct,Ae=on("species"),xe=function(t,n){var r,o,e,a,i,u=t.target,c=t.global,f=t.stat;if(r=c?jo:f?jo[u]||Zo(u,{}):(jo[u]||{}).prototype)for(o in n){if(a=n[o],e=t.dontCallGetSet?(i=Lo(r,o))&&i.value:r[o],!Go(c?o:u+(f?".":"#")+o,t.forced)&&void 0!==e){if(typeof a==typeof e)continue;Jo(a,e)}(t.sham||e&&e.sham)&&Bo(a,"sham",!0),Uo(r,o,a,t)}},ye=u,Fe=Vo,Oe=Y,ke=Lt,Me=oo,He=function(t){if(t>9007199254740991)throw qo("Maximum allowed index exceeded");return t},Ne=function(t,n,r){var o=Ko(n);o in t?Wo.f(t,o,_o(0,r)):t[o]=r},Ee=function(t,n){return new(be(t))(0===n?0:n)},De=function(t){return ve>=51||!Ce((function(){var n=[];return(n.constructor={})[Ae]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},ze=ct,je=on("isConcatSpreadable"),Le=ze>=51||!ye((function(){var t=[];return t[je]=!1,t.concat()[0]!==t})),Be=De("concat"),Ue=function(t){if(!Oe(t))return!1;var n=t[je];return void 0!==n?!!n:Fe(t)};xe({target:"Array",proto:!0,arity:1,forced:!Le||!Be},{concat:function(t){var n,r,o,e,a,i=ke(this),u=Ee(i,0),c=0;for(n=-1,o=arguments.length;n<o;n++)if(Ue(a=-1===n?i:arguments[n]))for(e=Me(a),He(c+e),r=0;r<e;r++,c++)r in a&&Ne(u,c,a[r]);else He(c+1),Ne(u,c++,a);return u.length=c,u}}),r.default.fn.bootstrapTable.locales["cs-CZ"]=r.default.fn.bootstrapTable.locales.cs={formatCopyRows:function(){return"Kopírovat řádky"},formatPrint:function(){return"Tisk"},formatLoadingMessage:function(){return"Čekejte, prosím"},formatRecordsPerPage:function(t){return"".concat(t," položek na stránku")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Zobrazena ".concat(t,". - ").concat(n," . položka z celkových ").concat(r," (filtered from ").concat(o," total rows)"):"Zobrazena ".concat(t,". - ").concat(n," . položka z celkových ").concat(r)},formatSRPaginationPreText:function(){return"předchozí strana"},formatSRPaginationPageText:function(t){return"na stranu ".concat(t)},formatSRPaginationNextText:function(){return"další strana"},formatDetailPagination:function(t){return"Zobrazuji ".concat(t," řádek")},formatClearSearch:function(){return"Smazat hledání"},formatSearch:function(){return"Vyhledávání"},formatNoMatches:function(){return"Nenalezena žádná vyhovující položka"},formatPaginationSwitch:function(){return"Skrýt/Zobrazit stránkování"},formatPaginationSwitchDown:function(){return"Zobrazit stránkování"},formatPaginationSwitchUp:function(){return"Skrýt stránkování"},formatRefresh:function(){return"Aktualizovat"},formatToggleOn:function(){return"Zobrazit karty"},formatToggleOff:function(){return"Zobrazit tabulku"},formatColumns:function(){return"Sloupce"},formatColumnsToggleAll:function(){return"Zobrazit/Skrýt vše"},formatFullscreen:function(){return"Zapnout/Vypnout fullscreen"},formatAllRows:function(){return"Vše"},formatAutoRefresh:function(){return"Automatické obnovení"},formatExport:function(){return"Export dat"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Pokročilé hledání"},formatAdvancedCloseButton:function(){return"Zavřít"},formatFilterControlSwitch:function(){return"Skrýt/Zobrazit ovladače"},formatFilterControlSwitchHide:function(){return"Skrýt ovladače"},formatFilterControlSwitchShow:function(){return"Zobrazit ovladače"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["cs-CZ"]),r.default.fn.bootstrapTable.locales["da-DK"]=r.default.fn.bootstrapTable.locales.da={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Indlæser, vent venligst"},formatRecordsPerPage:function(t){return"".concat(t," poster pr side")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Viser ".concat(t," til ").concat(n," af ").concat(r," række").concat(r>1?"r":""," (filtered from ").concat(o," total rows)"):"Viser ".concat(t," til ").concat(n," af ").concat(r," række").concat(r>1?"r":"")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Viser ".concat(t," række").concat(t>1?"r":"")},formatClearSearch:function(){return"Ryd filtre"},formatSearch:function(){return"Søg"},formatNoMatches:function(){return"Ingen poster fundet"},formatPaginationSwitch:function(){return"Skjul/vis nummerering"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Opdater"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolonner"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Alle"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Eksporter"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["da-DK"]),r.default.fn.bootstrapTable.locales["ar-SA"]=r.default.fn.bootstrapTable.locales.ar={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"جاري التحميل, يرجى الإنتظار"},formatRecordsPerPage:function(t){return"".concat(t," سجل لكل صفحة")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"الظاهر ".concat(t," إلى ").concat(n," من ").concat(r," سجل ").concat(o," total rows)"):"الظاهر ".concat(t," إلى ").concat(n," من ").concat(r," سجل")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"بحث"},formatNoMatches:function(){return"لا توجد نتائج مطابقة للبحث"},formatPaginationSwitch:function(){return"إخفاءإظهار ترقيم الصفحات"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"تحديث"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"أعمدة"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ar-SA"]),r.default.fn.bootstrapTable.locales["ca-ES"]=r.default.fn.bootstrapTable.locales.ca={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Espereu, si us plau"},formatRecordsPerPage:function(t){return"".concat(t," resultats per pàgina")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Mostrant de ".concat(t," fins ").concat(n," - total ").concat(r," resultats (filtered from ").concat(o," total rows)"):"Mostrant de ".concat(t," fins ").concat(n," - total ").concat(r," resultats")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Cerca"},formatNoMatches:function(){return"No s'han trobat resultats"},formatPaginationSwitch:function(){return"Amaga/Mostra paginació"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresca"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columnes"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Tots"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ca-ES"]),r.default.fn.bootstrapTable.locales["bg-BG"]=r.default.fn.bootstrapTable.locales.bg={formatCopyRows:function(){return"Копиране на редове"},formatPrint:function(){return"Печат"},formatLoadingMessage:function(){return"Зареждане, моля изчакайте"},formatRecordsPerPage:function(t){return"".concat(t," реда на страница")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Показани редове от ".concat(t," до ").concat(n," от ").concat(r," (филтрирани от общо ").concat(o,")"):"Показани редове от ".concat(t," до ").concat(n," от общо ").concat(r)},formatSRPaginationPreText:function(){return"предишна страница"},formatSRPaginationPageText:function(t){return"до страница ".concat(t)},formatSRPaginationNextText:function(){return"следваща страница"},formatDetailPagination:function(t){return"Показани ".concat(t," реда")},formatClearSearch:function(){return"Изчистване на търсенето"},formatSearch:function(){return"Търсене"},formatNoMatches:function(){return"Не са намерени съвпадащи записи"},formatPaginationSwitch:function(){return"Скриване/Показване на странициране"},formatPaginationSwitchDown:function(){return"Показване на странициране"},formatPaginationSwitchUp:function(){return"Скриване на странициране"},formatRefresh:function(){return"Обновяване"},formatToggleOn:function(){return"Показване на изглед карта"},formatToggleOff:function(){return"Скриване на изглед карта"},formatColumns:function(){return"Колони"},formatColumnsToggleAll:function(){return"Превключване на всички"},formatFullscreen:function(){return"Цял екран"},formatAllRows:function(){return"Всички"},formatAutoRefresh:function(){return"Автоматично обновяване"},formatExport:function(){return"Експорт на данни"},formatJumpTo:function(){return"ОТИДИ"},formatAdvancedSearch:function(){return"Разширено търсене"},formatAdvancedCloseButton:function(){return"Затваряне"},formatFilterControlSwitch:function(){return"Скрива/показва контроли"},formatFilterControlSwitchHide:function(){return"Скрива контроли"},formatFilterControlSwitchShow:function(){return"Показва контроли"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["bg-BG"]),r.default.fn.bootstrapTable.locales["de-DE"]=r.default.fn.bootstrapTable.locales.de={formatCopyRows:function(){return"Zeilen kopieren"},formatPrint:function(){return"Drucken"},formatLoadingMessage:function(){return"Lade, bitte warten"},formatRecordsPerPage:function(t){return"".concat(t," Zeilen pro Seite.")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Zeige Zeile ".concat(t," bis ").concat(n," von ").concat(r," Zeile").concat(r>1?"n":""," (Gefiltert von ").concat(o," Zeile").concat(o>1?"n":"",")"):"Zeige Zeile ".concat(t," bis ").concat(n," von ").concat(r," Zeile").concat(r>1?"n":"",".")},formatSRPaginationPreText:function(){return"Vorherige Seite"},formatSRPaginationPageText:function(t){return"Zu Seite ".concat(t)},formatSRPaginationNextText:function(){return"Nächste Seite"},formatDetailPagination:function(t){return"Zeige ".concat(t," Zeile").concat(t>1?"n":"",".")},formatClearSearch:function(){return"Lösche Filter"},formatSearch:function(){return"Suchen"},formatNoMatches:function(){return"Keine passenden Ergebnisse gefunden"},formatPaginationSwitch:function(){return"Verstecke/Zeige Nummerierung"},formatPaginationSwitchDown:function(){return"Zeige Nummerierung"},formatPaginationSwitchUp:function(){return"Verstecke Nummerierung"},formatRefresh:function(){return"Neu laden"},formatToggleOn:function(){return"Normale Ansicht"},formatToggleOff:function(){return"Kartenansicht"},formatColumns:function(){return"Spalten"},formatColumnsToggleAll:function(){return"Alle umschalten"},formatFullscreen:function(){return"Vollbild"},formatAllRows:function(){return"Alle"},formatAutoRefresh:function(){return"Automatisches Neuladen"},formatExport:function(){return"Datenexport"},formatJumpTo:function(){return"Springen"},formatAdvancedSearch:function(){return"Erweiterte Suche"},formatAdvancedCloseButton:function(){return"Schließen"},formatFilterControlSwitch:function(){return"Verstecke/Zeige Filter"},formatFilterControlSwitchHide:function(){return"Verstecke Filter"},formatFilterControlSwitchShow:function(){return"Zeige Filter"},formatAddLevel:function(){return"Ebene hinzufügen"},formatCancel:function(){return"Abbrechen"},formatColumn:function(){return"Spalte"},formatDeleteLevel:function(){return"Ebene entfernen"},formatDuplicateAlertTitle:function(){return"Doppelte Einträge gefunden!"},formatDuplicateAlertDescription:function(){return"Bitte doppelte Spalten entfenen oder ändern"},formatMultipleSort:function(){return"Mehrfachsortierung"},formatOrder:function(){return"Reihenfolge"},formatSort:function(){return"Sortieren"},formatSortBy:function(){return"Sortieren nach"},formatThenBy:function(){return"anschließend"},formatSortOrders:function(){return{asc:"Aufsteigend",desc:"Absteigend"}}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["de-DE"]),r.default.fn.bootstrapTable.locales["el-GR"]=r.default.fn.bootstrapTable.locales.el={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Φορτώνει, παρακαλώ περιμένετε"},formatRecordsPerPage:function(t){return"".concat(t," αποτελέσματα ανά σελίδα")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Εμφανίζονται από την ".concat(t," ως την ").concat(n," από σύνολο ").concat(r," σειρών (filtered from ").concat(o," total rows)"):"Εμφανίζονται από την ".concat(t," ως την ").concat(n," από σύνολο ").concat(r," σειρών")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Αναζητήστε"},formatNoMatches:function(){return"Δεν βρέθηκαν αποτελέσματα"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["el-GR"]),r.default.fn.bootstrapTable.locales["en-US"]=r.default.fn.bootstrapTable.locales.en={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(t){return"".concat(t," rows per page")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Showing ".concat(t," to ").concat(n," of ").concat(r," rows (filtered from ").concat(o," total rows)"):"Showing ".concat(t," to ").concat(n," of ").concat(r," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["en-US"]),r.default.fn.bootstrapTable.locales["es-AR"]={formatCopyRows:function(){return"Copiar Filas"},formatPrint:function(){return"Imprimir"},formatLoadingMessage:function(){return"Cargando, espere por favor"},formatRecordsPerPage:function(t){return"".concat(t," registros por página")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Mostrando desde ".concat(t," a ").concat(n," de ").concat(r," filas (filtrado de ").concat(o," columnas totales)"):"Mostrando desde ".concat(t," a ").concat(n," de ").concat(r," filas")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"a la página ".concat(t)},formatSRPaginationNextText:function(){return"siguiente página"},formatDetailPagination:function(t){return"Mostrando ".concat(t," columnas")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatPaginationSwitch:function(){return"Ocultar/Mostrar paginación"},formatPaginationSwitchDown:function(){return"Mostrar paginación"},formatPaginationSwitchUp:function(){return"Ocultar paginación"},formatRefresh:function(){return"Recargar"},formatToggleOn:function(){return"Mostrar vista de carta"},formatToggleOff:function(){return"Ocultar vista de carta"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Cambiar todo"},formatFullscreen:function(){return"Pantalla completa"},formatAllRows:function(){return"Todo"},formatAutoRefresh:function(){return"Auto Recargar"},formatExport:function(){return"Exportar datos"},formatJumpTo:function(){return"Ir"},formatAdvancedSearch:function(){return"Búsqueda avanzada"},formatAdvancedCloseButton:function(){return"Cerrar"},formatFilterControlSwitch:function(){return"Ocultar/Mostrar controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Mostrar controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["es-AR"]),r.default.fn.bootstrapTable.locales["es-CL"]={formatCopyRows:function(){return"Copiar Filas"},formatPrint:function(){return"Imprimir"},formatLoadingMessage:function(){return"Cargando, espere por favor"},formatRecordsPerPage:function(t){return"".concat(t," filas por página")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Mostrando ".concat(t," a ").concat(n," de ").concat(r," filas (filtrado de ").concat(o," filas totales)"):"Mostrando ".concat(t," a ").concat(n," de ").concat(r," filas")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"a la página ".concat(t)},formatSRPaginationNextText:function(){return"siguiente página"},formatDetailPagination:function(t){return"Mostrando ".concat(t," filas")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatPaginationSwitch:function(){return"Ocultar/Mostrar paginación"},formatPaginationSwitchDown:function(){return"Mostrar paginación"},formatPaginationSwitchUp:function(){return"Ocultar paginación"},formatRefresh:function(){return"Refrescar"},formatToggleOn:function(){return"Mostrar vista de carta"},formatToggleOff:function(){return"Ocultar vista de carta"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Cambiar todo"},formatFullscreen:function(){return"Pantalla completa"},formatAllRows:function(){return"Todo"},formatAutoRefresh:function(){return"Auto Recargar"},formatExport:function(){return"Exportar datos"},formatJumpTo:function(){return"IR"},formatAdvancedSearch:function(){return"Búsqueda avanzada"},formatAdvancedCloseButton:function(){return"Cerrar"},formatFilterControlSwitch:function(){return"Ocultar/Mostrar controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Mostrar controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["es-CL"]),r.default.fn.bootstrapTable.locales["es-ES"]=r.default.fn.bootstrapTable.locales.es={formatCopyRows:function(){return"Copiar filas"},formatPrint:function(){return"Imprimir"},formatLoadingMessage:function(){return"Cargando, por favor espere"},formatRecordsPerPage:function(t){return"".concat(t," resultados por página")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Mostrando desde ".concat(t," hasta ").concat(n," - En total ").concat(r," resultados (filtrado de ").concat(o," filas totales)"):"Mostrando desde ".concat(t," hasta ").concat(n," - En total ").concat(r," resultados")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"a la página ".concat(t)},formatSRPaginationNextText:function(){return"siguiente página"},formatDetailPagination:function(t){return"Mostrando ".concat(t," filas")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron resultados"},formatPaginationSwitch:function(){return"Ocultar/Mostrar paginación"},formatPaginationSwitchDown:function(){return"Mostrar paginación"},formatPaginationSwitchUp:function(){return"Ocultar paginación"},formatRefresh:function(){return"Recargar"},formatToggleOn:function(){return"Mostrar vista de carta"},formatToggleOff:function(){return"Ocultar vista de carta"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Cambiar todo"},formatFullscreen:function(){return"Pantalla completa"},formatAllRows:function(){return"Todos"},formatAutoRefresh:function(){return"Auto Recargar"},formatExport:function(){return"Exportar los datos"},formatJumpTo:function(){return"IR"},formatAdvancedSearch:function(){return"Búsqueda avanzada"},formatAdvancedCloseButton:function(){return"Cerrar"},formatFilterControlSwitch:function(){return"Ocultar/Mostrar controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Mostrar controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["es-ES"]),r.default.fn.bootstrapTable.locales["es-MX"]={formatCopyRows:function(){return"Copiar Filas"},formatPrint:function(){return"Imprimir"},formatLoadingMessage:function(){return"Cargando, espere por favor"},formatRecordsPerPage:function(t){return"".concat(t," resultados por página")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Mostrando ".concat(t," a ").concat(n," de ").concat(r," filas (filtrado de ").concat(o," filas totales)"):"Mostrando ".concat(t," a ").concat(n," de ").concat(r," filas")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"ir a la página ".concat(t)},formatSRPaginationNextText:function(){return"página siguiente"},formatDetailPagination:function(t){return"Mostrando ".concat(t," filas")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros que coincidan"},formatPaginationSwitch:function(){return"Mostrar/ocultar paginación"},formatPaginationSwitchDown:function(){return"Mostrar paginación"},formatPaginationSwitchUp:function(){return"Ocultar paginación"},formatRefresh:function(){return"Actualizar"},formatToggleOn:function(){return"Mostrar vista"},formatToggleOff:function(){return"Ocultar vista"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Alternar todo"},formatFullscreen:function(){return"Pantalla completa"},formatAllRows:function(){return"Todo"},formatAutoRefresh:function(){return"Auto actualizar"},formatExport:function(){return"Exportar datos"},formatJumpTo:function(){return"IR"},formatAdvancedSearch:function(){return"Búsqueda avanzada"},formatAdvancedCloseButton:function(){return"Cerrar"},formatFilterControlSwitch:function(){return"Ocultar/Mostrar controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Mostrar controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["es-MX"]),r.default.fn.bootstrapTable.locales["es-NI"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Cargando, por favor espere"},formatRecordsPerPage:function(t){return"".concat(t," registros por página")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Mostrando de ".concat(t," a ").concat(n," registros de ").concat(r," registros en total (filtered from ").concat(o," total rows)"):"Mostrando de ".concat(t," a ").concat(n," registros de ").concat(r," registros en total")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refrescar"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Todo"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Ocultar/Mostrar controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Mostrar controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["es-NI"]),r.default.fn.bootstrapTable.locales["es-SP"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Cargando, por favor espera"},formatRecordsPerPage:function(t){return"".concat(t," registros por p&#225;gina.")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"".concat(t," - ").concat(n," de ").concat(r," registros (filtered from ").concat(o," total rows)"):"".concat(t," - ").concat(n," de ").concat(r," registros.")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se han encontrado registros."},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Actualizar"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Todo"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Ocultar/Mostrar controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Mostrar controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["es-SP"]),r.default.fn.bootstrapTable.locales["et-EE"]=r.default.fn.bootstrapTable.locales.et={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Päring käib, palun oota"},formatRecordsPerPage:function(t){return"".concat(t," rida lehe kohta")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Näitan tulemusi ".concat(t," kuni ").concat(n," - kokku ").concat(r," tulemust (filtered from ").concat(o," total rows)"):"Näitan tulemusi ".concat(t," kuni ").concat(n," - kokku ").concat(r," tulemust")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Otsi"},formatNoMatches:function(){return"Päringu tingimustele ei vastanud ühtegi tulemust"},formatPaginationSwitch:function(){return"Näita/Peida lehtedeks jagamine"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Värskenda"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Veerud"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Kõik"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["et-EE"]),r.default.fn.bootstrapTable.locales["fa-IR"]=r.default.fn.bootstrapTable.locales.fa={formatCopyRows:function(){return"کپی ردیف ها"},formatPrint:function(){return"پرینت"},formatLoadingMessage:function(){return"در حال بارگذاری, لطفا صبر کنید"},formatRecordsPerPage:function(t){return"".concat(t," رکورد در صفحه")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"نمایش ".concat(t," تا ").concat(n," از ").concat(r," ردیف (filtered from ").concat(o," total rows)"):"نمایش ".concat(t," تا ").concat(n," از ").concat(r," ردیف")},formatSRPaginationPreText:function(){return"صفحه قبلی"},formatSRPaginationPageText:function(t){return"به صفحه ".concat(t)},formatSRPaginationNextText:function(){return"صفحه بعدی"},formatDetailPagination:function(t){return"نمایش ".concat(t," سطرها")},formatClearSearch:function(){return"پاک کردن جستجو"},formatSearch:function(){return"جستجو"},formatNoMatches:function(){return"رکوردی یافت نشد."},formatPaginationSwitch:function(){return"نمایش/مخفی صفحه بندی"},formatPaginationSwitchDown:function(){return"نمایش صفحه بندی"},formatPaginationSwitchUp:function(){return"پنهان کردن صفحه بندی"},formatRefresh:function(){return"به روز رسانی"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"سطر ها"},formatColumnsToggleAll:function(){return"تغییر وضعیت همه"},formatFullscreen:function(){return"تمام صفحه"},formatAllRows:function(){return"همه"},formatAutoRefresh:function(){return"رفرش اتوماتیک"},formatExport:function(){return"خروجی دیتا"},formatJumpTo:function(){return"برو"},formatAdvancedSearch:function(){return"جستجوی پیشرفته"},formatAdvancedCloseButton:function(){return"بستن"},formatFilterControlSwitch:function(){return"پنهان/نمایش دادن کنترل ها"},formatFilterControlSwitchHide:function(){return"پنهان کردن کنترل ها"},formatFilterControlSwitchShow:function(){return"نمایش کنترل ها"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fa-IR"]),r.default.fn.bootstrapTable.locales["fr-BE"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Chargement en cours"},formatRecordsPerPage:function(t){return"".concat(t," lignes par page")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Affiche de ".concat(t," à ").concat(n," sur ").concat(r," lignes (filtrés à partir de ").concat(o," lignes)"):"Affiche de ".concat(t," à ").concat(n," sur ").concat(r," lignes")},formatSRPaginationPreText:function(){return"page précédente"},formatSRPaginationPageText:function(t){return"vers la page ".concat(t)},formatSRPaginationNextText:function(){return"page suivante"},formatDetailPagination:function(t){return"Affiche ".concat(t," lignes")},formatClearSearch:function(){return"Effacer la recherche"},formatSearch:function(){return"Recherche"},formatNoMatches:function(){return"Pas de lignes trouvés"},formatPaginationSwitch:function(){return"Cacher/Afficher pagination"},formatPaginationSwitchDown:function(){return"Afficher pagination"},formatPaginationSwitchUp:function(){return"Cacher pagination"},formatRefresh:function(){return"Rafraichir"},formatToggleOn:function(){return"Afficher vue carte"},formatToggleOff:function(){return"Cacher vue carte"},formatColumns:function(){return"Colonnes"},formatColumnsToggleAll:function(){return"Tout basculer"},formatFullscreen:function(){return"Plein écran"},formatAllRows:function(){return"Tout"},formatAutoRefresh:function(){return"Rafraîchissement automatique"},formatExport:function(){return"Exporter les données"},formatJumpTo:function(){return"Aller à"},formatAdvancedSearch:function(){return"Recherche avancée"},formatAdvancedCloseButton:function(){return"Fermer"},formatFilterControlSwitch:function(){return"Cacher/Afficher controls"},formatFilterControlSwitchHide:function(){return"Cacher controls"},formatFilterControlSwitchShow:function(){return"Afficher controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fr-BE"]),r.default.fn.bootstrapTable.locales["eu-EU"]=r.default.fn.bootstrapTable.locales.eu={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Itxaron mesedez"},formatRecordsPerPage:function(t){return"".concat(t," emaitza orriko.")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"".concat(r," erregistroetatik ").concat(t,"etik ").concat(n,"erakoak erakusten (filtered from ").concat(o," total rows)"):"".concat(r," erregistroetatik ").concat(t,"etik ").concat(n,"erakoak erakusten.")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Bilatu"},formatNoMatches:function(){return"Ez da emaitzarik aurkitu"},formatPaginationSwitch:function(){return"Ezkutatu/Erakutsi orrikatzea"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Eguneratu"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Zutabeak"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Guztiak"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["eu-EU"]),r.default.fn.bootstrapTable.locales["fi-FI"]=r.default.fn.bootstrapTable.locales.fi={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Ladataan, ole hyvä ja odota"},formatRecordsPerPage:function(t){return"".concat(t," riviä sivulla")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Näytetään rivit ".concat(t," - ").concat(n," / ").concat(r," (filtered from ").concat(o," total rows)"):"Näytetään rivit ".concat(t," - ").concat(n," / ").concat(r)},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Poista suodattimet"},formatSearch:function(){return"Hae"},formatNoMatches:function(){return"Hakuehtoja vastaavia tuloksia ei löytynyt"},formatPaginationSwitch:function(){return"Näytä/Piilota sivutus"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Päivitä"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Sarakkeet"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Kaikki"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Vie tiedot"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fi-FI"]),r.default.fn.bootstrapTable.locales["es-CR"]={formatCopyRows:function(){return"Copiar filas"},formatPrint:function(){return"Imprimir"},formatLoadingMessage:function(){return"Cargando, por favor espere"},formatRecordsPerPage:function(t){return"".concat(t," filas por página")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Mostrando ".concat(t," a ").concat(n," de ").concat(r," filas (filtrado de un total de ").concat(o," filas)"):"Mostrando ".concat(t," a ").concat(n," de ").concat(r," filas")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"ir a la página ".concat(t)},formatSRPaginationNextText:function(){return"página siguiente"},formatDetailPagination:function(t){return"Mostrando ".concat(t," filas")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron resultados"},formatPaginationSwitch:function(){return"Mostrar/ocultar paginación"},formatPaginationSwitchDown:function(){return"Mostrar paginación"},formatPaginationSwitchUp:function(){return"Ocultar paginación"},formatRefresh:function(){return"Actualizar"},formatToggleOn:function(){return"Mostrar vista en tarjetas"},formatToggleOff:function(){return"Ocultar vista en tarjetas"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Alternar todo"},formatFullscreen:function(){return"Pantalla completa"},formatAllRows:function(){return"Todas las filas"},formatAutoRefresh:function(){return"Actualización automática"},formatExport:function(){return"Exportar"},formatJumpTo:function(){return"Ver"},formatAdvancedSearch:function(){return"Búsqueda avanzada"},formatAdvancedCloseButton:function(){return"Cerrar"},formatFilterControlSwitch:function(){return"Mostrar/ocultar controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Mostrar controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["es-CR"]),r.default.fn.bootstrapTable.locales["fr-CH"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Chargement en cours"},formatRecordsPerPage:function(t){return"".concat(t," lignes par page")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Affiche de ".concat(t," à ").concat(n," sur ").concat(r," lignes (filtrés à partir de ").concat(o," lignes)"):"Affiche de ".concat(t," à ").concat(n," sur ").concat(r," lignes")},formatSRPaginationPreText:function(){return"page précédente"},formatSRPaginationPageText:function(t){return"vers la page ".concat(t)},formatSRPaginationNextText:function(){return"page suivante"},formatDetailPagination:function(t){return"Affiche ".concat(t," lignes")},formatClearSearch:function(){return"Effacer la recherche"},formatSearch:function(){return"Recherche"},formatNoMatches:function(){return"Pas de lignes trouvés"},formatPaginationSwitch:function(){return"Cacher/Afficher pagination"},formatPaginationSwitchDown:function(){return"Afficher pagination"},formatPaginationSwitchUp:function(){return"Cacher pagination"},formatRefresh:function(){return"Rafraichir"},formatToggleOn:function(){return"Afficher vue carte"},formatToggleOff:function(){return"Cacher vue carte"},formatColumns:function(){return"Colonnes"},formatColumnsToggleAll:function(){return"Tout basculer"},formatFullscreen:function(){return"Plein écran"},formatAllRows:function(){return"Tout"},formatAutoRefresh:function(){return"Rafraîchissement automatique"},formatExport:function(){return"Exporter les données"},formatJumpTo:function(){return"Aller à"},formatAdvancedSearch:function(){return"Recherche avancée"},formatAdvancedCloseButton:function(){return"Fermer"},formatFilterControlSwitch:function(){return"Cacher/Afficher controls"},formatFilterControlSwitchHide:function(){return"Cacher controls"},formatFilterControlSwitchShow:function(){return"Afficher controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fr-CH"]),r.default.fn.bootstrapTable.locales["fr-LU"]={formatCopyRows:function(){return"Copier les lignes"},formatPrint:function(){return"Imprimer"},formatLoadingMessage:function(){return"Chargement en cours"},formatRecordsPerPage:function(t){return"".concat(t," lignes par page")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Affiche de ".concat(t," à ").concat(n," sur ").concat(r," lignes (filtrés à partir de ").concat(o," lignes)"):"Affiche de ".concat(t," à ").concat(n," sur ").concat(r," lignes")},formatSRPaginationPreText:function(){return"page précédente"},formatSRPaginationPageText:function(t){return"vers la page ".concat(t)},formatSRPaginationNextText:function(){return"page suivante"},formatDetailPagination:function(t){return"Affiche ".concat(t," lignes")},formatClearSearch:function(){return"Effacer la recherche"},formatSearch:function(){return"Recherche"},formatNoMatches:function(){return"Pas de lignes trouvés"},formatPaginationSwitch:function(){return"Cacher/Afficher pagination"},formatPaginationSwitchDown:function(){return"Afficher pagination"},formatPaginationSwitchUp:function(){return"Cacher pagination"},formatRefresh:function(){return"Rafraichir"},formatToggleOn:function(){return"Afficher vue carte"},formatToggleOff:function(){return"Cacher vue carte"},formatColumns:function(){return"Colonnes"},formatColumnsToggleAll:function(){return"Tout basculer"},formatFullscreen:function(){return"Plein écran"},formatAllRows:function(){return"Tout"},formatAutoRefresh:function(){return"Rafraîchissement automatique"},formatExport:function(){return"Exporter les données"},formatJumpTo:function(){return"Aller à"},formatAdvancedSearch:function(){return"Recherche avancée"},formatAdvancedCloseButton:function(){return"Fermer"},formatFilterControlSwitch:function(){return"Cacher/Afficher controls"},formatFilterControlSwitchHide:function(){return"Cacher controls"},formatFilterControlSwitchShow:function(){return"Afficher controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fr-LU"]),r.default.fn.bootstrapTable.locales["fr-FR"]=r.default.fn.bootstrapTable.locales.fr={formatCopyRows:function(){return"Copier les lignes"},formatPrint:function(){return"Imprimer"},formatLoadingMessage:function(){return"Chargement en cours"},formatRecordsPerPage:function(t){return"".concat(t," lignes par page")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Affichage de ".concat(t," à ").concat(n," sur ").concat(r," lignes (filtrés à partir de ").concat(o," lignes)"):"Affichage de ".concat(t," à ").concat(n," sur ").concat(r," lignes")},formatSRPaginationPreText:function(){return"page précédente"},formatSRPaginationPageText:function(t){return"vers la page ".concat(t)},formatSRPaginationNextText:function(){return"page suivante"},formatDetailPagination:function(t){return"Affichage de ".concat(t," lignes")},formatClearSearch:function(){return"Effacer la recherche"},formatSearch:function(){return"Recherche"},formatNoMatches:function(){return"Aucun résultat"},formatPaginationSwitch:function(){return"Masquer/Afficher la pagination"},formatPaginationSwitchDown:function(){return"Afficher la pagination"},formatPaginationSwitchUp:function(){return"Masquer la pagination"},formatRefresh:function(){return"Actualiser"},formatToggleOn:function(){return"Afficher la vue carte"},formatToggleOff:function(){return"Masquer la vue carte"},formatColumns:function(){return"Colonnes"},formatColumnsToggleAll:function(){return"Tout basculer"},formatFullscreen:function(){return"Plein écran"},formatAllRows:function(){return"Tout"},formatAutoRefresh:function(){return"Actualisation automatique"},formatExport:function(){return"Exporter les données"},formatJumpTo:function(){return"ALLER"},formatAdvancedSearch:function(){return"Recherche avancée"},formatAdvancedCloseButton:function(){return"Fermer"},formatFilterControlSwitch:function(){return"Masquer/Afficher les contrôles"},formatFilterControlSwitchHide:function(){return"Masquer les contrôles"},formatFilterControlSwitchShow:function(){return"Afficher les contrôles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fr-FR"]),r.default.fn.bootstrapTable.locales["he-IL"]=r.default.fn.bootstrapTable.locales.he={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"טוען, נא להמתין"},formatRecordsPerPage:function(t){return"".concat(t," שורות בעמוד")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"מציג ".concat(t," עד ").concat(n," מ-").concat(r,"שורות").concat(o," total rows)"):"מציג ".concat(t," עד ").concat(n," מ-").concat(r," שורות")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"חיפוש"},formatNoMatches:function(){return"לא נמצאו רשומות תואמות"},formatPaginationSwitch:function(){return"הסתר/הצג מספור דפים"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"רענן"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"עמודות"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"הכל"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["he-IL"]),r.default.fn.bootstrapTable.locales["hi-IN"]={formatCopyRows:function(){return"पंक्तियों की कॉपी करें"},formatPrint:function(){return"प्रिंट"},formatLoadingMessage:function(){return"लोड हो रहा है कृपया प्रतीक्षा करें"},formatRecordsPerPage:function(t){return"".concat(t," प्रति पृष्ठ पंक्तियाँ")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"".concat(t," - ").concat(n," पक्तिया ").concat(r," में से ( ").concat(o," पक्तिया)"):"".concat(t," - ").concat(n," पक्तिया ").concat(r," में से")},formatSRPaginationPreText:function(){return"पिछला पृष्ठ"},formatSRPaginationPageText:function(t){return"".concat(t," पृष्ठ पर")},formatSRPaginationNextText:function(){return"अगला पृष्ठ"},formatDetailPagination:function(t){return"".concat(t," पंक्तियां")},formatClearSearch:function(){return"सर्च क्लिअर करें"},formatSearch:function(){return"सर्च"},formatNoMatches:function(){return"मेल खाते रिकॉर्ड नही मिले"},formatPaginationSwitch:function(){return"छुपाओ/दिखाओ पृष्ठ संख्या"},formatPaginationSwitchDown:function(){return"दिखाओ पृष्ठ संख्या"},formatPaginationSwitchUp:function(){return"छुपाओ पृष्ठ संख्या"},formatRefresh:function(){return"रिफ्रेश"},formatToggleOn:function(){return"कार्ड दृश्य दिखाएं"},formatToggleOff:function(){return"का��्ड दृश्य छुपाएं"},formatColumns:function(){return"कॉलम"},formatColumnsToggleAll:function(){return"टॉगल आल"},formatFullscreen:function(){return"पूर्ण स्क्रीन"},formatAllRows:function(){return"सब"},formatAutoRefresh:function(){return"ऑटो रिफ्रेश"},formatExport:function(){return"एक्सपोर्ट डाटा"},formatJumpTo:function(){return"जाओ"},formatAdvancedSearch:function(){return"एडवांस सर्च"},formatAdvancedCloseButton:function(){return"बंद करे"},formatFilterControlSwitch:function(){return"छुपाओ/दिखाओ कंट्रोल्स"},formatFilterControlSwitchHide:function(){return"छुपाओ कंट्रोल्स"},formatFilterControlSwitchShow:function(){return"दिखाओ कंट्रोल्स"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["hi-IN"]),r.default.fn.bootstrapTable.locales["hr-HR"]=r.default.fn.bootstrapTable.locales.hr={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Molimo pričekajte"},formatRecordsPerPage:function(t){return"".concat(t," broj zapisa po stranici")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Prikazujem ".concat(t,". - ").concat(n,". od ukupnog broja zapisa ").concat(r," (filtered from ").concat(o," total rows)"):"Prikazujem ".concat(t,". - ").concat(n,". od ukupnog broja zapisa ").concat(r)},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Pretraži"},formatNoMatches:function(){return"Nije pronađen niti jedan zapis"},formatPaginationSwitch:function(){return"Prikaži/sakrij stranice"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Osvježi"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolone"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Sve"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["hr-HR"]),r.default.fn.bootstrapTable.locales["hu-HU"]=r.default.fn.bootstrapTable.locales.hu={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Betöltés, kérem várjon"},formatRecordsPerPage:function(t){return"".concat(t," rekord per oldal")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Megjelenítve ".concat(t," - ").concat(n," / ").concat(r," összesen (filtered from ").concat(o," total rows)"):"Megjelenítve ".concat(t," - ").concat(n," / ").concat(r," összesen")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Keresés"},formatNoMatches:function(){return"Nincs találat"},formatPaginationSwitch:function(){return"Lapozó elrejtése/megjelenítése"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Frissítés"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Oszlopok"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Összes"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["hu-HU"]),r.default.fn.bootstrapTable.locales["id-ID"]=r.default.fn.bootstrapTable.locales.id={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Memuat, mohon tunggu"},formatRecordsPerPage:function(t){return"".concat(t," baris per halaman")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Menampilkan ".concat(t," sampai ").concat(n," dari ").concat(r," baris (filtered from ").concat(o," total rows)"):"Menampilkan ".concat(t," sampai ").concat(n," dari ").concat(r," baris")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Bersihkan filter"},formatSearch:function(){return"Pencarian"},formatNoMatches:function(){return"Tidak ditemukan data yang cocok"},formatPaginationSwitch:function(){return"Sembunyikan/Tampilkan halaman"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Muat ulang"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"kolom"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Semua"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Ekspor data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["id-ID"]),r.default.fn.bootstrapTable.locales["ja-JP"]=r.default.fn.bootstrapTable.locales.ja={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"読み込み中です。少々お待ちください。"},formatRecordsPerPage:function(t){return"ページ当たり最大".concat(t,"件")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"全".concat(r,"件から、").concat(t,"から").concat(n,"件目まで表示しています (filtered from ").concat(o," total rows)"):"全".concat(r,"件から、").concat(t,"から").concat(n,"件目まで表示しています")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"検索"},formatNoMatches:function(){return"該当するレコードが見つかりません"},formatPaginationSwitch:function(){return"ページ数を表示・非表示"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"更新"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"列"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"すべて"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ja-JP"]),r.default.fn.bootstrapTable.locales["ko-KR"]=r.default.fn.bootstrapTable.locales.ko={formatCopyRows:function(){return"행 복사"},formatPrint:function(){return"프린트"},formatLoadingMessage:function(){return"데이터를 불러오는 중입니다"},formatRecordsPerPage:function(t){return"페이지 당 ".concat(t,"개 데이터 출력")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"전체 ".concat(r,"개 중 ").concat(t,"~").concat(n,"번째 데이터 출력, (전체 ").concat(o," 행에서 필터됨)"):"전체 ".concat(r,"개 중 ").concat(t,"~").concat(n,"번째 데이터 출력,")},formatSRPaginationPreText:function(){return"이전 페이지"},formatSRPaginationPageText:function(t){return"".concat(t," 페이지로 이동")},formatSRPaginationNextText:function(){return"다음 페이지"},formatDetailPagination:function(t){return"".concat(t," 행들 표시 중")},formatClearSearch:function(){return"검색 초기화"},formatSearch:function(){return"검색"},formatNoMatches:function(){return"조회된 데이터가 없습니다."},formatPaginationSwitch:function(){return"페이지 넘버 보기/숨기기"},formatPaginationSwitchDown:function(){return"페이지 넘버 보기"},formatPaginationSwitchUp:function(){return"페이지 넘버 숨기기"},formatRefresh:function(){return"새로 고침"},formatToggleOn:function(){return"카드뷰 보기"},formatToggleOff:function(){return"카드뷰 숨기기"},formatColumns:function(){return"컬럼 필터링"},formatColumnsToggleAll:function(){return"전체 토글"},formatFullscreen:function(){return"전체 화면"},formatAllRows:function(){return"전체"},formatAutoRefresh:function(){return"자동 갱신"},formatExport:function(){return"데이터 추출"},formatJumpTo:function(){return"이동"},formatAdvancedSearch:function(){return"심화 검색"},formatAdvancedCloseButton:function(){return"닫기"},formatFilterControlSwitch:function(){return"컨트롤 보기/숨기기"},formatFilterControlSwitchHide:function(){return"컨트롤 숨기기"},formatFilterControlSwitchShow:function(){return"컨트롤 보기"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ko-KR"]),r.default.fn.bootstrapTable.locales["it-IT"]=r.default.fn.bootstrapTable.locales.it={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Caricamento in corso"},formatRecordsPerPage:function(t){return"".concat(t," elementi per pagina")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Visualizzazione da ".concat(t," a ").concat(n," di ").concat(r," elementi (filtrati da ").concat(o," elementi totali)"):"Visualizzazione da ".concat(t," a ").concat(n," di ").concat(r," elementi")},formatSRPaginationPreText:function(){return"pagina precedente"},formatSRPaginationPageText:function(t){return"alla pagina ".concat(t)},formatSRPaginationNextText:function(){return"pagina successiva"},formatDetailPagination:function(t){return"Mostrando ".concat(t," elementi")},formatClearSearch:function(){return"Pulisci filtri"},formatSearch:function(){return"Cerca"},formatNoMatches:function(){return"Nessun elemento trovato"},formatPaginationSwitch:function(){return"Nascondi/Mostra paginazione"},formatPaginationSwitchDown:function(){return"Mostra paginazione"},formatPaginationSwitchUp:function(){return"Nascondi paginazione"},formatRefresh:function(){return"Aggiorna"},formatToggleOn:function(){return"Mostra visuale a scheda"},formatToggleOff:function(){return"Nascondi visuale a scheda"},formatColumns:function(){return"Colonne"},formatColumnsToggleAll:function(){return"Mostra tutte"},formatFullscreen:function(){return"Schermo intero"},formatAllRows:function(){return"Tutto"},formatAutoRefresh:function(){return"Auto Aggiornamento"},formatExport:function(){return"Esporta dati"},formatJumpTo:function(){return"VAI"},formatAdvancedSearch:function(){return"Filtri avanzati"},formatAdvancedCloseButton:function(){return"Chiudi"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["it-IT"]),r.default.fn.bootstrapTable.locales["lb-LU"]=r.default.fn.bootstrapTable.locales.lb={formatCopyRows:function(){return"Zeilen kopéieren"},formatPrint:function(){return"Drécken"},formatLoadingMessage:function(){return"Gëtt gelueden, gedellëgt Iech wannechgelift ee Moment"},formatRecordsPerPage:function(t){return"".concat(t," Zeilen per Säit")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Weist Zeil ".concat(t," bis ").concat(n," vun ").concat(r," Zeil").concat(r>1?"en":""," (gefiltert vun insgesamt ").concat(o," Zeil").concat(r>1?"en":"",")"):"Weist Zeil ".concat(t," bis ").concat(n," vun ").concat(r," Zeil").concat(r>1?"en":"")},formatSRPaginationPreText:function(){return"viregt Säit"},formatSRPaginationPageText:function(t){return"op Säit ".concat(t)},formatSRPaginationNextText:function(){return"nächst Säit"},formatDetailPagination:function(t){return"Weist ".concat(t," Zeilen")},formatClearSearch:function(){return"Sich réckgängeg maachen"},formatSearch:function(){return"Sich"},formatNoMatches:function(){return"Keng passend Anträg fonnt"},formatPaginationSwitch:function(){return"Paginatioun uweisen/verstoppen"},formatPaginationSwitchDown:function(){return"Paginatioun uweisen"},formatPaginationSwitchUp:function(){return"Paginatioun verstoppen"},formatRefresh:function(){return"Nei lueden"},formatToggleOn:function(){return"Kaartenusiicht uweisen"},formatToggleOff:function(){return"Kaartenusiicht verstoppen"},formatColumns:function(){return"Kolonnen"},formatColumnsToggleAll:function(){return"All ëmschalten"},formatFullscreen:function(){return"Vollbild"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Automatescht neilueden"},formatExport:function(){return"Daten exportéieren"},formatJumpTo:function(){return"Sprangen"},formatAdvancedSearch:function(){return"Erweidert Sich"},formatAdvancedCloseButton:function(){return"Zoumaachen"},formatFilterControlSwitch:function(){return"Schaltelementer uweisen/verstoppen"},formatFilterControlSwitchHide:function(){return"Schaltelementer verstoppen"},formatFilterControlSwitchShow:function(){return"Schaltelementer uweisen"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["lb-LU"]),r.default.fn.bootstrapTable.locales["ka-GE"]=r.default.fn.bootstrapTable.locales.ka={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"იტვირთება, გთხოვთ მოიცადოთ"},formatRecordsPerPage:function(t){return"".concat(t," ჩანაწერი თითო გვერდზე")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"ნაჩვენებია ".concat(t,"-დან ").concat(n,"-მდე ჩანაწერი ჯამური ").concat(r,"-დან (filtered from ").concat(o," total rows)"):"ნაჩვენებია ".concat(t,"-დან ").concat(n,"-მდე ჩანაწერი ჯამური ").concat(r,"-დან")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"ძებნა"},formatNoMatches:function(){return"მონაცემები არ არის"},formatPaginationSwitch:function(){return"გვერდების გადამრთველის დამალვა/გამოჩენა"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"განახლება"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"სვეტები"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ka-GE"]),r.default.fn.bootstrapTable.locales["ms-MY"]=r.default.fn.bootstrapTable.locales.ms={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Permintaan sedang dimuatkan. Sila tunggu sebentar"},formatRecordsPerPage:function(t){return"".concat(t," rekod setiap muka surat")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Sedang memaparkan rekod ".concat(t," hingga ").concat(n," daripada jumlah ").concat(r," rekod (filtered from ").concat(o," total rows)"):"Sedang memaparkan rekod ".concat(t," hingga ").concat(n," daripada jumlah ").concat(r," rekod")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Cari"},formatNoMatches:function(){return"Tiada rekod yang menyamai permintaan"},formatPaginationSwitch:function(){return"Tunjuk/sembunyi muka surat"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Muatsemula"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Lajur"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Semua"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ms-MY"]),r.default.fn.bootstrapTable.locales["nb-NO"]=r.default.fn.bootstrapTable.locales.nb={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Oppdaterer, vennligst vent"},formatRecordsPerPage:function(t){return"".concat(t," poster pr side")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Viser ".concat(t," til ").concat(n," av ").concat(r," rekker (filtered from ").concat(o," total rows)"):"Viser ".concat(t," til ").concat(n," av ").concat(r," rekker")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Søk"},formatNoMatches:function(){return"Ingen poster funnet"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Oppdater"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolonner"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["nb-NO"]),r.default.fn.bootstrapTable.locales["nl-BE"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Laden, even geduld"},formatRecordsPerPage:function(t){return"".concat(t," records per pagina")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Toon ".concat(t," tot ").concat(n," van ").concat(r," record").concat(r>1?"s":""," (gefilterd van ").concat(o," records in totaal)"):"Toon ".concat(t," tot ").concat(n," van ").concat(r," record").concat(r>1?"s":"")},formatSRPaginationPreText:function(){return"vorige pagina"},formatSRPaginationPageText:function(t){return"tot pagina ".concat(t)},formatSRPaginationNextText:function(){return"volgende pagina"},formatDetailPagination:function(t){return"Toon ".concat(t," record").concat(t>1?"s":"")},formatClearSearch:function(){return"Verwijder filters"},formatSearch:function(){return"Zoeken"},formatNoMatches:function(){return"Geen resultaten gevonden"},formatPaginationSwitch:function(){return"Verberg/Toon paginering"},formatPaginationSwitchDown:function(){return"Toon paginering"},formatPaginationSwitchUp:function(){return"Verberg paginering"},formatRefresh:function(){return"Vernieuwen"},formatToggleOn:function(){return"Toon kaartweergave"},formatToggleOff:function(){return"Verberg kaartweergave"},formatColumns:function(){return"Kolommen"},formatColumnsToggleAll:function(){return"Allen omschakelen"},formatFullscreen:function(){return"Volledig scherm"},formatAllRows:function(){return"Alle"},formatAutoRefresh:function(){return"Automatisch vernieuwen"},formatExport:function(){return"Exporteer gegevens"},formatJumpTo:function(){return"GA"},formatAdvancedSearch:function(){return"Geavanceerd zoeken"},formatAdvancedCloseButton:function(){return"Sluiten"},formatFilterControlSwitch:function(){return"Verberg/Toon controls"},formatFilterControlSwitchHide:function(){return"Verberg controls"},formatFilterControlSwitchShow:function(){return"Toon controls"},formatAddLevel:function(){return"Niveau toevoegen"},formatCancel:function(){return"Annuleren"},formatColumn:function(){return"Kolom"},formatDeleteLevel:function(){return"Niveau verwijderen"},formatDuplicateAlertTitle:function(){return"Duplicaten gevonden!"},formatDuplicateAlertDescription:function(){return"Gelieve dubbele kolommen te verwijderen of wijzigen"},formatMultipleSort:function(){return"Meervoudige sortering"},formatOrder:function(){return"Volgorde"},formatSort:function(){return"Sorteren"},formatSortBy:function(){return"Sorteren op"},formatThenBy:function(){return"vervolgens"},formatSortOrders:function(){return{asc:"Oplopend",desc:"Aflopend"}}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["nl-BE"]),r.default.fn.bootstrapTable.locales["nl-NL"]=r.default.fn.bootstrapTable.locales.nl={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Laden, even geduld"},formatRecordsPerPage:function(t){return"".concat(t," records per pagina")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Toon ".concat(t," tot ").concat(n," van ").concat(r," record").concat(r>1?"s":""," (gefilterd van ").concat(o," records in totaal)"):"Toon ".concat(t," tot ").concat(n," van ").concat(r," record").concat(r>1?"s":"")},formatSRPaginationPreText:function(){return"vorige pagina"},formatSRPaginationPageText:function(t){return"tot pagina ".concat(t)},formatSRPaginationNextText:function(){return"volgende pagina"},formatDetailPagination:function(t){return"Toon ".concat(t," record").concat(t>1?"s":"")},formatClearSearch:function(){return"Verwijder filters"},formatSearch:function(){return"Zoeken"},formatNoMatches:function(){return"Geen resultaten gevonden"},formatPaginationSwitch:function(){return"Verberg/Toon paginering"},formatPaginationSwitchDown:function(){return"Toon paginering"},formatPaginationSwitchUp:function(){return"Verberg paginering"},formatRefresh:function(){return"Vernieuwen"},formatToggleOn:function(){return"Toon kaartweergave"},formatToggleOff:function(){return"Verberg kaartweergave"},formatColumns:function(){return"Kolommen"},formatColumnsToggleAll:function(){return"Allen omschakelen"},formatFullscreen:function(){return"Volledig scherm"},formatAllRows:function(){return"Alle"},formatAutoRefresh:function(){return"Automatisch vernieuwen"},formatExport:function(){return"Exporteer gegevens"},formatJumpTo:function(){return"GA"},formatAdvancedSearch:function(){return"Geavanceerd zoeken"},formatAdvancedCloseButton:function(){return"Sluiten"},formatFilterControlSwitch:function(){return"Verberg/Toon controls"},formatFilterControlSwitchHide:function(){return"Verberg controls"},formatFilterControlSwitchShow:function(){return"Toon controls"},formatAddLevel:function(){return"Niveau toevoegen"},formatCancel:function(){return"Annuleren"},formatColumn:function(){return"Kolom"},formatDeleteLevel:function(){return"Niveau verwijderen"},formatDuplicateAlertTitle:function(){return"Duplicaten gevonden!"},formatDuplicateAlertDescription:function(){return"Gelieve dubbele kolommen te verwijderen of wijzigen"},formatMultipleSort:function(){return"Meervoudige sortering"},formatOrder:function(){return"Volgorde"},formatSort:function(){return"Sorteren"},formatSortBy:function(){return"Sorteren op"},formatThenBy:function(){return"vervolgens"},formatSortOrders:function(){return{asc:"Oplopend",desc:"Aflopend"}}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["nl-NL"]),r.default.fn.bootstrapTable.locales["pl-PL"]=r.default.fn.bootstrapTable.locales.pl={formatCopyRows:function(){return"Kopiuj wiersze"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Ładowanie, proszę czekać"},formatRecordsPerPage:function(t){return"".concat(t," rekordów na stronę")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Wyświetlanie rekordów od ".concat(t," do ").concat(n," z ").concat(r," (filtered from ").concat(o," total rows)"):"Wyświetlanie rekordów od ".concat(t," do ").concat(n," z ").concat(r)},formatSRPaginationPreText:function(){return"poprzednia strona"},formatSRPaginationPageText:function(t){return"z ".concat(t)},formatSRPaginationNextText:function(){return"następna strona"},formatDetailPagination:function(t){return"Wyświetla ".concat(t," wierszy")},formatClearSearch:function(){return"Wyczyść wyszukiwanie"},formatSearch:function(){return"Szukaj"},formatNoMatches:function(){return"Niestety, nic nie znaleziono"},formatPaginationSwitch:function(){return"Pokaż/ukryj stronicowanie"},formatPaginationSwitchDown:function(){return"Pokaż stronicowanie"},formatPaginationSwitchUp:function(){return"Ukryj stronicowanie"},formatRefresh:function(){return"Odśwież"},formatToggleOn:function(){return"Pokaż układ karty"},formatToggleOff:function(){return"Ukryj układ karty"},formatColumns:function(){return"Kolumny"},formatColumnsToggleAll:function(){return"Zaznacz wszystko"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Wszystkie"},formatAutoRefresh:function(){return"Auto odświeżanie"},formatExport:function(){return"Eksport danych"},formatJumpTo:function(){return"Przejdź"},formatAdvancedSearch:function(){return"Wyszukiwanie zaawansowane"},formatAdvancedCloseButton:function(){return"Zamknij"},formatFilterControlSwitch:function(){return"Pokaż/Ukryj"},formatFilterControlSwitchHide:function(){return"Pokaż"},formatFilterControlSwitchShow:function(){return"Ukryj"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["pl-PL"]),r.default.fn.bootstrapTable.locales["pt-BR"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Carregando, aguarde"},formatRecordsPerPage:function(t){return"".concat(t," registros por página")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Exibindo ".concat(t," até ").concat(n," de ").concat(r," linhas (filtradas de um total de ").concat(o," linhas)"):"Exibindo ".concat(t," até ").concat(n," de ").concat(r," linhas")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"Para a página ".concat(t)},formatSRPaginationNextText:function(){return"próxima página"},formatDetailPagination:function(t){return"Mostrando ".concat(t," linhas")},formatClearSearch:function(){return"Limpar Pesquisa"},formatSearch:function(){return"Pesquisar"},formatNoMatches:function(){return"Nenhum registro encontrado"},formatPaginationSwitch:function(){return"Ocultar/Exibir paginação"},formatPaginationSwitchDown:function(){return"Mostrar Paginação"},formatPaginationSwitchUp:function(){return"Esconder Paginação"},formatRefresh:function(){return"Recarregar"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Colunas"},formatColumnsToggleAll:function(){return"Alternar tudo"},formatFullscreen:function(){return"Tela cheia"},formatAllRows:function(){return"Tudo"},formatAutoRefresh:function(){return"Atualização Automática"},formatExport:function(){return"Exportar dados"},formatJumpTo:function(){return"IR"},formatAdvancedSearch:function(){return"Pesquisa Avançada"},formatAdvancedCloseButton:function(){return"Fechar"},formatFilterControlSwitch:function(){return"Ocultar/Exibir controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Exibir controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["pt-BR"]),r.default.fn.bootstrapTable.locales["ro-RO"]=r.default.fn.bootstrapTable.locales.ro={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Se incarca, va rugam asteptati"},formatRecordsPerPage:function(t){return"".concat(t," inregistrari pe pagina")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Arata de la ".concat(t," pana la ").concat(n," din ").concat(r," randuri (filtered from ").concat(o," total rows)"):"Arata de la ".concat(t," pana la ").concat(n," din ").concat(r," randuri")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Cauta"},formatNoMatches:function(){return"Nu au fost gasite inregistrari"},formatPaginationSwitch:function(){return"Ascunde/Arata paginatia"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Reincarca"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Coloane"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Toate"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ro-RO"]),r.default.fn.bootstrapTable.locales["ru-RU"]=r.default.fn.bootstrapTable.locales.ru={formatCopyRows:function(){return"Скопировать строки"},formatPrint:function(){return"Печать"},formatLoadingMessage:function(){return"Пожалуйста, подождите, идёт загрузка"},formatRecordsPerPage:function(t){return"".concat(t," записей на страницу")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Записи с ".concat(t," по ").concat(n," из ").concat(r," (отфильтровано, всего на сервере ").concat(o," записей)"):"Записи с ".concat(t," по ").concat(n," из ").concat(r)},formatSRPaginationPreText:function(){return"предыдущая страница"},formatSRPaginationPageText:function(t){return"перейти к странице ".concat(t)},formatSRPaginationNextText:function(){return"следующая страница"},formatDetailPagination:function(t){return"Загружено ".concat(t," строк")},formatClearSearch:function(){return"Очистить фильтры"},formatSearch:function(){return"Поиск"},formatNoMatches:function(){return"Ничего не найдено"},formatPaginationSwitch:function(){return"Скрыть/Показать постраничную навигацию"},formatPaginationSwitchDown:function(){return"Показать постраничную навигацию"},formatPaginationSwitchUp:function(){return"Скрыть постраничную навигацию"},formatRefresh:function(){return"Обновить"},formatToggleOn:function(){return"Показать записи в виде карточек"},formatToggleOff:function(){return"Табличный режим просмотра"},formatColumns:function(){return"Колонки"},formatColumnsToggleAll:function(){return"Выбрать все"},formatFullscreen:function(){return"Полноэкранный режим"},formatAllRows:function(){return"Все"},formatAutoRefresh:function(){return"Автоматическое обновление"},formatExport:function(){return"Экспортировать данные"},formatJumpTo:function(){return"Стр."},formatAdvancedSearch:function(){return"Расширенный поиск"},formatAdvancedCloseButton:function(){return"Закрыть"},formatFilterControlSwitch:function(){return"Скрыть/Показать панель инструментов"},formatFilterControlSwitchHide:function(){return"Скрыть панель инструментов"},formatFilterControlSwitchShow:function(){return"Показать панель инструментов"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ru-RU"]),r.default.fn.bootstrapTable.locales["sk-SK"]=r.default.fn.bootstrapTable.locales.sk={formatCopyRows:function(){return"Skopírovať riadky"},formatPrint:function(){return"Vytlačiť"},formatLoadingMessage:function(){return"Prosím čakajte"},formatRecordsPerPage:function(t){return"".concat(t," záznamov na stranu")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Zobrazená ".concat(t,". - ").concat(n,". položka z celkových ").concat(r," (filtered from ").concat(o," total rows)"):"Zobrazená ".concat(t,". - ").concat(n,". položka z celkových ").concat(r)},formatSRPaginationPreText:function(){return"Predchádzajúca strana"},formatSRPaginationPageText:function(t){return"na stranu ".concat(t)},formatSRPaginationNextText:function(){return"Nasledujúca strana"},formatDetailPagination:function(t){return"Zobrazuje sa ".concat(t," riadkov")},formatClearSearch:function(){return"Odstráň filtre"},formatSearch:function(){return"Vyhľadávanie"},formatNoMatches:function(){return"Nenájdená žiadna vyhovujúca položka"},formatPaginationSwitch:function(){return"Skry/Zobraz stránkovanie"},formatPaginationSwitchDown:function(){return"Zobraziť stránkovanie"},formatPaginationSwitchUp:function(){return"Skryť stránkovanie"},formatRefresh:function(){return"Obnoviť"},formatToggleOn:function(){return"Zobraziť kartové zobrazenie"},formatToggleOff:function(){return"skryť kartové zobrazenie"},formatColumns:function(){return"Stĺpce"},formatColumnsToggleAll:function(){return"Prepnúť všetky"},formatFullscreen:function(){return"Celá obrazovka"},formatAllRows:function(){return"Všetky"},formatAutoRefresh:function(){return"Automatické obnovenie"},formatExport:function(){return"Exportuj dáta"},formatJumpTo:function(){return"Ísť"},formatAdvancedSearch:function(){return"Pokročilé vyhľadávanie"},formatAdvancedCloseButton:function(){return"Zatvoriť"},formatFilterControlSwitch:function(){return"Zobraziť/Skryť tlačidlá"},formatFilterControlSwitchHide:function(){return"Skryť tlačidlá"},formatFilterControlSwitchShow:function(){return"Zobraziť tlačidlá"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["sk-SK"]),r.default.fn.bootstrapTable.locales["sr-Cyrl-RS"]=r.default.fn.bootstrapTable.locales.sr={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Молим сачекај"},formatRecordsPerPage:function(t){return"".concat(t," редова по страни")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Приказано ".concat(t,". - ").concat(n,". од укупног броја редова ").concat(r," (филтрирано од ").concat(o,")"):"Приказано ".concat(t,". - ").concat(n,". од укупног броја редова ").concat(r)},formatSRPaginationPreText:function(){return"претходна страна"},formatSRPaginationPageText:function(t){return"на страну ".concat(t)},formatSRPaginationNextText:function(){return"следећа страна"},formatDetailPagination:function(t){return"Приказано ".concat(t," редова")},formatClearSearch:function(){return"Обриши претрагу"},formatSearch:function(){return"Пронађи"},formatNoMatches:function(){return"Није пронађен ни један податак"},formatPaginationSwitch:function(){return"Прикажи/сакриј пагинацију"},formatPaginationSwitchDown:function(){return"Прикажи пагинацију"},formatPaginationSwitchUp:function(){return"Сакриј пагинацију"},formatRefresh:function(){return"Освежи"},formatToggleOn:function(){return"Прикажи картице"},formatToggleOff:function(){return"Сакриј картице"},formatColumns:function(){return"Колоне"},formatColumnsToggleAll:function(){return"Прикажи/сакриј све"},formatFullscreen:function(){return"Цео екран"},formatAllRows:function(){return"Све"},formatAutoRefresh:function(){return"Аутоматско освежавање"},formatExport:function(){return"Извези податке"},formatJumpTo:function(){return"Иди"},formatAdvancedSearch:function(){return"Напредна претрага"},formatAdvancedCloseButton:function(){return"Затвори"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["sr-Cyrl-RS"]),r.default.fn.bootstrapTable.locales["sr-Latn-RS"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Molim sačekaj"},formatRecordsPerPage:function(t){return"".concat(t," redova po strani")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Prikazano ".concat(t,". - ").concat(n,". od ukupnog broja redova ").concat(r," (filtrirano od ").concat(o,")"):"Prikazano ".concat(t,". - ").concat(n,". od ukupnog broja redova ").concat(r)},formatSRPaginationPreText:function(){return"prethodna strana"},formatSRPaginationPageText:function(t){return"na stranu ".concat(t)},formatSRPaginationNextText:function(){return"sledeća strana"},formatDetailPagination:function(t){return"Prikazano ".concat(t," redova")},formatClearSearch:function(){return"Obriši pretragu"},formatSearch:function(){return"Pronađi"},formatNoMatches:function(){return"Nije pronađen ni jedan podatak"},formatPaginationSwitch:function(){return"Prikaži/sakrij paginaciju"},formatPaginationSwitchDown:function(){return"Prikaži paginaciju"},formatPaginationSwitchUp:function(){return"Sakrij paginaciju"},formatRefresh:function(){return"Osveži"},formatToggleOn:function(){return"Prikaži kartice"},formatToggleOff:function(){return"Sakrij kartice"},formatColumns:function(){return"Kolone"},formatColumnsToggleAll:function(){return"Prikaži/sakrij sve"},formatFullscreen:function(){return"Ceo ekran"},formatAllRows:function(){return"Sve"},formatAutoRefresh:function(){return"Automatsko osvežavanje"},formatExport:function(){return"Izvezi podatke"},formatJumpTo:function(){return"Idi"},formatAdvancedSearch:function(){return"Napredna pretraga"},formatAdvancedCloseButton:function(){return"Zatvori"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["sr-Latn-RS"]),r.default.fn.bootstrapTable.locales["sv-SE"]=r.default.fn.bootstrapTable.locales.sv={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Laddar, vänligen vänta"},formatRecordsPerPage:function(t){return"".concat(t," rader per sida")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Visa ".concat(t," till ").concat(n," av ").concat(r," rader (filtered from ").concat(o," total rows)"):"Visa ".concat(t," till ").concat(n," av ").concat(r," rader")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Sök"},formatNoMatches:function(){return"Inga matchande resultat funna."},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Uppdatera"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"kolumn"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["sv-SE"]),r.default.fn.bootstrapTable.locales["pt-PT"]=r.default.fn.bootstrapTable.locales.pt={formatCopyRows:function(){return"Copiar Linhas"},formatPrint:function(){return"Imprimir"},formatLoadingMessage:function(){return"A carregar, por favor aguarde"},formatRecordsPerPage:function(t){return"".concat(t," registos por página")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"A mostrar ".concat(t," at&eacute; ").concat(n," de ").concat(r," linhas (filtered from ").concat(o," total rows)"):"A mostrar ".concat(t," até ").concat(n," de ").concat(r," linhas")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"ir para página ".concat(t)},formatSRPaginationNextText:function(){return"próxima página"},formatDetailPagination:function(t){return"Mostrando ".concat(t," linhas")},formatClearSearch:function(){return"Limpar Pesquisa"},formatSearch:function(){return"Pesquisa"},formatNoMatches:function(){return"Nenhum registo encontrado"},formatPaginationSwitch:function(){return"Esconder/Mostrar paginação"},formatPaginationSwitchDown:function(){return"Mostrar página"},formatPaginationSwitchUp:function(){return"Esconder página"},formatRefresh:function(){return"Actualizar"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Colunas"},formatColumnsToggleAll:function(){return"Activar tudo"},formatFullscreen:function(){return"Ecrã completo"},formatAllRows:function(){return"Tudo"},formatAutoRefresh:function(){return"Actualização autmática"},formatExport:function(){return"Exportar dados"},formatJumpTo:function(){return"Avançar"},formatAdvancedSearch:function(){return"Pesquisa avançada"},formatAdvancedCloseButton:function(){return"Fechar"},formatFilterControlSwitch:function(){return"Esconder/Exibir controlos"},formatFilterControlSwitchHide:function(){return"Esconder controlos"},formatFilterControlSwitchShow:function(){return"Exibir controlos"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["pt-PT"]),r.default.fn.bootstrapTable.locales["tr-TR"]=r.default.fn.bootstrapTable.locales.tr={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Yükleniyor, lütfen bekleyin"},formatRecordsPerPage:function(t){return"Sayfa başına ".concat(t," kayıt.")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"".concat(r," kayıttan ").concat(t,"-").concat(n," arası gösteriliyor (filtered from ").concat(o," total rows)."):"".concat(r," kayıttan ").concat(t,"-").concat(n," arası gösteriliyor.")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Ara"},formatNoMatches:function(){return"Eşleşen kayıt bulunamadı."},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Yenile"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Sütunlar"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Tüm Satırlar"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["tr-TR"]),r.default.fn.bootstrapTable.locales["th-TH"]=r.default.fn.bootstrapTable.locales.th={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"กำลังโหลดข้อมูล, กรุณารอสักครู่"},formatRecordsPerPage:function(t){return"".concat(t," รายการต่อหน้า")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"รายการที่ ".concat(t," ถึง ").concat(n," จากทั้งหมด ").concat(r," รายการ (filtered from ").concat(o," total rows)"):"รายการที่ ".concat(t," ถึง ").concat(n," จากทั้งหมด ").concat(r," รายการ")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"ค้นหา"},formatNoMatches:function(){return"ไม่พบรายการที่ค้นหา !"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"รีเฟรส"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"คอลัมน์"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["th-TH"]),r.default.fn.bootstrapTable.locales["uk-UA"]=r.default.fn.bootstrapTable.locales.uk={formatCopyRows:function(){return"Скопіювати рядки"},formatPrint:function(){return"Друк"},formatLoadingMessage:function(){return"Завантаження, будь ласка, зачекайте"},formatRecordsPerPage:function(t){return"".concat(t," рядків на сторінку")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Відображено рядки з ".concat(t," по ").concat(n," з ").concat(r," загалом (відфільтровано з ").concat(o," рядків)"):"Відображено рядки з ".concat(t," по ").concat(n," з ").concat(r," загалом")},formatSRPaginationPreText:function(){return"попередня сторінка"},formatSRPaginationPageText:function(t){return"до сторінки ".concat(t)},formatSRPaginationNextText:function(){return"наступна сторінка"},formatDetailPagination:function(t){return"Відображено ".concat(t," рядків")},formatClearSearch:function(){return"Скинути фільтри"},formatSearch:function(){return"Пошук"},formatNoMatches:function(){return"Не знайдено жодного запису"},formatPaginationSwitch:function(){return"Сховати/Відобразити пагінацію"},formatPaginationSwitchDown:function(){return"Відобразити пагінацію"},formatPaginationSwitchUp:function(){return"Сховати пагінацію"},formatRefresh:function(){return"Оновити"},formatToggleOn:function(){return"Відобразити у форматі карток"},formatToggleOff:function(){return"Вимкнути формат карток"},formatColumns:function(){return"Стовпці"},formatColumnsToggleAll:function(){return"Переключити усі"},formatFullscreen:function(){return"Повноекранний режим"},formatAllRows:function(){return"Усі"},formatAutoRefresh:function(){return"Автооновлення"},formatExport:function(){return"Експортувати дані"},formatJumpTo:function(){return"Швидкий перехід до"},formatAdvancedSearch:function(){return"Розширений пошук"},formatAdvancedCloseButton:function(){return"Закрити"},formatFilterControlSwitch:function(){return"Сховати/Відобразити елементи керування"},formatFilterControlSwitchHide:function(){return"Сховати елементи керування"},formatFilterControlSwitchShow:function(){return"Відобразити елементи керування"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["uk-UA"]),r.default.fn.bootstrapTable.locales["ur-PK"]=r.default.fn.bootstrapTable.locales.ur={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"براۓ مہربانی انتظار کیجئے"},formatRecordsPerPage:function(t){return"".concat(t," ریکارڈز فی صفہ ")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"دیکھیں ".concat(t," سے ").concat(n," کے ").concat(r,"ریکارڈز (filtered from ").concat(o," total rows)"):"دیکھیں ".concat(t," سے ").concat(n," کے ").concat(r,"ریکارڈز")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"تلاش"},formatNoMatches:function(){return"کوئی ریکارڈ نہیں ملا"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"تازہ کریں"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"کالم"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ur-PK"]),r.default.fn.bootstrapTable.locales["uz-Latn-UZ"]=r.default.fn.bootstrapTable.locales.uz={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Yuklanyapti, iltimos kuting"},formatRecordsPerPage:function(t){return"".concat(t," qator har sahifada")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Ko'rsatypati ".concat(t," dan ").concat(n," gacha ").concat(r," qatorlarni (filtered from ").concat(o," total rows)"):"Ko'rsatypati ".concat(t," dan ").concat(n," gacha ").concat(r," qatorlarni")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Filtrlarni tozalash"},formatSearch:function(){return"Qidirish"},formatNoMatches:function(){return"Hech narsa topilmadi"},formatPaginationSwitch:function(){return"Sahifalashni yashirish/ko'rsatish"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Yangilash"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Ustunlar"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Hammasi"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Eksport"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["uz-Latn-UZ"]),r.default.fn.bootstrapTable.locales["vi-VN"]=r.default.fn.bootstrapTable.locales.vi={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Đang tải"},formatRecordsPerPage:function(t){return"".concat(t," bản ghi mỗi trang")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Hiển thị từ trang ".concat(t," đến ").concat(n," của ").concat(r," bảng ghi (filtered from ").concat(o," total rows)"):"Hiển thị từ trang ".concat(t," đến ").concat(n," của ").concat(r," bảng ghi")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Tìm kiếm"},formatNoMatches:function(){return"Không có dữ liệu"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["vi-VN"]),r.default.fn.bootstrapTable.locales["zh-CN"]=r.default.fn.bootstrapTable.locales.zh={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"正在努力地加载数据中，请稍候"},formatRecordsPerPage:function(t){return"每页显示 ".concat(t," 条记录")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"显示第 ".concat(t," 到第 ").concat(n," 条记录，总共 ").concat(r," 条记录（从 ").concat(o," 总记录中过滤）"):"显示第 ".concat(t," 到第 ").concat(n," 条记录，总共 ").concat(r," 条记录")},formatSRPaginationPreText:function(){return"上一页"},formatSRPaginationPageText:function(t){return"第".concat(t,"页")},formatSRPaginationNextText:function(){return"下一页"},formatDetailPagination:function(t){return"总共 ".concat(t," 条记录")},formatClearSearch:function(){return"清空过滤"},formatSearch:function(){return"搜索"},formatNoMatches:function(){return"没有找到匹配的记录"},formatPaginationSwitch:function(){return"隐藏/显示分页"},formatPaginationSwitchDown:function(){return"显示分页"},formatPaginationSwitchUp:function(){return"隐藏分页"},formatRefresh:function(){return"刷新"},formatToggleOn:function(){return"显示卡片视图"},formatToggleOff:function(){return"隐藏卡片视图"},formatColumns:function(){return"列"},formatColumnsToggleAll:function(){return"切换所有"},formatFullscreen:function(){return"全屏"},formatAllRows:function(){return"所有"},formatAutoRefresh:function(){return"自动刷新"},formatExport:function(){return"导出数据"},formatJumpTo:function(){return"跳转"},formatAdvancedSearch:function(){return"高级搜索"},formatAdvancedCloseButton:function(){return"关闭"},formatFilterControlSwitch:function(){return"隐藏/显示过滤控制"},formatFilterControlSwitchHide:function(){return"隐藏过滤控制"},formatFilterControlSwitchShow:function(){return"显示过滤控制"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["zh-CN"]),r.default.fn.bootstrapTable.locales["zh-TW"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"正在努力地載入資料，請稍候"},formatRecordsPerPage:function(t){return"每頁顯示 ".concat(t," 項記錄")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"顯示第 ".concat(t," 到第 ").concat(n," 項記錄，總共 ").concat(r," 項記錄（從 ").concat(o," 總記錄中過濾）"):"顯示第 ".concat(t," 到第 ").concat(n," 項記錄，總共 ").concat(r," 項記錄")},formatSRPaginationPreText:function(){return"上一頁"},formatSRPaginationPageText:function(t){return"第".concat(t,"頁")},formatSRPaginationNextText:function(){return"下一頁"},formatDetailPagination:function(t){return"總共 ".concat(t," 項記錄")},formatClearSearch:function(){return"清空過濾"},formatSearch:function(){return"搜尋"},formatNoMatches:function(){return"沒有找到符合的結果"},formatPaginationSwitch:function(){return"隱藏/顯示分頁"},formatPaginationSwitchDown:function(){return"顯示分頁"},formatPaginationSwitchUp:function(){return"隱藏分頁"},formatRefresh:function(){return"重新整理"},formatToggleOn:function(){return"顯示卡片視圖"},formatToggleOff:function(){return"隱藏卡片視圖"},formatColumns:function(){return"列"},formatColumnsToggleAll:function(){return"切換所有"},formatFullscreen:function(){return"全屏"},formatAllRows:function(){return"所有"},formatAutoRefresh:function(){return"自動刷新"},formatExport:function(){return"導出數據"},formatJumpTo:function(){return"跳轉"},formatAdvancedSearch:function(){return"高級搜尋"},formatAdvancedCloseButton:function(){return"關閉"},formatFilterControlSwitch:function(){return"隱藏/顯示過濾控制"},formatFilterControlSwitchHide:function(){return"隱藏過濾控制"},formatFilterControlSwitchShow:function(){return"顯示過濾控制"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["zh-TW"]),r.default.fn.bootstrapTable.locales["af-ZA"]=r.default.fn.bootstrapTable.locales.af={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Besig om te laai, wag asseblief"},formatRecordsPerPage:function(t){return"".concat(t," rekords per bladsy")},formatShowingRows:function(t,n,r,o){return void 0!==o&&o>0&&o>r?"Resultate ".concat(t," tot ").concat(n," van ").concat(r," rye (filtered from ").concat(o," total rows)"):"Resultate ".concat(t," tot ").concat(n," van ").concat(r," rye")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Soek"},formatNoMatches:function(){return"Geen rekords gevind nie"},formatPaginationSwitch:function(){return"Wys/verberg bladsy nummering"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Herlaai"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolomme"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["af-ZA"])}));
