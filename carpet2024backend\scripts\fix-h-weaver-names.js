// Script to fix H- record weaver names
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

// H-format weaver names mapping
const hWeaverMapping = {
  'H-220169': 'Stock March 2024',
  'H-220030': 'Stock March 2025',
  'H-220031': 'Stock March 2026',
  'H-220012': 'Stock March 2027',
  'H-220032': 'Stock March 2028',
  'H-220019': 'Stock March 2029',
  'H-220011': 'Stock March 2030',
  'H-220130': 'Stock March 2031',
  'H-220116': 'Stock March 2032',
  'H-220111': 'Stock March 2033',
  'H-220168': 'Stock March 2034',
  'H-220129': 'Stock March 2035',
  'H-220112': 'Stock March 2036',
  'H-220017': 'Stock March 2037',
  'H-220018': 'Stock March 2038',
  'H-220009': 'Stock March 2039',
  'H-220010': 'Stock March 2040',
  'H-210534': 'Stock March 2041',
  'H-210325': 'Stock March 2042',
  'H-210966': 'Stock March 2043',
  'H-210977': 'Stock March 2044',
  'H-210535': 'Stock March 2045',
  'H-210223': 'Stock March 2046',
  'H-210967': 'Stock March 2047',
  'H-210735': 'Stock March 2048',
  'H-210178': 'Stock March 2049',
  'H-210176': 'Stock March 2050',
  'H-220026': 'Stock March 2051',
  'H-220027': 'Stock March 2052',
  'H-220028': 'Stock March 2053',
  'H-220029': 'Stock March 2054',
  'H-880012': 'Stock March 2057',
  'H-250018': 'Stock March 2058',
  'H-220158': 'Stock March 2059'
};

async function fixHWeaverNames() {
  console.log('🔄 FIXING H- RECORD WEAVER NAMES');
  console.log('='.repeat(60));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get all H- records
    const hRecords = await collection.find({ receiveNo: { $regex: /^H-/ } }).toArray();
    console.log(`📊 Found ${hRecords.length} H- records to update`);
    
    const results = { success: [], errors: [] };
    
    // Update each H- record with proper weaver name
    for (let i = 0; i < hRecords.length; i++) {
      try {
        const record = hRecords[i];
        const weaverName = hWeaverMapping[record.receiveNo];
        
        if (weaverName) {
          // Update the record
          await collection.updateOne(
            { _id: record._id },
            { 
              $set: { 
                weaverName: weaverName,
                quality: '7 X 52',
                design: 'MIR'
              } 
            }
          );
          
          results.success.push({
            receiveNo: record.receiveNo,
            oldWeaver: record.weaverName || 'N/A',
            newWeaver: weaverName
          });
        } else {
          results.errors.push({
            receiveNo: record.receiveNo,
            error: 'No weaver mapping found'
          });
        }
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Updated ${i + 1}/${hRecords.length} H- records`);
        }
        
      } catch (error) {
        console.error(`❌ Error updating H- record ${i + 1}:`, error.message);
        results.errors.push({
          receiveNo: hRecords[i]?.receiveNo || `Record ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error fixing H- weaver names:', error);
    return { success: [], errors: [] };
  }
}

async function verifyHRecords() {
  console.log('\n🔍 Verifying H- records...');
  
  try {
    const db = mongoose.connection.db;
    
    // Get sample H- records
    const samples = await db.collection('carpetreceiveds').find({ receiveNo: { $regex: /^H-/ } }).limit(10).toArray();
    
    console.log('\n📋 Sample H- records with fixed weaver names:');
    samples.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Weaver: ${record.weaverName || 'N/A'}`);
      console.log(`   Quality: ${record.quality || 'N/A'}`);
      console.log(`   Design: ${record.design || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ${record.amount || 'N/A'}`);
      console.log('   ' + '-'.repeat(50));
    });
    
    // Check distinct H- weaver names
    const hWeavers = await db.collection('carpetreceiveds').distinct('weaverName', { receiveNo: { $regex: /^H-/ } });
    console.log(`\n👥 H- Format Weaver Names (${hWeavers.length}):`);
    hWeavers.forEach((weaver, index) => {
      console.log(`   ${index + 1}. ${weaver}`);
    });
    
  } catch (error) {
    console.error('❌ Error verifying H- records:', error);
  }
}

async function main() {
  console.log('🔄 FIXING H- RECORD WEAVER NAMES');
  console.log('(Adding Stock March 2024, Stock March 2025, etc.)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Fix H- weaver names
    const results = await fixHWeaverNames();

    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 H- WEAVER NAME FIX COMPLETE');
    console.log('='.repeat(60));
    console.log(`✅ Successfully updated: ${results.success.length} H- records`);
    console.log(`❌ Failed: ${results.errors.length} H- records`);
    
    if (results.success.length > 0) {
      console.log('\n✅ SAMPLE UPDATED H- RECORDS:');
      results.success.slice(0, 10).forEach(record => {
        console.log(`  - ${record.receiveNo}: "${record.oldWeaver}" → "${record.newWeaver}"`);
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.slice(0, 5).forEach(error => {
        console.log(`  - ${error.receiveNo}: ${error.error}`);
      });
    }
    
    // Verify H- records
    await verifyHRecords();

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 H- WEAVER NAMES FIXED!');
    console.log('✅ All H- records now have Stock March weaver names');
    console.log('✅ K- records: Clean names (Rahila, Jeet Narayan, etc.)');
    console.log('✅ H- records: Stock March names (Stock March 2024, etc.)');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
