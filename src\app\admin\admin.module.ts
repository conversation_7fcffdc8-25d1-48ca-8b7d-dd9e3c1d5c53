import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AdminRoutingModule } from './admin-routing.module';
import { AdminComponent } from './admin.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { AdminHeaderComponent } from './include/admin-header/admin-header.component';
import { AdminFooterComponent } from './include/admin-footer/admin-footer.component';
import { AdminSidebarComponent } from './include/admin-sidebar/admin-sidebar.component';
import { BuyerMasterComponent } from './master/buyer-master/buyer-master.component';
import { ViewBuyerMasterComponent } from './master/view-buyer-master/view-buyer-master.component';
import { RawMaterialGroupComponent } from './master/raw-material-group/raw-material-group.component';

import { WeaverEmployeeComponent } from './master/weaver-employee/weaver-employee.component';
import { QualityDesignComponent } from './master/quality-design/quality-design.component';
import { MaterialLagatComponent } from './master/material-lagat/material-lagat.component';
import { SizeComponent } from './master/size/size.component';
import { ColorCodeDetailsComponent } from './master/color-code-details/color-code-details.component';
import { CodeDyeingDetailsComponent } from './master/code-dyeing-details/code-dyeing-details.component';
import { FinishingHeadComponent } from './master/finishing-head/finishing-head.component';
import { FinishingProcessComponent } from './master/finishing-process/finishing-process.component';
import { ManageBranchComponent } from './master/manage-branch/manage-branch.component';
import { CarpetStockComponent } from './master/carpet-stock/carpet-stock.component';
import { MapMasterComponent } from './master/map-master/map-master.component';
import { ViewMaterialLagatComponent } from './master/view-material-lagat/view-material-lagat.component';
import { ViewDetailMaterialLagatComponent } from './master/view-detail-material-lagat/view-detail-material-lagat.component';
import { PurchaseManufacturingComponent } from './manufacturing/purchase-manufacturing/purchase-manufacturing.component';
import { MaterialDeyingComponent } from './manufacturing/material-deying/material-deying.component';
import { BayerOrderComponent } from './manufacturing/bayer-order/bayer-order.component';
import { DesigningMapOrderComponent } from './manufacturing/designing-map-order/designing-map-order.component';
import { CarpetOrderIssueComponent } from './manufacturing/carpet-order-issue/carpet-order-issue.component';
import { RecivingCarpetComponent } from './manufacturing/reciving-carpet/reciving-carpet.component';
import { CarpetFinishingComponent } from './manufacturing/carpet-finishing/carpet-finishing.component';
import { ImporterDetailComponent } from './koti/master/importer-detail/importer-detail.component';
import { WholesellerCustomerComponent } from './koti/master/wholeseller-customer/wholeseller-customer.component';
import { RetailerCustomerComponent } from './koti/master/retailer-customer/retailer-customer.component';
import { QualityDesignCodeComponent } from './koti/master/quality-design-code/quality-design-code.component';
import { SizeCodeComponent } from './koti/master/size-code/size-code.component';
import { GstComponent } from './koti/master/gst/gst.component';
import { BarCodeDetailsComponent } from './koti/master/bar-code-details/bar-code-details.component';
import { ReportKotiComponent } from './koti/master/report-koti/report-koti.component';
import { OrderReportComponent } from './koti/order-form/order-report/order-report.component';
import { ContainerDespatchComponent } from './koti/import-details/container-despatch/container-despatch.component';
import { ContainerReceivedComponent } from './koti/import-details/container-received/container-received.component';
import { ImportDetailsReportComponent } from './koti/import-details/import-details-report/import-details-report.component';
// import { NewChallanSaleComponent } from './koti/sale/new-challan-sale/new-challan-sale.component';
import { NewChallanSaleComponent } from './koti/sale/new-challan-sale/new-challan-sale.component';
import { ChallanReportComponent } from './koti/sale/challan-report/challan-report.component';
import { BillsForWholesellerComponent } from './koti/sale/bills-for-wholeseller/bills-for-wholeseller.component';
import { BillsForRetailerComponent } from './koti/sale/bills-for-retailer/bills-for-retailer.component';
import { BillsReportComponent } from './koti/sale/bills-report/bills-report.component';
import { StockReportComponent } from './koti/stock/stock-report/stock-report.component';
import { ReportOrderComponent } from './report/report-order/report-order.component';
import { ReportMonthlyComponent } from './report/report-monthly/report-monthly.component';
import { ReportYearlyComponent } from './report/report-yearly/report-yearly.component';
import { ReportStockComponent } from './report/report-stock/report-stock.component';
import { ReportSaleComponent } from './report/report-sale/report-sale.component';
import { ReportGstComponent } from './report/report-gst/report-gst.component';
import { ImportInvoicesComponent } from './koti/import-details/import-invoices/import-invoices.component';
import { ImporterOrderPriceListComponent } from './koti/master/importer-order-price-list/importer-order-price-list.component';


import {MatIconModule} from '@angular/material/icon';
import {MatInputModule} from '@angular/material/input';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatDatepickerModule} from '@angular/material/datepicker';
import {provideNativeDateAdapter} from '@angular/material/core';
import {MatTooltipModule} from '@angular/material/tooltip';

import {MatSelectModule} from '@angular/material/select';

import {FormsModule, ReactiveFormsModule} from '@angular/forms';

import {MatDividerModule} from '@angular/material/divider';
import {MatButtonModule} from '@angular/material/button';
import {MatTableModule} from '@angular/material/table';

import {MatPaginator, MatPaginatorModule} from '@angular/material/paginator';
import {MatSort, MatSortModule} from '@angular/material/sort';



import {MatRadioModule} from '@angular/material/radio';
import { DesignComponent } from './master/design/design.component';
import { ViewImporterInvoiceComponent } from './koti/import-details/view-importer-invoice/view-importer-invoice.component';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { ColorPickerModule } from 'ngx-color-picker';
import { ImporterInvoicesReportComponent } from './report/importer-invoice/importer-invoices-report/importer-invoices-report.component';
import { ContDetailsReportComponent } from './report/importer-invoice/cont-details-report/cont-details-report.component';


// Removed duplicate import of MatCardModule
import {MatCheckboxModule} from '@angular/material/checkbox';

import {MatAutocompleteModule} from '@angular/material/autocomplete';

import {MatMenuModule} from '@angular/material/menu';
import { ImportInvoiceReportComponent } from './koti/import-details/import-invoice-report/import-invoice-report.component';
import { ContainerDespatchReportComponent } from './koti/import-details/container-despatch-report/container-despatch-report.component';
import { ContainerReceivedReportComponent } from './koti/import-details/container-received-report/container-received-report.component';
import {MatDialogModule} from '@angular/material/dialog';
import { MapRateComponent } from './master/map-rate/map-rate.component';
import { NgxUiLoaderConfig, NgxUiLoaderHttpModule, NgxUiLoaderModule, NgxUiLoaderRouterModule, PB_DIRECTION, POSITION, SPINNER } from 'ngx-ui-loader';
import { PrintBillComponent } from './koti/order-form/print-bill/print-bill.component';
import { DyeingRateComponent } from './master/dyeing-rate/dyeing-rate.component';
import { CarpetStockDetailsComponent } from './master/carpet-stock-details/carpet-stock-details.component';
import { StockModelDialogComponent } from './master/stock-model-dialog/stock-model-dialog.component';
import {MatTabsModule} from '@angular/material/tabs';
import { PurchaseDetailsComponent } from './master/purchase-details/purchase-details.component';
import { RegularMapOrderComponent } from './manufacturing/regular-map-order/regular-map-order.component';
import { CreateBuyerOrderComponent } from './manufacturing/create-buyer-order/create-buyer-order.component';
import { PrintComponent } from './koti/order-form/print/print.component';
import { ViewBuyerOrderComponent } from './manufacturing/view-buyer-order/view-buyer-order.component';
import { BuyerOrderListComponent } from './manufacturing/buyer-order-list/buyer-order-list.component';
import { ViewCarpetOrderIssueComponent } from './manufacturing/view-carpet-order-issue/view-carpet-order-issue.component';
import { PrintCarpetOrderIssueComponent } from './manufacturing/print-carpet-order-issue/print-carpet-order-issue.component';
// Removed duplicate import of ExportInvoiceComponent
import { ExportPackingListComponent } from './manufacturing/export-packing-list/export-packing-list.component';
import { ExportReportComponent } from './manufacturing/export-report/export-report.component';
import { AdminSidebarNewComponent } from './include/admin-sidebar-new/admin-sidebar-new.component';
import { SidebarAsifComponent } from './include/sidebar-asif/sidebar-asif.component';
import { SidebarImtayazComponent } from './include/sidebar-imtayaz/sidebar-imtayaz.component';
import { SidebarAnkitComponent } from './include/sidebar-ankit/sidebar-ankit.component';
import { ViewCarpetRecivingComponent } from './manufacturing/view-carpet-reciving/view-carpet-reciving.component';


import { MatCardModule } from '@angular/material/card';
import { ExportInvoiceComponent } from './manufacturing/export-invoice/export-invoice.component';
import { CdkTableModule } from '@angular/cdk/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ViewExportPackingListComponent } from './manufacturing/view-export-packing-list/view-export-packing-list.component';
import { ViewMainCarpetRecivingComponentComponent } from './manufacturing/view-main-carpet-reciving.component/view-main-carpet-reciving.component.component';
import { AssignedDetailsComponent } from './manufacturing/buyer-order-list/modals/assigned-details/assigned-details.component';
import { ReceivedDetailsComponent } from './manufacturing/buyer-order-list/modals/received-details/received-details.component';
import { QualityModalComponent } from './manufacturing/export-invoice/modals/quality-modal/quality-modal.component';
import { MaterialIssueComponent } from './manufacturing/material-issue&recive/material-issue/material-issue.component';
import { MaterialComponent } from './manufacturing/material-issue&recive/material/material.component';
import { MaterialReceiveComponent } from './manufacturing/material-issue&recive/material-receive/material-receive.component';
import { KatiModalComponent } from './manufacturing/material-issue&recive/material-issue/modal/kati-modal/kati-modal.component';
import { ViewWeaverMaterialComponent } from './manufacturing/material-issue&recive/view-weaver-material/view-weaver-material.component';
import { KatiReceiveModalComponent } from './manufacturing/material-issue&recive/material-receive/modal/kati-receive-modal/kati-receive-modal.component';
import { ChallanModalComponent } from './manufacturing/material-issue&recive/material-receive/modal/challan-modal/challan-modal.component';


// import { PurchaseDetailsComponent } from './master/purchase-details/purchase-details.component';

const ngxUiLoaderConfig: NgxUiLoaderConfig = {
bgsColor: 'red',
text:'Loading',
textColor:'#85c5ed',
textPosition:'center-center',
pbColor:'#85c5ed',
fgsColor:'#85c5ed',
fgsType:'three-strings',
fgsSize:100,
pbDirection:PB_DIRECTION.leftToRight,
pbThickness:5,
};
@NgModule({
  declarations: [

    AdminComponent,
    DashboardComponent,
    AdminHeaderComponent,
    AdminFooterComponent,
    AdminSidebarComponent,
    BuyerMasterComponent,
    ViewBuyerMasterComponent,
    RawMaterialGroupComponent,
    // PurchaseDetailsComponent,
    WeaverEmployeeComponent,
    QualityDesignComponent,
    MaterialLagatComponent,
    SizeComponent,
    ColorCodeDetailsComponent,
    CodeDyeingDetailsComponent,
    FinishingHeadComponent,
    FinishingProcessComponent,
    ManageBranchComponent,
    CarpetStockComponent,
    MapMasterComponent,
    ViewMaterialLagatComponent,
    ViewDetailMaterialLagatComponent,
    PurchaseManufacturingComponent,
    MaterialDeyingComponent,
    BayerOrderComponent,
    DesigningMapOrderComponent,
    CarpetOrderIssueComponent,
    RecivingCarpetComponent,
    CarpetFinishingComponent,
    ImporterDetailComponent,
    WholesellerCustomerComponent,
    RetailerCustomerComponent,
    QualityDesignCodeComponent,
    SizeCodeComponent,
    GstComponent,
    BarCodeDetailsComponent,
    ReportKotiComponent,
    OrderReportComponent,
    ContainerDespatchComponent,
    ContainerReceivedComponent,
    ImportDetailsReportComponent,
    NewChallanSaleComponent,
    ChallanReportComponent,
    BillsForWholesellerComponent,
    BillsForRetailerComponent,
    BillsReportComponent,
    StockReportComponent,
    ReportOrderComponent,
    ReportMonthlyComponent,
    ReportYearlyComponent,
    ReportStockComponent,
    ReportSaleComponent,
    ReportGstComponent,
    ImportInvoicesComponent,
    ImporterOrderPriceListComponent,
    DesignComponent,
    ViewImporterInvoiceComponent,
    ImporterInvoicesReportComponent,
    ContDetailsReportComponent,
    ImportInvoiceReportComponent,
    ContainerDespatchReportComponent,
    ContainerReceivedReportComponent,
    MapRateComponent,
    PrintBillComponent,
    DyeingRateComponent,
    CarpetStockDetailsComponent,
    StockModelDialogComponent,
    PurchaseDetailsComponent,
    RegularMapOrderComponent,
    CreateBuyerOrderComponent,
    PrintComponent,
    ViewBuyerOrderComponent,
    BuyerOrderListComponent,
    ViewCarpetOrderIssueComponent,
    PrintCarpetOrderIssueComponent,

    ExportPackingListComponent,
    ExportReportComponent,
    AdminSidebarNewComponent,
    SidebarAsifComponent,
    SidebarImtayazComponent,
    SidebarAnkitComponent,
    ViewCarpetRecivingComponent,
    ViewExportPackingListComponent,
    ViewMainCarpetRecivingComponentComponent,
    AssignedDetailsComponent,
    ReceivedDetailsComponent,
    MaterialIssueComponent,
    MaterialComponent,
    MaterialReceiveComponent,
    KatiModalComponent,
    ViewWeaverMaterialComponent,
    KatiReceiveModalComponent,
    ChallanModalComponent

   ],


    providers: [provideNativeDateAdapter()],
  imports: [
    CommonModule,
    AdminRoutingModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatSelectModule,
    FormsModule,
    MatDividerModule,
    MatButtonModule,
    // ChartsModule,
    MatTableModule,
    ReactiveFormsModule,
    MatPaginator,
    MatPaginatorModule,
    MatSort, MatSortModule,
    MatDialogModule,
    MatRadioModule,
    MatDatepickerModule,
    MatRadioModule,TooltipModule.forRoot(),ColorPickerModule,
    MatTooltipModule,
    MatCheckboxModule,
    MatAutocompleteModule,
    MatMenuModule,
    MatTabsModule,
    NgxUiLoaderModule.forRoot(ngxUiLoaderConfig),
    MatCardModule,
    MatProgressSpinnerModule,
    CdkTableModule,
    ExportInvoiceComponent, // Added standalone component
    ExportInvoiceComponent, // Added standalone component
    QualityModalComponent
  ]
})
export class AdminModule { }
