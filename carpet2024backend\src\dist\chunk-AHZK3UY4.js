import"./chunk-43RJOZUD.js";import{h as $,k as W}from"./chunk-OQ2WVHJX.js";import{A as E,D as C,F,G as U,H as T,I as B,J as V,b as O,j as v,k as d,m as R,o as S,s as x,t as y,x as w}from"./chunk-CATJFVJK.js";import"./chunk-RGNDWIHZ.js";import{$ as u,Gb as b,Sb as m,Ta as G,Uc as P,Xa as g,Ya as l,_ as k,da as D,ja as c,ka as f,ob as h,xb as r,yb as t,zb as a}from"./chunk-YIUCZFK7.js";import{g as I}from"./chunk-P2VZOJAX.js";var M=I(V());var N=(()=>{let e=class e{constructor(n){this.http=n,this.apiUrl=B.apiUrl,this.phaseOne=`${this.apiUrl}/phase-one/user`}register(n){return this.http.post(`${this.phaseOne}/create-user`,n)}login(n){return this.http.post(`${this.phaseOne}/loginUser`,n)}};e.\u0275fac=function(i){return new(i||e)(D(O))},e.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"root"});let o=e;return o})();var j=(()=>{let e=class e{constructor(n,i,s){this.fb=n,this.authService=i,this.route=s}ngOnInit(){this.frmLogin=this.fb.group({email:[],password:[]})}login(){debugger;let n=this.frmLogin.value;this.authService.login(n).subscribe({next:i=>{M.default.fire({title:"Success",text:"Login successfull",icon:"success"}),this.route.navigate(["/admin"])},error:i=>{M.default.fire({title:"Failed",text:"Something went wrong",icon:"warning"})}})}};e.\u0275fac=function(i){return new(i||e)(l(F),l(N),l(v))},e.\u0275cmp=c({type:e,selectors:[["app-login"]],decls:25,vars:1,consts:[[1,"bglogin","row","vh-100","d-flex","justify-content-center","align-items-center"],[1,"col-md-4","card","card-sign"],[1,"card-header"],[1,"card-title"],[1,"card-text"],[3,"ngSubmit","formGroup"],[1,"card-body"],[1,"mb-4"],[1,"form-label"],["type","text","placeholder","Enter your email address","formControlName","email",1,"form-control"],[1,"form-label","d-flex","justify-content-between"],["routerLink","forgot-password"],["type","password","placeholder","Enter your password","formControlName","password",1,"form-control"],[1,"btn","btn-primary","btn-sign"],[1,"mb-4","mt-2"],[1,"form-label","d-flex"],["routerLink","/register"]],template:function(i,s){i&1&&(r(0,"div",0)(1,"div",1)(2,"div",2)(3,"h3",3),m(4,"Rachin Export"),t(),r(5,"p",4),m(6,"Welcome back! Please signin to continue."),t()(),r(7,"form",5),b("ngSubmit",function(){return s.login()}),r(8,"div",6)(9,"div",7)(10,"label",8),m(11,"Contact No"),t(),a(12,"input",9),t(),r(13,"div",7)(14,"label",10),m(15,"Password "),r(16,"a",11),m(17,"Forgot password?"),t()(),a(18,"input",12),t(),r(19,"button",13),m(20,"Sign In"),t(),r(21,"div",14)(22,"label",15)(23,"a",16),m(24,"Signup"),t()()()()()()()),i&2&&(g(7),h("formGroup",s.frmLogin))},dependencies:[d,w,S,x,y,E,C],styles:['.bglogin[_ngcontent-%COMP%]{background:url("./media/loginbg-SYGBLMP5.webp")}']});let o=e;return o})();var H=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=c({type:e,selectors:[["app-forgot-password"]],decls:21,vars:0,consts:[[1,"page-auth"],[1,"header"],[1,"container"],["routerLink","",1,"header-logo"],[1,"content"],[1,"card","card-auth"],[1,"card-body","text-center"],[1,"mb-5"],["type","image/svg+xml","data",G`../../../assets/svg/forgot_password.svg`,1,"w-50"],[1,"card-title"],[1,"card-text","mb-5"],[1,"row","g-2"],[1,"col-sm-8"],["type","text","placeholder","Enter contact no.",1,"form-control"],[1,"col-sm-4"],["routerLink","",1,"btn","btn-primary"]],template:function(i,s){i&1&&(r(0,"body",0)(1,"div",1)(2,"div",2)(3,"a",3),m(4,"Rachin Export"),t()()(),r(5,"div",4)(6,"div",2)(7,"div",5)(8,"div",6)(9,"div",7),a(10,"object",8),t(),r(11,"h3",9),m(12,"Reset your password"),t(),r(13,"p",10),m(14,"Enter your phone no. address and we will send you a link to reset your password."),t(),r(15,"div",11)(16,"div",12),a(17,"input",13),t(),r(18,"div",14)(19,"a",15),m(20,"Reset"),t()()()()()()()())},dependencies:[d]});let o=e;return o})();var L=I(V());var q=(()=>{let e=class e{constructor(n,i,s){this.fb=n,this.authService=i,this.route=s}ngOnInit(){this.frmRegister=this.fb.group({name:[],email:[],password:[]})}register(){debugger;let n=this.frmRegister.value;this.authService.register(n).subscribe({next:i=>{L.default.fire({title:"Success",text:"Registration has been successfull",icon:"success"}),this.route.navigate(["/login"])},error:i=>{L.default.fire({title:"Failed",text:"Something went wrong",icon:"warning"})}})}};e.\u0275fac=function(i){return new(i||e)(l(F),l(N),l(v))},e.\u0275cmp=c({type:e,selectors:[["app-register"]],decls:30,vars:1,consts:[[1,"bglogin","row","vh-100","d-flex","justify-content-center","align-items-center"],[1,"col-md-4","card","card-sign"],[1,"card-header"],[1,"card-title"],[1,"card-text"],[3,"ngSubmit","formGroup"],[1,"card-body"],[1,"mb-4"],[1,"form-label"],["type","text","placeholder","Enter your name","formControlName","name",1,"form-control"],["type","text","placeholder","Enter your email address","formControlName","email",1,"form-control"],[1,"form-label","d-flex","justify-content-between"],["type","password","placeholder","Enter your password","formControlName","password",1,"form-control"],["type","checkbox","name","Remember","id",""],[1,"btn","btn-primary"],[1,"mb-4","mt-2"],[1,"form-label","d-flex"],["routerLink","/login"]],template:function(i,s){i&1&&(r(0,"div",0)(1,"div",1)(2,"div",2)(3,"h3",3),m(4,"Rachin Export"),t(),r(5,"p",4),m(6,"Welcome back! Please signin to continue."),t()(),r(7,"form",5),b("ngSubmit",function(){return s.register()}),r(8,"div",6)(9,"div",7)(10,"label",8),m(11,"Name"),t(),a(12,"input",9),t(),r(13,"div",7)(14,"label",8),m(15,"E-mail"),t(),a(16,"input",10),t(),r(17,"div",7)(18,"label",11),m(19,"Password "),t(),a(20,"input",12),t(),r(21,"div",7),a(22,"input",13),m(23," Remember me "),t(),r(24,"button",14),m(25,"Sign Up"),t(),r(26,"div",15)(27,"label",16)(28,"a",17),m(29,"Sign In"),t()()()()()()()),i&2&&(g(7),h("formGroup",s.frmRegister))},dependencies:[d,w,S,x,y,E,C],styles:['.bglogin[_ngcontent-%COMP%]{background:url("./media/loginbg-SYGBLMP5.webp")}']});let o=e;return o})();var Q=[{path:"",component:j},{path:"login",component:j},{path:"forgot-password",component:H},{path:"register",component:q}],z=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=f({type:e}),e.\u0275inj=u({imports:[R.forChild(Q),R]});let o=e;return o})();var xe=(()=>{let e=class e{};e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=f({type:e}),e.\u0275inj=u({imports:[P,z,W,$,U,T]});let o=e;return o})();export{xe as AuthModule};
