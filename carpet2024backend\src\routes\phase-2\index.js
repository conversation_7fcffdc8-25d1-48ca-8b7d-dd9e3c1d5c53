const express = require('express');

const profileRouter = require('../phase-2/profile-routes');
const gstRouter=require('../phase-2/gst-routes');
const impoterRouter =require ('../phase-2/impoter-routes');
const challanRouter = require('../phase-2/challan-routes');
const billWholesellerRouter = require('../phase-2/billwholeSeller-routes');
const qualityRouter = require('../phase-2/quality');
const designRouter=require('../phase-2/design.-routes');
const qualityanddesignRouter=require('../phase-2/qualityanddesign-routes');
const wholeSellerRoutes=require('../phase-2/wholeSeller-routes');
const retailerRoutes = require('../phase-2/retailer-routes');
const billforRetailerRouter=require('../phase-2/billforRetailer-routes');
const activitylogRoutes=require('../phase-2/activitylog')
const router = express.Router();

router.use('/impoter',impoterRouter);//done
router.use('/profile',profileRouter);//done
router.use('/gst',gstRouter);//done
router.use('/CreateChallan',challanRouter);//done
router.use('/wholeSeller',billWholesellerRouter);//done
router.use('/quality',qualityRouter);//done
router.use('/design',designRouter);//done
router.use('/qualityandDesign',qualityanddesignRouter);
router.use('/wholeseller',wholeSellerRoutes)//done
router.use('/retailer',retailerRoutes);
router.use('/billforretailer',billforRetailerRouter);
router.use('/activitylog',activitylogRoutes)

module.exports = router;
