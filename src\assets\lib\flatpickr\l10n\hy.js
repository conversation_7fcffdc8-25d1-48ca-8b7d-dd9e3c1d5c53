(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.hy = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Armenian = {
      weekdays: {
          shorthand: ["Կիր", "Երկ", "Երք", "Չրք", "Հնգ", "Ուրբ", "Շբթ"],
          longhand: [
              "Կիրակի",
              "Եկուշաբթի",
              "Երեքշաբթի",
              "Չորեքշաբթի",
              "Հինգշաբթի",
              "Ուրբաթ",
              "Շաբաթ",
          ],
      },
      months: {
          shorthand: [
              "Հնվ",
              "Փտր",
              "Մար",
              "Ապր",
              "Մայ",
              "Հնս",
              "Հլս",
              "Օգս",
              "Սեպ",
              "Հոկ",
              "Նմբ",
              "Դեկ",
          ],
          longhand: [
              "Հունվար",
              "Փետրվար",
              "Մարտ",
              "Ապրիլ",
              "Մայիս",
              "Հունիս",
              "Հուլիս",
              "Օգոստոս",
              "Սեպտեմբեր",
              "Հոկտեմբեր",
              "Նոյեմբեր",
              "Դեկտեմբեր",
          ],
      },
      firstDayOfWeek: 1,
      ordinal: function () {
          return "";
      },
      rangeSeparator: " — ",
      weekAbbreviation: "ՇԲՏ",
      scrollTitle: "Ոլորեք՝ մեծացնելու համար",
      toggleTitle: "Սեղմեք՝ փոխելու համար",
      amPM: ["ՄԿ", "ԿՀ"],
      yearAriaLabel: "Տարի",
      monthAriaLabel: "Ամիս",
      hourAriaLabel: "Ժամ",
      minuteAriaLabel: "Րոպե",
      time_24hr: true,
  };
  fp.l10ns.hy = Armenian;
  var hy = fp.l10ns;

  exports.Armenian = Armenian;
  exports.default = hy;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
