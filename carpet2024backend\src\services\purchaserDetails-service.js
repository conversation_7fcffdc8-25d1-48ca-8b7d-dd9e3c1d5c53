const purchaserDetailsRepository = require('../repositories/purchaserDetails-repository');

const createpurchaserDetails = async (data) => {
  return await purchaserDetailsRepository.createpurchaserDetails(data);
};

const getAllpurchaserDetails = async () => {
  return await purchaserDetailsRepository.getAllpurchaserDetails();
};

const getpurchaserDetailsById = async (id) => {
  return await purchaserDetailsRepository.getpurchaserDetailsById(id);
};

const updatepurchaserDetails = async (id, data) => {
  return await purchaserDetailsRepository.updatepurchaserDetails(id, data);
};

const deletepurchaserDetails = async (id) => {
  return await purchaserDetailsRepository.deletepurchaserDetails(id);
};

module.exports = {
  createpurchaserDetails,
  getAllpurchaserDetails,
  getpurchaserDetailsById,
  updatepurchaserDetails,
  deletepurchaserDetails
};