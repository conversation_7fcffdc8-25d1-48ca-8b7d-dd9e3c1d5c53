// impoterRepository.js
const ImpoterDetails = require('../model/phase-1/impoter-invoice');

async function createImpoterDetail(data) {
    return ImpoterDetails.create(data);
}

async function getImpoterDetailById(id) {
    return ImpoterDetails.findById(id);
}
async function getAllImpoterDetails() {
    return ImpoterDetails.find({}); // Retrieve all impoter details
}
async function updateImpoterDetail(id, data) {
    return ImpoterDetails.findByIdAndUpdate(id, data, { new: true });
}

async function deleteImpoterDetail(id) {
    return ImpoterDetails.findByIdAndDelete(id);
}

module.exports = {
    createImpoterDetail,
    getImpoterDetailById,
    getAllImpoterDetails,
    updateImpoterDetail,
    deleteImpoterDetail
};
