const {C<PERSON><PERSON>hallan ,deleted<PERSON>halanHistory}= require('../model/phase-2/createChallan');

class CreateChallanRepository {
    async create(challanData) {
        try {
            let isExsitChallan = await CreateChallan.findOne({challanNo:challanData.challanNo})
            if(isExsitChallan){
                return 
            }
            return await CreateChallan.create(challanData);
        } catch (error) {
            throw error;
        }
    }

    async findAll() {
        try {
            return await CreateChallan.find();
        } catch (error) {
            throw error;
        }
    }

    async  findChallans(data) {
        try {
            switch (data.filterValue) {
                case 'pending':
                    return await CreateChallan.find({ isBillCreated: false });
                    break;
                case 'all':
                    return await CreateChallan.find();
                    break; // Retrieve all entries, adjust as per your actual requirement
                default:
                    throw new Error('Invalid filter value');
                    break;
            }
        } catch (error) {
            throw error;
        }
    }

    async findById(id) {
        try {
            return await CreateChallan.findById(id);
        } catch (error) {
            throw error;
        }
    }

    async update(id, challanData) {
        try {
            return await CreateChallan.findByIdAndUpdate(id, challanData, { new: true });
        } catch (error) {
            throw error;
        }
    }

    async delete(id) {
        try {
            return await CreateChallan.findByIdAndDelete(id);
        } catch (error) {
            throw error;
        }
    }
    async deleteItem(id) {
        try {
            return await CreateChallan.findByIdAndDelete(id);
        } catch (error) {
            throw error;
        }
    }

//     async deleteChallanItem(id){
//         try{
// return await CreateChallan.
//         }catch(err){
// throw err
//         }
//     }

    async findByBarcode(barcodeNo) {
        try {
            return await CreateChallan.findOne({ barcodeNo });
        } catch (error) {
            throw error;
        }
    }
    async CreateChallanHistory(challanHistoryData) {
        try {
            return await deletedChalanHistory.create(challanHistoryData);
        } catch (error) {
            throw error;
        }
    }

    async getDeletedChallanHistoryData(){
        return await deletedChalanHistory.find();
    }
}

module.exports = CreateChallanRepository;
