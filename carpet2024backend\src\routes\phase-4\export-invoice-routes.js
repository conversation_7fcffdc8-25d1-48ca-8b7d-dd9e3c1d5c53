const express = require('express');
const router = express.Router();
const exportInvoiceController = require('../../controller/manifacturing/export-invoice-controller');

// Define routes for export invoices
router.get('/getAll', exportInvoiceController.getAllInvoices); // Get all invoices
router.get('/generate-number', exportInvoiceController.generateInvoiceNumber);
router.get('/:id', exportInvoiceController.getInvoiceById); // Get invoice by ID
router.post('/', exportInvoiceController.createExportInvoice);
router.put('/:id', exportInvoiceController.updateExportInvoice); // Update invoice by ID

module.exports = router;
