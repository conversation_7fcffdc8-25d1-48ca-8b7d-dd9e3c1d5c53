const AddDesignRepository = require('../repositories/design-repository');

class AddDesignService {
    constructor() {
        this.addDesignRepository = new AddDesignRepository();
    }

    async createDesign(designData) {
        try {
            return await this.addDesignRepository.createDesign(designData);
        } catch (error) {
            throw error;
        }
    }

    async getAllDesigns() {
        try {
            return await this.addDesignRepository.getAllDesigns();
        } catch (error) {
            throw error;
        }
    }

    async getDesignById(designId) {
        try {
            return await this.addDesignRepository.getDesignById(designId);
        } catch (error) {
            throw error;
        }
    }

    async updateDesign(designId, designData) {
        try {
            return await this.addDesignRepository.updateDesign(designId, designData);
        } catch (error) {
            throw error;
        }
    }

    async deleteDesign(designId) {
        try {
            return await this.addDesignRepository.deleteDesign(designId);
        } catch (error) {
            throw error;
        }
    }
}

module.exports = AddDesignService;
