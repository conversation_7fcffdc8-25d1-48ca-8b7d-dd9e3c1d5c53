const carpetOrderissueRepository = require('../../repositories/manifacturing/carpetOrder-repository');

const createCarpetOrderissue = async (data) => {
  return await carpetOrderissueRepository.createCarpetOrderissue(data);
};

const getAllCarpetOrderissues = async () => {
  return await carpetOrderissueRepository.getAllCarpetOrderissues();
};

const getCarpetOrderissueById = async (id) => {
  return await carpetOrderissueRepository.getCarpetOrderissueById(id);
};

const updateCarpetOrderissue = async (id, data) => {
  return await carpetOrderissueRepository.updateCarpetOrderissue(id, data);
};

const updateCarpetOrderissueField = async (id, field, value) => {
  return await carpetOrderissueRepository.updateCarpetOrderissueField(id, field, value);
};

const deleteCarpetOrderissueField = async (id, field) => {
  return await carpetOrderissueRepository.deleteCarpetOrderissueField(id, field);
};

const deleteCarpetOrderissue = async (id) => {
  return await carpetOrderissueRepository.deleteCarpetOrderissue(id);
};

const getPopulate = async ()=>{
  return await carpetOrderissueRepository.getUsingPopulate();
}

const updatePCSFields = async (issueId) => {
  return await carpetOrderissueRepository.updatePCSFields(issueId);
};

const initializePCSFields = async () => {
  return await carpetOrderissueRepository.initializePCSFields();
};

const fixBuyerOrderNaNValues = async () => {
  return await carpetOrderissueRepository.fixBuyerOrderNaNValues();
};

module.exports = {
  createCarpetOrderissue,
  getAllCarpetOrderissues,
  getCarpetOrderissueById,
  updateCarpetOrderissue,
  updateCarpetOrderissueField,
  deleteCarpetOrderissueField,
  deleteCarpetOrderissue,
  getPopulate,
  updatePCSFields,
  initializePCSFields,
  fixBuyerOrderNaNValues
};
