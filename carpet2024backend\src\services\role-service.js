const roleRepository = require('../repositories/role-repository');

class RoleService {
  async createRole(roleData) {
    try {
      const role = await roleRepository.createRole(roleData);
      return role;
    } catch (error) {
      throw error;
    }
  }

  async getRoleById(roleId) {
    try {
      const role = await roleRepository.getRoleById(roleId);
      return role;
    } catch (error) {
      throw error;
    }
  }

  async getAllRoles() {
    try {
      const roles = await roleRepository.getAllRoles();
      return roles;
    } catch (error) {
      throw error;
    }
  }

  async updateRole(roleId, roleData) {
    try {
      const role = await roleRepository.updateRole(roleId, roleData);
      return role;
    } catch (error) {
      throw error;
    }
  }

  async deleteRole(roleId) {
    try {
      const role = await roleRepository.deleteRole(roleId);
      return role;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new RoleService();
