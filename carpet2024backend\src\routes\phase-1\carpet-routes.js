const express = require('express');
const carpetController = require('../../controller/carpet-controller');
const carpetMiddleware = require('../../middleware/carpet-middleware');

const router = express.Router();

router.post('/carpets', carpetMiddleware.validateCarpetData, carpetController.createCarpet);
router.get('/carpets', carpetController.getAllCarpets);
router.get('/carpets/:id', carpetController.getCarpetById);
router.put('/carpets/:id', carpetMiddleware.validateCarpetData, carpetController.updateCarpet);
router.delete('/carpets/:id', carpetController.deleteCarpet);

module.exports = router;
