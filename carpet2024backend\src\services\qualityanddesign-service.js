// qualityAndDesignService.js

const QualityAndDesignRepository = require('../repositories/qualityanddesign-repository');

class QualityAndDesignService {
  async createQualityAndDesign(qualityAndDesignData) {
    try {
      return await QualityAndDesignRepository.createQualityAndDesign(qualityAndDesignData);
    } catch (error) {
      throw error;
    }
  }

  async getQualityAndDesignById(qualityAndDesignId) {
    try {
      return await QualityAndDesignRepository.getQualityAndDesignById(qualityAndDesignId);
    } catch (error) {
      throw error;
    }
  }

  async getAllQualityAndDesigns() {
    try {
      return await QualityAndDesignRepository.getAllQualityAndDesigns();
    } catch (error) {
      throw error;
    }
  }

  async updateQualityAndDesign(qualityAndDesignId, qualityAndDesignData) {
    try {
      return await QualityAndDesignRepository.updateQualityAndDesign(qualityAndDesignId, qualityAndDesignData);
    } catch (error) {
      throw error;
    }
  }

  async deleteQualityAndDesign(qualityAndDesignId) {
    try {
      return await QualityAndDesignRepository.deleteQualityAndDesign(qualityAndDesignId);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new QualityAndDesignService();
