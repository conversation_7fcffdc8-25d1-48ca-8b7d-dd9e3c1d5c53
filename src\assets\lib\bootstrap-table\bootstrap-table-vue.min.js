/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).BootstrapTable=t()}(this,(function(){"use strict";const e=window.jQuery,t=t=>void 0===t?t:e.extend(!0,Array.isArray(t)?[]:{},t);function n(e,t,n,o,s,i,a,r,d,l){"boolean"!=typeof a&&(d=r,r=a,a=!1);const c="function"==typeof n?n.options:n;let f;if(e&&e.render&&(c.render=e.render,c.staticRenderFns=e.staticRenderFns,c._compiled=!0,s&&(c.functional=!0)),o&&(c._scopeId=o),i?(f=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,d(e)),e&&e._registeredComponents&&e._registeredComponents.add(i)},c._ssrRegister=f):t&&(f=a?function(e){t.call(this,l(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,r(e))}),f)if(c.functional){const e=c.render;c.render=function(t,n){return f.call(n),e(t,n)}}else{const e=c.beforeCreate;c.beforeCreate=e?[].concat(e,f):[f]}return n}return n({render:function(){var e=this.$createElement;return(this._self._c||e)("table")},staticRenderFns:[]},undefined,{name:"BootstrapTable",props:{columns:{type:Array,require:!0},data:{type:[Array,Object],default(){}},options:{type:Object,default:()=>({})}},mounted(){this.$table=e(this.$el),this.$table.on("all.bs.table",((t,n,o)=>{let s=e.fn.bootstrapTable.events[n];s=s.replace(/([A-Z])/g,"-$1").toLowerCase(),this.$emit("on-all",...o),this.$emit(s,...o)})),this._initTable()},methods:{_initTable(){const e={...t(this.options),columns:t(this.columns),data:t(this.data)};this._hasInit?this.refreshOptions(e):(this.$table.bootstrapTable(e),this._hasInit=!0)},...(()=>{const t={};for(const n of e.fn.bootstrapTable.methods)t[n]=function(...e){return this.$table.bootstrapTable(n,...e)};return t})()},watch:{options:{handler(){this._initTable()},deep:!0},columns:{handler(){this._initTable()},deep:!0},data:{handler(){this.load(t(this.data))},deep:!0}}},undefined,false,undefined,!1,void 0,void 0,void 0)}));
