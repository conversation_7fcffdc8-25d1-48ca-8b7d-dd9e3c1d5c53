const RawMaterialGroup = require('../model/phase-3/rawMaterial');

const createRawMaterialGroup = async (data) => {
  return await RawMaterialGroup.create(data);
};

const getAllRawMaterialGroups = async () => {
  return await RawMaterialGroup.find();
};

const getRawMaterialGroupById = async (id) => {
  return await RawMaterialGroup.findById(id);
};

const updateRawMaterialGroup = async (id, data) => {
  return await RawMaterialGroup.findByIdAndUpdate(id, data, { new: true });
};

const deleteRawMaterialGroup = async (id) => {
  return await RawMaterialGroup.findByIdAndDelete(id);
}

module.exports = {
  createRawMaterialGroup,
  getAllRawMaterialGroups,
  getRawMaterialGroupById,
  updateRawMaterialGroup,
  deleteRawMaterialGroup
};
