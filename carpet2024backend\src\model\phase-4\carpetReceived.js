const mongoose = require("mongoose");

const carpetReceivedSchema = new mongoose.Schema(
  {
    K: {type:mongoose.Schema.Types.ObjectId, ref:'branch', required:true},

    receivingDate : { type: Date, required: true },

    issueNo: {
      type: mongoose.Schema.Types.Mixed, // Allow any type of data (e.g., nested object)
      required: true,
    },
    // issueNo: {
    //   type:mongoose.Schema.Types.ObjectId, ref:'CarpetOrderissue',
    //   required: true,
    // },

    weaverNumber: {
         type: mongoose.Schema.Types.ObjectId,
             ref: "WeaverEmployee",
             required: true,
    },


    // weight: {
    //   type: String,
    //   required: true,
    // },
    // yes: {
    //   type: Boolean,
    //   required: true,
    // },
    // no: {
    //   type: Boolean,
    //   required: true,
    // },
    receiveNo: {
      type: String,
      required: true,
      unique: true,
    },
    area: { type: String },
    amount: { type: String },
    pcs: { type: Number },

    // Additional fields that are present in the data
    weaverName: { type: String },
    quality: { type: String },
    design: { type: String },
    colour: { type: String },
    colour2: { type: String },
    size: { type: String },
    carpetNo: { type: String },

  },
  {
    autoCreate: true,
    timestamps: true,
  }
);

module.exports = mongoose.model("CarpetReceived", carpetReceivedSchema);
