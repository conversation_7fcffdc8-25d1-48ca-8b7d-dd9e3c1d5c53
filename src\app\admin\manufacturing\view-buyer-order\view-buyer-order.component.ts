import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { ManufactureService } from '../../../services/manufacture.service';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { CustomeServiceService } from '../../../services/custome-service.service';

export interface UserData {
  id:String,
  index:number,
  buyerName:String,
  orderNo:String,
  companyOrderNo:String,
  orderDate:String,
  shippingDate:String,
  orderType:String,
  customerOrder:String,
  // weavingUnit:String,
  priority:String,
  orderedItem:number;
  area:String,
}

const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-view-buyer-order',
  templateUrl: './view-buyer-order.component.html',
  styleUrl: './view-buyer-order.component.css'
})

export class ViewBuyerOrderComponent implements  OnInit{

  constructor(private customeServicex:CustomeServiceService,private manufactureService:ManufactureService,private router:Router){}

  ngOnInit(){
    this.getAllList()
  }

  displayedColumns: string[] = [
    "SrNo",
    "buyerName",
    "orderNo",
    "companyOrderNo",
    "orderDate",
    "shippingDate",
    "orderType",
    "customerOrder",
    // "weavingUnit",
    "priority",
    "area",
    "orderedItem",
    "action"
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }



  getAllList(){
    this.manufactureService.getAllBuyerOrder().subscribe((res:any)=>{
debugger

      if(res){
        ELEMENT_DATA.length = 0;
        res.map((x:any,i:number)=>{
          ELEMENT_DATA.push({
            id:x._id,
            index:i + 1,
            buyerName:x.buyerName,
           orderNo:x.orderNo,
           companyOrderNo:x.companyOrderNo,
           orderDate:this.customeServicex.convertDate(x.orderDate),
           shippingDate:this.customeServicex.convertDate(x.shippingDate),
           orderType:x.orderType,
           customerOrder:x.customerOrder,
          //  weavingUnit:x.weavingUnit,
           orderedItem:x.items.reduce((acc:any, curr:any)=>acc+curr.pcs,0),
           priority:x.priority,
           area:x.area,
          })
        })

      }
      this.dataSource = new MatTableDataSource(ELEMENT_DATA)
      this.ngAfterViewInit()
       return

    })

  }



  getSerialNumber(index:number){
    return index + 1
  }







  editBuyer(id:any){

  this.router.navigate([`admin/buyer-order/${id}`])

  }

  editOrderList(id:any){
    this.router.navigate([`admin/buyerOrderList/${id}`])
  }

  delete(id:any){
    debugger
    // Find the order to get the order number for confirmation
    const orderToDelete = ELEMENT_DATA.find(order => order.id === id);
    const orderNo = orderToDelete ? orderToDelete.orderNo : 'this order';

    Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to delete buyer order ${orderNo}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        this.manufactureService.deleteBuyerOrder(id).subscribe((res:any)=>{
          this.getAllList()
          Swal.fire(
            'Success!',
            'Data has been deleted successfully',
            'success'
         )
        },(error)=>{
          Swal.fire(
            'Warning!',
            error.error.message,
            'warning'
         )
        })
      }
    })
  }





}
