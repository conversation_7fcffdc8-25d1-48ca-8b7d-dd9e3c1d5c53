<div class="px-3">
  <!-- Start Content-->
  <div class="container-fluid vh-100">
    <!-- start page title -->
    <div class="py-3 py-lg-4">
      <div class="row">
        <div class="col-lg-6">
          <h2 class="page-title mb-0">{{isUpdated?'Update Container Despatch':'Add Container Despatch'}}</h2>
        </div>
      </div>
      <hr/>

      <!-- Form row -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div class="card-body">
              <form [formGroup]="frmContainerDespatch" (ngSubmit)="addContainerDespatch()">
                <div class="row">
                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline">
                      <mat-label>Container No.</mat-label>
                      <mat-select formControlName="impoterNo" (selectionChange)="FindInvoice($event.value)" >
                      
                        @for (importerNo of impotererNo; track importerNo) {
                      <mat-option [value]="importerNo">{{importerNo}}</mat-option>
                    }
                      </mat-select>
                    </mat-form-field>
                    
                  </div>
                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline">
                      <mat-label>Choose a date</mat-label>
                      <input matInput [matDatepicker]="picker"  formControlName="chooseAdate"/>
                          
                      <mat-datepicker-toggle
                        matIconSuffix
                        [for]="picker"
                      ></mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                  </div>
                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline">
                      <mat-label>Country</mat-label>
                      <mat-select formControlName="country" >
                        
                        
                        @for (country of countrys; track country) {
                      <mat-option [value]="country">{{country}}</mat-option>
                    }
                      </mat-select>
                    </mat-form-field>
                  </div>
                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline"> 
                      <mat-label>Currency</mat-label>
                      <mat-select formControlName="currency" >
                      
                        @for (currency of currencys; track currency) {
                      <mat-option [value]="currency">{{currency}}</mat-option>
                    }
                      </mat-select>
                    </mat-form-field>   
                  </div>
                </div>
             
                <div class="row">
                   <div class="mb-2 col-md-3">
                      <mat-form-field appearance="outline" class="example-full-width">
                      <mat-label>Container Number</mat-label>
                      <input matInput placeholder="Container Number" formControlName="containerNumber" />
                      </mat-form-field>
                    </div>
                  <div class="mb-2 col-md-3">
                    <mat-form-field
                      appearance="outline"
                      class="example-full-width"
                    >
                      <mat-label>Liner Details</mat-label>
                      <input matInput placeholder="Container Number" formControlName="linerDetails" />
                    </mat-form-field>
                  </div>
                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline" class="example-full-width">
                      <mat-label>No. of Invoices</mat-label>
                      <input matInput placeholder="No. of Invoices" formControlName="noOfInvoice" />
                    </mat-form-field>
                  </div>
                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline" class="example-full-width">
                      <mat-label>Number of pcs.</mat-label>
                      <input matInput placeholder="Container Number" formControlName="totalNumberOfPcs" />
                      <!-- <mat-select [(value)]="TotalNumberofpsc">
                        
                        <mat-option value="option1">Check 1 </mat-option>
                        <mat-option value="option2">Check 2</mat-option>
                        <mat-option value="option3">Check 3</mat-option>
                      </mat-select> -->
                    </mat-form-field>
                  </div>
                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline" class="example-full-width">
                      <mat-label>Area</mat-label>
                      <input matInput placeholder="Container Number" formControlName="totalQuantity" />
                    </mat-form-field>
                  </div>
                  <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline" class="example-full-width" floatLabel="always">
                      <mat-label>  Amount</mat-label>
                      <input matInput type="number" class="example-right-align" placeholder="0" formControlName="totalAmount" />
                    
                    </mat-form-field>
                  </div>
                  <div class="col-md-2">
                    <button mat-flat-button color="primary" *ngIf="!isUpdated" >Save</button>
                    <button mat-flat-button color="primary" type="button" (click)="updateContainer()"  *ngIf="isUpdated" >Update</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
        <div class="container-fluid vh-100">
          <div class="py-3 py-lg-4">
            <h4 class="page-title mb-0">Container Despatch Summary</h4>
            <div class="row mt-3">
            
                                <mat-form-field>
                                  <mat-label>Search</mat-label>
                                  <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Mia" #input>
                                </mat-form-field>
                                
                                <div class="mat-elevation-z8" style="overflow: auto; width: 1200px;">
                                  <table mat-table [dataSource]="dataSource" matSort>

                                     <!-- ID Column -->
                                     <ng-container matColumnDef="impoterNo">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Impoter Name</th>
                                      <td mat-cell *matCellDef="let row"> {{row.impoterNo}} </td>
                                    </ng-container>
                                    <!-- ID Column -->
                                    <!-- <ng-container matColumnDef="chooseAdate">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
                                      <td mat-cell *matCellDef="let row"> {{row.chooseAdate}} </td>
                                    </ng-container> -->
                                    
                                 <!-- Progress Column -->
                                    <!-- <ng-container matColumnDef="country">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header>country </th>
                                      <td mat-cell *matCellDef="let row"> {{row.country}}</td>
                                    </ng-container> -->
                                
                                    <!-- Name Column -->
                                    <ng-container matColumnDef="currency">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header> Currency  </th>
                                      <td mat-cell *matCellDef="let row"> {{row.currency}} </td>
                                    </ng-container>
                                
                                    <!-- Fruit Column -->
                                    <!-- <ng-container matColumnDef="containerNumber">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header> containerNumber </th>
                                      <td mat-cell *matCellDef="let row"> {{row.containerNumber}} </td>
                                    </ng-container> -->
                                    <!-- Fruit Column -->

                                    
                                    
                                    
                                    <ng-container matColumnDef="totalAmount">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header>  Amount </th>
                                      <td mat-cell *matCellDef="let row"> {{row.totalAmount}} </td>
                                    </ng-container>  
                                     <ng-container matColumnDef="id">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header> Sr No. </th>
                                      <td mat-cell *matCellDef="let row"> {{row.index}} </td>
                                    </ng-container>
                                    
                                    <!-- <ng-container matColumnDef="linerDetails">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header> linerDetails </th>
                                      <td mat-cell *matCellDef="let row"> {{row.linerDetails}} </td>
                                    </ng-container> -->
                                    <!-- Fruit Column -->
                                    <ng-container matColumnDef="noOfInvoice">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header> Invoice No.  </th>
                                      <td mat-cell *matCellDef="let row"> {{row.noOfInvoice}} </td>
                                    </ng-container>
                                    <!-- Fruit Column -->
                                    <ng-container matColumnDef="totalNumberOfPcs">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Area</th>
                                      <td mat-cell *matCellDef="let row"> {{row.totalNumberOfPcs}} </td>
                                    </ng-container>
                                    <!-- Fruit Column -->
                                    <ng-container matColumnDef="totalQuantity">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Number Of Pcs</th>
                                      <td mat-cell *matCellDef="let row"> {{row.totalQuantity}} </td>
                                    </ng-container>
                                

                                    <ng-container matColumnDef="action">
                                      <th mat-header-cell *matHeaderCellDef mat-sort-header>Action </th>
                                      <td mat-cell *matCellDef="let row"><a (click)="edit(row.id)"><i class="fa fa-pencil-square-o" aria-hidden="true"></i></a> <a (click)="deleteDespatchContainer(row.id)"><i class="fa fa-trash-o" aria-hidden="true"></i></a></td>
                                    </ng-container>
                                


                                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                                
                                    <!-- Row shown when there is no matching data. -->
                                    <tr class="mat-row" *matNoDataRow>
                                      <td class="mat-cell" colspan="4">No data matching the filter "{{input.value}}"</td>
                                    </tr>
                                  </table>
                                
                                  <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>
                                </div>
                                
                                
                                  
                                  
            </div>
            <hr />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
