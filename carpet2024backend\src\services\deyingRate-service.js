// services/deyingRateService.js
const deyingRateRepository = require('../repositories/deyingRate-repository');

async function createDeyingRate(data) {
  return await deyingRateRepository.createDeyingRate(data);
}

async function getDeyingRateById(id) {
  return await deyingRateRepository.getDeyingRateById(id);
}

async function getAllDeyingRates() {
  return await deyingRateRepository.getAllDeyingRates();
}

async function updateDeyingRate(id, data) {
  return await deyingRateRepository.updateDeyingRate(id, data);
}

async function deleteDeyingRate(id) {
  return await deyingRateRepository.deleteDeyingRate(id);
}

module.exports = {
  createDeyingRate,
  getDeyingRateById,
  getAllDeyingRates,
  updateDeyingRate,
  deleteDeyingRate
};
