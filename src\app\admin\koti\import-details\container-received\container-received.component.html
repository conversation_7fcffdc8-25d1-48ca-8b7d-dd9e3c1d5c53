<div class="px-3">

  <!-- Start Content-->
  <div class="container-fluid">

    <!-- start page title -->
    <div class="py-3 py-lg-4">
      <div class="row">
        <div class="col-lg-6">
          <h4 class="page-title mb-0">Add Container Received </h4>
        </div>

      </div>
    <hr/>

      <!-- Form row -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div class="card-body">

              <form [formGroup]="frmContainerRecieved" (ngSubmit)="addReceived()">
                <div class="row">
                  <div class="mb-2 col-md-4">

                    <mat-form-field appearance="outline">
                      <mat-label>Container No.</mat-label>
                      <mat-select formControlName="impoterName" (selectionChange)="getContainerData($event.value)">

                        @for (containerNo of containerList; track containerNo) {
                        <mat-option [value]="containerNo">{{containerNo}}</mat-option>
                        }
                      </mat-select>
                    </mat-form-field>

                  </div>
                  <div class="mb-2 col-md-4">

                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Expenses Amount</mat-label>
                      <input matInput type="number" autocomplete="off" placeholder="Expens Amount"
                        formControlName="expensesAmount" (blur)="inputExpenceAmount($event)">

                    </mat-form-field>
                  </div>

                  <div class="mb-2 col-md-4">

                    <label for="file" class="form-label">Upload File( .Pdf )</label>
                    <input type="file" class="form-control" (change)="uploadpdf($event)" />



                  </div>
                  <div class="mb-2 col-md-4">

                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Total Area</mat-label>
                      <input matInput type="number" autocomplete="off" placeholder="Total Area" readonly
                        formControlName="totalArea">

                    </mat-form-field>
                  </div>

                  <div class="mb-2 col-md-4">

                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>ESP.Price</mat-label>
                      <input matInput placeholder="ESP.Price" formControlName="espPrice" readonly>
                    </mat-form-field>

                  </div>
                  <div class="mb-2 col-md-4">

                    <button mat-flat-button color="primary">Set Bar Code</button>
                  </div>
                  <div class="col-md-4">
                    <button mat-button (click)="logSelection()">Log Selection</button>
                  </div>
                </div>
              </form>


            </div>

          </div>
        </div>

        <div class="col-xl-12">
          <hr class="hr-text" data-content="Packing list">

          <div class="mat-elevation-z8">
            <div class="mat-elevation-z8">
              <mat-form-field>
                <mat-label>Search</mat-label>
                <input matInput (keyup)="applyFilter($event)" placeholder="Type to filter...">
              </mat-form-field>
            </div>

            <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">


              <ng-container matColumnDef="select">
                <th mat-header-cell *matHeaderCellDef>
                  <mat-checkbox (change)="$event ? masterToggle() : null"
                    [checked]="selection.hasValue() && isAllSelected()"
                    [indeterminate]="selection.hasValue() && !isAllSelected()">
                  </mat-checkbox>
                </th>
                <td mat-cell *matCellDef="let row">
                  <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(row) : null"
                    [checked]="selection.isSelected(row)">
                  </mat-checkbox>
                </td>
              </ng-container>


              <ng-container matColumnDef="Sno">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>S No.</th>
                <td mat-cell *matCellDef="let element"> {{element.Sno}} </td>
              </ng-container>


              <ng-container matColumnDef="Amount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Amount</th>
                <td mat-cell *matCellDef="let element"> {{element.Amount}} </td>
              </ng-container>


              <ng-container matColumnDef="GerCarpetNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Ger Carpet No. </th>
                <td mat-cell *matCellDef="let element"> {{element.GerCarpetNo}} </td>
              </ng-container>


              <ng-container matColumnDef="QualityDesign">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality Design</th>
                <td mat-cell *matCellDef="let element">
                  {{element.QualityDesign}}
                </td>
              </ng-container>


              <ng-container matColumnDef="QCode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Q Code</th>
                <td mat-cell *matCellDef="let element"> {{ element.QCode ? element.QCode : '' }}
                  <a  *ngIf="!element.QCode" (click)="openDialog('/admin/quality-design-code')">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a>
                </td>
              </ng-container>

              <ng-container matColumnDef="Color">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Color</th>
                <td mat-cell *matCellDef="let element"> {{element.Color}} </td>
              </ng-container>

              <ng-container matColumnDef="CCode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> CCode</th>
                <td mat-cell *matCellDef="let element"> {{ element.CCode ? element.CCode : '' }}
                  <a  *ngIf="!element.CCode" (click)="openDialog('/admin/quality-design-code')">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a>
                </td>
              </ng-container>
              <ng-container matColumnDef="Size">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Size</th>
                <td mat-cell *matCellDef="let element"> {{element.Size}} </td>
              </ng-container>

              <ng-container matColumnDef="SCore">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> SCore</th>
                <td mat-cell *matCellDef="let element">
                  {{ element.SCore ? element.SCore : '' }}
                  <a  *ngIf="!element.SCore" (click)="openDialog('/admin/size-code')">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a>
                </td>
              </ng-container>

              <ng-container matColumnDef="Area">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Area</th>
                <td mat-cell *matCellDef="let element"> {{element.Area}} </td>
              </ng-container>

              <ng-container matColumnDef="Rate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Rate</th>
                <td mat-cell *matCellDef="let element"> {{element.Rate}} </td>
              </ng-container>

              <ng-container matColumnDef="EvKPrice">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> EvKPrice</th>
                <td mat-cell *matCellDef="let element"> {{element.EvKPrice}} </td>
              </ng-container>

              <ng-container matColumnDef="InvoiceNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Invoice No</th>
                <td mat-cell *matCellDef="let element"> {{element.InvoiceNo}} </td>
              </ng-container>

              <ng-container matColumnDef="ImporterCode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Importer Code</th>
                <td mat-cell *matCellDef="let element"> {{element.ImporterCode}} </td>
              </ng-container>

              <ng-container matColumnDef="Remarks">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Remarks</th>
                <td mat-cell *matCellDef="let element"> {{element.Remarks}} </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <mat-paginator [pageSizeOptions]="[5, 10, 20,100]" showFirstLastButtons></mat-paginator>
          </div>


         </div>
      </div>
        
         

