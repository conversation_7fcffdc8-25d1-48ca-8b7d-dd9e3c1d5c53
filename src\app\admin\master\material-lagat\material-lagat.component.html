<div class="container mt-4">
  <section>
    <fieldset>
      <legend>
        <b>{{ isUpdated ? "Update" : "Add" }} Material Lagat</b>
      </legend>
      <form [formGroup]="frmMaterialLagat" (ngSubmit)="addLagat()">
        <div class="row">
          <div class="col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>To Date</mat-label>
              <input
                matInput
                [matDatepicker]="picker"
                formControlName="todate"
              />
              <mat-datepicker-toggle
                matIconSuffix
                [for]="picker"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>From Date</mat-label>
              <input
                matInput
                [matDatepicker]="picker1"
                formControlName="fromdate"
              />
              <mat-datepicker-toggle
                matIconSuffix
                [for]="picker1"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker1></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="col-md-2">
            <mat-form-field appearance="outline">
              <mat-label>Quality</mat-label>
              <mat-select
                formControlName="quality"
                (valueChange)="onQuality($event)"
              >
                <mat-option
                  *ngFor="let quality of qualityList"
                  [value]="quality.id"
                >
                  {{ quality.quality }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline">
              <mat-label>Design</mat-label>
              <mat-select
                formControlName="AddDesign"
                (valueChange)="onDesign($event)"
              >
                <mat-option
                  *ngFor="let design of designList"
                  [value]="design._id"
                >
                  {{ design.design }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="col-md-2 d-flex">
            <mat-form-field appearance="outline">
              <mat-label>Ground Colour</mat-label>
              <input
                matInput
                formControlName="groundColour"
                placeholder="Ground Colour"
              />
            </mat-form-field>
            <mat-form-field appearance="outline">
              <mat-label>Border Colour</mat-label>
              <input
                matInput
                formControlName="borderColour"
                placeholder="Border Colour"
              />
            </mat-form-field>
          </div>
        </div>

        <div class="row">
          <div class="side">
            <h3><b>Description</b></h3>

            <h3><b>Lagat per sq yard</b></h3>

            <h3><b>Rate per kg</b></h3>
          </div>

          <div class="mb-2 col-sm-3 offset-2">
            <mat-form-field appearance="outline">
              <mat-label>Kati</mat-label>
              <mat-select formControlName="katiDescription">
                <mat-option
                  *ngFor="let desc of katiDescriptionList"
                  [value]="desc.id"
                >
                  {{ desc.description }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Kati</mat-label>
              <input matInput placeholder=".000" (blur)="setDigitKatiLagat($event)" formControlName="katiLagat" />
            </mat-form-field>
          </div>

          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Kati</mat-label>
              <input matInput placeholder=".00" (blur)="setDigitKatiRate($event)" formControlName="katiRatePerkg" />
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-3 offset-2">
            <mat-form-field appearance="outline">
              <mat-label>Tana</mat-label>
              <mat-select formControlName="tanaDescription">
                <mat-option
                  *ngFor="let desc of tanaDescriptionList"
                  [value]="desc.id"
                >
                  {{ desc.description }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Tana</mat-label>
              <input matInput placeholder=".000" (blur)="setDigitTanaLagat($event)" formControlName="tanaLagat" />
            </mat-form-field>
          </div>

          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Tana</mat-label>
              <input
                matInput
               placeholder=".00" (blur)="setDigitTanaRate($event)"
                formControlName="tanaRatePerkg"
              />
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-3 offset-2">
            <mat-form-field appearance="outline">
              <mat-label>Soot</mat-label>
              <mat-select formControlName="sootDescription">
                <mat-option
                  *ngFor="let desc of sootDescriptionList"
                  [value]="desc.id"
                >
                  {{ desc.description }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Soot</mat-label>
              <input matInput placeholder=".000" (blur)="setDigitSootLagat($event)" formControlName="sootLagat" />
            </mat-form-field>
          </div>

          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Soot</mat-label>
              <input matInput placeholder=".00" (blur)="setDigitSootRate($event)" formControlName="sootRatePerkg" />
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-3 offset-2">
            <mat-form-field appearance="outline">
              <mat-label>Tharri</mat-label>
              <mat-select formControlName="tharriDescription">
                <mat-option
                  *ngFor="let desc of tharriDescriptionList"
                  [value]="desc.id"
                >
                  {{ desc.description }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Tharri</mat-label>
              <input matInput placeholder=".000" (blur)="setDigitTharriLagat($event)" formControlName="tharriLagat" />
            </mat-form-field>
          </div>

          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Tharri</mat-label>
              <input
                matInput
               placeholder=".00" (blur)="setDigitTharriRate($event)"
                formControlName="tharriRatePerkg"
              />
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-3 offset-2">
            <mat-form-field appearance="outline">
              <mat-label>Silk</mat-label>
              <mat-select formControlName="silkDescription">
                <mat-option
                  *ngFor="let desc of silkDescriptionList"
                  [value]="desc.id"
                >
                  {{ desc.description }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Silk</mat-label>
              <input matInput placeholder=".000" (blur)="setDigitSilkLagat($event)" formControlName="silkLagat" />
            </mat-form-field>
          </div>

          <div class="mb-2 col-sm-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Silk</mat-label>
              <input matInput placeholder=".00" (blur)="setDigitSilkRate($event)" formControlName="silkRatePerkg" />
            </mat-form-field>
          </div>
<div style="max-height: 215px; min-height: 0px; overflow: auto;">
          <div formArrayName="item">
            <div
              *ngFor="let item of item.controls; let i = index"
              [formGroupName]="i"
            >
              <div class="row">
                <div class="col-sm-3 offset-2">
                  <mat-form-field appearance="outline">
                    <mat-label>Other</mat-label>
                    <mat-select formControlName="description">
                      <mat-option
                        *ngFor="let desc of othersDescriptionList"
                        [value]="desc.id"
                      >
                        {{ desc.description }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
                <div class="col-sm-3">
                  <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Other</mat-label>
                    <input
                      matInput
                      placeholder=".000"
                      (blur)="setDigitOtherLagat($event, i)"
                      formControlName="lagat"
                    />
                  </mat-form-field>
                </div>

                <div class="col-sm-3">
                  <mat-form-field appearance="outline" class="ex-width">
                    <mat-label>Other</mat-label>
                    <input
                      matInput
                      placeholder=".00" (blur)="setDigitOtherRate($event, i)"
                      formControlName="ratePerkg"
                    />
                  </mat-form-field>
                </div>

                <div
                  class="col-md-1"
                  style="padding-top: 18px; padding-left: 0px"
                >
                  <a (click)="removeItem(i)"
                    ><i
                      class="fa fa-trash-o"
                      aria-hidden="true"
                      title="Remove"
                    ></i
                  ></a>
                </div>
              </div>
            </div>
          </div>
</div>
          <div class="col-md-4 offset-2">
            <button type="button" class="btn btn-primary" (click)="addItem()">
              Add Others
            </button>
            &nbsp;
            <button
              *ngIf="isUpdated; else addButton"
              mat-raised-button
              type="button"
              (click)="update()"
              color="primary"
            >
              Update
            </button>
            <ng-template #addButton>
              <button mat-raised-button color="primary">Save</button>
            </ng-template>

            &nbsp;
          </div>
        </div>
      </form>
    </fieldset>
  </section>
</div>

<section class="mt-4">
  <fieldset>
    <legend><b>Material Lagat</b></legend>
    <div class="row">
      <div class="col-md-4"></div>
      <div class="col-md-12">
        <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef>No.</th>
            <td mat-cell *matCellDef="let element">{{ element.index }}</td>
          </ng-container>

          <ng-container matColumnDef="quality">
            <th mat-header-cell *matHeaderCellDef>Quality</th>
            <td mat-cell *matCellDef="let element">{{ element.quality }}</td>
          </ng-container>

          <ng-container matColumnDef="todate">
            <th mat-header-cell *matHeaderCellDef>To Date</th>
            <td mat-cell *matCellDef="let element">{{ element.todate }}</td>
          </ng-container>
          <ng-container matColumnDef="fromdate">
            <th mat-header-cell *matHeaderCellDef>From Date</th>
            <td mat-cell *matCellDef="let element">{{ element.fromdate }}</td>
          </ng-container>

          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef>Action</th>
            <td mat-cell *matCellDef="let element">
              <a
                (click)="view(element.id)"
                data-bs-toggle="modal"
                data-bs-target="#addControlModal"
              >
                <i class="fa fa-eye" aria-hidden="true"></i>
              </a>
              &nbsp;
              <a (click)="edit(element.id)"
                ><i
                  class="fa fa-pencil-square-o fa-edit"
                  title="Edit purchase details "
                  aria-hidden="true"
                ></i
              ></a>
              &nbsp;
              <a (click)="delete(element.id)"
                ><i class="fa fa-trash-o" aria-hidden="true"></i
              ></a>
            </td>
          </ng-container>

          <ng-container matColumnDef="addDesign">
            <th mat-header-cell *matHeaderCellDef>Design</th>
            <td mat-cell *matCellDef="let element">{{ element.addDesign }}</td>
          </ng-container>

          <ng-container matColumnDef="color">
            <th mat-header-cell *matHeaderCellDef>Color</th>
            <td mat-cell *matCellDef="let element">{{ element.color }}</td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 25, 100]"
          aria-label="Select page of users"
        ></mat-paginator>
      </div>
    </div>
  </fieldset>
</section>

<div
  class="modal fade"
  id="addControlModal"
  tabindex="-1"
  aria-labelledby="addControlModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content" style="width: max-content">
      <div class="modal-header">
        <h4
          style="padding-left: 40%"
          class="modal-title"
          id="addControlModalLabel"
        >
          Material Lagat
        </h4>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <table class="table">
          <thead class="thead-dark">
            <tr>
              <th scope="col">#</th>
              <th scope="col">Lagat per sq yard</th>
              <th scope="col">Description</th>
              <th scope="col">Rate Per Kg.</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Kati</td>
              <td>{{ viewLagat?.katiLagat }}</td>
              <td>{{ viewLagat?.katiDescription.Description }}</td>
              <td>{{ viewLagat?.katiRatePerkg }}</td>
            </tr>
            <tr>
              <td>Tana</td>
              <td>{{ viewLagat?.tanaLagat }}</td>
              <td>{{ viewLagat?.tanaDescription.Description }}</td>
              <td>{{ viewLagat?.tanaRatePerkg }}</td>
            </tr>
            <tr>
              <td>Soot</td>
              <td>{{ viewLagat?.sootLagat }}</td>
              <td>{{ viewLagat?.sootDescription.Description }}</td>
              <td>{{ viewLagat?.sootRatePerkg }}</td>
            </tr>

            <tr>
              <td>Tharri</td>
              <td>{{ viewLagat?.tharriLagat }}</td>
              <td>{{ viewLagat?.tharriDescription.Description }}</td>
              <td>{{ viewLagat?.tharriRatePerkg }}</td>
            </tr>
            <tr>
              <td>Silk</td>
              <td>{{ viewLagat?.silkLagat }}</td>
              <td>{{ viewLagat?.silkDescription.Description }}</td>
              <td>{{ viewLagat?.silkRatePerkg }}</td>
            </tr>
            <!-- <tr class="text-center">
            <td colspan="4">Others</td>
           </tr> -->
            <tr class="text-center">
              <td colspan="4" class="others-cell">
                <span class="others-text">Others</span>
              </td>
            </tr>

            <tr *ngFor="let dd of viewLagat?.item; let i = index">
              <td></td>
              <td>{{ dd?.lagat }}</td>
              <td>{{ dd?.description.Description }}</td>
              <td>{{ dd?.ratePerkg }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
