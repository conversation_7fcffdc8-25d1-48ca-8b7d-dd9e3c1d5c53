const mongoose = require("mongoose");
const billForWholesellerSchema = new mongoose.Schema(
  {
    billNo: {
      type: String,
      required: true,
      unique: true,
    },
    chooseAdate: {
      type: String,
      required: true,
    },
    // challanNo: {
    //   type: [String], // Make it an array of strings
    //   required: true,
    // },
    challanNo: [
      {
        challanNumber: { type: String }, // Make it an array of strings
        status: { type: String },
      },
    ],
    wholesellerName: {
      type: String,
      required: true,
    },
    isBillDeleted: { type: Boolean, default: false },
    total: { type: Number, required: false }, // Added total
    profit: { type: Number, required: false }, // Added profit
    gst: { type: Number, required: false }, // Added GST 19%
    discount: { type: Number, required: false }, // Added discount
  },
  { timestamps: true }
);

const BillForWholeseller = mongoose.model(
  "BillForWholeseller",
  billForWholesellerSchema
);

module.exports = BillForWholeseller;
