const mongoose = require('mongoose');

const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function checkWeaverNames() {
  try {
    // Connect to MongoDB
    await mongoose.connect(DB_URL);
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get all H- records
    const hRecords = await collection.find({ receiveNo: { $regex: /^H-/ } }).toArray();
    
    console.log(`\n📊 Found ${hRecords.length} H- records`);
    console.log('\n🔍 Checking weaver names:');
    console.log('='.repeat(80));
    
    hRecords.forEach((record, index) => {
      console.log(`${index + 1}. ${record.receiveNo}:`);
      console.log(`   weaverName: "${record.weaverName}"`);
      console.log(`   weaverNumber: ${record.weaverNumber}`);
      console.log('   ---');
    });
    
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkWeaverNames();
