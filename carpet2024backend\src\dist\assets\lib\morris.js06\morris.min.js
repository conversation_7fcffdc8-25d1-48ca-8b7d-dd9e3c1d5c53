/* @license
morris.js06 v0.6.9 
https://pierresh.github.io/morris.js/
Copyright 2021 O<PERSON> All rights reserved.
Licensed under the BSD-2-Clause License.
*/
(function(){var a,b,c,d,e=[].slice,f=function(a,b){return function(){return a.apply(b,arguments)}},g={}.hasOwnProperty,h=function(a,b){function c(){this.constructor=a}for(var d in b)g.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a},i=[].indexOf||function(a){for(var b=0,c=this.length;c>b;b++)if(b in this&&this[b]===a)return b;return-1};a=window.Morris={},b=function(a){return getComputedStyle?getComputedStyle(a,null):a.currentStyle?a.currentStyle:a.style},a.EventEmitter=function(){function a(){}return a.prototype.on=function(a,b){return null==this.handlers&&(this.handlers={}),null==this.handlers[a]&&(this.handlers[a]=[]),this.handlers[a].push(b),this},a.prototype.fire=function(){var a,b,c,d,f,g,h;if(c=arguments[0],a=2<=arguments.length?e.call(arguments,1):[],null!=this.handlers&&null!=this.handlers[c]){for(g=this.handlers[c],h=[],d=0,f=g.length;f>d;d++)b=g[d],h.push(b.apply(null,a));return h}},a}(),a.commas=function(a){var b,c,d,e;return null!=a?(d=0>a?"-":"",b=Math.abs(a),c=Math.floor(b).toFixed(0),d+=c.replace(/(?=(?:\d{3})+$)(?!^)/g,","),e=b.toString(),e.length>c.length&&(d+=e.slice(c.length)),d):"-"},a.pad2=function(a){return(10>a?"0":"")+a},a.extend=function(){var a,b,c,d,f,g,h;for(b=arguments[0],c=2<=arguments.length?e.call(arguments,1):[],null==b&&(b={}),g=0,h=c.length;h>g;g++)if(d=c[g],null!=d)for(a in d)f=d[a],d.hasOwnProperty(a)&&(b[a]=f);return b},a.offset=function(a){var b;return b=a.getBoundingClientRect(),{top:b.top+document.body.scrollTop,left:b.left+document.body.scrollLeft}},a.css=function(a,c){return b(a)[c]},a.on=function(a,b,c){return a.addEventListener?a.addEventListener(b,c):a.attachEvent("on"+b,c)},a.off=function(a,b,c){return a.removeEventListener?a.removeEventListener(b,c):a.detachEvent("on"+b,c)},a.dimensions=function(a){var c;return c=b(a),{width:parseInt(c.width),height:parseInt(c.height)}},a.innerDimensions=function(a){var c;return c=b(a),{width:parseInt(c.width)+parseInt(c.paddingLeft)+parseInt(c.paddingRight),height:parseInt(c.height)+parseInt(c.paddingTop)+parseInt(c.paddingBottom)}},a.Grid=function(b){function c(b){if(this.setLabels=f(this.setLabels,this),this.hasToShow=f(this.hasToShow,this),this.debouncedResizeHandler=f(this.debouncedResizeHandler,this),this.resizeHandler=f(this.resizeHandler,this),this.mouseupHandler=f(this.mouseupHandler,this),this.mousedownHandler=f(this.mousedownHandler,this),this.clickHandler=f(this.clickHandler,this),this.touchHandler=f(this.touchHandler,this),this.mouseleaveHandler=f(this.mouseleaveHandler,this),this.mousemoveHandler=f(this.mousemoveHandler,this),"string"==typeof b.element?this.el=document.getElementById(b.element):this.el=b.element[0]||b.element,null==this.el)throw new Error("Graph container element not found");"static"===a.css(this.el,"position")&&(this.el.style.position="relative"),this.options=a.extend({},this.gridDefaults,this.defaults||{},b),"string"==typeof this.options.units&&(this.options.postUnits=b.units),this.raphael=new Raphael(this.el),this.elementWidth=null,this.elementHeight=null,this.dirty=!1,this.selectFrom=null,this.init&&this.init(),this.setData(this.options.data),a.on(this.el,"mousemove",this.mousemoveHandler),a.on(this.el,"mouseleave",this.mouseleaveHandler),a.on(this.el,"touchstart touchmove touchend",this.touchHandler),a.on(this.el,"click",this.clickHandler),this.options.rangeSelect&&(this.selectionRect=this.raphael.rect(0,0,0,a.innerDimensions(this.el).height).attr({fill:this.options.rangeSelectColor,stroke:!1}).toBack().hide(),a.on(this.el,"mousedown",this.mousedownHandler),a.on(this.el,"mouseup",this.mouseupHandler)),this.options.resize&&a.on(window,"resize",this.resizeHandler),this.el.style.webkitTapHighlightColor="rgba(0,0,0,0)",this.postInit&&this.postInit()}return h(c,b),c.prototype.gridDefaults={dateFormat:null,axes:!0,freePosition:!1,grid:!0,gridIntegers:!1,gridLineColor:"#aaa",gridStrokeWidth:.5,gridTextColor:"#888",gridTextSize:12,gridTextFamily:"sans-serif",gridTextWeight:"normal",hideHover:"auto",yLabelFormat:null,yLabelAlign:"right",yLabelAlign2:"left",xLabelAngle:0,numLines:5,padding:25,parseTime:!0,postUnits:"",postUnits2:"",preUnits:"",preUnits2:"",ymax:"auto",ymin:"auto 0",ymax2:"auto",ymin2:"auto 0",regions:[],regionsColors:["#fde4e4"],goals:[],goals2:[],goalStrokeWidth:1,goalStrokeWidth2:1,goalLineColors:["red"],goalLineColors2:["red"],events:[],eventStrokeWidth:1,eventLineColors:["#005a04"],rangeSelect:null,rangeSelectColor:"#eef",resize:!0,dataLabels:!0,dataLabelsPosition:"outside",dataLabelsFamily:"sans-serif",dataLabelsSize:12,dataLabelsWeight:"normal",dataLabelsColor:"auto",animate:!0,nbYkeys2:0,smooth:!0},c.prototype.destroy=function(){return a.off(this.el,"mousemove",this.mousemoveHandler),a.off(this.el,"mouseleave",this.mouseleaveHandler),a.off(this.el,"touchstart touchmove touchend",this.touchHandler),a.off(this.el,"click",this.clickHandler),this.options.rangeSelect&&(a.off(this.el,"mousedown",this.mousedownHandler),a.off(this.el,"mouseup",this.mouseupHandler)),this.options.resize?(window.clearTimeout(this.timeoutId),a.off(window,"resize",this.resizeHandler)):void 0},c.prototype.setData=function(b,c){var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A;if(null==c&&(c=!0),this.options.data=b,null==b||0===b.length)return this.data=[],this.raphael.clear(),void(null!=this.hover&&this.hover.hide());if(s=this.cumulative?0:null,u=this.cumulative?0:null,t=this.cumulative?0:null,v=this.cumulative?0:null,this.options.goals.length>0&&(j=Math.min.apply(Math,this.options.goals),i=Math.max.apply(Math,this.options.goals),u=null!=u?Math.min(u,j):j,s=null!=s?Math.max(s,i):i),this.options.goals2.length>0&&(j=Math.min.apply(Math,this.options.goals2),i=Math.max.apply(Math,this.options.goals2),v=null!=v?Math.min(v,j):j,t=null!=t?Math.max(t,i):i),this.options.nbYkeys2>this.options.ykeys.length&&(this.options.nbYkeys2=this.options.ykeys.length),this.data=function(){var c,d,e;for(e=[],h=c=0,d=b.length;d>c;h=++c)l=b[h],k={src:l},k.label=l[this.options.xkey],this.options.parseTime?(k.x=a.parseDate(k.label),this.options.dateFormat?k.label=this.options.dateFormat(k.x):"number"==typeof k.label&&(k.label=new Date(k.label).toString())):this.options.freePosition?(k.x=parseFloat(l[this.options.xkey]),this.options.xLabelFormat&&(k.label=this.options.xLabelFormat(k))):(k.x=h,this.options.xLabelFormat&&(k.label=this.options.xLabelFormat(k))),p=0,k.y=function(){var a,b,c,d;for(c=this.options.ykeys,d=[],g=a=0,b=c.length;b>a;g=++a)r=c[g],w=l[r],"string"==typeof w&&(w=parseFloat(w)),null!=w&&"number"!=typeof w&&(w=null),g<this.options.ykeys.length-this.options.nbYkeys2?(null!=w&&this.hasToShow(g)&&(this.cumulative?0>p&&w>0?p=w:p+=w:null!=s?(s=Math.max(w,s),u=Math.min(w,u)):s=u=w),this.cumulative&&null!=p&&(s=Math.max(p,s),u=Math.min(p,u))):(null!=w&&this.hasToShow(g)&&(this.cumulative?p=w:null!=t?(t=Math.max(w,t),v=Math.min(w,v)):t=v=w),this.cumulative&&null!=p&&(t=Math.max(p,t),v=Math.min(p,v))),d.push(w);return d}.call(this),e.push(k);return e}.call(this),(this.options.parseTime||this.options.freePosition)&&(this.data=this.data.sort(function(a,b){return(a.x>b.x)-(b.x>a.x)})),this.xmin=this.data[0].x,this.xmax=this.data[this.data.length-1].x,this.events=[],this.options.events.length>0){if(this.options.parseTime)for(z=this.options.events,x=0,y=z.length;y>x;x++)d=z[x],d instanceof Array?(f=d[0],o=d[1],this.events.push([a.parseDate(f),a.parseDate(o)])):this.events.push(a.parseDate(d));else this.events=this.options.events;e=this.events.map(function(a){return a}),this.xmax=Math.max(this.xmax,Math.max.apply(Math,e)),this.xmin=Math.min(this.xmin,Math.min.apply(Math,e))}return this.xmin===this.xmax&&(this.xmin-=1,this.xmax+=1),this.ymin=this.yboundary("min",u),this.ymax=this.yboundary("max",s),this.ymin2=this.yboundary("min2",v),this.ymax2=this.yboundary("max2",t),this.ymin===this.ymax&&(u&&(this.ymin-=1),this.ymax+=1),this.ymin2===this.ymax2&&(v&&(this.ymin2-=1),this.ymax2+=1),((A=this.options.axes)===!0||"both"===A||"y"===A||this.options.grid===!0)&&(this.options.ymax===this.gridDefaults.ymax&&this.options.ymin===this.gridDefaults.ymin?(this.grid=this.autoGridLines(this.ymin,this.ymax,this.options.numLines),this.ymin=Math.min(this.ymin,this.grid[0]),this.ymax=Math.max(this.ymax,this.grid[this.grid.length-1])):(m=(this.ymax-this.ymin)/(this.options.numLines-1),this.options.gridIntegers&&(m=Math.max(1,Math.round(m))),this.grid=function(){var a,b,c,d;for(d=[],q=a=b=this.ymin,c=this.ymax;m>0?c>=a:a>=c;q=a+=m)d.push(parseFloat(q.toFixed(2)));return d}.call(this)),this.options.ymax2===this.gridDefaults.ymax2&&this.options.ymin2===this.gridDefaults.ymin2&&this.options.nbYkeys2>0?(this.grid2=this.autoGridLines(this.ymin2,this.ymax2,this.options.numLines),this.ymin2=Math.min(this.ymin2,this.grid2[0]),this.ymax2=Math.max(this.ymax2,this.grid2[this.grid2.length-1])):(n=(this.ymax2-this.ymin2)/(this.options.numLines-1),this.grid2=function(){var a,b,c,d;for(d=[],q=a=b=this.ymin2,c=this.ymax2;n>0?c>=a:a>=c;q=a+=n)d.push(parseFloat(q.toFixed(2)));return d}.call(this))),this.dirty=!0,c?this.redraw():void 0},c.prototype.yboundary=function(a,b){var c,d;return c=this.options["y"+a],"string"==typeof c?"auto"===c.slice(0,4)?c.length>5?(d=parseInt(c.slice(5),10),null==b?d:Math[a.substring(0,3)](b,d)):null!=b?b:0:parseInt(c,10):c},c.prototype.autoGridLines=function(a,b,c){var d,e,f,g,h,i,j,k,l;return h=b-a,l=Math.floor(Math.log(h)/Math.log(10)),j=Math.pow(10,l),e=Math.floor(a/j)*j,d=Math.ceil(b/j)*j,i=(d-e)/(c-1),1===j&&i>1&&Math.ceil(i)!==i&&(i=Math.ceil(i),d=e+i*(c-1)),0>e&&d>0&&(e=Math.floor(a/i)*i,d=Math.ceil(b/i)*i),1>i?(g=Math.floor(Math.log(i)/Math.log(10)),f=function(){var a,b;for(b=[],k=a=e;i>0?d>=a:a>=d;k=a+=i)b.push(parseFloat(k.toFixed(1-g)));return b}()):f=function(){var a,b;for(b=[],k=a=e;i>0?d>=a:a>=d;k=a+=i)b.push(k);return b}(),f},c.prototype._calc=function(){var b,c,d,e,f,g,h,i,j,k,l;return j=a.dimensions(this.el),g=j.width,e=j.height,(this.elementWidth!==g||this.elementHeight!==e||this.dirty)&&(this.elementWidth=g,this.elementHeight=e,this.dirty=!1,this.left=this.options.padding,this.right=this.elementWidth-this.options.padding,this.top=this.options.padding,this.bottom=this.elementHeight-this.options.padding,((k=this.options.axes)===!0||"both"===k||"y"===k)&&(null!=this.grid&&(h=function(){var a,b,c,e;for(c=this.grid,e=[],a=0,b=c.length;b>a;a++)d=c[a],e.push(this.measureText(this.yAxisFormat(d)).width);return e}.call(this)),this.options.nbYkeys2>0&&(i=function(){var a,b,c,e;for(c=this.grid2,e=[],a=0,b=c.length;b>a;a++)d=c[a],e.push(this.measureText(this.yAxisFormat2(d)).width);return e}.call(this)),this.options.horizontal?this.bottom-=this.options.padding/2:(this.left+=Math.max.apply(Math,h),this.options.nbYkeys2>0&&(this.right-=Math.max.apply(Math,i)))),((l=this.options.axes)===!0||"both"===l||"x"===l)&&(b=this.options.horizontal?-90:-this.options.xLabelAngle,c=function(){var a,c,d;for(d=[],f=a=0,c=this.data.length;c>=0?c>a:a>c;f=c>=0?++a:--a)d.push(this.measureText(this.data[f].label,b).height);return d}.call(this),this.options.horizontal?this.left+=Math.max.apply(Math,c):this.bottom-=Math.max.apply(Math,c)),this.width=Math.max(1,this.right-this.left),this.height=Math.max(1,this.bottom-this.top),this.options.horizontal?(this.dx=this.height/(this.xmax-this.xmin),this.dy=this.width/(this.ymax-this.ymin),this.dy2=this.width/(this.ymax2-this.ymin2),this.yStart=this.left,this.yEnd=this.right,this.xStart=this.top,this.xEnd=this.bottom,this.xSize=this.height,this.ySize=this.width):(this.dx=this.width/(this.xmax-this.xmin),this.dy=this.height/(this.ymax-this.ymin),this.dy2=this.height/(this.ymax2-this.ymin2),this.yStart=this.bottom,this.yEnd=this.top,this.xStart=this.left,this.xEnd=this.right,this.xSize=this.width,this.ySize=this.height),this.calc)?this.calc():void 0},c.prototype.transY=function(a){return this.options.horizontal?this.left+(a-this.ymin)*this.dy:this.bottom-(a-this.ymin)*this.dy},c.prototype.transY2=function(a){return this.options.horizontal?this.left+(a-this.ymin2)*this.dy2:this.bottom-(a-this.ymin2)*this.dy2},c.prototype.transX=function(a){return 1===this.data.length?(this.xStart+this.xEnd)/2:this.xStart+(a-this.xmin)*this.dx},c.prototype.redraw=function(){return this.raphael.clear(),this._calc(),this.drawGrid(),this.drawRegions(),this.drawEvents(),this.draw&&this.draw(),this.drawGoals(),this.setLabels()},c.prototype.measureText=function(a,b){var c,d;return null==b&&(b=0),d=this.raphael.text(100,100,a).attr("font-size",this.options.gridTextSize).attr("font-family",this.options.gridTextFamily).attr("font-weight",this.options.gridTextWeight).rotate(b),c=d.getBBox(),d.remove(),c},c.prototype.yAxisFormat=function(a){return this.yLabelFormat(a,0)},c.prototype.yAxisFormat2=function(a){return this.yLabelFormat(a,1e3)},c.prototype.yLabelFormat=function(b,c){return"function"==typeof this.options.yLabelFormat?this.options.yLabelFormat(b,c):0===this.options.nbYkeys2||c<=this.options.ykeys.length-this.options.nbYkeys2-1?""+this.options.preUnits+a.commas(b)+this.options.postUnits:""+this.options.preUnits2+a.commas(b)+this.options.postUnits2},c.prototype.yLabelFormat_noUnit=function(b,c){return"function"==typeof this.options.yLabelFormat?this.options.yLabelFormat(b,c):""+a.commas(b)},c.prototype.getYAxisLabelX=function(){return"right"===this.options.yLabelAlign?this.left-this.options.padding/2:this.options.padding/2},c.prototype.drawGrid=function(){var a,b,c,d,e,f,g,h,i,j,k,l,m,n;if(this.options.grid!==!1||(i=this.options.axes)===!0||"both"===i||"y"===i){if(this.options.horizontal?(a=this.getXAxisLabelY(),b=this.top-(this.options.xAxisLabelTopPadding||this.options.padding/2)):(a=this.getYAxisLabelX(),b=this.right+this.options.padding/2),null!=this.grid)for(j=this.grid,e=0,g=j.length;g>e;e++)c=j[e],d=this.transY(c),((k=this.options.axes)===!0||"both"===k||"y"===k)&&(this.options.horizontal?this.drawXAxisLabel(d,a,this.yAxisFormat(c)):this.drawYAxisLabel(a,d,this.yAxisFormat(c),1)),this.options.grid&&(d=Math.floor(d)+.5,this.options.horizontal?this.drawGridLine("M"+d+","+this.xStart+"V"+this.xEnd):(isNaN(this.xEnd)&&(this.xEnd=20),this.drawGridLine("M"+this.xStart+","+d+"H"+this.xEnd)));if(this.options.nbYkeys2>0){for(l=this.grid2,n=[],f=0,h=l.length;h>f;f++)c=l[f],d=this.transY2(c),(m=this.options.axes)===!0||"both"===m||"y"===m?this.options.horizontal?n.push(this.drawXAxisLabel(d,b,this.yAxisFormat2(c))):n.push(this.drawYAxisLabel(b,d,this.yAxisFormat2(c),2)):n.push(void 0);return n}}},c.prototype.drawRegions=function(){var a,b,c,d,e,f,g;for(f=this.options.regions,g=[],b=d=0,e=f.length;e>d;b=++d)c=f[b],a=this.options.regionsColors[b%this.options.regionsColors.length],g.push(this.drawRegion(c,a));return g},c.prototype.drawGoals=function(){var a,b,c,d,e,f,g,h,i,j;for(h=this.options.goals,c=d=0,f=h.length;f>d;c=++d)b=h[c],a=this.options.goalLineColors[c%this.options.goalLineColors.length],this.drawGoal(b,a);for(i=this.options.goals2,j=[],c=e=0,g=i.length;g>e;c=++e)b=i[c],a=this.options.goalLineColors2[c%this.options.goalLineColors2.length],j.push(this.drawGoal2(b,a));return j},c.prototype.drawEvents=function(){var a,b,c,d,e,f,g;if(null!=this.events){for(f=this.events,g=[],c=d=0,e=f.length;e>d;c=++d)b=f[c],a=this.options.eventLineColors[c%this.options.eventLineColors.length],g.push(this.drawEvent(b,a));return g}},c.prototype.drawGoal=function(a,b){var c,d;return d=Math.floor(this.transY(a))+.5,c=this.options.horizontal?"M"+d+","+this.xStart+"V"+this.xEnd:"M"+this.xStart+","+d+"H"+this.xEnd,this.raphael.path(c).attr("stroke",b).attr("stroke-width",this.options.goalStrokeWidth)},c.prototype.drawGoal2=function(a,b){var c,d;return d=Math.floor(this.transY2(a))+.5,c=this.options.horizontal?"M"+d+","+this.xStart+"V"+this.xEnd:"M"+this.xStart+","+d+"H"+this.xEnd,this.raphael.path(c).attr("stroke",b).attr("stroke-width",this.options.goalStrokeWidth2)},c.prototype.drawRegion=function(a,b){var c,d,e,f;return a instanceof Array?(c=Math.min(Math.max.apply(Math,a),this.ymax),e=Math.max(Math.min.apply(Math,a),this.ymin),this.options.horizontal?(e=Math.floor(this.transY(e)),c=Math.floor(this.transY(c))-e,this.raphael.rect(e,this.xStart,c,this.xEnd-this.xStart).attr({fill:b,stroke:!1}).toBack()):(c=Math.floor(this.transY(c)),e=Math.floor(this.transY(e))-c,this.raphael.rect(this.xStart,c,this.xEnd-this.xStart,e).attr({fill:b,stroke:!1}).toBack())):this.options.horizontal?(f=Math.floor(this.transY(area))+1,d="M"+f+","+this.xStart+"V"+this.xEnd,this.raphael.path(d).attr("stroke",b).attr("stroke-width",2)):(f=Math.floor(this.transY(area))+1,d="M"+this.xStart+","+f+"H"+this.xEnd,this.raphael.path(d).attr("stroke",b).attr("stroke-width",2))},c.prototype.drawEvent=function(a,b){var c,d,e,f;return a instanceof Array?(c=a[0],e=a[1],c=Math.floor(this.transX(c))+.5,e=Math.floor(this.transX(e))+.5,this.options.horizontal?this.raphael.rect(this.yStart,c,this.yEnd-this.yStart,e-c).attr({fill:b,stroke:!1}).toBack():this.raphael.rect(c,this.yEnd,e-c,this.yStart-this.yEnd).attr({fill:b,stroke:!1}).toBack()):(f=Math.floor(this.transX(a))+.5,d=this.options.horizontal?"M"+this.yStart+","+f+"H"+this.yEnd:"M"+f+","+this.yStart+"V"+this.yEnd,this.raphael.path(d).attr("stroke",b).attr("stroke-width",this.options.eventStrokeWidth))},c.prototype.drawYAxisLabel=function(a,b,c,d){var e;return e=this.raphael.text(a,b,c).attr("font-size",this.options.gridTextSize).attr("font-family",this.options.gridTextFamily).attr("font-weight",this.options.gridTextWeight).attr("fill",this.options.gridTextColor),1===d?"right"===this.options.yLabelAlign?e.attr("text-anchor","end"):e.attr("text-anchor","start"):"left"===this.options.yLabelAlign2?e.attr("text-anchor","start"):e.attr("text-anchor","end")},c.prototype.drawXAxisLabel=function(a,b,c){return this.raphael.text(a,b,c).attr("font-size",this.options.gridTextSize).attr("font-family",this.options.gridTextFamily).attr("font-weight",this.options.gridTextWeight).attr("fill",this.options.gridTextColor)},c.prototype.drawGridLine=function(a){return this.raphael.path(a).attr("stroke",this.options.gridLineColor).attr("stroke-width",this.options.gridStrokeWidth)},c.prototype.startRange=function(a){return this.hover.hide(),this.selectFrom=a,this.selectionRect.attr({x:a,width:0}).show()},c.prototype.endRange=function(a){var b,c;return this.selectFrom?(c=Math.min(this.selectFrom,a),b=Math.max(this.selectFrom,a),this.options.rangeSelect.call(this.el,{start:this.data[this.hitTest(c)].x,end:this.data[this.hitTest(b)].x}),this.selectFrom=null):void 0},c.prototype.mousemoveHandler=function(b){var c,d,e,f,g;return d=a.offset(this.el),g=b.pageX-d.left,this.selectFrom?(c=this.data[this.hitTest(Math.min(g,this.selectFrom))]._x,e=this.data[this.hitTest(Math.max(g,this.selectFrom))]._x,f=e-c,this.selectionRect.attr({x:c,width:f})):this.fire("hovermove",g,b.pageY-d.top)},c.prototype.mouseleaveHandler=function(a){return this.selectFrom&&(this.selectionRect.hide(),this.selectFrom=null),this.fire("hoverout")},c.prototype.touchHandler=function(b){var c,d;return d=b.originalEvent.touches[0]||b.originalEvent.changedTouches[0],c=a.offset(this.el),this.fire("hovermove",d.pageX-c.left,d.pageY-c.top)},c.prototype.clickHandler=function(b){var c;return c=a.offset(this.el),this.fire("gridclick",b.pageX-c.left,b.pageY-c.top)},c.prototype.mousedownHandler=function(b){var c;return c=a.offset(this.el),this.startRange(b.pageX-c.left)},c.prototype.mouseupHandler=function(b){var c;return c=a.offset(this.el),this.endRange(b.pageX-c.left),this.fire("hovermove",b.pageX-c.left,b.pageY-c.top)},c.prototype.resizeHandler=function(){return null!=this.timeoutId&&window.clearTimeout(this.timeoutId),this.timeoutId=window.setTimeout(this.debouncedResizeHandler,100)},c.prototype.debouncedResizeHandler=function(){var b,c,d;return this.timeoutId=null,d=a.dimensions(this.el),c=d.width,b=d.height,this.raphael.setSize(c,b),this.options.animate=!1,this.redraw()},c.prototype.hasToShow=function(a){return this.options.shown===!0||this.options.shown[a]===!0},c.prototype.isColorDark=function(a){var b,c,d,e,f;return null!=a?(a=a.substring(1),f=parseInt(a,16),e=f>>16&255,c=f>>8&255,b=f>>0&255,d=.2126*e+.7152*c+.0722*b,d>=128?!1:!0):!1},c.prototype.drawDataLabel=function(a,b,c,d){var e;return e=this.raphael.text(a,b,c).attr("text-anchor","middle").attr("font-size",this.options.dataLabelsSize).attr("font-family",this.options.dataLabelsFamily).attr("font-weight",this.options.dataLabelsWeight).attr("fill",d)},c.prototype.drawDataLabelExt=function(a,b,c,d,e){var f;return f=this.raphael.text(a,b,c).attr("text-anchor",d).attr("font-size",this.options.dataLabelsSize).attr("font-family",this.options.dataLabelsFamily).attr("font-weight",this.options.dataLabelsWeight).attr("fill",e)},c.prototype.setLabels=function(){var a,b,c,d,e,f,g,h;if(this.options.dataLabels){for(g=this.data,h=[],e=0,f=g.length;f>e;e++)c=g[e],h.push(function(){var e,f,g,h;for(g=this.options.ykeys,h=[],b=e=0,f=g.length;f>e;b=++e)d=g[b],a="auto"!==this.options.dataLabelsColor?this.options.dataLabelsColor:this.options.stacked===!0&&this.isColorDark(this.options.barColors[b%this.options.barColors.length])===!0?"#fff":"#000",null!=this.options.lineColors&&null!=this.options.lineType?(null!=c.label_y[b]&&this.drawDataLabel(c._x,c.label_y[b],this.yLabelFormat_noUnit(c.y[b],0),a),null!=c._y2&&null!=c._y2[b]?h.push(this.drawDataLabel(c._x,c._y2[b]-10,this.yLabelFormat_noUnit(c.y[b],1e3),a)):h.push(void 0)):null!=c.label_y[b]?this.options.horizontal===!1?h.push(this.drawDataLabel(c.label_x[b],c.label_y[b],this.yLabelFormat_noUnit(c.y[b],b),a)):h.push(this.drawDataLabelExt(c.label_x[b],c.label_y[b],this.yLabelFormat_noUnit(c.y[b]),"start",a)):null!=c._y2[b]?this.options.horizontal===!1?h.push(this.drawDataLabel(c._x,c._y2[b]-10,this.yLabelFormat_noUnit(c.y[b],b),a)):h.push(this.drawDataLabelExt(c._y2[b],c._x-10,this.yLabelFormat_noUnit(c.y[b]),"middle",a)):h.push(void 0);return h}.call(this));return h}},c}(a.EventEmitter),a.parseDate=function(a){var b,c,d,e,f,g,h,i,j,k,l;return"number"==typeof a?a:(c=a.match(/^(\d+) Q(\d)$/),e=a.match(/^(\d+)-(\d+)$/),f=a.match(/^(\d+)-(\d+)-(\d+)$/),h=a.match(/^(\d+) W(\d+)$/),i=a.match(/^(\d+)-(\d+)-(\d+)[ T](\d+):(\d+)(Z|([+-])(\d\d):?(\d\d))?$/),j=a.match(/^(\d+)-(\d+)-(\d+)[ T](\d+):(\d+):(\d+(\.\d+)?)(Z|([+-])(\d\d):?(\d\d))?$/),c?new Date(parseInt(c[1],10),3*parseInt(c[2],10)-1,1).getTime():e?new Date(parseInt(e[1],10),parseInt(e[2],10)-1,1).getTime():f?new Date(parseInt(f[1],10),parseInt(f[2],10)-1,parseInt(f[3],10)).getTime():h?(k=new Date(parseInt(h[1],10),0,1),4!==k.getDay()&&k.setMonth(0,1+(4-k.getDay()+7)%7),k.getTime()+6048e5*parseInt(h[2],10)):i?i[6]?(g=0,"Z"!==i[6]&&(g=60*parseInt(i[8],10)+parseInt(i[9],10),"+"===i[7]&&(g=0-g)),Date.UTC(parseInt(i[1],10),parseInt(i[2],10)-1,parseInt(i[3],10),parseInt(i[4],10),parseInt(i[5],10)+g)):new Date(parseInt(i[1],10),parseInt(i[2],10)-1,parseInt(i[3],10),parseInt(i[4],10),parseInt(i[5],10)).getTime():j?(l=parseFloat(j[6]),b=Math.floor(l),d=Math.round(1e3*(l-b)),j[8]?(g=0,"Z"!==j[8]&&(g=60*parseInt(j[10],10)+parseInt(j[11],10),"+"===j[9]&&(g=0-g)),Date.UTC(parseInt(j[1],10),parseInt(j[2],10)-1,parseInt(j[3],10),parseInt(j[4],10),parseInt(j[5],10)+g,b,d)):new Date(parseInt(j[1],10),parseInt(j[2],10)-1,parseInt(j[3],10),parseInt(j[4],10),parseInt(j[5],10),b,d).getTime()):new Date(parseInt(a,10),0,1).getTime())},a.Hover=function(){function b(b){null==b&&(b={}),this.options=a.extend({},a.Hover.defaults,b),this.el=document.createElement("div"),this.el.className=this.options["class"],this.el.style.display="none",(this.options.parent=this.options.parent[0]||this.options.parent).appendChild(this.el)}return b.defaults={"class":"morris-hover morris-default-style"},b.prototype.update=function(a,b,c,d){return a?(this.html(a),this.show(),this.moveTo(b,c,d)):this.hide()},b.prototype.html=function(a){return this.el.innerHTML=a},b.prototype.moveTo=function(b,c,d){var e,f,g,h,i,j,k;return k=a.innerDimensions(this.options.parent),i=k.width,h=k.height,f=this.el.offsetWidth,e=this.el.offsetHeight,g=Math.min(Math.max(0,b-f/2),i-f),null!=c?d===!0?(j=c-e/2,0>j&&(j=0)):(j=c-e-10,0>j&&(j=c+10,j+e>h&&(j=h/2-e/2))):j=h/2-e/2,this.el.style.left=parseInt(g)+"px",this.el.style.top=parseInt(j)+"px"},b.prototype.show=function(){return this.el.style.display=""},b.prototype.hide=function(){return this.el.style.display="none"},b}(),a.Line=function(b){function c(b){return this.hilight=f(this.hilight,this),this.onHoverOut=f(this.onHoverOut,this),this.onHoverMove=f(this.onHoverMove,this),this.onGridClick=f(this.onGridClick,this),this instanceof a.Line?void c.__super__.constructor.call(this,b):new a.Line(b)}return h(c,b),c.prototype.init=function(){return"always"!==this.options.hideHover?(this.hover=new a.Hover({parent:this.el}),this.on("hovermove",this.onHoverMove),this.on("hoverout",this.onHoverOut),this.on("gridclick",this.onGridClick)):void 0},c.prototype.defaults={lineWidth:3,pointSize:4,pointSizeGrow:3,lineColors:["#2f7df6","#53a351","#f6c244","#cb444a","#4aa0b5","#222529"],extraClassLine:"",extraClassCircle:"",pointStrokeWidths:[1],pointStrokeColors:["#ffffff"],pointFillColors:[],pointSuperimposed:!0,hoverOrdered:!1,hoverReversed:!1,smooth:!0,lineType:{},shown:!0,xLabels:"auto",xLabelFormat:null,xLabelMargin:0,verticalGrid:!1,verticalGridHeight:"full",verticalGridStartOffset:0,verticalGridType:"",trendLine:!1,trendLineType:"linear",trendLineWidth:2,trendLineWeight:!1,trendLineColors:["#689bc3","#a2b3bf","#64b764"]},c.prototype.calc=function(){return this.calcPoints(),this.generatePaths()},c.prototype.calcPoints=function(){var a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u;for(q=this.data,j=0,n=q.length;n>j;j++)g=q[j],g._x=this.transX(g.x),g._y=function(){var a,b,c,e;for(c=g.y,e=[],d=a=0,b=c.length;b>a;d=++a)i=c[d],d<this.options.ykeys.length-this.options.nbYkeys2?null!=i?e.push(this.transY(i)):e.push(i):e.push(void 0);return e}.call(this),g._y2=function(){var a,b,c,e;for(c=g.y,e=[],d=a=0,b=c.length;b>a;d=++a)i=c[d],d>=this.options.ykeys.length-this.options.nbYkeys2?null!=i?e.push(this.transY2(i)):e.push(i):e.push(void 0);return e}.call(this),g._ymax=Math.min.apply(Math,[this.bottom].concat(function(){var a,c,d,e;for(d=g._y,e=[],b=a=0,c=d.length;c>a;b=++a)i=d[b],null!=i&&this.hasToShow(b)&&e.push(i);return e}.call(this))),g._ymax2=Math.min.apply(Math,[this.bottom].concat(function(){var a,c,d,e;for(d=g._y2,e=[],b=a=0,c=d.length;c>a;b=++a)i=d[b],null!=i&&this.hasToShow(b)&&e.push(i);return e}.call(this)));for(r=this.data,c=k=0,o=r.length;o>k;c=++k)for(g=r[c],this.data[c].label_x=[],this.data[c].label_y=[],e=l=s=this.options.ykeys.length-1;0>=s?0>=l:l>=0;e=0>=s?++l:--l)null!=g._y[e]&&(this.data[c].label_x[e]=g._x,this.data[c].label_y[e]=g._y[e]-10),null!=g._y2&&null!=g._y2[e]&&(this.data[c].label_x[e]=g._x,this.data[c].label_y[e]=g._y2[e]-10);if(this.options.pointSuperimposed===!1){for(t=this.data,u=[],m=0,p=t.length;p>m;m++)g=t[m],u.push(function(){var d,e,i,j,k,l,m;for(k=g._y,m=[],c=i=0,d=k.length;d>i;c=++i){for(f=k[c],a=0,l=g._y,b=j=0,e=l.length;e>j;b=++j)h=l[b],f===h&&"number"==typeof f&&a++;a>1?(g._y[c]=g._y[c]+a*this.lineWidthForSeries(c),this.lineWidthForSeries(c)>1?m.push(g._y[c]=g._y[c]-1):m.push(void 0)):m.push(void 0)}return m}.call(this));return u}},c.prototype.hitTest=function(a){var b,c,d,e,f;if(0===this.data.length)return null;for(f=this.data.slice(1),b=d=0,e=f.length;e>d&&(c=f[b],!(a<(c._x+this.data[b]._x)/2));b=++d);return b},c.prototype.onGridClick=function(a,b){var c;return c=this.hitTest(a),this.fire("click",c,this.data[c].src,a,b)},c.prototype.onHoverMove=function(a,b){var c;return c=this.hitTest(a),this.displayHoverForRow(c)},c.prototype.onHoverOut=function(){return this.options.hideHover!==!1?this.displayHoverForRow(null):void 0},c.prototype.displayHoverForRow=function(a){var b;return null!=a?((b=this.hover).update.apply(b,this.hoverContentForRow(a)),this.hilight(a)):(this.hover.hide(),this.hilight())},c.prototype.escapeHTML=function(a){var b,c;return b={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;"},c=/[&<>"'/]/gi,a.replace(c,function(a){return b[a]})},c.prototype.hoverContentForRow=function(a){var b,c,d,e,f,g,h,j,k,l,m,n,o,p,q,r,s,t,u;if(j=this.data[a],c="",h=[],this.options.hoverOrdered===!0)for(s=j.y,e=m=0,q=s.length;q>m;e=++m){for(l=s[e],f=null,g=-1,t=j.y,d=n=0,r=t.length;r>n;d=++n)k=t[d],i.call(h,d)<0&&(k>=f||null===f)&&(f=k,g=d);h.push(g)}else for(u=j.y,e=o=u.length-1;o>=0;e=o+=-1)l=u[e],h.push(e);for(this.options.hoverReversed===!0&&(h=h.reverse()),b=-1,p=h.length-1;p>=0;p+=-1)d=h[p],this.options.labels[d]!==!1&&(void 0!==j.y[d]&&-1===b&&(b=d),c="<div class='morris-hover-point' style='color: "+this.colorFor(j,d,"label")+"'>\n  "+this.options.labels[d]+":\n  "+this.yLabelFormat(j.y[d],d)+"\n</div>"+c);return c="<div class='morris-hover-row-label'>"+this.escapeHTML(j.label)+"</div>"+c,"function"==typeof this.options.hoverCallback&&(c=this.options.hoverCallback(a,this.options,c,j.src)),b>this.options.nbYkeys2?[c,j._x,j._ymax2]:[c,j._x,j._ymax]},c.prototype.generatePaths=function(){var b,c,d,e,f,g;return this.paths=function(){var h,j,k,l;for(l=[],c=h=0,j=this.options.ykeys.length;j>=0?j>h:h>j;c=j>=0?++h:--h)g="boolean"==typeof this.options.smooth?this.options.smooth:(k=this.options.ykeys[c],i.call(this.options.smooth,k)>=0),d=g?"smooth":"jagged","string"==typeof this.options.lineType&&(d=this.options.lineType),void 0!==this.options.lineType[this.options.ykeys[c]]&&(d=this.options.lineType[this.options.ykeys[c]]),e=this.options.ykeys.length-this.options.nbYkeys2,b=e>c?function(){var a,b,d,e;for(d=this.data,e=[],a=0,b=d.length;b>a;a++)f=d[a],void 0!==f._y[c]&&e.push({x:f._x,y:f._y[c]});return e}.call(this):function(){var a,b,d,e;for(d=this.data,e=[],a=0,b=d.length;b>a;a++)f=d[a],void 0!==f._y2[c]&&e.push({x:f._x,y:f._y2[c]});return e}.call(this),b.length>1?l.push(a.Line.createPath(b,d,this.bottom,c,this.options.ykeys.length,this.options.lineWidth)):l.push(null);return l}.call(this)},c.prototype.draw=function(){var a;return((a=this.options.axes)===!0||"both"===a||"x"===a)&&this.drawXAxis(),this.drawSeries(),this.options.hideHover===!1?this.displayHoverForRow(this.data.length-1):void 0},c.prototype.drawXAxis=function(){var b,c,d,e,f,g,h,i,j,k,l,m,n,o=this;for(i=this.bottom+this.options.padding/2,g=null,f=null,b=function(b,c){var d,e,h,j,k;return d=o.drawXAxisLabel(o.transX(c),i,b),k=d.getBBox(),d.transform("r"+-o.options.xLabelAngle),e=d.getBBox(),d.transform("t0,"+e.height/2+"..."),0!==o.options.xLabelAngle&&(j=-.5*k.width*Math.cos(o.options.xLabelAngle*Math.PI/180),d.transform("t"+j+",0...")),e=d.getBBox(),(null==g||g>=e.x+e.width||null!=f&&f>=e.x)&&e.x>=0&&e.x+e.width<a.dimensions(o.el).width?(0!==o.options.xLabelAngle&&(h=1.25*o.options.gridTextSize/Math.sin(o.options.xLabelAngle*Math.PI/180),f=e.x-h),g=e.x-o.options.xLabelMargin,o.options.verticalGrid===!0?o.drawVerticalGridLine(c):void 0):d.remove()},d=this.options.parseTime?1===this.data.length&&"auto"===this.options.xLabels?[[this.data[0].label,this.data[0].x]]:a.labelSeries(this.xmin,this.xmax,this.width,this.options.xLabels,this.options.xLabelFormat):this.options.customLabels?function(){var a,b,c,d;for(c=this.options.customLabels,d=[],a=0,b=c.length;b>a;a++)h=c[a],d.push([h.label,h.x]);return d}.call(this):function(){var a,b,c,d;for(c=this.data,d=[],a=0,b=c.length;b>a;a++)h=c[a],d.push([h.label,h.x]);return d}.call(this),d.reverse(),j=0,l=d.length;l>j;j++)c=d[j],b(c[0],c[1]);if("string"==typeof this.options.verticalGrid){for(e=a.labelSeries(this.xmin,this.xmax,this.width,this.options.verticalGrid),n=[],k=0,m=e.length;m>k;k++)c=e[k],n.push(this.drawVerticalGridLine(c[1]));return n}},c.prototype.drawVerticalGridLine=function(a){var b,c;return a=Math.floor(this.transX(a))+.5,c=this.yStart+this.options.verticalGridStartOffset,b="full"===this.options.verticalGridHeight?this.yEnd:this.yStart-this.options.verticalGridHeight,this.drawGridLineVert("M"+a+","+c+"V"+b)},c.prototype.drawGridLineVert=function(a){return this.raphael.path(a).attr("stroke",this.options.gridLineColor).attr("stroke-width",this.options.gridStrokeWidth).attr("stroke-dasharray",this.options.verticalGridType);
},c.prototype.drawSeries=function(){var a,b,c,d,e,f;for(this.seriesPoints=[],a=b=d=this.options.ykeys.length-1;0>=d?0>=b:b>=0;a=0>=d?++b:--b)this.hasToShow(a)&&((this.options.trendLine!==!1&&this.options.trendLine===!0||this.options.trendLine[a]===!0)&&this.data.length>0&&this._drawTrendLine(a),this._drawLineFor(a));for(f=[],a=c=e=this.options.ykeys.length-1;0>=e?0>=c:c>=0;a=0>=e?++c:--c)this.hasToShow(a)?f.push(this._drawPointFor(a)):f.push(void 0);return f},c.prototype._drawPointFor=function(a){var b,c,d,e,f,g,h;for(this.seriesPoints[a]=[],g=this.data,h=[],c=e=0,f=g.length;f>e;c=++e)d=g[c],b=null,null!=d._y[a]&&(b=this.drawLinePoint(d._x,d._y[a],this.colorFor(d,a,"point"),a)),null!=d._y2&&null!=d._y2[a]&&(b=this.drawLinePoint(d._x,d._y2[a],this.colorFor(d,a,"point"),a)),h.push(this.seriesPoints[a].push(b));return h},c.prototype._drawLineFor=function(a){var b;return b=this.paths[a],null!==b?this.drawLinePath(b,this.colorFor(null,a,"line"),a):void 0},c.prototype._drawTrendLine=function(b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A;for(k=0,n=0,l=0,m=0,f=0,i=[],A=this.data,g=v=0,z=A.length;z>v;g=++v)r=A[g],t=r.x,u=r.y[b],null!=u&&(i.push([t,u]),s=this.options.trendLineWeight===!1?1:this.options.data[g][this.options.trendLineWeight],f+=s,k+=t*s,n+=u*s,l+=t*t*s,m+=t*u*s);if(c=(f*m-k*n)/(f*l-k*k),d=n/f-c*k/f,e=[{},{}],e[0].x=this.transX(this.data[0].x),e[0].y=this.transY(this.data[0].x*c+d),e[1].x=this.transX(this.data[this.data.length-1].x),e[1].y=this.transY(this.data[this.data.length-1].x*c+d),"linear"!==this.options.trendLineType)if("function"==typeof regression){if(o=(this.xmax-this.xmin)/30,e=[],"polynomial"===this.options.trendLineType)for(j=regression("polynomial",i,2),g=w=0;30>=w;g=++w)p=this.xmin+g*o,q=j.equation[2]*p*p+j.equation[1]*p+j.equation[0],e.push({x:this.transX(p),y:this.transY(q)});else if("logarithmic"===this.options.trendLineType)for(j=regression("logarithmic",i),g=x=0;30>=x;g=++x)p=this.xmin+g*o,q=j.equation[0]+j.equation[1]*Math.log(p),e.push({x:this.transX(p),y:this.transY(q)});else if("exponential"===this.options.trendLineType)for(j=regression("exponential",i),g=y=0;30>=y;g=++y)p=this.xmin+g*o,q=j.equation[0]+Math.exp(j.equation[1]*p),e.push({x:this.transX(p),y:this.transY(q)});console.log("Regression formula is: "+j.string+", r2:"+j.r2)}else console.log("Warning: regression() is undefined, please ensure that regression.js is loaded");return isNaN(c)?void 0:(h=a.Line.createPath(e,"jagged",this.bottom),h=this.raphael.path(h).attr("stroke",this.colorFor(null,b,"trendLine")).attr("stroke-width",this.options.trendLineWidth))},c.createPath=function(b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u;for(n="","smooth"===c&&(j=a.Line.gradients(b)),o={y:null},k=t=0,u=b.length;u>t;k=++t)h=b[k],null!=h.y&&(null!=o.y?"smooth"===c?(i=j[k],m=j[k-1],l=(h.x-o.x)/4,p=o.x+l,r=Math.min(d,o.y+l*m),q=h.x-l,s=Math.min(d,h.y-l*i),n+="C"+p+","+r+","+q+","+s+","+h.x+","+h.y):"jagged"===c?n+="L"+h.x+","+h.y:"step"===c?(n+="L"+h.x+","+o.y,n+="L"+h.x+","+h.y):"stepNoRiser"===c?(n+="L"+h.x+","+o.y,n+="M"+h.x+","+h.y):"vertical"===c&&(n+="L"+(o.x-(f-1)*(g/f)+e*g)+","+o.y,n+="L"+(o.x-(f-1)*(g/f)+e*g)+","+d,n+="M"+(h.x-(f-1)*(g/f)+e*g)+","+d,b.length===k+1&&(n+="L"+(h.x-(f-1)*(g/f)+e*g)+","+h.y,n+="L"+(h.x-(f-1)*(g/f)+e*g)+","+d)):("smooth"!==c||null!=j[k])&&(n+="M"+h.x+","+h.y)),o=h;return n},c.gradients=function(a){var b,c,d,e,f,g,h,i;for(c=function(a,b){return(a.y-b.y)/(a.x-b.x)},i=[],d=g=0,h=a.length;h>g;d=++g)b=a[d],null!=b.y?(e=a[d+1]||{y:null},f=a[d-1]||{y:null},null!=f.y&&null!=e.y?i.push(c(f,e)):null!=f.y?i.push(c(f,b)):null!=e.y?i.push(c(b,e)):i.push(null)):i.push(null);return i},c.prototype.hilight=function(a){var b,c,d,e,f;if(null!==this.prevHilight&&this.prevHilight!==a)for(b=c=0,e=this.seriesPoints.length-1;e>=0?e>=c:c>=e;b=e>=0?++c:--c)this.hasToShow(b)&&this.seriesPoints[b][this.prevHilight]&&this.seriesPoints[b][this.prevHilight].animate(this.pointShrinkSeries(b));if(null!==a&&this.prevHilight!==a)for(b=d=0,f=this.seriesPoints.length-1;f>=0?f>=d:d>=f;b=f>=0?++d:--d)this.hasToShow(b)&&this.seriesPoints[b][a]&&this.seriesPoints[b][a].animate(this.pointGrowSeries(b));return this.prevHilight=a},c.prototype.colorFor=function(a,b,c){return"function"==typeof this.options.lineColors?this.options.lineColors.call(this,a,b,c):"point"===c?this.options.pointFillColors[b%this.options.pointFillColors.length]||this.options.lineColors[b%this.options.lineColors.length]:"trendLine"===c?this.options.trendLineColors[b%this.options.trendLineColors.length]:this.options.lineColors[b%this.options.lineColors.length]},c.prototype.drawLinePath=function(a,b,c){var d,e,f,g,h,i,j,k;if(this.options.animate){for(h="",k=this.data,d=i=0,j=k.length;j>i;d=++i)f=k[d],""===h?c>=this.options.ykeys.length-this.options.nbYkeys2?null!=f._y2[c]&&(h="M"+f._x+","+this.transY2(this.ymin2)):null!=f._y[c]&&(h="vertical"!==this.options.lineType?"M"+f._x+","+this.transY(this.ymin):"M"+f._x+","+this.transY(0)+"L"+f._x+","+this.transY(0)+"L"+f._x+","+this.transY(0)):c>=this.options.ykeys.length-this.options.nbYkeys2?null!=f._y2[c]&&(h+=","+f._x+","+this.transY2(this.ymin2),"step"===this.options.lineType&&(h+=","+f._x+","+this.transY2(this.ymin2))):null!=f._y[c]&&("vertical"!==this.options.lineType?h+=","+f._x+","+this.transY(this.ymin):(g=f._x-(this.options.ykeys.length-1)*(this.options.lineWidth/this.options.ykeys.length)+c*this.options.lineWidth,h+="M"+g+","+this.transY(0)+"L"+g+","+this.transY(0)+"L"+g+","+this.transY(0)),"step"===this.options.lineType&&(h+=","+f._x+","+this.transY(this.ymin)));return e=this.raphael.path(h).attr("stroke",b).attr("stroke-width",this.lineWidthForSeries(c)).attr("class",this.options.extraClassLine).attr("class","line_"+c),this.options.cumulative?function(a,b){return a.animate({path:b},600,"<>")}(e,a):function(a,b){return a.animate({path:b},500,"<>")}(e,a)}return this.raphael.path(a).attr("stroke",b).attr("stroke-width",this.lineWidthForSeries(c)).attr("class",this.options.extraClassLine).attr("class","line_"+c)},c.prototype.drawLinePoint=function(a,b,c,d){return this.raphael.circle(a,b,this.pointSizeForSeries(d)).attr("fill",c).attr("stroke-width",this.pointStrokeWidthForSeries(d)).attr("stroke",this.pointStrokeColorForSeries(d)).attr("class",this.options.extraClassCircle).attr("class","circle_line_"+d)},c.prototype.pointStrokeWidthForSeries=function(a){return this.options.pointStrokeWidths[a%this.options.pointStrokeWidths.length]},c.prototype.pointStrokeColorForSeries=function(a){return this.options.pointStrokeColors[a%this.options.pointStrokeColors.length]},c.prototype.lineWidthForSeries=function(a){return this.options.lineWidth instanceof Array?this.options.lineWidth[a%this.options.lineWidth.length]:this.options.lineWidth},c.prototype.pointSizeForSeries=function(a){return this.options.pointSize instanceof Array?this.options.pointSize[a%this.options.pointSize.length]:this.options.pointSize},c.prototype.pointGrowSeries=function(a){return 0!==this.pointSizeForSeries(a)?Raphael.animation({r:this.pointSizeForSeries(a)+this.options.pointSizeGrow},25,"linear"):void 0},c.prototype.pointShrinkSeries=function(a){return Raphael.animation({r:this.pointSizeForSeries(a)},25,"linear")},c}(a.Grid),a.labelSeries=function(b,c,d,e,f){var g,h,i,j,k,l,m,n,o,p,q;if(i=200*(c-b)/d,h=new Date(b),m=a.LABEL_SPECS[e],void 0===m)for(q=a.AUTO_LABEL_ORDER,o=0,p=q.length;p>o;o++)if(j=q[o],l=a.LABEL_SPECS[j],i>=l.span){m=l;break}for(void 0===m&&(m=a.LABEL_SPECS.second),f&&(m=a.extend({},m,{fmt:f})),g=m.start(h),k=[];(n=g.getTime())<=c;)n>=b&&k.push([m.fmt(g),n]),m.incr(g);return k},c=function(b){return{span:60*b*1e3,start:function(a){return new Date(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours())},fmt:function(b){return""+a.pad2(b.getHours())+":"+a.pad2(b.getMinutes())},incr:function(a){return a.setUTCMinutes(a.getUTCMinutes()+b)}}},d=function(b){return{span:1e3*b,start:function(a){return new Date(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours(),a.getMinutes())},fmt:function(b){return""+a.pad2(b.getHours())+":"+a.pad2(b.getMinutes())+":"+a.pad2(b.getSeconds())},incr:function(a){return a.setUTCSeconds(a.getUTCSeconds()+b)}}},a.LABEL_SPECS={decade:{span:1728e8,start:function(a){return new Date(a.getFullYear()-a.getFullYear()%10,0,1)},fmt:function(a){return""+a.getFullYear()},incr:function(a){return a.setFullYear(a.getFullYear()+10)}},year:{span:1728e7,start:function(a){return new Date(a.getFullYear(),0,1)},fmt:function(a){return""+a.getFullYear()},incr:function(a){return a.setFullYear(a.getFullYear()+1)}},month:{span:24192e5,start:function(a){return new Date(a.getFullYear(),a.getMonth(),1)},fmt:function(b){return""+b.getFullYear()+"-"+a.pad2(b.getMonth()+1)},incr:function(a){return a.setMonth(a.getMonth()+1)}},week:{span:6048e5,start:function(a){return new Date(a.getFullYear(),a.getMonth(),a.getDate())},fmt:function(b){return""+b.getFullYear()+"-"+a.pad2(b.getMonth()+1)+"-"+a.pad2(b.getDate())},incr:function(a){return a.setDate(a.getDate()+7)}},day:{span:864e5,start:function(a){return new Date(a.getFullYear(),a.getMonth(),a.getDate())},fmt:function(b){return""+b.getFullYear()+"-"+a.pad2(b.getMonth()+1)+"-"+a.pad2(b.getDate())},incr:function(a){return a.setDate(a.getDate()+1)}},hour:c(60),"30min":c(30),"15min":c(15),"10min":c(10),"5min":c(5),minute:c(1),"30sec":d(30),"15sec":d(15),"10sec":d(10),"5sec":d(5),second:d(1)},a.AUTO_LABEL_ORDER=["decade","year","month","week","day","hour","30min","15min","10min","5min","minute","30sec","15sec","10sec","5sec","second"],a.Area=function(b){function c(b){var e;return this instanceof a.Area?(e=a.extend({},d,b),this.cumulative=!e.behaveLikeLine,"auto"===e.fillOpacity&&(e.fillOpacity=e.behaveLikeLine?.8:1),void c.__super__.constructor.call(this,e)):new a.Area(b)}var d;return h(c,b),d={fillOpacity:"auto",behaveLikeLine:!1,belowArea:!0,areaColors:[]},c.prototype.calcPoints=function(){var a,b,c,d,e,f,g,h,i,j,k,l,m;for(k=this.data,g=0,i=k.length;i>g;g++)d=k[g],d._x=this.transX(d.x),e=0,d._y=function(){var a,b,c,g;for(c=d.y,g=[],a=0,b=c.length;b>a;a++)f=c[a],this.options.behaveLikeLine?null!=f?g.push(this.transY(f)):g.push(f):null!=f?(e+=f||0,g.push(this.transY(e))):g.push(void 0);return g}.call(this),d._ymax=Math.max.apply(Math,[].concat(function(){var b,c,e,g;for(e=d._y,g=[],a=b=0,c=e.length;c>b;a=++b)f=e[a],null!=f&&g.push(f);return g}()));for(l=this.data,m=[],b=h=0,j=l.length;j>h;b=++h)d=l[b],this.data[b].label_x=[],this.data[b].label_y=[],m.push(function(){var a,e,f;for(f=[],c=a=e=this.options.ykeys.length-1;0>=e?0>=a:a>=0;c=0>=e?++a:--a)null!=d._y[c]&&(this.data[b].label_x[c]=d._x,this.data[b].label_y[c]=d._y[c]-10),null!=d._y2&&null!=d._y2[c]?(this.data[b].label_x[c]=d._x,f.push(this.data[b].label_y[c]=d._y2[c]-10)):f.push(void 0);return f}.call(this));return m},c.prototype.drawSeries=function(){var a,b,c,d,e,f,g,h,i,j;for(this.seriesPoints=[],b=this.options.behaveLikeLine?function(){h=[];for(var a=0,b=this.options.ykeys.length-1;b>=0?b>=a:a>=b;b>=0?a++:a--)h.push(a);return h}.apply(this):function(){i=[];for(var a=g=this.options.ykeys.length-1;0>=g?0>=a:a>=0;0>=g?a++:a--)i.push(a);return i}.apply(this),c=0,e=b.length;e>c;c++)a=b[c],this._drawFillFor(a);for(j=[],d=0,f=b.length;f>d;d++)a=b[d],this._drawLineFor(a),j.push(this._drawPointFor(a));return j},c.prototype._drawFillFor=function(b){var c,d,e,f;return d=this.paths[b],null!==d?this.options.belowArea===!0?(d+="L"+this.transX(this.xmax)+","+this.bottom+"L"+this.transX(this.xmin)+","+this.bottom+"Z",this.drawFilledPath(d,this.fillForSeries(b),b)):(c=function(){var a,b,c;for(b=this.data,c=[],a=b.length-1;a>=0;a+=-1)f=b[a],void 0!==f._y[0]&&c.push({x:f._x,y:f._y[0]});return c}.call(this),e=a.Line.createPath(c,"smooth",this.bottom),d=d+"L"+e.slice(1),this.drawFilledPath(d,this.fillForSeries(b),b)):void 0},c.prototype.fillForSeries=function(a){var b;return 0===this.options.areaColors.length&&(this.options.areaColors=this.options.lineColors),b=Raphael.rgb2hsl(this.options.areaColors[a%this.options.areaColors.length]),Raphael.hsl(b.h,this.options.behaveLikeLine?.9*b.s:.75*b.s,Math.min(.98,this.options.behaveLikeLine?1.2*b.l:1.25*b.l))},c.prototype.drawFilledPath=function(b,c,d){var e,f,g,h,i;return this.options.animate?(e=function(){var a,b,c,e;for(c=this.data,e=[],a=0,b=c.length;b>a;a++)g=c[a],void 0!==g._y[d]&&e.push({x:g._x,y:this.transY(0)});return e}.call(this),i=a.Line.createPath(e,"smooth",this.bottom),this.options.belowArea===!0?i+="L"+this.transX(this.xmax)+","+this.bottom+"L"+this.transX(this.xmin)+","+this.bottom+"Z":(e=function(){var a,b,c;for(b=this.data,c=[],a=b.length-1;a>=0;a+=-1)g=b[a],void 0!==g._y[d]&&c.push({x:g._x,y:this.transY(0)});return c}.call(this),f=a.Line.createPath(e,"smooth",this.bottom),i=i+"L"+f.slice(1)),i+="Z",h=this.raphael.path(i).attr("fill",c).attr("fill-opacity",this.options.fillOpacity).attr("stroke","none"),function(a,b){return a.animate({path:b},500,"<>")}(h,b)):this.raphael.path(b).attr("fill",c).attr("fill-opacity",this.options.fillOpacity).attr("stroke","none")},c}(a.Line),a.Bar=function(b){function c(b){return this.onHoverOut=f(this.onHoverOut,this),this.onHoverMove=f(this.onHoverMove,this),this.onGridClick=f(this.onGridClick,this),this instanceof a.Bar?void c.__super__.constructor.call(this,a.extend({},b,{parseTime:!1})):new a.Bar(b)}return h(c,b),c.prototype.init=function(){return this.cumulative=this.options.stacked,"always"!==this.options.hideHover?(this.hover=new a.Hover({parent:this.el}),this.on("hovermove",this.onHoverMove),this.on("hoverout",this.onHoverOut),this.on("gridclick",this.onGridClick)):void 0},c.prototype.defaults={barSizeRatio:.75,pointSize:4,lineWidth:3,barGap:3,barColors:["#2f7df6","#53a351","#f6c244","#cb444a","#4aa0b5","#222529"],barOpacity:1,barHighlightOpacity:1,highlightSpeed:150,barRadius:[0,0,0,0],xLabelMargin:0,horizontal:!1,stacked:!1,shown:!0,showZero:!0,inBarValue:!1,inBarValueTextColor:"white",inBarValueMinTopMargin:1,inBarValueRightMargin:4,rightAxisBar:!1},c.prototype.calc=function(){var a;return this.calcBars(),this.options.hideHover===!1?(a=this.hover).update.apply(a,this.hoverContentForRow(this.data.length-1)):void 0},c.prototype.calcBars=function(){var a,b,c,d,e,f,g,h;for(g=this.data,h=[],a=e=0,f=g.length;f>e;a=++e)c=g[a],c._x=this.xStart+this.xSize*(a+.5)/this.data.length,c._y=function(){var a,e,f,g;for(f=c.y,g=[],b=a=0,e=f.length;e>a;b=++a)d=f[b],b<this.options.ykeys.length-this.options.nbYkeys2?null!=d?g.push(this.transY(d)):g.push(null):g.push(void 0);return g}.call(this),h.push(c._y2=function(){var a,e,f,g;for(f=c.y,g=[],b=a=0,e=f.length;e>a;b=++a)d=f[b],b>=this.options.ykeys.length-this.options.nbYkeys2?null!=d?g.push(this.transY2(d)):g.push(null):g.push(void 0);return g}.call(this));return h},c.prototype.draw=function(){var a;return((a=this.options.axes)===!0||"both"===a||"x"===a)&&this.drawXAxis(),this.drawSeries(),this.options.rightAxisBar===!1?(this.drawBarLine(),this.drawBarPoints()):void 0},c.prototype.drawBarLine=function(){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z;for(k=this.options.ykeys.length-this.options.nbYkeys2,y=this.options.ykeys.slice(k,this.options.ykeys.length),z=[],h=u=0,w=y.length;w>u;h=u+=1){for(d=y[h],l="",p="",c=this.options.horizontal===!1?function(){var a,b,c,d;for(c=this.data,d=[],a=0,b=c.length;b>a;a++)n=c[a],void 0!==n._y2[k+h]&&d.push({x:n._x,y:n._y2[k+h]});return d}.call(this):function(){var a,b,c,d;for(c=this.data,d=[],a=0,b=c.length;b>a;a++)n=c[a],void 0!==n._y2[k+h]&&d.push({x:n._y2[k+h],y:n._x});return d}.call(this),this.options.smooth&&(f=a.Line.gradients(c)),m={y:null},g=v=0,x=c.length;x>v;g=++v)b=c[g],null!=b.y&&(null!=m.y?(this.options.smooth&&this.options.horizontal===!1?(e=f[g],j=f[g-1],i=(b.x-m.x)/4,q=m.x+i,s=Math.min(this.bottom,m.y+i*j),r=b.x-i,t=Math.min(this.bottom,b.y-i*e),l+="C"+q+","+s+","+r+","+t+","+b.x+","+b.y):l+="L"+b.x+","+b.y,p+=this.options.horizontal===!0?"L"+this.transY(0)+","+b.y:"L"+b.x+","+this.transY(0)):this.options.smooth&&null==f[g]||(l+="M"+b.x+","+b.y,p+=this.options.horizontal===!0?"M"+this.transY(0)+","+b.y:"M"+b.x+","+this.transY(0))),m=b;""!==l?this.options.animate?(o=this.raphael.path(p).attr("stroke",this.colorFor(b,k+h,"bar")).attr("stroke-width",this.lineWidthForSeries(h)),z.push(function(a,b){return a.animate({path:b},500,"<>")}(o,l))):z.push(o=this.raphael.path(l).attr("stroke",this.colorFor(b,k+h,"bar")).attr("stroke-width",this.lineWidthForSeries(h))):z.push(void 0)}return z},c.prototype.drawBarPoints=function(){var a,b,c,d,e,f,g,h,i,j;for(e=this.options.ykeys.length-this.options.nbYkeys2,this.seriesPoints=[],i=this.options.ykeys.slice(e,this.options.ykeys.length),j=[],d=g=0,h=i.length;h>g;d=g+=1)b=i[d],this.seriesPoints[d]=[],j.push(function(){var b,g,h,i;for(h=this.data,i=[],c=b=0,g=h.length;g>b;c=++b)f=h[c],a=null,null!=f._y2[e+d]?this.options.horizontal===!1?(a=this.raphael.circle(f._x,f._y2[e+d],this.pointSizeForSeries(d)).attr("fill",this.colorFor(f,e+d,"bar")).attr("stroke-width",1).attr("stroke","#ffffff"),i.push(this.seriesPoints[d].push(a))):(a=this.raphael.circle(f._y2[e+d],f._x,this.pointSizeForSeries(d)).attr("fill",this.colorFor(f,e+d,"bar")).attr("stroke-width",1).attr("stroke","#ffffff"),i.push(this.seriesPoints[d].push(a))):i.push(void 0);return i}.call(this));return j},c.prototype.lineWidthForSeries=function(a){return this.options.lineWidth instanceof Array?this.options.lineWidth[a%this.options.lineWidth.length]:this.options.lineWidth},c.prototype.pointSizeForSeries=function(a){return this.options.pointSize instanceof Array?this.options.pointSize[a%this.options.pointSize.length]:this.options.pointSize},c.prototype.drawXAxis=function(){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u;for(c=this.options.horizontal?this.getYAxisLabelX():this.getXAxisLabelY(),l=null,k=null,u=[],e=r=0,s=this.data.length;s>=0?s>r:r>s;e=s>=0?++r:--r)m=this.data[this.data.length-1-e],f=this.options.horizontal?this.drawYAxisLabel(c,m._x-.5*this.options.gridTextSize,m.label,1):this.drawXAxisLabel(m._x,c,m.label),b=this.options.horizontal?0:this.options.xLabelAngle,p=f.getBBox(),f.transform("r"+-b),g=f.getBBox(),f.transform("t0,"+g.height/2+"..."),0!==b&&(j=-.5*p.width*Math.cos(b*Math.PI/180),f.transform("t"+j+",0...")),t=a.dimensions(this.el),q=t.width,d=t.height,this.options.horizontal?(o=g.y,n=g.height,i=d):(o=g.x,n=g.width,i=q),(null==l||l>=o+n||null!=k&&k>=o)&&o>=0&&i>o+n?(0!==b&&(h=1.25*this.options.gridTextSize/Math.sin(b*Math.PI/180),k=o-h),this.options.horizontal?u.push(l=o):u.push(l=o-this.options.xLabelMargin)):u.push(f.remove());return u},c.prototype.getXAxisLabelY=function(){return this.bottom+(this.options.xAxisLabelTopPadding||this.options.padding/2)},c.prototype.drawSeries=function(){var a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v;if(this.seriesBars=[],e=this.xSize/this.options.data.length,this.options.stacked)m=1;else for(m=0,f=u=0,v=this.options.ykeys.length-1;v>=0?v>=u:u>=v;f=v>=0?++u:--u)this.hasToShow(f)&&(m+=1);return this.options.stacked===!1&&this.options.rightAxisBar===!1&&(m-=this.options.nbYkeys2),b=(e*this.options.barSizeRatio-this.options.barGap*(m-1))/m,this.options.barSize&&(b=Math.min(b,this.options.barSize)),q=e-b*m-this.options.barGap*(m-1),k=q/2,t=this.ymin<=0&&this.ymax>=0?this.transY(0):null,this.bars=function(){var f,m,q,u;for(q=this.data,u=[],g=f=0,m=q.length;m>f;g=++f)n=q[g],this.data[g].label_x=[],this.data[g].label_y=[],this.seriesBars[g]=[],i=null,h=null,l=this.options.rightAxisBar===!0?n._y.length:n._y.length-this.options.nbYkeys2,u.push(function(){var f,m,q,u;for(q=n._y.slice(0,l),u=[],o=f=0,m=q.length;m>f;o=++f)s=q[o],null!=n._y[o]?s=n._y[o]:null!=n._y2[o]&&(s=n._y2[o]),this.hasToShow(o)&&(null!==s?(t?(r=Math.min(s,t),c=Math.max(s,t)):(r=s,c=this.bottom),j=this.xStart+g*e+k,this.options.stacked||(j+=o*(b+this.options.barGap)),p=c-r,this.options.verticalGridCondition&&this.options.verticalGridCondition(n.x)&&(this.options.horizontal?this.drawBar(this.yStart,this.xStart+g*e,this.ySize,e,this.options.verticalGridColor,this.options.verticalGridOpacity,this.options.barRadius):this.drawBar(this.xStart+g*e,this.yEnd,e,this.ySize,this.options.verticalGridColor,this.options.verticalGridOpacity,this.options.barRadius)),this.options.horizontal?(h=c,this.options.stacked&&null!=i&&(r=i),i=r+p,0===p&&(p=1),this.seriesBars[g][o]=this.drawBar(r,j,p,b,this.colorFor(n,o,"bar"),this.options.barOpacity,this.options.barRadius),this.options.dataLabels&&(this.options.stacked||"inside"===this.options.dataLabelsPosition?(this.data[g].label_x[o]=r+p/2,this.data[g].label_y[o]=j+b/2):(this.data[g].label_x[o]=r+p+5,this.data[g].label_y[o]=j+b/2)),this.options.inBarValue&&b>this.options.gridTextSize+2*this.options.inBarValueMinTopMargin?(a=j+.5*b,u.push(this.raphael.text(c-this.options.inBarValueRightMargin,a,this.yLabelFormat(n.y[o],o)).attr("font-size",this.options.gridTextSize).attr("font-family",this.options.gridTextFamily).attr("font-weight",this.options.gridTextWeight).attr("fill",this.options.inBarValueTextColor).attr("text-anchor","end"))):u.push(void 0)):(this.options.stacked&&null!=i&&(r+=i-c),i=r,0===p&&this.options.showZero&&(p=1),this.seriesBars[g][o]=this.drawBar(j,r,b,p,this.colorFor(n,o,"bar"),this.options.barOpacity,this.options.barRadius),this.options.dataLabels?(d="inside"===this.options.dataLabelsPosition||this.options.stacked&&"force_outside"!==this.options.dataLabelsPosition?p/2:-7,p>this.options.dataLabelsSize||!this.options.stacked||"force_outside"===this.options.dataLabelsPosition?(this.data[g].label_x[o]=j+b/2,u.push(this.data[g].label_y[o]=r+d)):u.push(void 0)):u.push(void 0))):u.push(null));return u}.call(this));return u}.call(this)},c.prototype.hilight=function(a){var b,c,d,e,f,g,h,i;if(this.seriesBars&&this.seriesBars[this.prevHilight]&&null!==this.prevHilight&&this.prevHilight!==a)for(h=this.seriesBars[this.prevHilight],b=d=0,f=h.length;f>d;b=++d)c=h[b],c&&c.animate({"fill-opacity":this.options.barOpacity},this.options.highlightSpeed);if(this.seriesBars&&this.seriesBars[a]&&null!==a&&this.prevHilight!==a)for(i=this.seriesBars[a],b=e=0,g=i.length;g>e;b=++e)c=i[b],c&&c.animate({"fill-opacity":this.options.barHighlightOpacity},this.options.highlightSpeed);return this.prevHilight=a},c.prototype.colorFor=function(a,b,c){var d,e;return"function"==typeof this.options.barColors?(d={x:a.x,y:a.y[b],label:a.label,src:a.src},e={index:b,key:this.options.ykeys[b],label:this.options.labels[b]},this.options.barColors.call(this,d,e,c)):this.options.barColors[b%this.options.barColors.length]},c.prototype.hitTest=function(a,b){var c,d;return 0===this.data.length?null:(this.options.horizontal?(c=document.body.getBoundingClientRect(),d=b+c.top):d=a,d=Math.max(Math.min(d,this.xEnd),this.xStart),Math.min(this.data.length-1,Math.floor((d-this.xStart)/(this.xSize/this.data.length))))},c.prototype.onGridClick=function(a,b){var c;return c=this.hitTest(a,b),this.fire("click",c,this.data[c].src,a,b)},c.prototype.onHoverMove=function(a,b){var c,d;return c=this.hitTest(a,b),this.hilight(c),null!=c?(d=this.hover).update.apply(d,this.hoverContentForRow(c)):this.hover.hide()},c.prototype.onHoverOut=function(){return this.hilight(-1),this.options.hideHover!==!1?this.hover.hide():void 0},c.prototype.escapeHTML=function(a){var b,c;return b={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;"},c=/[&<>"'/]/gi,a.replace(c,function(a){return b[a]})},c.prototype.hoverContentForRow=function(a){var b,c,d,e,f,g,h,i,j,k,l,m;for(f=this.data[a],b="<div class='morris-hover-row-label'>"+this.escapeHTML(f.label)+"</div>",c=[],m=f.y,e=i=0,k=m.length;k>i;e=++i)h=m[e],c.unshift(h);for(e=j=0,l=c.length;l>j;e=++j)h=c[e],d=f.y.length-1-e,this.options.labels[d]!==!1&&(b+="<div class='morris-hover-point' style='color: "+this.colorFor(f,d,"label")+"'>\n  "+this.options.labels[d]+":\n  "+this.yLabelFormat(h,d)+"\n</div>");return"function"==typeof this.options.hoverCallback&&(b=this.options.hoverCallback(a,this.options,b,f.src)),this.options.horizontal?(g=this.left+.5*this.width,h=this.top+(a+.5)*this.height/this.data.length,[b,g,h,!0]):(g=this.left+(a+.5)*this.width/this.data.length,[b,g])},c.prototype.drawBar=function(a,b,c,d,e,f,g){var h,i;return h=Math.max.apply(Math,g),i=this.options.animate?this.options.horizontal?0===h||h>d?this.raphael.rect(this.transY(0),b,0,d).animate({x:a,width:c},500):this.raphael.path(this.roundedRect(this.transY(0),b+d,c,0,g).animate({y:b,height:d},500)):0===h||h>d?this.raphael.rect(a,this.transY(0),c,0).animate({y:b,height:d},500):this.raphael.path(this.roundedRect(a,this.transY(0),c,0,g).animate({y:b,height:d},500)):0===h||h>d?this.raphael.rect(a,b,c,d):this.raphael.path(this.roundedRect(a,b,c,d,g)),i.attr("fill",e).attr("fill-opacity",f).attr("stroke","none")},c.prototype.roundedRect=function(a,b,c,d,e){return null==e&&(e=[0,0,0,0]),["M",a,e[0]+b,"Q",a,b,a+e[0],b,"L",a+c-e[1],b,"Q",a+c,b,a+c,b+e[1],"L",a+c,b+d-e[2],"Q",a+c,b+d,a+c-e[2],b+d,"L",a+e[3],b+d,"Q",a,b+d,a,b+d-e[3],"Z"]},c}(a.Grid),a.Donut=function(b){function c(b){this.debouncedResizeHandler=f(this.debouncedResizeHandler,this),this.resizeHandler=f(this.resizeHandler,this),this.deselect=f(this.deselect,this),this.select=f(this.select,this),this.click=f(this.click,this);var c,d,e,g,h;if(!(this instanceof a.Donut))return new a.Donut(b);if(this.options=a.extend({},this.defaults,b),"string"==typeof b.element?this.el=document.getElementById(b.element):this.el=b.element[0]||b.element,null===this.el)throw new Error("Graph placeholder not found.");return this.raphael=new Raphael(this.el),void 0===b.data||0===b.data.length?(h=a.dimensions(this.el),g=h.width,e=h.height,c=g/2,d=e/2,void this.raphael.text(c,d,this.options.noDataLabel).attr("text-anchor","middle").attr("font-size",this.options.noDataLabelSize).attr("font-family",this.options.dataLabelsFamily).attr("font-weight",this.options.noDataLabelWeight).attr("fill",this.options.dataLabelsColor)):(this.options.resize&&a.on(window,"resize",this.resizeHandler),void this.setData(b.data))}return h(c,b),c.prototype.defaults={colors:["#2f7df6","#53a351","#f6c244","#cb444a","#4aa0b5","#222529","#44a1f8","#81d453","#f0bb40","#eb3f25","#b45184","#5f5f5f"],backgroundColor:"#FFFFFF",labelColor:"#000000",padding:0,formatter:a.commas,resize:!0,dataLabels:!1,dataLabelsPosition:"inside",dataLabelsFamily:"sans-serif",dataLabelsSize:12,dataLabelsWeight:"normal",dataLabelsColor:"auto",noDataLabel:"No data for this chart",noDataLabelSize:21,noDataLabelWeight:"bold",donutType:"donut",animate:!0,showPercentage:!1,postUnits:"",preUnits:""},c.prototype.destroy=function(){return this.options.resize?(window.clearTimeout(this.timeoutId),a.off(window,"resize",this.resizeHandler)):void 0},c.prototype.redraw=function(){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H;for(this.raphael.clear(),D=a.dimensions(this.el),w=D.width,h=D.height,d=w/2,e=h/2,v=(Math.min(d,e)-10)/3-this.options.padding,t=0,E=this.values,x=0,A=E.length;A>x;x++)u=E[x],t+=u;for(this.options.total=t,o=5/(2*v),b=1.9999*Math.PI-o*this.data.length,m=0,j=0,this.segments=[],0===t&&(t=1),F=this.values,i=y=0,B=F.length;B>y;i=++y)u=F[i],p=m+o+b*(u/t),s=new a.DonutSegment(d,e,2*v,v,m,p,this.data[i].color||this.options.colors[j%this.options.colors.length],this.options.backgroundColor,j,this.raphael,this.options),s.render(),this.segments.push(s),s.on("hover",this.select),s.on("click",this.click),s.on("mouseout",this.deselect),f=parseFloat(s.raphael.height)>parseFloat(h)?2*h-7*this.options.padding:s.raphael.height-7*this.options.padding,void 0===this.options.data[i].ratio&&(this.options.data[i].ratio=1),f*=this.options.data[i].ratio,this.options.dataLabels&&this.values.length>=1&&(r=Math.sin((m+p)/2),q=Math.cos((m+p)/2),"inside"===this.options.dataLabelsPosition?"pie"===this.options.donutType?(k=parseFloat(d)+parseFloat(.3*f*r),l=parseFloat(e)+parseFloat(.3*f*q)):(k=parseFloat(d)+parseFloat(.39*f*r),l=parseFloat(e)+parseFloat(.39*f*q)):(k=parseFloat(d)+parseFloat(.5*(f-9)*r),l=parseFloat(e)+parseFloat(.5*(f-9)*q)),c="auto"!==this.options.dataLabelsColor?this.options.dataLabelsColor:"inside"===this.options.dataLabelsPosition&&this.isColorDark(this.options.colors[i])===!0?"#fff":"#000",this.options.showPercentage?(g=Math.round(parseFloat(u)/parseFloat(t)*100)+"%",this.drawDataLabelExt(k,l,g,c)):this.drawDataLabelExt(k,l,this.options.preUnits+u+this.options.postUnits,c)),m=p,j+=1;if(this.text1=this.drawEmptyDonutLabel(d,e-10,this.options.labelColor,15,800),this.text2=this.drawEmptyDonutLabel(d,e+10,this.options.labelColor,14),n=Math.max.apply(Math,this.values),j=0,"donut"===this.options.donutType){for(G=this.values,H=[],z=0,C=G.length;C>z;z++){if(u=G[z],u===n){this.select(j);break}H.push(j+=1)}return H}},c.prototype.setData=function(a){var b;return this.data=a,this.values=function(){var a,c,d,e;for(d=this.data,e=[],a=0,c=d.length;c>a;a++)b=d[a],e.push(parseFloat(b.value));return e}.call(this),this.redraw()},c.prototype.drawDataLabel=function(a,b,c,d){var e;return e=this.raphael.text(a,b,c).attr("text-anchor","middle").attr("font-size",this.options.dataLabelsSize).attr("font-family",this.options.dataLabelsFamily).attr("font-weight",this.options.dataLabelsWeight).attr("fill",this.options.dataLabelsColor)},c.prototype.drawDataLabelExt=function(a,b,c,d){var e,f;return f=this.values.length>=1?"middle":"inside"===this.options.dataLabelsPosition?"middle":a>this.raphael.width/2?"start":a>.55*this.raphael.width&&a<.45*this.raphael.width?"middle":"end",e=this.raphael.text(a,b,c,d).attr("text-anchor",f).attr("font-size",this.options.dataLabelsSize).attr("font-family",this.options.dataLabelsFamily).attr("font-weight",this.options.dataLabelsWeight).attr("fill",d)},c.prototype.click=function(a){return this.fire("click",a,this.data[a])},c.prototype.select=function(a){var b,c,d,e,f,g,h;for(h=this.segments,f=0,g=h.length;g>f;f++)d=h[f],d.deselect();return e=this.segments[a],e.select(),c=this.data[a],"donut"===this.options.donutType?this.options.showPercentage&&!this.options.dataLabels?(b=Math.round(parseFloat(c.value)/parseFloat(this.options.total)*100)+"%",this.setLabels(c.label,b)):this.setLabels(c.label,this.options.formatter(c.value,c)):void 0},c.prototype.deselect=function(a){var b,c,d,e,f;for(e=this.segments,f=[],c=0,d=e.length;d>c;c++)b=e[c],f.push(b.deselect());return f},c.prototype.isColorDark=function(a){var b,c,d,e,f;return null!=a?(a=a.substring(1),f=parseInt(a,16),e=f>>16&255,c=f>>8&255,b=f>>0&255,d=.2126*e+.7152*c+.0722*b,d>=128?!1:!0):!1},c.prototype.setLabels=function(b,c){var d,e,f,g,h,i,j,k,l,m,n;return n=a.dimensions(this.el),m=n.width,d=n.height,e=2*(Math.min(m/2,d/2)-10)/3,h=1.8*e,g=e/2,f=e/3,this.text1.attr({text:b,transform:""}),i=this.text1.getBBox(),j=Math.min(h/i.width,g/i.height),this.text1.attr({transform:"S"+j+","+j+","+(i.x+i.width/2)+","+(i.y+i.height)}),this.text2.attr({text:c,transform:""}),k=this.text2.getBBox(),l=Math.min(h/k.width,f/k.height),this.text2.attr({transform:"S"+l+","+l+","+(k.x+k.width/2)+","+k.y})},c.prototype.drawEmptyDonutLabel=function(a,b,c,d,e){var f;return f=this.raphael.text(a,b,"").attr("font-size",d).attr("fill",c),null!=e&&f.attr("font-weight",e),f},c.prototype.resizeHandler=function(){return null!=this.timeoutId&&window.clearTimeout(this.timeoutId),this.timeoutId=window.setTimeout(this.debouncedResizeHandler,100)},c.prototype.debouncedResizeHandler=function(){var b,c,d;return this.timeoutId=null,d=a.dimensions(this.el),c=d.width,b=d.height,this.raphael.setSize(c,b),this.options.animate=!1,this.redraw()},c}(a.EventEmitter),a.DonutSegment=function(a){function b(a,b,c,d,e,g,h,i,j,k,l){this.cx=a,this.cy=b,this.inner=c,this.outer=d,this.color=h,this.backgroundColor=i,this.index=j,this.raphael=k,this.options=l,this.deselect=f(this.deselect,this),this.select=f(this.select,this),this.sin_p0=Math.sin(e),this.cos_p0=Math.cos(e),this.sin_p1=Math.sin(g),this.cos_p1=Math.cos(g),this.is_long=g-e>Math.PI?1:0,void 0===this.options.data[this.index].ratio&&(this.options.data[this.index].ratio=1),c=this.inner*this.options.data[this.index].ratio,this.path=this.calcSegment(c+3,c+this.outer-5),
this.selectedPath=this.calcSegment(c+3,c+this.outer),this.hilight=this.calcArc(c)}return h(b,a),b.prototype.calcArcPoints=function(a){return[this.cx+a*this.sin_p0,this.cy+a*this.cos_p0,this.cx+a*this.sin_p1,this.cy+a*this.cos_p1]},b.prototype.calcSegment=function(a,b){var c,d,e,f,g,h,i,j,k,l;return k=this.calcArcPoints(a),c=k[0],e=k[1],d=k[2],f=k[3],l=this.calcArcPoints(b),g=l[0],i=l[1],h=l[2],j=l[3],"pie"===this.options.donutType?"M"+g+","+i+("A"+b+","+b+",0,"+this.is_long+",0,"+h+","+j)+("L"+this.cx+","+this.cy)+"Z":"M"+c+","+e+("A"+a+","+a+",0,"+this.is_long+",0,"+d+","+f)+("L"+h+","+j)+("A"+b+","+b+",0,"+this.is_long+",1,"+g+","+i)+"Z"},b.prototype.calcArc=function(a){var b,c,d,e,f;return f=this.calcArcPoints(a),b=f[0],d=f[1],c=f[2],e=f[3],"M"+b+","+d+("A"+a+","+a+",0,"+this.is_long+",0,"+c+","+e)},b.prototype.render=function(){var a=this;return/NaN/.test(this.hilight)||(this.arc=this.drawDonutArc(this.hilight,this.color)),/NaN/.test(this.path)?void 0:this.seg=this.drawDonutSegment(this.path,this.color,this.backgroundColor,function(){return a.fire("hover",a.index)},function(){return a.fire("click",a.index)},function(){return a.fire("mouseout",a.index)})},b.prototype.drawDonutArc=function(a,b){var c;return this.options.animate?(c=this.raphael.path("M"+this.cx+","+this.cy+"Z").attr({stroke:b,"stroke-width":2,opacity:0}),function(a,b){return a.animate({path:b},500,"<>")}(c,a)):this.raphael.path(a).attr({stroke:b,"stroke-width":2,opacity:0})},b.prototype.drawDonutSegment=function(a,b,c,d,e,f){var g,h,i;return this.options.animate&&"pie"===this.options.donutType?(i=a,i=a.replace("A",","),i=i.replace("M",""),i=i.replace("C",","),i=i.replace("Z",""),h=i.split(","),i="pie"===this.options.donutType?"M"+h[0]+","+h[1]+","+h[h.length-2]+","+h[h.length-1]+","+h[h.length-2]+","+h[h.length-1]+"Z":"M"+h[0]+","+h[1]+","+h[h.length-2]+","+h[h.length-1]+"Z",g=this.raphael.path(i).attr({fill:b,stroke:c,"stroke-width":3}).hover(d).click(e).mouseout(f),function(a,b){return a.animate({path:b},500,"<>")}(g,a)):"pie"===this.options.donutType?this.raphael.path(a).attr({fill:b,stroke:c,"stroke-width":3}).hover(d).click(e).mouseout(f):this.raphael.path(a).attr({fill:b,stroke:c,"stroke-width":3}).hover(d).click(e)},b.prototype.select=function(){return this.selected||null==this.seg?void 0:(this.seg.animate({path:this.selectedPath},150,"<>"),this.arc.animate({opacity:1},150,"<>"),this.selected=!0)},b.prototype.deselect=function(){return this.selected?(this.seg.animate({path:this.path},150,"<>"),this.arc.animate({opacity:0},150,"<>"),this.selected=!1):void 0},b}(a.EventEmitter)}).call(this);