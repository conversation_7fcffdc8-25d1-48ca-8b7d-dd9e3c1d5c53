import{a as ot,b as un}from"./chunk-P2VZOJAX.js";var za=null;var Ro=1,Po=Symbol("SIGNAL");function b(e){let t=za;return za=e,t}var qa={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function yf(e){if(!(jo(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Ro)){if(!e.producerMustRecompute(e)&&!ko(e)){e.dirty=!1,e.lastCleanEpoch=Ro;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Ro}}function Ya(e){return e&&(e.nextProducerIndex=0),b(e)}function Qa(e,t){if(b(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(jo(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Lo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function ko(e){Gn(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(yf(n),r!==n.version))return!0}return!1}function Za(e){if(Gn(e),jo(e))for(let t=0;t<e.producerNode.length;t++)Lo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Lo(e,t){if(vf(e),Gn(e),e.liveConsumerNode.length===1)for(let r=0;r<e.producerNode.length;r++)Lo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Gn(o),o.producerIndexOfThis[r]=t}}function jo(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Gn(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function vf(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Df(){throw new Error}var wf=Df;function Ka(e){wf=e}function g(e){return typeof e=="function"}function Nt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Wn=Nt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function it(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var $=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(g(r))try{r()}catch(i){t=i instanceof Wn?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Ja(i)}catch(s){t=t??[],s instanceof Wn?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Wn(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Ja(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&it(n,t)}remove(t){let{_finalizers:n}=this;n&&it(n,t),t instanceof e&&t._removeParent(this)}};$.EMPTY=(()=>{let e=new $;return e.closed=!0,e})();var Vo=$.EMPTY;function zn(e){return e instanceof $||e&&"closed"in e&&g(e.remove)&&g(e.add)&&g(e.unsubscribe)}function Ja(e){g(e)?e():e.unsubscribe()}var we={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var At={setTimeout(e,t,...n){let{delegate:r}=At;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=At;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function qn(e){At.setTimeout(()=>{let{onUnhandledError:t}=we;if(t)t(e);else throw e})}function st(){}var Xa=Bo("C",void 0,void 0);function eu(e){return Bo("E",void 0,e)}function tu(e){return Bo("N",e,void 0)}function Bo(e,t,n){return{kind:e,value:t,error:n}}var at=null;function Ot(e){if(we.useDeprecatedSynchronousErrorHandling){let t=!at;if(t&&(at={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=at;if(at=null,n)throw r}}else e()}function nu(e){we.useDeprecatedSynchronousErrorHandling&&at&&(at.errorThrown=!0,at.error=e)}var ut=class extends ${constructor(t){super(),this.isStopped=!1,t?(this.destination=t,zn(t)&&t.add(this)):this.destination=Cf}static create(t,n,r){return new Ie(t,n,r)}next(t){this.isStopped?Ho(tu(t),this):this._next(t)}error(t){this.isStopped?Ho(eu(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Ho(Xa,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},If=Function.prototype.bind;function $o(e,t){return If.call(e,t)}var Uo=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Yn(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Yn(r)}else Yn(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Yn(n)}}},Ie=class extends ut{constructor(t,n,r){super();let o;if(g(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&we.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&$o(t.next,i),error:t.error&&$o(t.error,i),complete:t.complete&&$o(t.complete,i)}):o=t}this.destination=new Uo(o)}};function Yn(e){we.useDeprecatedSynchronousErrorHandling?nu(e):qn(e)}function Ef(e){throw e}function Ho(e,t){let{onStoppedNotification:n}=we;n&&At.setTimeout(()=>n(e,t))}var Cf={closed:!0,next:st,error:Ef,complete:st};var Ft=typeof Symbol=="function"&&Symbol.observable||"@@observable";function X(e){return e}function bf(...e){return Go(e)}function Go(e){return e.length===0?X:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var _=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=_f(n)?n:new Ie(n,r,o);return Ot(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=ru(r),new r((o,i)=>{let s=new Ie({next:a=>{try{n(a)}catch(u){i(u),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Ft](){return this}pipe(...n){return Go(n)(this)}toPromise(n){return n=ru(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function ru(e){var t;return(t=e??we.Promise)!==null&&t!==void 0?t:Promise}function Mf(e){return e&&g(e.next)&&g(e.error)&&g(e.complete)}function _f(e){return e&&e instanceof ut||Mf(e)&&zn(e)}function Wo(e){return g(e?.lift)}function w(e){return t=>{if(Wo(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function v(e,t,n,r,o){return new zo(e,t,n,r,o)}var zo=class extends ut{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(u){t.error(u)}}:super._next,this._error=o?function(a){try{o(a)}catch(u){t.error(u)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function qo(){return w((e,t)=>{let n=null;e._refCount++;let r=v(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Yo=class extends _{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Wo(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new $;let n=this.getSubject();t.add(this.source.subscribe(v(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=$.EMPTY)}return t}refCount(){return qo()(this)}};var Rt={schedule(e){let t=requestAnimationFrame,n=cancelAnimationFrame,{delegate:r}=Rt;r&&(t=r.requestAnimationFrame,n=r.cancelAnimationFrame);let o=t(i=>{n=void 0,e(i)});return new $(()=>n?.(o))},requestAnimationFrame(...e){let{delegate:t}=Rt;return(t?.requestAnimationFrame||requestAnimationFrame)(...e)},cancelAnimationFrame(...e){let{delegate:t}=Rt;return(t?.cancelAnimationFrame||cancelAnimationFrame)(...e)},delegate:void 0};var ou=Nt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ge=(()=>{class e extends _{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Qn(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new ou}next(n){Ot(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Ot(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Ot(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Vo:(this.currentObservers=null,i.push(n),new $(()=>{this.currentObservers=null,it(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new _;return n.source=this,n}}return e.create=(t,n)=>new Qn(t,n),e})(),Qn=class extends ge{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Vo}};var cn=class extends ge{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var ln={now(){return(ln.delegate||Date).now()},delegate:void 0};var dn=class extends ge{constructor(t=1/0,n=1/0,r=ln){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let u=1;u<r.length&&r[u]<=s;u+=2)a=u;a&&r.splice(0,a+1)}}};var Zn=class extends ${constructor(t,n){super()}schedule(t,n=0){return this}};var fn={setInterval(e,t,...n){let{delegate:r}=fn;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=fn;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Pt=class extends Zn{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return fn.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&fn.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,it(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var kt=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};kt.now=ln.now;var Lt=class extends kt{constructor(t,n=kt.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var ct=new Lt(Pt),iu=ct;var Kn=class extends Pt{constructor(t,n){super(t,n),this.scheduler=t,this.work=n}requestAsyncId(t,n,r=0){return r!==null&&r>0?super.requestAsyncId(t,n,r):(t.actions.push(this),t._scheduled||(t._scheduled=Rt.requestAnimationFrame(()=>t.flush(void 0))))}recycleAsyncId(t,n,r=0){var o;if(r!=null?r>0:this.delay>0)return super.recycleAsyncId(t,n,r);let{actions:i}=t;n!=null&&((o=i[i.length-1])===null||o===void 0?void 0:o.id)!==n&&(Rt.cancelAnimationFrame(n),t._scheduled=void 0)}};var Jn=class extends Lt{flush(t){this._active=!0;let n=this._scheduled;this._scheduled=void 0;let{actions:r}=this,o;t=t||r.shift();do if(o=t.execute(t.state,t.delay))break;while((t=r[0])&&t.id===n&&r.shift());if(this._active=!1,o){for(;(t=r[0])&&t.id===n&&r.shift();)t.unsubscribe();throw o}}};var xf=new Jn(Kn);var lt=new _(e=>e.complete());function Xn(e){return e&&g(e.schedule)}function Qo(e){return e[e.length-1]}function er(e){return g(Qo(e))?e.pop():void 0}function Ne(e){return Xn(Qo(e))?e.pop():void 0}function su(e,t){return typeof Qo(e)=="number"?e.pop():t}function uu(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{c(r.next(l))}catch(d){s(d)}}function u(l){try{c(r.throw(l))}catch(d){s(d)}}function c(l){l.done?i(l.value):o(l.value).then(a,u)}c((r=r.apply(e,t||[])).next())})}function au(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function dt(e){return this instanceof dt?(this.v=e,this):new dt(e)}function cu(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o={},s("next"),s("throw"),s("return"),o[Symbol.asyncIterator]=function(){return this},o;function s(f){r[f]&&(o[f]=function(p){return new Promise(function(h,y){i.push([f,p,h,y])>1||a(f,p)})})}function a(f,p){try{u(r[f](p))}catch(h){d(i[0][3],h)}}function u(f){f.value instanceof dt?Promise.resolve(f.value.v).then(c,l):d(i[0][2],f)}function c(f){a("next",f)}function l(f){a("throw",f)}function d(f,p){f(p),i.shift(),i.length&&a(i[0][0],i[0][1])}}function lu(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof au=="function"?au(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,u){s=e[i](s),o(a,u,s.done,s.value)})}}function o(i,s,a,u){Promise.resolve(u).then(function(c){i({value:c,done:a})},s)}}var jt=e=>e&&typeof e.length=="number"&&typeof e!="function";function tr(e){return g(e?.then)}function nr(e){return g(e[Ft])}function rr(e){return Symbol.asyncIterator&&g(e?.[Symbol.asyncIterator])}function or(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Tf(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var ir=Tf();function sr(e){return g(e?.[ir])}function ar(e){return cu(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield dt(n.read());if(o)return yield dt(void 0);yield yield dt(r)}}finally{n.releaseLock()}})}function ur(e){return g(e?.getReader)}function S(e){if(e instanceof _)return e;if(e!=null){if(nr(e))return Sf(e);if(jt(e))return Nf(e);if(tr(e))return Af(e);if(rr(e))return du(e);if(sr(e))return Of(e);if(ur(e))return Ff(e)}throw or(e)}function Sf(e){return new _(t=>{let n=e[Ft]();if(g(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Nf(e){return new _(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Af(e){return new _(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,qn)})}function Of(e){return new _(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function du(e){return new _(t=>{Rf(e,t).catch(n=>t.error(n))})}function Ff(e){return du(ar(e))}function Rf(e,t){var n,r,o,i;return uu(this,void 0,void 0,function*(){try{for(n=lu(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function re(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function cr(e,t=0){return w((n,r)=>{n.subscribe(v(r,o=>re(r,e,()=>r.next(o),t),()=>re(r,e,()=>r.complete(),t),o=>re(r,e,()=>r.error(o),t)))})}function lr(e,t=0){return w((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function fu(e,t){return S(e).pipe(lr(t),cr(t))}function pu(e,t){return S(e).pipe(lr(t),cr(t))}function hu(e,t){return new _(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function gu(e,t){return new _(n=>{let r;return re(n,t,()=>{r=e[ir](),re(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>g(r?.return)&&r.return()})}function dr(e,t){if(!e)throw new Error("Iterable cannot be null");return new _(n=>{re(n,t,()=>{let r=e[Symbol.asyncIterator]();re(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function mu(e,t){return dr(ar(e),t)}function yu(e,t){if(e!=null){if(nr(e))return fu(e,t);if(jt(e))return hu(e,t);if(tr(e))return pu(e,t);if(rr(e))return dr(e,t);if(sr(e))return gu(e,t);if(ur(e))return mu(e,t)}throw or(e)}function Ae(e,t){return t?yu(e,t):S(e)}function Pf(...e){let t=Ne(e);return Ae(e,t)}function kf(e,t){let n=g(e)?e:()=>e,r=o=>o.error(n());return new _(t?o=>t.schedule(r,0,o):r)}function Lf(e){return!!e&&(e instanceof _||g(e.lift)&&g(e.subscribe))}var je=Nt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function jf(e,t){let n=typeof t=="object";return new Promise((r,o)=>{let i=new Ie({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{n?r(t.defaultValue):o(new je)}});e.subscribe(i)})}function vu(e){return e instanceof Date&&!isNaN(e)}function Oe(e,t){return w((n,r)=>{let o=0;n.subscribe(v(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Vf}=Array;function Bf(e,t){return Vf(t)?e(...t):e(t)}function Vt(e){return Oe(t=>Bf(e,t))}var{isArray:$f}=Array,{getPrototypeOf:Hf,prototype:Uf,keys:Gf}=Object;function fr(e){if(e.length===1){let t=e[0];if($f(t))return{args:t,keys:null};if(Wf(t)){let n=Gf(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Wf(e){return e&&typeof e=="object"&&Hf(e)===Uf}function pr(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function zf(...e){let t=Ne(e),n=er(e),{args:r,keys:o}=fr(e);if(r.length===0)return Ae([],t);let i=new _(qf(r,t,o?s=>pr(o,s):X));return n?i.pipe(Vt(n)):i}function qf(e,t,n=X){return r=>{Du(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let u=0;u<o;u++)Du(t,()=>{let c=Ae(e[u],t),l=!1;c.subscribe(v(r,d=>{i[u]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Du(e,t,n){e?re(n,e,t):t()}function wu(e,t,n,r,o,i,s,a){let u=[],c=0,l=0,d=!1,f=()=>{d&&!u.length&&!c&&t.complete()},p=y=>c<r?h(y):u.push(y),h=y=>{i&&t.next(y),c++;let T=!1;S(n(y,l++)).subscribe(v(t,M=>{o?.(M),i?p(M):t.next(M)},()=>{T=!0},void 0,()=>{if(T)try{for(c--;u.length&&c<r;){let M=u.shift();s?re(t,s,()=>h(M)):h(M)}f()}catch(M){t.error(M)}}))};return e.subscribe(v(t,p,()=>{d=!0,f()})),()=>{a?.()}}function Ee(e,t,n=1/0){return g(t)?Ee((r,o)=>Oe((i,s)=>t(r,i,o,s))(S(e(r,o))),n):(typeof t=="number"&&(n=t),w((r,o)=>wu(r,o,e,n)))}function pn(e=1/0){return Ee(X,e)}function Iu(){return pn(1)}function Bt(...e){return Iu()(Ae(e,Ne(e)))}function Yf(e){return new _(t=>{S(e()).subscribe(t)})}function Qf(...e){let t=er(e),{args:n,keys:r}=fr(e),o=new _(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),u=s,c=s;for(let l=0;l<s;l++){let d=!1;S(n[l]).subscribe(v(i,f=>{d||(d=!0,c--),a[l]=f},()=>u--,void 0,()=>{(!u||!d)&&(c||i.next(r?pr(r,a):a),i.complete())}))}});return t?o.pipe(Vt(t)):o}var Zf=["addListener","removeListener"],Kf=["addEventListener","removeEventListener"],Jf=["on","off"];function Zo(e,t,n,r){if(g(n)&&(r=n,n=void 0),r)return Zo(e,t,n).pipe(Vt(r));let[o,i]=tp(e)?Kf.map(s=>a=>e[s](t,a,n)):Xf(e)?Zf.map(Eu(e,t)):ep(e)?Jf.map(Eu(e,t)):[];if(!o&&jt(e))return Ee(s=>Zo(s,t,n))(S(e));if(!o)throw new TypeError("Invalid event target");return new _(s=>{let a=(...u)=>s.next(1<u.length?u:u[0]);return o(a),()=>i(a)})}function Eu(e,t){return n=>r=>e[n](t,r)}function Xf(e){return g(e.addListener)&&g(e.removeListener)}function ep(e){return g(e.on)&&g(e.off)}function tp(e){return g(e.addEventListener)&&g(e.removeEventListener)}function hn(e=0,t,n=iu){let r=-1;return t!=null&&(Xn(t)?n=t:r=t),new _(o=>{let i=vu(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function np(...e){let t=Ne(e),n=su(e,1/0),r=e;return r.length?r.length===1?S(r[0]):pn(n)(Ae(r,t)):lt}function ft(e,t){return w((n,r)=>{let o=0;n.subscribe(v(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Cu(e){return w((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let c=o;o=null,n.next(c)}s&&n.complete()},u=()=>{i=null,s&&n.complete()};t.subscribe(v(n,c=>{r=!0,o=c,i||S(e(c)).subscribe(i=v(n,a,u))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function rp(e,t=ct){return Cu(()=>hn(e,t))}function bu(e){return w((t,n)=>{let r=null,o=!1,i;r=t.subscribe(v(n,void 0,void 0,s=>{i=S(e(s,bu(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Mu(e,t,n,r,o){return(i,s)=>{let a=n,u=t,c=0;i.subscribe(v(s,l=>{let d=c++;u=a?e(u,l,d):(a=!0,l),r&&s.next(u)},o&&(()=>{a&&s.next(u),s.complete()})))}}function op(e,t){return g(t)?Ee(e,t,1):Ee(e,1)}function ip(e,t=ct){return w((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let c=i;i=null,r.next(c)}};function u(){let c=s+e,l=t.now();if(l<c){o=this.schedule(void 0,c-l),r.add(o);return}a()}n.subscribe(v(r,c=>{i=c,s=t.now(),o||(o=t.schedule(u,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function gn(e){return w((t,n)=>{let r=!1;t.subscribe(v(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function $t(e){return e<=0?()=>lt:w((t,n)=>{let r=0;t.subscribe(v(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function _u(){return w((e,t)=>{e.subscribe(v(t,st))})}function Ko(e){return Oe(()=>e)}function Jo(e,t){return t?n=>Bt(t.pipe($t(1),_u()),n.pipe(Jo(e))):Ee((n,r)=>S(e(n,r)).pipe($t(1),Ko(n)))}function sp(e,t=ct){let n=hn(e,t);return Jo(()=>n)}function ap(e,t=X){return e=e??up,w((n,r)=>{let o,i=!0;n.subscribe(v(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function up(e,t){return e===t}function hr(e=cp){return w((t,n)=>{let r=!1;t.subscribe(v(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function cp(){return new je}function lp(e){return w((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function xu(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ft((o,i)=>e(o,i,r)):X,$t(1),n?gn(t):hr(()=>new je))}function Xo(e){return e<=0?()=>lt:w((t,n)=>{let r=[];t.subscribe(v(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function dp(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ft((o,i)=>e(o,i,r)):X,Xo(1),n?gn(t):hr(()=>new je))}function fp(e,t){return w(Mu(e,t,arguments.length>=2,!0))}function ti(e={}){let{connector:t=()=>new ge,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,u,c=0,l=!1,d=!1,f=()=>{a?.unsubscribe(),a=void 0},p=()=>{f(),s=u=void 0,l=d=!1},h=()=>{let y=s;p(),y?.unsubscribe()};return w((y,T)=>{c++,!d&&!l&&f();let M=u=u??t();T.add(()=>{c--,c===0&&!d&&!l&&(a=ei(h,o))}),M.subscribe(T),!s&&c>0&&(s=new Ie({next:W=>M.next(W),error:W=>{d=!0,f(),a=ei(p,n,W),M.error(W)},complete:()=>{l=!0,f(),a=ei(p,r),M.complete()}}),S(y).subscribe(s))})(i)}}function ei(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Ie({next:()=>{r.unsubscribe(),e()}});return S(t(...n)).subscribe(r)}function pp(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,ti({connector:()=>new dn(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function hp(e){return ft((t,n)=>e<=n)}function Tu(...e){let t=Ne(e);return w((n,r)=>{(t?Bt(e,n,t):Bt(e,n)).subscribe(r)})}function gp(e,t){return w((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(v(r,u=>{o?.unsubscribe();let c=0,l=i++;S(e(u,l)).subscribe(o=v(r,d=>r.next(t?t(u,d,l,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function mp(e){return w((t,n)=>{S(e).subscribe(v(n,()=>n.complete(),st)),!n.closed&&t.subscribe(n)})}function yp(e,t=!1){return w((n,r)=>{let o=0;n.subscribe(v(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function vp(e,t,n){let r=g(e)||t||n?{next:e,error:t,complete:n}:e;return r?w((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(v(i,u=>{var c;(c=r.next)===null||c===void 0||c.call(r,u),i.next(u)},()=>{var u;a=!1,(u=r.complete)===null||u===void 0||u.call(r),i.complete()},u=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,u),i.error(u)},()=>{var u,c;a&&((u=r.unsubscribe)===null||u===void 0||u.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):X}var yc="https://g.co/ng/security#xss",C=class extends Error{constructor(t,n){super(vc(t,n)),this.code=t}};function vc(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}function On(e){return{toString:e}.toString()}var gr="__parameters__";function Dp(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Dc(e,t,n){return On(()=>{let r=Dp(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(u,c,l){let d=u.hasOwnProperty(gr)?u[gr]:Object.defineProperty(u,gr,{value:[]})[gr];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),u}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var ue=globalThis;function R(e){for(let t in e)if(e[t]===R)return t;throw Error("Could not find renamed property on target object.")}function wp(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function te(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(te).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function vi(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var Ip=R({__forward_ref__:R});function wc(e){return e.__forward_ref__=wc,e.toString=function(){return te(this())},e}function ee(e){return Ic(e)?e():e}function Ic(e){return typeof e=="function"&&e.hasOwnProperty(Ip)&&e.__forward_ref__===wc}function k(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Os(e){return{providers:e.providers||[],imports:e.imports||[]}}function Jr(e){return Su(e,Ec)||Su(e,Cc)}function zx(e){return Jr(e)!==null}function Su(e,t){return e.hasOwnProperty(t)?e[t]:null}function Ep(e){let t=e&&(e[Ec]||e[Cc]);return t||null}function Nu(e){return e&&(e.hasOwnProperty(Au)||e.hasOwnProperty(Cp))?e[Au]:null}var Ec=R({\u0275prov:R}),Au=R({\u0275inj:R}),Cc=R({ngInjectableDef:R}),Cp=R({ngInjectorDef:R}),O=class{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=k({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function bc(e){return e&&!!e.\u0275providers}var bp=R({\u0275cmp:R}),Mp=R({\u0275dir:R}),_p=R({\u0275pipe:R}),xp=R({\u0275mod:R}),Tr=R({\u0275fac:R}),mn=R({__NG_ELEMENT_ID__:R}),Ou=R({__NG_ENV_ID__:R});function ce(e){return typeof e=="string"?e:e==null?"":String(e)}function Tp(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():ce(e)}function Sp(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new C(-200,e)}function Fs(e,t){throw new C(-201,!1)}var x=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(x||{}),Di;function Mc(){return Di}function oe(e){let t=Di;return Di=e,t}function _c(e,t,n){let r=Jr(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&x.Optional)return null;if(t!==void 0)return t;Fs(e,"Injector")}var Np={},vn=Np,wi="__NG_DI_FLAG__",Sr="ngTempTokenPath",Ap="ngTokenPath",Op=/\n/gm,Fp="\u0275",Fu="__source",zt;function Rp(){return zt}function Ye(e){let t=zt;return zt=e,t}function Pp(e,t=x.Default){if(zt===void 0)throw new C(-203,!1);return zt===null?_c(e,void 0,t):zt.get(e,t&x.Optional?null:void 0,t)}function H(e,t=x.Default){return(Mc()||Pp)(ee(e),t)}function F(e,t=x.Default){return H(e,Xr(t))}function Xr(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Ii(e){let t=[];for(let n=0;n<e.length;n++){let r=ee(e[n]);if(Array.isArray(r)){if(r.length===0)throw new C(900,!1);let o,i=x.Default;for(let s=0;s<r.length;s++){let a=r[s],u=kp(a);typeof u=="number"?u===-1?o=a.token:i|=u:o=a}t.push(H(o,i))}else t.push(H(r))}return t}function xc(e,t){return e[wi]=t,e.prototype[wi]=t,e}function kp(e){return e[wi]}function Lp(e,t,n,r){let o=e[Sr];throw t[Fu]&&o.unshift(t[Fu]),e.message=jp(`
`+e.message,o,n,r),e[Ap]=o,e[Sr]=null,e}function jp(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Fp?e.slice(2):e;let o=te(t);if(Array.isArray(t))o=t.map(te).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):te(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Op,`
  `)}`}var Tc=xc(Dc("Optional"),8);var Sc=xc(Dc("SkipSelf"),4);function mt(e,t){let n=e.hasOwnProperty(Tr);return n?e[Tr]:null}function Vp(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Bp(e){return e.flat(Number.POSITIVE_INFINITY)}function Rs(e,t){e.forEach(n=>Array.isArray(n)?Rs(n,t):t(n))}function Nc(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Nr(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function $p(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function Hp(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function eo(e,t,n){let r=Fn(e,t);return r>=0?e[r|1]=n:(r=~r,Hp(e,r,t,n)),r}function ni(e,t){let n=Fn(e,t);if(n>=0)return e[n|1]}function Fn(e,t){return Up(e,t,1)}function Up(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Yt={},ie=[],Ar=new O(""),Ac=new O("",-1),Oc=new O(""),Or=class{get(t,n=vn){if(n===vn){let r=new Error(`NullInjectorError: No provider for ${te(t)}!`);throw r.name="NullInjectorError",r}return n}},Fc=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Fc||{}),Dn=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Dn||{}),Ze=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Ze||{});function Gp(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}function Ei(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];Wp(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Rc(e){return e===3||e===4||e===6}function Wp(e){return e.charCodeAt(0)===64}function wn(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Ru(e,n,o,null,t[++r]):Ru(e,n,o,null,null))}}return e}function Ru(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(e[i+1]=o);return}else if(r===e[i+1]){e[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),r!==null&&e.splice(i++,0,r),o!==null&&e.splice(i++,0,o)}var Pc="ng-template";function zp(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Gp(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Ps(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Ps(e){return e.type===4&&e.value!==Pc}function qp(e,t,n){let r=e.type===4&&!n?Pc:e.value;return t===r}function Yp(e,t,n){let r=4,o=e.attrs,i=o!==null?Kp(o):0,s=!1;for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="number"){if(!s&&!Ce(r)&&!Ce(u))return!1;if(s&&Ce(u))continue;s=!1,r=u|r&1;continue}if(!s)if(r&4){if(r=2|r&1,u!==""&&!qp(e,u,n)||u===""&&t.length===1){if(Ce(r))return!1;s=!0}}else if(r&8){if(o===null||!zp(e,o,u,n)){if(Ce(r))return!1;s=!0}}else{let c=t[++a],l=Qp(u,o,Ps(e),n);if(l===-1){if(Ce(r))return!1;s=!0;continue}if(c!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&c!==d){if(Ce(r))return!1;s=!0}}}}return Ce(r)||s}function Ce(e){return(e&1)===0}function Qp(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Jp(t,e)}function kc(e,t,n=!1){for(let r=0;r<t.length;r++)if(Yp(e,t[r],n))return!0;return!1}function Zp(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if(!(n&1))return t[n+1]}return null}function Kp(e){for(let t=0;t<e.length;t++){let n=e[t];if(Rc(n))return t}return e.length}function Jp(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Xp(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Pu(e,t){return e?":not("+t.trim()+")":t}function eh(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Ce(s)&&(t+=Pu(i,o),o=""),r=s,i=i||!Ce(r);n++}return o!==""&&(t+=Pu(i,o)),t}function th(e){return e.map(eh).join(",")}function nh(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Ce(o))break;o=i}r++}return{attrs:t,classes:n}}function qx(e){return On(()=>{let t=Bc(e),n=un(ot({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Fc.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Dn.Emulated,styles:e.styles||ie,_:null,schemas:e.schemas||null,tView:null,id:""});$c(n);let r=e.dependencies;return n.directiveDefs=Lu(r,!1),n.pipeDefs=Lu(r,!0),n.id=sh(n),n})}function rh(e){return Ke(e)||Lc(e)}function oh(e){return e!==null}function ks(e){return On(()=>({type:e.type,bootstrap:e.bootstrap||ie,declarations:e.declarations||ie,imports:e.imports||ie,exports:e.exports||ie,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function ku(e,t){if(e==null)return Yt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a=Ze.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),t?(n[i]=a!==Ze.None?[r,a]:r,t[i]=s):n[i]=r}return n}function nn(e){return On(()=>{let t=Bc(e);return $c(t),t})}function Rn(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Ke(e){return e[bp]||null}function Lc(e){return e[Mp]||null}function jc(e){return e[_p]||null}function ih(e){let t=Ke(e)||Lc(e)||jc(e);return t!==null?t.standalone:!1}function Vc(e,t){let n=e[xp]||null;if(!n&&t===!0)throw new Error(`Type ${te(e)} does not have '\u0275mod' property.`);return n}function Bc(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Yt,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||ie,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:ku(e.inputs,t),outputs:ku(e.outputs),debugInfo:null}}function $c(e){e.features?.forEach(t=>t(e))}function Lu(e,t){if(!e)return null;let n=t?jc:rh;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(oh)}function sh(e){let t=0,n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=**********,"c"+t}function Yx(e){return{\u0275providers:e}}function ah(...e){return{\u0275providers:Hc(!0,e),\u0275fromNgModule:!0}}function Hc(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Rs(t,s=>{let a=s;Ci(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Uc(o,i),n}function Uc(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Ls(o,i=>{t(i,r)})}}function Ci(e,t,n,r){if(e=ee(e),!e)return!1;let o=null,i=Nu(e),s=!i&&Ke(e);if(!i&&!s){let u=e.ngModule;if(i=Nu(u),i)o=u;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let u=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of u)Ci(c,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let c;try{Rs(i.imports,l=>{Ci(l,t,n,r)&&(c||=[],c.push(l))})}finally{}c!==void 0&&Uc(c,t)}if(!a){let c=mt(o)||(()=>new o);t({provide:o,useFactory:c,deps:ie},o),t({provide:Oc,useValue:o,multi:!0},o),t({provide:Ar,useValue:()=>H(o),multi:!0},o)}let u=i.providers;if(u!=null&&!a){let c=e;Ls(u,l=>{t(l,c)})}}else return!1;return o!==e&&e.providers!==void 0}function Ls(e,t){for(let n of e)bc(n)&&(n=n.\u0275providers),Array.isArray(n)?Ls(n,t):t(n)}var uh=R({provide:String,useValue:R});function Gc(e){return e!==null&&typeof e=="object"&&uh in e}function ch(e){return!!(e&&e.useExisting)}function lh(e){return!!(e&&e.useFactory)}function Qt(e){return typeof e=="function"}function dh(e){return!!e.useClass}var Wc=new O(""),Er={},fh={},ri;function js(){return ri===void 0&&(ri=new Or),ri}var Je=class{},In=class extends Je{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,Mi(t,s=>this.processProvider(s)),this.records.set(Ac,Ht(void 0,this)),o.has("environment")&&this.records.set(Je,Ht(void 0,this));let i=this.records.get(Wc);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Oc,ie,x.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=b(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),b(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let n=Ye(this),r=oe(void 0),o;try{return t()}finally{Ye(n),oe(r)}}get(t,n=vn,r=x.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Ou))return t[Ou](this);r=Xr(r);let o,i=Ye(this),s=oe(void 0);try{if(!(r&x.SkipSelf)){let u=this.records.get(t);if(u===void 0){let c=yh(t)&&Jr(t);c&&this.injectableDefInScope(c)?u=Ht(bi(t),Er):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u)}let a=r&x.Self?js():this.parent;return n=r&x.Optional&&n===vn?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Sr]=a[Sr]||[]).unshift(te(t)),i)throw a;return Lp(a,t,"R3InjectorError",this.source)}else throw a}finally{oe(s),Ye(i)}}resolveInjectorInitializers(){let t=b(null),n=Ye(this),r=oe(void 0),o;try{let i=this.get(Ar,ie,x.Self);for(let s of i)s()}finally{Ye(n),oe(r),b(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(te(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new C(205,!1)}processProvider(t){t=ee(t);let n=Qt(t)?t:ee(t&&t.provide),r=hh(t);if(!Qt(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Ht(void 0,Er,!0),o.factory=()=>Ii(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=b(null);try{return n.value===Er&&(n.value=fh,n.value=n.factory()),typeof n.value=="object"&&n.value&&mh(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{b(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ee(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function bi(e){let t=Jr(e),n=t!==null?t.factory:mt(e);if(n!==null)return n;if(e instanceof O)throw new C(204,!1);if(e instanceof Function)return ph(e);throw new C(204,!1)}function ph(e){if(e.length>0)throw new C(204,!1);let n=Ep(e);return n!==null?()=>n.factory(e):()=>new e}function hh(e){if(Gc(e))return Ht(void 0,e.useValue);{let t=zc(e);return Ht(t,Er)}}function zc(e,t,n){let r;if(Qt(e)){let o=ee(e);return mt(o)||bi(o)}else if(Gc(e))r=()=>ee(e.useValue);else if(lh(e))r=()=>e.useFactory(...Ii(e.deps||[]));else if(ch(e))r=()=>H(ee(e.useExisting));else{let o=ee(e&&(e.useClass||e.provide));if(gh(e))r=()=>new o(...Ii(e.deps));else return mt(o)||bi(o)}return r}function Ht(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function gh(e){return!!e.deps}function mh(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function yh(e){return typeof e=="function"||typeof e=="object"&&e instanceof O}function Mi(e,t){for(let n of e)Array.isArray(n)?Mi(n,t):n&&bc(n)?Mi(n.\u0275providers,t):t(n)}function vh(e,t){e instanceof In&&e.assertNotDestroyed();let n,r=Ye(e),o=oe(void 0);try{return t()}finally{Ye(r),oe(o)}}function qc(){return Mc()!==void 0||Rp()!=null}function Dh(e){if(!qc())throw new C(-203,!1)}function wh(e){let t=ue.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function Ih(e){return typeof e=="function"}var ke=0,I=1,D=2,q=3,_e=4,de=5,Zt=6,En=7,K=8,Kt=9,xe=10,j=11,Cn=12,ju=13,rn=14,me=15,Pn=16,Ut=17,Ve=18,to=19,Yc=20,Qe=21,oi=22,yt=23,Y=25,Vs=1;var vt=7,Fr=8,Jt=9,J=10,Bs=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(Bs||{});function ht(e){return Array.isArray(e)&&typeof e[Vs]=="object"}function He(e){return Array.isArray(e)&&e[Vs]===!0}function $s(e){return(e.flags&4)!==0}function no(e){return e.componentOffset>-1}function ro(e){return(e.flags&1)===1}function Be(e){return!!e.template}function Eh(e){return(e[D]&512)!==0}var _i=class{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Qc(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}function Hs(){return Zc}function Zc(e){return e.type.prototype.ngOnChanges&&(e.setInput=bh),Ch}Hs.ngInherit=!0;function Ch(){let e=Jc(this),t=e?.current;if(t){let n=e.previous;if(n===Yt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function bh(e,t,n,r,o){let i=this.declaredInputs[r],s=Jc(e)||Mh(e,{previous:Yt,current:null}),a=s.current||(s.current={}),u=s.previous,c=u[i];a[i]=new _i(c&&c.currentValue,n,u===Yt),Qc(e,t,o,n)}var Kc="__ngSimpleChanges__";function Jc(e){return e[Kc]||null}function Mh(e,t){return e[Kc]=t}var Vu=null;var Fe=function(e,t,n){Vu?.(e,t,n)},Xc="svg",_h="math",xh=!1;function Th(){return xh}function Pe(e){for(;Array.isArray(e);)e=e[ke];return e}function Sh(e){for(;Array.isArray(e);){if(typeof e[Vs]=="object")return e;e=e[ke]}return null}function el(e,t){return Pe(t[e])}function ye(e,t){return Pe(t[e.index])}function Us(e,t){return e.data[t]}function Gs(e,t){return e[t]}function tt(e,t){let n=t[e];return ht(n)?n:n[ke]}function Nh(e){return(e[D]&4)===4}function Ws(e){return(e[D]&128)===128}function Ah(e){return He(e[q])}function Xt(e,t){return t==null?null:e[t]}function tl(e){e[Ut]=0}function Oh(e){e[D]&1024||(e[D]|=1024,Ws(e)&&bn(e))}function Fh(e,t){for(;e>0;)t=t[rn],e--;return t}function zs(e){return!!(e[D]&9216||e[yt]?.dirty)}function xi(e){e[xe].changeDetectionScheduler?.notify(1),zs(e)?bn(e):e[D]&64&&(Th()?(e[D]|=1024,bn(e)):e[xe].changeDetectionScheduler?.notify())}function bn(e){e[xe].changeDetectionScheduler?.notify();let t=Mn(e);for(;t!==null&&!(t[D]&8192||(t[D]|=8192,!Ws(t)));)t=Mn(t)}function nl(e,t){if((e[D]&256)===256)throw new C(911,!1);e[Qe]===null&&(e[Qe]=[]),e[Qe].push(t)}function Rh(e,t){if(e[Qe]===null)return;let n=e[Qe].indexOf(t);n!==-1&&e[Qe].splice(n,1)}function Mn(e){let t=e[q];return He(t)?t[q]:t}var E={lFrame:cl(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function Ph(){return E.lFrame.elementDepthCount}function kh(){E.lFrame.elementDepthCount++}function Lh(){E.lFrame.elementDepthCount--}function rl(){return E.bindingsEnabled}function ol(){return E.skipHydrationRootTNode!==null}function jh(e){return E.skipHydrationRootTNode===e}function Vh(){E.skipHydrationRootTNode=null}function m(){return E.lFrame.lView}function V(){return E.lFrame.tView}function Qx(e){return E.lFrame.contextLView=e,e[K]}function Zx(e){return E.lFrame.contextLView=null,e}function Q(){let e=il();for(;e!==null&&e.type===64;)e=e.parent;return e}function il(){return E.lFrame.currentTNode}function Bh(){let e=E.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function _t(e,t){let n=E.lFrame;n.currentTNode=e,n.isParent=t}function qs(){return E.lFrame.isParent}function Ys(){E.lFrame.isParent=!1}function $h(){return E.lFrame.contextLView}function kn(){let e=E.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Qs(){return E.lFrame.bindingIndex}function Hh(e){return E.lFrame.bindingIndex=e}function nt(){return E.lFrame.bindingIndex++}function Ln(e){let t=E.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Uh(){return E.lFrame.inI18n}function Gh(e,t){let n=E.lFrame;n.bindingIndex=n.bindingRootIndex=e,Ti(t)}function Wh(){return E.lFrame.currentDirectiveIndex}function Ti(e){E.lFrame.currentDirectiveIndex=e}function Zs(e){let t=E.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function sl(){return E.lFrame.currentQueryIndex}function Ks(e){E.lFrame.currentQueryIndex=e}function zh(e){let t=e[I];return t.type===2?t.declTNode:t.type===1?e[de]:null}function al(e,t,n){if(n&x.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&x.Host);)if(o=zh(i),o===null||(i=i[rn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=E.lFrame=ul();return r.currentTNode=t,r.lView=e,!0}function Js(e){let t=ul(),n=e[I];E.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function ul(){let e=E.lFrame,t=e===null?null:e.child;return t===null?cl(e):t}function cl(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function ll(){let e=E.lFrame;return E.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var dl=ll;function Xs(){let e=ll();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function qh(e){return(E.lFrame.contextLView=Fh(e,E.lFrame.contextLView))[K]}function Te(){return E.lFrame.selectedIndex}function Dt(e){E.lFrame.selectedIndex=e}function on(){let e=E.lFrame;return Us(e.tView,e.selectedIndex)}function Kx(){E.lFrame.currentNamespace=Xc}function Jx(){Yh()}function Yh(){E.lFrame.currentNamespace=null}function Qh(){return E.lFrame.currentNamespace}var fl=!0;function oo(){return fl}function io(e){fl=e}function Zh(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Zc(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function so(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:u,ngAfterViewChecked:c,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),u&&(e.viewHooks??=[]).push(-n,u),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Cr(e,t,n){pl(e,t,3,n)}function br(e,t,n,r){(e[D]&3)===n&&pl(e,t,n,r)}function ii(e,t){let n=e[D];(n&3)===t&&(n&=16383,n+=1,e[D]=n)}function pl(e,t,n,r){let o=r!==void 0?e[Ut]&65535:0,i=r??-1,s=t.length-1,a=0;for(let u=o;u<s;u++)if(typeof t[u+1]=="number"){if(a=t[u],r!=null&&a>=r)break}else t[u]<0&&(e[Ut]+=65536),(a<i||i==-1)&&(Kh(e,n,t,u),e[Ut]=(e[Ut]&**********)+u+2),u++}function Bu(e,t){Fe(4,e,t);let n=b(null);try{t.call(e)}finally{b(n),Fe(5,e,t)}}function Kh(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[D]>>14<e[Ut]>>16&&(e[D]&3)===t&&(e[D]+=16384,Bu(a,i)):Bu(a,i)}var qt=-1,wt=class{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function Jh(e){return e instanceof wt}function Xh(e){return(e.flags&8)!==0}function eg(e){return(e.flags&16)!==0}function hl(e){return e!==qt}function Rr(e){return e&32767}function tg(e){return e>>16}function Pr(e,t){let n=tg(e),r=t;for(;n>0;)r=r[rn],n--;return r}var Si=!0;function kr(e){let t=Si;return Si=e,t}var ng=256,gl=ng-1,ml=5,rg=0,Re={};function og(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(mn)&&(r=n[mn]),r==null&&(r=n[mn]=rg++);let o=r&gl,i=1<<o;t.data[e+(o>>ml)]|=i}function Lr(e,t){let n=yl(e,t);if(n!==-1)return n;let r=t[I];r.firstCreatePass&&(e.injectorIndex=t.length,si(r.data,e),si(t,null),si(r.blueprint,null));let o=ea(e,t),i=e.injectorIndex;if(hl(o)){let s=Rr(o),a=Pr(o,t),u=a[I].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|u[s+c]}return t[i+8]=o,i}function si(e,t){e.push(0,0,0,0,0,0,0,0,t)}function yl(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function ea(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=El(o),r===null)return qt;if(n++,o=o[rn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return qt}function Ni(e,t,n){og(e,t,n)}function ig(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Rc(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function vl(e,t,n){if(n&x.Optional||e!==void 0)return e;Fs(t,"NodeInjector")}function Dl(e,t,n,r){if(n&x.Optional&&r===void 0&&(r=null),!(n&(x.Self|x.Host))){let o=e[Kt],i=oe(void 0);try{return o?o.get(t,r,n&x.Optional):_c(t,r,n&x.Optional)}finally{oe(i)}}return vl(r,t,n)}function wl(e,t,n,r=x.Default,o){if(e!==null){if(t[D]&2048&&!(r&x.Self)){let s=cg(e,t,n,r,Re);if(s!==Re)return s}let i=Il(e,t,n,r,Re);if(i!==Re)return i}return Dl(t,n,r,o)}function Il(e,t,n,r,o){let i=ag(n);if(typeof i=="function"){if(!al(t,e,r))return r&x.Host?vl(o,n,r):Dl(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&x.Optional))Fs(n);else return s}finally{dl()}}else if(typeof i=="number"){let s=null,a=yl(e,t),u=qt,c=r&x.Host?t[me][de]:null;for((a===-1||r&x.SkipSelf)&&(u=a===-1?ea(e,t):t[a+8],u===qt||!Hu(r,!1)?a=-1:(s=t[I],a=Rr(u),t=Pr(u,t)));a!==-1;){let l=t[I];if($u(i,a,l.data)){let d=sg(a,t,n,s,r,c);if(d!==Re)return d}u=t[a+8],u!==qt&&Hu(r,t[I].data[a+8]===c)&&$u(i,a,t)?(s=l,a=Rr(u),t=Pr(u,t)):a=-1}}return o}function sg(e,t,n,r,o,i){let s=t[I],a=s.data[e+8],u=r==null?no(a)&&Si:r!=s&&(a.type&3)!==0,c=o&x.Host&&i===a,l=Mr(a,s,n,u,c);return l!==null?It(t,s,l,a):Re}function Mr(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,u=e.directiveStart,c=e.directiveEnd,l=i>>20,d=r?a:a+l,f=o?a+l:c;for(let p=d;p<f;p++){let h=s[p];if(p<u&&n===h||p>=u&&h.type===n)return p}if(o){let p=s[u];if(p&&Be(p)&&p.type===n)return u}return null}function It(e,t,n,r){let o=e[n],i=t.data;if(Jh(o)){let s=o;s.resolving&&Sp(Tp(i[n]));let a=kr(s.canSeeViewProviders);s.resolving=!0;let u,c=s.injectImpl?oe(s.injectImpl):null,l=al(e,r,x.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&Zh(n,i[n],t)}finally{c!==null&&oe(c),kr(a),s.resolving=!1,dl()}}return o}function ag(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(mn)?e[mn]:void 0;return typeof t=="number"?t>=0?t&gl:ug:t}function $u(e,t,n){let r=1<<e;return!!(n[t+(e>>ml)]&r)}function Hu(e,t){return!(e&x.Self)&&!(e&x.Host&&t)}var gt=class{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return wl(this._tNode,this._lView,t,Xr(r),n)}};function ug(){return new gt(Q(),m())}function Xx(e){return On(()=>{let t=e.prototype.constructor,n=t[Tr]||Ai(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Tr]||Ai(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Ai(e){return Ic(e)?()=>{let t=Ai(ee(e));return t&&t()}:mt(e)}function cg(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[D]&2048&&!(s[D]&512);){let a=Il(i,s,n,r|x.Self,Re);if(a!==Re)return a;let u=i.parent;if(!u){let c=s[Yc];if(c){let l=c.get(n,Re,r);if(l!==Re)return l}u=El(s),s=s[rn]}i=u}return o}function El(e){let t=e[I],n=t.type;return n===2?t.declTNode:n===1?e[de]:null}function lg(e){return ig(Q(),e)}function Uu(e,t=null,n=null,r){let o=Cl(e,t,n,r);return o.resolveInjectorInitializers(),o}function Cl(e,t=null,n=null,r,o=new Set){let i=[n||ie,ah(e)];return r=r||(typeof e=="object"?void 0:te(e)),new In(i,t||js(),r||null,o)}var xt=(()=>{let t=class t{static create(r,o){if(Array.isArray(r))return Uu({name:""},o,r,"");{let i=r.name??"";return Uu({name:i},r.parent,r.providers,i)}}};t.THROW_IF_NOT_FOUND=vn,t.NULL=new Or,t.\u0275prov=k({token:t,providedIn:"any",factory:()=>H(Ac)}),t.__NG_ELEMENT_ID__=-1;let e=t;return e})();var dg="ngOriginalError";function ai(e){return e[dg]}var Et=class{constructor(){this._console=console}handleError(t){let n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&ai(t);for(;n&&ai(n);)n=ai(n);return n||null}},bl=new O("",{providedIn:"root",factory:()=>F(Et).handleError.bind(void 0)}),ta=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=fg,t.__NG_ENV_ID__=r=>r;let e=t;return e})(),Oi=class extends ta{constructor(t){super(),this._lView=t}onDestroy(t){return nl(this._lView,t),()=>Rh(this._lView,t)}};function fg(){return new Oi(m())}function pg(){return sn(Q(),m())}function sn(e,t){return new rt(ye(e,t))}var rt=(()=>{let t=class t{constructor(r){this.nativeElement=r}};t.__NG_ELEMENT_ID__=pg;let e=t;return e})();function hg(e){return e instanceof rt?e.nativeElement:e}var Fi=class extends ge{constructor(t=!1){super(),this.destroyRef=void 0,this.__isAsync=t,qc()&&(this.destroyRef=F(ta,{optional:!0})??void 0)}emit(t){let n=b(null);try{super.next(t)}finally{b(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let u=t;o=u.next?.bind(u),i=u.error?.bind(u),s=u.complete?.bind(u)}this.__isAsync&&(i=ui(i),o&&(o=ui(o)),s&&(s=ui(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof $&&t.add(a),a}};function ui(e){return t=>{setTimeout(e,void 0,t)}}var Me=Fi;function gg(){return this._results[Symbol.iterator]()}var Ri=class e{get changes(){return this._changes??=new Me}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let n=e.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=gg)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Bp(t);(this._changesDetected=!Vp(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}};function Ml(e){return(e.flags&128)===128}var _l=new Map,mg=0;function yg(){return mg++}function vg(e){_l.set(e[to],e)}function Dg(e){_l.delete(e[to])}var Gu="__ngContext__";function Xe(e,t){ht(t)?(e[Gu]=t[to],vg(t)):e[Gu]=t}function xl(e){return Sl(e[Cn])}function Tl(e){return Sl(e[_e])}function Sl(e){for(;e!==null&&!He(e);)e=e[_e];return e}var Pi;function eT(e){Pi=e}function wg(){if(Pi!==void 0)return Pi;if(typeof document<"u")return document;throw new C(210,!1)}var tT=new O("",{providedIn:"root",factory:()=>Ig}),Ig="ng",Eg=new O(""),na=new O("",{providedIn:"platform",factory:()=>"unknown"});var nT=new O(""),rT=new O("",{providedIn:"root",factory:()=>wg().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Cg="h",bg="b";var Mg=()=>null;function ra(e,t,n=!1){return Mg(e,t,n)}var Nl=!1,_g=new O("",{providedIn:"root",factory:()=>Nl});var mr;function Al(){if(mr===void 0&&(mr=null,ue.trustedTypes))try{mr=ue.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return mr}function ao(e){return Al()?.createHTML(e)||e}function xg(e){return Al()?.createScriptURL(e)||e}var yr;function Tg(){if(yr===void 0&&(yr=null,ue.trustedTypes))try{yr=ue.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return yr}function Wu(e){return Tg()?.createScriptURL(e)||e}var $e=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${yc})`}},ki=class extends $e{getTypeName(){return"HTML"}},Li=class extends $e{getTypeName(){return"Style"}},ji=class extends $e{getTypeName(){return"Script"}},Vi=class extends $e{getTypeName(){return"URL"}},Bi=class extends $e{getTypeName(){return"ResourceURL"}};function jn(e){return e instanceof $e?e.changingThisBreaksApplicationSecurity:e}function Ol(e,t){let n=Sg(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${yc})`)}return n===t}function Sg(e){return e instanceof $e&&e.getTypeName()||null}function oT(e){return new ki(e)}function iT(e){return new Li(e)}function sT(e){return new ji(e)}function aT(e){return new Vi(e)}function uT(e){return new Bi(e)}function Ng(e){let t=new Hi(e);return Ag()?new $i(t):t}var $i=class{constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(ao(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.removeChild(n.firstChild),n)}catch{return null}}},Hi=class{constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=ao(t),n}};function Ag(){try{return!!new window.DOMParser().parseFromString(ao(""),"text/html")}catch{return!1}}var Og=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Fl(e){return e=String(e),e.match(Og)?e:"unsafe:"+e}function Ue(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Vn(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Rl=Ue("area,br,col,hr,img,wbr"),Pl=Ue("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),kl=Ue("rp,rt"),Fg=Vn(kl,Pl),Rg=Vn(Pl,Ue("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Pg=Vn(kl,Ue("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),zu=Vn(Rl,Rg,Pg,Fg),Ll=Ue("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),kg=Ue("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Lg=Ue("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),jg=Vn(Ll,kg,Lg),Vg=Ue("script,style,template"),Ui=class{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=Hg(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=$g(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=qu(t).toLowerCase();if(!zu.hasOwnProperty(n))return this.sanitizedSomething=!0,!Vg.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!jg.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let u=i.value;Ll[a]&&(u=Fl(u)),this.buf.push(" ",s,'="',Yu(u),'"')}return this.buf.push(">"),!0}endElement(t){let n=qu(t).toLowerCase();zu.hasOwnProperty(n)&&!Rl.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Yu(t))}};function Bg(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function $g(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw jl(t);return t}function Hg(e){let t=e.firstChild;if(t&&Bg(e,t))throw jl(t);return t}function qu(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function jl(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var Ug=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Gg=/([^\#-~ |!])/g;function Yu(e){return e.replace(/&/g,"&amp;").replace(Ug,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(Gg,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var vr;function cT(e,t){let n=null;try{vr=vr||Ng(e);let r=t?String(t):"";n=vr.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=vr.getInertBodyElement(r)}while(r!==i);let a=new Ui().sanitizeChildren(Qu(n)||n);return ao(a)}finally{if(n){let r=Qu(n)||n;for(;r.firstChild;)r.removeChild(r.firstChild)}}}function Qu(e){return"content"in e&&Wg(e)?e.content:null}function Wg(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var oa=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(oa||{});function zg(e){let t=Vl();return t?t.sanitize(oa.URL,e)||"":Ol(e,"URL")?jn(e):Fl(ce(e))}function qg(e){let t=Vl();if(t)return Wu(t.sanitize(oa.RESOURCE_URL,e)||"");if(Ol(e,"ResourceURL"))return Wu(jn(e));throw new C(904,!1)}function lT(e){return xg(e[0])}function Yg(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?qg:zg}function dT(e,t,n){return Yg(t,n)(e)}function Vl(){let e=m();return e&&e[xe].sanitizer}var Qg=/^>|^->|<!--|-->|--!>|<!-$/g,Zg=/(<|>)/g,Kg="\u200B$1\u200B";function Jg(e){return e.replace(Qg,t=>t.replace(Zg,Kg))}function fT(e){return e.ownerDocument}function Bl(e){return e instanceof Function?e():e}function Xg(e){return(e??F(xt)).get(na)==="browser"}var _n=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(_n||{}),em;function ia(e,t){return em(e,t)}function Gt(e,t,n,r,o){if(r!=null){let i,s=!1;He(r)?i=r:ht(r)&&(s=!0,r=r[ke]);let a=Pe(r);e===0&&n!==null?o==null?Wl(t,n,a):jr(t,n,a,o||null,!0):e===1&&n!==null?jr(t,n,a,o||null,!0):e===2?mm(t,a,s):e===3&&t.destroyNode(a),i!=null&&vm(t,e,i,n,o)}}function tm(e,t){return e.createText(t)}function nm(e,t,n){e.setValue(t,n)}function rm(e,t){return e.createComment(Jg(t))}function $l(e,t,n){return e.createElement(t,n)}function om(e,t){Hl(e,t),t[ke]=null,t[de]=null}function im(e,t,n,r,o,i){r[ke]=o,r[de]=t,lo(e,r,n,1,o,i)}function Hl(e,t){t[xe].changeDetectionScheduler?.notify(1),lo(e,t,t[j],2,null,null)}function sm(e){let t=e[Cn];if(!t)return ci(e[I],e);for(;t;){let n=null;if(ht(t))n=t[Cn];else{let r=t[J];r&&(n=r)}if(!n){for(;t&&!t[_e]&&t!==e;)ht(t)&&ci(t[I],t),t=t[q];t===null&&(t=e),ht(t)&&ci(t[I],t),n=t&&t[_e]}t=n}}function am(e,t,n,r){let o=J+r,i=n.length;r>0&&(n[o-1][_e]=t),r<i-J?(t[_e]=n[o],Nc(n,J+r,t)):(n.push(t),t[_e]=null),t[q]=n;let s=t[Pn];s!==null&&n!==s&&um(s,t);let a=t[Ve];a!==null&&a.insertView(e),xi(t),t[D]|=128}function um(e,t){let n=e[Jt],o=t[q][q][me];t[me]!==o&&(e[D]|=Bs.HasTransplantedViews),n===null?e[Jt]=[t]:n.push(t)}function Ul(e,t){let n=e[Jt],r=n.indexOf(t);n.splice(r,1)}function xn(e,t){if(e.length<=J)return;let n=J+t,r=e[n];if(r){let o=r[Pn];o!==null&&o!==e&&Ul(o,r),t>0&&(e[n-1][_e]=r[_e]);let i=Nr(e,J+t);om(r[I],r);let s=i[Ve];s!==null&&s.detachView(i[I]),r[q]=null,r[_e]=null,r[D]&=-129}return r}function uo(e,t){if(!(t[D]&256)){let n=t[j];n.destroyNode&&lo(e,t,n,3,null,null),sm(t)}}function ci(e,t){if(t[D]&256)return;let n=b(null);try{t[D]&=-129,t[D]|=256,t[yt]&&Za(t[yt]),lm(e,t),cm(e,t),t[I].type===1&&t[j].destroy();let r=t[Pn];if(r!==null&&He(t[q])){r!==t[q]&&Ul(r,t);let o=t[Ve];o!==null&&o.detachView(e)}Dg(t)}finally{b(n)}}function cm(e,t){let n=e.cleanup,r=t[En];if(n!==null)for(let i=0;i<n.length-1;i+=2)if(typeof n[i]=="string"){let s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else{let s=r[n[i+1]];n[i].call(s)}r!==null&&(t[En]=null);let o=t[Qe];if(o!==null){t[Qe]=null;for(let i=0;i<o.length;i++){let s=o[i];s()}}}function lm(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof wt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],u=i[s+1];Fe(4,a,u);try{u.call(a)}finally{Fe(5,a,u)}}else{Fe(4,o,i);try{i.call(o)}finally{Fe(5,o,i)}}}}}function Gl(e,t,n){return dm(e,t.parent,n)}function dm(e,t,n){let r=t;for(;r!==null&&r.type&40;)t=r,r=t.parent;if(r===null)return n[ke];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=e.data[r.directiveStart+o];if(i===Dn.None||i===Dn.Emulated)return null}return ye(r,n)}}function jr(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Wl(e,t,n){e.appendChild(t,n)}function Zu(e,t,n,r,o){r!==null?jr(e,t,n,r,o):Wl(e,t,n)}function fm(e,t,n,r){e.removeChild(t,n,r)}function sa(e,t){return e.parentNode(t)}function pm(e,t){return e.nextSibling(t)}function zl(e,t,n){return gm(e,t,n)}function hm(e,t,n){return e.type&40?ye(e,n):null}var gm=hm,Ku;function co(e,t,n,r){let o=Gl(e,r,t),i=t[j],s=r.parent||t[de],a=zl(s,r,t);if(o!=null)if(Array.isArray(n))for(let u=0;u<n.length;u++)Zu(i,o,n[u],a,!1);else Zu(i,o,n,a,!1);Ku!==void 0&&Ku(i,r,t,n,o)}function _r(e,t){if(t!==null){let n=t.type;if(n&3)return ye(t,e);if(n&4)return Gi(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return _r(e,r);{let o=e[t.index];return He(o)?Gi(-1,o):Pe(o)}}else{if(n&32)return ia(t,e)()||Pe(e[t.index]);{let r=ql(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=Mn(e[me]);return _r(o,r)}else return _r(e,t.next)}}}return null}function ql(e,t){if(t!==null){let r=e[me][de],o=t.projection;return r.projection[o]}return null}function Gi(e,t){let n=J+e+1;if(n<t.length){let r=t[n],o=r[I].firstChild;if(o!==null)return _r(r,o)}return t[vt]}function mm(e,t,n){let r=sa(e,t);r&&fm(e,r,t,n)}function aa(e,t,n,r,o,i,s){for(;n!=null;){let a=r[n.index],u=n.type;if(s&&t===0&&(a&&Xe(Pe(a),r),n.flags|=2),(n.flags&32)!==32)if(u&8)aa(e,t,n.child,r,o,i,!1),Gt(t,e,o,a,i);else if(u&32){let c=ia(n,r),l;for(;l=c();)Gt(t,e,o,l,i);Gt(t,e,o,a,i)}else u&16?Yl(e,t,r,n,o,i):Gt(t,e,o,a,i);n=s?n.projectionNext:n.next}}function lo(e,t,n,r,o,i){aa(n,r,e.firstChild,t,o,i,!1)}function ym(e,t,n){let r=t[j],o=Gl(e,n,t),i=n.parent||t[de],s=zl(i,n,t);Yl(r,0,t,n,o,s)}function Yl(e,t,n,r,o,i){let s=n[me],u=s[de].projection[r.projection];if(Array.isArray(u))for(let c=0;c<u.length;c++){let l=u[c];Gt(t,e,o,l,i)}else{let c=u,l=s[q];Ml(r)&&(c.flags|=128),aa(e,t,c,l,o,i,!0)}}function vm(e,t,n,r,o){let i=n[vt],s=Pe(n);i!==s&&Gt(t,e,r,i,o);for(let a=J;a<n.length;a++){let u=n[a];lo(u[I],u,e,t,r,i)}}function Dm(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:_n.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=_n.Important),e.setStyle(n,r,o,i))}}function wm(e,t,n){e.setAttribute(t,"style",n)}function Ql(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Zl(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Ei(e,t,r),o!==null&&Ql(e,t,o),i!==null&&wm(e,t,i)}var Z={};function pT(e=1){Kl(V(),m(),Te()+e,!1)}function Kl(e,t,n,r){if(!r)if((t[D]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Cr(t,i,n)}else{let i=e.preOrderHooks;i!==null&&br(t,i,0,n)}Dt(n)}function U(e,t=x.Default){let n=m();if(n===null)return H(e,t);let r=Q();return wl(r,n,ee(e),t)}function hT(){let e="invalid";throw new Error(e)}function Jl(e,t,n,r,o,i){let s=b(null);try{let a=null;o&Ze.SignalBased&&(a=t[r][Po]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&Ze.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(t,i)),e.setInput!==null?e.setInput(t,a,i,n,r):Qc(t,a,r,i)}finally{b(s)}}function Im(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Dt(~o);else{let i=o,s=n[++r],a=n[++r];Gh(s,i);let u=t[i];a(2,u)}}}finally{Dt(-1)}}function fo(e,t,n,r,o,i,s,a,u,c,l){let d=t.blueprint.slice();return d[ke]=o,d[D]=r|4|128|8|64,(c!==null||e&&e[D]&2048)&&(d[D]|=2048),tl(d),d[q]=d[rn]=e,d[K]=n,d[xe]=s||e&&e[xe],d[j]=a||e&&e[j],d[Kt]=u||e&&e[Kt]||null,d[de]=i,d[to]=yg(),d[Zt]=l,d[Yc]=c,d[me]=t.type==2?e[me]:d,d}function an(e,t,n,r,o){let i=e.data[t];if(i===null)i=Em(e,t,n,r,o),Uh()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Bh();i.injectorIndex=s===null?-1:s.injectorIndex}return _t(i,!0),i}function Em(e,t,n,r,o){let i=il(),s=qs(),a=s?i:i&&i.parent,u=e.data[t]=Tm(e,a,n,t,r,o);return e.firstChild===null&&(e.firstChild=u),i!==null&&(s?i.child==null&&u.parent!==null&&(i.child=u):i.next===null&&(i.next=u,u.prev=i)),u}function Xl(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function ed(e,t,n,r,o){let i=Te(),s=r&2;try{Dt(-1),s&&t.length>Y&&Kl(e,t,Y,!1),Fe(s?2:0,o),n(r,o)}finally{Dt(i),Fe(s?3:1,o)}}function ua(e,t,n){if($s(t)){let r=b(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let u=n[s];a.contentQueries(1,u,s)}}}finally{b(r)}}}function ca(e,t,n){rl()&&(Rm(e,t,n,ye(n,t)),(n.flags&64)===64&&rd(e,t,n))}function la(e,t,n=ye){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function td(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=da(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function da(e,t,n,r,o,i,s,a,u,c,l){let d=Y+r,f=d+o,p=Cm(d,f),h=typeof c=="function"?c():c;return p[I]={type:e,blueprint:p,template:n,queries:null,viewQuery:a,declTNode:t,data:p.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:f,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:u,consts:h,incompleteFirstPass:!1,ssrId:l}}function Cm(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Z);return n}function bm(e,t,n,r){let i=r.get(_g,Nl)||n===Dn.ShadowDom,s=e.selectRootElement(t,i);return Mm(s),s}function Mm(e){_m(e)}var _m=()=>null;function xm(e,t,n,r){let o=sd(t);o.push(n),e.firstCreatePass&&ad(e).push(r,o.length-1)}function Tm(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return ol()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function Ju(e,t,n,r,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;r??={};let a,u=Ze.None;Array.isArray(s)?(a=s[0],u=s[1]):a=s;let c=i;if(o!==null){if(!o.hasOwnProperty(i))continue;c=o[i]}e===0?Xu(r,n,c,a,u):Xu(r,n,c,a)}return r}function Xu(e,t,n,r,o){let i;e.hasOwnProperty(n)?(i=e[n]).push(t,r):i=e[n]=[t,r],o!==void 0&&i.push(o)}function Sm(e,t,n){let r=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],u=null,c=null;for(let l=r;l<o;l++){let d=i[l],f=n?n.get(d):null,p=f?f.inputs:null,h=f?f.outputs:null;u=Ju(0,d.inputs,l,u,p),c=Ju(1,d.outputs,l,c,h);let y=u!==null&&s!==null&&!Ps(t)?Wm(u,l,s):null;a.push(y)}u!==null&&(u.hasOwnProperty("class")&&(t.flags|=8),u.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=u,t.outputs=c}function Nm(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Bn(e,t,n,r,o,i,s,a){let u=ye(t,n),c=t.inputs,l;!a&&c!=null&&(l=c[r])?(pa(e,n,l,r,o),no(t)&&Am(n,t.index)):t.type&3?(r=Nm(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(u,r,o)):t.type&12}function Am(e,t){let n=tt(t,e);n[D]&16||(n[D]|=64)}function fa(e,t,n,r){if(rl()){let o=r===null?null:{"":-1},i=km(e,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&nd(e,t,n,s,o,a),o&&Lm(n,r,o)}n.mergedAttrs=wn(n.mergedAttrs,n.attrs)}function nd(e,t,n,r,o,i){for(let c=0;c<r.length;c++)Ni(Lr(n,t),e,r[c].type);Vm(n,e.data.length,r.length);for(let c=0;c<r.length;c++){let l=r[c];l.providersResolver&&l.providersResolver(l)}let s=!1,a=!1,u=Xl(e,t,r.length,null);for(let c=0;c<r.length;c++){let l=r[c];n.mergedAttrs=wn(n.mergedAttrs,l.hostAttrs),Bm(e,n,t,u,l),jm(u,l,o),l.contentQueries!==null&&(n.flags|=4),(l.hostBindings!==null||l.hostAttrs!==null||l.hostVars!==0)&&(n.flags|=64);let d=l.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),u++}Sm(e,n,i)}function Om(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;Fm(s)!=a&&s.push(a),s.push(n,r,i)}}function Fm(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function Rm(e,t,n,r){let o=n.directiveStart,i=n.directiveEnd;no(n)&&$m(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Lr(n,t),Xe(r,t);let s=n.initialInputs;for(let a=o;a<i;a++){let u=e.data[a],c=It(t,e,a,n);if(Xe(c,t),s!==null&&Gm(t,a-o,c,u,n,s),Be(u)){let l=tt(n.index,t);l[K]=It(t,e,a,n)}}}function rd(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Wh();try{Dt(i);for(let a=r;a<o;a++){let u=e.data[a],c=t[a];Ti(a),(u.hostBindings!==null||u.hostVars!==0||u.hostAttrs!==null)&&Pm(u,c)}}finally{Dt(-1),Ti(s)}}function Pm(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function km(e,t){let n=e.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(kc(t,s.selectors,!1))if(r||(r=[]),Be(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let u=a.length;Wi(e,t,u)}else r.unshift(s),Wi(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function Wi(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Lm(e,t,n){if(t){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new C(-301,!1);r.push(t[o],i)}}}function jm(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Be(t)&&(n[""]=e)}}function Vm(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Bm(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=mt(o.type,!0)),s=new wt(i,Be(o),U);e.blueprint[r]=s,n[r]=s,Om(e,t,r,Xl(e,n,o.hostVars,Z),o)}function $m(e,t,n){let r=ye(t,e),o=td(n),i=e[xe].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=po(e,fo(e,o,null,s,r,t,null,i.createRenderer(r,n),null,null,null));e[t.index]=a}function Hm(e,t,n,r,o,i){let s=ye(e,t);Um(t[j],s,i,e.value,n,r,o)}function Um(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?ce(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function Gm(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let u=s[a++],c=s[a++],l=s[a++],d=s[a++];Jl(r,n,u,c,l,d)}}function Wm(e,t,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){r===null&&(r=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function od(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function id(e,t){let n=e.contentQueries;if(n!==null){let r=b(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Ks(i),a.contentQueries(2,t[s],s)}}}finally{b(r)}}}function po(e,t){return e[Cn]?e[ju][_e]=t:e[Cn]=t,e[ju]=t,t}function zi(e,t,n){Ks(0);let r=b(null);try{t(e,n)}finally{b(r)}}function sd(e){return e[En]||(e[En]=[])}function ad(e){return e.cleanup||(e.cleanup=[])}function ud(e,t,n){return(e===null||Be(e))&&(n=Sh(n[t.index])),n[j]}function cd(e,t){let n=e[Kt],r=n?n.get(Et,null):null;r&&r.handleError(t)}function pa(e,t,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],u=n[i++],c=t[s],l=e.data[s];Jl(l,c,r,a,u,o)}}function ho(e,t,n){let r=el(t,e);nm(e[j],r,n)}function zm(e,t){let n=tt(t,e),r=n[I];qm(r,n);let o=n[ke];o!==null&&n[Zt]===null&&(n[Zt]=ra(o,n[Kt])),ha(r,n,n[K])}function qm(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function ha(e,t,n){Js(t);try{let r=e.viewQuery;r!==null&&zi(1,r,n);let o=e.template;o!==null&&ed(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Ve]?.finishViewCreation(e),e.staticContentQueries&&id(e,t),e.staticViewQueries&&zi(2,e.viewQuery,n);let i=e.components;i!==null&&Ym(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[D]&=-5,Xs()}}function Ym(e,t){for(let n=0;n<t.length;n++)zm(e,t[n])}function go(e,t,n,r){let o=b(null);try{let i=t.tView,a=e[D]&4096?4096:16,u=fo(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),c=e[t.index];u[Pn]=c;let l=e[Ve];return l!==null&&(u[Ve]=l.createEmbeddedView(i)),ha(i,u,n),u}finally{b(o)}}function ld(e,t){let n=J+t;if(n<e.length)return e[n]}function Tn(e,t){return!t||t.firstChild===null||Ml(e)}function mo(e,t,n,r=!0){let o=t[I];if(am(o,t,e,n),r){let s=Gi(n,e),a=t[j],u=sa(a,e[vt]);u!==null&&im(o,e[de],a,t,u,s)}let i=t[Zt];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function dd(e,t){let n=xn(e,t);return n!==void 0&&uo(n[I],n),n}function Vr(e,t,n,r,o=!1){for(;n!==null;){let i=t[n.index];i!==null&&r.push(Pe(i)),He(i)&&Qm(i,r);let s=n.type;if(s&8)Vr(e,t,n.child,r);else if(s&32){let a=ia(n,t),u;for(;u=a();)r.push(u)}else if(s&16){let a=ql(t,n);if(Array.isArray(a))r.push(...a);else{let u=Mn(t[me]);Vr(u[I],u,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Qm(e,t){for(let n=J;n<e.length;n++){let r=e[n],o=r[I].firstChild;o!==null&&Vr(r[I],r,o,t)}e[vt]!==e[ke]&&t.push(e[vt])}var fd=[];function Zm(e){return e[yt]??Km(e)}function Km(e){let t=fd.pop()??Object.create(Xm);return t.lView=e,t}function Jm(e){e.lView[yt]!==e&&(e.lView=null,fd.push(e))}var Xm=un(ot({},qa),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{bn(e.lView)},consumerOnSignalRead(){this.lView[yt]=this}}),pd=100;function hd(e,t=!0,n=0){let r=e[xe],o=r.rendererFactory,i=!1;i||o.begin?.();try{ey(e,n)}catch(s){throw t&&cd(e,s),s}finally{i||(o.end?.(),r.inlineEffectRunner?.flush())}}function ey(e,t){qi(e,t);let n=0;for(;zs(e);){if(n===pd)throw new C(103,!1);n++,qi(e,1)}}function ty(e,t,n,r){let o=t[D];if((o&256)===256)return;let i=!1;!i&&t[xe].inlineEffectRunner?.flush(),Js(t);let s=null,a=null;!i&&ny(e)&&(a=Zm(t),s=Ya(a));try{tl(t),Hh(e.bindingStartIndex),n!==null&&ed(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let d=e.preOrderCheckHooks;d!==null&&Cr(t,d,null)}else{let d=e.preOrderHooks;d!==null&&br(t,d,0,null),ii(t,0)}if(ry(t),gd(t,0),e.contentQueries!==null&&id(e,t),!i)if(u){let d=e.contentCheckHooks;d!==null&&Cr(t,d)}else{let d=e.contentHooks;d!==null&&br(t,d,1),ii(t,1)}Im(e,t);let c=e.components;c!==null&&yd(t,c,0);let l=e.viewQuery;if(l!==null&&zi(2,l,r),!i)if(u){let d=e.viewCheckHooks;d!==null&&Cr(t,d)}else{let d=e.viewHooks;d!==null&&br(t,d,2),ii(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[oi]){for(let d of t[oi])d();t[oi]=null}i||(t[D]&=-73)}catch(u){throw bn(t),u}finally{a!==null&&(Qa(a,s),Jm(a)),Xs()}}function ny(e){return e.type!==2}function gd(e,t){for(let n=xl(e);n!==null;n=Tl(n))for(let r=J;r<n.length;r++){let o=n[r];md(o,t)}}function ry(e){for(let t=xl(e);t!==null;t=Tl(t)){if(!(t[D]&Bs.HasTransplantedViews))continue;let n=t[Jt];for(let r=0;r<n.length;r++){let o=n[r],i=o[q];Oh(o)}}}function oy(e,t,n){let r=tt(t,e);md(r,n)}function md(e,t){Ws(e)&&qi(e,t)}function qi(e,t){let r=e[I],o=e[D],i=e[yt],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&ko(i)),i&&(i.dirty=!1),e[D]&=-9217,s)ty(r,e,r.template,e[K]);else if(o&8192){gd(e,1);let a=r.components;a!==null&&yd(e,a,1)}}function yd(e,t,n){for(let r=0;r<t.length;r++)oy(e,t[r],n)}function ga(e){for(e[xe].changeDetectionScheduler?.notify();e;){e[D]|=64;let t=Mn(e);if(Eh(e)&&!t)return e;e=t}return null}var Ct=class{get rootNodes(){let t=this._lView,n=t[I];return Vr(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[K]}set context(t){this._lView[K]=t}get destroyed(){return(this._lView[D]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[q];if(He(t)){let n=t[Fr],r=n?n.indexOf(this):-1;r>-1&&(xn(t,r),Nr(n,r))}this._attachedToViewContainer=!1}uo(this._lView[I],this._lView)}onDestroy(t){nl(this._lView,t)}markForCheck(){ga(this._cdRefInjectingView||this._lView)}detach(){this._lView[D]&=-129}reattach(){xi(this._lView),this._lView[D]|=128}detectChanges(){this._lView[D]|=1024,hd(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new C(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,Hl(this._lView[I],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new C(902,!1);this._appRef=t,xi(this._lView)}},bt=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=ay;let e=t;return e})(),iy=bt,sy=class extends iy{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=go(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Ct(o)}};function ay(){return yo(Q(),m())}function yo(e,t){return e.type&4?new sy(t,e,sn(e,t)):null}var mT=new RegExp(`^(\\d+)*(${bg}|${Cg})*(.*)`);var uy=()=>null;function Sn(e,t){return uy(e,t)}var Br=class{},Yi=class{},$r=class{};function cy(e){let t=Error(`No component factory found for ${te(e)}.`);return t[ly]=e,t}var ly="ngComponent";var Qi=class{resolveComponentFactory(t){throw cy(t)}},vo=(()=>{let t=class t{};t.NULL=new Qi;let e=t;return e})(),Zi=class{},Do=(()=>{let t=class t{constructor(){this.destroyNode=null}};t.__NG_ELEMENT_ID__=()=>dy();let e=t;return e})();function dy(){let e=m(),t=Q(),n=tt(t.index,e);return(ht(n)?n:e)[j]}var fy=(()=>{let t=class t{};t.\u0275prov=k({token:t,providedIn:"root",factory:()=>null});let e=t;return e})(),li={};var ec=new Set;function $n(e){ec.has(e)||(ec.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function tc(...e){}function py(){let e=typeof ue.requestAnimationFrame=="function",t=ue[e?"requestAnimationFrame":"setTimeout"],n=ue[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){let r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);let o=n[Zone.__symbol__("OriginalDelegate")];o&&(n=o)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}var le=class e{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Me(!1),this.onMicrotaskEmpty=new Me(!1),this.onStable=new Me(!1),this.onError=new Me(!1),typeof Zone>"u")throw new C(908,!1);Zone.assertZonePatched();let o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=py().nativeRequestAnimationFrame,my(o)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new C(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new C(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,hy,tc,tc);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},hy={};function ma(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function gy(e){e.isCheckStableRunning||e.lastRequestAnimationFrameId!==-1||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(ue,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,Ki(e),e.isCheckStableRunning=!0,ma(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),Ki(e))}function my(e){let t=()=>{gy(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{if(yy(a))return n.invokeTask(o,i,s,a);try{return nc(e),n.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&i.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),rc(e)}},onInvoke:(n,r,o,i,s,a,u)=>{try{return nc(e),n.invoke(o,i,s,a,u)}finally{e.shouldCoalesceRunChangeDetection&&t(),rc(e)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&(i.change=="microTask"?(e._hasPendingMicrotasks=i.microTask,Ki(e),ma(e)):i.change=="macroTask"&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}function Ki(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.lastRequestAnimationFrameId!==-1?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function nc(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function rc(e){e._nesting--,ma(e)}var Ji=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Me,this.onMicrotaskEmpty=new Me,this.onStable=new Me,this.onError=new Me}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function yy(e){return!Array.isArray(e)||e.length!==1?!1:e[0].data?.__ignore_ng_zone__===!0}function vy(e="zone.js",t){return e==="noop"?new Ji:e==="zone.js"?new le(t):e}var Wt=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Wt||{}),Dy={destroy(){}};function wy(e,t){!t&&Dh(wy);let n=t?.injector??F(xt);if(!Xg(n))return Dy;$n("NgAfterNextRender");let r=n.get(ya),o=r.handler??=new es,i=t?.phase??Wt.MixedReadWrite,s=()=>{o.unregister(u),a()},a=n.get(ta).onDestroy(s),u=vh(n,()=>new Xi(i,()=>{s(),e()}));return o.register(u),{destroy:s}}var Xi=class{constructor(t,n){this.phase=t,this.callbackFn=n,this.zone=F(le),this.errorHandler=F(Et,{optional:!0}),F(Br,{optional:!0})?.notify(1)}invoke(){try{this.zone.runOutsideAngular(this.callbackFn)}catch(t){this.errorHandler?.handleError(t)}}},es=class{constructor(){this.executingCallbacks=!1,this.buckets={[Wt.EarlyRead]:new Set,[Wt.Write]:new Set,[Wt.MixedReadWrite]:new Set,[Wt.Read]:new Set},this.deferredCallbacks=new Set}register(t){(this.executingCallbacks?this.deferredCallbacks:this.buckets[t.phase]).add(t)}unregister(t){this.buckets[t.phase].delete(t),this.deferredCallbacks.delete(t)}execute(){this.executingCallbacks=!0;for(let t of Object.values(this.buckets))for(let n of t)n.invoke();this.executingCallbacks=!1;for(let t of this.deferredCallbacks)this.buckets[t.phase].add(t);this.deferredCallbacks.clear()}destroy(){for(let t of Object.values(this.buckets))t.clear();this.deferredCallbacks.clear()}},ya=(()=>{let t=class t{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){this.executeInternalCallbacks(),this.handler?.execute()}executeInternalCallbacks(){let r=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let o of r)o()}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}};t.\u0275prov=k({token:t,providedIn:"root",factory:()=>new t});let e=t;return e})();function Hr(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=vi(o,a);else if(i==2){let u=a,c=t[++s];r=vi(r,u+": "+c+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}var Ur=class extends vo{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Ke(t);return new en(n,this.ngModule)}};function oc(e){let t=[];for(let n in e){if(!e.hasOwnProperty(n))continue;let r=e[n];r!==void 0&&t.push({propName:Array.isArray(r)?r[0]:r,templateName:n})}return t}function Iy(e){let t=e.toLowerCase();return t==="svg"?Xc:t==="math"?_h:null}var ts=class{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Xr(r);let o=this.injector.get(t,li,r);return o!==li||n===li?o:this.parentInjector.get(t,n,r)}},en=class extends $r{get inputs(){let t=this.componentDef,n=t.inputTransforms,r=oc(t.inputs);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return oc(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=th(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=b(null);try{o=o||this.ngModule;let s=o instanceof Je?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new ts(t,s):t,u=a.get(Zi,null);if(u===null)throw new C(407,!1);let c=a.get(fy,null),l=a.get(ya,null),d=a.get(Br,null),f={rendererFactory:u,sanitizer:c,inlineEffectRunner:null,afterRenderEventManager:l,changeDetectionScheduler:d},p=u.createRenderer(null,this.componentDef),h=this.componentDef.selectors[0][0]||"div",y=r?bm(p,r,this.componentDef.encapsulation,a):$l(p,h,Iy(h)),T=512;this.componentDef.signals?T|=4096:this.componentDef.onPush||(T|=16);let M=null;y!==null&&(M=ra(y,a,!0));let W=da(0,null,null,1,0,null,null,null,null,null,null),z=fo(null,W,null,T,null,null,f,p,a,null,M);Js(z);let ae,Le;try{let De=this.componentDef,St,Fo=null;De.findHostDirectiveDefs?(St=[],Fo=new Map,De.findHostDirectiveDefs(De,St,Fo),St.push(De)):St=[De];let gf=Ey(z,y),mf=Cy(gf,y,De,St,z,f,p);Le=Us(W,Y),y&&_y(p,De,y,r),n!==void 0&&xy(Le,this.ngContentSelectors,n),ae=My(mf,De,St,Fo,z,[Ty]),ha(W,z,null)}finally{Xs()}return new ns(this.componentType,ae,sn(Le,z),z,Le)}finally{b(i)}}},ns=class extends Yi{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new Ct(o,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView;pa(i[I],i,o,t,n),this.previousInputValues.set(t,n);let s=tt(this._tNode.index,i);ga(s)}}get injector(){return new gt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Ey(e,t){let n=e[I],r=Y;return e[r]=t,an(n,r,2,"#host",null)}function Cy(e,t,n,r,o,i,s){let a=o[I];by(r,e,t,s);let u=null;t!==null&&(u=ra(t,o[Kt]));let c=i.rendererFactory.createRenderer(t,n),l=16;n.signals?l=4096:n.onPush&&(l=64);let d=fo(o,td(n),null,l,o[e.index],e,i,c,null,null,u);return a.firstCreatePass&&Wi(a,e,r.length-1),po(o,d),o[e.index]=d}function by(e,t,n,r){for(let o of e)t.mergedAttrs=wn(t.mergedAttrs,o.hostAttrs);t.mergedAttrs!==null&&(Hr(t,t.mergedAttrs,!0),n!==null&&Zl(r,n,t))}function My(e,t,n,r,o,i){let s=Q(),a=o[I],u=ye(s,o);nd(a,o,s,n,null,r);for(let l=0;l<n.length;l++){let d=s.directiveStart+l,f=It(o,a,d,s);Xe(f,o)}rd(a,o,s),u&&Xe(u,o);let c=It(o,a,s.directiveStart+s.componentOffset,s);if(e[K]=o[K]=c,i!==null)for(let l of i)l(c,t);return ua(a,s,o),c}function _y(e,t,n,r){if(r)Ei(e,n,["ng-version","17.3.4"]);else{let{attrs:o,classes:i}=nh(t.selectors[0]);o&&Ei(e,n,o),i&&i.length>0&&Ql(e,n,i.join(" "))}}function xy(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null?Array.from(i):null)}}function Ty(){let e=Q();so(m()[I],e)}var Tt=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=Sy;let e=t;return e})();function Sy(){let e=Q();return Dd(e,m())}var Ny=Tt,vd=class extends Ny{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return sn(this._hostTNode,this._hostLView)}get injector(){return new gt(this._hostTNode,this._hostLView)}get parentInjector(){let t=ea(this._hostTNode,this._hostLView);if(hl(t)){let n=Pr(t,this._hostLView),r=Rr(t),o=n[I].data[r+8];return new gt(o,n)}else return new gt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=ic(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-J}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Sn(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Tn(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Ih(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let u=s?t:new en(Ke(t)),c=r||this.parentInjector;if(!i&&u.ngModule==null){let y=(s?c:this.parentInjector).get(Je,null);y&&(i=y)}let l=Ke(u.componentType??{}),d=Sn(this._lContainer,l?.id??null),f=d?.firstChild??null,p=u.create(c,o,f,i);return this.insertImpl(p.hostView,a,Tn(this._hostTNode,d)),p}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Ah(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let u=o[q],c=new vd(u,u[de],u[q]);c.detach(c.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return mo(s,o,i,r),t.attachToViewContainerRef(),Nc(di(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=ic(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=xn(this._lContainer,n);r&&(Nr(di(this._lContainer),n),uo(r[I],r))}detach(t){let n=this._adjustIndex(t,-1),r=xn(this._lContainer,n);return r&&Nr(di(this._lContainer),n)!=null?new Ct(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function ic(e){return e[Fr]}function di(e){return e[Fr]||(e[Fr]=[])}function Dd(e,t){let n,r=t[e.index];return He(r)?n=r:(n=od(r,t,null,e),t[e.index]=n,po(t,n)),Oy(n,t,e,r),new vd(n,e,t)}function Ay(e,t){let n=e[j],r=n.createComment(""),o=ye(t,e),i=sa(n,o);return jr(n,i,r,pm(n,o),!1),r}var Oy=Py,Fy=()=>!1;function Ry(e,t,n){return Fy(e,t,n)}function Py(e,t,n,r){if(e[vt])return;let o;n.type&8?o=Pe(r):o=Ay(t,n),e[vt]=o}var rs=class e{constructor(t){this.queryList=t,this.matches=null}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},os=class e{constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)va(t,n).matches!==null&&this.queries[n].setDirty()}},Gr=class{constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=Uy(t):this.predicate=t}},is=class e{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},ss=class e{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,ky(n,i)),this.matchTNodeWithReadOption(t,n,Mr(n,t,i,!1,!1))}else r===bt?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Mr(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===rt||o===Tt||o===bt&&n.type&4)this.addMatch(n.index,-2);else{let i=Mr(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function ky(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function Ly(e,t){return e.type&11?sn(e,t):e.type&4?yo(e,t):null}function jy(e,t,n,r){return n===-1?Ly(t,e):n===-2?Vy(e,t,r):It(e,e[I],n,t)}function Vy(e,t,n){if(n===rt)return sn(t,e);if(n===bt)return yo(t,e);if(n===Tt)return Dd(t,e)}function wd(e,t,n,r){let o=t[Ve].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let u=0;s!==null&&u<s.length;u+=2){let c=s[u];if(c<0)a.push(null);else{let l=i[c];a.push(jy(t,l,s[u+1],n.metadata.read))}}o.matches=a}return o.matches}function as(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=wd(e,t,o,n);for(let a=0;a<i.length;a+=2){let u=i[a];if(u>0)r.push(s[a/2]);else{let c=i[a+1],l=t[-u];for(let d=J;d<l.length;d++){let f=l[d];f[Pn]===f[q]&&as(f[I],f,c,r)}if(l[Jt]!==null){let d=l[Jt];for(let f=0;f<d.length;f++){let p=d[f];as(p[I],p,c,r)}}}}}return r}function By(e,t){return e[Ve].queries[t].queryList}function Id(e,t,n){let r=new Ri((n&4)===4);return xm(e,t,r,r.destroy),(t[Ve]??=new os).queries.push(new rs(r))-1}function $y(e,t,n){let r=V();return r.firstCreatePass&&(Ed(r,new Gr(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Id(r,m(),t)}function Hy(e,t,n,r){let o=V();if(o.firstCreatePass){let i=Q();Ed(o,new Gr(t,n,r),i.index),Gy(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Id(o,m(),n)}function Uy(e){return e.split(",").map(t=>t.trim())}function Ed(e,t,n){e.queries===null&&(e.queries=new is),e.queries.track(new ss(t,n))}function Gy(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function va(e,t){return e.queries.getByIndex(t)}function Wy(e,t){let n=e[I],r=va(n,t);return r.crossesNgTemplate?as(n,e,t,[]):wd(n,e,r,t)}function zy(e){return typeof e=="function"&&e[Po]!==void 0}function Cd(e){return zy(e)&&typeof e.set=="function"}function qy(e){let t=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=e(o);n.set(o,i=s.then(Ky))}return i}return Wr.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(c=>{o.template=c}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let c=o.styles.length,l=o.styleUrls;o.styleUrls.forEach((d,f)=>{a.push(""),s.push(r(d).then(p=>{a[c+f]=p,l.splice(l.indexOf(d),1),l.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(c=>{a.push(c),o.styleUrl=void 0}));let u=Promise.all(s).then(()=>Jy(i));t.push(u)}),Qy(),Promise.all(t).then(()=>{})}var Wr=new Map,Yy=new Set;function Qy(){let e=Wr;return Wr=new Map,e}function Zy(){return Wr.size===0}function Ky(e){return typeof e=="string"?e:e.text()}function Jy(e){Yy.delete(e)}function Xy(e){return Object.getPrototypeOf(e.prototype).constructor}function ev(e){let t=Xy(e.type),n=!0,r=[e];for(;t;){let o;if(Be(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new C(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Dr(e.inputs),s.inputTransforms=Dr(e.inputTransforms),s.declaredInputs=Dr(e.declaredInputs),s.outputs=Dr(e.outputs);let a=o.hostBindings;a&&iv(e,a);let u=o.viewQuery,c=o.contentQueries;if(u&&rv(e,u),c&&ov(e,c),tv(e,o),wp(e.outputs,o.outputs),Be(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===ev&&(n=!1)}}t=Object.getPrototypeOf(t)}nv(r)}function tv(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=t.inputTransforms[o]}}}function nv(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=wn(o.hostAttrs,n=wn(n,o.hostAttrs))}}function Dr(e){return e===Yt?{}:e===ie?[]:e}function rv(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function ov(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function iv(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function sv(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}var et=class{},us=class{};var zr=class extends et{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Ur(this);let o=Vc(t);this._bootstrapComponents=Bl(o.bootstrap),this._r3Injector=Cl(t,n,[{provide:et,useValue:this},{provide:vo,useValue:this.componentFactoryResolver},...r],te(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},qr=class extends us{constructor(t){super(),this.moduleType=t}create(t){return new zr(this.moduleType,t,[])}};function av(e,t,n){return new zr(e,t,n)}var cs=class extends et{constructor(t){super(),this.componentFactoryResolver=new Ur(this),this.instance=null;let n=new In([...t.providers,{provide:et,useValue:this},{provide:vo,useValue:this.componentFactoryResolver}],t.parent||js(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function uv(e,t,n=null){return new cs({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var bd=(()=>{let t=class t{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new cn(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let r=this.taskId++;return this.pendingTasks.add(r),r}remove(r){this.pendingTasks.delete(r),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function Md(e){return Da(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function cv(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Da(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function wa(e,t,n){return e[t]=n}function lv(e,t){return e[t]}function se(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Nn(e,t,n,r){let o=se(e,t,n);return se(e,t+1,r)||o}function dv(e,t,n,r,o){let i=Nn(e,t,n,r);return se(e,t+2,o)||i}function fv(e,t,n,r,o,i){let s=Nn(e,t,n,r);return Nn(e,t+2,o,i)||s}function pv(e){return(e.flags&32)===32}function hv(e,t,n,r,o,i,s,a,u){let c=t.consts,l=an(t,e,4,s||null,Xt(c,a));fa(t,n,l,Xt(c,u)),so(t,l);let d=l.tView=da(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function ls(e,t,n,r,o,i,s,a){let u=m(),c=V(),l=e+Y,d=c.firstCreatePass?hv(l,c,u,t,n,r,o,i,s):c.data[l];_t(d,!1);let f=gv(c,u,d,e);oo()&&co(c,u,f,d),Xe(f,u);let p=od(f,u,f,d);return u[l]=p,po(u,p),Ry(p,d,u),ro(d)&&ca(c,u,d),s!=null&&la(u,d,a),ls}var gv=mv;function mv(e,t,n,r){return io(!0),t[j].createComment("")}function yv(e,t,n,r){let o=m(),i=nt();if(se(o,i,t)){let s=V(),a=on();Hm(a,o,e,t,n,r)}return yv}function Ia(e,t,n,r){return se(e,nt(),n)?t+ce(n)+r:Z}function vv(e,t,n,r,o,i){let s=Qs(),a=Nn(e,s,n,o);return Ln(2),a?t+ce(n)+r+ce(o)+i:Z}function Dv(e,t,n,r,o,i,s,a){let u=Qs(),c=dv(e,u,n,o,s);return Ln(3),c?t+ce(n)+r+ce(o)+i+ce(s)+a:Z}function wv(e,t,n,r,o,i,s,a,u,c){let l=Qs(),d=fv(e,l,n,o,s,u);return Ln(4),d?t+ce(n)+r+ce(o)+i+ce(s)+a+ce(u)+c:Z}function wr(e,t){return e<<17|t<<2}function Mt(e){return e>>17&32767}function Iv(e){return(e&2)==2}function Ev(e,t){return e&131071|t<<17}function ds(e){return e|2}function tn(e){return(e&131068)>>2}function fi(e,t){return e&-131069|t<<2}function Cv(e){return(e&1)===1}function fs(e){return e|1}function bv(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Mt(s),u=tn(s);e[r]=n;let c=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||Fn(d,l)>0)&&(c=!0)}else l=n;if(o)if(u!==0){let f=Mt(e[a+1]);e[r+1]=wr(f,a),f!==0&&(e[f+1]=fi(e[f+1],r)),e[a+1]=Ev(e[a+1],r)}else e[r+1]=wr(a,0),a!==0&&(e[a+1]=fi(e[a+1],r)),a=r;else e[r+1]=wr(u,0),a===0?a=r:e[u+1]=fi(e[u+1],r),u=r;c&&(e[r+1]=ds(e[r+1])),sc(e,l,r,!0),sc(e,l,r,!1),Mv(t,l,e,r,i),s=wr(a,u),i?t.classBindings=s:t.styleBindings=s}function Mv(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Fn(i,t)>=0&&(n[r+1]=fs(n[r+1]))}function sc(e,t,n,r){let o=e[n+1],i=t===null,s=r?Mt(o):tn(o),a=!1;for(;s!==0&&(a===!1||i);){let u=e[s],c=e[s+1];_v(u,t)&&(a=!0,e[s+1]=r?fs(c):ds(c)),s=r?Mt(c):tn(c)}a&&(e[n+1]=r?ds(o):fs(o))}function _v(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Fn(e,t)>=0:!1}var be={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function xv(e){return e.substring(be.key,be.keyEnd)}function Tv(e){return Sv(e),_d(e,xd(e,0,be.textEnd))}function _d(e,t){let n=be.textEnd;return n===t?-1:(t=be.keyEnd=Nv(e,be.key=t,n),xd(e,t,n))}function Sv(e){be.key=0,be.keyEnd=0,be.value=0,be.valueEnd=0,be.textEnd=e.length}function xd(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function Nv(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function Av(e,t,n){let r=m(),o=nt();if(se(r,o,t)){let i=V(),s=on();Bn(i,s,r,e,t,r[j],n,!1)}return Av}function ps(e,t,n,r,o){let i=t.inputs,s=o?"class":"style";pa(e,n,i[s],s,r)}function Td(e,t,n){return Nd(e,t,n,!1),Td}function Ov(e,t){return Nd(e,t,null,!0),Ov}function vT(e){Ad(jv,Sd,e,!0)}function Sd(e,t){for(let n=Tv(t);n>=0;n=_d(t,n))eo(e,xv(t),!0)}function Nd(e,t,n,r){let o=m(),i=V(),s=Ln(2);if(i.firstUpdatePass&&Fd(i,e,s,r),t!==Z&&se(o,s,t)){let a=i.data[Te()];Rd(i,a,o,o[j],e,o[s+1]=Bv(t,n),r,s)}}function Ad(e,t,n,r){let o=V(),i=Ln(2);o.firstUpdatePass&&Fd(o,null,i,r);let s=m();if(n!==Z&&se(s,i,n)){let a=o.data[Te()];if(Pd(a,r)&&!Od(o,i)){let u=r?a.classesWithoutHost:a.stylesWithoutHost;u!==null&&(n=vi(u,n||"")),ps(o,a,s,n,r)}else Vv(o,a,s,s[j],s[i+1],s[i+1]=Lv(e,t,n),r,i)}}function Od(e,t){return t>=e.expandoStartIndex}function Fd(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Te()],s=Od(e,n);Pd(i,r)&&t===null&&!s&&(t=!1),t=Fv(o,i,t,r),bv(o,i,t,n,s,r)}}function Fv(e,t,n,r){let o=Zs(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=pi(null,e,t,n,r),n=An(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=pi(o,e,t,n,r),i===null){let u=Rv(e,t,r);u!==void 0&&Array.isArray(u)&&(u=pi(null,e,t,u[1],r),u=An(u,t.attrs,r),Pv(e,t,r,u))}else i=kv(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function Rv(e,t,n){let r=n?t.classBindings:t.styleBindings;if(tn(r)!==0)return e[Mt(r)]}function Pv(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Mt(o)]=r}function kv(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=An(r,s,n)}return An(r,t.attrs,n)}function pi(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=An(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function An(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),eo(e,s,n?!0:t[++i]))}return e===void 0?null:e}function Lv(e,t,n){if(n==null||n==="")return ie;let r=[],o=jn(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function jv(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&eo(e,r,n)}function Vv(e,t,n,r,o,i,s,a){o===Z&&(o=ie);let u=0,c=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let f=u<o.length?o[u+1]:void 0,p=c<i.length?i[c+1]:void 0,h=null,y;l===d?(u+=2,c+=2,f!==p&&(h=d,y=p)):d===null||l!==null&&l<d?(u+=2,h=l):(c+=2,h=d,y=p),h!==null&&Rd(e,t,n,r,h,y,s,a),l=u<o.length?o[u]:null,d=c<i.length?i[c]:null}}function Rd(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let u=e.data,c=u[a+1],l=Cv(c)?ac(u,t,n,o,tn(c),s):void 0;if(!Yr(l)){Yr(i)||Iv(c)&&(i=ac(u,null,n,o,a,s));let d=el(Te(),n);Dm(r,s,d,o,i)}}function ac(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let u=e[o],c=Array.isArray(u),l=c?u[1]:u,d=l===null,f=n[o+1];f===Z&&(f=d?ie:void 0);let p=d?ni(f,r):l===r?f:void 0;if(c&&!Yr(p)&&(p=ni(u,r)),Yr(p)&&(a=p,s))return a;let h=e[o+1];o=s?Mt(h):tn(h)}if(t!==null){let u=i?t.residualClasses:t.residualStyles;u!=null&&(a=ni(u,r))}return a}function Yr(e){return e!==void 0}function Bv(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=te(jn(e)))),e}function Pd(e,t){return(e.flags&(t?8:16))!==0}function DT(e,t,n){let r=m(),o=Ia(r,e,t,n);Ad(eo,Sd,o,!0)}var hs=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function hi(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function $v(e,t,n){let r,o,i=0,s=e.length-1;if(Array.isArray(t)){let a=t.length-1;for(;i<=s&&i<=a;){let u=e.at(i),c=t[i],l=hi(i,u,i,c,n);if(l!==0){l<0&&e.updateValue(i,c),i++;continue}let d=e.at(s),f=t[a],p=hi(s,d,a,f,n);if(p!==0){p<0&&e.updateValue(s,f),s--,a--;continue}let h=n(i,u),y=n(s,d),T=n(i,c);if(Object.is(T,y)){let M=n(a,f);Object.is(M,h)?(e.swap(i,s),e.updateValue(s,f),a--,s--):e.move(s,i),e.updateValue(i,c),i++;continue}if(r??=new Qr,o??=cc(e,i,s,n),gs(e,r,i,T))e.updateValue(i,c),i++,s++;else if(o.has(T))r.set(h,e.detach(i)),s--;else{let M=e.create(i,t[i]);e.attach(i,M),i++,s++}}for(;i<=a;)uc(e,r,n,i,t[i]),i++}else if(t!=null){let a=t[Symbol.iterator](),u=a.next();for(;!u.done&&i<=s;){let c=e.at(i),l=u.value,d=hi(i,c,i,l,n);if(d!==0)d<0&&e.updateValue(i,l),i++,u=a.next();else{r??=new Qr,o??=cc(e,i,s,n);let f=n(i,l);if(gs(e,r,i,f))e.updateValue(i,l),i++,s++,u=a.next();else if(!o.has(f))e.attach(i,e.create(i,l)),i++,s++,u=a.next();else{let p=n(i,c);r.set(p,e.detach(i)),s--}}}for(;!u.done;)uc(e,r,n,e.length,u.value),u=a.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(a=>{e.destroy(a)})}function gs(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function uc(e,t,n,r,o){if(gs(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function cc(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Qr=class{constructor(){this.kvMap=new Map,this._vMap=void 0}has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function wT(e,t,n){$n("NgControlFlow");let r=m(),o=nt(),i=Ds(r,Y+e),s=0;if(se(r,o,t)){let a=b(null);try{if(dd(i,s),t!==-1){let u=ws(r[I],Y+t),c=Sn(i,u.tView.ssrId),l=go(r,u,n,{dehydratedView:c});mo(i,l,s,Tn(u,c))}}finally{b(a)}}else{let a=ld(i,s);a!==void 0&&(a[K]=n)}}var ms=class{constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-J}};function IT(e,t){return t}var ys=class{constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function ET(e,t,n,r,o,i,s,a,u,c,l,d,f){$n("NgControlFlow");let p=u!==void 0,h=m(),y=a?s.bind(h[me][K]):s,T=new ys(p,y);h[Y+e]=T,ls(e+1,t,n,r,o,i),p&&ls(e+2,u,c,l,d,f)}var vs=class extends hs{constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r,this.needsIndexUpdate=!1}get length(){return this.lContainer.length-J}at(t){return this.getLView(t)[K].$implicit}attach(t,n){let r=n[Zt];this.needsIndexUpdate||=t!==this.length,mo(this.lContainer,n,t,Tn(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,Hv(this.lContainer,t)}create(t,n){let r=Sn(this.lContainer,this.templateTNode.tView.ssrId);return go(this.hostLView,this.templateTNode,new ms(this.lContainer,n,t),{dehydratedView:r})}destroy(t){uo(t[I],t)}updateValue(t,n){this.getLView(t)[K].$implicit=n}reset(){this.needsIndexUpdate=!1}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[K].$index=t}getLView(t){return Uv(this.lContainer,t)}};function CT(e){let t=b(null),n=Te();try{let r=m(),o=r[I],i=r[n];if(i.liveCollection===void 0){let a=n+1,u=Ds(r,a),c=ws(o,a);i.liveCollection=new vs(u,r,c)}else i.liveCollection.reset();let s=i.liveCollection;if($v(s,e,i.trackByFn),s.updateIndexes(),i.hasEmptyBlock){let a=nt(),u=s.length===0;if(se(r,a,u)){let c=n+2,l=Ds(r,c);if(u){let d=ws(o,c),f=Sn(l,d.tView.ssrId),p=go(r,d,void 0,{dehydratedView:f});mo(l,p,0,Tn(d,f))}else dd(l,0)}}}finally{b(t)}}function Ds(e,t){return e[t]}function Hv(e,t){return xn(e,t)}function Uv(e,t){return ld(e,t)}function ws(e,t){return Us(e,t)}function Gv(e,t,n,r,o,i){let s=t.consts,a=Xt(s,o),u=an(t,e,2,r,a);return fa(t,n,u,Xt(s,i)),u.attrs!==null&&Hr(u,u.attrs,!1),u.mergedAttrs!==null&&Hr(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function kd(e,t,n,r){let o=m(),i=V(),s=Y+e,a=o[j],u=i.firstCreatePass?Gv(s,i,o,t,n,r):i.data[s],c=zv(i,o,u,a,t,e);o[s]=c;let l=ro(u);return _t(u,!0),Zl(a,c,u),!pv(u)&&oo()&&co(i,o,c,u),Ph()===0&&Xe(c,o),kh(),l&&(ca(i,o,u),ua(i,u,o)),r!==null&&la(o,u),kd}function Ld(){let e=Q();qs()?Ys():(e=e.parent,_t(e,!1));let t=e;jh(t)&&Vh(),Lh();let n=V();return n.firstCreatePass&&(so(n,e),$s(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&Xh(t)&&ps(n,t,m(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&eg(t)&&ps(n,t,m(),t.stylesWithoutHost,!1),Ld}function Wv(e,t,n,r){return kd(e,t,n,r),Ld(),Wv}var zv=(e,t,n,r,o,i)=>(io(!0),$l(r,o,Qh()));function qv(e,t,n,r,o){let i=t.consts,s=Xt(i,r),a=an(t,e,8,"ng-container",s);s!==null&&Hr(a,s,!0);let u=Xt(i,o);return fa(t,n,a,u),t.queries!==null&&t.queries.elementStart(t,a),a}function jd(e,t,n){let r=m(),o=V(),i=e+Y,s=o.firstCreatePass?qv(i,o,r,t,n):o.data[i];_t(s,!0);let a=Qv(o,r,s,e);return r[i]=a,oo()&&co(o,r,a,s),Xe(a,r),ro(s)&&(ca(o,r,s),ua(o,s,r)),n!=null&&la(r,s),jd}function Vd(){let e=Q(),t=V();return qs()?Ys():(e=e.parent,_t(e,!1)),t.firstCreatePass&&(so(t,e),$s(e)&&t.queries.elementEnd(e)),Vd}function Yv(e,t,n){return jd(e,t,n),Vd(),Yv}var Qv=(e,t,n,r)=>(io(!0),rm(t[j],""));function bT(){return m()}function Zv(e,t,n){let r=m(),o=nt();if(se(r,o,t)){let i=V(),s=on();Bn(i,s,r,e,t,r[j],n,!0)}return Zv}function Kv(e,t,n){let r=m(),o=nt();if(se(r,o,t)){let i=V(),s=on(),a=Zs(i.data),u=ud(a,s,r);Bn(i,s,r,e,t,u,n,!0)}return Kv}var pt=void 0;function Jv(e){let t=e,n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var Xv=["en",[["a","p"],["AM","PM"],pt],[["AM","PM"],pt,pt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],pt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],pt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",pt,"{1} 'at' {0}",pt],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",Jv],gi={};function fe(e){let t=eD(e),n=lc(t);if(n)return n;let r=t.split("-")[0];if(n=lc(r),n)return n;if(r==="en")return Xv;throw new C(701,!1)}function lc(e){return e in gi||(gi[e]=ue.ng&&ue.ng.common&&ue.ng.common.locales&&ue.ng.common.locales[e]),gi[e]}var B=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(B||{});function eD(e){return e.toLowerCase().replace(/_/g,"-")}var Zr="en-US";var tD=Zr;function nD(e){typeof e=="string"&&(tD=e.toLowerCase().replace(/_/g,"-"))}function rD(e,t,n,r){let o=m(),i=V(),s=Q();return Ea(i,o,o[j],s,e,t,r),rD}function oD(e,t){let n=Q(),r=m(),o=V(),i=Zs(o.data),s=ud(i,n,r);return Ea(o,r,s,n,e,t),oD}function iD(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[En],u=o[i+2];return a.length>u?a[u]:null}typeof s=="string"&&(i+=2)}return null}function Ea(e,t,n,r,o,i,s){let a=ro(r),c=e.firstCreatePass&&ad(e),l=t[K],d=sd(t),f=!0;if(r.type&3||s){let y=ye(r,t),T=s?s(y):y,M=d.length,W=s?ae=>s(Pe(ae[r.index])):r.index,z=null;if(!s&&a&&(z=iD(e,t,o,r.index)),z!==null){let ae=z.__ngLastListenerFn__||z;ae.__ngNextListenerFn__=i,z.__ngLastListenerFn__=i,f=!1}else{i=fc(r,t,l,i,!1);let ae=n.listen(T,o,i);d.push(i,ae),c&&c.push(o,W,M,M+1)}}else i=fc(r,t,l,i,!1);let p=r.outputs,h;if(f&&p!==null&&(h=p[o])){let y=h.length;if(y)for(let T=0;T<y;T+=2){let M=h[T],W=h[T+1],Le=t[M][W].subscribe(i),De=d.length;d.push(i,Le),c&&c.push(o,r.index,De,-(De+1))}}}function dc(e,t,n,r){let o=b(null);try{return Fe(6,t,n),n(r)!==!1}catch(i){return cd(e,i),!1}finally{Fe(7,t,n),b(o)}}function fc(e,t,n,r,o){return function i(s){if(s===Function)return r;let a=e.componentOffset>-1?tt(e.index,t):t;ga(a);let u=dc(t,n,r,s),c=i.__ngNextListenerFn__;for(;c;)u=dc(t,n,c,s)&&u,c=c.__ngNextListenerFn__;return o&&u===!1&&s.preventDefault(),u}}function MT(e=1){return qh(e)}function sD(e,t){let n=null,r=Zp(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?kc(e,i,!0):Xp(r,i))return o}return n}function _T(e){let t=m()[me][de];if(!t.projection){let n=e?e.length:1,r=t.projection=$p(n,null),o=r.slice(),i=t.child;for(;i!==null;){let s=e?sD(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i),i=i.next}}}function xT(e,t=0,n){let r=m(),o=V(),i=an(o,Y+e,16,null,n||null);i.projection===null&&(i.projection=t),Ys(),(!r[Zt]||ol())&&(i.flags&32)!==32&&ym(o,r,i)}function aD(e,t,n){return Bd(e,"",t,"",n),aD}function Bd(e,t,n,r,o){let i=m(),s=Ia(i,t,n,r);if(s!==Z){let a=V(),u=on();Bn(a,u,i,e,s,i[j],o,!1)}return Bd}function TT(e,t,n,r){Hy(e,t,n,r)}function ST(e,t,n){$y(e,t,n)}function NT(e){let t=m(),n=V(),r=sl();Ks(r+1);let o=va(n,r);if(e.dirty&&Nh(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Wy(t,r);e.reset(i,hg),e.notifyOnChanges()}return!0}return!1}function AT(){return By(m(),sl())}function uD(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function OT(e){let t=$h();return Gs(t,Y+e)}function FT(e,t=""){let n=m(),r=V(),o=e+Y,i=r.firstCreatePass?an(r,o,1,t,null):r.data[o],s=cD(r,n,i,t,e);n[o]=s,oo()&&co(r,n,s,i),_t(i,!1)}var cD=(e,t,n,r,o)=>(io(!0),tm(t[j],r));function lD(e){return $d("",e,""),lD}function $d(e,t,n){let r=m(),o=Ia(r,e,t,n);return o!==Z&&ho(r,Te(),o),$d}function dD(e,t,n,r,o){let i=m(),s=vv(i,e,t,n,r,o);return s!==Z&&ho(i,Te(),s),dD}function fD(e,t,n,r,o,i,s){let a=m(),u=Dv(a,e,t,n,r,o,i,s);return u!==Z&&ho(a,Te(),u),fD}function pD(e,t,n,r,o,i,s,a,u){let c=m(),l=wv(c,e,t,n,r,o,i,s,a,u);return l!==Z&&ho(c,Te(),l),pD}function hD(e,t,n){Cd(t)&&(t=t());let r=m(),o=nt();if(se(r,o,t)){let i=V(),s=on();Bn(i,s,r,e,t,r[j],n,!1)}return hD}function RT(e,t){let n=Cd(e);return n&&e.set(t),n}function gD(e,t){let n=m(),r=V(),o=Q();return Ea(r,n,n[j],o,e,t),gD}function mD(e,t,n){let r=V();if(r.firstCreatePass){let o=Be(e);Is(n,r.data,r.blueprint,o,!0),Is(t,r.data,r.blueprint,o,!1)}}function Is(e,t,n,r,o){if(e=ee(e),Array.isArray(e))for(let i=0;i<e.length;i++)Is(e[i],t,n,r,o);else{let i=V(),s=m(),a=Q(),u=Qt(e)?e:ee(e.provide),c=zc(e),l=a.providerIndexes&1048575,d=a.directiveStart,f=a.providerIndexes>>20;if(Qt(e)||!e.multi){let p=new wt(c,o,U),h=yi(u,t,o?l:l+f,d);h===-1?(Ni(Lr(a,s),i,u),mi(i,e,t.length),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(p),s.push(p)):(n[h]=p,s[h]=p)}else{let p=yi(u,t,l+f,d),h=yi(u,t,l,l+f),y=p>=0&&n[p],T=h>=0&&n[h];if(o&&!T||!o&&!y){Ni(Lr(a,s),i,u);let M=DD(o?vD:yD,n.length,o,r,c);!o&&T&&(n[h].providerFactory=M),mi(i,e,t.length,0),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(M),s.push(M)}else{let M=Hd(n[o?h:p],c,!o&&r);mi(i,e,p>-1?p:h,M)}!o&&r&&T&&n[h].componentProviders++}}}function mi(e,t,n,r){let o=Qt(t),i=dh(t);if(o||i){let u=(i?ee(t.useClass):t).prototype.ngOnDestroy;if(u){let c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=c.indexOf(n);l===-1?c.push(n,[r,u]):c[l+1].push(r,u)}else c.push(n,u)}}}function Hd(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function yi(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function yD(e,t,n,r){return Es(this.multi,[])}function vD(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=It(n,n[I],this.providerFactory.index,r);i=a.slice(0,s),Es(o,i);for(let u=s;u<a.length;u++)i.push(a[u])}else i=[],Es(o,i);return i}function Es(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function DD(e,t,n,r,o){let i=new wt(e,n,U);return i.multi=[],i.index=t,i.componentProviders=0,Hd(i,o,r&&!n),i}function PT(e,t=[]){return n=>{n.providersResolver=(r,o)=>mD(r,o?o(e):e,t)}}var wD=(()=>{let t=class t{constructor(r){this._injector=r,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(r){if(!r.standalone)return null;if(!this.cachedInjectors.has(r)){let o=Hc(!1,r.type),i=o.length>0?uv([o],this._injector,`Standalone[${r.type.name}]`):null;this.cachedInjectors.set(r,i)}return this.cachedInjectors.get(r)}ngOnDestroy(){try{for(let r of this.cachedInjectors.values())r!==null&&r.destroy()}finally{this.cachedInjectors.clear()}}};t.\u0275prov=k({token:t,providedIn:"environment",factory:()=>new t(H(Je))});let e=t;return e})();function kT(e){$n("NgStandalone"),e.getStandaloneInjector=t=>t.get(wD).getOrCreateStandaloneInjector(e)}function LT(e,t,n){let r=kn()+e,o=m();return o[r]===Z?wa(o,r,n?t.call(n):t()):lv(o,r)}function jT(e,t,n,r){return Gd(m(),kn(),e,t,n,r)}function VT(e,t,n,r,o){return Wd(m(),kn(),e,t,n,r,o)}function Ud(e,t){let n=e[t];return n===Z?void 0:n}function Gd(e,t,n,r,o,i){let s=t+n;return se(e,s,o)?wa(e,s+1,i?r.call(i,o):r(o)):Ud(e,s+1)}function Wd(e,t,n,r,o,i,s){let a=t+n;return Nn(e,a,o,i)?wa(e,a+2,s?r.call(s,o,i):r(o,i)):Ud(e,a+2)}function BT(e,t){let n=V(),r,o=e+Y;n.firstCreatePass?(r=ID(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=mt(r.type,!0)),s,a=oe(U);try{let u=kr(!1),c=i();return kr(u),uD(n,m(),o,c),c}finally{oe(a)}}function ID(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function $T(e,t,n){let r=e+Y,o=m(),i=Gs(o,r);return zd(o,r)?Gd(o,kn(),t,i.transform,n,i):i.transform(n)}function HT(e,t,n,r){let o=e+Y,i=m(),s=Gs(i,o);return zd(i,o)?Wd(i,kn(),t,s.transform,n,r,s):s.transform(n,r)}function zd(e,t){return e[I].data[t].pure}function UT(e,t){return yo(e,t)}var Ir=null;function ED(e){Ir!==null&&(e.defaultEncapsulation!==Ir.defaultEncapsulation||e.preserveWhitespaces!==Ir.preserveWhitespaces)||(Ir=e)}var GT=(()=>{let t=class t{log(r){console.log(r)}warn(r){console.warn(r)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"platform"});let e=t;return e})();var CD=new O(""),bD=new O(""),WT=(()=>{let t=class t{constructor(r,o,i){this._ngZone=r,this.registry=o,this._pendingCount=0,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,Ca||(_D(i),i.addToWindow(o)),this._watchAngularEvents(),r.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{le.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&this._pendingCount===0&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let r=this._callbacks.pop();clearTimeout(r.timeoutId),r.doneCb()}});else{let r=this.getPendingTasks();this._callbacks=this._callbacks.filter(o=>o.updateCb&&o.updateCb(r)?(clearTimeout(o.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(r=>({source:r.source,creationLocation:r.creationLocation,data:r.data})):[]}addCallback(r,o,i){let s=-1;o&&o>0&&(s=setTimeout(()=>{this._callbacks=this._callbacks.filter(a=>a.timeoutId!==s),r()},o)),this._callbacks.push({doneCb:r,timeoutId:s,updateCb:i})}whenStable(r,o,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(r,o,i),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(r){this.registry.registerApplication(r,this)}unregisterApplication(r){this.registry.unregisterApplication(r)}findProviders(r,o,i){return[]}};t.\u0275fac=function(o){return new(o||t)(H(le),H(MD),H(bD))},t.\u0275prov=k({token:t,factory:t.\u0275fac});let e=t;return e})(),MD=(()=>{let t=class t{constructor(){this._applications=new Map}registerApplication(r,o){this._applications.set(r,o)}unregisterApplication(r){this._applications.delete(r)}unregisterAllApplications(){this._applications.clear()}getTestability(r){return this._applications.get(r)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(r,o=!0){return Ca?.findTestabilityInTree(this,r,o)??null}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"platform"});let e=t;return e})();function _D(e){Ca=e}var Ca;function wo(e){return!!e&&typeof e.then=="function"}function ba(e){return!!e&&typeof e.subscribe=="function"}var xD=new O(""),qd=(()=>{let t=class t{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((r,o)=>{this.resolve=r,this.reject=o}),this.appInits=F(xD,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let r=[];for(let i of this.appInits){let s=i();if(wo(s))r.push(s);else if(ba(s)){let a=new Promise((u,c)=>{s.subscribe({complete:u,error:c})});r.push(a)}}let o=()=>{this.done=!0,this.resolve()};Promise.all(r).then(()=>{o()}).catch(i=>{this.reject(i)}),r.length===0&&o(),this.initialized=!0}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),TD=new O("");function SD(){Ka(()=>{throw new C(600,!1)})}function ND(e){return e.isBoundToModule}function AD(e,t,n){try{let r=n();return wo(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}function Yd(e,t){return Array.isArray(t)?t.reduce(Yd,e):ot(ot({},e),t)}var Ma=(()=>{let t=class t{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=F(bl),this.afterRenderEffectManager=F(ya),this.externalTestViews=new Set,this.beforeRender=new ge,this.afterTick=new ge,this.componentTypes=[],this.components=[],this.isStable=F(bd).hasPendingTasks.pipe(Oe(r=>!r)),this._injector=F(Je)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(r,o){let i=r instanceof $r;if(!this._injector.get(qd).done){let p=!i&&ih(r),h=!1;throw new C(405,h)}let a;i?a=r:a=this._injector.get(vo).resolveComponentFactory(r),this.componentTypes.push(a.componentType);let u=ND(a)?void 0:this._injector.get(et),c=o||a.selector,l=a.create(xt.NULL,[],c,u),d=l.location.nativeElement,f=l.injector.get(CD,null);return f?.registerApplication(d),l.onDestroy(()=>{this.detachView(l.hostView),xr(this.components,l),f?.unregisterApplication(d)}),this._loadComponent(l),l}tick(){this._tick(!0)}_tick(r){if(this._runningTick)throw new C(101,!1);let o=b(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(r)}catch(i){this.internalErrorHandler(i)}finally{this.afterTick.next(),this._runningTick=!1,b(o)}}detectChangesInAttachedViews(r){let o=0,i=this.afterRenderEffectManager;for(;;){if(o===pd)throw new C(103,!1);if(r){let s=o===0;this.beforeRender.next(s);for(let{_lView:a,notifyErrorHandler:u}of this._views)OD(a,s,u)}if(o++,i.executeInternalCallbacks(),![...this.externalTestViews.keys(),...this._views].some(({_lView:s})=>Cs(s))&&(i.execute(),![...this.externalTestViews.keys(),...this._views].some(({_lView:s})=>Cs(s))))break}}attachView(r){let o=r;this._views.push(o),o.attachToAppRef(this)}detachView(r){let o=r;xr(this._views,o),o.detachFromAppRef()}_loadComponent(r){this.attachView(r.hostView),this.tick(),this.components.push(r);let o=this._injector.get(TD,[]);[...this._bootstrapListeners,...o].forEach(i=>i(r))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(r=>r()),this._views.slice().forEach(r=>r.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(r){return this._destroyListeners.push(r),()=>xr(this._destroyListeners,r)}destroy(){if(this._destroyed)throw new C(406,!1);let r=this._injector;r.destroy&&!r.destroyed&&r.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function xr(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function OD(e,t,n){!t&&!Cs(e)||FD(e,n,t)}function Cs(e){return zs(e)}function FD(e,t,n){let r;n?(r=0,e[D]|=1024):e[D]&64?r=0:r=1,hd(e,t,r)}var bs=class{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},zT=(()=>{let t=class t{compileModuleSync(r){return new qr(r)}compileModuleAsync(r){return Promise.resolve(this.compileModuleSync(r))}compileModuleAndAllComponentsSync(r){let o=this.compileModuleSync(r),i=Vc(r),s=Bl(i.declarations).reduce((a,u)=>{let c=Ke(u);return c&&a.push(new en(c)),a},[]);return new bs(o,s)}compileModuleAndAllComponentsAsync(r){return Promise.resolve(this.compileModuleAndAllComponentsSync(r))}clearCache(){}clearCacheFor(r){}getModuleId(r){}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),RD=new O("");function PD(e,t,n){let r=new qr(n);return Promise.resolve(r)}function pc(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var kD=(()=>{let t=class t{constructor(){this.zone=F(le),this.applicationRef=F(Ma)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function LD(e){return[{provide:le,useFactory:e},{provide:Ar,multi:!0,useFactory:()=>{let t=F(kD,{optional:!0});return()=>t.initialize()}},{provide:Ar,multi:!0,useFactory:()=>{let t=F(BD);return()=>{t.initialize()}}},{provide:bl,useFactory:jD}]}function jD(){let e=F(le),t=F(Et);return n=>e.runOutsideAngular(()=>t.handleError(n))}function VD(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var BD=(()=>{let t=class t{constructor(){this.subscription=new $,this.initialized=!1,this.zone=F(le),this.pendingTasks=F(bd)}initialize(){if(this.initialized)return;this.initialized=!0;let r=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(r=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{le.assertNotInAngularZone(),queueMicrotask(()=>{r!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(r),r=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{le.assertInAngularZone(),r??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function $D(){return typeof $localize<"u"&&$localize.locale||Zr}var Hn=new O("",{providedIn:"root",factory:()=>F(Hn,x.Optional|x.SkipSelf)||$D()});var Qd=new O(""),Zd=(()=>{let t=class t{constructor(r){this._injector=r,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(r,o){let i=vy(o?.ngZone,VD({eventCoalescing:o?.ngZoneEventCoalescing,runCoalescing:o?.ngZoneRunCoalescing}));return i.run(()=>{let s=av(r.moduleType,this.injector,LD(()=>i)),a=s.injector.get(Et,null);return i.runOutsideAngular(()=>{let u=i.onError.subscribe({next:c=>{a.handleError(c)}});s.onDestroy(()=>{xr(this._modules,s),u.unsubscribe()})}),AD(a,i,()=>{let u=s.injector.get(qd);return u.runInitializers(),u.donePromise.then(()=>{let c=s.injector.get(Hn,Zr);return nD(c||Zr),this._moduleDoBootstrap(s),s})})})}bootstrapModule(r,o=[]){let i=Yd({},o);return PD(this.injector,i,r).then(s=>this.bootstrapModuleFactory(s,i))}_moduleDoBootstrap(r){let o=r.injector.get(Ma);if(r._bootstrapComponents.length>0)r._bootstrapComponents.forEach(i=>o.bootstrap(i));else if(r.instance.ngDoBootstrap)r.instance.ngDoBootstrap(o);else throw new C(-403,!1);this._modules.push(r)}onDestroy(r){this._destroyListeners.push(r)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new C(404,!1);this._modules.slice().forEach(o=>o.destroy()),this._destroyListeners.forEach(o=>o());let r=this._injector.get(Qd,null);r&&(r.forEach(o=>o()),r.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}};t.\u0275fac=function(o){return new(o||t)(H(xt))},t.\u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"platform"});let e=t;return e})(),yn=null,Kd=new O("");function HD(e){if(yn&&!yn.get(Kd,!1))throw new C(400,!1);SD(),yn=e;let t=e.get(Zd);return zD(e),t}function UD(e,t,n=[]){let r=`Platform: ${t}`,o=new O(r);return(i=[])=>{let s=Jd();if(!s||s.injector.get(Kd,!1)){let a=[...n,...i,{provide:o,useValue:!0}];e?e(a):HD(GD(a,r))}return WD(o)}}function GD(e=[],t){return xt.create({name:t,providers:[{provide:Wc,useValue:"platform"},{provide:Qd,useValue:new Set([()=>yn=null])},...e]})}function WD(e){let t=Jd();if(!t)throw new C(401,!1);return t}function Jd(){return yn?.get(Zd)??null}function zD(e){e.get(Eg,null)?.forEach(n=>n())}var _a=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=qD;let e=t;return e})();function qD(e){return YD(Q(),m(),(e&16)===16)}function YD(e,t,n){if(no(e)&&!n){let r=tt(e.index,t);return new Ct(r,r)}else if(e.type&47){let r=t[me];return new Ct(r,t)}return null}var Ms=class{constructor(){}supports(t){return Md(t)}create(t){return new _s(t)}},QD=(e,t)=>t,_s=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||QD}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<hc(r,o,i)?n:r,a=hc(s,o,i),u=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let c=a-o,l=u-o;if(c!=l){for(let f=0;f<c;f++){let p=f<i.length?i[f]:i[f]=0,h=p+f;l<=h&&h<c&&(i[f]=p+1)}let d=s.previousIndex;i[d]=l-c}}a!==u&&t(s,a,u)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Md(t))throw new C(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,cv(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new xs(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Kr),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Kr),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},xs=class{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},Ts=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Kr=class{constructor(){this.map=new Map}put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Ts,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function hc(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}var Ss=class{constructor(){}supports(t){return t instanceof Map||Da(t)}create(){return new Ns}},Ns=class{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(t){let n;for(n=this._mapHead;n!==null;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}diff(t){if(!t)t=new Map;else if(!(t instanceof Map||Da(t)))throw new C(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{let i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){let r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){let o=this._records.get(t);this._maybeAddToChanges(o,n);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new As(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;t!==null;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;t!=null;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){this._additionsHead===null?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){this._changesHead===null?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}},As=class{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}};function gc(){return new xa([new Ms])}var xa=(()=>{let t=class t{constructor(r){this.factories=r}static create(r,o){if(o!=null){let i=o.factories.slice();r=r.concat(i)}return new t(r)}static extend(r){return{provide:t,useFactory:o=>t.create(r,o||gc()),deps:[[t,new Sc,new Tc]]}}find(r){let o=this.factories.find(i=>i.supports(r));if(o!=null)return o;throw new C(901,!1)}};t.\u0275prov=k({token:t,providedIn:"root",factory:gc});let e=t;return e})();function mc(){return new Ta([new Ss])}var Ta=(()=>{let t=class t{constructor(r){this.factories=r}static create(r,o){if(o){let i=o.factories.slice();r=r.concat(i)}return new t(r)}static extend(r){return{provide:t,useFactory:o=>t.create(r,o||mc()),deps:[[t,new Sc,new Tc]]}}find(r){let o=this.factories.find(i=>i.supports(r));if(o)return o;throw new C(901,!1)}};t.\u0275prov=k({token:t,providedIn:"root",factory:mc});let e=t;return e})();var qT=UD(null,"core",[]),YT=(()=>{let t=class t{constructor(r){}};t.\u0275fac=function(o){return new(o||t)(H(Ma))},t.\u0275mod=ks({type:t}),t.\u0275inj=Os({});let e=t;return e})();function ZD(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function KD(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function Sa(e){let t=b(null);try{return e()}finally{b(t)}}function QT(e){let t=Ke(e);if(!t)return null;let n=new en(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var cf=null;function Na(){return cf}function gS(e){cf??=e}var Xd=class{};var Ua=new O(""),Ga=(()=>{let t=class t{historyGo(r){throw new Error("")}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:()=>F(XD),providedIn:"platform"});let e=t;return e})(),mS=new O(""),XD=(()=>{let t=class t extends Ga{constructor(){super(),this._doc=F(Ua),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Na().getBaseHref(this._doc)}onPopState(r){let o=Na().getGlobalEventTarget(this._doc,"window");return o.addEventListener("popstate",r,!1),()=>o.removeEventListener("popstate",r)}onHashChange(r){let o=Na().getGlobalEventTarget(this._doc,"window");return o.addEventListener("hashchange",r,!1),()=>o.removeEventListener("hashchange",r)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(r){this._location.pathname=r}pushState(r,o,i){this._history.pushState(r,o,i)}replaceState(r,o,i){this._history.replaceState(r,o,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(r=0){this._history.go(r)}getState(){return this._history.state}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:()=>new t,providedIn:"platform"});let e=t;return e})();function Wa(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function ef(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function We(e){return e&&e[0]!=="?"?"?"+e:e}var Ao=(()=>{let t=class t{historyGo(r){throw new Error("")}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=k({token:t,factory:()=>F(ew),providedIn:"root"});let e=t;return e})(),lf=new O(""),ew=(()=>{let t=class t extends Ao{constructor(r,o){super(),this._platformLocation=r,this._removeListenerFns=[],this._baseHref=o??this._platformLocation.getBaseHrefFromDOM()??F(Ua).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(r){this._removeListenerFns.push(this._platformLocation.onPopState(r),this._platformLocation.onHashChange(r))}getBaseHref(){return this._baseHref}prepareExternalUrl(r){return Wa(this._baseHref,r)}path(r=!1){let o=this._platformLocation.pathname+We(this._platformLocation.search),i=this._platformLocation.hash;return i&&r?`${o}${i}`:o}pushState(r,o,i,s){let a=this.prepareExternalUrl(i+We(s));this._platformLocation.pushState(r,o,a)}replaceState(r,o,i,s){let a=this.prepareExternalUrl(i+We(s));this._platformLocation.replaceState(r,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(r=0){this._platformLocation.historyGo?.(r)}};t.\u0275fac=function(o){return new(o||t)(H(Ga),H(lf,8))},t.\u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),yS=(()=>{let t=class t extends Ao{constructor(r,o){super(),this._platformLocation=r,this._baseHref="",this._removeListenerFns=[],o!=null&&(this._baseHref=o)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(r){this._removeListenerFns.push(this._platformLocation.onPopState(r),this._platformLocation.onHashChange(r))}getBaseHref(){return this._baseHref}path(r=!1){let o=this._platformLocation.hash??"#";return o.length>0?o.substring(1):o}prepareExternalUrl(r){let o=Wa(this._baseHref,r);return o.length>0?"#"+o:o}pushState(r,o,i,s){let a=this.prepareExternalUrl(i+We(s));a.length==0&&(a=this._platformLocation.pathname),this._platformLocation.pushState(r,o,a)}replaceState(r,o,i,s){let a=this.prepareExternalUrl(i+We(s));a.length==0&&(a=this._platformLocation.pathname),this._platformLocation.replaceState(r,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(r=0){this._platformLocation.historyGo?.(r)}};t.\u0275fac=function(o){return new(o||t)(H(Ga),H(lf,8))},t.\u0275prov=k({token:t,factory:t.\u0275fac});let e=t;return e})(),tw=(()=>{let t=class t{constructor(r){this._subject=new Me,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=r;let o=this._locationStrategy.getBaseHref();this._basePath=ow(ef(tf(o))),this._locationStrategy.onPopState(i=>{this._subject.emit({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(r=!1){return this.normalize(this._locationStrategy.path(r))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(r,o=""){return this.path()==this.normalize(r+We(o))}normalize(r){return t.stripTrailingSlash(rw(this._basePath,tf(r)))}prepareExternalUrl(r){return r&&r[0]!=="/"&&(r="/"+r),this._locationStrategy.prepareExternalUrl(r)}go(r,o="",i=null){this._locationStrategy.pushState(i,"",r,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(r+We(o)),i)}replaceState(r,o="",i=null){this._locationStrategy.replaceState(i,"",r,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(r+We(o)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(r=0){this._locationStrategy.historyGo?.(r)}onUrlChange(r){return this._urlChangeListeners.push(r),this._urlChangeSubscription??=this.subscribe(o=>{this._notifyUrlChangeListeners(o.url,o.state)}),()=>{let o=this._urlChangeListeners.indexOf(r);this._urlChangeListeners.splice(o,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(r="",o){this._urlChangeListeners.forEach(i=>i(r,o))}subscribe(r,o,i){return this._subject.subscribe({next:r,error:o,complete:i})}};t.normalizeQueryParams=We,t.joinWithSlash=Wa,t.stripTrailingSlash=ef,t.\u0275fac=function(o){return new(o||t)(H(Ao))},t.\u0275prov=k({token:t,factory:()=>nw(),providedIn:"root"});let e=t;return e})();function nw(){return new tw(H(Ao))}function rw(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function tf(e){return e.replace(/\/index.html$/,"")}function ow(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var df=function(e){return e[e.Decimal=0]="Decimal",e[e.Percent=1]="Percent",e[e.Currency=2]="Currency",e[e.Scientific=3]="Scientific",e}(df||{});var ne=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(ne||{}),P=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(P||{}),pe=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(pe||{}),he={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function iw(e){return fe(e)[B.LocaleId]}function sw(e,t,n){let r=fe(e),o=[r[B.DayPeriodsFormat],r[B.DayPeriodsStandalone]],i=ve(o,t);return ve(i,n)}function aw(e,t,n){let r=fe(e),o=[r[B.DaysFormat],r[B.DaysStandalone]],i=ve(o,t);return ve(i,n)}function uw(e,t,n){let r=fe(e),o=[r[B.MonthsFormat],r[B.MonthsStandalone]],i=ve(o,t);return ve(i,n)}function cw(e,t){let r=fe(e)[B.Eras];return ve(r,t)}function Io(e,t){let n=fe(e);return ve(n[B.DateFormat],t)}function Eo(e,t){let n=fe(e);return ve(n[B.TimeFormat],t)}function Co(e,t){let r=fe(e)[B.DateTimeFormat];return ve(r,t)}function ze(e,t){let n=fe(e),r=n[B.NumberSymbols][t];if(typeof r>"u"){if(t===he.CurrencyDecimal)return n[B.NumberSymbols][he.Decimal];if(t===he.CurrencyGroup)return n[B.NumberSymbols][he.Group]}return r}function lw(e,t){return fe(e)[B.NumberFormats][t]}function ff(e){if(!e[B.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[B.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function dw(e){let t=fe(e);return ff(t),(t[B.ExtraData][2]||[]).map(r=>typeof r=="string"?Aa(r):[Aa(r[0]),Aa(r[1])])}function fw(e,t,n){let r=fe(e);ff(r);let o=[r[B.ExtraData][0],r[B.ExtraData][1]],i=ve(o,t)||[];return ve(i,n)||[]}function ve(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function Aa(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var pw=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,bo={},hw=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,qe=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(qe||{}),A=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(A||{}),N=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(N||{});function gw(e,t,n,r){let o=bw(e);t=Ge(n,t)||t;let s=[],a;for(;t;)if(a=hw.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let u=o.getTimezoneOffset();r&&(u=hf(r,u),o=Cw(o,r,!0));let c="";return s.forEach(l=>{let d=Iw(l);c+=d?d(o,n,u):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function So(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Ge(e,t){let n=iw(e);if(bo[n]??={},bo[n][t])return bo[n][t];let r="";switch(t){case"shortDate":r=Io(e,pe.Short);break;case"mediumDate":r=Io(e,pe.Medium);break;case"longDate":r=Io(e,pe.Long);break;case"fullDate":r=Io(e,pe.Full);break;case"shortTime":r=Eo(e,pe.Short);break;case"mediumTime":r=Eo(e,pe.Medium);break;case"longTime":r=Eo(e,pe.Long);break;case"fullTime":r=Eo(e,pe.Full);break;case"short":let o=Ge(e,"shortTime"),i=Ge(e,"shortDate");r=Mo(Co(e,pe.Short),[o,i]);break;case"medium":let s=Ge(e,"mediumTime"),a=Ge(e,"mediumDate");r=Mo(Co(e,pe.Medium),[s,a]);break;case"long":let u=Ge(e,"longTime"),c=Ge(e,"longDate");r=Mo(Co(e,pe.Long),[u,c]);break;case"full":let l=Ge(e,"fullTime"),d=Ge(e,"fullDate");r=Mo(Co(e,pe.Full),[l,d]);break}return r&&(bo[n][t]=r),r}function Mo(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function Se(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function mw(e,t){return Se(e,3).substring(0,t)}function G(e,t,n=0,r=!1,o=!1){return function(i,s){let a=yw(e,i);if((n>0||a>-n)&&(a+=n),e===A.Hours)a===0&&n===-12&&(a=12);else if(e===A.FractionalSeconds)return mw(a,t);let u=ze(s,he.MinusSign);return Se(a,t,u,r,o)}}function yw(e,t){switch(e){case A.FullYear:return t.getFullYear();case A.Month:return t.getMonth();case A.Date:return t.getDate();case A.Hours:return t.getHours();case A.Minutes:return t.getMinutes();case A.Seconds:return t.getSeconds();case A.FractionalSeconds:return t.getMilliseconds();case A.Day:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function L(e,t,n=ne.Format,r=!1){return function(o,i){return vw(o,i,e,t,n,r)}}function vw(e,t,n,r,o,i){switch(n){case N.Months:return uw(t,o,r)[e.getMonth()];case N.Days:return aw(t,o,r)[e.getDay()];case N.DayPeriods:let s=e.getHours(),a=e.getMinutes();if(i){let c=dw(t),l=fw(t,o,r),d=c.findIndex(f=>{if(Array.isArray(f)){let[p,h]=f,y=s>=p.hours&&a>=p.minutes,T=s<h.hours||s===h.hours&&a<h.minutes;if(p.hours<h.hours){if(y&&T)return!0}else if(y||T)return!0}else if(f.hours===s&&f.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return sw(t,o,r)[s<12?0:1];case N.Eras:return cw(t,r)[e.getFullYear()<=0?0:1];default:let u=n;throw new Error(`unexpected translation type ${u}`)}}function _o(e){return function(t,n,r){let o=-1*r,i=ze(n,he.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case qe.Short:return(o>=0?"+":"")+Se(s,2,i)+Se(Math.abs(o%60),2,i);case qe.ShortGMT:return"GMT"+(o>=0?"+":"")+Se(s,1,i);case qe.Long:return"GMT"+(o>=0?"+":"")+Se(s,2,i)+":"+Se(Math.abs(o%60),2,i);case qe.Extended:return r===0?"Z":(o>=0?"+":"")+Se(s,2,i)+":"+Se(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var Dw=0,To=4;function ww(e){let t=So(e,Dw,1).getDay();return So(e,0,1+(t<=To?To:To+7)-t)}function pf(e){let t=e.getDay(),n=t===0?-3:To-t;return So(e.getFullYear(),e.getMonth(),e.getDate()+n)}function Oa(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=pf(n),s=ww(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Se(o,e,ze(r,he.MinusSign))}}function xo(e,t=!1){return function(n,r){let i=pf(n).getFullYear();return Se(i,e,ze(r,he.MinusSign),t)}}var Fa={};function Iw(e){if(Fa[e])return Fa[e];let t;switch(e){case"G":case"GG":case"GGG":t=L(N.Eras,P.Abbreviated);break;case"GGGG":t=L(N.Eras,P.Wide);break;case"GGGGG":t=L(N.Eras,P.Narrow);break;case"y":t=G(A.FullYear,1,0,!1,!0);break;case"yy":t=G(A.FullYear,2,0,!0,!0);break;case"yyy":t=G(A.FullYear,3,0,!1,!0);break;case"yyyy":t=G(A.FullYear,4,0,!1,!0);break;case"Y":t=xo(1);break;case"YY":t=xo(2,!0);break;case"YYY":t=xo(3);break;case"YYYY":t=xo(4);break;case"M":case"L":t=G(A.Month,1,1);break;case"MM":case"LL":t=G(A.Month,2,1);break;case"MMM":t=L(N.Months,P.Abbreviated);break;case"MMMM":t=L(N.Months,P.Wide);break;case"MMMMM":t=L(N.Months,P.Narrow);break;case"LLL":t=L(N.Months,P.Abbreviated,ne.Standalone);break;case"LLLL":t=L(N.Months,P.Wide,ne.Standalone);break;case"LLLLL":t=L(N.Months,P.Narrow,ne.Standalone);break;case"w":t=Oa(1);break;case"ww":t=Oa(2);break;case"W":t=Oa(1,!0);break;case"d":t=G(A.Date,1);break;case"dd":t=G(A.Date,2);break;case"c":case"cc":t=G(A.Day,1);break;case"ccc":t=L(N.Days,P.Abbreviated,ne.Standalone);break;case"cccc":t=L(N.Days,P.Wide,ne.Standalone);break;case"ccccc":t=L(N.Days,P.Narrow,ne.Standalone);break;case"cccccc":t=L(N.Days,P.Short,ne.Standalone);break;case"E":case"EE":case"EEE":t=L(N.Days,P.Abbreviated);break;case"EEEE":t=L(N.Days,P.Wide);break;case"EEEEE":t=L(N.Days,P.Narrow);break;case"EEEEEE":t=L(N.Days,P.Short);break;case"a":case"aa":case"aaa":t=L(N.DayPeriods,P.Abbreviated);break;case"aaaa":t=L(N.DayPeriods,P.Wide);break;case"aaaaa":t=L(N.DayPeriods,P.Narrow);break;case"b":case"bb":case"bbb":t=L(N.DayPeriods,P.Abbreviated,ne.Standalone,!0);break;case"bbbb":t=L(N.DayPeriods,P.Wide,ne.Standalone,!0);break;case"bbbbb":t=L(N.DayPeriods,P.Narrow,ne.Standalone,!0);break;case"B":case"BB":case"BBB":t=L(N.DayPeriods,P.Abbreviated,ne.Format,!0);break;case"BBBB":t=L(N.DayPeriods,P.Wide,ne.Format,!0);break;case"BBBBB":t=L(N.DayPeriods,P.Narrow,ne.Format,!0);break;case"h":t=G(A.Hours,1,-12);break;case"hh":t=G(A.Hours,2,-12);break;case"H":t=G(A.Hours,1);break;case"HH":t=G(A.Hours,2);break;case"m":t=G(A.Minutes,1);break;case"mm":t=G(A.Minutes,2);break;case"s":t=G(A.Seconds,1);break;case"ss":t=G(A.Seconds,2);break;case"S":t=G(A.FractionalSeconds,1);break;case"SS":t=G(A.FractionalSeconds,2);break;case"SSS":t=G(A.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":t=_o(qe.Short);break;case"ZZZZZ":t=_o(qe.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=_o(qe.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":t=_o(qe.Long);break;default:return null}return Fa[e]=t,t}function hf(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function Ew(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function Cw(e,t,n){let r=n?-1:1,o=e.getTimezoneOffset(),i=hf(t,o);return Ew(e,r*(i-o))}function bw(e){if(nf(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return So(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(pw))return Mw(r)}let t=new Date(e);if(!nf(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function Mw(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,u=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,u,c),t}function nf(e){return e instanceof Date&&!isNaN(e.valueOf())}var _w=/^(\d+)?\.((\d+)(-(\d+))?)?$/,rf=22,No=".",Un="0",xw=";",Tw=",",Ra="#";function Sw(e,t,n,r,o,i,s=!1){let a="",u=!1;if(!isFinite(e))a=ze(n,he.Infinity);else{let c=Fw(e);s&&(c=Ow(c));let l=t.minInt,d=t.minFrac,f=t.maxFrac;if(i){let W=i.match(_w);if(W===null)throw new Error(`${i} is not a valid digit info`);let z=W[1],ae=W[3],Le=W[5];z!=null&&(l=Pa(z)),ae!=null&&(d=Pa(ae)),Le!=null?f=Pa(Le):ae!=null&&d>f&&(f=d)}Rw(c,d,f);let p=c.digits,h=c.integerLen,y=c.exponent,T=[];for(u=p.every(W=>!W);h<l;h++)p.unshift(0);for(;h<0;h++)p.unshift(0);h>0?T=p.splice(h,p.length):(T=p,p=[0]);let M=[];for(p.length>=t.lgSize&&M.unshift(p.splice(-t.lgSize,p.length).join(""));p.length>t.gSize;)M.unshift(p.splice(-t.gSize,p.length).join(""));p.length&&M.unshift(p.join("")),a=M.join(ze(n,r)),T.length&&(a+=ze(n,o)+T.join("")),y&&(a+=ze(n,he.Exponential)+"+"+y)}return e<0&&!u?a=t.negPre+a+t.negSuf:a=t.posPre+a+t.posSuf,a}function Nw(e,t,n){let r=lw(t,df.Decimal),o=Aw(r,ze(t,he.MinusSign));return Sw(e,o,t,he.Group,he.Decimal,n)}function Aw(e,t="-"){let n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},r=e.split(xw),o=r[0],i=r[1],s=o.indexOf(No)!==-1?o.split(No):[o.substring(0,o.lastIndexOf(Un)+1),o.substring(o.lastIndexOf(Un)+1)],a=s[0],u=s[1]||"";n.posPre=a.substring(0,a.indexOf(Ra));for(let l=0;l<u.length;l++){let d=u.charAt(l);d===Un?n.minFrac=n.maxFrac=l+1:d===Ra?n.maxFrac=l+1:n.posSuf+=d}let c=a.split(Tw);if(n.gSize=c[1]?c[1].length:0,n.lgSize=c[2]||c[1]?(c[2]||c[1]).length:0,i){let l=o.length-n.posPre.length-n.posSuf.length,d=i.indexOf(Ra);n.negPre=i.substring(0,d).replace(/'/g,""),n.negSuf=i.slice(d+l).replace(/'/g,"")}else n.negPre=t+n.posPre,n.negSuf=n.posSuf;return n}function Ow(e){if(e.digits[0]===0)return e;let t=e.digits.length-e.integerLen;return e.exponent?e.exponent+=2:(t===0?e.digits.push(0,0):t===1&&e.digits.push(0),e.integerLen+=2),e}function Fw(e){let t=Math.abs(e)+"",n=0,r,o,i,s,a;for((o=t.indexOf(No))>-1&&(t=t.replace(No,"")),(i=t.search(/e/i))>0?(o<0&&(o=i),o+=+t.slice(i+1),t=t.substring(0,i)):o<0&&(o=t.length),i=0;t.charAt(i)===Un;i++);if(i===(a=t.length))r=[0],o=1;else{for(a--;t.charAt(a)===Un;)a--;for(o-=i,r=[],s=0;i<=a;i++,s++)r[s]=Number(t.charAt(i))}return o>rf&&(r=r.splice(0,rf-1),n=o-1,o=1),{digits:r,exponent:n,integerLen:o}}function Rw(e,t,n){if(t>n)throw new Error(`The minimum number of digits after fraction (${t}) is higher than the maximum (${n}).`);let r=e.digits,o=r.length-e.integerLen,i=Math.min(Math.max(t,o),n),s=i+e.integerLen,a=r[s];if(s>0){r.splice(Math.max(e.integerLen,s));for(let d=s;d<r.length;d++)r[d]=0}else{o=Math.max(0,o),e.integerLen=1,r.length=Math.max(1,s=i+1),r[0]=0;for(let d=1;d<s;d++)r[d]=0}if(a>=5)if(s-1<0){for(let d=0;d>s;d--)r.unshift(0),e.integerLen++;r.unshift(1),e.integerLen++}else r[s-1]++;for(;o<Math.max(0,i);o++)r.push(0);let u=i!==0,c=t+e.integerLen,l=r.reduceRight(function(d,f,p,h){return f=f+d,h[p]=f<10?f:f-10,u&&(h[p]===0&&p>=c?h.pop():u=!1),f>=10?1:0},0);l&&(r.unshift(l),e.integerLen++)}function Pa(e){let t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}function vS(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var ka=/\s+/,of=[],DS=(()=>{let t=class t{constructor(r,o){this._ngEl=r,this._renderer=o,this.initialClasses=of,this.stateMap=new Map}set klass(r){this.initialClasses=r!=null?r.trim().split(ka):of}set ngClass(r){this.rawClass=typeof r=="string"?r.trim().split(ka):r}ngDoCheck(){for(let o of this.initialClasses)this._updateState(o,!0);let r=this.rawClass;if(Array.isArray(r)||r instanceof Set)for(let o of r)this._updateState(o,!0);else if(r!=null)for(let o of Object.keys(r))this._updateState(o,!!r[o]);this._applyStateDiff()}_updateState(r,o){let i=this.stateMap.get(r);i!==void 0?(i.enabled!==o&&(i.changed=!0,i.enabled=o),i.touched=!0):this.stateMap.set(r,{enabled:o,changed:!0,touched:!0})}_applyStateDiff(){for(let r of this.stateMap){let o=r[0],i=r[1];i.changed?(this._toggleClass(o,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(o,!1),this.stateMap.delete(o)),i.touched=!1}}_toggleClass(r,o){r=r.trim(),r.length>0&&r.split(ka).forEach(i=>{o?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}};t.\u0275fac=function(o){return new(o||t)(U(rt),U(Do))},t.\u0275dir=nn({type:t,selectors:[["","ngClass",""]],inputs:{klass:[Ze.None,"class","klass"],ngClass:"ngClass"},standalone:!0});let e=t;return e})();var La=class{constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},wS=(()=>{let t=class t{set ngForOf(r){this._ngForOf=r,this._ngForOfDirty=!0}set ngForTrackBy(r){this._trackByFn=r}get ngForTrackBy(){return this._trackByFn}constructor(r,o,i){this._viewContainer=r,this._template=o,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(r){r&&(this._template=r)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let r=this._ngForOf;if(!this._differ&&r)if(0)try{}catch{}else this._differ=this._differs.find(r).create(this.ngForTrackBy)}if(this._differ){let r=this._differ.diff(this._ngForOf);r&&this._applyChanges(r)}}_applyChanges(r){let o=this._viewContainer;r.forEachOperation((i,s,a)=>{if(i.previousIndex==null)o.createEmbeddedView(this._template,new La(i.item,this._ngForOf,-1,-1),a===null?void 0:a);else if(a==null)o.remove(s===null?void 0:s);else if(s!==null){let u=o.get(s);o.move(u,a),sf(u,i)}});for(let i=0,s=o.length;i<s;i++){let u=o.get(i).context;u.index=i,u.count=s,u.ngForOf=this._ngForOf}r.forEachIdentityChange(i=>{let s=o.get(i.currentIndex);sf(s,i)})}static ngTemplateContextGuard(r,o){return!0}};t.\u0275fac=function(o){return new(o||t)(U(Tt),U(bt),U(xa))},t.\u0275dir=nn({type:t,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0});let e=t;return e})();function sf(e,t){e.context.$implicit=t.item}var IS=(()=>{let t=class t{constructor(r,o){this._viewContainer=r,this._context=new ja,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=o}set ngIf(r){this._context.$implicit=this._context.ngIf=r,this._updateView()}set ngIfThen(r){af("ngIfThen",r),this._thenTemplateRef=r,this._thenViewRef=null,this._updateView()}set ngIfElse(r){af("ngIfElse",r),this._elseTemplateRef=r,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(r,o){return!0}};t.\u0275fac=function(o){return new(o||t)(U(Tt),U(bt))},t.\u0275dir=nn({type:t,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0});let e=t;return e})(),ja=class{constructor(){this.$implicit=null,this.ngIf=null}};function af(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${te(t)}'.`)}var ES=(()=>{let t=class t{constructor(r,o,i){this._ngEl=r,this._differs=o,this._renderer=i,this._ngStyle=null,this._differ=null}set ngStyle(r){this._ngStyle=r,!this._differ&&r&&(this._differ=this._differs.find(r).create())}ngDoCheck(){if(this._differ){let r=this._differ.diff(this._ngStyle);r&&this._applyChanges(r)}}_setStyle(r,o){let[i,s]=r.split("."),a=i.indexOf("-")===-1?void 0:_n.DashCase;o!=null?this._renderer.setStyle(this._ngEl.nativeElement,i,s?`${o}${s}`:o,a):this._renderer.removeStyle(this._ngEl.nativeElement,i,a)}_applyChanges(r){r.forEachRemovedItem(o=>this._setStyle(o.key,null)),r.forEachAddedItem(o=>this._setStyle(o.key,o.currentValue)),r.forEachChangedItem(o=>this._setStyle(o.key,o.currentValue))}};t.\u0275fac=function(o){return new(o||t)(U(rt),U(Ta),U(Do))},t.\u0275dir=nn({type:t,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"},standalone:!0});let e=t;return e})(),CS=(()=>{let t=class t{constructor(r){this._viewContainerRef=r,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null,this.ngTemplateOutletInjector=null}ngOnChanges(r){if(this._shouldRecreateView(r)){let o=this._viewContainerRef;if(this._viewRef&&o.remove(o.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let i=this._createContextForwardProxy();this._viewRef=o.createEmbeddedView(this.ngTemplateOutlet,i,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(r){return!!r.ngTemplateOutlet||!!r.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(r,o,i)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,o,i):!1,get:(r,o,i)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,o,i)}})}};t.\u0275fac=function(o){return new(o||t)(U(Tt))},t.\u0275dir=nn({type:t,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},standalone:!0,features:[Hs]});let e=t;return e})();function Oo(e,t){return new C(2100,!1)}var Va=class{createSubscription(t,n){return Sa(()=>t.subscribe({next:n,error:r=>{throw r}}))}dispose(t){Sa(()=>t.unsubscribe())}},Ba=class{createSubscription(t,n){return t.then(n,r=>{throw r})}dispose(t){}},Pw=new Ba,kw=new Va,bS=(()=>{let t=class t{constructor(r){this._latestValue=null,this.markForCheckOnValueUpdate=!0,this._subscription=null,this._obj=null,this._strategy=null,this._ref=r}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(r){if(!this._obj){if(r)try{this.markForCheckOnValueUpdate=!1,this._subscribe(r)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return r!==this._obj?(this._dispose(),this.transform(r)):this._latestValue}_subscribe(r){this._obj=r,this._strategy=this._selectStrategy(r),this._subscription=this._strategy.createSubscription(r,o=>this._updateLatestValue(r,o))}_selectStrategy(r){if(wo(r))return Pw;if(ba(r))return kw;throw Oo(t,r)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(r,o){r===this._obj&&(this._latestValue=o,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}};t.\u0275fac=function(o){return new(o||t)(U(_a,16))},t.\u0275pipe=Rn({name:"async",type:t,pure:!1,standalone:!0});let e=t;return e})();var Lw=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g,MS=(()=>{let t=class t{transform(r){if(r==null)return null;if(typeof r!="string")throw Oo(t,r);return r.replace(Lw,o=>o[0].toUpperCase()+o.slice(1).toLowerCase())}};t.\u0275fac=function(o){return new(o||t)},t.\u0275pipe=Rn({name:"titlecase",type:t,pure:!0,standalone:!0});let e=t;return e})();var jw="mediumDate",Vw=new O(""),Bw=new O(""),_S=(()=>{let t=class t{constructor(r,o,i){this.locale=r,this.defaultTimezone=o,this.defaultOptions=i}transform(r,o,i,s){if(r==null||r===""||r!==r)return null;try{let a=o??this.defaultOptions?.dateFormat??jw,u=i??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return gw(r,a,s||this.locale,u)}catch(a){throw Oo(t,a.message)}}};t.\u0275fac=function(o){return new(o||t)(U(Hn,16),U(Vw,24),U(Bw,24))},t.\u0275pipe=Rn({name:"date",type:t,pure:!0,standalone:!0});let e=t;return e})();var xS=(()=>{let t=class t{constructor(r){this._locale=r}transform(r,o,i){if(!$w(r))return null;i||=this._locale;try{let s=Hw(r);return Nw(s,i,o)}catch(s){throw Oo(t,s.message)}}};t.\u0275fac=function(o){return new(o||t)(U(Hn,16))},t.\u0275pipe=Rn({name:"number",type:t,pure:!0,standalone:!0});let e=t;return e})();function $w(e){return!(e==null||e===""||e!==e)}function Hw(e){if(typeof e=="string"&&!isNaN(Number(e)-parseFloat(e)))return Number(e);if(typeof e!="number")throw new Error(`${e} is not a number`);return e}var TS=(()=>{let t=class t{};t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=ks({type:t}),t.\u0275inj=Os({});let e=t;return e})(),Uw="browser",Gw="server";function Ww(e){return e===Uw}function SS(e){return e===Gw}var NS=(()=>{let t=class t{};t.\u0275prov=k({token:t,providedIn:"root",factory:()=>Ww(F(na))?new $a(F(Ua),window):new Ha});let e=t;return e})(),$a=class{constructor(t,n){this.document=t,this.window=n,this.offset=()=>[0,0]}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=zw(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function zw(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Ha=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}},uf=class{};export{$ as a,bf as b,_ as c,qo as d,Yo as e,ge as f,cn as g,dn as h,xf as i,lt as j,Ae as k,Pf as l,kf as m,Lf as n,je as o,jf as p,Oe as q,zf as r,Ee as s,pn as t,Bt as u,Yf as v,Qf as w,Zo as x,hn as y,np as z,ft as A,rp as B,bu as C,op as D,ip as E,gn as F,$t as G,Ko as H,sp as I,ap as J,lp as K,xu as L,Xo as M,dp as N,fp as O,ti as P,pp as Q,hp as R,Tu as S,gp as T,mp as U,yp as V,vp as W,C as X,ue as Y,wc as Z,k as _,Os as $,zx as aa,O as ba,x as ca,H as da,F as ea,Tc as fa,Sc as ga,Dn as ha,Ze as ia,qx as ja,ks as ka,nn as la,Yx as ma,Wc as na,Je as oa,vh as pa,Hs as qa,Qx as ra,Zx as sa,Kx as ta,Jx as ua,Xx as va,lg as wa,xt as xa,Et as ya,rt as za,Me as Aa,Ri as Ba,eT as Ca,tT as Da,Eg as Ea,na as Fa,nT as Ga,rT as Ha,jn as Ia,Ol as Ja,oT as Ka,iT as La,sT as Ma,aT as Na,uT as Oa,Fl as Pa,cT as Qa,oa as Ra,zg as Sa,lT as Ta,dT as Ua,fT as Va,_n as Wa,pT as Xa,U as Ya,hT as Za,bt as _a,Br as $a,vo as ab,Zi as bb,Do as cb,$n as db,le as eb,wy as fb,Tt as gb,ev as hb,sv as ib,us as jb,uv as kb,bd as lb,ls as mb,yv as nb,Av as ob,Td as pb,Ov as qb,vT as rb,DT as sb,wT as tb,IT as ub,ET as vb,CT as wb,kd as xb,Ld as yb,Wv as zb,jd as Ab,Vd as Bb,Yv as Cb,bT as Db,Zv as Eb,Kv as Fb,rD as Gb,oD as Hb,MT as Ib,_T as Jb,xT as Kb,aD as Lb,Bd as Mb,TT as Nb,ST as Ob,NT as Pb,AT as Qb,OT as Rb,FT as Sb,lD as Tb,$d as Ub,dD as Vb,fD as Wb,pD as Xb,hD as Yb,RT as Zb,gD as _b,PT as $b,kT as ac,LT as bc,jT as cc,VT as dc,BT as ec,$T as fc,HT as gc,UT as hc,GT as ic,CD as jc,bD as kc,WT as lc,MD as mc,wo as nc,xD as oc,TD as pc,Ma as qc,zT as rc,Hn as sc,UD as tc,_a as uc,xa as vc,qT as wc,YT as xc,ZD as yc,KD as zc,QT as Ac,Na as Bc,gS as Cc,Xd as Dc,Ua as Ec,mS as Fc,Ao as Gc,ew as Hc,yS as Ic,tw as Jc,vS as Kc,DS as Lc,wS as Mc,IS as Nc,ES as Oc,CS as Pc,bS as Qc,MS as Rc,_S as Sc,xS as Tc,TS as Uc,Uw as Vc,Ww as Wc,SS as Xc,NS as Yc,uf as Zc};
