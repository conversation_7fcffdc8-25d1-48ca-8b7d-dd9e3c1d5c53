<div class="container mt-4"style="width: 90%">
    <section>
        <fieldset>
            <legend><b>Material Deying</b></legend>
            <div class="row">
                <div class="mb-2 col-md-4">
                    <mat-form-field appearance="outline"class="ex-width">
                        <mat-label>Challan No.</mat-label>
                        <input matInput placeholder="Challan No.">
                    </mat-form-field>
                </div>
                <div class="mb-2 col-md-4">
                    <mat-form-field class="full-width" appearance="outline">
                        <mat-label>Date</mat-label>
                        <input matInput [matDatepicker]="picker">
                        <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                      </mat-form-field>
                </div>
                <div class="mb-2 col-md-4">
                    <mat-form-field appearance="outline"class="ex-width">
                        <mat-label> Dyers Name</mat-label>
                        <mat-select [(value)]="Name">
                          <mat-option value="option1">Option 1</mat-option>
                          <mat-option value="option2">Option 2</mat-option>
                          <mat-option value="option3">Option 3</mat-option>
                        </mat-select>
                      </mat-form-field>
                </div><hr><br><br><legend><b>Dyeing Order Issue</b></legend><hr>
                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline"class="ex-width">
                        <mat-label>Group</mat-label>
                        <mat-select [(value)]="Group">
                          <mat-option value="option1">Option 1</mat-option>
                          <mat-option value="option2">Option 2</mat-option>
                          <mat-option value="option3">Option 3</mat-option>
                        </mat-select>
                      </mat-form-field>
                 </div>
                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline"class="ex-width">
                        <mat-label>Item</mat-label>
                        <mat-select [(value)]="Item">
                          <mat-option value="option1">Option 1</mat-option>
                          <mat-option value="option2">Option 2</mat-option>
                          <mat-option value="option3">Option 3</mat-option>
                        </mat-select>
                      </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline"class="ex-width">
                        <mat-label>Quality</mat-label>
                        <mat-select [(value)]="WoolQuality">
                          <mat-option value="option1">Option 1</mat-option>
                          <mat-option value="option2">Option 2</mat-option>
                          <mat-option value="option3">Option 3</mat-option>
                        </mat-select>
                      </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline"class="ex-width">
                        <mat-label>Count</mat-label>
                        <input matInput placeholder="Count">
                    </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline"class="ex-width">
                    <mat-label>Colours</mat-label>
                    <mat-select [(value)]="Colours">
                      <mat-option value="option1">Option 1</mat-option>
                      <mat-option value="option2">Option 2</mat-option>
                      <mat-option value="option3">Option 3</mat-option>
                    </mat-select>
                  </mat-form-field>
                    
                </div>
                <div class="mb-2 col-md-3">
                  <mat-form-field appearance="outline"class="ex-width">
                    <mat-label>Party Name</mat-label>
                    <input matInput placeholder="Party Name">
                </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline"class="ex-width">
                        <mat-label>Lot No.</mat-label>
                        <input matInput placeholder="Lot No.">
                    </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline"class="ex-width">
                        <mat-label>Weight</mat-label>
                        <input matInput placeholder="Weight">
                    </mat-form-field>
                </div> 
                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline"class="ex-width">
                        <mat-label>Rate</mat-label>
                        <input matInput placeholder="Rate">
                      </mat-form-field>
                </div>
                <div class="mb-2 col-md-3">
                    <mat-form-field appearance="outline"class="ex-width">
                        <mat-label>Amount</mat-label>
                        <input matInput placeholder="Amount">
                      </mat-form-field>
                </div>
                 
                
            </div>
            <div class="mb-2 col-md-4 space">

                <button mat-flat-button color="primary">Save</button> 
                <button mat-flat-button color="primary">Edit</button> 
                <button mat-flat-button color="primary">Print</button>  
                <button mat-flat-button color="primary">Delete</button>
            </div>
          
        </fieldset>
    </section>
</div>

<div class="container mt-4"style="width: 90%">
    <section>
        <fieldset>
            <legend><b> Order List</b></legend>
            <div class="row">
                <div class="col-md-12">
                    <mat-form-field appearance="outline">
                        <mat-label>Search</mat-label>
                        <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Jack" #input>
                      </mat-form-field>


                    <div class="mat-elevation-z8 scroll-container">
                      <table mat-table [dataSource]="dataSource" matSort>
                        <!-- No Column -->
                        <ng-container matColumnDef="SrNo">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Sr. No.</th>
                          <td mat-cell *matCellDef="let row">{{ row.SrNo }}</td>
                        </ng-container>
                        <!-- Challan No Column -->
                        <ng-container matColumnDef="ChallanNo">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Challan No.</th>
                          <td mat-cell *matCellDef="let row">{{ row.ChallanNo }}</td>
                        </ng-container>

                        <!-- Date Column -->
                        <ng-container matColumnDef="Date">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
                          <td mat-cell *matCellDef="let row">{{ row.Date }}
                          </td>
                        </ng-container>

                        <!-- Name Column -->
                        <ng-container matColumnDef="Name">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                          <td mat-cell *matCellDef="let row">{{ row.Name}}</td>
                        </ng-container>
                         <!-- Group Column -->
                        <ng-container matColumnDef="Group">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Group</th>
                            <td mat-cell *matCellDef="let row">{{ row.Group }}</td>
                          </ng-container>
                         <!-- Item Column -->
                        <ng-container matColumnDef="Item">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Item</th>
                            <td mat-cell *matCellDef="let row">{{ row.Item }}</td>
                          </ng-container>

                        <!-- Wool Quality Column -->
                        <ng-container matColumnDef="WoolQuality">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header> Wool Quality</th>
                          <td mat-cell *matCellDef="let row">{{ row.WoolQuality }}</td>
                        </ng-container>
                         <!-- Count Column -->
                         <ng-container matColumnDef="Count">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Count</th>
                          <td mat-cell *matCellDef="let row">{{ row.Count}}</td>
                        </ng-container>
                         <!-- Colour Column -->
                        <ng-container matColumnDef="Colour">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Colour</th>
                            <td mat-cell *matCellDef="let row">{{ row.Colour }}</td>
                          </ng-container>
                         <!-- PartyName Column -->
                        <ng-container matColumnDef="PartyName">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Party Name</th>
                            <td mat-cell *matCellDef="let row">{{ row.PartyName }}</td>
                          </ng-container>

                           <!-- LotNo Column -->
                        <ng-container matColumnDef="LotNo">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Lot No.</th>
                            <td mat-cell *matCellDef="let row">{{ row.LotNo }}</td>
                          </ng-container>
                         <!-- Weight Column -->
                        <ng-container matColumnDef="Weight">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Weight</th>
                            <td mat-cell *matCellDef="let row">{{ row.Weight }}</td>
                          </ng-container>
                           <!-- Rate Column -->
                        <ng-container matColumnDef="Rate">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Rate</th>
                            <td mat-cell *matCellDef="let row">{{ row.Rate }}</td>
                          </ng-container>
                         <!-- Amount Column -->
                        <ng-container matColumnDef="Amount">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Amount</th>
                            <td mat-cell *matCellDef="let row">{{ row.Amount }}</td>
                          </ng-container>
                          
                        
                          
                         <!-- Action Column -->  
                        <ng-container matColumnDef="Action">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Action</th>
                          <td mat-cell *matCellDef="let row">{{ row.Action }}</td>
                        </ng-container>


                        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                        <tr
                          mat-row
                          *matRowDef="let row; columns: displayedColumns"
                        ></tr>

                        <!-- Row shown when there is no matching data. -->
                        <tr class="mat-row" *matNoDataRow>
                          <td class="mat-cell" colspan="4">
                            No data matching the filter "{{ input.value }}"
                          </td>
                        </tr>
                      </table>

                      <mat-paginator
                        [pageSizeOptions]="[5, 10, 25, 100]"
                        aria-label="Select page of users"
                      ></mat-paginator>
                </div>
            </div>
            </div>
        </fieldset>
    </section>
 </div>
 


