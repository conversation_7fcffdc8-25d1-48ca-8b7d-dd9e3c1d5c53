import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-admin-sidebar',
  templateUrl: 'admin-sidebar.component.html',
  styleUrl: 'admin-sidebar.component.css'
})
export class AdminSidebarComponent implements OnInit {
[x: string]: any;
  @Input()isOpen:boolean=true;

  ngOnInit(): void {
    this.loadScript();
  }

  loadScript() {
    const script = document.createElement('script');
     
    script.onload = () => {
      // Script loaded and ready to use
      // Place your initialization code here
    };
    document.body.appendChild(script);
  }
  
  
  
}
