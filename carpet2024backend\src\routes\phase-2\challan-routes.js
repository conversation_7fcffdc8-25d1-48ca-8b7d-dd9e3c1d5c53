const express = require('express');
const CreateChallanController = require('../../controller/challan-controller');
const checkDuplicateChallanNo = require('../../middleware/createChallan-middleware');

const router = express.Router();
const createChallanController = new CreateChallanController();

const { logRequestResponse, logDataChanges } = require('../../middleware/logger');

// Apply logger middleware to log request and response
// router.use(logRequestResponse);

// Apply data changes logger middleware to log data updates, creations, deletions, and retrievals
// router.use(logDataChanges);

router.post('/CreateChallan', checkDuplicateChallanNo, createChallanController.createChallan.bind(createChallanController));
router.post('/process-return', createChallanController.processReturn.bind(createChallanController));
router.get('/CreateChallan', createChallanController.getAllChallans.bind(createChallanController));
router.get('/CreateChallan/:id', createChallanController.getChallanById.bind(createChallanController));
router.get('/barcode-history/:barcodeNo', createChallanController.getBarcodeHistory.bind(createChallanController));
router.put('/CreateChallan/:id', createChallanController.updateChallan.bind(createChallanController));
router.delete('/CreateChallan/:id/:itemId', createChallanController.deleteChallanItem.bind(createChallanController));
router.get('/get-deleted-challan-data', createChallanController.getAllDeletedChallanHistory.bind(createChallanController));
router.post('/getChallans', createChallanController.getChallans.bind(createChallanController));

module.exports = router;
