.material-container {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Prompt Section Styles */
.prompt-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 70vh;
}

.prompt-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
  width: 100%;
  animation: fadeInUp 0.6s ease-out;
}

.prompt-header {
  margin-bottom: 40px;
}

.prompt-title {
  color: #1f497d;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 10px;
  text-decoration: underline;
}

.prompt-subtitle {
  color: #666;
  font-size: 18px;
  margin: 0;
}

.prompt-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.action-button {
  width: 250px;
  height: 60px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  text-transform: none;
}

.material-issue-btn {
  background: linear-gradient(45deg, #2196F3, #21CBF3);
  color: white;
}

.material-issue-btn:hover {
  background: linear-gradient(45deg, #1976D2, #0288D1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
}

.material-receive-btn {
  background: linear-gradient(45deg, #4CAF50, #8BC34A);
  color: white;
}

.material-receive-btn:hover {
  background: linear-gradient(45deg, #388E3C, #689F38);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

/* Component Section Styles */
.component-section {
  animation: fadeIn 0.5s ease-in;
}

.back-button-container {
  margin-bottom: 20px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.back-button:hover {
  background-color: #f5f5f5;
  transform: translateX(-2px);
}

.component-wrapper {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  animation: slideInRight 0.5s ease-out;
}

.component-header {
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.component-header h3 {
  color: #1f497d;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.error-message {
  text-align: center;
  padding: 40px;
  color: #f44336;
  font-size: 18px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .material-container {
    padding: 15px;
  }

  .prompt-card {
    padding: 30px 20px;
    margin: 0 10px;
  }

  .prompt-title {
    font-size: 24px;
  }

  .action-button {
    width: 100%;
    max-width: 280px;
  }

  .component-wrapper {
    padding: 15px;
  }
}

/* Material Icon Styling */
mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Button Focus States */
.action-button:focus,
.back-button:focus {
  outline: 2px solid #2196F3;
  outline-offset: 2px;
}