import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MasterService } from '../../../services/master.service';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SweetalertService } from '../../../services/sweetalert.service';
import { DomSanitizer } from '@angular/platform-browser';

export interface UserData {
  id: string;
  index: number;
  quality: string;
  colour: string;
  colourCode: string;
  material: string;
  item: string;
  companyColorCode:string;
  count: string;
}

const ELEMENT_DATA: UserData[] = [];

@Component({
  selector: 'app-code-dyeing-details',
  templateUrl: './code-dyeing-details.component.html',
  styleUrl: './code-dyeing-details.component.css',
})
export class CodeDyeingDetailsComponent implements OnInit, AfterViewInit {
  Quality = 'option1';
  Colour = 'option2';
  Material = 'option1';
  Count = 'option3';
  color: any;
  Item = 'option1';
  qualityList: any = [];
  displayedColumns: string[] = [
    'id',


    'colour',
    // 'colourCode',
    'companyColorCode',
    'quality',
    'material',
    'item',
    'count',
    'action',
  ];
  dataSource = new MatTableDataSource<UserData>(ELEMENT_DATA);
  qty: any;
  constructor(
    private services: MasterService,
    private fb: FormBuilder,
    private alert: SweetalertService,
    private sanitizer: DomSanitizer
  ) {}

  frmDyeingDetail!: FormGroup;

  ngOnInit(): void {
    this.frmDyeingDetail = this.fb.group({
      quality: ['',[Validators.required]],
      Color: ['',[Validators.required]],
      companyColorCode:['',[Validators.required]],
      // colourcode: [],
      material: ['',[Validators.required]],
      item: ['',[Validators.required]],
      count: ['',[Validators.required]],
      chemicals: this.fb.array([])
    });
    this.qualitys();
    this.getMaterial();
    this.getsDyeingDetails();
  }
  get chemicals(): FormArray {
    return this.frmDyeingDetail.get('chemicals') as FormArray;
  }

  newRecipe(): FormGroup {
    return this.fb.group({
      chemical: ['', Validators.required],
      weight: ['', Validators.required],
      vendorName: ['', Validators.required],
      dyesName: ['', Validators.required],
      weight2: ['', Validators.required],
      vendorName2: ['', Validators.required]
    });
  }

  addRecipe() {
    this.chemicals.push(this.newRecipe());
  }

  removeRecipe(i: number) {
    this.chemicals.removeAt(i);
  }
  @ViewChild(MatPaginator)
  paginator!: MatPaginator;
  @ViewChild(MatSort)
  sort!: MatSort;

  qualitys() {
    this.services.getqualityList().subscribe({
      next: (value: any) => {
        value.map((v: any) => {
          this.qualityList.push({
            quality: v.quality,
            id: v._id,
          });
        });
      },
      error: (err) => {},
    });
  }
  onQuality(data: any) {
    this.qty = data;
    this.getsColor(this.qty);
    this.color = '';
    this.frmDyeingDetail.get('companyColorCode')?.setValue('')

  }
  colorList: any = [];
  getsColor(id: string) {
    this.services.getsColourCodeDetails().subscribe({
      next: (value: any) => {
        debugger;
        this.colorList = value.filter((x: any) => x.quality._id === id);
        console.log(value);
      },
    });
  }
  onColor(id: string) {
    const colorObject = this.colorList.find((x: any) => x._id === id);
debugger
    // If the color object is found, return the colorCode property; otherwise, return a default value (e.g., an empty string)
    this.color = colorObject ? colorObject.colorCode : '';
    this.frmDyeingDetail.get('companyColorCode')?.setValue(colorObject.companyColorCode)
    debugger;
  }
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  groupList: any = [];
  getMaterial() {
    this.services.getsRawMaterial().subscribe({
      next: (value: any) => {
        this.groupList = value;
      },
    });
  }

  itemList: any = [];
  onItem(id: string) {
    this.itemList.length = 0;
    this.countList.length = 0;
    this.itemList = this.groupList.filter((x: any) => x._id === id);
    debugger;
  }
  countList: any = [];
  onCount(item: string) {
    this.countList = this.groupList.filter((x: any) => x.Item === item);
  }
  selectCount(count: string) {}
dyeingList:any=[];
  getsDyeingDetails() {
    this.services.getsDyeingDetail().subscribe({
      next: (value: any) => {
        this.dyeingList=value;
        ELEMENT_DATA.length=0;
        value.map((v: any, i: number) => {
          ELEMENT_DATA.push({
            id: v._id,
            index: i + 1,
            quality: v.quality?.quality,
            colour: v.Color?.newColor,
            companyColorCode:v.companyColorCode,
            colourCode: v.colourcode,
            material: v.material?.Group,
            item: v.item,
            count: v.count,
          });
        });
        this.dataSource = new MatTableDataSource(ELEMENT_DATA);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        return;
      },
    });
  }
  addDyiengDetail() {
    let formdata = this.frmDyeingDetail.value;
    formdata.colourcode = this.color;

    this.services.addDyeingDetail(formdata).subscribe({
      next: (value) => {
        this.alert.success('success', 'Material has been saved successfully');
        this.getsDyeingDetails();
        this.clear();
      },
      error: (err) => {
        this.alert.error('Failed !', 'Something went wrong');
      },
    });
  }

  update(){
    let formdata = this.frmDyeingDetail.value;
    formdata.colourcode = this.color;

    this.services.updateDyeingDetail(this.editId,formdata).subscribe({
      next: (value) => {
        this.alert.success('success', 'Material has been updated successfully');
        this.getsDyeingDetails();
        this.clear();
      },
      error: (err) => {
        this.alert.error('Failed !', 'Something went wrong');
      },
    });
  }
  clear() {
    this.frmDyeingDetail.reset();
  }
  editId: string | undefined;
  isUpdated: boolean = false;
  edit(id: string) {
    this.services.getDyeingDetail(id).subscribe({
      next: (value:any) => {
        this.editId = id;
        this.isUpdated = true;
        debugger
        this.frmDyeingDetail.patchValue({ quality: value.quality,
          Color: value.Color,
          colourcode: value.colourcode,
          material: value.material,
          item: value.item,
          count: value.count});
          this.color=value.colourcode;
          this.getsColor(value.quality);
          this.onItem(value.material);
          this.onCount(value.item);

          value.chemicals.forEach((item: any) => {
            const recipe = this.newRecipe();
            recipe.patchValue(item);
            this.chemicals.push(recipe);
          });
      },
    });
  }
  delete(id: string) {
    debugger;
    this.alert
      .confirm({
        title: 'Delete Item',
        text: 'Are you sure you want to delete this item?',
        icon: 'warning',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'No, keep it',
      })
      .then((confi: any) => {
        if (confi.isConfirmed) {
          this.services.deleteDyeingDetail(id).subscribe({
            next: (value) => {
              this.alert.success('success', 'Dyeign details has been deleted');
              this.getsDyeingDetails();
            }, error: (err) => {
              this.alert.error('Failed !', 'Something went wrong');
            },
          });
        }
      });
  }
  viewData:any;
  view(id:string){
    this.viewData = this.dyeingList.find((x: any) => x._id === id)?.chemicals || '';
    console.log(this.viewData);
  }
}
